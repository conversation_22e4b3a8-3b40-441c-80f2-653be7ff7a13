# Yemen Market Integration - Project Structure

## Root Directory Layout (Post-Cleanup June 2025)

```
yemen-market-integration/
├── .claude/                    # Claude Code configuration
├── .venv/                      # UV-managed virtual environment
├── archive/                    # Deprecated and historical files
├── config/                     # Application configuration
├── data/                       # Data storage (excluded from git)
├── deployment/                 # Production deployment configs
├── docs/                       # Comprehensive documentation
├── examples/                   # Usage examples and demos
├── notebooks/                  # Jupyter analysis notebooks
├── plugins/                    # Extensible plugin system
├── reports/                    # Generated analysis reports
├── results/                    # Analysis results storage
├── scripts/                    # Utility and analysis scripts
├── src/                        # Main application source code
├── tests/                      # Test suites
├── tools/                      # Development and setup tools
├── CLAUDE.md                   # Claude Code instructions
├── CONTRIBUTING.md             # Contribution guidelines
├── pyproject.toml              # UV/Python project configuration
├── pytest.ini                 # Test configuration
├── README.md                   # Project overview
└── uv.lock                     # Locked dependency versions
```

## Key Changes (June 5, 2025)

### Archived Files
- `requirements.txt` → `archive/deprecated-2025-06-05/`
- Phase completion reports → `archive/deprecated-2025-06-05/`
- Legacy scripts → `archive/deprecated-2025-06-05/`

### Reorganized
- `PROJECT_OVERVIEW.md` → `docs/01-architecture/`
- `EXCHANGE_RATE_DATA_SOURCES.md` → `docs/05-methodology/data-processing/`
- `coverage.xml` → `reports/coverage/`
- `setup_mcp_code_checker.sh` → `tools/`
- `perplexity-ai-spaces-content/` → `deployments/archived/`

### Package Management
- **Primary**: `pyproject.toml` + `uv.lock`
- **Deprecated**: `requirements.txt` (archived)
- **Commands**: `uv sync`, `uv run`, `uv add`

## Claude Code Usage

Use UV exclusively:
```bash
uv sync --extra dev        # Setup
uv run python script.py   # Execute
uv run pytest tests/      # Test
```

Never use:
- `pip install -r requirements.txt` (file archived)
- `python -m venv venv` (conflicts with .venv)
- Manual virtual environment activation