"""
Example: Comprehensive Robustness Testing for H1 (Exchange Rate Mechanism)

This example demonstrates how to use the robustness framework to test
the hypothesis that currency fragmentation explains the Yemen price paradox.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import json

# Import robustness framework
from src.core.models.robustness import ComprehensiveRobustnessFramework
from src.core.models.robustness.yemen_specific_robustness import YemenSpecificRobustness
from src.core.models.hypothesis_testing.h1_exchange_rate import ExchangeRateMechanismTest

# For this example, we'll create a realistic model function
def exchange_rate_passthrough_model(data: pd.DataFrame) -> dict:
    """
    Model testing exchange rate pass-through to prices.
    
    This is the core test for H1: Do changes in exchange rates
    explain price differences between currency zones?
    """
    import statsmodels.api as sm
    from statsmodels.iolib.summary2 import summary_col
    
    # Ensure we have the required columns
    required_cols = ['price_usd', 'exchange_rate', 'conflict_intensity', 
                     'market_id', 'commodity_id', 'date', 'currency_zone']
    missing_cols = [col for col in required_cols if col not in data.columns]
    if missing_cols:
        raise ValueError(f"Missing required columns: {missing_cols}")
    
    # Create additional controls
    data['log_price'] = np.log(data['price_usd'] + 1)
    data['log_exchange_rate'] = np.log(data['exchange_rate'])
    
    # Add time and market fixed effects
    data['year_month'] = pd.to_datetime(data['date']).dt.to_period('M')
    
    # Main specification: Log-log model with fixed effects
    formula = """
    log_price ~ log_exchange_rate + conflict_intensity + 
    C(market_id) + C(commodity_id) + C(year_month)
    """
    
    try:
        # Run regression with clustered standard errors
        model = sm.OLS.from_formula(formula, data=data).fit(
            cov_type='cluster',
            cov_kwds={'groups': data['market_id']}
        )
        
        # Extract key results
        coef = model.params['log_exchange_rate']
        se = model.bse['log_exchange_rate']
        pval = model.pvalues['log_exchange_rate']
        ci = model.conf_int().loc['log_exchange_rate'].values
        
        return {
            'coefficient': coef,
            'se': se,
            'p_value': pval,
            'confidence_interval': tuple(ci),
            'n_obs': int(model.nobs),
            'r_squared': model.rsquared,
            'r_squared_adj': model.rsquared_adj,
            'converged': True,
            'exchange_rate_elasticity': coef,  # In log-log, coefficient is elasticity
            'implied_passthrough': coef * 100  # Percentage pass-through
        }
        
    except Exception as e:
        print(f"Model estimation failed: {e}")
        return {
            'coefficient': np.nan,
            'se': np.nan,
            'p_value': np.nan,
            'converged': False,
            'error': str(e)
        }


def run_h1_robustness_analysis():
    """
    Complete robustness analysis for H1: Exchange Rate Mechanism.
    """
    print("="*60)
    print("H1 ROBUSTNESS ANALYSIS: Exchange Rate Mechanism")
    print("="*60)
    
    # Load data (using example data structure)
    print("\n1. Loading data...")
    data = load_yemen_panel_data()  # Your data loading function
    print(f"   Loaded {len(data)} observations")
    print(f"   Markets: {data['market_id'].nunique()}")
    print(f"   Time periods: {data['date'].nunique()}")
    print(f"   Missing data: {data['price_usd'].isna().mean():.1%}")
    
    # Initialize frameworks
    print("\n2. Initializing robustness frameworks...")
    general_robustness = ComprehensiveRobustnessFramework(
        project_name="Yemen Market Integration - H1",
        output_dir="results/robustness/h1_exchange_rate"
    )
    
    yemen_robustness = YemenSpecificRobustness()
    
    # Define custom analytical choices for H1
    print("\n3. Defining analytical choices...")
    custom_choices = [
        {
            'name': 'exchange_rate_source',
            'options': ['official_cbya', 'official_cbys', 'parallel_market', 'weighted_avg'],
            'description': 'Exchange rate data source'
        },
        {
            'name': 'price_currency',
            'options': ['usd_converted', 'yer_original', 'real_prices'],
            'description': 'Price denomination'
        },
        {
            'name': 'zone_definition',
            'options': ['baseline_2024', 'time_varying', 'fuzzy_borders'],
            'description': 'Currency zone definition method'
        }
    ]
    
    # Run main analysis
    print("\n4. Running main specification...")
    main_results = exchange_rate_passthrough_model(data)
    print(f"   Main effect: {main_results['coefficient']:.3f} (p={main_results['p_value']:.4f})")
    print(f"   Implied pass-through: {main_results['implied_passthrough']:.1f}%")
    
    # Test currency zone robustness (CRITICAL for H1)
    print("\n5. Testing currency zone definition robustness...")
    baseline_zones = get_baseline_currency_zones()  # Your zone definitions
    
    zone_robustness = yemen_robustness.test_currency_zone_robustness(
        data=data,
        model=exchange_rate_passthrough_model,
        baseline_zones=baseline_zones
    )
    
    print(f"   Zone definition stability: {zone_robustness.effect_stability:.2f}")
    print(f"   Critical markets: {', '.join(zone_robustness.critical_markets[:5])}")
    
    if zone_robustness.effect_stability < 0.7:
        print("   ⚠️  WARNING: Results sensitive to zone definitions!")
    
    # Run comprehensive robustness
    print("\n6. Running comprehensive robustness tests...")
    print("   This will take several minutes...")
    
    # Define H1-specific placebo tests
    h1_placebo_specs = [
        {
            'name': 'Pre-fragmentation placebo',
            'type': 'fake_treatment_time',
            'fake_date': '2018-01-01',
            'description': 'Before currency fragmentation began'
        },
        {
            'name': 'Non-tradable goods placebo',
            'type': 'unaffected_outcome',
            'placebo_outcome': 'service_prices',
            'description': 'Services should show less pass-through'
        },
        {
            'name': 'Stable period placebo',
            'type': 'subsample',
            'condition': 'exchange_rate_volatility < median',
            'description': 'No effect when exchange rate stable'
        }
    ]
    
    robust_results = general_robustness.run_comprehensive_test(
        data=data,
        main_model=exchange_rate_passthrough_model,
        hypothesis_name="H1_Exchange_Rate_Mechanism",
        cluster_var='market_id',
        treatment_var='log_exchange_rate',
        outcome_var='log_price',
        custom_choices=custom_choices,
        placebo_specs=h1_placebo_specs,
        n_bootstrap=500  # Reduced for example
    )
    
    # Summarize results
    print("\n7. ROBUSTNESS SUMMARY")
    print("="*60)
    print(f"Overall Robustness Score: {robust_results.robustness_score:.1%}")
    print(f"Assessment: {robust_results.overall_assessment}")
    
    # Specification curve results
    if robust_results.specification_curve:
        sc = robust_results.specification_curve
        print(f"\nSpecification Curve Analysis:")
        print(f"  - Specifications tested: {sc['n_specifications']}")
        print(f"  - Percent significant: {sc['prop_significant']*100:.1f}%")
        print(f"  - Median effect: {sc['median_effect']:.3f}")
        print(f"  - Effect range: [{sc['min_effect']:.3f}, {sc['max_effect']:.3f}]")
        print(f"  - Same sign: {sc['pct_same_sign']:.1f}%")
    
    # Bootstrap results
    if robust_results.bootstrap_results:
        bs = robust_results.bootstrap_results
        print(f"\nBootstrap Inference:")
        print(f"  - Bootstrap SE: {bs['bootstrap_se']:.4f}")
        print(f"  - 95% CI: [{bs['ci_lower']:.3f}, {bs['ci_upper']:.3f}]")
        print(f"  - Convergence rate: {bs['convergence_rate']:.1%}")
    
    # Yemen-specific results
    print(f"\nYemen-Specific Tests:")
    print(f"  - Currency zone stability: {zone_robustness.effect_stability:.2f}")
    print(f"  - Exchange rate measurement: Tested {len(custom_choices[0]['options'])} sources")
    
    # Policy interpretation
    print("\n8. POLICY INTERPRETATION")
    print("="*60)
    
    if robust_results.robustness_score >= 0.8:
        print("✓ Results are HIGHLY ROBUST and suitable for policy decisions")
        print(f"\nKey Finding: A 10% currency depreciation leads to approximately")
        print(f"{main_results['implied_passthrough']/10:.1f}% increase in food prices.")
        print("\nThis confirms that currency fragmentation, not conflict itself,")
        print("explains the apparent 'Yemen Paradox' of lower prices in conflict zones.")
        
    elif robust_results.robustness_score >= 0.6:
        print("⚠️  Results are MODERATELY ROBUST with some caveats")
        print("\nMain finding holds but shows sensitivity to:")
        # Identify sources of fragility
        if zone_robustness.effect_stability < 0.8:
            print("  - Currency zone boundary definitions")
        if sc['prop_significant'] < 0.75:
            print("  - Model specification choices")
            
    else:
        print("❌ Results are FRAGILE and require further investigation")
        print("\nRecommendations:")
        print("  1. Collect additional data on zone boundaries")
        print("  2. Validate exchange rate data sources")
        print("  3. Test alternative identification strategies")
    
    # Save detailed results
    print(f"\n9. Detailed results saved to: {robust_results.report_path}")
    
    # Generate publication-ready visualizations
    print("\n10. Generating visualizations...")
    generate_h1_visualizations(robust_results, zone_robustness)
    
    return robust_results, zone_robustness


def generate_h1_visualizations(robust_results, zone_robustness):
    """Generate H1-specific visualizations."""
    import matplotlib.pyplot as plt
    
    # Create figure with H1-specific panels
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # Panel 1: Exchange rate divergence over time
    ax1 = axes[0, 0]
    plot_exchange_rate_divergence(ax1)
    ax1.set_title('Exchange Rate Divergence')
    
    # Panel 2: Price differences by zone
    ax2 = axes[0, 1]
    plot_price_differences_by_zone(ax2)
    ax2.set_title('Price Differences by Currency Zone')
    
    # Panel 3: Specification curve
    ax3 = axes[1, 0]
    plot_h1_specification_curve(ax3, robust_results)
    ax3.set_title('Specification Curve: Pass-through Estimates')
    
    # Panel 4: Zone stability map
    ax4 = axes[1, 1]
    plot_zone_stability_map(ax4, zone_robustness)
    ax4.set_title('Currency Zone Assignment Stability')
    
    plt.suptitle('H1 Robustness Analysis: Exchange Rate Mechanism', fontsize=16)
    plt.tight_layout()
    plt.savefig('results/robustness/h1_summary_figure.pdf', dpi=300)
    print("   Saved summary figure")


# Helper functions (implement based on your data structure)
def load_yemen_panel_data():
    """Load and prepare panel data for analysis."""
    # This is a placeholder - implement based on your data
    # Should return DataFrame with required columns
    pass

def get_baseline_currency_zones():
    """Load baseline currency zone definitions."""
    # Return dict mapping market_id to zone ('north' or 'south')
    pass

def plot_exchange_rate_divergence(ax):
    """Plot how exchange rates diverged between zones."""
    pass

def plot_price_differences_by_zone(ax):
    """Plot price differences between currency zones."""
    pass

def plot_h1_specification_curve(ax, results):
    """Plot specification curve for H1."""
    pass

def plot_zone_stability_map(ax, zone_robustness):
    """Map showing which markets have stable zone assignments."""
    pass


if __name__ == "__main__":
    # Run the complete robustness analysis
    results, zone_results = run_h1_robustness_analysis()
    
    # Print key takeaway
    print("\n" + "="*60)
    print("KEY TAKEAWAY:")
    if results.robustness_score >= 0.8:
        print("Currency fragmentation robustly explains Yemen's price patterns.")
        print("Policy makers can confidently use exchange rate analysis for")
        print("humanitarian aid planning and market intervention design.")
    else:
        print("Results show some fragility. See detailed report for")
        print("conditions under which the mechanism holds.")