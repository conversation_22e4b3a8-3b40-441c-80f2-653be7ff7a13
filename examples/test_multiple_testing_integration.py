"""
Test integration between methodology validator and multiple testing framework.

This demonstrates how the system enforces proper multiple testing corrections:
- <PERSON><PERSON><PERSON><PERSON> correction for primary hypotheses (H1-H5)
- Benjamini-<PERSON><PERSON>berg FDR control for secondary hypotheses (H6-H10)
"""

import pandas as pd
import numpy as np
from datetime import datetime

from src.core.validation.methodology_validator import (
    MethodologyValidator,
    AnalysisType
)
from src.core.models.hypothesis_testing.multiple_testing_framework import (
    MultipleTestingFramework,
    MultipleTestingMethod,
    HypothesisTest,
    PowerAnalysisType
)


def create_valid_test_data():
    """Create a valid dataset that passes methodology validation."""
    dates = pd.date_range(start='2020-01-01', end='2023-12-31', freq='M')
    markets = [f"Market_{i}" for i in range(350)]  # 350 markets > 300 required
    
    data = []
    for date in dates:
        for market in markets:
            # Assign to currency zones
            market_num = int(market.split('_')[1])
            if market_num < 150:
                zone = 'HOUTHI'
                rate = 535 + np.random.normal(0, 5)
            elif market_num < 250:
                zone = 'GOVERNMENT' 
                rate = 2000 + np.random.normal(0, 50)
            else:
                zone = 'CONTESTED'
                rate = 1200 + np.random.normal(0, 100)
            
            price_yer = np.random.uniform(1000, 5000)
            price_usd = price_yer / rate
            
            data.append({
                'date': date,
                'market_id': market,
                'commodity': 'Wheat',
                'price_yer': price_yer,
                'price_usd': price_usd,
                'exchange_rate_used': rate,
                'currency_zone': zone,
                'exchange_rate_source': 'CBY_Aden' if zone == 'GOVERNMENT' else 'CBY_Sanaa'
            })
    
    return pd.DataFrame(data)


def simulate_hypothesis_tests():
    """Simulate test results for H1-H10."""
    # Primary hypotheses (H1-H5) - mix of significant and non-significant
    primary_results = [
        HypothesisTest(
            hypothesis_id="H1",
            test_statistic=3.24,
            p_value=0.001,  # Significant
            effect_size=0.45,
            standard_error=0.14,
            n_observations=10000,
            test_type="t_test"
        ),
        HypothesisTest(
            hypothesis_id="H2", 
            test_statistic=2.15,
            p_value=0.032,  # Borderline
            effect_size=0.28,
            standard_error=0.13,
            n_observations=10000,
            test_type="t_test"
        ),
        HypothesisTest(
            hypothesis_id="H3",
            test_statistic=1.45,
            p_value=0.147,  # Not significant
            effect_size=0.15,
            standard_error=0.10,
            n_observations=10000,
            test_type="t_test"
        ),
        HypothesisTest(
            hypothesis_id="H4",
            test_statistic=2.89,
            p_value=0.004,  # Significant
            effect_size=0.38,
            standard_error=0.13,
            n_observations=10000,
            test_type="t_test"
        ),
        HypothesisTest(
            hypothesis_id="H5",
            test_statistic=0.95,
            p_value=0.342,  # Not significant
            effect_size=0.08,
            standard_error=0.08,
            n_observations=10000,
            test_type="t_test"
        )
    ]
    
    # Secondary hypotheses (H6-H10)
    secondary_results = [
        HypothesisTest(
            hypothesis_id="H6",
            test_statistic=2.45,
            p_value=0.014,  # Significant
            effect_size=0.32,
            standard_error=0.13,
            n_observations=8000,
            test_type="t_test"
        ),
        HypothesisTest(
            hypothesis_id="H7",
            test_statistic=1.89,
            p_value=0.059,  # Borderline
            effect_size=0.22,
            standard_error=0.12,
            n_observations=8000,
            test_type="t_test"
        ),
        HypothesisTest(
            hypothesis_id="H8",
            test_statistic=3.12,
            p_value=0.002,  # Significant
            effect_size=0.41,
            standard_error=0.13,
            n_observations=8000,
            test_type="t_test"
        ),
        HypothesisTest(
            hypothesis_id="H9",
            test_statistic=1.23,
            p_value=0.219,  # Not significant
            effect_size=0.12,
            standard_error=0.10,
            n_observations=8000,
            test_type="t_test"
        ),
        HypothesisTest(
            hypothesis_id="H10",
            test_statistic=2.67,
            p_value=0.008,  # Significant
            effect_size=0.35,
            standard_error=0.13,
            n_observations=8000,
            test_type="t_test"
        )
    ]
    
    return primary_results, secondary_results


def main():
    """Demonstrate integrated methodology validation and multiple testing."""
    print("=" * 80)
    print("INTEGRATED METHODOLOGY VALIDATION & MULTIPLE TESTING DEMONSTRATION")
    print("=" * 80)
    
    # Step 1: Create and validate data
    print("\n1. Creating and validating data...")
    data = create_valid_test_data()
    
    validator = MethodologyValidator()
    is_valid, report = validator.validate_analysis_inputs(
        data,
        AnalysisType.HYPOTHESIS_TESTING,
        hypothesis_tests=["H1", "H2", "H3", "H4", "H5", "H6", "H7", "H8", "H9", "H10"]
    )
    
    print(f"Data validation: {'PASSED' if is_valid else 'FAILED'}")
    print(f"Currency conversion compliance: {report.currency_conversion_compliance:.1%}")
    print(f"Statistical power adequate: {report.statistical_power_adequate}")
    
    if not is_valid:
        print("\n❌ Cannot proceed - data validation failed")
        return
    
    # Step 2: Set up multiple testing framework
    print("\n2. Setting up multiple testing framework...")
    mt_framework = MultipleTestingFramework(
        primary_alpha=0.05,    # For primary hypotheses
        secondary_alpha=0.10,  # For secondary hypotheses
        target_power=0.80
    )
    
    # Register hypothesis hierarchy
    mt_framework.register_hypothesis_hierarchy(
        primary=["H1", "H2", "H3", "H4", "H5"],
        secondary=["H6", "H7", "H8", "H9", "H10"]
    )
    
    # Step 3: Simulate hypothesis test results
    print("\n3. Running hypothesis tests...")
    primary_results, secondary_results = simulate_hypothesis_tests()
    all_results = primary_results + secondary_results
    
    # Display original results
    print("\nOriginal test results:")
    print("-" * 60)
    print(f"{'Hypothesis':<12} {'Test Stat':<10} {'P-value':<10} {'Significant?':<15}")
    print("-" * 60)
    
    for result in all_results:
        sig = "Yes" if result.p_value < 0.05 else "No"
        print(f"{result.hypothesis_id:<12} {result.test_statistic:<10.3f} {result.p_value:<10.4f} {sig:<15}")
    
    # Step 4: Apply multiple testing corrections
    print("\n4. Applying multiple testing corrections...")
    
    # Apply hierarchical correction (Bonferroni for H1-H5, FDR for H6-H10)
    corrected_results = mt_framework.apply_multiple_testing_correction(
        all_results,
        method=MultipleTestingMethod.BONFERRONI,  # Will be applied hierarchically
        hierarchical=True
    )
    
    # Display corrected results
    print("\nCorrected test results:")
    print("-" * 80)
    print(f"{'Hypothesis':<12} {'Original P':<12} {'Corrected P':<12} {'Method':<15} {'Reject H0?':<12}")
    print("-" * 80)
    
    for hyp_id, corrected_p in corrected_results.corrected_p_values.items():
        original = next(r for r in all_results if r.hypothesis_id == hyp_id)
        reject = hyp_id in corrected_results.significant_hypotheses
        
        # Determine which correction was applied
        if hyp_id in ["H1", "H2", "H3", "H4", "H5"]:
            method = "Bonferroni"
            alpha = mt_framework.primary_alpha
        else:
            method = "FDR (BH)"
            alpha = mt_framework.secondary_alpha
            
        print(f"{hyp_id:<12} {original.p_value:<12.4f} {corrected_p:<12.4f} {method:<15} {'Yes' if reject else 'No':<12}")
    
    # Step 5: Power analysis
    print("\n5. Conducting power analysis...")
    
    effect_sizes = {r.hypothesis_id: r.effect_size for r in all_results}
    sample_sizes = {r.hypothesis_id: r.n_observations for r in all_results}
    
    power_results = mt_framework.conduct_power_analysis(
        effect_sizes=effect_sizes,
        sample_sizes=sample_sizes,
        analysis_type=PowerAnalysisType.EX_POST
    )
    
    print(f"\nAdequately powered tests: {len(power_results.adequately_powered_tests)}")
    print(f"Underpowered tests: {len(power_results.underpowered_tests)}")
    
    if power_results.underpowered_tests:
        print("\nUnderpowered tests:")
        for hyp_id in power_results.underpowered_tests:
            power = power_results.statistical_power[hyp_id]
            print(f"  - {hyp_id}: Power = {power:.2f}")
    
    # Step 6: Generate comprehensive report
    print("\n6. Summary Report")
    print("-" * 80)
    
    summary_report = mt_framework.generate_testing_report(
        corrected_results,
        power_results
    )
    
    print(summary_report)
    
    # Step 7: Key insights
    print("\n7. Key Insights:")
    print("-" * 50)
    
    # Primary hypotheses (H1-H5)
    primary_sig = [h for h in corrected_results.significant_hypotheses if h in ["H1", "H2", "H3", "H4", "H5"]]
    print(f"\nPrimary hypotheses (H1-H5) with Bonferroni correction (α = {mt_framework.primary_alpha/5:.3f}):")
    print(f"  - {len(primary_sig)} out of 5 remain significant after correction")
    print(f"  - H2 had p=0.032, but needs p<{mt_framework.primary_alpha/5:.3f} with Bonferroni")
    
    # Secondary hypotheses (H6-H10)
    secondary_sig = [h for h in corrected_results.significant_hypotheses if h in ["H6", "H7", "H8", "H9", "H10"]]
    print(f"\nSecondary hypotheses (H6-H10) with FDR control:")
    print(f"  - {len(secondary_sig)} out of 5 significant with FDR control")
    print(f"  - FDR method is less conservative than Bonferroni")
    
    print("\n" + "=" * 80)
    print("DEMONSTRATION COMPLETE")
    print("=" * 80)
    
    # Final validation message
    print("\n✅ This analysis followed proper methodology:")
    print("   1. All prices converted to USD before analysis")
    print("   2. Proper multiple testing corrections applied")
    print("   3. Statistical power assessed")
    print("   4. Hierarchical hypothesis structure respected")


if __name__ == "__main__":
    main()