#!/usr/bin/env python3
"""Test script to verify ML clustering integration with Tier 1 runner."""

import sys
from pathlib import Path
# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from src.application.commands.run_three_tier_analysis import RunThreeTierAnalysisCommand
from src.core.utils.logging import get_logger

logger = get_logger(__name__)


async def test_tier1_with_clustering():
    """Test Tier 1 analysis with currency-aware clustering enabled."""
    
    # Create mock panel data for testing
    # This simulates data with two currency zones
    np.random.seed(42)
    
    # Generate dates
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    
    # Markets in different zones
    northern_markets = ['market_N1', 'market_N2', 'market_N3', 'market_N4', 'market_N5']
    southern_markets = ['market_S1', 'market_S2', 'market_S3', 'market_S4', 'market_S5']
    all_markets = northern_markets + southern_markets
    
    # Create panel data
    data = []
    for date in dates:
        for market in all_markets:
            # Determine zone
            if market.startswith('market_N'):
                zone = 'HOUTHI'
                exchange_rate = 535.0 + np.random.normal(0, 10)
                base_price = 1000 + np.random.normal(0, 100)
                governorate = 'Sana\'a'
                lat, lon = 15.35 + np.random.normal(0, 0.1), 44.20 + np.random.normal(0, 0.1)
            else:
                zone = 'GOVERNMENT'
                exchange_rate = 2000.0 + np.random.normal(0, 50)
                base_price = 3000 + np.random.normal(0, 200)
                governorate = 'Aden'
                lat, lon = 12.78 + np.random.normal(0, 0.1), 45.02 + np.random.normal(0, 0.1)
            
            # Create observation
            obs = {
                'market_id': market,
                'date': date,
                'commodity': 'wheat',
                'price_yer': base_price,
                'exchange_rate_used': exchange_rate,
                'currency_zone': zone,
                'price_usd': base_price / exchange_rate,
                'governorate': governorate,
                'latitude': lat,
                'longitude': lon,
                'conflict_intensity': np.random.uniform(0, 0.5),
                'distance_to_port': np.random.uniform(10, 100)
            }
            data.append(obs)
    
    df = pd.DataFrame(data)
    
    # Create command with clustering enabled
    command = RunThreeTierAnalysisCommand(
        start_date=datetime(2023, 1, 1),
        end_date=datetime(2023, 12, 31),
        commodity_codes=['wheat'],
        tier1_config={
            "model": "two_way_fixed_effects",
            "se_type": "driscoll_kraay",
            "entity_trends": False,
            "log_transform": True,
            "use_clustering": True,  # Enable clustering
            "clustering_config": {
                "n_clusters_per_zone": {
                    "HOUTHI": 2,
                    "GOVERNMENT": 2
                }
            }
        },
        run_diagnostics=False  # Skip diagnostics for this test
    )
    
    # Mock the necessary components for testing
    from unittest.mock import AsyncMock, MagicMock
    
    # Mock repositories
    market_repo = AsyncMock()
    price_repo = AsyncMock()
    
    # Mock market data
    from src.core.domain.market.entities import Market
    from src.core.domain.shared.value_objects import Coordinates
    markets = []
    for market_id in all_markets:
        if market_id.startswith('market_N'):
            gov = 'Sana\'a'
            lat, lon = 15.35, 44.20
        else:
            gov = 'Aden'
            lat, lon = 12.78, 45.02
            
        market = MagicMock()
        market.market_id.value = market_id
        market.governorate = gov
        market.district = 'District1'
        market.coordinates = Coordinates(latitude=lat, longitude=lon)
        market.market_type.value = 'retail'
        market.is_accessible = True
        markets.append(market)
    
    market_repo.find_all.return_value = markets
    
    # Mock price data
    price_records = []
    for _, row in df.iterrows():
        record = MagicMock()
        record.market_id.value = row['market_id']
        record.date = row['date']
        record.price_per_unit = row['price_yer']
        record.commodity_code = row['commodity']
        record.source = 'test'
        record.unit_of_measure = 'kg'
        record.quality_grade = 'standard'
        price_records.append(record)
    
    price_repo.find_by_date_range.return_value = price_records
    
    # Mock orchestrator
    orchestrator = AsyncMock()
    orchestrator.update_progress = AsyncMock()
    
    # Mock estimator service
    estimator_service = AsyncMock()
    
    # Create mock result
    mock_result = MagicMock()
    mock_result.params = pd.Series({
        'conflict_intensity': -0.05,
        'distance_to_port': 0.02,
        'cluster': 0.10,  # Cluster effect
        'const': 5.0
    })
    mock_result.pvalues = pd.Series({
        'conflict_intensity': 0.001,
        'distance_to_port': 0.05,
        'cluster': 0.01,
        'const': 0.000
    })
    mock_result.standard_errors = mock_result.pvalues * 0.1
    mock_result.residuals = pd.Series(np.random.normal(0, 0.1, 100))
    mock_result.rsquared = 0.85
    
    estimator_service.estimate.return_value = mock_result
    
    # Create and run Tier 1 runner
    from src.application.analysis_tiers.tier1_runner import Tier1Runner
    
    runner = Tier1Runner(
        market_repo=market_repo,
        price_repo=price_repo,
        orchestrator=orchestrator,
        estimator_service=estimator_service
    )
    
    # Run analysis
    try:
        result = await runner.run(command, "test_analysis_123")
        
        # Verify results
        logger.info("=== Tier 1 Analysis with Clustering Results ===")
        logger.info(f"Model: {result['model']}")
        logger.info(f"Clustering applied: {result['metadata']['clustering_applied']}")
        logger.info(f"Number of clusters: {result['metadata']['n_clusters']}")
        
        if result['clustering']:
            clustering = result['clustering']
            logger.info(f"Total clusters: {clustering.total_clusters}")
            logger.info(f"Clusters by zone: {clustering.clusters_by_zone}")
            logger.info(f"Overall silhouette score: {clustering.overall_silhouette:.3f}")
            logger.info(f"Methodology compliant: {clustering.methodology_compliance}")
        
        # Check model results
        model_result = result['result']
        logger.info("\n=== Model Coefficients ===")
        for var, coef in model_result.params.items():
            p_val = model_result.pvalues[var]
            logger.info(f"{var}: {coef:.4f} (p={p_val:.4f})")
        
        logger.info(f"\nR-squared: {model_result.rsquared:.3f}")
        
        logger.info("\n✅ Tier 1 clustering integration test PASSED!")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(test_tier1_with_clustering())