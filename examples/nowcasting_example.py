"""
Example script demonstrating nowcasting and early warning system for Yemen markets.

This example shows how to:
1. Run nowcasting models for price predictions
2. Generate early warning signals for humanitarian intervention
3. Visualize forecast results and warning dashboard
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns

# Import nowcasting components
from src.infrastructure.estimators.nowcasting_estimators import (
    NowcastingOrchestrator,
    DynamicFactorEstimator,
    SARIMAXEstimator,
    MachineLearningEstimator,
    EnsembleEstimator,
    EarlyWarningEstimator
)
from src.core.models.nowcasting import EarlyWarningSystem

# For visualization
plt.style.use('seaborn-v0_8-darkgrid')


async def run_nowcasting_example():
    """Run comprehensive nowcasting example."""
    
    print("=" * 80)
    print("Yemen Market Integration - Nowcasting and Early Warning Example")
    print("=" * 80)
    
    # 1. Generate sample data (in practice, load from actual sources)
    print("\n1. Loading market data...")
    panel_data = generate_sample_panel_data()
    print(f"   - Loaded {len(panel_data)} observations")
    print(f"   - Markets: {panel_data.index.get_level_values('market_id').nunique()}")
    print(f"   - Time period: {panel_data.index.get_level_values('date').min()} to {panel_data.index.get_level_values('date').max()}")
    
    # 2. Initialize nowcasting orchestrator
    print("\n2. Initializing nowcasting system...")
    orchestrator = NowcastingOrchestrator()
    
    # Define markets and commodities to analyze
    markets = ['Sana\'a', 'Aden', 'Hodeidah', 'Taiz', 'Ibb']
    commodities = ['wheat_flour', 'rice', 'fuel_diesel']
    
    # 3. Run comprehensive nowcasting
    print("\n3. Running nowcasting models...")
    print("   - Methods: Dynamic Factor, SARIMAX, Random Forest ensemble")
    print("   - Forecast horizon: 3 months")
    
    results = await orchestrator.run_comprehensive_nowcasting(
        panel_data=panel_data,
        markets=markets,
        commodities=commodities,
        methods=['dynamic_factor', 'sarimax', 'random_forest'],
        generate_warnings=True,
        forecast_horizon=3,
        n_factors=3,
        baseline_period='2019'
    )
    
    # 4. Display nowcast results
    print("\n4. Nowcasting Results:")
    print("-" * 40)
    
    if 'panel_nowcast' in results and results['panel_nowcast']:
        print("   Panel-wide Dynamic Factor Model:")
        dfm_result = results['panel_nowcast']['nowcast']
        print(f"     - Forecast periods: {dfm_result.forecast_horizon}")
        print(f"     - Number of factors: {results['panel_nowcast']['diagnostics']['n_factors']}")
        
    print("\n   Market-Commodity Specific Forecasts:")
    for key, result in results['market_commodity_nowcasts'].items():
        if result and 'nowcast' in result:
            nowcast = result['nowcast']
            print(f"     - {key}:")
            print(f"       Last observed: ${nowcast.metadata.get('last_observed', 'N/A'):.2f}")
            print(f"       1-month forecast: ${nowcast.point_forecast.iloc[0]:.2f}")
            print(f"       3-month forecast: ${nowcast.point_forecast.iloc[-1]:.2f}")
            
    # 5. Display early warning signals
    print("\n5. Early Warning Signals:")
    print("-" * 40)
    
    if 'warnings' in results and results['warnings']:
        warnings = results['warnings']
        print(f"   Total warnings generated: {len(warnings)}")
        
        # Group by severity
        severity_counts = {}
        for warning in warnings:
            severity_counts[warning.severity] = severity_counts.get(warning.severity, 0) + 1
            
        print("\n   Warnings by severity:")
        for severity in ['critical', 'high', 'medium', 'low']:
            count = severity_counts.get(severity, 0)
            if count > 0:
                print(f"     - {severity.upper()}: {count} warnings")
                
        # Show critical warnings
        critical_warnings = [w for w in warnings if w.severity == 'critical']
        if critical_warnings:
            print("\n   CRITICAL WARNINGS requiring immediate action:")
            for i, warning in enumerate(critical_warnings[:5], 1):
                print(f"\n     Warning #{i}:")
                print(f"       Market: {warning.market_id}")
                print(f"       Commodity: {warning.commodity}")
                print(f"       Type: {warning.signal_type}")
                print(f"       Predicted date: {warning.predicted_date.strftime('%Y-%m-%d')}")
                print(f"       Probability: {warning.probability:.1%}")
                print(f"       Action: {warning.recommended_action}")
                
        # Generate warning dashboard
        if 'warning_dashboard' in results:
            dashboard = results['warning_dashboard']
            print("\n   Warning Dashboard Summary:")
            print(f"     - Markets at risk: {dashboard['market_id'].nunique()}")
            print(f"     - Commodities affected: {dashboard['commodity'].nunique()}")
            print(f"     - Average days until event: {dashboard['days_until'].mean():.1f}")
            
    else:
        print("   No warnings generated (market conditions stable)")
        
    # 6. Visualize results
    print("\n6. Generating visualizations...")
    visualize_nowcast_results(results, markets[:3], commodities[:2])
    
    # 7. Export results for operational use
    print("\n7. Exporting results...")
    export_results_for_operations(results)
    
    print("\n" + "=" * 80)
    print("Nowcasting analysis complete!")
    print("Results exported to ./results/nowcasting/")
    print("=" * 80)


def generate_sample_panel_data():
    """Generate sample panel data for demonstration."""
    # Create date range
    dates = pd.date_range('2019-01-01', '2024-01-01', freq='M')
    
    # Markets with currency zones
    market_info = {
        'Sana\'a': {'zone': 'north', 'base_price_mult': 0.8},
        'Aden': {'zone': 'south', 'base_price_mult': 1.2},
        'Hodeidah': {'zone': 'north', 'base_price_mult': 0.85},
        'Taiz': {'zone': 'contested', 'base_price_mult': 1.0},
        'Ibb': {'zone': 'north', 'base_price_mult': 0.9},
        'Hadramaut': {'zone': 'south', 'base_price_mult': 1.15},
        'Al Bayda': {'zone': 'contested', 'base_price_mult': 0.95}
    }
    
    # Commodities with base prices (USD)
    commodities = {
        'wheat_flour': 0.8,
        'rice': 1.2,
        'fuel_diesel': 0.9,
        'cooking_oil': 1.5,
        'beans': 1.0
    }
    
    # Generate data
    data = []
    np.random.seed(42)
    
    for date in dates:
        for market, info in market_info.items():
            for commodity, base_price in commodities.items():
                # Base price with market adjustment
                price = base_price * info['base_price_mult']
                
                # Add temporal trend
                months_elapsed = (date - dates[0]).days / 30
                trend = 0.002 * months_elapsed  # 0.2% monthly inflation
                
                # Add seasonality
                seasonal = 0.1 * np.sin(2 * np.pi * date.month / 12)
                
                # Add random variation
                noise = np.random.normal(0, 0.1)
                
                # Conflict effect (higher in contested zones)
                if info['zone'] == 'contested':
                    conflict_effect = np.random.exponential(0.1)
                else:
                    conflict_effect = 0
                    
                # Calculate final USD price
                usd_price = price * (1 + trend + seasonal + noise + conflict_effect)
                usd_price = max(0.1, usd_price)  # Ensure positive
                
                data.append({
                    'market_id': market,
                    'date': date,
                    'commodity': commodity,
                    'usd_price': usd_price,
                    'currency': 'USD',
                    'currency_zone': info['zone'],
                    'conflict_intensity': conflict_effect * 10  # Scale for visibility
                })
                
    # Create DataFrame
    df = pd.DataFrame(data)
    
    # Add some missing data (realistic for conflict settings)
    missing_mask = np.random.random(len(df)) < 0.05  # 5% missing
    df.loc[missing_mask, 'usd_price'] = np.nan
    
    # Set MultiIndex
    df = df.set_index(['market_id', 'date'])
    
    return df


def visualize_nowcast_results(results, markets_to_plot, commodities_to_plot):
    """Create visualizations of nowcast results."""
    
    # Create figure with subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Yemen Market Nowcasting Results', fontsize=16)
    
    # 1. Plot individual market forecasts
    ax1 = axes[0, 0]
    
    for i, market in enumerate(markets_to_plot):
        commodity = commodities_to_plot[0]
        key = f"{market}_{commodity}"
        
        if key in results['market_commodity_nowcasts']:
            result = results['market_commodity_nowcasts'][key]
            if 'nowcast' in result:
                nowcast = result['nowcast']
                
                # Plot forecast with confidence intervals
                forecast_dates = nowcast.point_forecast.index
                ax1.plot(forecast_dates, nowcast.point_forecast.values,
                        label=market, marker='o', linewidth=2)
                
                # Add confidence interval
                if 0.95 in nowcast.prediction_intervals:
                    ci = nowcast.prediction_intervals[0.95]
                    ax1.fill_between(forecast_dates,
                                   ci['lower'].values,
                                   ci['upper'].values,
                                   alpha=0.2)
                    
    ax1.set_title(f'{commodity.replace("_", " ").title()} Price Forecasts')
    ax1.set_xlabel('Date')
    ax1.set_ylabel('Price (USD)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Plot warning severity distribution
    ax2 = axes[0, 1]
    
    if 'warnings' in results and results['warnings']:
        severity_counts = {}
        for warning in results['warnings']:
            severity_counts[warning.severity] = severity_counts.get(warning.severity, 0) + 1
            
        severities = ['critical', 'high', 'medium', 'low']
        counts = [severity_counts.get(s, 0) for s in severities]
        colors = ['#d62728', '#ff7f0e', '#ffbb78', '#98df8a']
        
        bars = ax2.bar(severities, counts, color=colors)
        ax2.set_title('Early Warning Signals by Severity')
        ax2.set_xlabel('Severity Level')
        ax2.set_ylabel('Number of Warnings')
        
        # Add value labels on bars
        for bar, count in zip(bars, counts):
            if count > 0:
                ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                        str(count), ha='center', va='bottom')
    else:
        ax2.text(0.5, 0.5, 'No warnings generated', ha='center', va='center',
                transform=ax2.transAxes, fontsize=12)
        ax2.set_xlim(0, 1)
        ax2.set_ylim(0, 1)
        
    # 3. Plot forecast accuracy metrics (if available)
    ax3 = axes[1, 0]
    
    if 'ensemble_nowcasts' in results and 'full_panel' in results['ensemble_nowcasts']:
        ensemble = results['ensemble_nowcasts']['full_panel']
        if 'diagnostics' in ensemble and 'model_weights' in ensemble['diagnostics']:
            weights = ensemble['diagnostics']['model_weights']
            
            models = list(weights.keys())
            weight_values = list(weights.values())
            
            bars = ax3.bar(models, weight_values, color='skyblue', edgecolor='navy')
            ax3.set_title('Ensemble Model Weights')
            ax3.set_xlabel('Model')
            ax3.set_ylabel('Weight')
            ax3.set_ylim(0, 1)
            
            # Rotate x labels
            plt.setp(ax3.xaxis.get_majorticklabels(), rotation=45, ha='right')
            
            # Add value labels
            for bar, weight in zip(bars, weight_values):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{weight:.2f}', ha='center', va='bottom')
    
    # 4. Plot warning timeline
    ax4 = axes[1, 1]
    
    if 'warning_dashboard' in results:
        dashboard = results['warning_dashboard']
        
        # Group by predicted date and severity
        timeline_data = dashboard.groupby(['predicted_date', 'severity']).size().unstack(fill_value=0)
        
        # Plot stacked bar chart
        timeline_data.plot(kind='bar', stacked=True, ax=ax4,
                          color=['#d62728', '#ff7f0e', '#ffbb78', '#98df8a'])
        ax4.set_title('Warning Timeline')
        ax4.set_xlabel('Predicted Date')
        ax4.set_ylabel('Number of Warnings')
        ax4.legend(title='Severity', bbox_to_anchor=(1.05, 1), loc='upper left')
        
        # Rotate x labels
        plt.setp(ax4.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    plt.tight_layout()
    plt.savefig('nowcast_results.png', dpi=300, bbox_inches='tight')
    print("   - Saved visualization to nowcast_results.png")


def export_results_for_operations(results):
    """Export results in formats suitable for operational use."""
    import os
    import json
    
    # Create output directory
    output_dir = './results/nowcasting'
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. Export warning dashboard as CSV
    if 'warning_dashboard' in results:
        dashboard = results['warning_dashboard']
        dashboard.to_csv(f'{output_dir}/early_warning_dashboard.csv', index=False)
        print(f"   - Exported warning dashboard ({len(dashboard)} warnings)")
        
    # 2. Export forecasts as JSON for API consumption
    forecast_data = {}
    for key, result in results.get('market_commodity_nowcasts', {}).items():
        if result and 'nowcast' in result:
            nowcast = result['nowcast']
            forecast_data[key] = {
                'point_forecast': nowcast.point_forecast.to_dict(),
                'confidence_intervals': {
                    str(level): {
                        'lower': ci['lower'].to_dict(),
                        'upper': ci['upper'].to_dict()
                    }
                    for level, ci in nowcast.prediction_intervals.items()
                },
                'method': nowcast.method,
                'horizon': nowcast.forecast_horizon
            }
            
    with open(f'{output_dir}/forecasts.json', 'w') as f:
        json.dump(forecast_data, f, indent=2, default=str)
    print(f"   - Exported {len(forecast_data)} market forecasts")
    
    # 3. Create operational summary
    summary = {
        'analysis_date': datetime.now().isoformat(),
        'markets_analyzed': results['summary']['n_markets_analyzed'],
        'successful_nowcasts': results['summary']['n_successful_nowcasts'],
        'forecast_horizon': results['summary']['forecast_horizon'],
        'warning_summary': results['summary']['warning_summary'],
        'critical_actions_required': []
    }
    
    # Add critical actions
    if 'warnings' in results:
        critical_warnings = [w for w in results['warnings'] if w.severity == 'critical']
        for warning in critical_warnings[:5]:
            summary['critical_actions_required'].append({
                'market': warning.market_id,
                'commodity': warning.commodity,
                'action': warning.recommended_action,
                'date': warning.predicted_date.isoformat()
            })
            
    with open(f'{output_dir}/operational_summary.json', 'w') as f:
        json.dump(summary, f, indent=2)
    print("   - Exported operational summary")


if __name__ == "__main__":
    # Run the example
    asyncio.run(run_nowcasting_example())