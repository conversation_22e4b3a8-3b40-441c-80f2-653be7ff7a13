"""
Test script to demonstrate the new methodology validator functionality.

This shows how the validator enforces critical research methodology requirements
before any analysis can proceed.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.core.validation.methodology_validator import (
    MethodologyValidator, 
    AnalysisType,
    MethodologyViolation
)


def create_invalid_data():
    """Create a dataset that violates methodology requirements."""
    dates = pd.date_range(start='2020-01-01', end='2022-12-31', freq='M')
    markets = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah', 'Marib']
    commodities = ['Wheat', 'Rice', 'Sugar']
    
    data = []
    for date in dates:
        for market in markets:
            for commodity in commodities:
                # Create data WITHOUT currency conversion (the critical error!)
                data.append({
                    'date': date,
                    'market_id': market,
                    'commodity': commodity,
                    'price_yer': np.random.uniform(500, 2000),
                    # Missing: price_usd, exchange_rate_used, currency_zone
                })
    
    return pd.DataFrame(data)


def create_partially_valid_data():
    """Create a dataset with some USD prices but missing other requirements."""
    dates = pd.date_range(start='2020-01-01', end='2022-12-31', freq='M')
    markets = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah', 'Marib']
    commodities = ['Wheat', 'Rice', 'Sugar']
    
    data = []
    for date in dates:
        for market in markets:
            for commodity in commodities:
                # Determine currency zone based on market
                if market in ['Sana\'a']:
                    zone = 'HOUTHI'
                    rate = 535 + np.random.normal(0, 5)
                elif market in ['Aden', 'Marib']:
                    zone = 'GOVERNMENT'
                    rate = 2000 + np.random.normal(0, 50)
                else:
                    zone = 'CONTESTED'
                    rate = 1200 + np.random.normal(0, 100)
                
                price_yer = np.random.uniform(500, 2000)
                
                # Only 70% have USD prices (violates 100% requirement)
                if np.random.random() > 0.3:
                    price_usd = price_yer / rate
                else:
                    price_usd = np.nan
                    rate = np.nan
                
                data.append({
                    'date': date,
                    'market_id': market,
                    'commodity': commodity,
                    'price_yer': price_yer,
                    'price_usd': price_usd,
                    'exchange_rate_used': rate,
                    'currency_zone': zone,
                    # Missing exchange rate source tracking
                })
    
    return pd.DataFrame(data)


def create_valid_data():
    """Create a dataset that meets all methodology requirements."""
    dates = pd.date_range(start='2020-01-01', end='2023-12-31', freq='M')  # 48 months
    
    # Ensure we have 300+ markets
    governorates = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah', 'Marib', 'Ibb', 'Hajjah', 
                   'Dhamar', 'Al-Bayda', 'Lahij', 'Abyan', 'Sa\'ada']
    districts_per_gov = 30  # Creates 360 markets total
    
    markets = []
    for gov in governorates:
        for i in range(districts_per_gov):
            markets.append(f"{gov}_District_{i+1}")
    
    commodities = ['Wheat', 'Rice', 'Sugar', 'Oil', 'Beans']
    
    data = []
    for date in dates:
        for market in markets:
            for commodity in commodities:
                # Determine currency zone based on governorate
                gov = market.split('_')[0]
                if gov in ['Sana\'a', 'Sa\'ada', 'Hajjah', 'Dhamar']:
                    zone = 'HOUTHI'
                    rate = 535 + np.random.normal(0, 5)
                elif gov in ['Aden', 'Marib', 'Lahij', 'Abyan']:
                    zone = 'GOVERNMENT'
                    rate = 2000 + np.random.normal(0, 50)
                else:
                    zone = 'CONTESTED'
                    rate = 1200 + np.random.normal(0, 100)
                
                price_yer = np.random.uniform(500, 5000)
                price_usd = price_yer / rate
                
                # Rotate through exchange rate sources
                sources = ['CBY_Aden', 'CBY_Sanaa', 'parallel_market']
                source = sources[hash(f"{date}{market}{commodity}") % 3]
                
                data.append({
                    'date': date,
                    'market_id': market,
                    'commodity': commodity,
                    'price_yer': price_yer,
                    'price_usd': price_usd,
                    'exchange_rate_used': rate,
                    'currency_zone': zone,
                    'exchange_rate_source': source,
                })
    
    df = pd.DataFrame(data)
    
    # Add 12% missing data (to stay under 88.4% coverage target)
    missing_indices = np.random.choice(df.index, size=int(len(df) * 0.12), replace=False)
    df = df.drop(missing_indices)
    
    return df


def main():
    """Demonstrate methodology validation."""
    validator = MethodologyValidator()
    
    print("=" * 80)
    print("YEMEN MARKET INTEGRATION - METHODOLOGY VALIDATOR DEMONSTRATION")
    print("=" * 80)
    
    # Test 1: Invalid data (missing currency conversion)
    print("\n1. Testing INVALID data (no currency conversion):")
    print("-" * 50)
    invalid_data = create_invalid_data()
    print(f"Created dataset with {len(invalid_data)} observations")
    print(f"Columns: {list(invalid_data.columns)}")
    
    try:
        is_valid, report = validator.validate_analysis_inputs(
            invalid_data, 
            AnalysisType.PANEL_ANALYSIS,
            hypothesis_tests=["H1", "H2"]
        )
        print(f"\nValidation result: {'PASSED' if is_valid else 'FAILED'}")
        print(f"Critical failures: {len(report.critical_failures)}")
        if report.critical_failures:
            print("\nCritical issues:")
            for failure in report.critical_failures:
                print(f"  - {failure}")
    except Exception as e:
        print(f"Error during validation: {e}")
    
    # Test 2: Partially valid data
    print("\n\n2. Testing PARTIALLY VALID data (70% USD coverage):")
    print("-" * 50)
    partial_data = create_partially_valid_data()
    print(f"Created dataset with {len(partial_data)} observations")
    
    is_valid, report = validator.validate_analysis_inputs(
        partial_data,
        AnalysisType.HYPOTHESIS_TESTING,
        hypothesis_tests=["H1", "H2", "H3", "H4", "H5"]  # Primary hypotheses
    )
    
    print(f"\nValidation result: {'PASSED' if is_valid else 'FAILED'}")
    print(f"\nCompliance metrics:")
    print(f"  - Currency conversion: {report.currency_conversion_compliance:.1%}")
    print(f"  - Exchange rate coverage: {report.exchange_rate_coverage:.1%}")
    print(f"  - Zone classification: {report.zone_classification_coverage:.1%}")
    print(f"  - Statistical power: {'Adequate' if report.statistical_power_adequate else 'Insufficient'}")
    
    # Test 3: Valid data
    print("\n\n3. Testing VALID data (meets all requirements):")
    print("-" * 50)
    valid_data = create_valid_data()
    print(f"Created dataset with {len(valid_data)} observations")
    print(f"Markets: {valid_data['market_id'].nunique()}")
    print(f"Time periods: {valid_data['date'].nunique()}")
    
    is_valid, report = validator.validate_analysis_inputs(
        valid_data,
        AnalysisType.PANEL_ANALYSIS,
        hypothesis_tests=["H1", "H2"],
        additional_checks=["outlier_detection", "temporal_gaps"]
    )
    
    print(f"\nValidation result: {'PASSED' if is_valid else 'FAILED'}")
    print(f"\nCompliance summary:")
    print(f"  - Currency conversion: {report.currency_conversion_compliance:.1%}")
    print(f"  - Data coverage: {report.data_coverage_percentage:.1%}")
    print(f"  - Statistical power: {'✅' if report.statistical_power_adequate else '❌'}")
    
    if report.warnings:
        print(f"\nWarnings ({len(report.warnings)}):")
        for warning in report.warnings[:3]:  # Show first 3
            print(f"  - {warning}")
    
    # Test 4: Demonstrate enforcement
    print("\n\n4. Demonstrating enforcement (trying to analyze invalid data):")
    print("-" * 50)
    
    try:
        cleaned_data = validator.enforce_pre_analysis_requirements(
            invalid_data,
            AnalysisType.PANEL_ANALYSIS
        )
        print("This shouldn't print - invalid data should be rejected")
    except MethodologyViolation as e:
        print(f"✅ Correctly blocked invalid analysis:")
        print(f"   {str(e).split(chr(10))[0]}")  # First line of error
    
    # Generate full report
    print("\n\n5. Full validation report example:")
    print("-" * 50)
    summary = validator.generate_validation_summary(report)
    print(summary[:1000] + "..." if len(summary) > 1000 else summary)
    
    print("\n" + "=" * 80)
    print("DEMONSTRATION COMPLETE")
    print("=" * 80)


if __name__ == "__main__":
    main()