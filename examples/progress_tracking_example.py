"""
Example of using progress tracking with data processors.

This example demonstrates how to integrate the rich progress tracking
UI components with data processors for beautiful progress displays.
"""

import asyncio
from datetime import datetime
import pandas as pd
from typing import List

from src.infrastructure.processors.processor_factory import (
    ProcessorFactory, ProcessorConfig, ProcessorType, SourceConfig
)
from src.infrastructure.processors.base_processor import (
    DataFrameProcessor, ValidationLevel
)
from src.infrastructure.ui.progress_tracker import (
    PipelineProgressTracker, track_progress
)
from src.infrastructure.ui.progress_adapter import (
    ProgressAdapter, create_rich_progress_callback
)
from src.infrastructure.caching.cache_manager import CacheManager
from src.infrastructure.data_quality.validation_framework import DataValidator


# Mock processor for demonstration
class DemoProcessor(DataFrameProcessor):
    """Demo processor that simulates processing with delays."""
    
    async def download(self) -> pd.DataFrame:
        """Simulate downloading data."""
        # Simulate network delay
        await asyncio.sleep(2)
        
        # Create sample data
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        data = []
        for date in dates:
            for market in ['Market_A', 'Market_B', 'Market_C']:
                data.append({
                    'date': date,
                    'market': market,
                    'commodity': 'wheat',
                    'price': 100 + (hash(str(date) + market) % 50)
                })
        
        return pd.DataFrame(data)
    
    async def transform(self, raw_data: pd.DataFrame) -> List[dict]:
        """Simulate transforming data."""
        # Simulate processing delay
        await asyncio.sleep(1)
        
        entities = []
        for _, row in raw_data.iterrows():
            entities.append({
                'market_id': row['market'],
                'date': row['date'],
                'price': row['price']
            })
        
        return entities
    
    async def aggregate(self, entities: List[dict]) -> pd.DataFrame:
        """Simulate aggregating data."""
        # Simulate processing delay
        await asyncio.sleep(0.5)
        
        df = pd.DataFrame(entities)
        return df.groupby('market_id').agg({
            'price': ['mean', 'std', 'count']
        }).reset_index()
    
    def get_required_columns(self) -> List[str]:
        return ['date', 'market', 'commodity', 'price']
    
    async def validate_specific(self, raw_data: pd.DataFrame):
        from src.infrastructure.data_quality.validation_framework import ValidationReport
        return ValidationReport(source=self.config.source_id)


async def example_1_basic_progress():
    """Example 1: Basic progress tracking with processor."""
    print("\n=== Example 1: Basic Progress Tracking ===\n")
    
    # Create processor
    config = SourceConfig(
        source_id="demo_source",
        source_type="csv",
        update_frequency="daily",
        cache_ttl=60,
        validation_level=ValidationLevel.WARN
    )
    
    processor = DemoProcessor(
        config,
        cache_manager=CacheManager(),
        validator=DataValidator()
    )
    
    # Create progress tracker and adapter
    tracker = PipelineProgressTracker("Demo Data Processing")
    adapter = ProgressAdapter(tracker)
    
    # Start tracking
    tracker.start()
    
    try:
        # Process with progress tracking
        result = await processor.process(
            force_refresh=True,
            progress_callback=adapter.create_callback()
        )
        
        if result.success:
            print(f"\nProcessing successful! Processed {len(result.data)} records")
        else:
            print(f"\nProcessing failed: {result.error_summary}")
            
    finally:
        tracker.stop()
    
    await asyncio.sleep(1)  # Let console settle


async def example_2_context_manager():
    """Example 2: Using context manager for progress tracking."""
    print("\n=== Example 2: Context Manager Progress Tracking ===\n")
    
    stages = ["Download", "Validate", "Transform", "Aggregate"]
    
    async with track_progress("Multi-Stage Processing", stages) as tracker:
        # Download stage
        await tracker.start_stage("Download")
        await tracker.update_progress(25)
        await asyncio.sleep(1)
        await tracker.update_progress(50)
        await asyncio.sleep(1)
        await tracker.update_progress(25)
        await tracker.complete_stage("Download")
        
        # Validate stage
        await tracker.start_stage("Validate")
        await asyncio.sleep(0.5)
        await tracker.add_warning("Found 5 duplicate records")
        await tracker.complete_stage("Validate")
        
        # Transform stage
        await tracker.start_stage("Transform")
        for i in range(10):
            await tracker.update_progress(10)
            await asyncio.sleep(0.1)
        await tracker.complete_stage("Transform")
        
        # Aggregate stage
        await tracker.start_stage("Aggregate")
        await asyncio.sleep(0.5)
        await tracker.add_error("Memory limit exceeded")
        await tracker.complete_stage("Aggregate", StageStatus.FAILED)
    
    await asyncio.sleep(1)


async def example_3_multiple_processors():
    """Example 3: Progress tracking for multiple processors."""
    print("\n=== Example 3: Multiple Processor Progress ===\n")
    
    # Create multiple processors
    processors = {
        "WFP Prices": DemoProcessor(
            SourceConfig(source_id="wfp", source_type="api", 
                        update_frequency="daily", cache_ttl=3600),
            CacheManager(), DataValidator()
        ),
        "ACLED Conflict": DemoProcessor(
            SourceConfig(source_id="acled", source_type="api",
                        update_frequency="weekly", cache_ttl=86400),
            CacheManager(), DataValidator()
        ),
        "Climate Data": DemoProcessor(
            SourceConfig(source_id="chirps", source_type="netcdf",
                        update_frequency="monthly", cache_ttl=2592000),
            CacheManager(), DataValidator()
        )
    }
    
    # Process all with individual progress tracking
    async with track_progress("Pipeline Processing", list(processors.keys())) as tracker:
        for name, processor in processors.items():
            await tracker.start_stage(name)
            
            # Create sub-tracker for processor
            sub_tracker, callback = create_rich_progress_callback(
                f"Processing {name}",
                {"Downloading": 100, "Transforming": 300}
            )
            
            try:
                result = await processor.process(
                    force_refresh=True,
                    progress_callback=callback
                )
                
                if result.success:
                    await tracker.complete_stage(name)
                else:
                    await tracker.add_error(result.error_summary)
                    await tracker.complete_stage(name, StageStatus.FAILED)
                    
            except Exception as e:
                await tracker.add_error(str(e))
                await tracker.complete_stage(name, StageStatus.FAILED)
    
    await asyncio.sleep(1)


async def example_4_error_handling():
    """Example 4: Progress tracking with error handling."""
    print("\n=== Example 4: Error Handling in Progress ===\n")
    
    async with track_progress("Error Handling Demo", ["Stage 1", "Stage 2", "Stage 3"]) as tracker:
        # Stage 1: Success
        await tracker.start_stage("Stage 1")
        await asyncio.sleep(1)
        await tracker.complete_stage("Stage 1")
        
        # Stage 2: Warnings
        await tracker.start_stage("Stage 2")
        await tracker.add_warning("Missing 10% of data")
        await tracker.add_warning("Using fallback exchange rates")
        await asyncio.sleep(1)
        await tracker.complete_stage("Stage 2", StageStatus.WARNING)
        
        # Stage 3: Failure
        await tracker.start_stage("Stage 3")
        await tracker.add_error("Connection timeout")
        await tracker.add_error("Retry failed")
        await asyncio.sleep(0.5)
        await tracker.complete_stage("Stage 3", StageStatus.FAILED)
    
    await asyncio.sleep(1)


async def main():
    """Run all examples."""
    examples = [
        example_1_basic_progress,
        example_2_context_manager,
        example_3_multiple_processors,
        example_4_error_handling
    ]
    
    for example in examples:
        await example()
        print("\n" + "="*60 + "\n")
    
    print("All examples completed!")


if __name__ == "__main__":
    # Note: Rich console features work best in a real terminal
    # Some features may be limited in notebook environments
    asyncio.run(main())