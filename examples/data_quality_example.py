"""
Example usage of the comprehensive data quality framework.

This script demonstrates how to use the new data quality framework
to address critical technical gaps in Yemen market analysis.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the new data quality framework
from src.infrastructure.data_quality.integration import (
    DataQualityService,
    validate_yemen_market_data,
    quick_exchange_rate_check,
    create_data_quality_pipeline
)

from src.infrastructure.processors.currency_aware_wfp_processor import CurrencyAwareWFPProcessor

# Import domain objects
from src.core.domain.market.currency_zones import CurrencyZone
from src.core.domain.market.value_objects import Currency, Price, MarketId
from src.core.domain.market.entities import Market, PriceObservation, Commodity


def create_sample_data() -> tuple[List[Market], List[PriceObservation]]:
    """Create sample Yemen market data for demonstration."""
    
    # Create sample markets representing different zones
    markets = [
        Market(
            market_id=MarketId("sanaa_central"),
            name="Sanaa Central Market",
            governorate="Sanaa",
            district="Al Azhar",
            latitude=Decimal("15.3694"),
            longitude=Decimal("44.1910")
        ),
        Market(
            market_id=MarketId("aden_main"),
            name="Aden Main Market", 
            governorate="Aden",
            district="Crater",
            latitude=Decimal("12.7797"),
            longitude=Decimal("45.0365")
        ),
        Market(
            market_id=MarketId("taiz_central"),
            name="Taiz Central Market",
            governorate="Taiz", 
            district="Salh",
            latitude=Decimal("13.5795"),
            longitude=Decimal("44.0202")
        )
    ]
    
    # Create sample price observations with currency fragmentation
    observations = []
    base_date = datetime.now() - timedelta(days=30)
    
    # Wheat flour prices (YER per kg)
    wheat_flour = Commodity(name="Wheat Flour")
    
    for i in range(30):  # 30 days of data
        date = base_date + timedelta(days=i)
        
        # Sanaa (Houthi zone) - lower YER prices due to different exchange rate
        observations.append(PriceObservation(
            market_id=MarketId("sanaa_central"),
            commodity=wheat_flour,
            price=Price(amount=Decimal("300") + Decimal(str(i * 2)), currency=Currency.YER),
            observed_date=date,
            source="WFP_market_monitor",
            quality="good",
            observations_count=5
        ))
        
        # Aden (Government zone) - higher YER prices due to exchange rate
        observations.append(PriceObservation(
            market_id=MarketId("aden_main"),
            commodity=wheat_flour,
            price=Price(amount=Decimal("1200") + Decimal(str(i * 8)), currency=Currency.YER),
            observed_date=date,
            source="WFP_market_monitor", 
            quality="good",
            observations_count=3
        ))
        
        # Taiz (Contested zone) - mixed patterns, some missing data
        if i % 3 != 0:  # Simulate 33% missing data due to conflict
            observations.append(PriceObservation(
                market_id=MarketId("taiz_central"),
                commodity=wheat_flour,
                price=Price(amount=Decimal("800") + Decimal(str(i * 12)), currency=Currency.YER),
                observed_date=date,
                source="WFP_market_monitor",
                quality="estimated",
                observations_count=1
            ))
    
    return markets, observations


async def demonstrate_exchange_rate_validation():
    """Demonstrate dynamic exchange rate validation."""
    logger.info("=== Exchange Rate Validation Example ===")
    
    # Test exchange rates that demonstrate the Yemen Paradox
    test_rates = {
        CurrencyZone.HOUTHI: 535.0,      # Official Sana'a rate
        CurrencyZone.GOVERNMENT: 1850.0,  # Official Aden rate 
        CurrencyZone.CONTESTED: 1200.0,   # Mixed rate
    }
    
    # Validate rates
    validation_result = await quick_exchange_rate_check(test_rates)
    
    logger.info("Exchange Rate Validation Results:")
    for validation in validation_result['individual_validations']:
        logger.info(
            f"  {validation['zone']}: {validation['rate']} YER/USD - "
            f"Validity: {validation['validity']} (Confidence: {validation['confidence']:.2f})"
        )
        
        if validation['flags']:
            logger.info(f"    Flags: {', '.join(validation['flags'])}")
    
    logger.info(f"Overall validation quality: {validation_result['validation_summary']['data_quality_score']:.1f}%")


async def demonstrate_comprehensive_processing():
    """Demonstrate comprehensive data quality processing."""
    logger.info("=== Comprehensive Data Quality Processing ===")
    
    # Create sample data
    markets, observations = create_sample_data()
    logger.info(f"Created {len(markets)} markets and {len(observations)} price observations")
    
    # Process with comprehensive quality framework
    service = create_data_quality_pipeline()
    
    processed_observations, quality_report = await service.process_market_data(
        observations=observations,
        markets=markets,
        target_currency=Currency.USD,
        enable_imputation=True,
        priority="high"
    )
    
    logger.info("Data Quality Processing Results:")
    logger.info(f"  Input observations: {len(observations)}")
    logger.info(f"  Processed observations: {len(processed_observations)}")
    logger.info(f"  Quality score: {quality_report['quality_score']:.1f}/100")
    logger.info(f"  Data coverage: {quality_report['data_coverage']:.1f}%")
    logger.info(f"  Conversion success rate: {quality_report['conversion_success_rate']:.1f}%")
    logger.info(f"  Processing time: {quality_report['processing_time_seconds']:.2f}s")
    
    if quality_report['warnings']:
        logger.info("  Warnings:")
        for warning in quality_report['warnings'][:3]:
            logger.info(f"    - {warning}")
    
    if quality_report['recommendations']:
        logger.info("  Recommendations:")
        for rec in quality_report['recommendations'][:3]:
            logger.info(f"    - {rec}")
    
    # Demonstrate the Yemen Paradox resolution
    sanaa_prices = [obs for obs in processed_observations if "sanaa" in str(obs.market_id.value)]
    aden_prices = [obs for obs in processed_observations if "aden" in str(obs.market_id.value)]
    
    if sanaa_prices and aden_prices:
        avg_sanaa_usd = sum(float(obs.price.amount) for obs in sanaa_prices) / len(sanaa_prices)
        avg_aden_usd = sum(float(obs.price.amount) for obs in aden_prices) / len(aden_prices)
        
        logger.info("\n=== Yemen Paradox Resolution ===")
        logger.info(f"Average Sanaa price (USD): ${avg_sanaa_usd:.2f}/kg")
        logger.info(f"Average Aden price (USD): ${avg_aden_usd:.2f}/kg")
        
        if avg_sanaa_usd > avg_aden_usd:
            logger.info("✓ Paradox resolved: Houthi areas show higher real prices after currency conversion")
        else:
            logger.info("⚠ Paradox not fully resolved - may need additional validation")


async def demonstrate_enhanced_processor():
    """Demonstrate the enhanced currency-aware processor."""
    logger.info("=== Enhanced Currency-Aware Processor ===")
    
    # Create sample data
    markets, observations = create_sample_data()
    
    # Initialize enhanced processor
    processor = CurrencyAwareWFPProcessor(
        enable_advanced_quality_control=True
    )
    
    # Process data (note: would need actual data source in real usage)
    try:
        # This would typically process actual WFP data
        logger.info("Enhanced processor initialized with advanced quality control")
        logger.info("In production, this would process actual WFP CSV/Excel files with full pipeline")
        
        # Demonstrate key features
        logger.info("\nKey features of enhanced processor:")
        logger.info("  ✓ Dynamic exchange rate validation")
        logger.info("  ✓ Zone-specific quality control bounds")
        logger.info("  ✓ Conflict-aware missing data imputation")
        logger.info("  ✓ Proper currency conversion timing")
        logger.info("  ✓ Comprehensive quality reporting")
        
    except Exception as e:
        logger.info(f"Note: Full demonstration requires actual WFP data source: {e}")


async def demonstrate_missing_data_imputation():
    """Demonstrate conflict-aware missing data imputation."""
    logger.info("=== Conflict-Aware Missing Data Imputation ===")
    
    # Create data with systematic missing patterns
    markets, observations = create_sample_data()
    
    # Show missing data statistics
    total_expected = len(markets) * 30  # 3 markets * 30 days
    actual_observations = len(observations)
    missing_pct = (total_expected - actual_observations) / total_expected * 100
    
    logger.info(f"Missing data simulation:")
    logger.info(f"  Expected observations: {total_expected}")
    logger.info(f"  Actual observations: {actual_observations}")
    logger.info(f"  Missing percentage: {missing_pct:.1f}%")
    logger.info(f"  Pattern: Systematic gaps in contested areas (Taiz)")
    
    # Process with imputation
    service = create_data_quality_pipeline()
    
    processed_observations, quality_report = await service.process_market_data(
        observations=observations,
        markets=markets,
        target_currency=Currency.USD,
        enable_imputation=True,
        priority="normal"
    )
    
    logger.info(f"\nAfter imputation:")
    logger.info(f"  Processed observations: {len(processed_observations)}")
    logger.info(f"  Imputation success rate: {quality_report['imputation_success_rate']:.1f}%")
    
    # Show coverage by market
    market_coverage = {}
    for obs in processed_observations:
        market_id = str(obs.market_id.value)
        market_coverage[market_id] = market_coverage.get(market_id, 0) + 1
    
    logger.info("  Coverage by market:")
    for market_id, count in market_coverage.items():
        coverage_pct = count / 30 * 100  # 30 days expected
        logger.info(f"    {market_id}: {count}/30 ({coverage_pct:.1f}%)")


async def main():
    """Run all demonstration examples."""
    logger.info("Yemen Market Integration - Data Quality Framework Demonstration")
    logger.info("=" * 70)
    
    try:
        # Run all demonstrations
        await demonstrate_exchange_rate_validation()
        print()
        
        await demonstrate_comprehensive_processing()
        print()
        
        await demonstrate_enhanced_processor()
        print()
        
        await demonstrate_missing_data_imputation()
        print()
        
        logger.info("=" * 70)
        logger.info("All demonstrations completed successfully!")
        logger.info("\nKey achievements:")
        logger.info("  ✓ Replaced hard-coded exchange rate multipliers with dynamic validation")
        logger.info("  ✓ Implemented zone-specific quality control bounds")
        logger.info("  ✓ Added proper currency conversion timing protocols")
        logger.info("  ✓ Implemented conflict-aware missing data imputation")
        logger.info("  ✓ Integrated comprehensive data quality orchestration")
        
    except Exception as e:
        logger.error(f"Demonstration failed: {e}")
        raise


if __name__ == "__main__":
    # Run the demonstration
    asyncio.run(main())