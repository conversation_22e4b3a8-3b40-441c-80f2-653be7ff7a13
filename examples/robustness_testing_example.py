"""
Example: Automated Robustness Testing Pipeline for Yemen Market Integration

This example demonstrates how to use the comprehensive robustness testing
pipeline for hypothesis testing with World Bank publication standards.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.core.models.robustness.automated_pipeline import (
    AutomatedRobustnessPipeline, 
    RobustnessPipelineConfig,
    create_default_pipeline
)

# Example model functions for hypotheses
def h1_exchange_rate_model(data: pd.DataFrame, **kwargs) -> dict:
    """
    H1: Exchange rate mechanism affects price integration.
    
    Simplified example - would use actual econometric model.
    """
    # Simulate regression results
    np.random.seed(42)
    
    n_obs = len(data)
    coefficient = 0.052 + np.random.normal(0, 0.01)  # Main effect
    se = 0.015 + np.random.normal(0, 0.002)
    t_stat = coefficient / se
    p_value = 2 * (1 - stats.t.cdf(abs(t_stat), df=n_obs-10))
    
    return {
        'coefficient': coefficient,
        'se': se,
        'p_value': p_value,
        'ci_lower': coefficient - 1.96 * se,
        'ci_upper': coefficient + 1.96 * se,
        'n_obs': n_obs,
        'r_squared': 0.15 + np.random.normal(0, 0.02),
        'converged': True
    }

def h2_aid_distribution_model(data: pd.DataFrame, **kwargs) -> dict:
    """
    H2: Aid distribution affects market price levels.
    
    Simplified example - would use actual econometric model.
    """
    np.random.seed(43)
    
    n_obs = len(data)
    coefficient = -0.038 + np.random.normal(0, 0.008)  # Negative effect
    se = 0.012 + np.random.normal(0, 0.001)
    t_stat = coefficient / se
    p_value = 2 * (1 - stats.t.cdf(abs(t_stat), df=n_obs-8))
    
    return {
        'coefficient': coefficient,
        'se': se,
        'p_value': p_value,
        'ci_lower': coefficient - 1.96 * se,
        'ci_upper': coefficient + 1.96 * se,
        'n_obs': n_obs,
        'r_squared': 0.22 + np.random.normal(0, 0.03),
        'converged': True
    }

def h5_arbitrage_model(data: pd.DataFrame, **kwargs) -> dict:
    """
    H5: Cross-border arbitrage mechanisms.
    
    Simplified example - would use actual econometric model.
    """
    np.random.seed(45)
    
    n_obs = len(data)
    coefficient = 0.074 + np.random.normal(0, 0.012)  # Larger effect
    se = 0.018 + np.random.normal(0, 0.002)
    t_stat = coefficient / se
    p_value = 2 * (1 - stats.t.cdf(abs(t_stat), df=n_obs-12))
    
    return {
        'coefficient': coefficient,
        'se': se,
        'p_value': p_value,
        'ci_lower': coefficient - 1.96 * se,
        'ci_upper': coefficient + 1.96 * se,
        'n_obs': n_obs,
        'r_squared': 0.18 + np.random.normal(0, 0.025),
        'converged': True
    }

def create_example_data(n_markets: int = 50, n_periods: int = 60) -> pd.DataFrame:
    """Create example Yemen market data for testing."""
    np.random.seed(42)
    
    # Create market-time panel
    markets = [f"Market_{i:02d}" for i in range(n_markets)]
    dates = pd.date_range('2020-01-01', periods=n_periods, freq='M')
    
    # Create full panel
    data = []
    for market in markets:
        for date in dates:
            # Assign currency zones (simplified)
            currency_zone = 'north' if hash(market) % 2 == 0 else 'south'
            
            # Simulate data with realistic Yemen characteristics
            base_price = 100 + np.random.normal(0, 20)
            conflict_intensity = np.random.exponential(2)
            aid_presence = np.random.binomial(1, 0.3)
            
            # Add currency zone effects
            if currency_zone == 'south':
                base_price *= 1.8  # Southern price premium
                
            # Add temporal effects
            if date >= '2021-01-01':
                base_price *= 1.2  # Price increase after 2021
                conflict_intensity *= 1.5
                
            data.append({
                'market_id': market,
                'date': date,
                'price': base_price + np.random.normal(0, 5),
                'currency_zone': currency_zone,
                'conflict_intensity': conflict_intensity,
                'aid_presence': aid_presence,
                'latitude': 15.0 + np.random.uniform(-2, 2),
                'longitude': 48.0 + np.random.uniform(-3, 3),
                'population': np.random.lognormal(10, 1),
                'temperature': 25 + 10 * np.sin(2 * np.pi * date.month / 12) + np.random.normal(0, 2),
                'rainfall': np.random.exponential(5)
            })
    
    df = pd.DataFrame(data)
    
    # Add missing data pattern (38% missing, non-random)
    missing_prob = 0.38
    for i, row in df.iterrows():
        # Higher missingness during high conflict
        prob = missing_prob * (1 + 0.5 * (row['conflict_intensity'] > 3))
        if np.random.random() < prob:
            df.loc[i, 'price'] = np.nan
            
    return df

def main():
    """Run example robustness testing."""
    print("Yemen Market Integration - Automated Robustness Testing Example")
    print("=" * 60)
    
    # Create example data
    print("Creating example Yemen market data...")
    data = create_example_data(n_markets=50, n_periods=60)
    print(f"Created panel data: {len(data)} observations, "
          f"{data['market_id'].nunique()} markets, "
          f"{data['date'].nunique()} time periods")
    print(f"Missing data: {data['price'].isna().mean():.1%}")
    
    # Define currency zones
    baseline_zones = data.groupby('market_id')['currency_zone'].first().to_dict()
    print(f"Currency zones: {sum(1 for z in baseline_zones.values() if z == 'north')} north, "
          f"{sum(1 for z in baseline_zones.values() if z == 'south')} south")
    
    # Configure robustness pipeline
    config = RobustnessPipelineConfig(
        n_bootstrap=500,  # Reduced for example
        n_specifications=1000,  # Reduced for example
        parallel=True,
        baseline_zones=baseline_zones,
        currency_zone_buffers=[10, 25, 50],
        placebo_outcomes=['temperature', 'rainfall', 'population'],
        min_robustness_score=0.7,
        output_dir="results/robustness_example"
    )
    
    # Create pipeline
    pipeline = AutomatedRobustnessPipeline(config)
    
    # Define hypothesis models
    hypothesis_models = {
        'H1_Exchange_Rate_Mechanism': h1_exchange_rate_model,
        'H2_Aid_Distribution_Effects': h2_aid_distribution_model,
        'H5_Cross_Border_Arbitrage': h5_arbitrage_model
    }
    
    print(f"\nRunning robustness tests for {len(hypothesis_models)} hypotheses...")
    print("-" * 40)
    
    # Run robustness testing for all hypotheses
    all_results = pipeline.run_all_hypotheses_robustness(
        data=data.dropna(subset=['price']),  # Use complete cases for example
        hypothesis_models=hypothesis_models,
        cluster_var='market_id'
    )
    
    # Display results summary
    print("\nRobustness Testing Results:")
    print("=" * 40)
    
    for hypothesis, results in all_results.items():
        print(f"\n{hypothesis}:")
        print(f"  Robustness Score: {results.robustness_score:.2f}")
        print(f"  Assessment: {results.overall_assessment}")
        
        if results.main_results:
            coef = results.main_results.get('coefficient', 0)
            se = results.main_results.get('se', 0)
            print(f"  Main Effect: {coef:.4f} (SE: {se:.4f})")
            
        if results.specification_curve:
            n_specs = results.specification_curve.get('n_specifications', 0)
            prop_sig = results.specification_curve.get('prop_significant', 0)
            print(f"  Specifications: {n_specs} total, {prop_sig*100:.1f}% significant")
            
        if results.bootstrap_results:
            conv_rate = results.bootstrap_results.get('convergence_rate', 0)
            ci_lower = results.bootstrap_results.get('ci_lower', 0)
            ci_upper = results.bootstrap_results.get('ci_upper', 0)
            print(f"  Bootstrap: {conv_rate*100:.1f}% convergence, "
                  f"95% CI: [{ci_lower:.4f}, {ci_upper:.4f}]")
    
    # Export publication tables
    print(f"\nExporting publication tables...")
    latex_path = pipeline.export_publication_tables(all_results, 'latex')
    excel_path = pipeline.export_publication_tables(all_results, 'excel')
    
    print(f"LaTeX tables: {latex_path}")
    print(f"Excel tables: {excel_path}")
    
    # Summary statistics
    robust_count = sum(1 for r in all_results.values() 
                      if r.robustness_score >= config.min_robustness_score)
    mean_score = np.mean([r.robustness_score for r in all_results.values()])
    
    print(f"\nOverall Summary:")
    print(f"  {robust_count}/{len(all_results)} hypotheses meet robustness standards")
    print(f"  Mean robustness score: {mean_score:.2f}")
    print(f"  Results saved to: {config.output_dir}")
    
    print(f"\nRobustness testing completed successfully!")

if __name__ == "__main__":
    # Import scipy.stats for the example
    from scipy import stats
    main()