"""
Example of proper, unbiased hypothesis testing with pre-registration.

This example demonstrates best practices for conducting hypothesis tests
without p-hacking or confirmation bias.
"""

import pandas as pd
import numpy as np
from datetime import datetime

# Import hypothesis testing framework
from src.core.models.hypothesis_testing import (
    HypothesisRegistry,
    BiasDetector,
    create_pre_analysis_plan
)
from src.core.models.hypothesis_testing.pre_registration import (
    register_analysis_plan,
    get_registered_plan
)


def main():
    """Demonstrate proper hypothesis testing workflow."""
    
    print("=== Proper Hypothesis Testing Example ===\n")
    
    # Step 1: Pre-register analysis plan BEFORE looking at data
    print("Step 1: Pre-registering analysis plan...")
    
    plan_hash = register_analysis_plan(
        hypothesis_id='H1',
        primary_specification={
            'model': 'PanelOLS',
            'fixed_effects': ['market', 'time'],
            'clustering': 'market',
            'controls': ['conflict_intensity'],
            'transformation': 'log_prices',
            'outlier_handling': 'winsorize_1pct'
        },
        decision_rules={
            'alpha': 0.05,
            'minimum_effect_size': 0.10,
            'one_tailed': False,
            'interpretation': {
                'null_rejected': "Evidence of relationship between exchange rates and prices",
                'fail_to_reject': "No evidence of relationship",
                'insufficient_power': "Unable to draw conclusions due to limited data"
            }
        },
        secondary_analyses=[
            {
                'name': 'Heterogeneity by region',
                'specification': 'interaction with north/south indicator'
            },
            {
                'name': 'Time-varying effects',
                'specification': 'rolling window analysis'
            }
        ],
        power_analysis={
            'target_power': 0.80,
            'assumed_effect_size': 0.25,
            'required_n': 1000
        }
    )
    
    print(f"✓ Analysis plan registered with hash: {plan_hash}")
    print("  - Cannot be modified after registration")
    print("  - All analyses must follow registered plan\n")
    
    # Step 2: Initialize bias detector
    print("Step 2: Initializing bias detection...")
    
    bias_detector = BiasDetector(analysis_log_path=".hypothesis_logs/example_log.json")
    
    # Create pre-analysis plan for bias detector
    pre_analysis_plan = create_pre_analysis_plan(
        hypotheses=['H1'],
        specifications=[{
            'fixed_effects': ['market', 'time'],
            'clustering': 'market'
        }],
        alpha=0.05,
        power=0.80
    )
    
    bias_detector.register_pre_analysis_plan(pre_analysis_plan)
    print("✓ Bias detection active\n")
    
    # Step 3: Load and prepare data
    print("Step 3: Loading data (with pre-registration check)...")
    
    # This would normally load real data
    # The @requires_preregistration decorator ensures we can't access data
    # without a registered plan
    
    try:
        # Simulate data loading
        data = load_price_data()  # This would be the actual data loading function
        print("✓ Data loaded successfully")
        print(f"  - Sample size: {len(data)} observations")
        print(f"  - Time period: {data['date'].min()} to {data['date'].max()}\n")
    except Exception as e:
        print(f"✗ Data loading failed: {e}\n")
        return
    
    # Step 4: Run primary analysis following pre-registered plan
    print("Step 4: Running primary analysis...")
    
    # Get registered plan to ensure we follow it
    registered_plan = get_registered_plan('H1')
    primary_spec = registered_plan['primary_specification']
    
    print(f"  Following pre-registered specification:")
    for key, value in primary_spec.items():
        print(f"    - {key}: {value}")
    
    # Run the test
    from src.core.models.hypothesis_testing.h1_exchange_rate import H1ExchangeRateMechanism
    
    h1_test = H1ExchangeRateMechanism()
    
    # Prepare data
    test_data = h1_test.prepare_data({'prices': data, 'exchange_rates': data})
    
    # Run test
    results = h1_test.run_test(test_data)
    
    print(f"\n✓ Primary analysis complete:")
    print(f"  - Test statistic: {results.test_statistic:.4f}")
    print(f"  - P-value: {results.p_value:.4f}")
    print(f"  - Effect size: {results.effect_size:.4f}")
    print(f"  - Statistical power: {results.statistical_power:.2f}")
    print(f"  - Outcome: {results.outcome.value}\n")
    
    # Step 5: Check for bias
    print("Step 5: Checking for analytical bias...")
    
    bias_report = bias_detector.check_p_hacking('H1')
    
    if bias_report['p_hacking_risk']:
        print("⚠️  WARNING: Potential bias detected!")
        for pattern in bias_report['suspicious_patterns']:
            print(f"    - {pattern}")
    else:
        print("✓ No bias detected")
    
    # Step 6: Run pre-registered secondary analyses
    print("\nStep 6: Running pre-registered secondary analyses...")
    
    for secondary in registered_plan['secondary_analyses']:
        print(f"  - {secondary['name']}: {secondary['specification']}")
        # Run secondary analysis...
    
    # Step 7: Report results honestly
    print("\nStep 7: Generating unbiased report...")
    
    interpretation = h1_test.interpret_results(results)
    
    print("\n" + "="*60)
    print("FINAL REPORT")
    print("="*60)
    print(f"\nStatistical Summary:")
    print(interpretation.statistical_summary)
    
    print(f"\nKey Findings:")
    for finding in interpretation.findings:
        print(f"  • {finding}")
    
    print(f"\nConsiderations for Policy:")
    for consideration in interpretation.considerations:
        print(f"  • {consideration}")
    
    print(f"\n{interpretation.uncertainty_statement}")
    
    print(f"\nLimitations:")
    for limitation in interpretation.limitations:
        print(f"  • {limitation}")
    
    if interpretation.data_quality_notes:
        print(f"\nData Quality Notes:")
        for note in interpretation.data_quality_notes:
            print(f"  • {note}")
    
    # Step 8: Save audit trail
    print("\n" + "="*60)
    print("Step 8: Saving complete audit trail...")
    
    audit_info = {
        'pre_registration_hash': plan_hash,
        'analysis_date': datetime.now().isoformat(),
        'bias_detection': bias_report,
        'degrees_of_freedom': h1_test.dof_tracker.calculate_degrees_of_freedom(),
        'robustness_score': results.comprehensive_robustness.robustness_score 
            if results.comprehensive_robustness else None
    }
    
    print("✓ Audit trail saved")
    print("  - All analytical choices documented")
    print("  - Results reproducible")
    print("  - Bias checks included")
    
    print("\n✅ Analysis complete with full transparency and bias prevention")


def load_price_data():
    """Simulate loading price data."""
    # In real usage, this would load actual data
    # For example purposes, create synthetic data
    
    np.random.seed(42)  # For reproducibility
    
    dates = pd.date_range('2020-01-01', '2023-12-31', freq='D')
    markets = ['Market_A', 'Market_B', 'Market_C', 'Market_D']
    
    data = []
    for date in dates:
        for market in markets:
            # Simulate price with some structure
            base_price = 100 + np.sin(date.dayofyear / 365 * 2 * np.pi) * 20
            market_effect = hash(market) % 20 - 10
            noise = np.random.normal(0, 5)
            
            # Currency zone
            zone = 'houthi' if market in ['Market_A', 'Market_B'] else 'government'
            
            # Exchange rate (simplified)
            if zone == 'houthi':
                exchange_rate = 535 + np.random.normal(0, 10)
            else:
                exchange_rate = 2000 + np.random.normal(0, 50)
            
            data.append({
                'date': date,
                'market_id': market,
                'price_yer': base_price + market_effect + noise,
                'currency_zone': zone,
                'exchange_rate': exchange_rate,
                'governorate': 'Gov_' + market[-1],
                'conflict_intensity': np.random.uniform(0, 10)
            })
    
    return pd.DataFrame(data)


if __name__ == "__main__":
    main()