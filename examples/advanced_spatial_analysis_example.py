"""
Advanced Spatial Econometric Analysis Example for Yemen Market Integration.

This example demonstrates the use of advanced spatial econometric methods
for analyzing market integration in conflict settings.
"""

import numpy as np
import pandas as pd
import logging
from pathlib import Path
import sys

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.models.spatial import (
    SpatialWeightMatrix, SpatialWeightOptions,
    SpatialRegimeModel, RegimeType,
    DynamicSpatialPanelModel, DynamicSpecification,
    SpatialThresholdModel, ThresholdType,
    GeographicallyWeightedRegression, KernelType, BandwidthSelection
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def generate_example_data():
    """Generate example Yemen market data for spatial analysis."""
    
    # Set random seed for reproducibility
    np.random.seed(42)
    
    # Yemen governorates and approximate coordinates
    markets = [
        {'market_id': 'sanaa_city', 'latitude': 15.3694, 'longitude': 44.1910, 'governorate': 'Sanaa', 'currency_zone': 'houthi'},
        {'market_id': 'aden_city', 'latitude': 12.7794, 'longitude': 45.0367, 'governorate': 'Aden', 'currency_zone': 'government'},
        {'market_id': 'taiz_city', 'latitude': 13.5779, 'longitude': 44.0155, 'governorate': 'Taiz', 'currency_zone': 'contested'},
        {'market_id': 'hodeidah', 'latitude': 14.7978, 'longitude': 42.9545, 'governorate': 'Hodeidah', 'currency_zone': 'houthi'},
        {'market_id': 'ibb_city', 'latitude': 13.9667, 'longitude': 44.1833, 'governorate': 'Ibb', 'currency_zone': 'houthi'},
        {'market_id': 'mukalla', 'latitude': 14.5425, 'longitude': 49.1242, 'governorate': 'Hadramout', 'currency_zone': 'government'},
        {'market_id': 'marib_city', 'latitude': 15.4167, 'longitude': 45.3167, 'governorate': 'Marib', 'currency_zone': 'government'},
        {'market_id': 'hajjah', 'latitude': 15.6944, 'longitude': 43.6056, 'governorate': 'Hajjah', 'currency_zone': 'houthi'},
        {'market_id': 'zinjibar', 'latitude': 13.1289, 'longitude': 45.3917, 'governorate': 'Abyan', 'currency_zone': 'government'},
        {'market_id': 'saada', 'latitude': 16.9402, 'longitude': 43.7637, 'governorate': 'Saada', 'currency_zone': 'houthi'}
    ]
    
    coordinates = pd.DataFrame(markets)
    
    # Generate time series data (monthly from 2019-2024)
    dates = pd.date_range('2019-01-01', '2024-12-01', freq='MS')
    n_periods = len(dates)
    n_markets = len(markets)
    
    # Conflict intensity varies by location and time
    conflict_base = {
        'sanaa_city': 0.3, 'aden_city': 0.2, 'taiz_city': 0.8, 'hodeidah': 0.4,
        'ibb_city': 0.2, 'mukalla': 0.1, 'marib_city': 0.6, 'hajjah': 0.3,
        'zinjibar': 0.4, 'saada': 0.5
    }
    
    # Generate panel data
    panel_data = []
    
    for market in markets:
        market_id = market['market_id']
        currency_zone = market['currency_zone']
        
        # Base price level varies by currency zone
        if currency_zone == 'houthi':
            base_price = 4.0  # Log price in YER (Northern rate)
        elif currency_zone == 'government':
            base_price = 6.5  # Log price in YER (Southern rate)
        else:  # contested
            base_price = 5.2  # Mixed rate
            
        for i, date in enumerate(dates):
            # Time-varying conflict intensity
            base_conflict = conflict_base[market_id]
            conflict_shock = 0.3 * np.sin(i * 0.1) + 0.2 * np.random.normal()
            conflict_intensity = np.clip(base_conflict + conflict_shock, 0, 1)
            
            # Distance to nearest conflict (inverse of conflict intensity)
            distance_to_conflict = (1 - conflict_intensity) * 100  # km
            
            # Fatality rate (events per 100k population)
            fatality_rate = conflict_intensity * 50 + np.random.exponential(10)
            
            # Economic variables
            fuel_price = 2.5 + 0.1 * i + 0.5 * conflict_intensity + np.random.normal(0, 0.2)
            exchange_rate_vol = conflict_intensity * 0.3 + np.random.normal(0, 0.1)
            transport_cost = 1.0 + 0.5 * conflict_intensity + np.random.normal(0, 0.15)
            
            # Food price (dependent variable)
            # Spatial spillover effect (simplified)
            spatial_effect = 0.3 * np.random.normal()
            
            # Price determination with regime effects
            if currency_zone == 'houthi':
                regime_effect = -0.2
            elif currency_zone == 'government':
                regime_effect = 0.1
            else:
                regime_effect = 0.0
                
            log_price = (base_price + 0.1 * i + 
                        regime_effect +
                        0.4 * fuel_price + 
                        0.3 * transport_cost +
                        -0.2 * conflict_intensity +
                        spatial_effect +
                        np.random.normal(0, 0.15))
            
            panel_data.append({
                'market_id': market_id,
                'date': date,
                'log_price': log_price,
                'fuel_price': fuel_price,
                'exchange_rate_volatility': exchange_rate_vol,
                'transport_cost': transport_cost,
                'conflict_intensity': conflict_intensity,
                'distance_to_conflict': distance_to_conflict,
                'fatality_rate': fatality_rate,
                'currency_zone': currency_zone,
                'governorate': market['governorate']
            })
    
    panel_df = pd.DataFrame(panel_data)
    
    logger.info(f"Generated panel data: {len(panel_df)} observations, {len(markets)} markets, {n_periods} periods")
    
    return panel_df, coordinates


def demonstrate_spatial_regime_analysis(data, coordinates):
    """Demonstrate spatial regime model analysis."""
    
    logger.info("=" * 60)
    logger.info("SPATIAL REGIME MODEL ANALYSIS")
    logger.info("=" * 60)
    
    # Define variables
    outcome_var = 'log_price'
    exog_vars = ['fuel_price', 'transport_cost', 'exchange_rate_volatility']
    regime_var = 'currency_zone'
    
    try:
        # Estimate spatial regime model
        regime_model = SpatialRegimeModel(
            data=data,
            coordinates=coordinates,
            outcome_var=outcome_var,
            exog_vars=exog_vars,
            regime_var=regime_var,
            market_id_var='market_id'
        )
        
        regime_results = regime_model.fit()
        
        # Display results
        summary = regime_model.summary()
        logger.info(f"Model Type: {summary['model_type']}")
        logger.info(f"Number of Regimes: {summary['n_regimes']}")
        logger.info(f"Regimes: {summary['regimes']}")
        
        logger.info("\nModel Fit:")
        for key, value in summary['model_fit'].items():
            logger.info(f"  {key}: {value:.4f}")
            
        logger.info("\nRegime Test:")
        regime_test = summary['regime_test']
        logger.info(f"  Test Statistic: {regime_test['statistic']:.4f}")
        logger.info(f"  P-value: {regime_test['p_value']:.4f}")
        logger.info(f"  Significant Differences: {regime_test['significant']}")
        
        logger.info("\nRegime-Specific Spatial Parameters:")
        for regime, results in summary['regime_results'].items():
            spatial_param = results['spatial_parameter']
            logger.info(f"  {regime.upper()} Zone:")
            logger.info(f"    Spatial Parameter: {spatial_param['value']:.4f} (SE: {spatial_param['standard_error']:.4f})")
            logger.info(f"    Significant: {spatial_param['significant']}")
            logger.info(f"    Sample Size: {results['sample_size']}")
            
        # Compare regimes
        comparison = regime_model.compare_regimes()
        logger.info("\nRegime Comparison:")
        logger.info(comparison.to_string(index=False))
        
    except Exception as e:
        logger.error(f"Spatial regime analysis failed: {e}")


def demonstrate_dynamic_spatial_panel(data, coordinates):
    """Demonstrate dynamic spatial panel model."""
    
    logger.info("=" * 60)
    logger.info("DYNAMIC SPATIAL PANEL MODEL ANALYSIS")
    logger.info("=" * 60)
    
    # Create simple spatial weights for demonstration
    try:
        weight_matrix = SpatialWeightMatrix.from_coordinates(
            coordinates,
            weight_type='distance'
        )
        W = weight_matrix.matrix
        
        # Define variables
        outcome_var = 'log_price'
        exog_vars = ['fuel_price', 'transport_cost']
        
        # Estimate dynamic spatial panel
        dynamic_model = DynamicSpatialPanelModel(
            data=data,
            weight_matrix=W,
            outcome_var=outcome_var,
            exog_vars=exog_vars,
            market_id_var='market_id',
            time_var='date',
            max_lags=2
        )
        
        # Fit with different specifications
        specifications = [
            DynamicSpecification.TEMPORAL_LAG_SPATIAL_LAG,
            DynamicSpecification.FULL_DYNAMIC
        ]
        
        for spec in specifications:
            logger.info(f"\nEstimating {spec.value} specification:")
            
            try:
                dynamic_results = dynamic_model.fit(
                    specification=spec,
                    estimation_method='ols'  # Use OLS for stability
                )
                
                summary = dynamic_model.summary()
                
                logger.info(f"Model Type: {summary['model_type']}")
                logger.info(f"Specification: {summary['specification']['max_lags']} lags")
                
                logger.info("\nDynamic Parameters:")
                dyn_params = summary['dynamic_parameters']
                logger.info(f"  Temporal Lag Coefficients: {dyn_params['temporal_lag_coefficients']}")
                logger.info(f"  Spatial Lag Coefficient: {dyn_params['spatial_lag_coefficient']:.4f}")
                
                logger.info("\nModel Fit:")
                fit_stats = summary['model_fit']
                for key, value in fit_stats.items():
                    logger.info(f"  {key}: {value:.4f}")
                    
                logger.info("\nStability:")
                stability = summary['stability']
                logger.info(f"  Overall Stable: {stability['overall_stable']}")
                logger.info(f"  Max Eigenvalue: {stability['max_eigenvalue']:.4f}")
                
            except Exception as e:
                logger.error(f"Failed to estimate {spec.value}: {e}")
                
    except Exception as e:
        logger.error(f"Dynamic spatial panel analysis failed: {e}")


def demonstrate_spatial_threshold_analysis(data, coordinates):
    """Demonstrate spatial threshold model analysis."""
    
    logger.info("=" * 60)
    logger.info("SPATIAL THRESHOLD MODEL ANALYSIS")
    logger.info("=" * 60)
    
    try:
        # Create spatial weights
        weight_matrix = SpatialWeightMatrix.from_coordinates(
            coordinates,
            weight_type='distance'
        )
        W = weight_matrix.matrix
        
        # Define variables
        outcome_var = 'log_price'
        exog_vars = ['fuel_price', 'transport_cost']
        threshold_var = 'conflict_intensity'
        
        # Estimate spatial threshold model
        threshold_model = SpatialThresholdModel(
            data=data,
            weight_matrix=W,
            outcome_var=outcome_var,
            exog_vars=exog_vars,
            threshold_var=threshold_var,
            market_id_var='market_id'
        )
        
        # Test different threshold types
        threshold_types = [ThresholdType.SINGLE_THRESHOLD]
        
        for thresh_type in threshold_types:
            logger.info(f"\nEstimating {thresh_type.value} model:")
            
            try:
                threshold_results = threshold_model.fit(
                    threshold_type=thresh_type,
                    trim_percentage=0.2
                )
                
                summary = threshold_model.summary()
                
                logger.info(f"Model Type: {summary['model_type']}")
                logger.info(f"Threshold Variable: {summary['threshold_variable']}")
                
                logger.info("\nThreshold Values:")
                thresholds = summary['thresholds']
                logger.info(f"  Values: {thresholds['values']}")
                logger.info(f"  Significant: {thresholds['significant']}")
                
                logger.info("\nRegime Effects:")
                for regime, effects in summary['regime_effects'].items():
                    logger.info(f"  {regime.upper()} Regime:")
                    logger.info(f"    Spatial Parameter: {effects['spatial_parameter']:.4f}")
                    logger.info(f"    Standard Error: {effects['standard_error']:.4f}")
                    logger.info(f"    Significant: {effects['significant']}")
                    
                logger.info("\nModel Fit:")
                fit_stats = summary['model_fit']
                for key, value in fit_stats.items():
                    logger.info(f"  {key}: {value:.4f}")
                    
                logger.info("\nThreshold Tests:")
                tests = summary['threshold_tests']
                logger.info(f"  Linearity Test Statistic: {tests['linearity_test_statistic']:.4f}")
                logger.info(f"  Linearity Test P-value: {tests['linearity_test_pvalue']:.4f}")
                
                logger.info("\nRegime Composition:")
                composition = summary['regime_composition']
                for regime, count in composition.items():
                    logger.info(f"  {regime}: {count} observations")
                    
            except Exception as e:
                logger.error(f"Failed to estimate {thresh_type.value}: {e}")
                
    except Exception as e:
        logger.error(f"Spatial threshold analysis failed: {e}")


def demonstrate_gwr_analysis(data, coordinates):
    """Demonstrate Geographically Weighted Regression analysis."""
    
    logger.info("=" * 60)
    logger.info("GEOGRAPHICALLY WEIGHTED REGRESSION ANALYSIS")
    logger.info("=" * 60)
    
    try:
        # Define variables
        outcome_var = 'log_price'
        exog_vars = ['fuel_price', 'transport_cost', 'conflict_intensity']
        
        # Estimate GWR model
        gwr_model = GeographicallyWeightedRegression(
            data=data,
            coordinates=coordinates,
            outcome_var=outcome_var,
            exog_vars=exog_vars,
            market_id_var='market_id'
        )
        
        # Test different kernel types
        kernels = [KernelType.GAUSSIAN, KernelType.BISQUARE]
        
        for kernel in kernels:
            logger.info(f"\nEstimating GWR with {kernel.value} kernel:")
            
            try:
                gwr_results = gwr_model.fit(
                    kernel_type=kernel,
                    bandwidth_method=BandwidthSelection.CV
                )
                
                summary = gwr_model.summary()
                
                logger.info(f"Model Type: {summary['model_type']}")
                logger.info(f"Kernel Type: {summary['kernel_type']}")
                logger.info(f"Optimal Bandwidth: {summary['optimal_bandwidth_km']:.2f} km")
                
                logger.info("\nModel Fit:")
                fit_stats = summary['model_fit']
                for key, value in fit_stats.items():
                    logger.info(f"  {key}: {value:.4f}")
                    
                logger.info("\nSpatial Variation:")
                spatial_var = summary['spatial_variation']
                logger.info(f"  Global R²: {spatial_var['global_r_squared']:.4f}")
                logger.info(f"  Mean Local R²: {spatial_var['mean_local_r_squared']:.4f}")
                logger.info(f"  Improvement over Global: {spatial_var['improvement_over_global']:.4f}")
                logger.info(f"  Local R² Range: {spatial_var['r_squared_range']}")
                
                logger.info("\nCoefficient Variation:")
                for var, stats in summary['coefficient_variation'].items():
                    logger.info(f"  {var}:")
                    logger.info(f"    Mean: {stats['mean']:.4f}")
                    logger.info(f"    Std Dev: {stats['std']:.4f}")
                    logger.info(f"    Range: {stats['range']:.4f}")
                    logger.info(f"    Significant Locations: {stats['significant_locations']}")
                    
                logger.info("\nSpatial Diagnostics:")
                spatial_diag = summary['spatial_diagnostics']
                logger.info(f"  Moran's I (Residuals): {spatial_diag['moran_i_residuals']:.4f}")
                logger.info(f"  P-value: {spatial_diag['moran_i_p_value']:.4f}")
                logger.info(f"  Spatially Autocorrelated: {spatial_diag['residuals_spatially_autocorrelated']}")
                
                logger.info("\nModel Diagnostics:")
                model_diag = summary['model_diagnostics']
                logger.info(f"  Mean Condition Number: {model_diag['mean_condition_number']:.2f}")
                logger.info(f"  Problematic Locations: {model_diag['problematic_locations']}")
                logger.info(f"  Successful Estimations: {model_diag['successful_estimations']}")
                
            except Exception as e:
                logger.error(f"Failed to estimate GWR with {kernel.value}: {e}")
                
    except Exception as e:
        logger.error(f"GWR analysis failed: {e}")


def main():
    """Main analysis workflow."""
    
    logger.info("Starting Advanced Spatial Econometric Analysis Example")
    logger.info("=" * 80)
    
    # Generate example data
    data, coordinates = generate_example_data()
    
    logger.info(f"Data Summary:")
    logger.info(f"  Shape: {data.shape}")
    logger.info(f"  Date Range: {data['date'].min()} to {data['date'].max()}")
    logger.info(f"  Markets: {data['market_id'].nunique()}")
    logger.info(f"  Currency Zones: {data['currency_zone'].unique()}")
    
    # Run analyses
    demonstrate_spatial_regime_analysis(data, coordinates)
    demonstrate_dynamic_spatial_panel(data, coordinates)
    demonstrate_spatial_threshold_analysis(data, coordinates)
    demonstrate_gwr_analysis(data, coordinates)
    
    logger.info("=" * 80)
    logger.info("Advanced Spatial Analysis Complete")


if __name__ == "__main__":
    main()