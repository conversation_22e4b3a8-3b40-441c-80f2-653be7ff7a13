#!/usr/bin/env python3
"""Simple test to verify Interactive Fixed Effects is working."""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Test IFE import
try:
    from src.core.models.econometric import InteractiveFixedEffects
    print("✅ IFE import successful")
except ImportError as e:
    print(f"❌ IFE import failed: {e}")
    exit(1)

# Generate test data
np.random.seed(42)
n_markets = 20
n_periods = 30
n_obs = n_markets * n_periods

# Create panel data
dates = pd.date_range("2019-01-01", periods=n_periods, freq="MS")
market_ids = [f"M{i:03d}" for i in range(n_markets)]

panel_data = []
for t, date in enumerate(dates):
    for m, market_id in enumerate(market_ids):
        # Add interactive effects
        factor1 = 0.5 * np.sin(2 * np.pi * t / 12)  # Seasonal
        factor2 = 0.3 * (m / n_markets)  # Market-specific
        
        # Generate data with factors
        price = (
            50 + 
            10 * np.random.randn() +
            factor1 * (20 if m < n_markets/2 else -10) +
            factor2 * 15
        )
        
        panel_data.append({
            "market_id": market_id,
            "date": date,
            "price": price,
            "treatment": 1 if m < n_markets/2 else 0,
            "factor1_true": factor1,
            "factor2_true": factor2
        })

df = pd.DataFrame(panel_data)

# Test IFE model
print("\nTesting IFE model...")

# Create model
model = InteractiveFixedEffects(n_factors=2, robust_se=True)

# Prepare data
y = df["price"].values
X = df[["treatment"]].values
entity_ids = pd.Categorical(df["market_id"]).codes
time_ids = pd.Categorical(df["date"]).codes

# Fit model
try:
    model.fit(y, X, entity_ids, time_ids)
    print("✅ Model fitted successfully")
    
    # Check results stored as attributes
    print(f"\nResults:")
    if hasattr(model, 'beta'):
        print(f"- Coefficient on treatment: {model.beta[0]:.3f}")
    if hasattr(model, 'se'):
        print(f"- Standard error: {model.se[0]:.3f}")
    print(f"- Number of factors: {model.n_factors}")
    if hasattr(model, 'r_squared'):
        print(f"- R-squared: {model.r_squared:.3f}")
    
    # Extract factors
    if hasattr(model, 'F'):
        print(f"\nExtracted factors shape: {model.F.shape}")
    if hasattr(model, 'Lambda'):
        print(f"Factor loadings shape: {model.Lambda.shape}")
    
    print("\n✅ IFE is working correctly!")
    
except Exception as e:
    print(f"❌ Model fitting failed: {e}")
    import traceback
    traceback.print_exc()