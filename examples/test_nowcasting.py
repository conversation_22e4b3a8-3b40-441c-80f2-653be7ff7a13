#!/usr/bin/env python3
"""
Quick test script for nowcasting functionality.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime

# Test imports
try:
    from src.core.models.nowcasting import (
        NowcastingModel,
        DynamicFactorNowcast,
        SARIMAXNowcast,
        MachineLearningNowcast,
        EnsembleNowcast,
        EarlyWarningSystem
    )
    print("✓ Successfully imported nowcasting models")
except ImportError as e:
    print(f"✗ Failed to import nowcasting models: {e}")
    exit(1)

try:
    from src.infrastructure.estimators.nowcasting_estimators import (
        NowcastingOrchestrator,
        DynamicFactorEstimator,
        SARIMAXEstimator
    )
    print("✓ Successfully imported nowcasting estimators")
except ImportError as e:
    print(f"✗ Failed to import nowcasting estimators: {e}")
    exit(1)


def create_test_data():
    """Create minimal test data."""
    dates = pd.date_range('2023-01-01', '2023-12-01', freq='M')
    markets = ['Sana\'a', 'Aden']
    
    data = []
    np.random.seed(42)
    
    for date in dates:
        for market in markets:
            price = 100 + np.random.normal(0, 10)
            data.append({
                'market_id': market,
                'date': date,
                'usd_price': price,
                'currency': 'USD',
                'currency_zone': 'north' if market == 'Sana\'a' else 'south'
            })
            
    df = pd.DataFrame(data)
    df = df.set_index(['market_id', 'date'])
    return df


def test_basic_nowcasting():
    """Test basic nowcasting functionality."""
    print("\n" + "="*60)
    print("Testing Basic Nowcasting Functionality")
    print("="*60)
    
    # Create test data
    data = create_test_data()
    print(f"\n1. Created test data: {len(data)} observations")
    
    # Test SARIMAX nowcast
    print("\n2. Testing SARIMAX nowcast...")
    try:
        sarimax_model = SARIMAXNowcast(
            order=(1, 0, 1),
            seasonal_order=(0, 0, 0, 0),
            forecast_horizon=3,
            auto_order=False
        )
        
        # Filter for single series
        single_series = data[data.index.get_level_values('market_id') == 'Sana\'a'].copy()
        sarimax_model.fit(single_series)
        result = sarimax_model.predict()
        
        print(f"   ✓ SARIMAX forecast generated: {len(result.point_forecast)} periods")
        print(f"   ✓ Forecast values: {result.point_forecast.values}")
    except Exception as e:
        print(f"   ✗ SARIMAX test failed: {e}")
        
    # Test ML nowcast
    print("\n3. Testing Machine Learning nowcast...")
    try:
        ml_model = MachineLearningNowcast(
            model_type='random_forest',
            forecast_horizon=1,
            n_estimators=10  # Small for testing
        )
        
        ml_model.fit(data)
        result = ml_model.predict()
        
        print(f"   ✓ ML forecast generated: {len(result.point_forecast)} periods")
        print(f"   ✓ Number of features used: {len(ml_model.feature_names)}")
    except Exception as e:
        print(f"   ✗ ML test failed: {e}")
        
    # Test Early Warning System
    print("\n4. Testing Early Warning System...")
    try:
        # Create a simple nowcast result
        from src.core.models.nowcasting.nowcasting_models import NowcastResult
        
        nowcast_results = {
            'Sana\'a_wheat_flour': NowcastResult(
                point_forecast=pd.Series([150, 180, 220], 
                                       index=pd.date_range('2024-01-01', periods=3, freq='M')),
                prediction_intervals={
                    0.95: pd.DataFrame({
                        'lower': [140, 160, 190],
                        'upper': [160, 200, 250]
                    })
                },
                forecast_horizon=3,
                method='test'
            )
        }
        
        ews = EarlyWarningSystem(
            nowcast_model=None,  # Not needed for this test
            thresholds={
                'wheat_flour': {
                    'low': 1.2,
                    'medium': 1.5,
                    'high': 2.0,
                    'critical': 2.5
                }
            }
        )
        
        baseline_prices = pd.Series({'Sana\'a_wheat_flour': 100})
        warnings = ews.generate_warnings(nowcast_results, baseline_prices)
        
        print(f"   ✓ Generated {len(warnings)} warning signals")
        for warning in warnings:
            print(f"     - {warning.severity} warning for {warning.market_id}: {warning.signal_type}")
            
    except Exception as e:
        print(f"   ✗ Early warning test failed: {e}")
        
    print("\n" + "="*60)
    print("Nowcasting Tests Complete!")
    print("="*60)


if __name__ == "__main__":
    test_basic_nowcasting()