# Dependency Analysis: requirements.txt vs pyproject.toml

## Issues Found

### 1. Missing from pyproject.toml (present in requirements.txt)
- `psutil>=5.9.0` - System monitoring
- `jax>=0.6.0` - Numerical computing
- `esda>=2.7.0` - Spatial statistics
- `ipython>=9.0.0` - Interactive Python
- `mlflow>=2.22.0` - Experiment tracking (higher version)
- `neptune>=1.14.0` - Experiment tracking (higher version)
- `pydantic>=2.5.0` - Data validation
- `typer>=0.9.0` - CLI framework
- `isort>=5.12.0` - Import sorting

### 2. Missing from requirements.txt (present in pyproject.toml)
- `wandb>=0.16.0` - Experiment tracking
- `tensorflow>=2.12.0` - ML framework (optional)
- `torch>=2.0.0` - ML framework (optional)
- `pytest-mock>=3.11.0` - Testing
- `ruff>=0.0.270` - Linting
- `mypy>=1.3.0` - Type checking
- `pre-commit>=3.3.0` - Git hooks
- `nbconvert>=7.4.0` - Notebook conversion
- `sphinx>=6.2.0` - Documentation

### 3. Version Mismatches
- `mlflow`: requirements.txt has `>=2.22.0`, pyproject.toml has `>=2.10.0`
- `neptune`: requirements.txt has `>=1.14.0`, pyproject.toml has `>=1.8.0`

### 4. Structural Differences
- requirements.txt is flatter with basic dependencies
- pyproject.toml has better organization with optional dependency groups
- pyproject.toml separates dev, experiment, and ml dependencies

## Recommendations

1. **Use pyproject.toml as primary source** - It has better dependency management
2. **Update requirements.txt** to be a subset for production deployment
3. **Add missing critical dependencies** to pyproject.toml
4. **Consolidate experiment tracking** - choose between mlflow, neptune, wandb
5. **Create requirements.txt from pyproject.toml** for backwards compatibility

## Action Items

1. Add missing critical dependencies to pyproject.toml
2. Update version specifications to be consistent
3. Generate production requirements.txt from pyproject.toml
4. Test installation with both methods