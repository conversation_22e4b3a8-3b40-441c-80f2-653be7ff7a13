# Streamlined Data Pipeline Documentation

## Overview

The Yemen Market Integration project has been updated with a fully automated, integrated data pipeline that processes all data sources through a unified orchestration system within the `src/` directory structure.

## Architecture

### Core Components

1. **Data Pipeline Orchestrator** (`src/application/services/data_pipeline_orchestrator.py`)
   - Manages the complete workflow from collection to panel building
   - Coordinates all data sources (WFP, ACLED, ACAPS, HDX)
   - Enforces World Bank methodology standards
   - Provides progress tracking and validation

2. **Infrastructure Processors** (`src/infrastructure/processors/`)
   - `wfp_processor.py` - Handles WFP price and exchange rate data
   - `acled_processor.py` - Processes conflict event data
   - `acaps_processor.py` - Extracts control zone information from nested zips
   - `currency_aware_wfp_processor.py` - Enforces currency conversion

3. **CLI Interface** (`src/interfaces/cli/`)
   - `data_pipeline.py` - New commands for data management
   - Integrated into main CLI as `yemen-market data` subcommands

## Data Flow

```
1. Collection Stage
   ├── WFP prices/exchange rates (HDX API)
   ├── ACLED conflict events (API)
   ├── ACAPS control zones (nested zips)
   └── HDX boundaries (shapefiles)
           ↓
2. Processing Stage
   ├── Currency conversion enforcement
   ├── Zone classification
   ├── Spatial joins
   └── Temporal alignment
           ↓
3. Integration Stage
   ├── Market-zone-time mapping
   ├── Conflict aggregation
   └── Exchange rate application
           ↓
4. Validation Stage
   ├── Methodology compliance
   ├── Coverage verification (88.4% target)
   └── Statistical power checks
           ↓
5. Panel Building Stage
   ├── Balanced panel creation
   ├── Econometric preparation
   └── Analysis-ready output
```

## Usage

### Running Full Pipeline

```bash
# Run complete pipeline with defaults
uv run python src/cli.py data run-pipeline

# Customize date range and options
uv run python src/cli.py data run-pipeline \
    --start-date 2019-01-01 \
    --end-date 2025-03-01 \
    --force \
    --commodities wheat sugar fuel
```

### Updating Specific Sources

```bash
# Update only WFP and ACLED data
uv run python src/cli.py data update-data wfp acled

# Force refresh of ACAPS control zones
uv run python src/cli.py data update-data acaps --force
```

### Validating Data

```bash
# Validate integrated panel
uv run python src/cli.py data validate-data

# Validate specific file
uv run python src/cli.py data validate-data \
    --data-path data/processed/integrated_panel/yemen_integrated_balanced_panel.parquet
```

## Key Improvements

### 1. Unified Orchestration
- Single entry point for all data processing
- Consistent error handling and logging
- Progress tracking throughout pipeline
- Atomic operations with rollback capability

### 2. ACAPS Processing Enhancement
- Handles nested zip structure automatically
- Extracts multiple control authority shapefiles (DFA, IRG, STC, AQAP)
- Creates consolidated control zones dataset
- Maintains temporal history of control changes

### 3. Currency Enforcement
- All prices automatically converted to USD
- Multi-source exchange rate validation
- Zone-specific rate application
- Blocks analysis of unconverted data

### 4. CLI Integration
- Simple commands for complex operations
- Rich console output with progress bars
- Comprehensive validation reporting
- Batch and targeted updates

## Configuration

### Pipeline Configuration

```python
PipelineConfig(
    # Date range
    start_date=datetime(2019, 1, 1),
    end_date=datetime(2025, 3, 1),
    
    # Data sources
    include_wfp=True,
    include_acled=True,
    include_acaps=True,
    include_hdx=True,
    
    # Processing options
    validate_currency=True,
    enforce_coverage=True,
    target_coverage=0.884,  # 88.4%
    
    # Panel building
    create_balanced_panel=True,
    commodities=["wheat", "sugar", "fuel", "rice"],
    min_markets_per_commodity=10,
    
    # Output
    output_dir=Path("data/processed"),
    save_intermediate=True
)
```

### Environment Variables

```bash
# Required for external APIs
export HDX_API_KEY="your_hdx_key"
export ACLED_API_KEY="your_acled_key"
export EXCHANGE_RATE_API_KEY="your_exchange_rate_key"

# Optional configuration
export YEMEN_MARKET_LOG_LEVEL="INFO"
export YEMEN_MARKET_CACHE_DIR="/tmp/yemen_market_cache"
```

## Monitoring & Diagnostics

### Pipeline Status

The orchestrator provides detailed status information:

```python
PipelineStatus(
    pipeline_id=UUID,
    stage=PipelineStage.PROCESSING,
    stages_completed={
        COLLECTION: True,
        PROCESSING: False,
        ...
    },
    current_stage_progress=0.75,
    records_collected={"wfp": 33926, "acled": 15234},
    coverage_achieved=0.95,
    errors=[],
    warnings=["Missing data for 5 markets"]
)
```

### Logging

Structured logging throughout:

```
2025-06-05 14:15:23 INFO  [data_pipeline_orchestrator] Starting data collection stage
2025-06-05 14:15:45 INFO  [acaps_processor] Found nested zip: Yemen Analysis Hub - Areas of control_shapefile.zip
2025-06-05 14:16:12 INFO  [acaps_processor] Loading DFA control zone
2025-06-05 14:16:15 INFO  [acaps_processor] Loading IRG control zone
2025-06-05 14:16:30 INFO  [methodology_validator] ✅ Currency conversion check PASSED
```

## Error Handling

### Common Issues & Solutions

1. **ACAPS Nested Zip Error**
   - **Issue**: "No shapefile found in data/raw/acaps/*.zip"
   - **Solution**: Processor now handles nested zips automatically

2. **Missing Control Zones**
   - **Issue**: "Control zones file not found"
   - **Solution**: Run `uv run python src/cli.py data update-data acaps`

3. **Currency Validation Failure**
   - **Issue**: "Critical failures: ['Missing USD prices for 1000 observations']"
   - **Solution**: Ensure exchange rate data is current; force refresh if needed

4. **Low Coverage Warning**
   - **Issue**: "Coverage achieved: 75% (target: 88.4%)"
   - **Solution**: Check date range and market availability

## Migration from Old Scripts

### Deprecated Scripts (Removed)
- `fix_exchange_rate_analysis.py` → Use pipeline orchestrator
- `simple_lock_plan.py` → Integrated into panel builder
- `process_acaps_simple.py` → Use ACAPS processor
- `test_exchange_rate_coverage.py` → Use `data validate-data`

### Essential Scripts (Retained)
Scripts in `scripts/` directory remain for:
- Manual debugging and inspection
- Legacy compatibility
- Direct processor testing

But primary workflow should use CLI commands.

## Best Practices

1. **Always validate before analysis**
   ```bash
   uv run python src/cli.py data validate-data
   ```

2. **Use force refresh sparingly**
   - Only when data sources have updates
   - Or when debugging data issues

3. **Monitor pipeline progress**
   - Check logs for detailed information
   - Use status callbacks in programmatic usage

4. **Maintain data lineage**
   - Keep intermediate files for debugging
   - Document any manual interventions

## Future Enhancements

1. **Scheduled Updates**
   - Airflow/Prefect integration for automated runs
   - Incremental updates for efficiency

2. **Data Quality Metrics**
   - Automated anomaly detection
   - Historical comparison reports

3. **Parallel Processing**
   - Concurrent source processing
   - Distributed computation for large datasets

4. **External Integration**
   - Direct database loading
   - API endpoints for data access

---

The streamlined data pipeline represents a significant improvement in automation, reliability, and compliance with World Bank research standards. All data processing now flows through validated, tested components within the src/ directory structure.