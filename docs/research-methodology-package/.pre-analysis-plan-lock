# Pre-Analysis Plan Lock File - LOCKED
# This file provides cryptographic proof of pre-commitment
# ANY MODIFICATION of this file invalidates the pre-commitment

VERSION=1.0
LOCK_DATE=2025-06-03T17:46:16.086113
LOCK_COMMIT_HASH=f0fb3a31c0585cf490bd82b428a415c9066be0ba
PLAN_SHA256_HASH=5676fdf70fe12ff993d52ad3616ded235152645c3d56dc73f936cacb28e1ae4b

# Files locked:
# - docs/research-methodology-package/00-overview/PRE_ANALYSIS_PLAN.md (MD5: 013b5608555060fd9b62086952ba96fc)
# - docs/research-methodology-package/03-econometric-methodology/identification-strategies/power-analysis.md (MD5: c47876e0ab84da89a52de1d61c669cb5)
# - docs/research-methodology-package/00-overview/ANALYSIS_WORKFLOW.md (MD5: 212353fe187e5ae3390f06279985c0eb)
# - src/core/models/pre_analysis/locked_specifications.py (MD5: 9b66ae57606b30f7e15ec7d59cf1eaaa)

# CRYPTOGRAPHIC VERIFICATION
LOCK_INTEGRITY=VALID
LOCKED_BY=Claude-Code-Agent
LOCK_TIMESTAMP=2025-06-03T17:46:16.086113

# This lock prevents:
# 1. Modification of hypothesis specifications after seeing data
# 2. Addition of new hypotheses not in the original plan  
# 3. Changes to sample criteria or variable definitions
# 4. Alterations to multiple testing correction procedures
# 5. Modifications to the analysis workflow sequence

# Full metadata (JSON):
{
  "version": "1.0",
  "lock_timestamp": "2025-06-03T17:46:16.086113",
  "git_commit_hash": "f0fb3a31c0585cf490bd82b428a415c9066be0ba",
  "plan_hash": "5676fdf70fe12ff993d52ad3616ded235152645c3d56dc73f936cacb28e1ae4b",
  "file_hashes": {
    "docs/research-methodology-package/00-overview/PRE_ANALYSIS_PLAN.md": "013b5608555060fd9b62086952ba96fc",
    "docs/research-methodology-package/03-econometric-methodology/identification-strategies/power-analysis.md": "c47876e0ab84da89a52de1d61c669cb5",
    "docs/research-methodology-package/00-overview/ANALYSIS_WORKFLOW.md": "212353fe187e5ae3390f06279985c0eb",
    "src/core/models/pre_analysis/locked_specifications.py": "9b66ae57606b30f7e15ec7d59cf1eaaa"
  },
  "status": "LOCKED"
}
