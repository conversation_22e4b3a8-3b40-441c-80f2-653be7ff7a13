# Replication Package: Market Integration Under Institutional Fragmentation

**Complete Documentation and Code for Yemen Market Integration Analysis**

## Overview

This replication package provides all materials necessary to reproduce the findings in "Market Integration in Conflict Settings: A Spatial Econometric Analysis of Yemen's Fragmented Economy." The package includes data processing scripts, analytical code, robustness testing procedures, and documentation meeting the highest standards of reproducible research.

---

## Package Structure

```
replication_package/
├── README.md                              # This file
├── REQUIREMENTS.md                        # System and software requirements
├── LICENSE.md                            # Data usage license and attributions
├── CHANGELOG.md                          # Version history and updates
├── data/
│   ├── raw/                              # Original data sources (where shareable)
│   │   ├── wfp_prices/                   # WFP price monitoring data
│   │   ├── acled_events/                 # ACLED conflict data
│   │   ├── acaps_control/                # ACAPS territorial control
│   │   ├── exchange_rates/               # Exchange rate sources
│   │   └── geographic/                   # Market coordinates and boundaries
│   ├── processed/                        # Cleaned and analysis-ready datasets
│   │   ├── master_dataset.csv           # Main analytical dataset
│   │   ├── currency_verified_prices.csv # Prices after currency verification
│   │   ├── market_clusters.csv          # Machine learning cluster assignments
│   │   └── cross_country_data.csv       # External validation data
│   ├── intermediate/                     # Intermediate processing outputs
│   └── metadata/                         # Data dictionaries and documentation
├── code/
│   ├── 00_setup/
│   │   ├── install_requirements.py      # Setup script for dependencies
│   │   ├── download_data.py             # Data download automation
│   │   └── verify_setup.py              # Environment verification
│   ├── 01_data_processing/
│   │   ├── currency_verification.py     # Currency verification protocols
│   │   ├── data_cleaning.py             # Data cleaning and harmonization
│   │   ├── missing_data_handling.py     # Missing data treatment
│   │   ├── spatial_processing.py        # Geographic data processing
│   │   └── quality_assurance.py         # Data quality validation
│   ├── 02_analysis/
│   │   ├── tier1_pooled_analysis.py     # Tier 1: Pooled panel analysis
│   │   ├── tier2_commodity_analysis.py  # Tier 2: Commodity-specific models
│   │   ├── tier3_validation.py          # Tier 3: External validation
│   │   ├── machine_learning_clustering.py # ML market clustering
│   │   ├── bayesian_panel_models.py     # Bayesian estimation
│   │   └── regime_switching_models.py   # Markov-switching VECM
│   ├── 03_robustness/
│   │   ├── exchange_rate_sensitivity.py # Exchange rate robustness tests
│   │   ├── model_specification_tests.py # Model specification robustness
│   │   ├── missing_data_sensitivity.py  # Missing data sensitivity
│   │   ├── outlier_analysis.py          # Outlier detection and treatment
│   │   └── bootstrap_inference.py       # Bootstrap confidence intervals
│   ├── 04_cross_country/
│   │   ├── syria_analysis.py            # Syria external validation
│   │   ├── lebanon_analysis.py          # Lebanon external validation
│   │   ├── somalia_analysis.py          # Somalia external validation
│   │   └── cross_country_comparison.py  # Comparative analysis
│   ├── 05_visualization/
│   │   ├── generate_figures.py          # All paper figures
│   │   ├── generate_tables.py           # All paper tables
│   │   ├── interactive_maps.py          # Interactive visualizations
│   │   └── robustness_plots.py          # Robustness testing plots
│   ├── 06_policy_analysis/
│   │   ├── counterfactual_scenarios.py  # Policy counterfactuals
│   │   ├── welfare_analysis.py          # Welfare impact calculations
│   │   └── humanitarian_implications.py # Humanitarian programming analysis
│   └── 99_utilities/
│       ├── helper_functions.py          # Common utility functions
│       ├── logging_config.py            # Logging configuration
│       └── validation_tests.py          # Code validation tests
├── output/
│   ├── figures/                         # Generated figures
│   │   ├── main_paper/                  # Main paper figures
│   │   ├── appendix/                    # Appendix figures
│   │   └── supplementary/               # Additional visualizations
│   ├── tables/                          # Generated tables
│   │   ├── main_paper/                  # Main paper tables
│   │   ├── appendix/                    # Appendix tables
│   │   └── robustness/                  # Robustness testing tables
│   ├── results/                         # Analysis results
│   │   ├── tier1_results/               # Tier 1 outputs
│   │   ├── tier2_results/               # Tier 2 outputs
│   │   ├── tier3_results/               # Tier 3 outputs
│   │   └── robustness_results/          # Robustness testing outputs
│   └── logs/                            # Execution logs
├── documentation/
│   ├── data_dictionary.md               # Complete variable documentation
│   ├── methodology_guide.md             # Detailed methodology
│   ├── software_guide.md                # Software and technical requirements
│   ├── troubleshooting.md               # Common issues and solutions
│   └── api_reference.md                 # Code documentation
├── tests/
│   ├── unit_tests/                      # Unit tests for individual functions
│   ├── integration_tests/               # Integration tests for workflows
│   ├── validation_tests/                # Data validation tests
│   └── regression_tests/                # Results consistency tests
├── validation/
│   ├── cross_country_data/              # External validation datasets
│   ├── benchmark_results/               # Expected results for validation
│   └── validation_reports/              # Validation test reports
├── scripts/
│   ├── run_full_replication.py          # Master replication script
│   ├── run_main_analysis.py             # Core analysis only
│   ├── run_robustness_tests.py          # Robustness testing only
│   └── generate_paper_outputs.py        # Paper figures and tables only
└── environments/
    ├── conda_environment.yml            # Conda environment specification
    ├── requirements.txt                  # Python package requirements
    └── docker/                          # Docker containerization files
```

---

## Quick Start Guide

### 1. System Requirements

**Minimum Requirements:**
- Python 3.8 or higher
- 16 GB RAM (32 GB recommended for full analysis)
- 50 GB free disk space
- Git for version control

**Recommended Environment:**
- Linux or macOS (Windows with WSL2)
- Python 3.9 or 3.10
- Jupyter Lab for interactive analysis
- VS Code or similar IDE for code development

### 2. Installation

```bash
# Clone the repository
git clone [REPOSITORY_URL]
cd replication_package

# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install requirements
pip install -r requirements.txt

# Verify installation
python code/00_setup/verify_setup.py
```

### 3. Data Setup

```bash
# Download and prepare data (if permissions allow)
python code/00_setup/download_data.py

# Verify data integrity
python code/00_setup/verify_data.py

# Set up directory structure
python code/00_setup/initialize_directories.py
```

### 4. Run Complete Replication

```bash
# Full replication (2-4 hours depending on system)
python scripts/run_full_replication.py

# Or run individual components
python scripts/run_main_analysis.py
python scripts/run_robustness_tests.py
python scripts/generate_paper_outputs.py
```

---

## Detailed Usage Instructions

### Data Processing Pipeline

The data processing pipeline consists of several sequential steps that must be completed in order:

#### Step 1: Currency Verification

```python
from code.data_processing.currency_verification import CurrencyVerificationProtocol

# Initialize currency verification
verifier = CurrencyVerificationProtocol(
    price_data='data/raw/wfp_prices/monthly_prices.csv',
    exchange_rates='data/raw/exchange_rates/',
    territorial_control='data/raw/acaps_control/control_data.csv'
)

# Run verification process
verified_data = verifier.run_full_verification()

# Save results
verified_data.to_csv('data/processed/currency_verified_prices.csv', index=False)
```

#### Step 2: Data Cleaning and Quality Assurance

```python
from code.data_processing.data_cleaning import DataCleaner

# Initialize cleaner
cleaner = DataCleaner(
    input_file='data/processed/currency_verified_prices.csv',
    validation_rules='code/data_processing/validation_rules.json'
)

# Run cleaning process
cleaned_data = cleaner.run_cleaning_pipeline()

# Generate quality report
quality_report = cleaner.generate_quality_report()
```

#### Step 3: Missing Data Treatment

```python
from code.data_processing.missing_data_handling import MissingDataHandler

# Initialize handler
handler = MissingDataHandler(
    data=cleaned_data,
    missing_threshold=0.5,  # Maximum allowable missingness
    imputation_methods=['multiple_imputation', 'selection_model']
)

# Apply missing data procedures
final_data = handler.process_missing_data()
```

### Main Analysis Workflow

#### Tier 1: Pooled Panel Analysis

```python
from code.analysis.tier1_pooled_analysis import Tier1Analysis

# Initialize analysis
tier1 = Tier1Analysis(
    data='data/processed/master_dataset.csv',
    config='code/analysis/tier1_config.json'
)

# Run machine learning clustering
clusters = tier1.run_ml_clustering()

# Run Bayesian panel estimation
bayesian_results = tier1.run_bayesian_estimation()

# Generate spatial weights matrices
spatial_weights = tier1.generate_spatial_weights()

# Save results
tier1.save_results('output/results/tier1_results/')
```

#### Tier 2: Commodity-Specific Analysis

```python
from code.analysis.tier2_commodity_analysis import Tier2Analysis

# Initialize commodity analysis
tier2 = Tier2Analysis(
    data='data/processed/master_dataset.csv',
    clusters=clusters
)

# Run regime-switching models
regime_results = tier2.run_regime_switching_models()

# Run threshold models
threshold_results = tier2.run_threshold_models()

# Analyze time-varying parameters
timevar_results = tier2.run_timevarying_analysis()

# Save results
tier2.save_results('output/results/tier2_results/')
```

#### Tier 3: External Validation

```python
from code.analysis.tier3_validation import Tier3Analysis

# Initialize validation
tier3 = Tier3Analysis(
    yemen_results={'tier1': tier1, 'tier2': tier2},
    external_data='data/processed/cross_country_data.csv'
)

# Run cross-country validation
cross_country_results = tier3.run_cross_country_validation()

# Run out-of-sample prediction
prediction_results = tier3.run_prediction_validation()

# Save results
tier3.save_results('output/results/tier3_results/')
```

### Robustness Testing

#### Exchange Rate Sensitivity

```python
from code.robustness.exchange_rate_sensitivity import ExchangeRateSensitivityAnalysis

# Initialize sensitivity analysis
sensitivity = ExchangeRateSensitivityAnalysis(
    base_results=tier1.results,
    price_data='data/processed/master_dataset.csv'
)

# Run comprehensive sensitivity testing
sensitivity_results = sensitivity.run_comprehensive_sensitivity()

# Generate sensitivity plots
sensitivity.generate_sensitivity_plots()
```

#### Model Specification Robustness

```python
from code.robustness.model_specification_tests import ModelSpecificationRobustness

# Initialize specification testing
spec_tests = ModelSpecificationRobustness(
    data='data/processed/master_dataset.csv'
)

# Run specification tests
spec_results = spec_tests.run_specification_tests()

# Generate robustness summary
robustness_summary = spec_tests.generate_robustness_summary()
```

### Generating Paper Outputs

#### Figures

```python
from code.visualization.generate_figures import FigureGenerator

# Initialize figure generator
fig_gen = FigureGenerator(
    data=final_data,
    results={'tier1': tier1, 'tier2': tier2, 'tier3': tier3}
)

# Generate all figures
figures = fig_gen.generate_all_figures()

# Save figures
fig_gen.save_figures('output/figures/main_paper/')
```

#### Tables

```python
from code.visualization.generate_tables import TableGenerator

# Initialize table generator
table_gen = TableGenerator(
    results={'tier1': tier1, 'tier2': tier2, 'tier3': tier3}
)

# Generate all tables
tables = table_gen.generate_all_tables()

# Save tables
table_gen.save_tables('output/tables/main_paper/')
```

---

## Configuration and Customization

### Configuration Files

The replication package uses JSON configuration files to manage parameters:

#### Main Analysis Configuration

```json
{
  "tier1_config": {
    "clustering": {
      "method": "gaussian_mixture",
      "n_components_range": [2, 10],
      "selection_criterion": "silhouette"
    },
    "bayesian_estimation": {
      "draws": 2000,
      "tune": 1000,
      "cores": 4,
      "chains": 4
    },
    "spatial_weights": {
      "geographic_threshold_km": 500,
      "institutional_weight": 0.4,
      "economic_weight": 0.3,
      "geographic_weight": 0.3
    }
  },
  "tier2_config": {
    "regime_switching": {
      "n_regimes": 2,
      "switching_variance": true,
      "max_iterations": 1000
    },
    "threshold_models": {
      "trim_percentage": 0.15,
      "bootstrap_iterations": 1000
    }
  },
  "robustness_config": {
    "exchange_rate_scenarios": [
      "conservative",
      "market_based", 
      "mixed",
      "time_varying"
    ],
    "missing_data_methods": [
      "complete_case",
      "multiple_imputation",
      "selection_model"
    ]
  }
}
```

### Customizing Analysis

#### Adding New Exchange Rate Scenarios

```python
# Edit code/robustness/exchange_rate_sensitivity.py

def add_custom_scenario(self, scenario_name: str, rate_function: callable):
    """
    Add custom exchange rate scenario
    
    Parameters:
    -----------
    scenario_name : str
        Name for the new scenario
    rate_function : callable
        Function that takes (location, date) and returns exchange rate
    """
    self.custom_scenarios[scenario_name] = rate_function
```

#### Adding New Countries for External Validation

```python
# Edit code/cross_country/cross_country_comparison.py

def add_validation_country(self, country_name: str, data_path: str, 
                          institutional_mapping: dict):
    """
    Add new country for external validation
    
    Parameters:
    -----------
    country_name : str
        Name of the country
    data_path : str  
        Path to harmonized price data
    institutional_mapping : dict
        Mapping of institutional boundaries
    """
    self.validation_countries[country_name] = {
        'data': self._load_country_data(data_path),
        'institutions': institutional_mapping
    }
```

---

## Testing and Validation

### Running Tests

```bash
# Run all tests
python -m pytest tests/ -v

# Run specific test categories
python -m pytest tests/unit_tests/ -v
python -m pytest tests/integration_tests/ -v
python -m pytest tests/validation_tests/ -v

# Run tests with coverage
python -m pytest tests/ --cov=code --cov-report=html
```

### Validation Procedures

#### Data Validation

```python
from tests.validation_tests.data_validation import DataValidator

# Initialize validator
validator = DataValidator()

# Run comprehensive data validation
validation_results = validator.run_all_validations()

# Check results
assert validation_results['all_passed'], "Data validation failed"
```

#### Results Validation

```python
from tests.validation_tests.results_validation import ResultsValidator

# Initialize results validator
results_validator = ResultsValidator(
    benchmark_results='validation/benchmark_results/',
    current_results='output/results/'
)

# Validate results against benchmarks
validation_status = results_validator.validate_all_results()
```

---

## Performance Optimization

### Parallel Processing

The replication package supports parallel processing for computationally intensive operations:

#### Configuration for Parallel Processing

```python
# Edit configuration files or use environment variables
import os

# Set number of cores for different operations
os.environ['NUMBA_NUM_THREADS'] = '4'
os.environ['MKL_NUM_THREADS'] = '4'
os.environ['OMP_NUM_THREADS'] = '4'

# For Bayesian estimation
BAYESIAN_CORES = 4
BAYESIAN_CHAINS = 4

# For bootstrap operations
BOOTSTRAP_CORES = 4
```

#### Memory Management

```python
# For large datasets, use chunking
def process_large_dataset(data_path: str, chunk_size: int = 10000):
    """
    Process large datasets in chunks to manage memory
    """
    for chunk in pd.read_csv(data_path, chunksize=chunk_size):
        processed_chunk = process_data_chunk(chunk)
        yield processed_chunk
```

### Runtime Estimates

**Full Replication Runtime (approximate):**
- Data processing: 30-60 minutes
- Tier 1 analysis: 45-90 minutes  
- Tier 2 analysis: 60-120 minutes
- Tier 3 validation: 30-60 minutes
- Robustness testing: 60-180 minutes
- Figure/table generation: 15-30 minutes

**Total: 4-8 hours** (depends on system specifications)

**Memory Requirements:**
- Peak memory usage: 12-16 GB
- Recommended available: 20+ GB
- Disk space for outputs: 5-10 GB

---

## Troubleshooting

### Common Issues and Solutions

#### Installation Issues

**Problem**: Package installation failures
```bash
# Solution: Use conda for complex dependencies
conda env create -f environments/conda_environment.yml
conda activate yemen_analysis
```

**Problem**: GDAL/GeoPandas installation issues
```bash
# Solution: Install system dependencies first
# Ubuntu/Debian:
sudo apt-get install gdal-bin libgdal-dev

# macOS:
brew install gdal

# Then install Python packages:
pip install gdal==$(gdal-config --version)
pip install geopandas
```

#### Memory Issues

**Problem**: Out of memory errors during analysis
```python
# Solution: Reduce batch sizes and use chunking
# Edit configuration files:
{
  "processing": {
    "chunk_size": 5000,  # Reduce from default 10000
    "parallel_processes": 2  # Reduce from default 4
  }
}
```

#### Data Access Issues

**Problem**: Missing or corrupted data files
```bash
# Solution: Re-download and verify data
python code/00_setup/download_data.py --force-redownload
python code/00_setup/verify_data.py --repair-corrupted
```

#### Convergence Issues

**Problem**: Bayesian models not converging
```python
# Solution: Adjust sampling parameters
bayesian_config = {
    "draws": 4000,  # Increase from 2000
    "tune": 2000,   # Increase from 1000
    "target_accept": 0.8  # Reduce from default 0.95
}
```

### Getting Help

**Documentation**: Check `documentation/` directory for detailed guides

**Issues**: Report issues at [ISSUE_TRACKER_URL]

**Questions**: Contact research team at [CONTACT_EMAIL]

**Updates**: Check [REPOSITORY_URL] for updates and patches

---

## Data Sources and Attributions

### Primary Data Sources

**World Food Programme (WFP)**
- Market price monitoring data
- Usage: Academic research with proper attribution
- Citation: World Food Programme. (2024). Yemen Market Price Monitoring Data.

**Armed Conflict Location & Event Data Project (ACLED)**  
- Conflict event database
- Usage: Open access with attribution
- Citation: Raleigh, C., et al. (2010). "Introducing ACLED-Armed Conflict Location and Event Data." Journal of Peace Research, 47(5), 651-660.

**Assessment Capacities Project (ACAPS)**
- Territorial control mapping
- Usage: Humanitarian research with attribution
- Citation: ACAPS. (2024). Yemen - Areas of Control Analysis.

### Exchange Rate Sources

**Central Bank of Yemen - Aden**
- Official exchange rates for government areas
- Usage: Public data

**Central Bank of Yemen - Sana'a**
- Official exchange rates for Houthi areas  
- Usage: Public data

**Parallel Market Sources**
- Various money changer networks
- Usage: Anonymized for research purposes

### External Validation Data

**Syria**: Multiple humanitarian and research sources
**Lebanon**: Central bank and commercial sources
**Somalia**: UN agencies and research institutions

*All data usage complies with applicable privacy laws and research ethics standards.*

---

## Citation and Attribution

### Main Paper Citation

```
[Authors]. (2025). "Market Integration in Conflict Settings: A Spatial Econometric 
Analysis of Yemen's Fragmented Economy." [Journal/Working Paper].
```

### Replication Package Citation

```
[Authors]. (2025). "Replication Package: Market Integration Under Institutional 
Fragmentation - Yemen Analysis." [Repository/DOI].
```

### Software Attribution

This replication package uses the following open-source software:
- Python and scientific computing ecosystem
- PyMC3 for Bayesian analysis
- scikit-learn for machine learning
- GeoPandas for spatial analysis
- Statsmodels for econometric analysis

*See `requirements.txt` for complete package list with versions.*

---

## License and Terms of Use

### Code License

The analytical code in this replication package is released under the MIT License:

```
MIT License

Copyright (c) 2025 [Authors]

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

### Data Usage Terms

**Research Use**: Data included in this package is for academic and research purposes only.

**Attribution Required**: All use must include proper attribution to original data sources.

**No Commercial Use**: Commercial use requires separate permissions from data providers.

**Ethical Use**: Users must comply with ethical research standards and applicable laws.

### Disclaimer

The authors provide this replication package "as is" without warranty. While every effort has been made to ensure accuracy and reliability, users are responsible for verifying results and ensuring appropriate use.

**Data Quality**: While comprehensive quality checks have been performed, users should be aware of data limitations documented in the research paper.

**Context Sensitivity**: Results are specific to the Yemen context and time period. Generalization to other contexts requires careful consideration.

**Policy Implications**: Policy recommendations are based on analytical findings and should be considered alongside other information sources.

---

## Version History and Updates

### Version 1.0.0 (June 2025)
- Initial release with complete replication package
- All three-tier analysis components
- Comprehensive robustness testing
- Cross-country external validation

### Planned Updates
- Enhanced visualization capabilities
- Additional robustness tests based on reviewer feedback
- Extended external validation to additional countries
- Performance optimizations for large-scale analysis

### Requesting Updates

To request updates or report issues:
1. Check existing issues at [ISSUE_TRACKER_URL]
2. Create new issue with detailed description
3. Include system information and error messages
4. Specify whether issue affects replication results

---

## Acknowledgments

This replication package was developed with support from:
- [Research institution acknowledgments]
- [Funding source acknowledgments]  
- [Data provider acknowledgments]
- [Technical support acknowledgments]

Special thanks to the humanitarian organizations maintaining data collection in challenging conditions, enabling this research to contribute to evidence-based programming in conflict settings.

---

**Contact Information**

**Technical Support**: [TECHNICAL_EMAIL]  
**Research Questions**: [RESEARCH_EMAIL]  
**Data Issues**: [DATA_EMAIL]  
**General Inquiries**: [GENERAL_EMAIL]

**Website**: [PROJECT_WEBSITE]  
**Repository**: [REPOSITORY_URL]  
**Documentation**: [DOCUMENTATION_URL]