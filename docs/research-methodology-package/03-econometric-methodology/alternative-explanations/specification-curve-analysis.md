# Specification Curve Analysis for Alternative Explanations

## Overview

Specification curve analysis systematically tests all reasonable combinations of model specifications to assess the robustness of competing hypotheses. This approach prevents cherry-picking specifications and provides transparent evidence about which explanations are robust across modeling choices.

## Theoretical Foundation

### Core Principle

**Specification Robustness**: A hypothesis is credible only if it remains significant across the majority of reasonable specification choices. Sensitivity to specific modeling decisions suggests fragile results.

### Analysis Dimensions

1. **Sample Variations**: Different time periods, geographic regions, commodity subsets
2. **Variable Definitions**: Alternative measures of key concepts
3. **Functional Forms**: Linear, log-linear, polynomial specifications
4. **Fixed Effects**: Market, time, commodity, zone combinations
5. **Standard Error Corrections**: Clustering levels and robust estimators
6. **Control Variables**: Different sets of confounding variables

## Specification Universe Definition

### Core Design Principle

Focus on **meaningful specification variations** that represent genuine analytical choices, not arbitrary combinations. Target ~1,000 specifications that test substantive robustness.

### Dimension 1: Sample Selection (4 variations)

```
S1: Full sample (2019-2024)
S2: Pre-escalation (2019-2021)
S3: Post-escalation (2022-2024)
S4: Balanced panel only
```

### Dimension 2: Price Specification (3 variations)

```
P1: log(Price_YER)
P2: log(Price_USD)
P3: Price_YER with exchange rate control
```

### Dimension 3: Hypothesis Tests (12 variations)

```
Single Hypotheses:
H1: Exchange Rate Mechanism (baseline)
H2: Quality Differences (ALT1)
H3: Market Power (ALT2)
H4: Supply Disruption (ALT3)
H5: Government Policy (ALT4)
H6: Demand Destruction (ALT5)
H7: Transaction Costs (ALT6)

Key Combinations:
H8: Exchange Rate + Quality
H9: Exchange Rate + Supply Disruption
H10: Exchange Rate + Government Policy
H11: Quality + Market Power + Supply
H12: All significant alternatives
```

### Dimension 4: Model Specifications (4 levels per hypothesis)

```
M1: Baseline (hypothesis variable only)
M2: + Zone controls
M3: + Standard controls (conflict, distance, seasonality)
M4: + Full controls (all available covariates)
```

### Dimension 5: Estimation Strategy (3 approaches)

```
E1: Pooled OLS with robust SE
E2: Fixed effects (market + time) with clustered SE
E3: Random effects with bootstrap SE
```

### Dimension 6: Robustness Checks (3 variations)

```
R1: Full sample
R2: Outliers removed (1% tails)
R3: Conflict-intensity weighted
```

## Total Specification Count

**Primary Specifications**: 4 × 3 × 7 × 4 × 3 × 3 = 1,008 specifications

This focused approach tests meaningful variations while maintaining computational feasibility.

## Implementation Protocol

### Stage 1: Specification Generation

```python
import itertools
import pandas as pd

# Define specification dimensions
samples = ['full', 'pre_escalation', 'post_escalation', 'food_only', 'fuel_only', 'north', 'south', 'urban']
dependent_vars = ['log_price_yer', 'log_price_usd', 'price_yer', 'price_ratio']
explanatory_sets = ['exchange_only', 'quality_only', 'power_only', 'exchange_quality', 'exchange_power', 'all_vars']
control_levels = ['minimal', 'basic', 'standard', 'extended']
fixed_effects = ['none', 'market', 'time', 'market_time', 'market_time_commodity', 'all_fe']
se_types = ['ols', 'robust', 'cluster_market', 'cluster_two_way']

# Generate all combinations
specifications = list(itertools.product(
    samples, dependent_vars, explanatory_sets,
    control_levels, fixed_effects, se_types
))

print(f"Total specifications to estimate: {len(specifications)}")
```

### Stage 2: Structured Estimation

```python
from typing import Dict, List, Tuple
import pandas as pd
import numpy as np
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm

def estimate_specification(spec_dict: Dict) -> Dict:
    """Estimate single specification with proper error handling."""
    try:
        # Extract specification parameters
        sample = spec_dict['sample']
        price_spec = spec_dict['price_spec']
        hypothesis = spec_dict['hypothesis']
        model_spec = spec_dict['model_spec']
        estimation = spec_dict['estimation']
        robustness = spec_dict['robustness']

        # Prepare data
        data = prepare_data(
            sample_period=sample,
            price_variable=price_spec,
            outlier_handling=robustness
        )

        # Build model based on hypothesis
        model = build_hypothesis_model(
            data=data,
            hypothesis=hypothesis,
            controls=model_spec,
            estimation_method=estimation
        )

        # Estimate and extract results
        results = model.fit()

        # Extract hypothesis-specific coefficient
        hypothesis_vars = {
            'H1': 'log_exchange_rate',
            'H2': 'quality_index',
            'H3': 'market_concentration',
            'H4': 'infrastructure_damage',
            'H5': 'policy_regime',
            'H6': 'population_change',
            'H7': 'transport_cost'
        }

        main_var = hypothesis_vars.get(hypothesis, 'unknown')

        return {
            **spec_dict,
            'coefficient': results.params.get(main_var, np.nan),
            'std_error': results.std_errors.get(main_var, np.nan),
            'p_value': results.pvalues.get(main_var, np.nan),
            't_statistic': results.tstats.get(main_var, np.nan),
            'r_squared': results.rsquared,
            'adj_r_squared': results.rsquared_adj,
            'n_obs': results.nobs,
            'aic': results.aic,
            'bic': results.bic,
            'converged': True,
            'zone_effect_remains': results.pvalues.get('is_houthi', 1.0) < 0.05
        }

    except Exception as e:
        return {
            **spec_dict,
            'error': str(e),
            'converged': False
        }

# Generate specifications
specifications = generate_specifications()
print(f"Total specifications to estimate: {len(specifications)}")

# Run estimation with progress bar
results = []
with ProcessPoolExecutor(max_workers=8) as executor:
    futures = {executor.submit(estimate_specification, spec): spec
               for spec in specifications}

    for future in tqdm(as_completed(futures), total=len(specifications)):
        result = future.result()
        results.append(result)

# Convert to DataFrame for analysis
spec_curve_df = pd.DataFrame(results)
print(f"Successfully estimated {spec_curve_df['converged'].sum()} specifications")
```

### Stage 3: Results Analysis

```python
def analyze_specification_curve(results_df):
    """Analyze specification curve results."""

    # Filter successful estimations
    valid_results = results_df[results_df['converged'] == True].copy()

    # Calculate robustness metrics
    robustness_metrics = {}

    for variable in ['exchange', 'quality', 'power']:
        coef_col = f'{variable}_coef'
        pval_col = f'{variable}_pvalue'

        if coef_col in valid_results.columns:
            # Significance rate
            sig_rate = (valid_results[pval_col] < 0.05).mean()

            # Coefficient distribution
            coef_dist = valid_results[coef_col].describe()

            # Sign consistency
            positive_rate = (valid_results[coef_col] > 0).mean()

            robustness_metrics[variable] = {
                'significance_rate': sig_rate,
                'coefficient_distribution': coef_dist.to_dict(),
                'positive_coefficient_rate': positive_rate,
                'median_coefficient': valid_results[coef_col].median(),
                'coefficient_range': (valid_results[coef_col].min(), valid_results[coef_col].max())
            }

    return robustness_metrics

robustness = analyze_specification_curve(spec_curve_results)
```

## Visualization Framework

### Primary Visualization: Specification Curve Plot

```python
import matplotlib.pyplot as plt
import seaborn as sns

def create_specification_curve_plot(results_df, variable='exchange'):
    """Create specification curve plot for given variable."""

    coef_col = f'{variable}_coef'
    pval_col = f'{variable}_pvalue'

    # Sort by coefficient value
    results_sorted = results_df.sort_values(coef_col)
    results_sorted['spec_rank'] = range(len(results_sorted))

    # Create figure with subplots
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10),
                                   gridspec_kw={'height_ratios': [2, 1]})

    # Top panel: Coefficient estimates
    colors = ['red' if p > 0.05 else 'blue' for p in results_sorted[pval_col]]
    ax1.scatter(results_sorted['spec_rank'], results_sorted[coef_col],
                c=colors, alpha=0.6, s=1)
    ax1.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax1.set_ylabel(f'{variable.title()} Coefficient')
    ax1.set_title(f'Specification Curve: {variable.title()} Effect')
    ax1.grid(True, alpha=0.3)

    # Add significance bands
    ax1.axhline(y=results_sorted[coef_col].quantile(0.025),
                color='gray', linestyle=':', alpha=0.5)
    ax1.axhline(y=results_sorted[coef_col].quantile(0.975),
                color='gray', linestyle=':', alpha=0.5)

    # Bottom panel: Specification choices
    spec_dims = ['sample', 'dependent_var', 'explanatory_vars',
                 'controls', 'fixed_effects', 'se_type']

    for i, dim in enumerate(spec_dims):
        unique_vals = results_sorted[dim].unique()
        for j, val in enumerate(unique_vals):
            mask = results_sorted[dim] == val
            ax2.scatter(results_sorted.loc[mask, 'spec_rank'],
                       [i + j*0.1] * mask.sum(),
                       alpha=0.3, s=0.5, label=val if i == 0 else "")

    ax2.set_xlabel('Specification Rank')
    ax2.set_ylabel('Specification Choices')
    ax2.set_yticks(range(len(spec_dims)))
    ax2.set_yticklabels(spec_dims)

    plt.tight_layout()
    return fig

# Generate plots for each variable
for var in ['exchange', 'quality', 'power']:
    fig = create_specification_curve_plot(spec_curve_results, var)
    fig.savefig(f'specification_curve_{var}.png', dpi=300, bbox_inches='tight')
    plt.close()
```

### Robustness Dashboard

```python
def create_robustness_dashboard(robustness_metrics):
    """Create comprehensive robustness dashboard."""

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    variables = list(robustness_metrics.keys())

    for i, var in enumerate(variables):
        if i < 3:
            ax = axes[0, i]
        else:
            ax = axes[1, i-3]

        metrics = robustness_metrics[var]

        # Significance rate bar
        ax.bar(['Significance Rate'], [metrics['significance_rate']],
               color='green' if metrics['significance_rate'] > 0.5 else 'red', alpha=0.7)
        ax.set_ylim(0, 1)
        ax.set_title(f'{var.title()} Robustness')
        ax.set_ylabel('Rate')

        # Add text annotations
        ax.text(0, metrics['significance_rate'] + 0.05,
                f"{metrics['significance_rate']:.2%}",
                ha='center', va='bottom', fontweight='bold')

    # Summary statistics table
    if len(variables) < 6:
        ax = axes[1, len(variables)]
        ax.axis('off')

        summary_data = []
        for var in variables:
            metrics = robustness_metrics[var]
            summary_data.append([
                var.title(),
                f"{metrics['significance_rate']:.1%}",
                f"{metrics['median_coefficient']:.3f}",
                f"{metrics['positive_coefficient_rate']:.1%}"
            ])

        table = ax.table(cellText=summary_data,
                        colLabels=['Variable', 'Sig Rate', 'Median Coef', 'Positive Rate'],
                        cellLoc='center',
                        loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1, 2)

    plt.suptitle('Specification Robustness Dashboard', fontsize=16, fontweight='bold')
    plt.tight_layout()
    return fig
```

## Interpretation Framework

### Robustness Criteria

1. **Strong Robustness**: Significant in >75% of specifications
2. **Moderate Robustness**: Significant in 50-75% of specifications
3. **Weak Robustness**: Significant in 25-50% of specifications
4. **No Robustness**: Significant in <25% of specifications

### Effect Size Consistency

1. **Consistent Effect**: 95% of coefficients have same sign
2. **Mostly Consistent**: 80-95% same sign
3. **Mixed Effects**: 60-80% same sign
4. **Inconsistent**: <60% same sign

### Sensitivity Analysis

```python
def sensitivity_analysis(results_df):
    """Analyze which specification choices drive sensitivity."""

    sensitivity_report = {}

    for variable in ['exchange', 'quality', 'power']:
        coef_col = f'{variable}_coef'
        pval_col = f'{variable}_pvalue'

        # Group by specification dimensions
        spec_dims = ['sample', 'dependent_var', 'explanatory_vars',
                     'controls', 'fixed_effects', 'se_type']

        dimension_effects = {}

        for dim in spec_dims:
            grouped = results_df.groupby(dim)[pval_col].agg(['mean', 'std']).reset_index()
            grouped['sig_rate'] = 1 - grouped['mean']  # Convert p-value to significance rate

            # Calculate variance across groups
            variance = grouped['sig_rate'].var()

            dimension_effects[dim] = {
                'variance_in_significance': variance,
                'group_effects': grouped.to_dict('records'),
                'most_sensitive': variance > 0.1  # Threshold for high sensitivity
            }

        sensitivity_report[variable] = dimension_effects

    return sensitivity_report

sensitivity = sensitivity_analysis(spec_curve_results)
```

## Implementation Protocol

### Phase 1: Hypothesis-Specific Analysis (Days 1-3)

- Run baseline specifications for each hypothesis
- Identify which hypotheses show promise
- Eliminate hypotheses with p > 0.20 in all baseline specs

### Phase 2: Focused Robustness Testing (Days 4-6)

- Run full specification curve for promising hypotheses only
- Test sensitivity to key modeling choices
- Document specification-dependent findings

### Phase 3: Horse Race Competition (Days 7-8)

- Combine significant hypotheses in joint models
- Test for multicollinearity and redundancy
- Identify dominant explanations

### Phase 4: Final Validation (Days 9-10)

- Out-of-sample testing for winning models
- Bootstrap confidence intervals
- Prepare publication-quality output

## Interpretation Guidelines

### Hypothesis Evaluation Criteria

```python
def evaluate_hypothesis(spec_results: pd.DataFrame) -> Dict:
    """Evaluate robustness of hypothesis across specifications."""

    # Filter to converged models only
    valid_specs = spec_results[spec_results['converged']]

    if len(valid_specs) < 10:
        return {'status': 'insufficient_data'}

    # Calculate key metrics
    metrics = {
        'n_specs': len(valid_specs),
        'sig_rate': (valid_specs['p_value'] < 0.05).mean(),
        'median_coef': valid_specs['coefficient'].median(),
        'coef_iqr': valid_specs['coefficient'].quantile(0.75) -
                    valid_specs['coefficient'].quantile(0.25),
        'sign_consistency': (valid_specs['coefficient'] > 0).mean(),
        'zone_elimination_rate': (~valid_specs['zone_effect_remains']).mean()
    }

    # Classification
    if metrics['sig_rate'] > 0.75 and metrics['sign_consistency'] > 0.95:
        metrics['robustness'] = 'strong'
    elif metrics['sig_rate'] > 0.50 and metrics['sign_consistency'] > 0.80:
        metrics['robustness'] = 'moderate'
    elif metrics['sig_rate'] > 0.25:
        metrics['robustness'] = 'weak'
    else:
        metrics['robustness'] = 'not_supported'

    return metrics
```

## Quality Controls

### Computational Validation

1. **Convergence Monitoring**: Track estimation convergence rates
2. **Numerical Stability**: Check for extreme coefficient values
3. **Sample Size Requirements**: Ensure adequate observations per specification
4. **Multicollinearity Detection**: Monitor condition numbers

### Results Validation

1. **Benchmark Comparisons**: Compare to known results from literature
2. **Cross-Validation**: Test on holdout samples
3. **Bootstrap Confidence**: Add bootstrap uncertainty quantification
4. **External Replication**: Test on different datasets

## Expected Outcomes

### Likely Scenarios

#### Scenario 1: Clear Winner

- One hypothesis dominates across specifications
- Exchange rate effects disappear when controlling for winner
- Policy implications are straightforward

#### Scenario 2: Multiple Mechanisms

- 2-3 hypotheses show consistent significance
- Each explains different aspect of price variation
- Policy requires multi-faceted approach

#### Scenario 3: Context Dependence

- Different hypotheses win in different time periods/regions
- Suggests structural breaks or spatial heterogeneity
- Policy must be context-specific

#### Scenario 4: Exchange Rate Dominance

- Exchange rate mechanism remains significant
- Other factors provide marginal improvements
- Confirms importance of currency analysis

### Decision Framework

```
If sig_rate > 0.75 and zone_elimination > 0.50:
    → Strong alternative explanation
    → Revise understanding of price formation
    → Design targeted interventions

Elif sig_rate > 0.50 and zone_elimination > 0.25:
    → Complementary mechanism
    → Exchange rates matter but not exclusively
    → Combined policy approach needed

Else:
    → Weak/no support for alternative
    → Original framework stands
    → Focus on other hypotheses
```

## Conclusion

This specification curve analysis provides the most rigorous possible test of competing hypotheses about Yemen's price patterns. By systematically varying all modeling choices, we ensure that our conclusions are robust to researcher discretion and provide reliable foundations for both academic understanding and policy action.

The framework's strength lies in its comprehensiveness - no reasonable specification choice is excluded, preventing cherry-picking and ensuring transparent, credible results that can withstand peer review and policy scrutiny.
