# Horse Race Testing Framework for Competing Hypotheses

## Overview

This framework implements systematic horse race testing to identify which alternative explanations best account for price differentials in Yemen's fragmented markets. The methodology ensures fair competition between hypotheses without privileging any single explanation.

## Methodological Foundation

### Core Principle

**No Hypothesis Favoritism**: Each competing explanation receives equal methodological treatment with identical:

- Sample selection criteria
- Control variable specifications
- Standard error calculations
- Diagnostic testing requirements
- Robustness validation protocols

### Testing Hierarchy

1. **Individual Significance**: Test each hypothesis in isolation
2. **Pairwise Competition**: Compare hypotheses two at a time
3. **Full Model Competition**: Include all significant hypotheses simultaneously
4. **Nested Model Testing**: Formal statistical comparison of explanatory power
5. **Out-of-Sample Validation**: Test winning hypotheses on holdout data

## Horse Race Specification Framework

### Base Model Template

```
P_it = α + β₁ExchangeRate_it + β₂Alternative_it + γ'X_it + δ_i + λ_t + ε_it

Where:
- P_it = log price of commodity in market i at time t
- ExchangeRate_it = relevant exchange rate measure
- Alternative_it = specific alternative explanation variable
- X_it = standard control variables (conflict, distance, seasonal)
- δ_i = market fixed effects
- λ_t = time fixed effects
- ε_it = error term
```

### Competition Metrics

1. **Coefficient Significance**: t-statistics and p-values for each hypothesis
2. **R-squared Comparison**: Explanatory power contribution
3. **Information Criteria**: AIC, BIC for model selection
4. **F-tests**: Nested model comparisons
5. **Prediction Accuracy**: Out-of-sample forecasting performance

## Individual Hypothesis Testing

### H1: Exchange Rate Mechanism (Baseline)

```
P_it = α + β₁ExchangeRate_it + γ'X_it + δ_i + λ_t + ε_it

Test: H₀: β₁ = 0 vs H₁: β₁ ≠ 0
Expected: β₁ < 0 (higher exchange rate → lower USD prices)
```

### H2: Quality Differences

```
P_it = α + β₁ExchangeRate_it + β₂QualityIndex_it + γ'X_it + δ_i + λ_t + ε_it

Test: H₀: β₂ = 0 vs H₁: β₂ ≠ 0
Expected: β₂ > 0 (higher quality → higher prices)
Differentiation: β₁ coefficient should reduce when β₂ included
```

### H3: Market Power

```
P_it = α + β₁ExchangeRate_it + β₂HHI_it + β₃CheckpointDensity_it + γ'X_it + δ_i + λ_t + ε_it

Test: H₀: β₂ = β₃ = 0 vs H₁: β₂ > 0, β₃ > 0
Expected: β₂, β₃ > 0 (concentration, barriers → higher prices)
```

### H4: Measurement Error

```
P_it = α + β₁ExchangeRate_it + β₂EnumeratorFE_it + β₃SamplingBias_it + γ'X_it + δ_i + λ_t + ε_it

Test: H₀: β₂ = β₃ = 0 vs H₁: β₂ ≠ 0, β₃ ≠ 0
Expected: β₁ unstable across specifications when measurement corrected
```

### H5: Risk Premium

```
P_it = α + β₁ExchangeRate_it + β₂RiskPremium_it + β₃Volatility_it + γ'X_it + δ_i + λ_t + ε_it

Test: H₀: β₂ = β₃ = 0 vs H₁: β₂ > 0, β₃ > 0
Expected: β₂, β₃ > 0 (risk → higher prices)
```

### H6: Transaction Costs

```
P_it = α + β₁ExchangeRate_it + β₂TransactionCosts_it + β₃Infrastructure_it + γ'X_it + δ_i + λ_t + ε_it

Test: H₀: β₂ = β₃ = 0 vs H₁: β₂ > 0, β₃ < 0
Expected: β₂ > 0, β₃ < 0 (costs → higher prices, infrastructure → lower prices)
```

### H7: Network Effects

```
P_it = α + β₁ExchangeRate_it + β₂NetworkCentrality_it + β₃InformationSpeed_it + γ'X_it + δ_i + λ_t + ε_it

Test: H₀: β₂ = β₃ = 0 vs H₁: β₂ < 0, β₃ < 0
Expected: β₂, β₃ < 0 (better networks → lower prices)
```

## Pairwise Competition Framework

### Round 1: Exchange Rate vs. Each Alternative

For each alternative hypothesis j:

```
P_it = α + β₁ExchangeRate_it + β₂Alternative_j_it + γ'X_it + δ_i + λ_t + ε_it

Evaluation Criteria:
1. Statistical Significance: Compare t-statistics for β₁ vs β₂
2. Economic Significance: Compare coefficient magnitudes
3. R-squared Contribution: Marginal explanatory power
4. Residual Analysis: Which leaves smaller unexplained variation
```

### Round 2: Top Alternatives vs. Each Other

Test best performing non-exchange rate hypotheses against each other:

```
P_it = α + β₁Alternative_j_it + β₂Alternative_k_it + γ'X_it + δ_i + λ_t + ε_it

Winner Criteria:
- Higher individual t-statistic
- Larger R-squared contribution
- Better out-of-sample prediction
- Stronger theoretical foundation
```

## Full Model Competition

### All Hypotheses Model

```
P_it = α + β₁ExchangeRate_it + β₂QualityIndex_it + β₃HHI_it +
       β₄CheckpointDensity_it + β₅EnumeratorFE_it + β₆SamplingBias_it +
       β₇RiskPremium_it + β₈Volatility_it + β₉TransactionCosts_it +
       β₁₀Infrastructure_it + β₁₁NetworkCentrality_it + β₁₂InformationSpeed_it +
       γ'X_it + δ_i + λ_t + ε_it
```

### Model Selection Criteria

1. **Statistical Significance**: Only retain significant coefficients
2. **Multicollinearity**: VIF < 5 for included variables
3. **Information Criteria**: Select model minimizing AIC/BIC
4. **Cross-Validation**: Best out-of-sample performance
5. **Economic Interpretability**: Coefficients have sensible magnitudes

## Nested Model Testing Protocol

### Likelihood Ratio Tests

For each alternative hypothesis j:

```
Full Model: P_it = α + β₁ExchangeRate_it + β₂Alternative_j_it + γ'X_it + δ_i + λ_t + ε_it
Restricted: P_it = α + β₁ExchangeRate_it + γ'X_it + δ_i + λ_t + ε_it

LR = 2(L_full - L_restricted) ~ χ²(1)
Reject H₀: β₂ = 0 if LR > χ²₀.₀₅(1) = 3.84
```

### Wald Tests for Multiple Restrictions

Test joint significance of alternative explanation components:

```
H₀: β₂ = β₃ = ... = βₖ = 0 (all alternative variables jointly zero)
H₁: At least one βⱼ ≠ 0

Wald = (Rβ̂)'[R(X'X)⁻¹R']⁻¹(Rβ̂) ~ χ²(q)
where R imposes restrictions and q = number of restrictions
```

### Vuong Test for Non-Nested Models

Compare models with different theoretical foundations:

```
Vuong Statistic = n⁻¹/²∑ᵢ[log(L₁ᵢ/L₂ᵢ)]/σ̂ ~ N(0,1)

Where:
- L₁ᵢ, L₂ᵢ = likelihood contributions for models 1 and 2
- σ̂ = standard error of log-likelihood differences
```

## Robustness Testing Framework

### Sample Robustness

1. **Time Period Splits**: Test stability across different conflict phases
2. **Geographic Subsamples**: Exclude border regions, specific governorates
3. **Commodity Restrictions**: Test on subsets (food only, fuel only, etc.)
4. **Market Type**: Urban vs. rural market subsamples

### Specification Robustness

1. **Fixed Effects**: Market, time, commodity, zone variations
2. **Standard Errors**: Robust, clustered by market/time/commodity
3. **Functional Form**: Log-linear, linear, Box-Cox transformations
4. **Outlier Treatment**: Winsorization, trimming, robust regression

### Instrument Validity (for Endogenous Variables)

1. **Weak Instruments**: F-statistic > 10 for first stage
2. **Overidentification**: Hansen J-test for instrument validity
3. **Exclusion Restriction**: Economic justification for instruments

## Out-of-Sample Validation

### Cross-Validation Protocol

1. **Time Series Split**: Train on 2019-2021, test on 2022-2024
2. **Geographic Split**: Train on 15 governorates, test on remaining 7
3. **Random Split**: 70% training, 30% testing with 100 replications

### Prediction Accuracy Metrics

```
1. Root Mean Squared Error: RMSE = √[n⁻¹∑(Pᵢ - P̂ᵢ)²]
2. Mean Absolute Error: MAE = n⁻¹∑|Pᵢ - P̂ᵢ|
3. Mean Absolute Percentage Error: MAPE = n⁻¹∑|Pᵢ - P̂ᵢ|/|Pᵢ|
4. Directional Accuracy: Fraction of correct price movement predictions
```

## Winner Declaration Criteria

### Primary Criteria (Must Meet All)

1. **Statistical Significance**: p < 0.05 in full model
2. **Economic Significance**: Coefficient magnitude economically meaningful
3. **Robustness**: Significant across ≥75% of specification variations
4. **Predictive Power**: Best out-of-sample performance

### Tiebreaker Criteria (If Multiple Winners)

1. **Theoretical Foundation**: Stronger causal mechanism
2. **Data Quality**: Less reliance on imputed/estimated variables
3. **Policy Relevance**: More actionable for humanitarian programming
4. **External Validity**: Replicable in other conflict settings

## Implementation Schedule

### Phase 1: Individual Testing (Weeks 1-2)

- Estimate each hypothesis model independently
- Calculate significance tests and effect sizes
- Document coefficients and standard errors

### Phase 2: Pairwise Competition (Weeks 3-4)

- Run all pairwise comparisons
- Calculate nested model test statistics
- Identify top 3 competing hypotheses

### Phase 3: Full Model (Week 5)

- Estimate complete model with all hypotheses
- Apply model selection criteria
- Conduct multicollinearity diagnostics

### Phase 4: Robustness (Weeks 6-7)

- Run specification curve analysis
- Test sample robustness
- Validate instruments if needed

### Phase 5: Out-of-Sample (Week 8)

- Implement cross-validation
- Calculate prediction metrics
- Declare winners

## Documentation Requirements

### For Each Hypothesis Test

1. **Estimation Results**: Coefficients, standard errors, R-squared
2. **Diagnostic Tests**: Residual analysis, specification tests
3. **Economic Interpretation**: Magnitude and policy implications
4. **Limitations**: Data quality concerns, identification issues

### Final Report Components

1. **Executive Summary**: Winner and runner-up hypotheses
2. **Methodology**: Complete testing protocol documentation
3. **Results Table**: Comprehensive comparison across all criteria
4. **Policy Implications**: Humanitarian programming recommendations
5. **Future Research**: Data needs and methodology improvements

## Quality Assurance

### Replication Requirements

- Code availability for all estimation procedures
- Data documentation for all variables
- Step-by-step result reproduction guide

### Peer Review Standards

- External economist review of methodology
- Statistical consultant validation of procedures
- World Bank quality standards compliance

This horse race framework ensures rigorous, unbiased competition between alternative explanations, leading to evidence-based conclusions about Yemen's market dysfunction drivers.

## Critical Additions for Complete Framework

### Hypothesis Numbering Standardization

For consistency across all project documents:
- **Baseline**: Exchange Rate Mechanism (not an alternative, but the comparison baseline)
- **ALT1-ALT11**: Alternative explanations to be tested against the baseline

This ensures clear distinction between the baseline hypothesis and alternatives.

### Data Feasibility Classifications

**Immediately Testable (with current WFP/ACLED/HDX data)**:
- ALT1: Quality Differences (urban/rural, import/local proxies)
- ALT2: Market Power (market counts, checkpoint data)
- ALT3: Supply Disruption (infrastructure damage events)
- ALT4: Government Policy (policy announcements, fuel subsidies)
- ALT5: Demand Destruction (IOM displacement data)
- ALT6: Transportation Costs (fuel prices from WFP)
- ALT7: Credit/Financing (banking infrastructure data)

**Requires Additional Data Collection**:
- ALT8: Transaction Costs (needs comprehensive cost surveys)
- ALT9: Risk Premium (needs conflict prediction models)
- ALT10: Network Effects (needs trader relationship mapping)
- ALT11: Measurement Error (needs enumerator metadata)

### Policy Decision Framework

Based on test results, specific interventions are triggered:

**If Single Dominant Alternative (>50% effect, eliminates exchange rate)**:
→ Major revision of humanitarian programming approach
→ Redesign aid distribution around dominant mechanism
→ Monitor new mechanism as primary indicator

**If Multiple Significant Alternatives (2-3 with p<0.05)**:
→ Weighted intervention strategy based on effect sizes
→ Address each mechanism proportionally
→ Monitor interaction effects between mechanisms

**If Exchange Rate Remains Significant**:
→ Currency conversion remains essential
→ Layer additional interventions for alternative mechanisms
→ Continue monitoring exchange rate divergence

**If No Clear Winner (all effects weak)**:
→ Maintain current approach with improvements
→ Invest in better data collection
→ Consider structural breaks or omitted variables
