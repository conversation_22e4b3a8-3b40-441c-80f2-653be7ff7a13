# Spatial Econometric Framework for Market Integration Analysis

**Target Audience**: Econometricians, Spatial Analysts  
**Module**: `yemen_market.models.spatial`  
**Priority**: CRITICAL - Missing fundamental component

## Overview

Market integration analysis requires proper spatial econometric methods to account for geographic relationships between markets. This framework addresses the critical gap in spatial modeling currently missing from the methodology.

## Spatial Weight Matrix Construction

### 1. Geographic Distance Weights

```python
import numpy as np
import pandas as pd
from sklearn.metrics.pairwise import haversine_distances

def create_distance_weights(market_coords: pd.DataFrame, 
                          cutoff_km: float = 500,
                          decay_parameter: float = 2) -> np.ndarray:
    """
    Create spatial weight matrix based on geographic distance.
    
    Parameters:
    - market_coords: DataFrame with 'latitude', 'longitude', 'market_id'
    - cutoff_km: Maximum distance for non-zero weights
    - decay_parameter: Distance decay parameter (higher = faster decay)
    
    Returns:
    - W: Spatial weight matrix (n_markets × n_markets)
    """
    # Convert to radians for haversine
    coords_rad = np.radians(market_coords[['latitude', 'longitude']])
    
    # Calculate distance matrix in kilometers
    distances_km = haversine_distances(coords_rad) * 6371
    
    # Create weights with inverse distance decay
    W = np.zeros_like(distances_km)
    
    # Apply cutoff and distance decay
    mask = (distances_km > 0) & (distances_km <= cutoff_km)
    W[mask] = 1 / (distances_km[mask] ** decay_parameter)
    
    # Row-standardize weights
    row_sums = W.sum(axis=1)
    row_sums[row_sums == 0] = 1  # Avoid division by zero
    W = W / row_sums[:, np.newaxis]
    
    return W
```

### 2. Economic Distance Weights

```python
def create_economic_weights(market_data: pd.DataFrame,
                          distance_matrix: np.ndarray,
                          transport_cost_data: pd.DataFrame = None) -> np.ndarray:
    """
    Create weights based on economic connectivity.
    
    Incorporates:
    - Transport infrastructure quality
    - Trade flow volumes (if available)
    - Administrative boundaries
    - Currency zone alignment
    """
    n_markets = len(market_data)
    W_economic = np.zeros((n_markets, n_markets))
    
    for i in range(n_markets):
        for j in range(n_markets):
            if i != j:
                # Base geographic component
                geo_weight = 1 / distance_matrix[i, j] if distance_matrix[i, j] > 0 else 0
                
                # Currency zone penalty
                same_currency = (market_data.iloc[i]['currency_zone'] == 
                               market_data.iloc[j]['currency_zone'])
                currency_factor = 1.0 if same_currency else 0.3
                
                # Administrative boundary factor
                same_governorate = (market_data.iloc[i]['governorate'] == 
                                  market_data.iloc[j]['governorate'])
                admin_factor = 1.2 if same_governorate else 1.0
                
                W_economic[i, j] = geo_weight * currency_factor * admin_factor
    
    # Row-standardize
    row_sums = W_economic.sum(axis=1)
    row_sums[row_sums == 0] = 1
    W_economic = W_economic / row_sums[:, np.newaxis]
    
    return W_economic
```

## Spatial Autoregressive Models

### 1. Spatial Lag Model (SAR)

$$P_{i,j,t} = \rho \sum_{k} W_{ik} P_{k,j,t} + \beta X_{i,j,t} + \alpha_i + \phi_j + \tau_t + \varepsilon_{i,j,t}$$

```python
from spreg import ML_Lag
import libpysal as lps

def estimate_spatial_lag_model(data: pd.DataFrame,
                              W: np.ndarray,
                              outcome_var: str = 'log_price',
                              exog_vars: list = None) -> ML_Lag:
    """
    Estimate spatial autoregressive (SAR) model.
    
    Tests for spatial dependence in outcome variable.
    """
    # Prepare data
    y = data[outcome_var].values
    X = data[exog_vars].values if exog_vars else np.ones((len(data), 1))
    
    # Convert weight matrix to libpysal format
    w = lps.weights.W(W)
    
    # Estimate spatial lag model
    model = ML_Lag(y, X, w, name_y=outcome_var, name_x=exog_vars)
    
    return model
```

### 2. Spatial Error Model (SEM)

$$P_{i,j,t} = \beta X_{i,j,t} + \alpha_i + \phi_j + \tau_t + u_{i,j,t}$$
$$u_{i,j,t} = \lambda \sum_{k} W_{ik} u_{k,j,t} + \varepsilon_{i,j,t}$$

```python
from spreg import ML_Error

def estimate_spatial_error_model(data: pd.DataFrame,
                               W: np.ndarray,
                               outcome_var: str = 'log_price',
                               exog_vars: list = None) -> ML_Error:
    """
    Estimate spatial error model (SEM).
    
    Tests for spatial correlation in residuals.
    """
    y = data[outcome_var].values
    X = data[exog_vars].values if exog_vars else np.ones((len(data), 1))
    
    w = lps.weights.W(W)
    
    model = ML_Error(y, X, w, name_y=outcome_var, name_x=exog_vars)
    
    return model
```

## Spatial Panel Models

### 1. Fixed Effects Spatial Panel

```python
def estimate_spatial_panel_model(panel_data: pd.DataFrame,
                                W: np.ndarray,
                                outcome_var: str = 'log_price',
                                treatment_var: str = 'conflict_intensity',
                                control_vars: list = None,
                                spatial_lag: bool = True,
                                time_effects: bool = True,
                                market_effects: bool = True):
    """
    Estimate spatial panel model with fixed effects.
    
    Handles three-dimensional panel: market × commodity × time
    """
    # Reshape data for spatial panel estimation
    # This is a simplified framework - full implementation requires
    # specialized spatial panel packages or custom maximum likelihood
    
    models = {}
    
    # Estimate for each commodity separately (Tier 2 approach)
    for commodity in panel_data['commodity'].unique():
        commodity_data = panel_data[panel_data['commodity'] == commodity]
        
        # Time-varying spatial weights (if markets enter/exit)
        active_markets = commodity_data['market_id'].unique()
        market_indices = [i for i, mid in enumerate(all_markets) if mid in active_markets]
        W_commodity = W[np.ix_(market_indices, market_indices)]
        
        if spatial_lag:
            model = estimate_spatial_lag_model(
                commodity_data, W_commodity, outcome_var, 
                [treatment_var] + (control_vars or [])
            )
        else:
            model = estimate_spatial_error_model(
                commodity_data, W_commodity, outcome_var,
                [treatment_var] + (control_vars or [])
            )
        
        models[commodity] = model
    
    return models
```

## Spatial Diagnostic Tests

### 1. Moran's I Test for Spatial Correlation

```python
from spreg import moran

def test_spatial_correlation(residuals: np.ndarray, 
                           W: np.ndarray) -> dict:
    """
    Test for spatial correlation in model residuals.
    """
    w = lps.weights.W(W)
    mi = moran.Moran(residuals, w)
    
    return {
        'morans_i': mi.I,
        'p_value': mi.p_norm,
        'z_score': mi.z_norm,
        'interpretation': 'Spatial correlation detected' if mi.p_norm < 0.05 else 'No spatial correlation'
    }
```

### 2. Lagrange Multiplier Tests

```python
from spreg import lm

def spatial_specification_tests(y: np.ndarray, 
                              X: np.ndarray, 
                              W: np.ndarray) -> dict:
    """
    Test whether to use spatial lag vs spatial error model.
    """
    w = lps.weights.W(W)
    
    # Estimate OLS model first
    ols_model = lm.OLS(y, X)
    
    # LM tests for spatial dependence
    lm_lag = lm.LMlag(ols_model, w)
    lm_error = lm.LMerror(ols_model, w)
    lm_sarma = lm.LMsarma(ols_model, w)
    
    return {
        'lm_lag_statistic': lm_lag.lm,
        'lm_lag_p_value': lm_lag.p,
        'lm_error_statistic': lm_error.lm,
        'lm_error_p_value': lm_error.p,
        'lm_sarma_statistic': lm_sarma.lm,
        'lm_sarma_p_value': lm_sarma.p,
        'recommended_model': get_model_recommendation(lm_lag.p, lm_error.p)
    }

def get_model_recommendation(p_lag: float, p_error: float) -> str:
    """
    Recommend spatial model based on LM test results.
    """
    if p_lag < 0.05 and p_error >= 0.05:
        return "Spatial Lag Model (SAR)"
    elif p_error < 0.05 and p_lag >= 0.05:
        return "Spatial Error Model (SEM)"
    elif p_lag < 0.05 and p_error < 0.05:
        return "General Spatial Model (SARAR) - test robust LM statistics"
    else:
        return "OLS sufficient - no spatial dependence"
```

## Integration with Three-Tier Framework

### Tier 1: Pooled Spatial Panel
- Use economic distance weights combining geographic and currency factors
- Test for spatial lag vs spatial error specifications
- Account for cross-market spillovers in conflict effects

### Tier 2: Commodity-Specific Spatial Models
- Estimate separate spatial models for each commodity
- Allow for commodity-specific spatial decay parameters
- Test for differential spatial integration by commodity type

### Tier 3: Spatial Validation and Robustness
- Cross-validation using spatial leave-one-out procedures
- Sensitivity analysis for different weight matrix specifications
- Spatial regime models for currency zones

## Implementation Priority

### Phase 1 (Critical): Basic Spatial Framework
1. Implement distance-based weight matrix construction
2. Add spatial diagnostic tests to existing models
3. Test for spatial correlation in current model residuals

### Phase 2 (High): Spatial Panel Models
1. Implement spatial lag and error models
2. Integrate with three-tier framework
3. Add currency zone spatial weights

### Phase 3 (Medium): Advanced Spatial Methods
1. Spatial regime models for currency zones
2. Dynamic spatial panel models
3. Spatial network analysis for trader relationships

## Data Requirements

### Immediate (Available):
- Market geographic coordinates (latitude, longitude)
- Administrative boundaries (governorate assignments)
- Currency zone classifications

### Enhanced (Obtainable):
- Road network distance matrices
- Transport cost data between market pairs
- Trader network relationships

### Advanced (Research Extension):
- High-frequency trade flow data
- Infrastructure quality indices
- Real-time transport cost monitoring

This spatial framework addresses the critical gap in the current methodology and provides the foundation for proper market integration analysis.