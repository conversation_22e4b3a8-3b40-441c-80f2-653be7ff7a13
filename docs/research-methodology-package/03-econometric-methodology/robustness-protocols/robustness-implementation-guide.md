# Practical Implementation Guide for Robustness Testing

## Quick Start Template

### Step 1: Basic Setup
```python
from src.core.models.robustness import ComprehensiveRobustnessFramework
from src.core.models.robustness.yemen_specific_robustness import YemenSpecificRobustness

# Initialize framework
robustness = ComprehensiveRobustnessFramework(
    project_name="Yemen Market Integration - H1",
    output_dir="results/robustness/h1_exchange_rate"
)

# For Yemen-specific tests
yemen_robustness = YemenSpecificRobustness()
```

### Step 2: Define Your Model
```python
def my_hypothesis_model(data):
    """Your main regression model."""
    # Example: Exchange rate pass-through regression
    import statsmodels.api as sm
    
    # Prepare variables
    y = data['price_usd']
    X = data[['exchange_rate', 'conflict_intensity', 'market_fe']]
    X = sm.add_constant(X)
    
    # Run regression
    model = sm.OLS(y, X).fit(cov_type='cluster', cov_kwds={'groups': data['market_id']})
    
    # Return standardized format
    return {
        'coefficient': model.params['exchange_rate'],
        'se': model.bse['exchange_rate'],
        'p_value': model.pvalues['exchange_rate'],
        'n_obs': len(data),
        'r_squared': model.rsquared
    }
```

### Step 3: Run Comprehensive Tests
```python
# Load your data
data = pd.read_csv('path/to/panel_data.csv')

# Run all robustness tests
results = robustness.run_comprehensive_test(
    data=data,
    main_model=my_hypothesis_model,
    hypothesis_name="H1_Exchange_Rate_Mechanism",
    cluster_var='market_id',
    treatment_var='exchange_rate',
    outcome_var='price_usd'
)

# Check overall robustness
print(f"Robustness Score: {results.robustness_score:.1%}")
print(f"Assessment: {results.overall_assessment}")
```

## Common Use Cases

### 1. Testing H1 (Exchange Rate Mechanism)
```python
# Currency zone robustness is critical for H1
baseline_zones = load_currency_zones()  # Your zone definitions

zone_robustness = yemen_robustness.test_currency_zone_robustness(
    data=data,
    model=my_hypothesis_model,
    baseline_zones=baseline_zones
)

if zone_robustness.effect_stability < 0.7:
    print("WARNING: Results sensitive to currency zone definitions!")
    print(f"Critical markets to verify: {zone_robustness.critical_markets}")
```

### 2. Testing H2 (Aid Distribution Effects)
```python
# Need to handle aid endogeneity
def aid_model_with_iv(data):
    # Use lagged conflict as instrument for aid
    from statsmodels.sandbox.regression.gmm import IV2SLS
    
    # First stage: Aid ~ lagged_conflict + controls
    # Second stage: Prices ~ predicted_aid + controls
    
    model = IV2SLS(
        data['price'],
        data[['aid_presence', 'controls']],
        data[['lagged_conflict', 'controls']]
    ).fit()
    
    return standard_format(model)

# Add placebo specifications for aid
placebo_specs = [
    {
        'name': 'Future aid placebo',
        'type': 'fake_treatment_time',
        'fake_date': '2025-01-01'  # Future aid shouldn't affect current prices
    },
    {
        'name': 'Non-food aid placebo',
        'type': 'unaffected_outcome',
        'placebo_outcome': 'non_food_prices'  # Shouldn't affect non-food items
    }
]

results = robustness.run_comprehensive_test(
    data=data,
    main_model=aid_model_with_iv,
    hypothesis_name="H2_Aid_Distribution",
    placebo_specs=placebo_specs
)
```

### 3. Testing with Missing Data (38% in Yemen)
```python
# Special handling for Yemen's missing data patterns
missing_results = yemen_robustness.test_missing_data_robustness(
    data=observed_data,
    model=my_hypothesis_model,
    original_missing=full_data_with_na
)

# Interpret bounds
if missing_results['missing_bounds']['lower'] > 0:
    print("Effect is positive even under worst-case missing data assumptions")
```

## Interpreting Robustness Results

### Overall Robustness Score

| Score Range | Interpretation | Policy Recommendation |
|------------|----------------|----------------------|
| 80-100% | Highly Robust | Results suitable for policy decisions |
| 60-79% | Moderately Robust | Results informative with stated caveats |
| 40-59% | Mixed Robustness | Requires careful interpretation |
| 0-39% | Fragile | Not suitable for policy without further investigation |

### Specification Curve Results

**Good Pattern:**
- Most specifications show same sign
- Median effect close to main estimate
- >75% specifications statistically significant

**Warning Signs:**
- Effect changes sign in >10% of specifications
- Wide range between min and max effects
- Significance depends heavily on one choice

### Bootstrap Confidence Intervals

**Interpretation:**
- If bootstrap CI much wider than analytical CI → standard errors underestimated
- If bootstrap fails to converge → model may be misspecified
- Wild bootstrap essential when clusters < 30 (many Yemen governorates)

### Yemen-Specific Interpretations

**Currency Zone Robustness:**
- Stability > 0.8: Zone definitions are not driving results
- Stability 0.6-0.8: Some sensitivity, report bounds
- Stability < 0.6: Results may be artifact of zone definitions

**Conflict Endogeneity:**
- If IV and OLS similar → limited endogeneity concern
- If IV much larger → negative selection into conflict areas
- If event study shows pre-trends → identification concern

## Common Pitfalls and Solutions

### Pitfall 1: Computational Burden
**Problem:** Running 1000+ specifications takes hours

**Solution:**
```python
# Use caching for repeated calculations
robustness.enable_caching = True

# Run in parallel
results = robustness.run_comprehensive_test(
    data=data,
    main_model=cached_model,
    parallel=True,
    max_workers=8
)

# Or run incrementally
results = robustness.run_subset(
    test_types=['specification_curve', 'bootstrap'],
    skip_types=['sensitivity']  # Run later
)
```

### Pitfall 2: Interpreting Fragile Results
**Problem:** Main result is statistically significant but robustness score is low

**Solution:**
1. Identify which tests are failing:
```python
# Diagnostic breakdown
print(results.specification_curve['fragility_assessment'])
print(results.bootstrap_results['convergence_rate'])
print(results.sensitivity_results)
```

2. Report bounded effects:
```python
# Instead of point estimate, report range
lower_bound = results.specification_curve['percentile_5']
upper_bound = results.specification_curve['percentile_95']
print(f"Effect range: [{lower_bound:.3f}, {upper_bound:.3f}]")
```

### Pitfall 3: P-hacking Through Robustness
**Problem:** Running many robustness tests until finding favorable results

**Solution:**
```python
# Pre-register specifications
from src.core.models.pre_analysis import PreAnalysisPlan

plan = PreAnalysisPlan(hypothesis="H1")
plan.register_main_specification(my_hypothesis_model)
plan.register_robustness_tests(['currency_zones', 'bootstrap', 'outliers'])
plan.lock()  # Prevents changes after seeing data

# Run only pre-registered tests
results = robustness.run_preregistered_tests(plan)
```

## Frequently Asked Questions

### Q1: How many robustness tests are "enough"?
**A:** Quality over quantity. At minimum:
- Specification curve with key analytical choices
- Appropriate bootstrap for your data structure  
- One sensitivity test for main threat to validity
- Subsamples if heterogeneity is expected

### Q2: My results are fragile. Should I abandon the research?
**A:** No! Fragile results are still informative:
- Report the full specification curve
- Identify conditions under which effect holds
- Discuss what drives the fragility
- May lead to new insights about mechanisms

### Q3: How do I handle robustness for multiple hypotheses?
**A:** 
```python
# Run all with multiple testing correction
all_results = {}
for hypothesis in ['H1', 'H2', 'H3']:
    all_results[hypothesis] = robustness.run_comprehensive_test(...)
    
# Apply FDR correction across all tests
adjusted_results = robustness.apply_multiple_testing_correction(all_results)
```

### Q4: What if bootstrap won't converge?
**A:** This signals model problems:
1. Check for perfect collinearity
2. Ensure sufficient variation in treatment
3. Try wild bootstrap for few clusters
4. Consider simpler model specification

### Q5: How do I present robustness in a paper?
**A:** Follow this structure:
1. **Main text**: Report robustness score and key tests
2. **Robustness section**: Specification curve plot + summary table
3. **Appendix**: Full results, all specifications tested
4. **Online supplement**: Code and detailed output

## Example Results Section

> Our main specification shows that a 10% currency depreciation leads to a 7.5% increase in food prices (β = 0.75, SE = 0.12, p < 0.001). 
>
> We conduct comprehensive robustness tests following World Bank standards. Across 1,247 reasonable model specifications, the median effect is 0.73 with 89% showing statistical significance at the 5% level. The effect remains positive in 98% of specifications, ranging from 0.45 to 0.95.
>
> Cluster bootstrap confidence intervals [0.52, 0.98] confirm our analytical standard errors are appropriate. The effect is robust to currency zone definitions (stability score: 0.84), though markets within 25km of zone boundaries show attenuated effects.
>
> Sensitivity analysis reveals the results are robust to omitted variables that would need to explain >70% of residual variance to eliminate the effect (Oster δ = 2.3). 
>
> Overall robustness score: 82%. We conclude the exchange rate pass-through effect is highly robust to analytical choices.

## Next Steps

1. **Run the template** on your data
2. **Customize** for your specific hypothesis  
3. **Pre-register** your robustness plan
4. **Interpret** using the guidelines above
5. **Report** transparently and completely

Remember: Robustness testing is not about finding the "right" answer, but understanding how your conclusions depend on analytical choices.