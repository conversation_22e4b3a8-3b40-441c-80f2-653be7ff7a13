# Yemen Market Integration Pre-Analysis Plan

**Version:** 1.0
**Date:** January 6, 2025
**Status:** LOCKED - No modifications after data analysis begins
**Lock Commit Hash:** [TO BE COMPLETED]
**Lock MD5 Hash:** [TO BE COMPLETED]

---

## Executive Summary

This pre-analysis plan (PAP) establishes a comprehensive framework for testing hypotheses about currency fragmentation and market integration in Yemen's food markets. This plan locks in all analytical decisions before examining results to prevent p-hacking, data mining, and specification searching.

**Research Context:** The "Yemen Paradox" - observed price patterns that appear counterintuitive require systematic investigation.

**Theoretical Transparency:** While our theoretical framework suggests currency fragmentation may explain price patterns, we commit to neutral hypothesis testing with two-tailed tests and unbiased interpretation regardless of whether results confirm or contradict theoretical expectations.

## I. Primary vs Secondary Hypotheses

### Primary Hypotheses (Confirmatory - Max 3)

These hypotheses are central to our research question and will receive confirmatory statistical testing with strict multiple testing corrections:

### H1: Exchange Rate and Price Differentials

- **Statement:** No statistically significant price differentials exist between currency zones when prices are expressed in common currency (USD)
- **Significance:** Core test of currency fragmentation hypothesis
- **Two-tailed test:** No expected direction specified

### H3: Conflict Effects on Commodity Markets

- **Statement:** Conflict intensity affects commodity market prices
- **Significance:** Tests direct conflict impact on markets
- **Two-tailed test:** Direction not pre-specified

### H4: Cross-Border Arbitrage Mechanisms

- **Statement:** Price differentials between markets are systematically related to transport costs and exchange rate differentials
- **Significance:** Tests economic theory of market integration
- **Two tests:** (1) Any relationship (β₁ = β₂ = 0), (2) Theoretical prediction (β₁ = β₂ = 1)

### Secondary Hypotheses (Exploratory - All Others)

**H2:** Humanitarian aid distribution effects on local prices
**H5:** Long-run price convergence properties within currency zones
**H6:** Discrete price adjustments with territorial control changes
**H7:** Currency substitution patterns with exchange rate volatility
**H8:** Aid effectiveness by currency alignment
**H9:** Information spillovers across currency zones
**H10:** Exchange rate effects on market integration
**S1:** Spatial integration and currency boundaries
**N1:** Network effects on price transmission
**P1:** Political economy of currency zones

## II. Exact Model Specifications

### H1: Exchange Rate and Price Differentials

**Primary Specification (Confirmatory):**

```
Price_USD_it = α + β₁Zone_i + β₂X_it + ε_it

Where:
- Price_USD_it = WFP price converted to USD using appropriate exchange rate
- Zone_i = Currency zone indicator (Houthi vs Government)
- X_it = Control variables
- ε_it = Error term clustered at governorate level

Test: H₀: β₁ = 0 vs H₁: β₁ ≠ 0 (two-tailed)
Significance level: α = 0.0167 (Bonferroni corrected)
```

**Robustness Specification 1:**

```
log(Price_USD_it) = α + β₁Zone_Houthi_i + β₂X_it + γ_i + δ_ct + ε_it

Where δ_ct = Commodity × Month fixed effects
```

**Robustness Specification 2:**

```
Δlog(Price_USD_it) = α + β₁ΔZone_Houthi_i + β₂ΔX_it + γ_i + ε_it

First-difference specification for control change events
```

### H3: Conflict Effects on Commodity Markets

**Primary Specification:**

```
log(Price_it) = α + β₁Conflict_it + β₂X_it + γ_i + δ_t + ε_it

Where:
- Price_it = Commodity price (in relevant currency)
- Conflict_it = Conflict intensity measure (e.g., ACLED fatalities within 20km)
- X_it = Control variables
- γ_i = Market fixed effects
- δ_t = Time fixed effects

Test: H₀: β₁ = 0 vs H₁: β₁ ≠ 0 (two-tailed)
Significance level: α = 0.0167 (Bonferroni corrected)
```

### H4: Cross-Border Arbitrage Mechanisms

**Primary Specification:**

```
ΔP_ijt = α + β₁Transport_ij + β₂ExchangeDiff_ijt + ε_ijt

Where:
- ΔP_ijt = Price differential between markets i and j at time t
- Transport_ij = Transport costs between markets
- ExchangeDiff_ijt = Exchange rate differential between zones
- Sample restricted to tradeable goods only

Primary Test: H₀: β₁ = β₂ = 0 vs H₁: At least one ≠ 0 (test for any relationship)
Secondary Test: H₀: β₁ = β₂ = 1 vs H₁: At least one ≠ 1 (test theoretical prediction)
Significance level: α = 0.0167 (Bonferroni corrected)
```

## III. Multiple Testing Corrections

### For Primary Hypotheses

- **Method:** Bonferroni correction
- **Family-wise error rate:** α = 0.05
- **Number of tests:** 3 primary hypotheses
- **Individual test level:** α/3 = 0.0167
- **Critical values:** Applied uniformly to all primary tests

### For Secondary Hypotheses

- **Method:** Bonferroni correction
- **Individual test level:** α = 0.01/10 = 0.001
- **Rationale:** More conservative given exploratory nature
- **Number of tests:** 10 secondary hypotheses (H2, H5-H10, S1, N1, P1)

### Robustness Checks

- **No correction applied** - these support main results but are not independent tests
- **Reporting:** Will report both corrected and uncorrected p-values

## IV. Sample Definition and Restrictions

### Inclusion Criteria (LOCKED)

**Geographic Scope:**

- All markets in WFP price database for Yemen
- Minimum 24 months of observations per market
- At least 5 commodities tracked per market

**Temporal Scope:**

- January 2015 - December 2024 (10-year panel)
- Exclude first 3 months of data for each market (initialization effects)

**Commodity Scope:**

- Essential: Wheat flour, rice, vegetable oil, fuel (diesel/petrol)
- Non-essential: Meat, eggs, sugar, onions, tomatoes
- Minimum 60% data availability per commodity-market pair

**Exchange Rate Data:**

- CBY-Aden (government) rate for government-controlled areas
- CBY-Sana'a (Houthi) rate for Houthi-controlled areas
- Parallel market rates where official rates unavailable

### Exclusion Criteria (LOCKED)

**Market-Level Exclusions:**

- Markets with <12 months continuous data
- Markets with extreme outliers (>5 standard deviations from governorate mean)
- Markets in disputed areas with unclear currency zone classification

**Time-Period Exclusions:**

- Ramadan months (demand seasonality)
- Three months following major conflict escalations (defined as >200 ACLED fatalities/month in governorate)

**Data Quality Exclusions:**

- Price observations flagged as "estimated" by WFP
- Months with <3 commodity price observations per market

### Final Sample Size Projections

- **Markets:** ~150 (from 200+ in raw data)
- **Time periods:** ~108 months
- **Observations:** ~45,000 market-commodity-month
- **Balanced subsample:** ~25,000 observations (target 80% completion rate)

## V. Variable Definitions (LOCKED)

### Dependent Variables

**Price_USD_it:**

```
= Price_YER_it / Exchange_rate_it
Where Exchange_rate_it determined by territorial control:
- Government areas: CBY-Aden official rate
- Houthi areas: CBY-Sana'a official rate
- Contested areas: Average of both rates weighted by territorial control %
```

**Price_YER_it:**

```
= WFP reported price in Yemeni Rial
= Raw price if currency field = "YER"
= Raw price × Exchange_rate_it if currency field = "USD"
```

### Key Independent Variables

**Zone_Houthi_i:**

```
= 1 if market i in Houthi de facto control at t=0 (Jan 2015)
= 0 if market i in Government/Coalition control at t=0
Source: ACAPS territorial control maps
```

**Conflict_it:**

```
= log(1 + ACLED_fatalities_20km_it)
Where ACLED_fatalities_20km_it = sum of battle deaths within 20km of market i in month t
```

**Essential_i:**

```
= 1 if commodity ∈ {Wheat flour, Rice, Vegetable oil, Diesel, Petrol}
= 0 otherwise
Based on WFP essential commodities classification
```

### Control Variables

**Distance_to_port_i:**

```
= min(distance_to_Hodeidah, distance_to_Aden) in kilometers
Calculated using great circle distance
```

**Population_it:**

```
= log(Governorate population from WorldPop)
Linear interpolation between available years
```

**Transport_Cost_ij:**

```
= Distance_ij × Average_fuel_price_t × 0.15
Where 0.15 = estimated fuel cost per km per kg transported
```

## VI. Missing Data Protocol (LOCKED)

### Imputation Rules

**Price Data (Max 20% missing per series):**

1. **Linear interpolation** for gaps ≤2 months
2. **Carry-forward last observation** for gaps of 3 months
3. **Exclude series** with gaps >3 months or >20% total missing

**Exchange Rate Data:**

1. **Use parallel market rates** when official rates unavailable
2. **Linear interpolation** for weekends/holidays (max 7 days)
3. **Cross-validate** with neighboring governorate rates

**Conflict Data:**

1. **Zero fatalities** assumed for missing ACLED data (conservative)
2. **Validated** against alternative sources (UCDP) for major events

### Missing Not at Random (MNAR) Protocol

**Recognition:** In conflict settings, missing data is often systematic - markets stop reporting during intense conflict.

**Diagnostic Tests:**
1. **Missingness pattern analysis** by conflict intensity
2. **Selection model estimation** to test MNAR hypothesis
3. **Comparison with external conflict databases** for validation

**Mitigation Strategies:**
1. **Heckman selection models** for prices conditional on reporting
2. **Instrumental variables** using lagged reporting patterns
3. **Bounds analysis** for sensitivity to missing data assumptions

### Sensitivity Analysis

- **Complete cases only** analysis for robustness
- **Multiple imputation** (m=5) for price data sensitivity  
- **Bootstrapped standard errors** to account for imputation uncertainty
- **Missing data pattern robustness** with different thresholds

## VII. Outlier Detection Protocol (LOCKED)

### Statistical Rules (Applied Before Analysis)

**Price Outliers:**

1. **Governorate-month median ± 4 MAD** (Median Absolute Deviation)
2. **Year-over-year growth >400%** (hyperinflation threshold)
3. **Manual review** of flagged observations against news sources

**Exchange Rate Outliers:**

1. **Daily change >20%** triggers manual verification
2. **Deviation >50%** from parallel market rates triggers investigation
3. **Crossover events** (government rate > Houthi rate) flagged for review

### Treatment of Outliers

- **Winsorization** at 99th percentile (not deletion)
- **Documented reasons** for any manual adjustments
- **Robustness checks** with and without outlier treatment

## VIII. Subgroup Analyses (Pre-Specified Only)

### Primary Subgroups

**By Commodity Type:**

- Essential vs Non-essential goods
- Tradeable vs Non-tradeable goods
- Imported vs Locally-produced goods

**By Geographic Characteristics:**

- Coastal vs Inland markets
- Border vs Interior markets
- High vs Low population density

**By Temporal Periods:**

- Pre-2018 (before economic collapse)
- Post-2018 (severe economic crisis)
- Conflict escalation periods

### Interaction Tests

- **Zone × Tradeable:** Tests arbitrage mechanisms (H4)
- **Zone × Period:** Tests stability over time
- **Conflict × Commodity Type:** Exploratory robustness for H3

**No other subgroup analyses permitted** without amending this plan.

## IX. Statistical Decision Rules (LOCKED)

### Significance Levels

- **Primary hypotheses:** α = 0.0167 (Bonferroni-corrected: 0.05/3)
- **Secondary hypotheses:** α = 0.001 (Bonferroni-corrected: 0.01/10)
- **Robustness checks:** α = 0.10 (uncorrected, descriptive only)

### One-tailed vs Two-tailed Tests

- **H1:** Two-tailed (no directional prediction)
- **H3:** Two-tailed (sign ambiguous ex ante)
- **H4:** Two-tailed (joint test)
- **All secondary:** Two-tailed (exploratory)

### Minimum Effect Sizes of Interest

**Economic Significance Thresholds:**

- **H1:** |β₁| ≥ 0.15 (15% price differential economically meaningful)
- **H3:** |β₁| ≥ 0.10 (10% price response to conflict economically meaningful) 
- **H4:** Coefficients within 25% of theoretical prediction (β₁, β₂ ∈ [0.75, 1.25])

**Policy Relevance:**

- Effects below minimum thresholds reported as "statistically significant but not economically meaningful"

### Stopping Rules

- **Analysis stops** after testing pre-specified hypotheses
- **No fishing** for significant results in untested specifications
- **No post-hoc subgroup analyses** without formal plan amendment

### Decision Rules for Mixed Results

**Currency Fragmentation Theory Support:**
- **Strong Support:** H1 and H4 both significant with economically meaningful effects
- **Moderate Support:** H1 significant OR H4 significant with consistent directional evidence
- **Limited Support:** H1 or H4 significant but economically small effects
- **No Support:** Neither H1 nor H4 significant at corrected α levels

**Conflict Effects (H3) Interpretation:**
- **Independent of currency fragmentation theory**
- **Report effects regardless of H1/H4 results**
- **Acknowledge if results contradict expectations**

**Robustness Threshold:**
- **Results considered robust if stable across ≥75% of robustness checks**
- **Document specification sensitivity without post-hoc explanations**
- **Report confidence in conclusions based on robustness**

## X. Robustness Checks (Pre-Specified Complete List)

### Specification Robustness

1. **Alternative Fixed Effects:**
   - Commodity × Time FE instead of separate
   - Governorate × Time FE for local shocks
   - Market-specific linear time trends

2. **Alternative Standard Errors:**
   - Clustered at market level (instead of governorate)
   - Two-way clustering (market × time)
   - Bootstrapped standard errors

3. **Sample Robustness:**
   - Balanced panel only
   - Exclude border markets
   - Exclude 2020-2021 (COVID period)

### Variable Definition Robustness

4. **Alternative Exchange Rates:**
   - Use parallel market rates throughout
   - Use IMF official rates where available
   - Use trade-weighted average rates

5. **Alternative Conflict Measures:**
   - 10km radius instead of 20km
   - Weighted by distance decay
   - Include non-violent events

6. **Alternative Essential Good Definition:**
   - WFP food basket definition
   - Caloric content threshold
   - Import dependence threshold

### Methodological Robustness

7. **Non-parametric Approaches:**
   - Median regression (quantile τ=0.5)
   - Matching estimators for zone effects
   - Machine learning predictions as controls

8. **Alternative Estimation:**
   - Instrumental variables for endogenous regressors
   - System GMM for dynamic panels
   - Spatial regression models

**Total robustness checks:** 16 (8 × 2 alternative approaches)

**Protocol for Unstable Results:**
- If main result fails in >25% of robustness checks: Report as "specification-sensitive"
- If result changes sign across specifications: Report as "directionally uncertain"  
- If result loses significance in majority of checks: Report as "not robust"
- Always report the complete range of estimates across specifications

**No additional robustness checks permitted** after seeing results.

## XI. Software and Computational Details

### Primary Analysis Platform

- **Software:** Stata 18.0
- **Packages:** reghdfe, ftools, ivreghdfe, asdoc
- **Random seed:** 12345 (for bootstrapping)

### Data Processing

- **Language:** Python 3.9+
- **Libraries:** pandas, numpy, geopandas, scipy
- **Workflow:** Documented in scripts/analysis/

### Version Control

- **Platform:** Git/GitHub
- **Branch:** pre-analysis-plan-locked
- **Commit requirements:** Signed commits for all analysis code

## XII. Timeline and Milestones

### Phase 1: Data Preparation (Week 1-2)

- Finalize exchange rate database
- Complete territorial control mapping
- Generate analysis dataset

### Phase 2: Primary Hypothesis Testing (Week 3-4)

- H1, H3, H4 estimation and testing
- Multiple testing corrections applied
- Primary results documentation

### Phase 3: Secondary Analyses (Week 5-6)

- H2, H5, H6-H10, S1, N1, P1 estimation
- Exploratory findings documentation
- Robustness check execution

### Phase 4: Results Integration (Week 7-8)

- Cross-hypothesis synthesis
- Policy implications analysis
- Publication preparation

**Total timeline:** 8 weeks from data lock to final results

## XIII. Amendments and Violations

### Amendment Process

- **Major changes:** Require unanimous co-author agreement + external reviewer approval
- **Minor changes:** Technical clarifications only, no substantive modifications
- **Documentation:** All amendments logged with justification

### Violation Monitoring

- **Automated checks:** Git hooks prevent analysis code commits before plan lock
- **Manual oversight:** External monitor reviews all analysis decisions
- **Transparency:** Any violations disclosed in methodology appendix

### Acceptable Deviations

- **Software updates:** Minor version changes acceptable
- **Data corrections:** Obvious errors (negative prices, prices >10x market median, duplicate entries, clear unit conversion errors) can be corrected
- **Sample adjustments:** Only if external data sources change substantially

## XIV. Publication and Transparency Commitments

### Open Science Practices

- **Pre-registration:** AEA RCT Registry within 48 hours of plan lock
- **Code sharing:** Full replication package on GitHub upon publication
- **Data sharing:** De-identified data archived with Dataverse

### Results Reporting

- **All results reported:** Significant and non-significant findings
- **Multiple testing disclosure:** Corrected and uncorrected p-values
- **Effect size reporting:** Economic and statistical significance distinguished

### Conflicts of Interest

- **Funding disclosure:** All funding sources acknowledged
- **Researcher bias:** Potential confirmation bias regarding currency fragmentation acknowledged

---

## XV. Digital Signatures and Timestamps

**Principal Investigator:** [NAME]
**Signature:** [DIGITAL SIGNATURE]
**Date:** January 6, 2025

**Co-Investigators:** [NAMES]
**Signatures:** [DIGITAL SIGNATURES]
**Date:** January 6, 2025

**External Monitor:** [NAME]
**Signature:** [DIGITAL SIGNATURE]
**Date:** January 6, 2025

**Git Commit Hash:** [TO BE COMPLETED UPON LOCK]
**MD5 Hash:** [TO BE COMPLETED UPON LOCK]
**Cryptographic Timestamp:** [TO BE COMPLETED UPON LOCK]

---

**END OF PRE-ANALYSIS PLAN**

*This document is cryptographically signed and timestamped. Any modifications after the lock date invalidate the pre-commitment and must be disclosed as post-hoc changes.*
