# Yemen Market Integration Analysis Workflow

**Document Version:** 1.0
**Date:** January 6, 2025
**Status:** LOCKED (Part of Pre-Analysis Plan)
**Last Modified:** [LOCKED - NO MODIFICATIONS PERMITTED]

## I. Workflow Overview

This document specifies the exact sequence of analytical steps for the Yemen Market Integration study. **No deviations from this workflow are permitted** without formal amendment to the pre-analysis plan. All steps must be completed in the specified order to prevent data mining and specification searching.

### Workflow Principles

1. **Sequential execution:** Steps cannot be reordered or skipped
2. **No feedback loops:** Results from later steps cannot modify earlier steps
3. **Complete documentation:** Every decision point must be documented before proceeding
4. **Stopping rule enforcement:** Analysis ends after completing all pre-specified steps

## II. Phase 1: Data Preparation and Validation (Week 1-2)

### Step 1.1: Raw Data Collection and Verification

**Duration:** 2 days
**Responsible:** Data Manager
**Deliverable:** `data/raw/` directory with timestamped files

**Tasks:**

1. **Download WFP price data** from official API (automated script)
2. **Download ACLED conflict data** for Yemen 2015-2024
3. **Collect exchange rate data** from CBY-Aden, CBY-Sana'a, parallel market sources
4. **Gather territorial control maps** from ACAPS (monthly snapshots)
5. **Archive population data** from WorldPop for Yemen governorates

**Quality Checks:**

- Verify file timestamps and checksums
- Cross-check record counts with expected values
- Document any data source changes or anomalies

**Stopping Criterion:** All data sources successfully downloaded and verified

### Step 1.2: Exchange Rate Database Construction

**Duration:** 3 days
**Responsible:** Exchange Rate Specialist
**Deliverable:** `data/processed/exchange_rates_master.csv`

**Tasks:**

1. **Standardize exchange rate series** to daily frequency
2. **Map territorial control** to appropriate exchange rate regime
3. **Fill weekends/holidays** using linear interpolation (max 7 days)
4. **Validate against external sources** (IMF, Central Bank announcements)
5. **Create parallel market rate estimates** for missing official rates

**Quality Checks:**

- No gaps >7 days in any series
- Cross-validation with news reports for major rate changes
- Logical consistency (no negative rates, extreme jumps)

**Pre-specified Rules:**

- Use CBY-Aden rates for government-controlled areas
- Use CBY-Sana'a rates for Houthi-controlled areas
- Use population-weighted average for contested areas
- Linear interpolation only for gaps ≤7 days

### Step 1.3: Currency Zone Assignment

**Duration:** 2 days
**Responsible:** GIS Analyst
**Deliverable:** `data/processed/market_currency_zones.csv`

**Tasks:**

1. **Geocode all WFP markets** using official coordinates
2. **Overlay with ACAPS control maps** (monthly basis)
3. **Assign currency zone classification** based on territorial control
4. **Handle disputed areas** using population-weighted allocation
5. **Create time-varying zone indicators** for markets with control changes

**Quality Checks:**

- 100% of markets successfully geocoded
- No markets assigned to multiple zones simultaneously
- Control changes align with conflict event timing

**Decision Rules (LOCKED):**

- Markets in 60%+ controlled territory → assigned to controlling party
- Markets in contested areas → weighted average of both rates
- Missing control data → use nearest neighbor assignment

### Step 1.4: Price Data Standardization

**Duration:** 3 days
**Responsible:** Price Data Analyst
**Deliverable:** `data/processed/prices_standardized.csv`

**Tasks:**

1. **Convert all prices to common units** (per kg for solids, per liter for liquids)
2. **Standardize commodity classifications** using WFP taxonomy
3. **Create USD price series** using appropriate exchange rates
4. **Flag outlier observations** using pre-specified rules
5. **Generate missing data indicators** and patterns analysis

**Outlier Detection Rules (LOCKED):**

- Governorate-month median ± 4 MAD (Median Absolute Deviation)
- Year-over-year growth >400% (hyperinflation threshold)
- Negative prices or zero prices (data errors)
- Manual review against news sources for major price shocks

**Treatment Rules:**

- Winsorize at 99th percentile (no deletion)
- Document all manual adjustments with justification
- Create robustness flags for sensitivity analysis

### Step 1.5: Sample Definition and Restrictions

**Duration:** 1 day
**Responsible:** Principal Investigator
**Deliverable:** `data/analysis/final_analysis_sample.csv`

**Tasks:**

1. **Apply inclusion criteria** as specified in pre-analysis plan
2. **Apply exclusion criteria** as specified in pre-analysis plan
3. **Generate sample summary statistics** and CONSORT-style flow diagram
4. **Create analysis weights** for representative inference
5. **Validate sample representativeness** against population parameters

**Final Sample Criteria (LOCKED):**

- Markets: ≥24 months of observations, ≥5 commodities tracked
- Time: January 2015 - December 2024, exclude Ramadan months
- Commodities: ≥60% data availability per market-commodity pair
- Quality: Exclude WFP "estimated" price flags

**Documentation Required:**

- Exact number of observations at each filtering step
- Geographic and temporal distribution of final sample
- Comparison with excluded observations (selection bias assessment)

## III. Phase 2: Descriptive Analysis and Validation (Week 2-3)

### Step 2.1: Exploratory Data Analysis (PRE-SPECIFIED ONLY)

**Duration:** 2 days
**Responsible:** Statistical Analyst
**Deliverable:** `results/descriptive/eda_report.html`

**Permitted Analyses (LOCKED LIST):**

1. **Sample summary statistics** by currency zone, commodity, time period
2. **Price level comparisons** in YER vs USD across zones
3. **Exchange rate volatility patterns** over time and space
4. **Missing data patterns** and representativeness assessment
5. **Conflict intensity distributions** by zone and time
6. **Market accessibility measures** (distance to ports, roads)

**Visualization Requirements:**

- Time series plots for key variables (no data mining allowed)
- Geographic maps of sample coverage and zones
- Distribution plots for main outcome variables
- Correlation matrices for control variables

**Prohibited Analyses:**

- Any regression analysis or hypothesis testing
- Subgroup comparisons beyond pre-specified categories
- Exploration of relationships not specified in hypotheses

### Step 2.2: Data Quality Assessment

**Duration:** 1 day
**Responsible:** Data Quality Manager
**Deliverable:** `results/diagnostics/data_quality_report.pdf`

**Required Diagnostics:**

1. **Completeness rates** by variable, zone, time period
2. **Temporal stability** of reporting patterns
3. **Cross-source validation** where possible
4. **Logical consistency checks** (price relationships, exchange rate bounds)
5. **External validity** comparison with other data sources

**Quality Thresholds:**

- Overall completeness: >80% for primary variables
- Temporal stability: No systematic reporting gaps >3 months
- Cross-validation: Correlation >0.7 with external sources where available

### Step 2.3: Sample Representativeness Assessment

**Duration:** 1 day
**Responsible:** Sampling Specialist
**Deliverable:** `results/diagnostics/representativeness_assessment.pdf`

**Analyses Required:**

1. **Geographic coverage** vs total market population
2. **Temporal coverage** vs conflict event intensity
3. **Commodity coverage** vs national consumption patterns
4. **Population weighting** validation and adjustment
5. **Selection bias assessment** for missing markets/periods

**Benchmarking Sources:**

- UN OCHA market assessments for geographic coverage
- FAO consumption data for commodity importance
- World Bank conflict databases for temporal patterns

## IV. Phase 3: Primary Hypothesis Testing (Week 3-4)

**Primary Hypotheses:** H1, H3, H4 estimation and testing with Bonferroni correction

### Step 3.1: H1 - Exchange Rate and Price Differentials Testing

**Duration:** 2 days
**Responsible:** Econometrician
**Deliverable:** `results/primary/h1_price_differentials.log`

**Exact Analysis Sequence:**

1. **Run primary specification** as defined in pre-analysis plan
2. **Apply multiple testing correction** (Bonferroni for primary hypotheses)
3. **Calculate economic significance** assessment
4. **Run robustness specification 1** (commodity × time FE)
5. **Run robustness specification 2** (first differences)

**Statistical Code (LOCKED):**

```stata
* Primary specification  
reg price_usd zone controls, cluster(governorate)

* Alternative with fixed effects
reghdfe log_price_usd zone conflict_intensity log_population ///
    distance_to_port, absorb(market month) cluster(governorate)

* Store results and apply correction
```

**Required Outputs:**

- Point estimates and confidence intervals
- Multiple testing corrected p-values
- Economic significance assessment (15% threshold)
- Robustness check results comparison

**Interpretation Guidelines:**

- Statistical significance at corrected α = 0.0167
- Economic significance if |β₁| ≥ 0.14 (15% effect)
- Two-tailed test: report direction without predetermined expectations

### Step 3.2: H3 - Conflict Effects on Commodity Markets Testing

**Duration:** 2 days
**Responsible:** Conflict Specialist
**Deliverable:** `results/primary/h3_conflict_effects.log`

**Exact Analysis Sequence:**

1. **Run primary specification** with conflict intensity
2. **Test coefficient significance**
3. **Calculate effect magnitude** at different conflict levels
4. **Run robustness checks** with alternative conflict measures
5. **Assess economic magnitude** of effects

**Statistical Code (LOCKED):**

```stata
* Primary specification
reghdfe log_price conflict_intensity log_population distance_to_port, ///
    absorb(market month) cluster(governorate)

* Test significance
test conflict_intensity = 0
```

**Required Outputs:**

- Conflict coefficient and significance
- Effect size interpretation
- Economic magnitude assessment (10% threshold)
- Robustness across specifications

### Step 3.3: H4 - Cross-Border Arbitrage Testing

**Duration:** 2 days
**Responsible:** Trade Economist
**Deliverable:** `results/primary/h4_arbitrage_mechanism.log`

**Exact Analysis Sequence:**

1. **Construct market pair dataset** for tradeable goods only
2. **Run arbitrage equation** as specified in pre-analysis plan
3. **Test joint hypothesis** β₁ = β₂ = 1
4. **Calculate deviations** from perfect arbitrage theory
5. **Assess economic significance** of arbitrage failures

**Statistical Code (LOCKED):**

```stata
* Arbitrage specification for tradeable goods
reg price_diff transport_cost exchange_diff if tradeable==1, ///
    cluster(market_pair)

* Joint test of perfect arbitrage
test transport_cost = 1
test exchange_diff = 1
test transport_cost = exchange_diff = 1
```

**Required Outputs:**

- Individual coefficient tests against theory
- Joint test of perfect arbitrage (F-statistic)
- Economic assessment of arbitrage efficiency
- Subgroup analysis by commodity type

### Step 3.4: Primary Results Integration

**Duration:** 1 day
**Responsible:** Principal Investigator
**Deliverable:** `results/primary/primary_results_summary.pdf`

**Required Analyses:**

1. **Synthesize primary hypothesis results** with corrected p-values
2. **Assess overall support** for currency fragmentation theory
3. **Calculate combined effect sizes** and policy implications
4. **Document any unexpected findings** requiring further investigation
5. **Prepare preliminary policy recommendations**

**Decision Points:**

- Strong support: All 3 primary hypotheses significant at corrected α
- Moderate support: 2 of 3 hypotheses significant + consistent signs
- Weak support: 1 hypothesis significant or inconsistent patterns
- No support: 0 hypotheses significant at corrected α

## V. Phase 4: Secondary Hypothesis Testing (Week 5-6)

### Step 4.1: Secondary Hypotheses (H2, H5, H6-H10)

**Duration:** 4 days
**Responsible:** Team of Specialists
**Deliverable:** `results/secondary/` directory with individual hypothesis results

**Analysis Protocol for Each Hypothesis:**

1. **Check data availability** and sample size adequacy
2. **Run primary specification** if data sufficient
3. **Apply Bonferroni correction** (α = 0.001)
4. **Focus on effect size interpretation** given low power
5. **Report confidence intervals** rather than binary significance

**Permitted Explorations:**

- Only hypotheses specified in pre-analysis plan
- Only specifications defined in advance
- No post-hoc subgroup analyses
- No alternative variable definitions

### Step 4.2: Methodological Hypotheses (S1, N1, P1)

**Duration:** 2 days
**Responsible:** Methodology Team
**Deliverable:** `results/methodological/methodology_extensions.pdf`

**Purpose:** Model specification guidance rather than confirmatory testing

**Analyses Permitted:**

- Spatial correlation patterns (S1)
- Network effect assessments (N1)
- Political economy relationships (P1)
- Model specification comparisons
- Identification strategy validation

**Interpretation:** Guidance for future research, not definitive conclusions

## VI. Phase 5: Robustness Testing (Week 6-7)

### Step 5.1: Pre-Specified Robustness Checks

**Duration:** 3 days
**Responsible:** Robustness Team
**Deliverable:** `results/robustness/robustness_battery.pdf`

**Required Robustness Checks (COMPLETE LIST):**

**Specification Robustness:**

1. Alternative fixed effects structures
2. Alternative standard error clustering
3. Market-specific linear time trends
4. Different lag structures for dynamic variables

**Sample Robustness:**
5. Balanced panel only
6. Exclude border markets
7. Exclude COVID period (2020-2021)
8. Alternative outlier treatment

**Variable Definition Robustness:**
9. Alternative exchange rate sources
10. Alternative conflict measures (10km vs 20km radius)
11. Alternative essential good definitions
12. Alternative transport cost calculations

**Methodological Robustness:**
13. Instrumental variables for endogenous regressors
14. Quantile regression (median)
15. Bootstrapped standard errors
16. Placebo tests with randomized treatment

**Reporting Requirements:**

- Summary table of all robustness check results
- Assessment of result stability across specifications
- Documentation of any specification-sensitive findings
- Confidence assessment for main conclusions

### Step 5.2: Sensitivity Analysis

**Duration:** 2 days
**Responsible:** Sensitivity Analyst
**Deliverable:** `results/sensitivity/sensitivity_analysis.pdf`

**Required Sensitivity Tests:**

1. **Parameter stability** across subsamples
2. **Influence diagnostics** for outlier markets/periods
3. **Specification curve analysis** for reasonable variations
4. **Bootstrap confidence intervals** for primary estimates
5. **Power analysis validation** using achieved sample

**Documentation:**

- Which results are robust vs sensitive
- Explanation for any result instability
- Assessment of practical significance despite sensitivity

## VII. Phase 6: Results Integration and Interpretation (Week 7-8)

### Step 6.1: Cross-Hypothesis Synthesis

**Duration:** 2 days
**Responsible:** Principal Investigator
**Deliverable:** `results/final/integrated_results.pdf`

**Required Analyses:**

1. **Synthesize evidence** across all hypotheses
2. **Assess consistency** with currency fragmentation theory
3. **Quantify policy implications** with confidence intervals
4. **Identify remaining uncertainties** and limitations
5. **Compare findings** with theoretical predictions

**Integration Framework:**

- Weight evidence by hypothesis priority and power
- Acknowledge contradictory findings without post-hoc explanations
- Focus on effect sizes and economic significance
- Provide nuanced interpretation of borderline results

### Step 6.2: Policy Implications Analysis

**Duration:** 2 days
**Responsible:** Policy Team
**Deliverable:** `results/policy/policy_implications.pdf`

**Required Elements:**

1. **Humanitarian aid targeting** recommendations with quantified benefits
2. **Exchange rate policy** implications for market integration
3. **Market intervention** strategies based on arbitrage findings
4. **Early warning system** potential using established relationships
5. **Cross-country applicability** assessment

**Policy Translation:**

- Convert statistical findings to operational guidance
- Quantify expected impacts of policy changes
- Acknowledge uncertainty and provide confidence bounds
- Suggest implementation pilots and evaluation frameworks

### Step 6.3: Publication Preparation

**Duration:** 3 days
**Responsible:** Writing Team
**Deliverable:** `manuscripts/main_paper_draft.tex`

**Required Sections:**

1. **Abstract** summarizing main findings and policy implications
2. **Introduction** with literature review and contribution
3. **Data and Methods** with full transparency about decisions
4. **Results** reporting all pre-specified analyses
5. **Discussion** interpreting findings within broader context
6. **Conclusion** with limitations and future research directions

**Transparency Requirements:**

- Full disclosure of pre-analysis plan adherence
- Report all results (significant and non-significant)
- Distinguish confirmatory from exploratory findings
- Provide access to replication materials

## VIII. Quality Control and Stopping Rules

### Quality Gates

- **Gate 1 (End Phase 1):** Data quality meets minimum thresholds
- **Gate 2 (End Phase 2):** Sample representativeness validated
- **Gate 3 (End Phase 3):** Primary results obtained and documented
- **Gate 4 (End Phase 5):** Robustness analysis completed
- **Gate 5 (End Phase 6):** Final results integrated and validated

### Stopping Rules

1. **Analysis ends** after completing all pre-specified steps
2. **No additional fishing** for significant results
3. **No post-hoc modifications** to hypotheses or specifications
4. **No alternative interpretations** not supported by pre-specified tests

### Documentation Requirements

- **Daily log** of all analytical decisions and rationale
- **Code repository** with timestamped commits for each step
- **Results archive** with intermediate outputs saved
- **Deviation log** documenting any unavoidable changes from plan

### Final Validation

- **Independent replication** of all key results by external analyst
- **Code review** by methodology expert not involved in analysis
- **Results verification** against pre-analysis plan commitments
- **Sign-off** by all co-authors on final interpretations

---

**WORKFLOW STATUS: LOCKED**
**Next Action:** Begin Phase 1, Step 1.1 - Raw Data Collection
**Responsible:** Data Manager
**Timeline:** Start immediately upon pre-analysis plan lock

**NO DEVIATIONS FROM THIS WORKFLOW ARE PERMITTED WITHOUT FORMAL AMENDMENT TO THE PRE-ANALYSIS PLAN**
