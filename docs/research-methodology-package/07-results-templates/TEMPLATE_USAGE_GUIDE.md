# Template Usage Guide: Maintaining Scientific Integrity

## Purpose

This guide ensures that all results templates are used properly to maintain scientific integrity and prevent predetermined conclusions. Following these guidelines is essential for producing credible, peer-reviewable research.

## Core Principles

### 1. Templates Are Not Predictions
Templates provide structure, not expected outcomes. They should:
- ✅ Define what to measure
- ✅ Specify how to interpret results
- ✅ Prepare for multiple outcomes
- ❌ NOT suggest what results "should" be
- ❌ NOT contain predetermined conclusions

### 2. Pre-Specification is Required
Before running any analysis:
- Define all hypotheses clearly
- Establish interpretation thresholds
- Prepare for null results
- Document decision rules
- Commit to reporting all tests

### 3. Let Data Drive Conclusions
The analysis process should follow this order:
1. **Hypothesize** → What might be true?
2. **Test** → What do the data show?
3. **Interpret** → What does this mean?
4. **Report** → What did we learn?

Never reverse this order by starting with desired conclusions.

## Step-by-Step Template Usage

### Step 1: Select Appropriate Template

Choose based on analysis type:
- **Exchange Rate Analysis** → `exchange-rate-impact.md`
- **Commodity Analysis** → `wheat-analysis.md` (general commodity template)
- **Conflict Analysis** → `conflict-spillovers.md`
- **Null Results** → `NULL_RESULTS_TEMPLATE.md`

### Step 2: Fill Pre-Analysis Sections

Before touching any data:

```markdown
## Pre-Analysis Plan
- **Hypothesis**: [STATE CLEARLY]
- **Expected Direction**: [IF THEORY SUGGESTS]
- **Alternative Outcomes**: [LIST ALL POSSIBILITIES]
- **Interpretation Rules**: [DEFINE THRESHOLDS]
- **Robustness Checks**: [PLAN VARIATIONS]
```

### Step 3: Run Analysis

Execute analysis following the pre-specified plan:
- Use the exact tests specified
- Apply predetermined thresholds
- Run all planned robustness checks
- Document any deviations with explanations

### Step 4: Fill Results Sections

Replace placeholders with actual findings:

#### Good Example:
```markdown
**Test Performed**: Cointegration test (Johansen)
**Test Statistic**: 23.45
**P-value**: 0.034
**Interpretation**: Evidence for long-run relationship at 5% level
```

#### Bad Example:
```markdown
**Test Performed**: Cointegration test
**Result**: As expected, markets are strongly integrated
**Interpretation**: This revolutionary finding changes everything
```

### Step 5: Apply Decision Framework

Use the pre-specified decision rules from `RESULTS_DECISION_FRAMEWORK.md`:

```python
if p_value < 0.05 and effect_size > 0.2:
    interpretation = "Significant and meaningful effect"
elif p_value < 0.05 and effect_size <= 0.2:
    interpretation = "Statistically significant but small effect"
else:
    interpretation = "No significant effect detected"
```

### Step 6: Handle Unexpected Results

When results contradict hypotheses:
1. **Report honestly**: State what was expected vs. found
2. **Investigate**: Check for errors, alternative explanations
3. **Learn**: Update understanding based on evidence
4. **Document**: Explain why initial hypothesis may have been wrong

## Quality Control Checklist

### Before Analysis
- [ ] Hypotheses clearly stated
- [ ] Decision rules pre-specified
- [ ] All tests planned in advance
- [ ] Alternative outcomes considered
- [ ] Null result protocols ready

### During Analysis
- [ ] Following pre-analysis plan
- [ ] Documenting any deviations
- [ ] Running all specified tests
- [ ] Checking data quality
- [ ] Noting unexpected patterns

### After Analysis
- [ ] All results reported (not just significant ones)
- [ ] Confidence intervals included
- [ ] Effect sizes calculated
- [ ] Robustness checks completed
- [ ] Limitations acknowledged

## Common Pitfalls to Avoid

### 1. HARKing (Hypothesizing After Results are Known)
**Wrong**: "We hypothesized that X would affect Y" (after finding effect)
**Right**: "Exploratory analysis revealed an unexpected relationship between X and Y"

### 2. P-Hacking
**Wrong**: Running multiple tests until finding p < 0.05
**Right**: Reporting all tests with appropriate multiple testing corrections

### 3. Cherry-Picking
**Wrong**: Reporting only favorable robustness checks
**Right**: Reporting all robustness checks, explaining variations

### 4. Overstating Findings
**Wrong**: "This proves that markets are integrated"
**Right**: "Evidence suggests markets may be integrated (p = 0.03, 95% CI: [0.15, 0.45])"

### 5. Ignoring Null Results
**Wrong**: Not reporting non-significant findings
**Right**: "We found no evidence for hypothesis X (p = 0.34), suggesting..."

## Template Modification Protocol

If templates need updating:

1. **Document reason for change**
   - What limitation was discovered?
   - How does change improve analysis?

2. **Maintain version history**
   - Keep original template
   - Date and annotate changes
   - Explain modifications

3. **Update consistently**
   - Apply changes to all relevant templates
   - Update decision framework
   - Revise code implementations

## Integration with Code

Use the `template_generator.py` module:

```python
from src.core.reporting.template_generator import ResultsTemplateGenerator

# Initialize generator
generator = ResultsTemplateGenerator()

# Generate results section
results_text = generator.generate_results_section(
    test_results=actual_results,  # From your analysis
    hypothesis_id="H1",
    template_type=TemplateType.EXCHANGE_RATE
)

# Validate output
if generator.validate_template_integrity(results_text):
    print("Results properly formatted")
else:
    print("Warning: Predetermined language detected")
```

## Methodological Evolution

Templates should evolve based on learning:

### Initial Analysis
- Start with standard templates
- Follow established methodology
- Document all findings

### Learning Phase
- Identify what worked/didn't work
- Note methodological insights
- Update templates accordingly

### Improved Analysis
- Apply lessons learned
- Use refined templates
- Maintain transparency about evolution

## Example: Currency Conversion Learning

The project learned from initial currency conversion errors:

**Original Approach**: Compare prices directly in local currency
**Problem Discovered**: Different exchange rates made comparison invalid
**Lesson Learned**: Always convert to common currency first
**Template Updated**: Added currency conversion checklist

This demonstrates healthy methodological evolution.

## Reporting Guidelines

### For Academic Papers
- Use formal statistical language
- Include all technical details
- Follow journal requirements
- Provide replication materials

### For Policy Briefs
- Translate statistics to practical terms
- Focus on actionable insights
- Include confidence assessments
- Avoid technical jargon

### For Public Communication
- Use accessible language
- Emphasize uncertainty appropriately
- Avoid sensational claims
- Provide context for findings

## Final Reminders

1. **Science is about discovery, not confirmation**
   - Be excited to learn what's true
   - Not disappointed when hypotheses fail

2. **Null results are valuable**
   - They prevent bad policies
   - Guide future research
   - Build cumulative knowledge

3. **Transparency builds trust**
   - Acknowledge limitations
   - Document methodology
   - Share data and code

4. **Methods matter more than results**
   - Good methods with null results > Bad methods with "significant" results
   - Reproducibility is key
   - Learn from mistakes

## Conclusion

These templates are tools for discovering truth, not confirming beliefs. Use them to:
- Structure rigorous analysis
- Maintain scientific standards
- Produce credible research
- Learn from data

Remember: The goal is not to find what we expect, but to discover what is actually true.

---
*"In God we trust. All others must bring data."* - W. Edwards Deming