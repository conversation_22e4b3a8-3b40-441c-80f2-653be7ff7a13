# Template Version Control System

## Purpose

This document tracks all changes to results templates, ensuring transparency about methodological evolution and maintaining historical records of template improvements.

## Version Control Principles

1. **All changes documented** - No silent updates
2. **Reasons recorded** - Why each change was made
3. **Lessons preserved** - What we learned that prompted changes
4. **Backwards compatibility** - Old analyses remain valid

## Current Template Versions

### Core Templates

| Template | Current Version | Last Updated | Status |
|----------|----------------|--------------|---------|
| Exchange Rate Impact | 2.0 | 2025-01-06 | Active |
| Commodity Analysis | 2.0 | 2025-01-06 | Active |
| Descriptive Statistics | 1.0 | 2025-01-06 | Active |
| Policy Brief | 1.0 | 2025-01-06 | Active |
| Null Results | 1.0 | 2025-01-06 | Active |
| Pre-Analysis Registration | 1.0 | 2025-01-06 | Active |

### Supporting Documents

| Document | Version | Last Updated | Status |
|----------|---------|--------------|---------|
| Results Decision Framework | 1.0 | 2025-01-06 | Active |
| Template Usage Guide | 1.0 | 2025-01-06 | Active |
| Template Generator Code | 1.0 | 2025-01-06 | Active |

## Change Log

### 2025-01-06: Major Transformation (v2.0)

**What Changed**:
- Removed all predetermined conclusions from templates
- Added placeholders for actual results
- Created decision trees for interpretation
- Added mandatory currency conversion checklists
- Implemented null results protocols

**Why Changed**:
- Initial templates contained predetermined findings
- Currency conversion errors in early analysis taught important lessons
- Scientific integrity requires pre-specified decision rules
- Null results were not adequately valued

**Lessons Learned**:
- Templates should guide analysis, not predetermine outcomes
- Currency conversion must be verified before any price analysis
- Decision rules must be established before seeing data
- All results (including null) have scientific value

**Files Affected**:
- exchange-rate-impact.md (v1.0 → v2.0)
- wheat-analysis.md (v1.0 → v2.0) [generalized to commodity template]
- All templates now include currency checklists

### Template Evolution Examples

#### Exchange Rate Template Evolution

**Version 1.0 (Original)**:
```markdown
"Yemen has 4x exchange rate differential creating extreme fragmentation"
```

**Version 2.0 (Current)**:
```markdown
"Exchange rate differential magnitude: [TO BE CALCULATED]"
```

**Reason**: Original contained predetermined conclusion; new version requires empirical determination.

#### Currency Checklist Addition

**Added to All Templates v2.0**:
```markdown
### CRITICAL: Currency Conversion Checklist
⚠️ **MUST COMPLETE BEFORE ANY ANALYSIS** ⚠️
- [ ] All prices identified as YER or USD in raw data
- [ ] Exchange rates obtained for each market and time period
- [ ] Conversion method documented
- [ ] Verification: Converted prices fall within reasonable ranges
```

**Reason**: Early analysis errors from comparing YER and USD prices directly.

## Version Migration Guide

### For Ongoing Analyses Using v1.0 Templates

1. **Complete currency verification** immediately
2. **Remove any predetermined language** from drafts
3. **Apply decision framework** to interpret existing results
4. **Document what was pre-specified** vs. exploratory

### For New Analyses

1. **Use only v2.0+ templates**
2. **Complete pre-analysis registration** first
3. **Follow template usage guide** strictly
4. **Document any deviations** with reasons

## Quality Control Process

### Before Template Updates

1. **Identify issue** requiring template change
2. **Document lesson** learned
3. **Propose specific** changes
4. **Review impact** on existing analyses
5. **Update version** number

### Template Change Checklist

- [ ] Issue clearly documented
- [ ] Change improves scientific integrity
- [ ] Backwards compatibility considered
- [ ] Examples updated to match
- [ ] Usage guide reflects changes
- [ ] Version control updated

## Deprecated Elements

### Removed in v2.0

1. **Predetermined findings**
   - "Revolutionary discovery"
   - "Game-changing results"
   - "As expected"
   - Specific numerical claims before analysis

2. **Biased interpretations**
   - Leading language
   - Causal claims without evidence
   - Cherry-picked comparisons

3. **Missing protocols**
   - No null results guidance
   - No decision rules
   - No currency verification

## Future Improvements Planned

### Version 2.1 (Planned)

**Potential Additions**:
- Machine learning integration templates
- Real-time monitoring protocols
- Multi-country comparison frameworks
- Automated quality checks

**Triggers for Update**:
- New methodological insights
- User feedback on unclear sections
- Integration with new tools
- Regulatory requirements

## Template Integrity Verification

### Automated Checks (template_generator.py)

```python
forbidden_phrases = [
    "revolutionary discovery",
    "game-changing",
    "as expected",
    "confirms our hypothesis"
]
```

### Manual Review Points

1. Contains placeholders not predetermined values
2. Includes multiple outcome scenarios
3. Has decision rules for interpretation
4. Acknowledges uncertainty appropriately
5. Values null results

## Historical Preservation

### Archive Location
`/docs/research-methodology-package/archive/template-versions/`

### What's Preserved
- Original template versions
- Change justifications
- Lessons learned documents
- Example analyses from each version

## User Feedback Integration

### Reporting Issues
- GitHub Issues: Tag with `template-improvement`
- Email: [<EMAIL>]
- Include: Specific problem, suggested solution

### Review Process
1. Quarterly template review meetings
2. User feedback assessment
3. Methodological advancement integration
4. Version update decision

## Certification

By using these templates, researchers certify that they:
1. Understand version differences
2. Will apply current best practices
3. Will document any modifications
4. Will report version used in publications

---

*Version control ensures methodological transparency and continuous improvement while maintaining scientific integrity.*