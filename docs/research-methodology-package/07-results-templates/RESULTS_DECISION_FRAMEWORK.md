# Results Interpretation Decision Framework

## Purpose
This document provides decision rules for interpreting results BEFORE seeing them. All interpretation criteria must be established prior to running analyses to ensure scientific integrity and prevent cherry-picking.

## Core Principles

### 1. Pre-specification Requirement
- All interpretation thresholds defined before analysis
- Decision trees established independent of results
- Alternative explanations prepared for each outcome
- Policy implications conditional on findings

### 2. Scientific Integrity Standards
- Report ALL pre-specified tests, regardless of outcome
- Include confidence intervals and effect sizes, not just p-values
- Acknowledge when results don't support hypotheses
- Present null results as valuable scientific findings

## Decision Rules by Hypothesis

### H1: Exchange Rate Mechanism
```
IF β_zone significant in YER regression AND
   β_zone NOT significant in USD regression THEN
   → "Exchange rates explain price differentials"
   → Policy focus: Currency reunification
   
ELSE IF β_zone significant in BOTH regressions THEN
   → "Factors beyond exchange rates matter"
   → Investigate: Quality, transaction costs, market power
   → Policy focus: Multi-dimensional interventions
   
ELSE IF β_zone NOT significant in EITHER THEN
   → "No systematic price differentials found"
   → Check: Statistical power, measurement error, aggregation
   → Policy focus: Current system adequate
```

### H2: Market Integration Levels
```
Integration Metric Interpretation:
Speed of Adjustment (λ):
- λ > 0.5: "Strong integration" - Markets adjust quickly
- 0.2 ≤ λ ≤ 0.5: "Moderate integration" - Normal adjustment
- λ < 0.2: "Weak integration" - Markets segmented

Half-life (periods):
- < 3 periods: "Rapid adjustment" - Efficient arbitrage
- 3-12 periods: "Normal adjustment" - Typical for developing markets
- > 12 periods: "Slow adjustment" - Barriers present

Long-run Pass-through:
- > 0.8: "Complete transmission" - Price shocks fully transmitted
- 0.3-0.8: "Partial transmission" - Some price isolation
- < 0.3: "Limited transmission" - Markets largely isolated
```

### H3: Conflict Impact Assessment
```
IF conflict_coefficient > 0 AND significant THEN
   → "Conflict increases prices/reduces integration"
   → Magnitude interpretation:
     - < 10%: "Small effect" - Markets resilient
     - 10-30%: "Moderate effect" - Some market disruption
     - > 30%: "Large effect" - Severe market breakdown

ELSE IF conflict_coefficient < 0 AND significant THEN
   → "Conflict reduces prices" (potential data quality issue)
   → Investigate: Survivor bias, reporting patterns, quality changes
   
ELSE IF NOT significant THEN
   → "No detectable conflict effect"
   → Check: Statistical power, timing, local vs distant violence
```

### H4: Currency Zone Differences
```
Zone Effect Assessment:
Price Differential Thresholds:
- < 5%: "Negligible difference" - Markets integrated
- 5-20%: "Small but meaningful difference" - Monitor
- 20-50%: "Large difference" - Policy intervention needed
- > 50%: "Extreme segmentation" - Crisis response required

Statistical Significance:
- p < 0.01: "Strong evidence"
- 0.01 ≤ p < 0.05: "Moderate evidence"
- 0.05 ≤ p < 0.10: "Weak evidence"
- p ≥ 0.10: "No evidence"

Effect Size (Cohen's d):
- d < 0.2: "Small effect"
- 0.2 ≤ d < 0.8: "Medium effect"
- d ≥ 0.8: "Large effect"
```

## Methodology-Specific Thresholds

### Cointegration Tests
```
Johansen Test:
- Trace statistic > critical value: "Cointegrated"
- Max eigenvalue test confirmatory
- Both tests agree: High confidence
- Tests disagree: Report uncertainty, use additional tests

Error Correction Models:
- ECM coefficient significant: "Long-run relationship exists"
- ECM coefficient magnitude indicates adjustment speed
- R² indicates fit quality
```

### Vector Autoregression (VAR)
```
Granger Causality:
- F-stat > critical value: "Evidence of causality"
- Bidirectional causality: "Integrated markets"
- Unidirectional: "Price transmission exists"
- No causality: "Markets segmented"

Impulse Response Functions:
- Response magnitude > 2 std errors: "Significant response"
- Response duration indicates integration speed
- Asymmetric responses suggest structural differences
```

### Panel Data Models
```
Fixed Effects vs Random Effects:
- Hausman test p < 0.05: Use fixed effects
- Hausman test p ≥ 0.05: Random effects acceptable

Within vs Between R²:
- High within R²: Time series relationships strong
- High between R²: Cross-sectional relationships strong
- Low both: Model may be misspecified
```

## Robustness Check Protocols

### Required Robustness Tests
1. **Alternative Specifications**
   - Include/exclude control variables
   - Different functional forms (log vs level)
   - Various lag structures

2. **Sample Sensitivity**
   - Exclude outliers (>3 standard deviations)
   - Different time periods
   - Balanced vs unbalanced panels

3. **Estimation Methods**
   - OLS vs instrumental variables
   - Fixed vs random effects
   - Robust standard errors

### Robustness Assessment Rules
```
IF results consistent across specifications THEN
   → "Robust finding" - High confidence
   
ELSE IF results vary but same sign/direction THEN
   → "Moderate robustness" - Qualified confidence
   
ELSE IF results fundamentally different THEN
   → "Fragile finding" - Report sensitivity
   → Investigate sources of variation
```

## Null Results Interpretation

### When Null Hypotheses Are Not Rejected
**Template Statement**: "Analysis finds no statistically significant evidence for [HYPOTHESIS] (p = [VALUE], 95% CI: [RANGE]). This null result suggests that [INTERPRETATION]."

**Possible Explanations**:
1. **True null**: Effect genuinely doesn't exist
2. **Insufficient power**: Sample too small to detect effect
3. **Measurement error**: Variables imperfectly measured
4. **Model misspecification**: Wrong functional form
5. **Timing issues**: Effect occurs outside sample period

**Next Steps for Null Results**:
- [ ] Calculate post-hoc statistical power
- [ ] Check for non-linear relationships
- [ ] Investigate measurement issues
- [ ] Consider alternative time periods
- [ ] Report finding to prevent publication bias

## Context-Specific Considerations

### Yemen Market Context
**Expected Patterns**:
- Higher integration within currency zones
- Exchange rate effects stronger post-2020
- Conflict effects may be non-linear
- Seasonal patterns important (Ramadan effects)

**Data Quality Flags**:
- Missing data patterns (conflict-related)
- Currency conversion issues
- Quality variations by zone
- Reporting consistency across sources

### Policy Relevance Thresholds
**High Policy Relevance**: Effects > 20% or statistically strong
**Moderate Relevance**: Effects 5-20% and robust
**Low Relevance**: Effects < 5% or fragile
**No Policy Relevance**: Null results after robustness checks

## Quality Assurance Checklist

### Before Analysis
- [ ] All hypotheses pre-specified
- [ ] Decision thresholds established
- [ ] Alternative explanations prepared
- [ ] Robustness tests planned

### During Analysis
- [ ] Stick to pre-specified tests
- [ ] Document all decisions
- [ ] Run all planned robustness checks
- [ ] Report unexpected findings honestly

### After Analysis
- [ ] Apply decision rules consistently
- [ ] Report confidence levels accurately
- [ ] Acknowledge limitations openly
- [ ] Prepare policy advice conditionally

## Documentation Requirements

### For Each Result
1. **Test performed**: Exact specification used
2. **Statistical output**: Full results, not just p-values
3. **Effect size**: Practical significance assessment
4. **Confidence intervals**: Uncertainty quantification
5. **Robustness**: Sensitivity to specification changes
6. **Interpretation**: Based on pre-specified criteria

### For Unexpected Results
1. **Description**: What was expected vs found
2. **Investigation**: Potential explanations explored
3. **Implications**: How this changes conclusions
4. **Next steps**: Additional tests performed

## Scientific Standards Compliance

### Publication Standards
- Meet requirements for top economics journals
- Sufficient detail for replication
- Honest acknowledgment of limitations
- Appropriate qualification of claims

### World Bank Standards
- Policy relevance clearly established
- Uncertainty appropriately communicated
- Alternative scenarios considered
- Implementation guidance provided

This framework ensures that all results interpretation follows scientific best practices and avoids predetermined conclusions.