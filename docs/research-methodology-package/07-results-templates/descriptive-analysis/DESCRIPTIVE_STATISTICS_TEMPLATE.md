# Descriptive Statistics Analysis Template

## Purpose

This template guides the presentation of descriptive statistics in a way that explores data patterns without imposing predetermined interpretations. Descriptive analysis should reveal what IS in the data, not confirm what we expect.

## Pre-Analysis Setup

### MANDATORY: Currency Validation Protocol
🚨 **FAILURE TO COMPLETE = INVALID ANALYSIS** 🚨
1. [ ] Raw data currency column checked (YER/USD/mixed)
2. [ ] Exchange rate source documented for each market
3. [ ] Conversion date matches price date exactly
4. [ ] Post-conversion reasonability check:
   - Wheat: $0.20-0.80/kg typical range
   - Rice: $0.40-1.20/kg typical range
   - Sugar: $0.30-1.00/kg typical range
5. [ ] NO arithmetic operations before conversion

### Data Quality Assessment
Before calculating any statistics:
- [ ] Missing data patterns documented
- [ ] Outliers identified using objective criteria
- [ ] Currency units verified and consistent
- [ ] Time period coverage checked
- [ ] Sample representativeness assessed

### Variable Definitions
```
Variable Name: [EXACT NAME IN DATASET]
Description: [WHAT IT MEASURES]
Unit: [YER/USD/kg/etc.]
Source: [DATA PROVIDER]
Coverage: [TIME PERIOD AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SCOPE]
Missing: [PERCENTAGE AND PATTERN]
```

## Summary Statistics Framework

### Table 1: Basic Summary Statistics
```
Variable         | N    | Mean | SD   | Min  | Max  | p25  | p50  | p75  | CV   
-----------------|------|------|------|------|------|------|------|------|------
[VARIABLE_NAME]  | [N]  | [X]  | [X]  | [X]  | [X]  | [X]  | [X]  | [X]  | [X]
[VARIABLE_NAME]  | [N]  | [X]  | [X]  | [X]  | [X]  | [X]  | [X]  | [X]  | [X]

Notes: 
- All prices converted to [CURRENCY] using [METHOD]
- Statistics weighted by [WEIGHT] if applicable
- Missing values handled by [METHOD]
```

### Interpretation Guidelines
- **Coefficient of Variation (CV)**:
  - CV < 0.5: Low variability
  - 0.5 ≤ CV < 1.0: Moderate variability  
  - CV ≥ 1.0: High variability
  
- **Skewness Assessment**:
  - Compare mean vs median
  - If mean >> median: Right-skewed (common for prices)
  - If mean << median: Left-skewed (investigate why)

## Distributional Analysis

### Histogram Requirements
For each key variable, create histogram showing:
- [ ] Actual distribution shape
- [ ] Normal distribution overlay for comparison
- [ ] Kernel density estimate
- [ ] Clear axis labels with units

### Outlier Investigation Protocol
```
IF value > p75 + 1.5*IQR OR value < p25 - 1.5*IQR THEN
  → Flag as potential outlier
  → Investigate context (conflict event? data error?)
  → Document decision to include/exclude
  → Report results with and without outliers
```

## Temporal Patterns

### Table 2: Statistics by Time Period
```
Period    | Mean | SD   | Min  | Max  | N    | Missing% | Events
----------|------|------|------|------|------|----------|--------
[YYYY-MM] | [X]  | [X]  | [X]  | [X]  | [X]  | [X]      | [NOTE]
[YYYY-MM] | [X]  | [X]  | [X]  | [X]  | [X]  | [X]      | [NOTE]

Events: Notable occurrences affecting data (holidays, conflicts, policy changes)
```

### Trend Assessment
- **DO**: "Prices increased by X% over the period"
- **DON'T**: "Prices increased as expected due to conflict"
- **DO**: "Volatility highest in months A, B, C"
- **DON'T**: "Volatility confirms our market fragmentation hypothesis"

## Spatial Patterns

### Table 3: Statistics by Geographic Zone
```
Zone/Region      | Markets | Mean | SD   | CV   | Min  | Max  | Missing%
-----------------|---------|------|------|------|------|------|----------
[ZONE_NAME]      | [N]     | [X]  | [X]  | [X]  | [X]  | [X]  | [X]
[ZONE_NAME]      | [N]     | [X]  | [X]  | [X]  | [X]  | [X]  | [X]

Geographic definitions: [SOURCE OF ZONE BOUNDARIES]
```

### Comparison Protocol
When comparing zones:
1. State objective differences: "Zone A mean is X% higher than Zone B"
2. Note statistical significance: "Difference is/isn't statistically significant (p = X)"
3. Avoid causal claims: NOT "This proves Zone A has market power"
4. Consider composition: "Zone A has more urban markets which may affect comparison"

## Cross-Commodity Analysis

### Table 4: Statistics by Commodity Type
```
Commodity        | Unit    | Mean | CV   | Import% | Observations
-----------------|---------|------|------|---------|---------------
[COMMODITY]      | [kg/L]  | [X]  | [X]  | [X]     | [N]
[COMMODITY]      | [kg/L]  | [X]  | [X]  | [X]     | [N]

Import%: Percentage typically imported vs locally produced
```

### Commodity Grouping Logic
Group by objective characteristics:
- Importables vs local products
- Perishables vs non-perishables  
- Essentials vs non-essentials
- Weight/volume ratios

NOT by expected behavior or hypothesis support.

## Correlation Analysis

### Table 5: Pairwise Correlations
```
           | Var1 | Var2 | Var3 | Var4
-----------|------|------|------|------
Variable 1 | 1.00 |      |      |
Variable 2 | [ρ]  | 1.00 |      |
Variable 3 | [ρ]  | [ρ]  | 1.00 |
Variable 4 | [ρ]  | [ρ]  | [ρ]  | 1.00

Note: Correlations > 0.7 or < -0.7 in bold
Significance: * p<0.05, ** p<0.01, *** p<0.001
```

### Correlation Interpretation Rules
- Report what IS: "Variables X and Y show correlation of 0.75"
- Avoid causation: NOT "X drives Y" 
- Consider confounders: "Both may be influenced by Z"
- Check robustness: "Correlation is 0.75 in full sample, 0.72 excluding outliers"

## Missing Data Analysis

### Table 6: Missing Data Patterns
```
Variable    | Total Obs | Missing N | Missing % | Pattern
------------|-----------|-----------|-----------|------------------
[VAR_NAME]  | [N]       | [N]       | [%]       | [Random/Systematic]
[VAR_NAME]  | [N]       | [N]       | [%]       | [Random/Systematic]

Pattern descriptions:
- Random: No clear pattern to missingness
- Systematic: Missing correlated with [VARIABLE/EVENT]
```

### Missing Data Decision Tree
```
IF missing < 5% AND random THEN
  → Complete case analysis acceptable
  
ELSE IF missing < 20% AND systematic THEN
  → Investigate mechanism
  → Consider imputation with documentation
  
ELSE IF missing > 20% THEN
  → Serious limitation
  → Report subset analyses
  → Acknowledge uncertainty
```

## Quality Checks

### Internal Consistency
- [ ] Prices in same market for same good vary < 10% within day
- [ ] No negative prices or quantities
- [ ] Units consistent across observations
- [ ] Temporal sequence logical (no future dates)

### External Validity
- [ ] Compare with other sources where available
- [ ] Check against known events (Ramadan prices, harvest seasons)
- [ ] Verify extreme values have explanations
- [ ] Cross-reference with qualitative reports

## Reporting Standards

### Good Descriptive Text Examples

✅ **GOOD**: "Wheat prices ranged from 150 to 890 YER/kg with mean 425 and standard deviation 156. The coefficient of variation (0.37) indicates moderate price variability across markets and time periods."

❌ **BAD**: "Wheat prices showed the expected pattern of fragmentation with clear evidence of market breakdown in conflict areas."

✅ **GOOD**: "Missing data accounts for 23% of potential observations, with higher rates in eastern governorates (45%) compared to western (12%). Missingness appears correlated with conflict events (ρ = 0.34)."

❌ **BAD**: "Data gaps confirm that conflict disrupts market reporting as our theory predicted."

### Visualization Requirements

All descriptive plots must:
- Show actual data, not smoothed/fitted values
- Include confidence intervals where appropriate
- Label axes clearly with units
- Note sample size and exclusions
- Use consistent scales for comparison

## Final Checklist

Before finalizing descriptive analysis:
- [ ] All statistics based on verified, clean data
- [ ] Currency conversions explicitly documented
- [ ] Missing data patterns analyzed and reported
- [ ] Outliers investigated and decisions documented
- [ ] No causal language in descriptive text
- [ ] Tables formatted consistently
- [ ] Visualizations meet standards
- [ ] External validity checks performed

## Remember

Descriptive statistics should:
- **Describe** what is in the data
- **Explore** patterns without prejudice
- **Document** data quality issues
- **Prepare** for inferential analysis

They should NOT:
- Confirm hypotheses
- Make causal claims
- Hide inconvenient patterns
- Overinterpret noise

The goal is to understand your data deeply before testing hypotheses.