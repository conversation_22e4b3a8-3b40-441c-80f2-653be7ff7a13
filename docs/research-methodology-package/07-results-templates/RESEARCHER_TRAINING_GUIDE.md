# Researcher Training Guide: Scientific Integrity in Practice

## Welcome to Evidence-Based Research

This guide teaches the essential mindset and practices for conducting research that discovers truth rather than confirms beliefs.

## 🧠 The Right Mindset

### From Confirmation to Discovery
**Old Mindset**: "How can I prove my hypothesis?"  
**New Mindset**: "What does the data actually show?"

**Old Mindset**: "This surprising result must be wrong."  
**New Mindset**: "This surprising result is interesting - let me investigate."

**Old Mindset**: "I need significant results to publish."  
**New Mindset**: "I need to report what I found, whether significant or not."

### Embrace Uncertainty
- Your job is to **quantify uncertainty**, not eliminate it
- Confidence intervals are more informative than p-values
- "I don't know" is a valid and valuable scientific statement
- Null results prevent bad policy decisions

## 📚 Essential Training Modules

### Module 1: Currency Conversion Fundamentals (30 minutes)

**The Critical Lesson**: Early analysis in this project failed because researchers compared YER prices in one region to USD prices in another, creating spurious patterns.

**Practice Exercise**:
```
Given:
- Market A: [LOCAL_PRICE] [LOCAL_CURRENCY]/kg wheat, exchange rate [RATE_A] YER/USD
- Market B: [USD_PRICE] USD/kg wheat, exchange rate [RATE_B] YER/USD

Question: Which market has higher prices?

Wrong approach: Compare [LOCAL_PRICE] vs [USD_PRICE] → Incorrect comparison
Right approach: Convert both to USD
- Market A: [LOCAL_PRICE] ÷ [RATE_A] = [CONVERTED_PRICE_A] USD/kg  
- Market B: [USD_PRICE] USD/kg
→ True price comparison revealed
```

**Key Takeaway**: ALWAYS convert to common currency before any price analysis.

### Module 2: Template Usage (45 minutes)

**Core Principle**: Templates guide discovery, they don't predetermine outcomes.

**Before/After Example**:

❌ **Bad Template Usage**:
"Yemen's revolutionary currency fragmentation creates a 4x price differential, confirming our hypothesis about conflict economics."

✅ **Good Template Usage**:
"Exchange rate differential magnitude: [TO BE CALCULATED FROM DATA]
IF differential > 100% THEN → 'Severe fragmentation'
ELSE IF differential 20-100% THEN → 'Moderate fragmentation'"

**Practice Exercise**: Take a predetermined conclusion and convert it to a proper template with placeholders and decision rules.

### Module 3: Null Results Training (60 minutes)

**Core Principle**: Null results are scientific findings, not failures.

**Example Null Result Investigation**:
```
Finding: No significant effect of conflict on wheat prices (p = 0.73)

Step 1: Check statistical power
→ Power = 0.89 for medium effects ✓

Step 2: Test alternative specifications  
→ All specifications confirm null ✓

Step 3: Investigate explanations
→ Markets may have adapted to chronic conflict

Step 4: Extract value
→ "Surprising resilience suggests adaptive mechanisms"

Step 5: Policy implications
→ "Focus interventions on factors other than violence"
```

**Practice Exercise**: Take a non-significant result and work through the full investigation protocol.

### Module 4: Pre-Analysis Registration (30 minutes)

**Core Principle**: Lock in your analysis plan before seeing data.

**Registration Components**:
1. **Exact hypotheses** with directional predictions
2. **Statistical tests** to be used
3. **Interpretation thresholds** (what constitutes "large effect"?)
4. **Robustness checks** to be performed
5. **Multiple testing corrections** if applicable

**Practice Exercise**: Register an analysis plan for a simple research question using the provided template.

## 🎯 Practical Exercises

### Exercise 1: Spot the Bias
Review these statements and identify problems:

1. "As expected, our data confirms the theoretical predictions."
2. "This groundbreaking discovery revolutionizes conflict economics."
3. "Results prove that aid effectiveness depends on currency alignment."
4. "The significant p-value validates our methodological approach."

**Answers**: All contain predetermined language or overstatement.

### Exercise 2: Rewrite for Integrity
Convert these biased statements to objective ones:

1. "Revolutionary finding: Markets are integrated" → "Analysis suggests market integration (β = 0.45, 95% CI: [0.23, 0.67])"

2. "As predicted, conflict increases prices" → "Conflict events associated with higher prices (β = 0.15, p = 0.03)"

### Exercise 3: Currency Conversion Practice
Given mixed currency data, practice the conversion protocol:
1. Identify currency for each observation
2. Match exchange rates to dates
3. Convert to common currency
4. Verify reasonableness
5. Document all decisions

## 🛠️ Tools and Resources

### Automated Validation
```bash
# Check for predetermined language
python src/core/validation/methodology_validator.py your_analysis.md

# Test template integrity  
python -c "from src.core.reporting import ResultsTemplateGenerator; 
generator = ResultsTemplateGenerator();
print('✅ Pass' if generator.validate_template_integrity(open('your_file.md').read()) else '❌ Fail')"
```

### Template Library
- `exchange-rate-impact.md` - For currency analysis
- `wheat-analysis.md` - For commodity analysis (any commodity)
- `DESCRIPTIVE_STATISTICS_TEMPLATE.md` - For data exploration
- `POLICY_BRIEF_TEMPLATE.md` - For policy communication

### Decision Support
- `RESULTS_DECISION_FRAMEWORK.md` - Pre-specified interpretation rules
- `NULL_RESULTS_TEMPLATE.md` - How to handle non-significant findings
- `METHODOLOGICAL_INTEGRITY_CHECKLIST.md` - Final quality check

## 🚩 Common Mistakes and How to Avoid Them

### Mistake 1: Starting Analysis Before Currency Check
**Problem**: Comparing apples to oranges
**Solution**: Complete currency checklist FIRST
**Check**: "Did I verify all prices are in the same currency?"

### Mistake 2: Writing Conclusions Before Analysis
**Problem**: Predetermined results bias
**Solution**: Use templates with placeholders
**Check**: "Could my template be filled with opposite results?"

### Mistake 3: Hiding Null Results
**Problem**: Publication bias, false confidence
**Solution**: Report everything you planned to test
**Check**: "Am I showing all pre-specified analyses?"

### Mistake 4: Overstating Findings
**Problem**: Claims not supported by evidence
**Solution**: Match language to statistical strength
**Check**: "Do my conclusions match my confidence intervals?"

### Mistake 5: Ignoring Uncertainty
**Problem**: False precision, overconfidence
**Solution**: Always report confidence intervals
**Check**: "Have I quantified uncertainty appropriately?"

## 📊 Quality Metrics

### How to Know You're Doing Well
- You're excited about null results (they're informative!)
- You report confidence intervals routinely
- You pre-register analysis plans
- You worry more about false positives than false negatives
- You update beliefs based on evidence

### Warning Signs
- All your results are "just significant"
- You keep changing specifications until significant
- You hide non-significant findings
- You claim to "prove" relationships
- You're disappointed by null results

## 🎓 Certification Process

### Self-Assessment (Required)
Complete these tasks to demonstrate competency:

1. **Currency Exercise**: Successfully convert mixed-currency price data
2. **Template Exercise**: Fill a template with hypothetical results
3. **Null Result Exercise**: Investigate a non-significant finding
4. **Registration Exercise**: Pre-register an analysis plan
5. **Language Exercise**: Rewrite biased text objectively

### Peer Review (Recommended)
Have a colleague review:
- Your filled templates
- Your pre-analysis registration
- Your results interpretation
- Your uncertainty quantification

### Ongoing Learning
- Review template examples regularly
- Study high-quality null result reports
- Practice with methodology validator
- Stay updated on template improvements

## 📞 Getting Help

### When Stuck
1. **Check examples** in `examples/` directory
2. **Review decision framework** for interpretation rules
3. **Run automated validation** to catch errors
4. **Consult transformation record** for lessons learned

### For Complex Cases
- Document your specific situation
- Apply general principles (objectivity, uncertainty, transparency)
- When in doubt, be more conservative
- Ask "Would this pass peer review?"

## 🏆 Success Stories

### From Predetermined to Discovery
**Before**: "Yemen's currency fragmentation creates market segmentation as our theory predicts."
**After**: "Analysis finds evidence consistent with currency-driven market segmentation (p = 0.02, effect size = 0.34), though alternative explanations remain possible."

### From Hidden to Highlighted Nulls
**Before**: [Null result buried in appendix]
**After**: "Despite theoretical predictions, we find no evidence that conflict affects wheat prices (p = 0.73, 95% CI: [-0.05, 0.07]). This unexpected resilience suggests markets have adapted to chronic instability."

## 🔄 Continuous Improvement

### Template Evolution
Templates improve based on:
- User feedback on unclear sections
- New methodological insights
- Lessons from errors
- Best practice updates

### Personal Growth
Track your progress:
- Do you pre-register analyses?
- Do you report null results prominently?
- Do you quantify uncertainty appropriately?
- Have you stopped using predetermined language?

---

**Remember**: Good methodology is like good medicine - it sometimes tells you what you don't want to hear, but it's always better to know the truth.

The goal is not to confirm our beliefs, but to discover reality - whatever form it takes.