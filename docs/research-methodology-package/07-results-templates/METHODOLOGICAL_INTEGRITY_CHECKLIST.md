# Methodological Integrity Checklist

## Purpose

This checklist ensures that all research outputs maintain the highest standards of scientific integrity. Complete ALL sections before publishing, presenting, or sharing any analysis results.

## ✅ Pre-Analysis Integrity Check

### Research Design
- [ ] **Pre-analysis plan registered** before seeing data
- [ ] **Hypotheses pre-specified** with directional predictions (if any)
- [ ] **Analysis methods committed** to before data access
- [ ] **Sample size determined** based on power analysis
- [ ] **Multiple testing corrections** planned if applicable

### Currency and Data Validation (CRITICAL)
- [ ] **Currency units verified** for all price variables
- [ ] **Exchange rates obtained** for correct dates and locations
- [ ] **Conversion completed** before any price analysis
- [ ] **Reasonableness checked** (converted prices within expected ranges)
- [ ] **Missing data patterns** documented and addressed
- [ ] **Data quality flags** reviewed and justified

## ✅ Analysis Integrity Check

### Statistical Standards
- [ ] **All pre-specified tests** completed regardless of results
- [ ] **Effect sizes calculated** with confidence intervals
- [ ] **Robustness checks** completed across specifications
- [ ] **Assumption testing** performed and documented
- [ ] **Power analysis** conducted for null results

### Methodological Rigor
- [ ] **No data snooping** - analysis plan followed exactly
- [ ] **Deviations documented** with clear justification
- [ ] **Alternative explanations** considered systematically
- [ ] **Outliers handled** consistently and transparently
- [ ] **Missing data** addressed appropriately

## ✅ Results Reporting Integrity

### Language Standards
- [ ] **No predetermined conclusions** stated as fact
- [ ] **Uncertainty quantified** appropriately (CIs, p-values)
- [ ] **Causal language avoided** unless strong identification
- [ ] **Null results valued** equally with significant findings
- [ ] **Limitations acknowledged** honestly and prominently

### Forbidden Language Check
❌ **NEVER USE**:
- "Revolutionary discovery"
- "Game-changing findings"
- "Proves that..." 
- "Confirms our hypothesis"
- "As expected..."
- "Obviously..." / "Clearly..."
- "Without question..."

✅ **USE INSTEAD**:
- "Analysis suggests..."
- "Evidence indicates..."
- "Results are consistent with..."
- "Data show..."
- "Findings suggest..."

### Required Elements
- [ ] **Confidence intervals** reported for all estimates
- [ ] **P-values** exact values, not just significance stars
- [ ] **Sample sizes** clearly stated
- [ ] **Missing data** percentages reported
- [ ] **Robustness** sensitivity documented

## ✅ Template Compliance Check

### Template Usage
- [ ] **Appropriate template** selected for analysis type
- [ ] **Placeholders filled** with actual results (not predetermined)
- [ ] **Decision trees applied** as specified in framework
- [ ] **Currency checklist** completed for price analyses
- [ ] **Examples reviewed** for proper usage patterns

### Template Elements Present
- [ ] **Executive summary** with [TO BE DETERMINED] replaced
- [ ] **Background** without predetermined assumptions
- [ ] **Methods** clearly specified and followed
- [ ] **Results** reported objectively
- [ ] **Policy implications** conditional on findings

## ✅ Scientific Integrity Standards

### Transparency Requirements
- [ ] **All data sources** clearly documented
- [ ] **All methods** replicable by others
- [ ] **All code** available and documented
- [ ] **All decisions** justified and recorded
- [ ] **All conflicts of interest** disclosed

### Reproducibility Standards
- [ ] **Version control** used for all files
- [ ] **Random seeds** set for reproducible results
- [ ] **Software versions** documented
- [ ] **Hardware specifications** noted if relevant
- [ ] **Replication package** prepared

## ✅ Peer Review Readiness

### Academic Standards
- [ ] **Literature review** comprehensive and balanced
- [ ] **Methodology** follows disciplinary best practices
- [ ] **Results** presented with appropriate detail
- [ ] **Discussion** acknowledges limitations honestly
- [ ] **Conclusions** supported by evidence presented

### World Bank Standards (if applicable)
- [ ] **Policy relevance** clearly established
- [ ] **Operational guidance** provided where appropriate
- [ ] **Cost-benefit considerations** addressed
- [ ] **Implementation feasibility** discussed
- [ ] **Risk assessment** included

## ✅ Final Quality Assurance

### Internal Review
- [ ] **Self-audit** completed using this checklist
- [ ] **Colleague review** obtained from independent researcher
- [ ] **Methodology validation** run and passed
- [ ] **Template integrity** verified
- [ ] **Language audit** completed for bias

### External Validation
- [ ] **Independent replication** possible with provided materials
- [ ] **Alternative explanations** addressed satisfactorily
- [ ] **Sensitivity analysis** supports main conclusions
- [ ] **Cross-validation** performed where feasible
- [ ] **Out-of-sample testing** conducted if appropriate

## ✅ Publication Checklist

### Before Submission
- [ ] **All co-authors** have reviewed and approved
- [ ] **Institutional approvals** obtained if required
- [ ] **Ethical clearances** secured if applicable
- [ ] **Data permissions** verified for sharing
- [ ] **Acknowledgments** complete and accurate

### Submission Materials
- [ ] **Main manuscript** follows journal guidelines
- [ ] **Supplementary materials** complete
- [ ] **Replication files** organized and documented
- [ ] **Data files** prepared (or access instructions)
- [ ] **Code files** cleaned and commented

## 🚨 Red Flags - STOP if Any Present

### Methodological Red Flags
- Results change dramatically with small specification changes
- All tests happen to be "just significant" (p-hacking signs)
- Analysis plan changed after seeing results
- Outliers removed without clear criteria
- Missing data ignored without justification

### Language Red Flags
- Claims of "proving" relationships
- Predetermined conclusions stated as discoveries
- Causal language without proper identification
- Null results hidden or downplayed
- Uncertainty not appropriately communicated

### Data Red Flags
- Currency units mixed without conversion
- Exchange rates not verified
- Missing data patterns not random
- Sample selection not justified
- Quality issues not addressed

## 📋 Sign-Off Section

### Principal Investigator Certification
I certify that this research meets all methodological integrity standards:

**Name**: _______________  
**Date**: _______________  
**Signature**: _______________

### Co-Author Certifications
Each co-author must certify they have reviewed and approve:

**Co-Author 1**: _______________ Date: _______  
**Co-Author 2**: _______________ Date: _______  
**Co-Author 3**: _______________ Date: _______  

### Methodological Review
Independent methodological review completed by:

**Reviewer**: _______________  
**Date**: _______________  
**Status**: [ ] Approved [ ] Requires revision [ ] Rejected

## 📚 Supporting Resources

### For Detailed Guidance See:
- `TEMPLATE_USAGE_GUIDE.md` - How to use templates properly
- `RESULTS_DECISION_FRAMEWORK.md` - Pre-specified interpretation rules
- `NULL_RESULTS_TEMPLATE.md` - How to report non-significant findings
- `PRE_ANALYSIS_REGISTRATION.md` - How to lock in your analysis plan

### For Code Validation:
```bash
# Run methodology validator
python src/core/validation/methodology_validator.py docs/research-methodology-package

# Test template integrity
python -c "from src.core.reporting import ResultsTemplateGenerator; print('✅ Pass' if ResultsTemplateGenerator().validate_template_integrity(open('your_file.md').read()) else '❌ Fail')"
```

### For Questions or Issues:
- Review template examples in `examples/` directory
- Check version control documentation
- Consult transformation record for lessons learned

---

**Remember**: Scientific integrity is not optional. These standards protect both the research community and the populations we aim to serve through evidence-based policy.