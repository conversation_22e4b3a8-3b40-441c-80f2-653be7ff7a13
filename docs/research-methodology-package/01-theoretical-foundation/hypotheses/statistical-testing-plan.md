# Statistical Testing Plan: Multiple Testing and Power Analysis

## Overview

This document outlines the statistical testing framework for the Yemen Market Integration research, addressing multiple testing corrections, power analysis, and sequential testing protocols to ensure rigorous hypothesis evaluation.

## Multiple Testing Problem

### Challenge

With 13 hypotheses (H1-H10, S1, N1, P1), the probability of at least one Type I error under the global null hypothesis is:

```
P(at least one Type I error) = 1 - (1 - α)^13 ≈ 1 - (0.95)^13 = 0.487
```

### Solution Framework

Implement hierarchical testing with different correction levels based on hypothesis importance and exploratory nature.

## Testing Hierarchy and Corrections

### Tier 1: Primary Hypotheses (H1-H5)

**Objective**: Control familywise error rate (FWER) at α = 0.05

**Method**: Bonferroni correction

- Individual test level: α/5 = 0.01
- Global Type I error: ≤ 0.05

**Rationale**: Primary hypotheses are central to research question and require strongest statistical evidence.

**Testing Sequence**:

1. H1: Exchange Rate and Price Differentials (α = 0.01)
2. H4: Cross-Border Arbitrage Mechanisms (α = 0.01)
3. H5: Long-run Price Convergence (α = 0.01)
4. H2: Humanitarian Aid Price Effects (α = 0.01)
5. H3: Conflict Effects on Commodity Markets (α = 0.01)

### Tier 2: Secondary Hypotheses (H6-H10)

**Objective**: Control false discovery rate (FDR) at q = 0.10

**Method**: Benjamini-Hochberg procedure

- More power than Bonferroni for exploratory analysis
- Expected proportion of false discoveries ≤ 10%

**Testing Protocol**:

1. Order p-values: p₁ ≤ p₂ ≤ ... ≤ p₅
2. Find largest k such that p_k ≤ (k/5) × 0.10
3. Reject hypotheses 1, 2, ..., k

### Tier 3: Methodological Hypotheses (S1, N1, P1)

**Objective**: Model specification guidance

**Method**: Unadjusted tests at α = 0.15

- Results inform model choice rather than substantive conclusions
- Higher Type I error acceptable for methodological insights

## Power Analysis Framework

### Minimum Detectable Effect Sizes

#### H1: Exchange Rate and Price Differentials

- **Effect size**: 10% price differential between zones
- **Statistical test**: Panel regression with fixed effects
- **Power calculation**:

  ```
  n = 2 × (z_{α/2} + z_β)² × σ² / δ²
  n = 2 × (2.576 + 0.842)² × (0.3)² / (0.1)² ≈ 315 markets per zone
  ```

- **Current sample**: 400+ markets (adequate)

#### H2: Humanitarian Aid Price Effects

- **Effect size**: 5% price effect from aid (either direction)
- **Statistical test**: IV regression with clustered standard errors
- **Design effect**: 2.0 (clustering adjustment)
- **Required observations**: 1,200+ market-month combinations
- **Current sample**: 8,000+ observations (adequate)

#### H4: Cross-Border Arbitrage

- **Effect size**: Coefficient significantly different from zero (any relationship)
- **Detectable range**: Effect size of 0.2 standard deviations
- **Required market pairs**: 150+
- **Current sample**: 300+ pairs (adequate)

#### H5: Long-run Price Convergence

- **Effect size**: Cointegration relationship vs. no cointegration
- **Time series length**: 36+ months required
- **Cross-sectional units**: 50+ markets per currency zone
- **Current sample**: 48 months, 200+ markets (adequate)

#### H3: Conflict Effects

- **Effect size**: To be determined based on conflict intensity variation
- **Statistical test**: Panel regression with log prices
- **Required observations**: Depends on within-market conflict variation
- **Power consideration**: Requires sufficient temporal variation in conflict

#### Secondary Hypotheses (H6-H10)

- **Note**: Power calculations deferred pending data availability
- **General principle**: Minimum 80% power to detect medium effect sizes (Cohen's d = 0.5)
- **Sample requirements**: Will be determined based on pilot data analysis

#### Methodological Hypotheses (S1, N1, P1)

- **Purpose**: Model specification guidance rather than hypothesis testing
- **Power considerations**: Less stringent given exploratory nature
- **Effect sizes**: To be determined empirically

### Sample Size Adequacy Assessment

**Current Data Characteristics**:

- Markets: 400+ across both currency zones
- Time periods: 48 months (2019-2023)
- Commodities: 25 major food items
- Observations: 180,000+ market-commodity-month combinations

**Adequacy by Hypothesis Type**:

- ✅ Adequate: H1, H2, H4, H5, S1
- ⚠️ Marginal: H3 (conflict intensity data incomplete)
- ❌ Insufficient: H6-H10, N1, P1 (require additional data)

## Sequential Testing Protocol

### Phase 1: Immediate Implementation (Month 1-2)

**Hypotheses**: H1, H4, H5, S1
**Rationale**: Available data, high power, core research questions

**Analysis Protocol**:

- All hypotheses tested according to pre-specified statistical plan
- Results interpreted based on statistical evidence, not theoretical preconceptions
- No predetermined decision rules based on hypothesis test outcomes

### Phase 2: Primary Hypotheses with Additional Data (Month 1-3)

**Hypotheses**: H2, H3 (as soon as data available)
**Rationale**: Primary hypotheses tested immediately when data permits
**Requirements**: OCHA 3W data, conflict intensity data

### Phase 3: Secondary Hypotheses (Month 3-6)

**Hypotheses**: H6, H7, H8
**Requirements**: Control change timeline, price denomination data, detailed aid records

**Go/No-Go Criteria**:

- Aid data coverage: ≥70% of market-months
- Conflict intensity data: ≥50% of events covered
- Control changes: ≥10 documented switches

### Phase 4: Advanced Analysis (Month 7-12)

**Hypotheses**: H9, H10, N1, P1
**Requirements**: Extended data collection, high-frequency information

**Implementation Requirements**:

- All phases implemented according to data availability, not results
- Minimum 24 months additional data required for advanced methods

## Statistical Specifications

### Primary Hypothesis Tests

#### H1: Price Differential Comparison Test

```stata
* Create price differentials
sort market_pair commodity month
gen price_diff_yer = abs(price_yer_i - price_yer_j)
gen price_usd_i = price_yer_i / exchange_rate_i  
gen price_usd_j = price_yer_j / exchange_rate_j
gen price_diff_usd = abs(price_usd_i - price_usd_j)

* Test if currency conversion reduces differentials
reg price_diff_yer i.commodity##i.month, absorb(market_pair) cluster(market_pair)
predict diff_yer_resid, residuals
reg price_diff_usd i.commodity##i.month, absorb(market_pair) cluster(market_pair)  
predict diff_usd_resid, residuals

* Test if residuals differ significantly
ttest diff_yer_resid == diff_usd_resid
```

#### H2: Aid Effect (IV Approach)

```stata
* First stage
reghdfe aid_intensity aid_planned_lag, ///
        absorb(commodity_month market) ///
        cluster(market)

* Weak instrument test
estat firststage

* Second stage
ivreghdfe price_local (aid_intensity = aid_planned_lag), ///
          absorb(commodity_month market) ///
          cluster(market)
```

#### H4: Arbitrage Test

```stata
* Sample restriction
* Note: distance measured in kilometers
keep if tradeable == 1 & distance_km < 500

* Main regression
reg price_diff transport_cost exchange_differential, ///
    cluster(market_pair)

* Test for any relationship (primary)
test transport_cost = exchange_differential = 0
* If relationship exists, examine magnitude
* Generate confidence intervals for coefficients
estat vce
margins, dydx(transport_cost exchange_differential)
```

### Robustness Testing Requirements

**Alternative Specifications** (minimum 3 per hypothesis):

1. Different clustering schemes (market vs. governorate vs. time)
2. Alternative fixed effects structures
3. Various outlier treatment methods
4. Sensitivity to sample restrictions

**Diagnostic Tests**:

- Weak instrument detection (F-statistic > 10)
- Overidentification tests where applicable
- Heteroskedasticity-robust inference
- Spatial correlation tests for geographic data

## False Discovery Rate Control

### Benjamini-Hochberg Implementation

```stata
* Collect all p-values from secondary hypotheses
matrix pvals = J(1,5,.)
matrix pvals[1,1] = r(p_h6)
matrix pvals[1,2] = r(p_h7)
matrix pvals[1,3] = r(p_h8)
matrix pvals[1,4] = r(p_h9)
matrix pvals[1,5] = r(p_h10)

* Sort p-values and apply BH procedure
preserve
clear
svmat pvals
gen rank = _n
sort pvals1
gen bh_critical = (rank/5)*0.10
gen reject = (pvals1 <= bh_critical)
* Find largest k where p_k <= critical value
sum rank if reject==1
local max_reject = r(max)
restore

* Alternative: Use -multproc- or -qqvalue- if installed
```

### Reporting Standards

**Required Reporting Elements**:

1. Raw p-values for all tests
2. Adjusted p-values by correction method
3. Confidence intervals (adjusted when appropriate)
4. Effect sizes with practical significance assessment
5. Power calculations for non-significant results

**Multiple Testing Summary Table**:

```
Hypothesis | Raw p-value | Adjusted p-value | Decision | Effect Size | 95% CI
H1         | 0.XXX       | 0.XXX           | [TBD]    | [TBD]       | [TBD]
H2         | 0.XXX       | 0.XXX           | [TBD]    | [TBD]       | [TBD]
...
```

## Type S and Type M Error Considerations

### Type S Error (Sign Error)

**Risk**: Concluding effect in wrong direction when true effect is small
**Mitigation**:

- Report confidence intervals, not just significance
- Emphasize effect size interpretation
- Use directional tests only when theoretically justified

### Type M Error (Magnitude Error)

**Risk**: Overestimating effect size when underpowered
**Mitigation**:

- Pre-register effect sizes of practical importance
- Report statistical vs. practical significance
- Use confidence intervals to assess precision

## Implementation Timeline

**Month 1**: Phase 1 hypothesis testing (H1, H4, H5, S1)
**Month 2**: Robustness analysis and sensitivity testing
**Month 3-6**: Data collection for Phase 2 hypotheses
**Month 7**: Phase 2 testing with multiple testing corrections
**Month 8-12**: Phase 3 implementation
**Month 12**: Final multiple testing analysis and reporting

## Quality Assurance Checklist

- [ ] Power analysis completed for all primary hypotheses
- [ ] Multiple testing corrections specified before analysis
- [ ] Pre-registered effect sizes of practical importance
- [ ] Robust standard error methods specified
- [ ] Alternative specifications defined
- [ ] Diagnostic test procedures outlined
- [ ] Reporting standards documented
- [ ] Type S/M error mitigation strategies implemented
