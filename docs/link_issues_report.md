# Link Issues Report

*Generated: 2025-06-03 17:41:00*

Found 70 link issues.

## Issues by Type

### Broken (70 issues)

- `research-methodology-package/02-data-infrastructure/quality-assurance/missing-data-methodology.md:272`
  - Link: `[Quality Control Procedures](../sources/quality-control.md)`
- `research-methodology-package/02-data-infrastructure/quality-assurance/missing-data-methodology.md:273`
  - Link: `[Python Implementation](../../04-implementation/code-examples/missing-data-implementations.py)`
- `research-methodology-package/02-data-infrastructure/quality-assurance/missing-data-methodology.md:274`
  - Link: `[Advanced Robustness Checks](../../03-methodology/robustness/advanced-robustness-checks.md)`
- `research-methodology-package/02-data-infrastructure/transformation-procedures/missing-data.md:1013`
  - Link: `[Robustness Checks](../statistical-tests/robustness-checks.md)`
- `research-methodology-package/02-data-infrastructure/transformation-procedures/missing-data.md:1014`
  - Link: `[API Reference: Panel Builder](../../03-api-reference/data/panel_builder.md)`
- `research-methodology-package/02-data-infrastructure/transformation-procedures/outlier-detection.md:942`
  - Link: `[Robustness Checks](../statistical-tests/robustness-checks.md)`
- `research-methodology-package/02-data-infrastructure/transformation-procedures/outlier-detection.md:943`
  - Link: `[API Reference: Panel Builder](../../03-api-reference/data/panel_builder.md)`
- `research-methodology-package/02-data-infrastructure/transformation-procedures/panel-construction.md:858`
  - Link: `[Panel Models](../econometric-models/panel-models.md)`
- `research-methodology-package/02-data-infrastructure/transformation-procedures/panel-construction.md:859`
  - Link: `[API Reference: Panel Builder](../../03-api-reference/data/panel_builder.md)`
- `research-methodology-package/02-data-infrastructure/transformation-procedures/spatial-matching.md:1274`
  - Link: `[Conflict Data Processing](../../03-api-reference/data/acled_processor.md)`
- `research-methodology-package/02-data-infrastructure/transformation-procedures/spatial-matching.md:1275`
  - Link: `[API Reference: Spatial Joins](../../03-api-reference/data/spatial_joiner.md)`
- `research-methodology-package/03-econometric-methodology/core-methods/cointegration.md:883`
  - Link: `[Unit Root Tests](../statistical-tests/unit-root-tests.md)`
- `research-methodology-package/03-econometric-methodology/core-methods/cointegration.md:884`
  - Link: `[API Reference: Cointegration](../../03-api-reference/models/three_tier/tier2_commodity.md)`
- `research-methodology-package/03-econometric-methodology/core-methods/panel-models.md:614`
  - Link: `[Diagnostic Tests](../statistical-tests/diagnostic-tests.md)`
- `research-methodology-package/03-econometric-methodology/core-methods/panel-models.md:615`
  - Link: `[API Reference: PooledPanelModel](../../03-api-reference/models/three_tier/tier1_pooled.md)`
- `research-methodology-package/03-econometric-methodology/core-methods/threshold-models.md:1095`
  - Link: `[Advanced Robustness Checks](../robustness/advanced-robustness-checks.md)`
- `research-methodology-package/03-econometric-methodology/core-methods/threshold-models.md:1099`
  - Link: `[Python Implementation](../../04-implementation/code-examples/threshold-model-extensions.py)`
- `research-methodology-package/03-econometric-methodology/core-methods/threshold-models.md:1100`
  - Link: `[API Reference: ThresholdVECM](../../03-api-reference/models/three_tier/tier2_commodity.md)`
- `research-methodology-package/03-econometric-methodology/core-methods/time-series.md:857`
  - Link: `[Unit Root Tests](../statistical-tests/unit-root-tests.md)`
- `research-methodology-package/03-econometric-methodology/identification-strategies/instrumental-variables-strategy.md:243`
  - Link: `[Advanced Robustness Checks](../robustness/advanced-robustness-checks.md)`
- *...and 50 more*
