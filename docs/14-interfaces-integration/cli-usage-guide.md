# CLI Usage Guide

The Yemen Market Integration CLI provides a command-line interface for running market integration analyses and managing the system.

## Overview

The CLI is built using Typer and provides:
- **Market integration analysis** commands
- **Data pipeline management** operations  
- **System status and monitoring** tools
- **Configuration management** utilities

## Installation and Setup

### Prerequisites

```bash
# Python 3.8+ required
python --version

# Install the package
pip install -e .

# Verify installation
yemen-market --help
```

### Configuration

The CLI can be configured through environment variables or configuration files:

```bash
# Set database connection
export YEMEN_MARKET_DB_URL="postgresql://user:pass@localhost/yemen_market"

# Set cache configuration
export YEMEN_MARKET_CACHE_TYPE="redis"
export YEMEN_MARKET_CACHE_URL="redis://localhost:6379"

# Set logging level
export YEMEN_MARKET_LOG_LEVEL="INFO"
```

## Core Commands

### Analysis Commands

#### Run Market Integration Analysis

```bash
# Basic three-tier analysis
yemen-market analyze --markets aden sanaa --commodities wheat rice \
  --start-date 2023-01-01 --end-date 2023-12-31

# Analysis with specific output format
yemen-market analyze --markets aden sanaa hodeidah --commodities wheat \
  --start-date 2023-01-01 --end-date 2023-12-31 \
  --output results/analysis_report.json \
  --format json

# Currency-aware analysis
yemen-market analyze --markets aden sanaa --commodities wheat rice \
  --currency-zones government houthi \
  --exchange-rate-source dynamic \
  --start-date 2023-01-01 --end-date 2023-12-31
```

#### Run Specific Analysis Tiers

```bash
# Run only Tier 1 analysis (pooled panel)
yemen-market tier1 --markets aden sanaa hodeidah \
  --commodities wheat rice maize \
  --start-date 2023-01-01 --end-date 2023-12-31 \
  --fixed-effects market time \
  --cluster-se governorate

# Run Tier 2 analysis (commodity-specific)
yemen-market tier2 --commodity wheat \
  --markets aden sanaa hodeidah taiz \
  --start-date 2023-01-01 --end-date 2023-12-31 \
  --regime-switching \
  --structural-breaks

# Run Tier 3 analysis (validation)
yemen-market tier3 --markets aden sanaa hodeidah \
  --commodities wheat rice \
  --spatial-weights queen \
  --cross-validation \
  --forecast-horizon 6
```

### Data Management Commands

#### Data Pipeline Operations

```bash
# Download and process WFP data
yemen-market data download --source wfp \
  --start-date 2023-01-01 --end-date 2023-12-31 \
  --markets all --commodities all

# Process ACLED conflict data
yemen-market data download --source acled \
  --start-date 2023-01-01 --end-date 2023-12-31 \
  --event-types "Violence against civilians,Battles"

# Create balanced panel
yemen-market data panel create \
  --input data/processed/wfp_prices.csv \
  --output data/processed/balanced_panel.csv \
  --coverage-threshold 0.8 \
  --imputation-method conflict-aware

# Validate data quality
yemen-market data validate \
  --input data/processed/balanced_panel.csv \
  --currency-validation \
  --zone-validation \
  --conflict-validation
```

#### Currency and Exchange Rate Management

```bash
# Validate exchange rates
yemen-market currency validate \
  --data data/processed/wfp_prices.csv \
  --sources cby-aden cby-sanaa parallel-market

# Convert currencies
yemen-market currency convert \
  --input data/raw/wfp_prices_mixed.csv \
  --output data/processed/wfp_prices_usd.csv \
  --target-currency USD \
  --rate-source dynamic

# Check currency zone mapping
yemen-market currency zones \
  --markets aden sanaa hodeidah taiz \
  --date 2023-06-01
```

### System Management Commands

#### Status and Monitoring

```bash
# Check system status
yemen-market status

# Detailed health check
yemen-market status --detailed

# Check specific components
yemen-market status --component database
yemen-market status --component cache
yemen-market status --component api

# Monitor active analyses
yemen-market monitor analyses

# View analysis logs
yemen-market logs --analysis-id abc123 --tail 100
```

#### Configuration Management

```bash
# Show current configuration
yemen-market config show

# Validate configuration
yemen-market config validate

# Set configuration values
yemen-market config set database.url "postgresql://new-host/yemen_market"
yemen-market config set cache.ttl 3600

# Export configuration
yemen-market config export --format yaml --output config/production.yaml

# Import configuration
yemen-market config import --file config/production.yaml
```

## Advanced Usage

### Batch Processing

```bash
# Run multiple analyses from configuration file
yemen-market batch run --config batch_analyses.yaml

# Example batch_analyses.yaml:
# analyses:
#   - name: "wheat_integration"
#     markets: ["aden", "sanaa", "hodeidah"]
#     commodities: ["wheat"]
#     start_date: "2023-01-01"
#     end_date: "2023-12-31"
#   - name: "rice_integration"
#     markets: ["aden", "sanaa", "hodeidah"]
#     commodities: ["rice"]
#     start_date: "2023-01-01"
#     end_date: "2023-12-31"
```

### Report Generation

```bash
# Generate executive summary
yemen-market report executive \
  --analysis-id abc123 \
  --output reports/executive_summary.pdf \
  --template world-bank

# Generate technical report
yemen-market report technical \
  --analysis-id abc123 \
  --output reports/technical_report.tex \
  --include-appendices

# Generate policy brief
yemen-market report policy \
  --analysis-id abc123 \
  --output reports/policy_brief.md \
  --target-audience humanitarian
```

### Export and Integration

```bash
# Export results to various formats
yemen-market export --analysis-id abc123 --format csv --output results.csv
yemen-market export --analysis-id abc123 --format json --output results.json
yemen-market export --analysis-id abc123 --format excel --output results.xlsx

# Export for external tools
yemen-market export --analysis-id abc123 --format stata --output results.dta
yemen-market export --analysis-id abc123 --format r --output results.rds

# Integration with other systems
yemen-market integrate --target-system world-bank-api \
  --analysis-id abc123 --credentials credentials.json
```

## Interactive Mode

### Analysis Wizard

```bash
# Start interactive analysis wizard
yemen-market wizard

# Example interaction:
# ? Select analysis type: Three-tier integration analysis
# ? Select markets: aden, sanaa, hodeidah
# ? Select commodities: wheat, rice
# ? Start date: 2023-01-01
# ? End date: 2023-12-31
# ? Currency handling: Dynamic conversion
# ✓ Starting analysis...
```

### Configuration Wizard

```bash
# Start configuration wizard
yemen-market config wizard

# Example interaction:
# ? Database type: PostgreSQL
# ? Database host: localhost
# ? Database port: 5432
# ? Cache type: Redis
# ? Log level: INFO
# ✓ Configuration saved
```

## Debugging and Troubleshooting

### Verbose Logging

```bash
# Enable verbose logging
yemen-market --verbose analyze --markets aden sanaa --commodities wheat

# Enable debug logging
yemen-market --debug analyze --markets aden sanaa --commodities wheat

# Save logs to file
yemen-market --log-file debug.log analyze --markets aden sanaa --commodities wheat
```

### Diagnostic Commands

```bash
# Test database connection
yemen-market diagnose database

# Test data quality
yemen-market diagnose data --input data/processed/panel.csv

# Test analysis components
yemen-market diagnose analysis --quick

# Full system diagnostic
yemen-market diagnose system --comprehensive
```

### Performance Profiling

```bash
# Profile analysis performance
yemen-market profile analyze --markets aden sanaa --commodities wheat \
  --start-date 2023-01-01 --end-date 2023-12-31

# Memory usage profiling
yemen-market profile memory --analysis-id abc123

# Performance benchmarking
yemen-market benchmark --config benchmark.yaml
```

## Integration with Scripts

### Shell Integration

```bash
#!/bin/bash
# analysis_pipeline.sh

# Set up environment
export YEMEN_MARKET_ENV=production

# Download latest data
echo "Downloading WFP data..."
yemen-market data download --source wfp --latest

# Validate data quality
echo "Validating data quality..."
if ! yemen-market data validate --input data/latest/wfp_prices.csv; then
    echo "Data validation failed"
    exit 1
fi

# Run analysis
echo "Running three-tier analysis..."
ANALYSIS_ID=$(yemen-market analyze --markets aden sanaa hodeidah \
    --commodities wheat rice --output json | jq -r '.analysis_id')

# Wait for completion
echo "Waiting for analysis completion..."
while [ "$(yemen-market status --analysis-id $ANALYSIS_ID --format json | jq -r '.status')" != "completed" ]; do
    sleep 30
done

# Generate reports
echo "Generating reports..."
yemen-market report executive --analysis-id $ANALYSIS_ID --output reports/
yemen-market report technical --analysis-id $ANALYSIS_ID --output reports/

echo "Analysis pipeline completed successfully"
```

### Python Integration

```python
import subprocess
import json
from pathlib import Path

def run_yemen_analysis(markets, commodities, start_date, end_date):
    """Run Yemen market analysis via CLI."""
    
    cmd = [
        "yemen-market", "analyze",
        "--markets"] + markets + [
        "--commodities"] + commodities + [
        "--start-date", start_date,
        "--end-date", end_date,
        "--format", "json"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        return json.loads(result.stdout)
    else:
        raise RuntimeError(f"Analysis failed: {result.stderr}")

# Usage
result = run_yemen_analysis(
    markets=["aden", "sanaa"],
    commodities=["wheat", "rice"],
    start_date="2023-01-01",
    end_date="2023-12-31"
)

print(f"Analysis ID: {result['analysis_id']}")
```

## Command Reference

### Global Options

| Option | Description | Default |
|--------|-------------|---------|
| `--verbose` | Enable verbose output | False |
| `--debug` | Enable debug logging | False |
| `--config` | Configuration file path | config/default.yaml |
| `--log-file` | Log to file | None |
| `--no-color` | Disable colored output | False |

### Analysis Commands

| Command | Description | Key Options |
|---------|-------------|-------------|
| `analyze` | Run three-tier analysis | `--markets`, `--commodities`, `--start-date`, `--end-date` |
| `tier1` | Run Tier 1 analysis | `--fixed-effects`, `--cluster-se` |
| `tier2` | Run Tier 2 analysis | `--regime-switching`, `--structural-breaks` |
| `tier3` | Run Tier 3 analysis | `--spatial-weights`, `--cross-validation` |

### Data Commands

| Command | Description | Key Options |
|---------|-------------|-------------|
| `data download` | Download source data | `--source`, `--start-date`, `--end-date` |
| `data validate` | Validate data quality | `--currency-validation`, `--zone-validation` |
| `data panel` | Create balanced panel | `--coverage-threshold`, `--imputation-method` |

### System Commands

| Command | Description | Key Options |
|---------|-------------|-------------|
| `status` | Check system status | `--detailed`, `--component` |
| `config` | Manage configuration | `show`, `set`, `validate` |
| `logs` | View logs | `--analysis-id`, `--tail` |

## Related Documentation

- [API Integration Guide](./api-integration-guide.md)
- [Jupyter Notebook Integration](./notebook-integration.md)
- [Application Guides](../11-application-guides/)
- [User Guides](../02-user-guides/)