# Dependency Injection Guide

The Yemen Market Integration system uses a comprehensive dependency injection (DI) container to manage service dependencies and provide clean separation of concerns.

## Overview

The DI container provides:
- **Service registration and resolution** for all system components
- **Configuration management** through environment-aware settings
- **Lifecycle management** for singleton and transient services
- **Plugin integration** for extensible architecture

## Container Architecture

### Main Container

The system uses a declarative container pattern with automatic fallback:

```python
from src.shared.container import Container

# Try to use dependency_injector if available, fallback to simple container
try:
    from dependency_injector import containers, providers
    DEPENDENCY_INJECTOR_AVAILABLE = True
except ImportError:
    DEPENDENCY_INJECTOR_AVAILABLE = False
    from .container_simple import SimpleContainer

class Container(containers.DeclarativeContainer):
    """Main dependency injection container."""
    
    # Configuration
    config = providers.Configuration()
    
    # Infrastructure services
    database = providers.Singleton(DatabaseConnection, config.database.url)
    cache = providers.Singleton(CacheService, config.cache)
    event_bus = providers.Singleton(EventBus, config.events)
    
    # Domain services
    market_service = providers.Factory(MarketIntegrationService, database)
    price_service = providers.Factory(PriceTransmissionService, database)
    
    # Application services
    analysis_orchestrator = providers.Factory(
        AnalysisOrchestrator,
        market_service=market_service,
        price_service=price_service,
        cache=cache
    )
```

### Fallback Container

For environments without dependency_injector:

```python
class SimpleContainer:
    """Simple container implementation as fallback."""
    
    def __init__(self):
        self._services = {}
        self._singletons = {}
    
    def register_singleton(self, name: str, service_class: type, *args, **kwargs):
        """Register a singleton service."""
        if name not in self._singletons:
            self._singletons[name] = service_class(*args, **kwargs)
        return self._singletons[name]
    
    def register_transient(self, name: str, service_class: type):
        """Register a transient service."""
        self._services[name] = service_class
    
    def resolve(self, name: str):
        """Resolve a service by name."""
        if name in self._singletons:
            return self._singletons[name]
        elif name in self._services:
            return self._services[name]()
        else:
            raise ValueError(f"Service '{name}' not registered")
```

## Service Registration

### Infrastructure Services

```python
class Container(containers.DeclarativeContainer):
    """Container with infrastructure service definitions."""
    
    # Configuration
    config = providers.Configuration()
    
    # Database services
    database_connection = providers.Singleton(
        DatabaseConnection,
        url=config.database.url,
        pool_size=config.database.pool_size.as_int(),
        max_overflow=config.database.max_overflow.as_int()
    )
    
    # Cache services (conditional based on type)
    cache = providers.Singleton(
        providers.Factory(
            lambda cache_type, **kwargs: (
                RedisCache(**kwargs) if cache_type == "redis" 
                else MemoryCache(**kwargs)
            ),
            cache_type=config.cache.type,
            url=config.cache.url,
            ttl=config.cache.ttl.as_int()
        )
    )
    
    # Event bus services
    event_bus = providers.Singleton(
        providers.Factory(
            lambda event_type, **kwargs: (
                AsyncEventBus(**kwargs) if event_type == "async"
                else InMemoryEventBus(**kwargs)
            ),
            event_type=config.events.type
        )
    )
```

### Data Processing Services

```python
# Data source clients
hdx_client = providers.Singleton(
    HDXClient,
    api_key=config.hdx.api_key,
    base_url=config.hdx.base_url
)

wfp_client = providers.Singleton(
    WFPClient,
    api_key=config.wfp.api_key,
    base_url=config.wfp.base_url
)

acled_client = providers.Singleton(
    ACLEDClient,
    api_key=config.acled.api_key,
    base_url=config.acled.base_url
)

# Data processors
wfp_processor = providers.Factory(
    WFPProcessor,
    client=wfp_client,
    cache=cache,
    quality_validator=providers.Factory(DataQualityValidator)
)

panel_builder = providers.Factory(
    PanelBuilder,
    wfp_processor=wfp_processor,
    acled_processor=providers.Factory(ACLEDProcessor, client=acled_client),
    spatial_joiner=providers.Factory(SpatialJoiner)
)
```

### Application Services

```python
# Command and query handlers
analyze_market_integration_handler = providers.Factory(
    AnalyzeMarketIntegrationHandler,
    analysis_orchestrator=providers.Factory(
        AnalysisOrchestrator,
        tier1_runner=providers.Factory(Tier1Runner),
        tier2_runner=providers.Factory(Tier2Runner),
        tier3_runner=providers.Factory(Tier3Runner),
        event_bus=event_bus
    ),
    unit_of_work=providers.Factory(PostgresUnitOfWork, database_connection)
)

# Policy analysis services
policy_orchestrator = providers.Factory(
    PolicyOrchestrator,
    welfare_calculator=providers.Factory(ZoneWelfareCalculator),
    aid_optimizer=providers.Factory(AidOptimizer),
    fragmentation_analyzer=providers.Factory(FragmentationCostAnalyzer)
)
```

### Security Services

```python
# Authentication and authorization
jwt_handler = providers.Singleton(
    JWTHandler,
    secret_key=config.security.jwt_secret,
    algorithm=config.security.jwt_algorithm,
    expiration_minutes=config.security.jwt_expiration.as_int()
)

password_handler = providers.Singleton(PasswordHandler)

rbac_manager = providers.Singleton(
    RBACManager,
    user_repository=providers.Factory(UserRepository, database_connection)
)

authentication_service = providers.Factory(
    AuthenticationService,
    jwt_handler=jwt_handler,
    password_handler=password_handler,
    user_repository=providers.Factory(UserRepository, database_connection)
)
```

## Configuration Management

### Environment-Based Configuration

```python
# config/container_config.py
from pathlib import Path
import os

def load_container_config(environment: str = None) -> dict:
    """Load configuration for container based on environment."""
    
    env = environment or os.getenv("YEMEN_MARKET_ENV", "development")
    
    base_config = {
        "database": {
            "url": os.getenv("DATABASE_URL", "postgresql://localhost/yemen_market"),
            "pool_size": int(os.getenv("DB_POOL_SIZE", "5")),
            "max_overflow": int(os.getenv("DB_MAX_OVERFLOW", "10"))
        },
        "cache": {
            "type": os.getenv("CACHE_TYPE", "memory"),
            "url": os.getenv("CACHE_URL", "redis://localhost:6379"),
            "ttl": int(os.getenv("CACHE_TTL", "3600"))
        },
        "events": {
            "type": os.getenv("EVENT_BUS_TYPE", "inmemory")
        }
    }
    
    # Environment-specific overrides
    if env == "production":
        base_config.update({
            "database": {
                **base_config["database"],
                "pool_size": 20,
                "max_overflow": 30
            },
            "cache": {
                **base_config["cache"],
                "type": "redis"
            },
            "events": {
                "type": "async"
            }
        })
    
    return base_config
```

### Configuration Usage

```python
from src.shared.container import Container
from config.container_config import load_container_config

# Initialize container with environment configuration
container = Container()
config = load_container_config("production")
container.config.from_dict(config)

# Wire up all dependencies
container.wire(modules=[
    "src.interfaces.api",
    "src.interfaces.cli", 
    "src.application.commands",
    "src.application.queries"
])
```

## Service Lifecycle Management

### Singleton Services

Singleton services are created once and reused:

```python
# Database connections, caches, and configuration
database = providers.Singleton(DatabaseConnection, config.database.url)
cache = providers.Singleton(RedisCache, config.cache.url)
config_service = providers.Singleton(ConfigurationService)

# Usage - same instance returned every time
db1 = container.database()
db2 = container.database()
assert db1 is db2  # Same instance
```

### Factory Services

Factory services create new instances each time:

```python
# Request-scoped services
unit_of_work = providers.Factory(UnitOfWork, database)
command_handler = providers.Factory(CommandHandler, unit_of_work)

# Usage - new instance each time
uow1 = container.unit_of_work()
uow2 = container.unit_of_work()
assert uow1 is not uow2  # Different instances
```

### Resource Management

```python
class ManagedResource(providers.Resource):
    """Managed resource with proper cleanup."""
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self._connection = None
    
    def __enter__(self):
        """Initialize the resource."""
        self._connection = create_connection(self.connection_string)
        return self._connection
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Clean up the resource."""
        if self._connection:
            self._connection.close()

# Register as resource
database = providers.Resource(ManagedResource, config.database.url)
```

## Plugin Integration

### Plugin Manager

```python
class PluginManager:
    """Manages plugin registration and lifecycle."""
    
    def __init__(self, container: Container):
        self.container = container
        self.plugins = {}
    
    def register_plugin(self, plugin_name: str, plugin_class: type):
        """Register a plugin with the container."""
        
        # Create plugin instance
        plugin = plugin_class()
        
        # Register plugin services with container
        if hasattr(plugin, 'register_services'):
            plugin.register_services(self.container)
        
        # Store plugin reference
        self.plugins[plugin_name] = plugin
    
    def load_plugins_from_directory(self, plugin_dir: Path):
        """Load all plugins from a directory."""
        
        for plugin_file in plugin_dir.glob("*/plugin.py"):
            plugin_module = import_module_from_path(plugin_file)
            
            if hasattr(plugin_module, 'Plugin'):
                plugin_name = plugin_file.parent.name
                self.register_plugin(plugin_name, plugin_module.Plugin)
```

### Plugin Example

```python
# plugins/custom_data_source/plugin.py
from src.shared.container import Container

class CustomDataSourcePlugin:
    """Custom data source plugin."""
    
    def register_services(self, container: Container):
        """Register plugin services with container."""
        
        # Register custom data client
        container.register_singleton(
            "custom_data_client",
            CustomDataClient,
            api_key=container.config.custom_data.api_key
        )
        
        # Register custom processor
        container.register_factory(
            "custom_processor",
            CustomDataProcessor,
            client=container.resolve("custom_data_client")
        )
    
    def initialize(self):
        """Initialize plugin."""
        pass
    
    def cleanup(self):
        """Cleanup plugin resources."""
        pass
```

## Usage Patterns

### Basic Service Resolution

```python
from src.shared.container import Container

# Initialize container
container = Container()

# Resolve services
market_service = container.market_service()
analysis_orchestrator = container.analysis_orchestrator()

# Use services
result = await market_service.get_market_integration(market_ids)
```

### Dependency Injection in Handlers

```python
from dependency_injector.wiring import Provide, inject

class AnalysisController:
    """API controller with injected dependencies."""
    
    @inject
    def __init__(
        self,
        orchestrator: AnalysisOrchestrator = Provide[Container.analysis_orchestrator],
        cache: CacheService = Provide[Container.cache]
    ):
        self.orchestrator = orchestrator
        self.cache = cache
    
    async def run_analysis(self, request: AnalysisRequest):
        """Run analysis with injected services."""
        
        # Check cache first
        cache_key = f"analysis:{request.hash()}"
        cached_result = await self.cache.get(cache_key)
        
        if cached_result:
            return cached_result
        
        # Run analysis
        result = await self.orchestrator.run_analysis(request)
        
        # Cache result
        await self.cache.set(cache_key, result, ttl=3600)
        
        return result
```

### Context Manager Usage

```python
async def run_analysis_with_context():
    """Run analysis using container context manager."""
    
    async with Container() as container:
        # Services are properly initialized
        orchestrator = container.analysis_orchestrator()
        
        try:
            result = await orchestrator.run_three_tier_analysis(command)
            return result
        finally:
            # Resources are automatically cleaned up
            pass
```

## Testing with Container

### Test Container Configuration

```python
# tests/conftest.py
import pytest
from src.shared.container import Container

@pytest.fixture
def test_container():
    """Container configured for testing."""
    
    container = Container()
    
    # Override with test configuration
    test_config = {
        "database": {
            "url": "sqlite:///:memory:",
            "pool_size": 1,
            "max_overflow": 0
        },
        "cache": {
            "type": "memory",
            "ttl": 60
        },
        "events": {
            "type": "inmemory"
        }
    }
    
    container.config.from_dict(test_config)
    return container

@pytest.fixture
def analysis_service(test_container):
    """Analysis service with test dependencies."""
    return test_container.analysis_orchestrator()
```

### Mock Service Registration

```python
def test_analysis_with_mocks(test_container):
    """Test analysis with mocked dependencies."""
    
    # Replace real service with mock
    mock_market_service = Mock(spec=MarketIntegrationService)
    mock_market_service.get_integration_data.return_value = test_data
    
    test_container.override_singleton("market_service", mock_market_service)
    
    # Use container with mocked service
    orchestrator = test_container.analysis_orchestrator()
    result = orchestrator.run_analysis(test_command)
    
    # Verify mock was called
    mock_market_service.get_integration_data.assert_called_once()
```

## Performance Considerations

### Lazy Loading

```python
class LazyContainer(Container):
    """Container with lazy service initialization."""
    
    def __init__(self):
        super().__init__()
        self._lazy_services = {}
    
    def get_lazy_service(self, service_name: str):
        """Get service with lazy initialization."""
        
        if service_name not in self._lazy_services:
            self._lazy_services[service_name] = self.resolve(service_name)
        
        return self._lazy_services[service_name]
```

### Connection Pooling

```python
# Optimized database configuration
database = providers.Singleton(
    DatabaseConnection,
    url=config.database.url,
    pool_size=20,  # Increase for high load
    max_overflow=30,
    pool_timeout=30,
    pool_recycle=3600  # Recycle connections hourly
)
```

## Related Documentation

- [Plugin Development Guide](./plugin-development-guide.md)
- [Configuration Management](./configuration-management.md)
- [Application Architecture](../01-architecture/components.md)
- [Development Setup](../04-development/setup/development-setup.md)