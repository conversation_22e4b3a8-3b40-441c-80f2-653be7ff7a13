# Shared Utilities

This section documents the shared components including dependency injection containers, plugin systems, and common utilities used across the Yemen Market Integration system.

## Contents

### Container Configuration
- **Dependency Injection**: Service container setup and configuration
- **Service Registration**: Registering services and their dependencies
- **Lifecycle Management**: Service initialization and cleanup
- **Configuration Management**: Environment-specific configurations

### Plugin Development
- **Plugin Architecture**: Extensible plugin system design
- **Plugin Interface**: Standard interfaces for plugin development
- **Plugin Discovery**: Automatic plugin detection and loading
- **Plugin Examples**: Sample plugins for data sources and models

### Shared Components
- **Logging Framework**: Centralized logging configuration
- **Error Handling**: Common error handling patterns and utilities
- **Configuration Loading**: Environment and file-based configuration
- **Utility Functions**: Mathematical, statistical, and data utilities

### Extension Points
- **Data Source Plugins**: Adding new data sources
- **Model Plugins**: Custom econometric models
- **Output Plugins**: Custom report formats and visualizations
- **Validation Plugins**: Custom validation rules and checks

## Quick Start

### Container Usage
```python
from src.shared.container import Container

# Initialize container
container = Container()

# Register services
container.register_singleton("data_processor", WFPProcessor)
container.register_transient("model", MarketIntegrationModel)

# Resolve dependencies
processor = container.resolve("data_processor")
model = container.resolve("model")
```

### Plugin Development
```python
from plugins import DataSourcePlugin

class CustomDataSource(DataSourcePlugin):
    def load_data(self) -> pd.DataFrame:
        # Custom data loading logic
        return data
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        # Custom validation logic
        return True

# Register plugin
container.register_plugin("custom_source", CustomDataSource)
```

### Configuration
```python
from src.shared.config import load_config

# Load environment-specific configuration
config = load_config("production")

# Access nested configuration
database_url = config.database.url
redis_settings = config.cache.redis
```

## Plugin Examples

### Data Source Plugin
```python
# plugins/data_sources/world_bank/plugin.py
class WorldBankDataSource(DataSourcePlugin):
    name = "world_bank"
    description = "World Bank commodity price data"
    
    def fetch_data(self, indicators: List[str]) -> pd.DataFrame:
        # Implement World Bank API integration
        pass
```

### Model Plugin
```python
# plugins/models/custom_vecm/plugin.py
class CustomVECMPlugin(ModelPlugin):
    name = "custom_vecm"
    description = "Custom VECM implementation"
    
    def estimate(self, data: pd.DataFrame) -> ModelResults:
        # Implement custom VECM estimation
        pass
```

## Related Documentation

- [Development Setup](../04-development/setup/development-setup.md)
- [Plugin Development Guide](../04-development/plugin-development-guide.md)
- [Core Reference](../12-core-reference/README.md)