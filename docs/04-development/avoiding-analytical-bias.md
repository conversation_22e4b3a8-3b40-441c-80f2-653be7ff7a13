# Avoiding Analytical Bias in Hypothesis Testing

This guide provides best practices for conducting unbiased hypothesis tests in the Yemen Market Integration project.

## Core Principles

### 1. Pre-Registration is Mandatory

**Before accessing any data**, you must register your analysis plan:

```python
from src.core.models.hypothesis_testing import register_analysis_plan

plan_hash = register_analysis_plan(
    hypothesis_id='H1',
    primary_specification={
        'model': 'PanelOLS',
        'fixed_effects': ['market', 'time'],
        'clustering': 'market',
        'controls': ['conflict_intensity']
    },
    decision_rules={
        'alpha': 0.05,
        'minimum_effect_size': 0.10
    }
)
```

### 2. Neutral Language Only

❌ **Avoid biased language:**
- "This will show..."
- "We expect to find..."
- "The hypothesis predicts..."
- "This confirms..."

✅ **Use neutral language:**
- "This tests whether..."
- "We examine the relationship..."
- "The analysis investigates..."
- "Results indicate..."

### 3. Report All Results

The framework automatically tracks all analyses you run. You cannot selectively report only significant results.

```python
# All analyses are logged automatically
bias_detector = BiasDetector()
bias_report = bias_detector.check_p_hacking('H1')
```

### 4. Use Specification Curves

Instead of choosing a single "best" specification, test all reasonable specifications:

```python
from src.core.models.hypothesis_testing import SpecificationCurve

curve = SpecificationCurve(your_model_function)
curve.add_specification_choice('fixed_effects', ['none', 'entity', 'time', 'both'])
curve.add_specification_choice('clustering', ['none', 'entity', 'robust'])
curve.run_all_specifications(data)
```

### 5. Multiple Testing Correction

When running multiple hypothesis tests, p-values are automatically adjusted:

```python
# The framework applies FDR correction automatically
results = HypothesisRegistry.run_all(data)
# Each result includes both p_value and adjusted_p_value
```

## Common Pitfalls to Avoid

### P-Hacking
❌ **Don't:**
- Run multiple tests until you find p < 0.05
- Change specifications after seeing results
- Add/remove controls based on significance

✅ **Do:**
- Pre-register all analyses
- Report all tests run
- Use adjusted p-values

### HARKing (Hypothesizing After Results are Known)
❌ **Don't:**
- Create hypotheses after seeing patterns in data
- Present exploratory findings as confirmatory
- Change hypothesis direction based on results

✅ **Do:**
- Register hypotheses before data access
- Clearly label exploratory vs confirmatory analyses
- Stick to pre-registered plans

### Cherry-Picking
❌ **Don't:**
- Report only favorable subgroups
- Choose time periods that support hypothesis
- Select outlier handling that improves results

✅ **Do:**
- Test all pre-registered subgroups
- Use full time period or pre-registered subset
- Apply consistent outlier handling

## Using the Bias Prevention Tools

### 1. Degrees of Freedom Tracker

Track all analytical choices made:

```python
from src.core.models.hypothesis_testing import track_degrees_of_freedom

tracker = track_degrees_of_freedom('H1')
tracker.record_choice(
    choice_type='outlier_handling',
    choice_made='winsorize_1pct',
    alternatives=['none', 'winsorize_1pct', 'winsorize_5pct', 'trim_1pct']
)
```

### 2. Robustness Testing

All tests automatically include robustness checks:

```python
# Robustness testing is built into the framework
results = h1_test.run_test(data)
print(f"Robustness score: {results.comprehensive_robustness.robustness_score:.0%}")
```

### 3. Bias Detection

Check for analytical bias patterns:

```python
bias_check = bias_detector.check_p_hacking('H1')
if bias_check['p_hacking_risk']:
    print("Warning: Potential bias detected!")
    for pattern in bias_check['suspicious_patterns']:
        print(f"- {pattern}")
```

## Interpreting Results Honestly

### Use Neutral Outcome Language

```python
if results.outcome == HypothesisOutcome.NULL_REJECTED:
    print("The null hypothesis was rejected at the specified significance level.")
elif results.outcome == HypothesisOutcome.FAIL_TO_REJECT_NULL:
    print("We fail to reject the null hypothesis.")
elif results.outcome == HypothesisOutcome.INSUFFICIENT_POWER:
    print("The test lacks sufficient statistical power for reliable conclusions.")
```

### Report Uncertainty

Always include:
- Confidence intervals (not just point estimates)
- Statistical power
- Robustness across specifications
- Data quality limitations

### Example Unbiased Report

```python
interpretation = h1_test.interpret_results(results)

print(f"Statistical Summary: {interpretation.statistical_summary}")
print(f"Key Findings:")
for finding in interpretation.findings:
    print(f"  • {finding}")
    
print(f"Uncertainty: {interpretation.uncertainty_statement}")
print(f"Limitations:")
for limitation in interpretation.limitations:
    print(f"  • {limitation}")
```

## Workflow Summary

1. **Design Study** → Define hypotheses and analysis plan
2. **Pre-Register** → Register plan before data access
3. **Access Data** → Load data (pre-registration enforced)
4. **Run Analyses** → Execute only pre-registered tests
5. **Check Bias** → Review bias detection results
6. **Report Honestly** → Include all results and limitations

## Red Flags to Watch For

If you find yourself:
- 🚩 Running "just one more test" to check something
- 🚩 Thinking "this can't be right" about a null result
- 🚩 Wanting to exclude certain observations post-hoc
- 🚩 Considering a different model after seeing results

**STOP** and consult the pre-analysis plan. These are signs of potential bias.

## Getting Help

If you're unsure about:
- How to pre-register an analysis
- Whether a choice constitutes p-hacking
- How to interpret ambiguous results

Contact the methodology team or review the examples in `/examples/proper_hypothesis_testing.py`.

---

Remember: **Good science means accepting all results, not just the ones we expect.**