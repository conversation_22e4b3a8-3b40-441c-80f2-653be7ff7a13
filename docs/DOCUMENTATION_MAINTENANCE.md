# Documentation Maintenance Guide

This guide provides instructions for maintaining the documentation quality and structure of the Yemen Market Integration project.

## Automated Quality Checks

### GitHub Actions Workflow

The repository includes automated documentation quality checks that run on every push and pull request:

- **Link validation**: Checks for broken internal and external links
- **Structure validation**: Ensures SRC architecture alignment is maintained
- **Research methodology integrity**: Verifies the research methodology package remains intact
- **World Bank standards compliance**: Validates academic rigor indicators

### Running Checks Locally

```bash
# Validate documentation structure
python3 scripts/validate_docs_structure.py

# Validate internal links
python3 scripts/validate_internal_links.py

# Check external links (requires markdown-link-check)
npm install -g markdown-link-check
find docs -name "*.md" | xargs markdown-link-check --config .github/markdown-link-check-config.json
```

## Documentation Structure Standards

### SRC Architecture Alignment

The documentation is organized to align with the SRC (Shared-Repository-Core) architecture:

```
docs/
├── 00-10-contributing/          # Standard documentation sections
├── 11-application-guides/       # Application layer (NEW)
├── 12-core-reference/          # Core domain (NEW)  
├── 13-infrastructure-ops/      # Infrastructure layer (NEW)
├── 14-interfaces-integration/  # Interface layer (NEW)
├── 15-shared-utilities/        # Shared components (NEW)
└── research-methodology-package/ # PRESERVED - Complete methodology
```

### File Organization Rules

1. **Implementation Reports** → `docs/04-development/implementation-reports/`
2. **Technical Issues** → `docs/09-troubleshooting/technical-issues/`
3. **Validation Reports** → `docs/08-results/validation-reports/`
4. **Strategic Documents** → `docs/05-methodology/strategic-documents/`

### README Requirements

Every directory must have a `README.md` file that includes:
- **Purpose and scope** of the directory contents
- **Quick navigation** to key files
- **Usage examples** where applicable
- **Related documentation** links

## Research Methodology Package Protection

### Critical Preservation Rules

The `docs/research-methodology-package/` directory is **PROTECTED** and must be preserved exactly as-is:

- **264 files** must be maintained (current count validated by CI)
- **Directory structure** cannot be modified
- **Cross-references** within the package must remain intact
- **World Bank methodological standards** must be preserved

### Validation Checks

```bash
# Check file count
find docs/research-methodology-package -name "*.md" | wc -l
# Should return 264

# Validate World Bank references
grep -r "World Bank" docs/research-methodology-package/ | wc -l
# Should return > 0
```

## Link Management

### Internal Link Standards

- Use **relative paths** for internal documentation links
- Include **anchor links** for specific sections when helpful
- Update **cross-references** when moving files
- Test links after any reorganization

### External Link Maintenance

- Regular validation through CI/CD
- Use stable URLs when possible
- Include backup references for critical external resources
- Document API dependencies that may change

### Link Validation Process

1. **Automated checks** run on every commit
2. **Manual verification** for complex reorganizations
3. **Issue tracking** for broken external links
4. **Regular maintenance** schedule (monthly)

## Adding New Documentation

### SRC-Aligned Content

When adding new documentation, place it in the appropriate SRC section:

```bash
# Application layer workflows
docs/11-application-guides/new-workflow-guide.md

# Core domain models
docs/12-core-reference/new-domain-model.md

# Infrastructure operations
docs/13-infrastructure-ops/new-infrastructure-guide.md

# Interface integration patterns
docs/14-interfaces-integration/new-interface-guide.md

# Shared utility documentation
docs/15-shared-utilities/new-utility-guide.md
```

### Content Standards

All new documentation must include:

1. **Clear purpose statement**
2. **Target audience** identification
3. **Prerequisites** and dependencies
4. **Step-by-step instructions** with examples
5. **Related documentation** links
6. **Last updated date**

### Academic Quality Requirements

For research-related content:

- **Methodological rigor** appropriate for World Bank standards
- **Proper citations** and references
- **Clear hypothesis** and validation frameworks
- **Reproducible examples** and instructions
- **Conflict-aware** considerations for Yemen context

## Quality Assurance Process

### Pre-commit Checks

Before committing documentation changes:

```bash
# 1. Run structure validation
python3 scripts/validate_docs_structure.py

# 2. Run link validation  
python3 scripts/validate_internal_links.py

# 3. Check for moved file references
grep -r "ALTERNATIVE_EXPLANATIONS_FIX_REPORT" . --exclude-dir=docs/04-development

# 4. Validate research methodology integrity
find docs/research-methodology-package -name "*.md" | wc -l
```

### Monthly Maintenance Tasks

1. **External link audit**: Check for broken external links
2. **Content freshness review**: Update dates and outdated information  
3. **Structure optimization**: Identify navigation improvements
4. **User feedback integration**: Address documentation gaps reported by users

### Quarterly Reviews

1. **SRC alignment assessment**: Ensure documentation follows code architecture
2. **Academic standards review**: Validate World Bank compliance
3. **User journey optimization**: Test common documentation paths
4. **Performance assessment**: Monitor documentation load times and accessibility

## Troubleshooting Common Issues

### Broken Links After File Moves

```bash
# Find references to moved files
grep -r "old-filename.md" docs/

# Update references to new location
sed -i 's|old-path/old-filename.md|new-path/old-filename.md|g' docs/**/*.md
```

### Missing README Files

```bash
# Create template README for new directory
cat > docs/new-directory/README.md << 'EOF'
# New Directory

Brief description of the directory purpose.

## Contents

- [File 1](file1.md) - Description
- [File 2](file2.md) - Description

## Related Documentation

- [Related Section](../related-section/)
EOF
```

### Research Methodology Package Issues

If the methodology package validation fails:

1. **DO NOT** modify files within `docs/research-methodology-package/`
2. **Check git history** to see what was changed
3. **Restore from backup** if files were accidentally modified
4. **Report issue** if systematic corruption detected

## CI/CD Integration

### Workflow Configuration

The documentation checks are configured in `.github/workflows/documentation-checks.yml`:

- **Triggers**: Push to main/develop, PRs affecting documentation
- **Checks**: Links, structure, methodology integrity, World Bank standards
- **Failure actions**: Block merges, notify maintainers

### Adding New Checks

To add new documentation validation:

1. **Create validation script** in `scripts/`
2. **Add to workflow** in `.github/workflows/documentation-checks.yml`
3. **Test locally** before committing
4. **Update maintenance guide** with new requirements

## Best Practices

### Writing Guidelines

- **Clear hierarchy**: Use consistent heading levels (H1 → H2 → H3)
- **Scannable content**: Use bullet points, numbered lists, code blocks
- **Practical examples**: Include working code samples and commands
- **Context awareness**: Consider Yemen-specific requirements and constraints

### Maintenance Workflow

1. **Regular validation**: Run checks before major changes
2. **Incremental updates**: Make small, focused documentation changes  
3. **Cross-reference maintenance**: Update related links when restructuring
4. **Version tracking**: Document major organizational changes

### Performance Considerations

- **Optimize images**: Compress screenshots and diagrams
- **Minimize external dependencies**: Cache critical external content locally
- **Efficient navigation**: Provide multiple paths to important content
- **Search optimization**: Use consistent terminology and keywords

## Emergency Procedures

### Documentation Corruption

If documentation becomes corrupted or incorrectly modified:

1. **Stop all changes** immediately
2. **Assess impact** using validation scripts
3. **Restore from git history** for affected files
4. **Verify integrity** using automated checks
5. **Document incident** and prevention measures

### Research Methodology Package Emergency

If the research methodology package is compromised:

1. **IMMEDIATE STOP** - Do not make any further changes
2. **Assess damage** - Run integrity validation
3. **Contact research team** - Notify methodology authors
4. **Restore from backup** - Use git history or archive backups
5. **Validate restoration** - Ensure all 264 files are intact

This maintenance guide ensures the documentation remains high-quality, well-organized, and aligned with both technical architecture and academic standards.