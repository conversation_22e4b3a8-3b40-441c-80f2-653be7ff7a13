# Command and Query Patterns (CQRS)

The Yemen Market Integration system implements Command Query Responsibility Segregation (CQRS) patterns to separate read and write operations for better scalability and maintainability.

## Overview

The application layer uses CQRS to:
- **Separate concerns** between commands (writes) and queries (reads)
- **Optimize performance** with different models for read/write operations
- **Improve scalability** by allowing independent scaling of read/write sides
- **Enhance maintainability** through clear separation of responsibilities

## Core Interfaces

### Command Pattern

Commands represent actions that change system state:

```python
from src.application.interfaces import Command, CommandHandler

class RunThreeTierAnalysis(Command):
    """Command to run three-tier market integration analysis."""
    
    def __init__(
        self,
        market_ids: List[str],
        commodity_ids: List[str],
        start_date: str,
        end_date: str,
        currency_zones: List[str]
    ):
        self.market_ids = market_ids
        self.commodity_ids = commodity_ids
        self.start_date = start_date
        self.end_date = end_date
        self.currency_zones = currency_zones

class RunThreeTierAnalysisHandler(CommandHandler):
    """Handler for three-tier analysis command."""
    
    async def handle(self, command: RunThreeTierAnalysis) -> AnalysisResult:
        # Implementation details
        pass
```

### Query Pattern

Queries represent read operations that don't change state:

```python
from src.application.interfaces import Query, QueryHandler

class GetMarketPricesQuery(Query):
    """Query to retrieve market price data."""
    
    def __init__(
        self,
        market_ids: List[str],
        commodity_ids: List[str],
        date_range: DateRange
    ):
        self.market_ids = market_ids
        self.commodity_ids = commodity_ids
        self.date_range = date_range

class GetMarketPricesQueryHandler(QueryHandler):
    """Handler for market prices query."""
    
    async def handle(self, query: GetMarketPricesQuery) -> PriceData:
        # Implementation details
        pass
```

## Command Bus

The command bus orchestrates command execution:

```python
from src.application.interfaces import CommandBus

# Execute a command
command = RunThreeTierAnalysis(
    market_ids=["market_1", "market_2"],
    commodity_ids=["wheat", "rice"],
    start_date="2023-01-01",
    end_date="2023-12-31",
    currency_zones=["houthi", "government"]
)

result = await command_bus.execute(command)
```

## Query Bus

The query bus handles query execution:

```python
from src.application.interfaces import QueryBus

# Execute a query
query = GetMarketPricesQuery(
    market_ids=["market_1", "market_2"],
    commodity_ids=["wheat", "rice"],
    date_range=DateRange("2023-01-01", "2023-12-31")
)

prices = await query_bus.execute(query)
```

## Event-Driven Architecture

The system publishes domain events when important state changes occur:

```python
from src.application.interfaces import EventBus
from src.core.domain.shared.events import AnalysisCompletedEvent

# Publish domain event
event = AnalysisCompletedEvent(
    analysis_id="analysis_123",
    results_summary=results.summary,
    completed_at=datetime.utcnow()
)

await event_bus.publish(event)
```

## Unit of Work Pattern

Ensures transactional consistency across operations:

```python
from src.application.interfaces import UnitOfWork

async with unit_of_work:
    # Perform multiple operations
    await repository.save(analysis)
    await cache.invalidate(cache_key)
    await event_bus.publish(event)
    
    # Commit all changes atomically
    await unit_of_work.commit()
```

## Caching Strategy

The application layer implements intelligent caching:

```python
from src.application.interfaces import Cache

# Cache expensive analysis results
cache_key = f"analysis_{market_ids}_{commodity_ids}_{date_hash}"
cached_result = await cache.get(cache_key)

if cached_result is None:
    result = await run_expensive_analysis()
    await cache.set(cache_key, result, ttl=3600)  # 1 hour TTL
    return result

return cached_result
```

## Best Practices

### Command Design
- **Single responsibility** - Each command should do one thing
- **Immutable** - Commands should be immutable after creation
- **Validation** - Include validation logic in command constructors
- **Clear naming** - Use verb-noun naming pattern (e.g., `RunAnalysis`, `UpdatePrices`)

### Query Design
- **Read-only** - Queries should never modify state
- **Efficient** - Optimize for fast read operations
- **Specific** - Design queries for specific use cases
- **Cacheable** - Structure queries to enable effective caching

### Handler Implementation
- **Async** - Use async/await for non-blocking operations
- **Error handling** - Implement comprehensive error handling
- **Logging** - Add structured logging for debugging
- **Metrics** - Include performance metrics and monitoring

## Yemen-Specific Patterns

### Currency-Aware Commands

```python
class RunCurrencyAwareAnalysis(Command):
    """Command that properly handles multiple currency zones."""
    
    def __init__(self, currency_conversion_strategy: str = "dynamic_rates"):
        self.currency_conversion_strategy = currency_conversion_strategy
        # Always validate currency conversion approach
        self._validate_currency_strategy()
```

### Conflict-Aware Queries

```python
class GetConflictAdjustedPrices(Query):
    """Query that accounts for conflict-related data quality issues."""
    
    def __init__(self, include_imputed_data: bool = True):
        self.include_imputed_data = include_imputed_data
        # Handle missing data patterns due to conflict
```

### Three-Tier Analysis Commands

```python
class RunTierAnalysis(Command):
    """Base command for tier-specific analysis."""
    
    tier: int
    spatial_weights: Optional[str] = None
    currency_zones: List[str] = field(default_factory=list)
    conflict_controls: bool = True
```

## Related Documentation

- [Application Architecture](../01-architecture/components.md)
- [Three-Tier Framework](./three-tier-implementation.md)
- [Event Handling](./event-handling.md)
- [Core Domain Models](../12-core-reference/domain-models.md)