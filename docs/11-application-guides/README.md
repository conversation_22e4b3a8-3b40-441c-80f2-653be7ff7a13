# Application Layer Guides

This section contains documentation for the application layer components that orchestrate the Yemen Market Integration analysis workflows.

## Contents

### Analysis Tiers
- **Three-Tier Framework**: Implementation guides for the three-tier econometric approach
- **Tier 1 Analysis**: Pooled panel analysis with ML clustering and Bayesian methods
- **Tier 2 Analysis**: Commodity-specific analysis with regime-switching
- **Tier 3 Analysis**: Validation framework with nowcasting capabilities

### Commands and Queries
- **Command Patterns**: CQRS implementation for analysis workflows
- **Query Processing**: Data retrieval and filtering strategies
- **Event Handling**: Analysis event processing and coordination

### Service Integration
- **Application Services**: Business logic and workflow coordination
- **Service Composition**: Orchestrating complex analysis pipelines
- **Error Handling**: Resilient operation patterns

## Quick Start

```python
from src.application.commands import AnalyzeMarketIntegration
from src.application.analysis_tiers import Tier1Runner

# Run three-tier analysis
command = AnalyzeMarketIntegration(
    data_source="wfp_processed",
    analysis_type="three_tier",
    currency_zones=["houthi", "government"]
)

# Execute tier 1 analysis
tier1 = Tier1Runner()
results = tier1.execute(command.data)
```

## Related Documentation

- [SRC Architecture Overview](../01-architecture/overview.md)
- [Core Domain Reference](../12-core-reference/README.md)
- [API Integration](../14-interfaces-integration/README.md)