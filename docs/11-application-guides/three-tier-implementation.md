# Three-Tier Analysis Implementation Guide

The Yemen Market Integration system implements a sophisticated three-tier econometric framework for comprehensive market integration analysis in conflict settings.

## Overview

The three-tier approach provides:
1. **Tier 1**: Pooled panel analysis with currency zone controls
2. **Tier 2**: Commodity-specific analysis with regime-switching
3. **Tier 3**: Validation framework with spatial models

Each tier builds upon the previous one, providing increasingly sophisticated analysis while maintaining methodological rigor.

## Tier 1: Pooled Panel Analysis

### Purpose
- Establish baseline market integration patterns
- Control for currency zone effects
- Identify general trends across all markets and commodities

### Implementation

```python
from src.application.analysis_tiers import Tier1Runner
from src.application.commands import RunThreeTierAnalysis

class Tier1Runner:
    """Handles pooled panel regression analysis."""
    
    async def run(self, command: RunThreeTierAnalysis, analysis_id: str) -> Dict[str, Any]:
        """Execute Tier 1 analysis."""
        
        # Load and prepare panel data
        panel_data = await self._load_panel_data(command)
        
        # Configure model specification
        spec = ModelSpecification(
            model_type="panel_regression",
            dependent_variable="log_price",
            independent_variables=[
                "distance_to_main_market",
                "conflict_intensity",
                "currency_zone",
                "aid_presence"
            ],
            fixed_effects=["market_id", "time_period"],
            cluster_variables=["governorate", "month"]
        )
        
        # Estimate model
        results = await self.estimator_service.estimate(spec, panel_data)
        
        return {
            "coefficients": results.coefficients,
            "standard_errors": results.standard_errors,
            "diagnostics": results.diagnostics,
            "r_squared": results.r_squared
        }
```

### Key Features
- **Currency conversion validation**: Ensures proper USD/YER handling
- **Missing data imputation**: Conflict-aware imputation strategies
- **Robust standard errors**: Clustered by governorate and time
- **Fixed effects**: Market and time fixed effects

## Tier 2: Commodity-Specific Analysis

### Purpose
- Analyze commodity-specific integration patterns
- Detect structural breaks and regime changes
- Account for commodity characteristics

### Implementation

```python
from src.application.analysis_tiers import Tier2Runner

class Tier2Runner:
    """Handles commodity-specific analysis."""
    
    async def run(self, command: RunThreeTierAnalysis, analysis_id: str) -> Dict[str, Any]:
        """Execute Tier 2 analysis for each commodity."""
        
        results = {}
        
        for commodity_id in command.commodity_ids:
            # Load commodity-specific data
            commodity_data = await self._load_commodity_data(commodity_id, command)
            
            # Test for regime switching
            regime_results = await self._test_regime_switching(commodity_data)
            
            # Estimate commodity-specific model
            model_results = await self._estimate_commodity_model(
                commodity_data, regime_results
            )
            
            results[commodity_id] = {
                "regime_switching": regime_results,
                "integration_coefficients": model_results.coefficients,
                "structural_breaks": model_results.structural_breaks,
                "persistence": model_results.persistence_measures
            }
        
        return results
```

### Key Features
- **Regime-switching models**: Markov-switching vector error correction models
- **Structural break detection**: Bai-Perron tests for multiple breaks
- **Commodity characteristics**: Storage, perishability, import dependence
- **Threshold effects**: Hansen threshold models for non-linear integration

## Tier 3: Validation Framework

### Purpose
- Validate findings through spatial models
- Cross-validate with external data sources
- Generate policy-relevant insights

### Implementation

```python
from src.application.analysis_tiers import Tier3Runner

class Tier3Runner:
    """Handles validation and spatial analysis."""
    
    async def run(self, command: RunThreeTierAnalysis, analysis_id: str) -> Dict[str, Any]:
        """Execute Tier 3 validation analysis."""
        
        # Spatial weight matrix construction
        spatial_weights = await self._construct_spatial_weights(command.market_ids)
        
        # Spatial panel models
        spatial_results = await self._estimate_spatial_models(
            command, spatial_weights
        )
        
        # Cross-validation with external sources
        validation_results = await self._cross_validate_external(command)
        
        # Nowcasting and forecasting
        forecast_results = await self._generate_forecasts(command)
        
        return {
            "spatial_analysis": spatial_results,
            "external_validation": validation_results,
            "forecasts": forecast_results,
            "robustness_checks": await self._robustness_testing(command)
        }
```

### Key Features
- **Spatial econometrics**: Spatial lag and error models
- **Cross-validation**: External data source validation
- **Nowcasting**: Real-time integration monitoring
- **Robustness testing**: Comprehensive sensitivity analysis

## Configuration Management

### Tier 1 Configuration
```python
tier1_config = {
    "log_transform": True,
    "include_weather": True,
    "fixed_effects": ["market", "time"],
    "cluster_variables": ["governorate", "month"],
    "currency_conversion": "dynamic_rates"
}
```

### Tier 2 Configuration
```python
tier2_config = {
    "regime_switching": True,
    "max_regimes": 3,
    "structural_breaks": True,
    "max_breaks": 5,
    "threshold_models": True
}
```

### Tier 3 Configuration
```python
tier3_config = {
    "spatial_weights": "queen_contiguity",
    "spatial_models": ["lag", "error", "durbin"],
    "cross_validation": True,
    "nowcasting": True,
    "forecast_horizon": 6
}
```

## Error Handling and Logging

```python
import logging
from src.infrastructure.logging import get_logger

logger = get_logger(__name__)

class TierRunner:
    """Base class for tier runners."""
    
    async def run_with_error_handling(self, command, analysis_id):
        """Run analysis with comprehensive error handling."""
        try:
            await self.orchestrator.update_progress(
                analysis_id, self.tier_name, 0, "Starting analysis..."
            )
            
            results = await self.run(command, analysis_id)
            
            await self.orchestrator.update_progress(
                analysis_id, self.tier_name, 100, "Analysis completed"
            )
            
            return results
            
        except CurrencyConversionError as e:
            logger.error(f"Currency conversion error in {self.tier_name}: {e}")
            raise AnalysisError(f"Currency validation failed: {e}")
            
        except DataQualityError as e:
            logger.error(f"Data quality error in {self.tier_name}: {e}")
            raise AnalysisError(f"Data quality check failed: {e}")
            
        except Exception as e:
            logger.error(f"Unexpected error in {self.tier_name}: {e}")
            await self.orchestrator.update_progress(
                analysis_id, self.tier_name, -1, f"Error: {str(e)}"
            )
            raise
```

## Progress Tracking

```python
class AnalysisOrchestrator:
    """Orchestrates multi-tier analysis with progress tracking."""
    
    async def run_three_tier_analysis(self, command: RunThreeTierAnalysis) -> str:
        """Run complete three-tier analysis."""
        analysis_id = await self._create_analysis_session(command)
        
        try:
            # Tier 1: Pooled analysis
            tier1_results = await self.tier1_runner.run(command, analysis_id)
            await self._save_tier_results(analysis_id, "tier1", tier1_results)
            
            # Tier 2: Commodity-specific analysis
            tier2_results = await self.tier2_runner.run(command, analysis_id)
            await self._save_tier_results(analysis_id, "tier2", tier2_results)
            
            # Tier 3: Validation framework
            tier3_results = await self.tier3_runner.run(command, analysis_id)
            await self._save_tier_results(analysis_id, "tier3", tier3_results)
            
            # Combine and finalize results
            final_results = await self._combine_tier_results(
                analysis_id, tier1_results, tier2_results, tier3_results
            )
            
            await self.update_progress(analysis_id, "complete", 100, "Analysis completed")
            return analysis_id
            
        except Exception as e:
            await self.update_progress(analysis_id, "error", -1, str(e))
            raise
```

## Currency Validation (Critical)

```python
class CurrencyValidator:
    """Ensures proper currency handling across all tiers."""
    
    def validate_currency_consistency(self, data: pd.DataFrame) -> None:
        """Validate currency consistency in panel data."""
        
        # Check for mixed currencies in same analysis
        currencies = data['currency'].unique()
        if len(currencies) > 1:
            logger.warning(f"Multiple currencies detected: {currencies}")
            
        # Validate conversion rates
        if 'usd_price' in data.columns and 'yer_price' in data.columns:
            self._validate_conversion_rates(data)
            
        # Check for extreme values indicating conversion errors
        self._check_extreme_values(data)
    
    def _validate_conversion_rates(self, data: pd.DataFrame) -> None:
        """Validate YER to USD conversion rates."""
        data['implied_rate'] = data['yer_price'] / data['usd_price']
        
        # Check for reasonable exchange rate ranges
        median_rate = data['implied_rate'].median()
        if not (500 <= median_rate <= 2500):
            raise CurrencyConversionError(
                f"Suspicious exchange rate detected: {median_rate}"
            )
```

## Performance Optimization

```python
class PerformanceOptimizer:
    """Optimizes three-tier analysis performance."""
    
    async def optimize_data_loading(self, command: RunThreeTierAnalysis) -> pd.DataFrame:
        """Load data efficiently for three-tier analysis."""
        
        # Parallel data loading
        tasks = [
            self._load_price_data(command),
            self._load_market_data(command),
            self._load_conflict_data(command)
        ]
        
        price_data, market_data, conflict_data = await asyncio.gather(*tasks)
        
        # Efficient merging strategy
        return await self._efficient_merge(price_data, market_data, conflict_data)
```

## Related Documentation

- [Command and Query Patterns](./command-query-patterns.md)
- [Service Integration](./service-integration.md)
- [Core Domain Models](../12-core-reference/domain-models.md)
- [Econometric Framework](../05-methodology/econometric-models/)