# Exchange Rate Data Sources in Yemen Market Integration

## Current Situation

### 1. **Panel Data Exchange Rates** (data/processed/integrated_panel/)
- **Source**: Implied from WFP price data
- **Calculation**: `exchange_rate = YER_price / USD_price`
- **Range**: 243-2500 YER/USD (mostly 250)
- **Issue**: Shows very little variation because USD prices were calculated using a fixed 250 YER/USD rate

### 2. **WFP Exchange Rate Data** (data/processed/wfp_exchange_rates.parquet)
- **Source**: WFP's actual exchange rate monitoring
- **Format**: USD per 1000 YER (e.g., 2.14 means $2.14 buys 1000 YER)
- **Contains**:
  - `official_rate`: Official CBY rate (in YER/USD)
  - `parallel_rate`: Black market rate (in USD/1000 YER)
  - `exchange_rate`: Actual market rate (in USD/1000 YER)
- **Coverage**: 2019-2025, 1609 observations
- **By Zone**:
  - DFA (North): ~2.28 USD/1000 YER = ~438 YER/USD
  - IRG (South): ~4.61 USD/1000 YER = ~217 YER/USD

## The Problem

The panel data has exchange rates that are mostly constant (250) because:
1. WFP originally reported prices in both YER and USD
2. USD prices were calculated using a fixed 250 YER/USD rate
3. When we calculate implied ER = YER/USD, we get back 250

This explains why:
- Exchange rate variation is minimal
- FE models show negative coefficients (mechanical relationship)
- The data doesn't capture the actual exchange rate fragmentation

## The Reality (from WFP Exchange Rate Data)

### North (Houthi/DFA areas):
- Stronger currency: ~438 YER/USD
- Lower nominal prices but potentially higher real costs

### South (Government/IRG areas):
- Weaker currency: ~217 YER/USD  
- Higher nominal prices but potentially lower real costs

## Data Pipeline Issues

1. **Exchange Rate Not Merged**: The actual WFP exchange rate data exists but wasn't merged with price data
2. **Fixed Rate Assumption**: USD prices calculated with fixed 250 rate, not actual market rates
3. **Zone Misalignment**: Exchange rates vary by zone, but this wasn't incorporated

## Solution Required

### Short-term:
1. Use the WFP exchange rate data (already available)
2. Recalculate USD prices using actual zone-specific exchange rates
3. Test zone effects on properly calculated USD prices

### Long-term:
1. Integrate ExchangeRateCollectorV2 for real-time rates
2. Use multiple sources (XE, OANDA, CBY, parallel markets)
3. Implement proper zone-based exchange rate assignment

## Key Insight

The "negative coefficient" problem isn't a bug - it's revealing that:
- The panel data uses mostly fixed exchange rates
- Real exchange rate variation by zone isn't captured
- We need to use the actual WFP exchange rate data, not implied rates

## Next Steps

1. **Merge WFP exchange rates** with panel data by date and market
2. **Recalculate USD prices** using actual zone-specific rates
3. **Re-run analysis** with proper exchange rate variation
4. **Test hypothesis H1** about exchange rate fragmentation effects

The exchange rate data EXISTS - it just needs to be properly integrated!