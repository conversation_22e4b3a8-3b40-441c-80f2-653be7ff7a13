# Nowcasting and Early Warning System Guide

## Overview

The nowcasting module provides real-time price prediction capabilities for Yemen's fragmented markets, enabling humanitarian organizations to anticipate price changes and prepare interventions before crises occur. This is particularly critical in conflict settings where market disruptions can rapidly escalate into humanitarian emergencies.

## Key Features

1. **Multiple Nowcasting Methods**
   - Dynamic Factor Models for panel-wide trends
   - SARIMAX for individual time series with seasonality
   - Machine Learning ensemble methods (Random Forest, Gradient Boosting)
   - Ensemble combining multiple approaches

2. **Early Warning System**
   - Automated detection of price spike risks
   - Severity-based alert system (Low → Medium → High → Critical)
   - Recommended actions for each warning level
   - Integration breakdown detection

3. **Currency Zone Awareness**
   - All predictions in USD (methodology requirement)
   - Zone-specific effects incorporated
   - Exchange rate risk quantification

## Quick Start

### Basic Nowcasting Example

```python
from src.infrastructure.estimators.nowcasting_estimators import NowcastingOrchestrator
import pandas as pd

# Initialize orchestrator
orchestrator = NowcastingOrchestrator()

# Run nowcasting (data must have 'usd_price' column)
results = await orchestrator.run_comprehensive_nowcasting(
    panel_data=your_panel_data,
    markets=['Sana\'a', 'Aden', 'Taiz'],
    commodities=['wheat_flour', 'rice'],
    forecast_horizon=3,  # 3 months ahead
    generate_warnings=True
)

# Access warnings
if results['warnings']:
    for warning in results['warnings']:
        print(f"{warning.severity}: {warning.market_id}/{warning.commodity}")
        print(f"Action: {warning.recommended_action}")
```

### Individual Model Usage

```python
from src.core.models.nowcasting import SARIMAXNowcast

# Create and fit model
model = SARIMAXNowcast(
    order=(1, 1, 1),
    seasonal_order=(1, 1, 1, 12),
    forecast_horizon=3
)

model.fit(time_series_data)
nowcast = model.predict()

# Access results
print(f"Next month forecast: ${nowcast.point_forecast.iloc[0]:.2f}")
print(f"95% CI: ${nowcast.prediction_intervals[0.95]['lower'].iloc[0]:.2f} - "
      f"${nowcast.prediction_intervals[0.95]['upper'].iloc[0]:.2f}")
```

## Nowcasting Methods

### 1. Dynamic Factor Model (DFM)

Best for: Capturing common trends across multiple markets and commodities.

```python
from src.core.models.nowcasting import DynamicFactorNowcast

dfm = DynamicFactorNowcast(
    n_factors=3,        # Number of common factors
    factor_order=1,     # AR order for factors
    forecast_horizon=3
)

dfm.fit(panel_data)
result = dfm.predict()
```

**When to use:**
- You have data from multiple markets
- You want to identify common shocks affecting all markets
- Missing data is a concern (DFM handles missing values well)

### 2. SARIMAX

Best for: Individual market-commodity pairs with seasonal patterns.

```python
from src.core.models.nowcasting import SARIMAXNowcast

sarimax = SARIMAXNowcast(
    order=(1, 1, 1),              # ARIMA(p,d,q)
    seasonal_order=(1, 1, 1, 12), # Seasonal(P,D,Q,s)
    auto_order=True,              # Auto-select orders
    forecast_horizon=3
)

sarimax.fit(time_series_data, exog=conflict_data)
result = sarimax.predict(exog_future=future_conflict_data)
```

**When to use:**
- Clear seasonal patterns (e.g., Ramadan effects)
- Single time series with good historical data
- You have exogenous predictors (conflict events, exchange rates)

### 3. Machine Learning Methods

Best for: Non-linear relationships and interaction effects.

```python
from src.core.models.nowcasting import MachineLearningNowcast

ml_model = MachineLearningNowcast(
    model_type='random_forest',  # or 'gradient_boosting', 'xgboost'
    forecast_horizon=1,
    n_estimators=100,
    max_depth=10
)

ml_model.fit(panel_data, feature_cols=['conflict_intensity', 'currency_zone'])
result = ml_model.predict()

# Check feature importance
print(result.metadata['feature_importance'])
```

**When to use:**
- Complex non-linear relationships
- Many potential predictors
- Short-term (1-2 period) forecasts

### 4. Ensemble Methods

Best for: Robust predictions combining multiple approaches.

```python
from src.core.models.nowcasting import EnsembleNowcast

ensemble = EnsembleNowcast(
    models=None,  # Uses default model set
    weight_method='performance',  # Weights based on CV performance
    forecast_horizon=3
)

ensemble.fit(panel_data)
result = ensemble.predict()

# Check model weights
print(result.metadata['weights'])
```

## Early Warning System

The Early Warning System translates nowcast results into actionable humanitarian alerts.

### Warning Thresholds

Default thresholds by commodity type:

| Commodity | Low | Medium | High | Critical |
|-----------|-----|--------|------|----------|
| Wheat Flour | 20% | 50% | 100% | 200% |
| Rice | 20% | 50% | 100% | 200% |
| Fuel | 15% | 30% | 50% | 100% |
| Other | 25% | 50% | 100% | 150% |

### Customizing Thresholds

```python
from src.core.models.nowcasting import EarlyWarningSystem

# Custom thresholds
thresholds = {
    'wheat_flour': {
        'low': 1.15,      # 15% increase triggers low warning
        'medium': 1.3,    # 30% increase
        'high': 1.5,      # 50% increase
        'critical': 2.0   # 100% increase
    }
}

ews = EarlyWarningSystem(
    nowcast_model=your_model,
    thresholds=thresholds
)

warnings = ews.generate_warnings(
    nowcast_results=results,
    baseline_prices=baseline_data
)
```

### Warning Response Actions

The system provides recommended actions for each severity level:

**Low Severity:**
- Monitor situation closely
- Review supply chain indicators
- Prepare contingency plans

**Medium Severity:**
- Activate enhanced monitoring
- Adjust cash transfer values
- Engage with local suppliers

**High Severity:**
- Implement cash transfer increases
- Consider market support interventions
- Prepare for potential distribution

**Critical Severity:**
- Emergency response activation
- Direct distribution consideration
- Full humanitarian intervention

## Integration with Three-Tier Analysis

Nowcasting is integrated into Tier 3 validation:

```python
# In tier3_config
tier3_config = {
    "validation_methods": ["cross_validation", "structural_break"],
    "nowcasting": True,  # Enable nowcasting
    "factor_analysis": True,
    "pca_analysis": True
}

# Results will include nowcasting
tier3_results = await tier3_runner.run(
    command=command,
    analysis_id=analysis_id,
    tier1_result=tier1_result,
    tier2_results=tier2_results
)

# Access nowcast results
nowcast_results = tier3_results['nowcasting']
warnings = nowcast_results['warnings']
```

## Operational Dashboard Export

Generate operational dashboards for field teams:

```python
# After running nowcasting
if 'warning_dashboard' in results:
    dashboard = results['warning_dashboard']
    
    # Export for operations
    dashboard.to_csv('early_warnings.csv')
    
    # Filter critical warnings
    critical = dashboard[dashboard['severity'] == 'critical']
    print(f"URGENT: {len(critical)} critical warnings require immediate action")
```

## Best Practices

1. **Data Quality**
   - Ensure all prices are converted to USD before nowcasting
   - Validate currency zones are correctly assigned
   - Check for outliers that might skew predictions

2. **Model Selection**
   - Use ensemble methods for critical decisions
   - Validate on recent out-of-sample data
   - Consider conflict dynamics in model choice

3. **Warning Calibration**
   - Adjust thresholds based on local context
   - Consider humanitarian capacity when setting thresholds
   - Regular back-testing of warning accuracy

4. **Operational Integration**
   - Automate warning generation for regular monitoring
   - Integrate with existing humanitarian information systems
   - Establish clear escalation procedures

## Troubleshooting

### Common Issues

**"CRITICAL: All prices must be converted to USD"**
- Run MethodologyValidator before nowcasting
- Ensure exchange_rate_collector_v2 is properly configured

**"No models produced valid predictions"**
- Check data has sufficient history (minimum 24 months recommended)
- Verify no extreme outliers in recent data
- Ensure time series is not all missing values

**"Warning thresholds seem too sensitive/insensitive"**
- Adjust thresholds based on historical price volatility
- Consider zone-specific thresholds for contested areas
- Review baseline period selection

## API Reference

See the detailed API documentation:
- [Nowcasting Models API](../03-api-reference/models/nowcasting.md)
- [Nowcasting Estimators API](../03-api-reference/infrastructure/nowcasting_estimators.md)

## Examples

Full working examples available in:
- `examples/nowcasting_example.py` - Comprehensive demonstration
- `examples/test_nowcasting.py` - Quick functionality test
- `notebooks/07_nowcasting_analysis.ipynb` - Interactive exploration