# Currency-Aware ML Clustering Guide

## Overview

The Yemen Market Integration project includes a specialized ML clustering feature that respects currency zone boundaries. This is critical for proper analysis in Yemen's fragmented exchange rate environment (North: ~535 YER/USD, South: ~2000+ YER/USD).

## Key Features

1. **Hard Currency Zone Constraints**: Markets from different currency zones NEVER cluster together
2. **Zone-Specific Normalization**: Features normalized within each zone separately
3. **Automatic Integration**: Seamlessly adds cluster variables to panel models
4. **Methodology Compliance**: Enforces USD conversion before clustering

## Usage

### Enabling Clustering in Tier 1 Analysis

```python
from src.application.commands.run_three_tier_analysis import RunThreeTierAnalysisCommand

command = RunThreeTierAnalysisCommand(
    start_date=datetime(2023, 1, 1),
    end_date=datetime(2023, 12, 31),
    commodity_codes=['wheat', 'sugar'],
    tier1_config={
        "model": "two_way_fixed_effects",
        "se_type": "driscoll_kraay",
        "log_transform": True,
        "use_clustering": True,  # Enable clustering
        "clustering_config": {
            "n_clusters_per_zone": {
                "HOUTHI": 3,      # 3 clusters in northern zone
                "GOVERNMENT": 4,   # 4 clusters in southern zone
                "CONTESTED": 2     # 2 clusters in contested areas
            }
        }
    }
)
```

### Automatic Cluster Selection

If you don't specify the number of clusters, the algorithm will automatically determine the optimal number:

```python
tier1_config={
    "use_clustering": True,
    "clustering_config": {}  # Empty config = automatic selection
}
```

## How It Works

1. **Data Preparation**: Panel data is aggregated by market to create features
2. **Zone Separation**: Markets are strictly separated by currency zone
3. **Feature Engineering**: Creates price, conflict, and geographic features
4. **Within-Zone Clustering**: K-means clustering applied within each zone
5. **Integration**: Cluster assignments added as control variables

## Features Used for Clustering

- **Price Features** (all in USD):
  - Average price level
  - Price volatility
  - Price trend
  
- **Exchange Rate Features**:
  - Average exchange rate
  - Exchange rate volatility
  - Exchange rate trend
  
- **Conflict Features**:
  - Conflict intensity
  - Conflict volatility
  - Control stability
  
- **Geographic Features**:
  - Distance to capital
  - Distance to port
  - Distance to border

## Interpreting Results

### Clustering Results Object

```python
results = tier1_analysis["clustering"]

# Total clusters across all zones
print(f"Total clusters: {results.total_clusters}")

# Clusters by zone
print(f"Northern zone clusters: {results.clusters_by_zone['HOUTHI']}")
print(f"Southern zone clusters: {results.clusters_by_zone['GOVERNMENT']}")

# Quality metrics
print(f"Overall silhouette score: {results.overall_silhouette}")
print(f"Zone silhouette scores: {results.within_zone_silhouette}")

# Feature importance
for zone, importance in results.feature_importance_by_zone.items():
    print(f"\n{zone} zone top features:")
    for feature, score in sorted(importance.items(), key=lambda x: x[1], reverse=True)[:3]:
        print(f"  - {feature}: {score:.3f}")
```

### Model Coefficients

When clustering is enabled, the panel model includes cluster indicators:

```python
model_result = tier1_analysis["result"]

# Cluster effect on prices
cluster_coef = model_result.params.get("cluster", 0)
cluster_pval = model_result.pvalues.get("cluster", 1)

print(f"Cluster effect: {cluster_coef:.4f} (p={cluster_pval:.4f})")
```

## Best Practices

1. **Always Validate Data First**: Ensure methodology validation passes before clustering
2. **Consider Sample Size**: Need at least 5 markets per cluster
3. **Check Stability**: Use `cluster_stability` scores to assess temporal consistency
4. **Compare Models**: Run with and without clustering to assess impact
5. **Zone Awareness**: Remember clusters are zone-specific - cluster 1 in North ≠ cluster 1 in South

## Example: Full Analysis with Clustering

```python
import asyncio
from src.application.services.three_tier_analysis_service import ThreeTierAnalysisService
from src.shared.container import create_container

async def run_analysis_with_clustering():
    # Set up dependencies
    container = create_container()
    service = container.three_tier_service()
    
    # Configure analysis with clustering
    config = {
        'tier1': {
            'model': 'two_way_fixed_effects',
            'use_clustering': True,
            'clustering_config': {
                'n_clusters_per_zone': {'HOUTHI': 3, 'GOVERNMENT': 4}
            }
        },
        'tier2': {'model': 'threshold_vecm'},
        'tier3': {'validation_methods': ['cross_validation']}
    }
    
    # Run analysis
    results = await service.run_analysis(
        start_date=datetime(2023, 1, 1),
        end_date=datetime(2023, 12, 31),
        commodity_codes=['wheat'],
        config=config
    )
    
    # Access clustering results
    clustering = results['tier1']['clustering']
    print(f"Created {clustering.total_clusters} market clusters")
    print(f"Methodology compliant: {clustering.methodology_compliance}")
    
    return results

# Run the analysis
results = asyncio.run(run_analysis_with_clustering())
```

## Troubleshooting

### "MethodologyViolation: Missing USD prices"
- Ensure all price data has been converted to USD before clustering
- Check that exchange rates are available for all observations

### "ValueError: Zone X has only Y markets"
- Need at least 5 markets per zone for clustering
- Consider reducing number of clusters or excluding small zones

### "Clustering failed: proceeding without clustering"
- Check logs for specific error
- Verify data has required fields (price_usd, currency_zone, exchange_rate_used)
- Ensure sufficient variance in features for clustering

## Technical Details

The implementation uses:
- **scikit-learn** for K-means clustering
- **Hard constraints** enforced through zone separation
- **Silhouette analysis** for cluster quality assessment
- **Random Forest** for feature importance calculation

For implementation details, see:
- `src/core/models/machine_learning/currency_aware_clustering.py`
- `src/application/analysis_tiers/tier1_runner.py` (integration)

## Why This Matters

Currency-aware clustering is essential because:

1. **Different Economic Realities**: Northern markets (535 YER/USD) face fundamentally different conditions than Southern markets (2000+ YER/USD)
2. **Aid Effectiveness**: Humanitarian interventions must account for zone-specific market dynamics
3. **Price Transmission**: Understanding how shocks propagate within zones informs policy
4. **Market Integration**: Identifies which markets move together for targeted interventions

Remember: The "Yemen Paradox" shows that nominal prices can be deeply misleading. Proper clustering ensures your analysis respects the economic reality of currency fragmentation.