# Core Domain Models

The core domain models represent the fundamental business concepts and data structures in the Yemen Market Integration system.

## Overview

The domain models are organized into several key areas:
- **Market Integration Models**: Core econometric model interfaces and results
- **Data Models**: Price data, market information, and spatial relationships  
- **Validation Models**: Data quality and model validation structures
- **Reporting Models**: Analysis results and output formatting

## Model Result Framework

### Base Model Result

All econometric models return results using a standardized interface:

```python
from src.core.models.interfaces import ModelResult
from datetime import datetime

@dataclass
class ModelResult:
    """Base class for all econometric model results."""
    
    model_type: str
    estimation_date: datetime
    n_observations: int
    parameters: Dict[str, float]
    standard_errors: Dict[str, float]
    t_statistics: Dict[str, float]
    p_values: Dict[str, float]
    r_squared: Optional[float] = None
    adjusted_r_squared: Optional[float] = None
    log_likelihood: Optional[float] = None
    aic: Optional[float] = None
    bic: Optional[float] = None
    diagnostics: Dict[str, Any] = None
    metadata: Dict[str, Any] = None
    
    def is_significant(self, param: str, alpha: float = 0.05) -> bool:
        """Check if parameter is statistically significant."""
        return self.p_values.get(param, 1.0) < alpha
    
    def get_confidence_interval(self, param: str, alpha: float = 0.05) -> Tuple[float, float]:
        """Get confidence interval for parameter."""
        # Implementation details...
```

### Usage Example

```python
# Check parameter significance
if result.is_significant("currency_zone_effect"):
    print("Currency zone effect is statistically significant")

# Get confidence intervals
lower, upper = result.get_confidence_interval("integration_coefficient")
print(f"95% CI: [{lower:.3f}, {upper:.3f}]")
```

## Econometric Model Interfaces

### Panel Model Interface

```python
from abc import ABC, abstractmethod

class PanelModel(ABC):
    """Abstract base class for panel econometric models."""
    
    @abstractmethod
    async def estimate(
        self, 
        data: pd.DataFrame, 
        specification: ModelSpecification
    ) -> ModelResult:
        """Estimate the panel model."""
        pass
    
    @abstractmethod
    def validate_data(self, data: pd.DataFrame) -> ValidationResult:
        """Validate input data for the model."""
        pass
    
    @abstractmethod
    def generate_diagnostics(self, result: ModelResult) -> Dict[str, Any]:
        """Generate diagnostic tests for the model."""
        pass
```

### Spatial Model Interface

```python
class SpatialModel(ABC):
    """Abstract base class for spatial econometric models."""
    
    @abstractmethod
    async def estimate_spatial_lag(
        self,
        data: pd.DataFrame,
        spatial_weights: np.ndarray,
        specification: ModelSpecification
    ) -> SpatialModelResult:
        """Estimate spatial lag model."""
        pass
    
    @abstractmethod
    async def estimate_spatial_error(
        self,
        data: pd.DataFrame,
        spatial_weights: np.ndarray,
        specification: ModelSpecification
    ) -> SpatialModelResult:
        """Estimate spatial error model."""
        pass
```

## Data Value Objects

### Currency Value Object

```python
from dataclasses import dataclass
from enum import Enum

class CurrencyType(Enum):
    YER = "YER"
    USD = "USD"

@dataclass(frozen=True)
class Currency:
    """Represents currency information with validation."""
    
    currency_type: CurrencyType
    exchange_rate: Optional[float] = None
    rate_date: Optional[datetime] = None
    currency_zone: Optional[str] = None
    
    def __post_init__(self):
        """Validate currency data."""
        if self.currency_type == CurrencyType.YER:
            if not self.currency_zone:
                raise ValueError("YER currency must specify currency zone")
            if self.exchange_rate and not (500 <= self.exchange_rate <= 2500):
                raise ValueError(f"Suspicious YER exchange rate: {self.exchange_rate}")
    
    def to_usd(self, amount: float) -> float:
        """Convert amount to USD."""
        if self.currency_type == CurrencyType.USD:
            return amount
        if not self.exchange_rate:
            raise ValueError("Exchange rate required for currency conversion")
        return amount / self.exchange_rate
```

### Price Data Model

```python
@dataclass
class PriceObservation:
    """Represents a single price observation."""
    
    market_id: str
    commodity_id: str
    date: datetime
    price: float
    currency: Currency
    unit: str
    source: str
    quality_score: Optional[float] = None
    is_imputed: bool = False
    conflict_affected: bool = False
    
    def to_usd_price(self) -> float:
        """Convert price to USD."""
        return self.currency.to_usd(self.price)
    
    def validate(self) -> List[str]:
        """Validate price observation."""
        errors = []
        
        if self.price <= 0:
            errors.append("Price must be positive")
        
        if self.quality_score and not (0 <= self.quality_score <= 1):
            errors.append("Quality score must be between 0 and 1")
        
        # Yemen-specific validation
        if self.currency.currency_type == CurrencyType.YER:
            if self.price > 10000:  # Suspiciously high YER price
                errors.append("Suspiciously high YER price - check conversion")
        
        return errors
```

### Market Information Model

```python
@dataclass
class Market:
    """Represents a market location."""
    
    market_id: str
    name: str
    governorate: str
    latitude: float
    longitude: float
    currency_zone: str
    population: Optional[int] = None
    infrastructure_index: Optional[float] = None
    conflict_exposure: Optional[float] = None
    
    def distance_to(self, other: 'Market') -> float:
        """Calculate distance to another market in kilometers."""
        from geopy.distance import geodesic
        return geodesic(
            (self.latitude, self.longitude),
            (other.latitude, other.longitude)
        ).kilometers
    
    def is_accessible_from(self, other: 'Market', conflict_data: pd.DataFrame) -> bool:
        """Check if market is accessible from another market."""
        # Implementation would check for road blockages, checkpoints, etc.
        pass
```

## Validation Framework Models

### Data Validation Result

```python
@dataclass
class ValidationResult:
    """Result of data validation checks."""
    
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    metadata: Dict[str, Any]
    
    def add_error(self, message: str) -> None:
        """Add validation error."""
        self.errors.append(message)
        self.is_valid = False
    
    def add_warning(self, message: str) -> None:
        """Add validation warning."""
        self.warnings.append(message)
    
    def summary(self) -> str:
        """Generate validation summary."""
        status = "VALID" if self.is_valid else "INVALID"
        return f"Validation {status}: {len(self.errors)} errors, {len(self.warnings)} warnings"
```

### Model Specification

```python
@dataclass
class ModelSpecification:
    """Specification for econometric models."""
    
    model_type: str
    dependent_variable: str
    independent_variables: List[str]
    fixed_effects: Optional[List[str]] = None
    random_effects: Optional[List[str]] = None
    instruments: Optional[List[str]] = None
    cluster_variables: Optional[List[str]] = None
    spatial_weights: Optional[str] = None
    estimation_method: str = "ols"
    robust_standard_errors: bool = True
    
    def validate(self) -> ValidationResult:
        """Validate model specification."""
        result = ValidationResult(is_valid=True, errors=[], warnings=[], metadata={})
        
        if not self.dependent_variable:
            result.add_error("Dependent variable must be specified")
        
        if not self.independent_variables:
            result.add_error("At least one independent variable must be specified")
        
        # Yemen-specific validation
        if "currency" in self.independent_variables and "currency_zone" not in self.independent_variables:
            result.add_warning("Consider adding currency_zone controls when using currency variables")
        
        return result
```

## Welfare Analysis Models

### Zone Welfare Calculator

```python
@dataclass
class WelfareResult:
    """Results from welfare analysis calculations."""
    
    consumer_surplus_change: float
    producer_surplus_change: float
    deadweight_loss: float
    total_welfare_change: float
    currency_zone: str
    confidence_interval: Tuple[float, float]
    
    def welfare_improvement_percentage(self) -> float:
        """Calculate percentage welfare improvement."""
        return (self.total_welfare_change / abs(self.total_welfare_change)) * 100
```

### Aid Optimization Models

```python
@dataclass
class AidAllocation:
    """Represents optimal aid allocation."""
    
    market_id: str
    commodity_id: str
    recommended_amount: float
    expected_welfare_gain: float
    cost_effectiveness: float
    priority_score: float
    
@dataclass
class AidOptimizationResult:
    """Result of aid optimization analysis."""
    
    allocations: List[AidAllocation]
    total_welfare_gain: float
    total_cost: float
    efficiency_ratio: float
    sensitivity_analysis: Dict[str, Any]
```

## Currency Zone Models

### Exchange Rate Regime

```python
@dataclass
class ExchangeRateRegime:
    """Represents an exchange rate regime in a specific zone."""
    
    zone_id: str
    regime_type: str  # "fixed", "floating", "parallel"
    official_rate: Optional[float]
    parallel_rate: Optional[float]
    rate_volatility: float
    regime_start_date: datetime
    regime_end_date: Optional[datetime] = None
    
    def get_effective_rate(self, transaction_type: str = "commercial") -> float:
        """Get effective exchange rate for transaction type."""
        if transaction_type == "parallel":
            return self.parallel_rate or self.official_rate
        return self.official_rate
```

## Usage Examples

### Creating a Price Panel

```python
from src.core.models import PriceObservation, Currency, CurrencyType

# Create price observations
observations = []
for market_id in ["aden", "sanaa"]:
    for date in pd.date_range("2023-01-01", "2023-12-31", freq="M"):
        price_obs = PriceObservation(
            market_id=market_id,
            commodity_id="wheat",
            date=date,
            price=100.0,  # Will be validated
            currency=Currency(CurrencyType.USD),
            unit="kg",
            source="wfp"
        )
        
        # Validate observation
        errors = price_obs.validate()
        if not errors:
            observations.append(price_obs)
```

### Running Model Estimation

```python
from src.core.models import ModelSpecification

# Define model specification
spec = ModelSpecification(
    model_type="panel_regression",
    dependent_variable="log_price_usd",
    independent_variables=[
        "distance_to_port",
        "conflict_intensity", 
        "currency_zone_dummy"
    ],
    fixed_effects=["market_id", "time_period"],
    cluster_variables=["governorate"],
    robust_standard_errors=True
)

# Validate specification
validation = spec.validate()
if validation.is_valid:
    result = await model.estimate(data, spec)
    print(f"R-squared: {result.r_squared:.3f}")
```

## Related Documentation

- [Validation Framework](./validation-framework.md)
- [Reporting System](./reporting-system.md)
- [Application Guides](../11-application-guides/)
- [Methodology Reference](../05-methodology/)