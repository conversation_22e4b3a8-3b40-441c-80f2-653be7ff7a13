# Core Domain Reference

This section documents the core domain models, validation frameworks, and utility functions that form the foundation of the Yemen Market Integration analysis system.

## Contents

### Domain Models
- **Market Integration Models**: Core econometric model implementations
- **Data Models**: Price data, market information, and spatial relationships
- **Result Models**: Analysis results and reporting structures
- **Configuration Models**: System configuration and parameter management

### Validation Framework
- **Data Validation**: Quality assurance for input data
- **Model Validation**: Econometric model diagnostics and testing
- **Result Validation**: Output verification and consistency checks
- **Cross-Validation**: External validation protocols

### Reporting System
- **Report Generation**: Automated report creation and formatting
- **Visualization**: Chart and graph generation for analysis results
- **Export Formats**: LaTeX, Excel, and publication-ready outputs
- **Template Management**: Standardized report templates

### Utility Functions
- **Mathematical Utilities**: Statistical and econometric calculations
- **Data Transformation**: Currency conversion and normalization
- **Spatial Utilities**: Geographic processing and distance calculations
- **Time Series Utilities**: Panel data manipulation and analysis

## Quick Start

```python
from src.core.models import MarketIntegrationModel
from src.core.validation import DataValidator
from src.core.reporting import ReportGenerator

# Validate input data
validator = DataValidator()
is_valid = validator.validate_panel_data(price_data)

# Create and configure model
model = MarketIntegrationModel(
    spatial_weights="queen_contiguity",
    currency_conversion="dynamic_rates"
)

# Generate analysis report
report = ReportGenerator()
report.create_three_tier_report(model.results)
```

## Related Documentation

- [Methodology Reference](../05-methodology/README.md)
- [Application Guides](../11-application-guides/README.md)
- [Infrastructure Operations](../13-infrastructure-ops/README.md)