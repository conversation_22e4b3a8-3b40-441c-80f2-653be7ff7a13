# Currency Zone Analysis Report

*Generated: 2025-06-03 17:41:00*

This report identifies all references to Houthi vs Government controlled areas
and tracks whether proper exchange rate conversion is mentioned.

**Total currency zone references found: 6501**

## Executive Summary

- **Total zone references**: 6501
- **Files with zone references**: 224
- **Files with exchange rate mentions**: 169

### Zone Type Distribution
- Houthi-controlled areas: 153
- Government-controlled areas: 252
- Both zones mentioned: 108
- Unclear zone reference: 5988

### Currency Conversion Coverage
- References with exchange rate context: 1089
- References with currency mentions: 659
- **Coverage gap**: 5412 references lack exchange rate context

## Cross-Reference Matrix

### Files by Zone Type and Exchange Rate Coverage

| Zone Type | With Exchange Rate | Without Exchange Rate |
|-----------|--------------------|-----------------------|
| Houthi | 0 files | 0 files |
| Government | 0 files | 2 files |
| Both | 0 files | 0 files |
| Unclear | 169 files | 53 files |

## Detailed Findings

### High-Risk Files (Zone References Without Exchange Rate Mention)

#### research-methodology-package/03-econometric-methodology/core-methods/threshold-models.md

- Line 936: `markets = ['SANA', 'ADEN', 'TAIZ', 'HODEIDAH']...`
  - Zone: government, Currencies: None

#### research-methodology-package/03-econometric-methodology/identification-strategies/ECONOMETRIC_RESEARCH_PLAN.md

- Line 92: `- [ ] Exclude capital cities (Sana'a, Aden)...`
  - Zone: both, Currencies: None
- Line 101: `- [ ] Duration (months under non-government control)...`
  - Zone: government, Currencies: None

#### research-methodology-package/02-data-infrastructure/transformation-procedures/data_sources.md

- Line 57: `- IRG (Internationally Recognized Government)...`
  - Zone: government, Currencies: None
- Line 100: `- **Actor Information**: Government forces, Houthis, AQAP, ISIS, STC, Coalition...`
  - Zone: government, Currencies: None
- Line 152: `- "Amanat Al Asimah" → "Sana'a City"...`
  - Zone: houthi, Currencies: None

#### research-methodology-package/02-data-infrastructure/transformation-procedures/spatial-matching.md

- Line 980: `('ADEN', 'SANA'),      # Alternative southern route...`
  - Zone: government, Currencies: None
- Line 986: `('ADEN', 'MUKALLA'),...`
  - Zone: government, Currencies: None
- Line 989: `('SANA', 'MARIB'),  # Houthi to Government...`
  - Zone: both, Currencies: None
- *...and 1 more references*

#### research-methodology-package/02-data-infrastructure/transformation-procedures/outlier-detection.md

- Line 871: `port_cities = ['HODEIDAH', 'ADEN', 'MUKALLA']...`
  - Zone: government, Currencies: None
- Line 876: `('ADEN', '2019-08-01', '2019-09-30')...`
  - Zone: government, Currencies: None

#### research-methodology-package/02-data-infrastructure/transformation-procedures/spatial-weights.md

- Line 66: `YE001     | Sana'a City | 15.3694  | 44.1910  | Houthi  | Sana'a...`
  - Zone: houthi, Currencies: None
- Line 67: `YE002     | Aden Port   | 12.7855  | 45.0187  | Gov     | Aden...`
  - Zone: government, Currencies: None

#### research-methodology-package/07-results-templates/policy-briefs/POLICY_BRIEF_TEMPLATE.md

- Line 157: `### For Government...`
  - Zone: government, Currencies: None

#### research-methodology-package/01-theoretical-foundation/theoretical-framework/network-proxies.md

- Line 21: `- Primary hubs: Aden (south), Sana'a (north), Hodeidah (west)...`
  - Zone: both, Currencies: None

#### research-methodology-package/archive/pre-refactoring-backup/01-foundation/theory/network-proxies.md

- Line 21: `- Primary hubs: Aden (south), Sana'a (north), Hodeidah (west)...`
  - Zone: both, Currencies: None

#### research-methodology-package/archive/pre-refactoring-backup/02-data/transformations/data_sources.md

- Line 57: `- IRG (Internationally Recognized Government)...`
  - Zone: government, Currencies: None
- Line 100: `- **Actor Information**: Government forces, Houthis, AQAP, ISIS, STC, Coalition...`
  - Zone: government, Currencies: None
- Line 152: `- "Amanat Al Asimah" → "Sana'a City"...`
  - Zone: houthi, Currencies: None

#### research-methodology-package/archive/pre-refactoring-backup/02-data/transformations/spatial-matching.md

- Line 980: `('ADEN', 'SANA'),      # Alternative southern route...`
  - Zone: government, Currencies: None
- Line 986: `('ADEN', 'MUKALLA'),...`
  - Zone: government, Currencies: None
- Line 989: `('SANA', 'MARIB'),  # Houthi to Government...`
  - Zone: both, Currencies: None
- *...and 1 more references*

#### research-methodology-package/archive/pre-refactoring-backup/02-data/transformations/outlier-detection.md

- Line 871: `port_cities = ['HODEIDAH', 'ADEN', 'MUKALLA']...`
  - Zone: government, Currencies: None
- Line 876: `('ADEN', '2019-08-01', '2019-09-30')...`
  - Zone: government, Currencies: None

#### research-methodology-package/archive/pre-refactoring-backup/02-data/transformations/spatial-weights.md

- Line 66: `YE001     | Sana'a City | 15.3694  | 44.1910  | Houthi  | Sana'a...`
  - Zone: houthi, Currencies: None
- Line 67: `YE002     | Aden Port   | 12.7855  | 45.0187  | Gov     | Aden...`
  - Zone: government, Currencies: None

#### research-methodology-package/archive/pre-refactoring-backup/05-results/main-findings/conflict-spillovers.md

- Line 10: `- **Pre-war hubs**: Sana'a, Aden, Taiz, Hudaydah dominated trade flows...`
  - Zone: both, Currencies: None

#### research-methodology-package/archive/pre-refactoring-backup/04-implementation/code-mappings/network_proxy_implementation.md

- Line 66: `'Aden': (12.7855, 45.0187),          # Major port...`
  - Zone: government, Currencies: None
- Line 68: `"Sana'a": (15.3694, 44.1910),        # Capital...`
  - Zone: houthi, Currencies: None
- Line 114: `'Aden': 0.9,            # Major port city...`
  - Zone: government, Currencies: None
- *...and 1 more references*

#### research-methodology-package/archive/pre-refactoring-backup/04-implementation/diagnostics/robustness-checks.md

- Line 179: `~self.base_data['market_id'].isin(['SANA', 'ADEN'])...`
  - Zone: government, Currencies: None
- Line 663: `urban_markets = ['SANA', 'ADEN', 'TAIZ', 'HODEIDAH']...`
  - Zone: government, Currencies: None

#### research-methodology-package/archive/pre-refactoring-backup/04-implementation/validation/conflict-validation.md

- Line 75: `source_market: str = 'ADEN',  # Main port...`
  - Zone: government, Currencies: None
- Line 852: `if market in ['SANA', 'ADEN', 'TAIZ', 'HODEIDAH']:...`
  - Zone: government, Currencies: None

#### research-methodology-package/archive/pre-refactoring-backup/03-methodology/econometric-models/threshold-models.md

- Line 936: `markets = ['SANA', 'ADEN', 'TAIZ', 'HODEIDAH']...`
  - Zone: government, Currencies: None

#### research-methodology-package/archive/pre-refactoring-backup/03-methodology/identification/ECONOMETRIC_RESEARCH_PLAN.md

- Line 92: `- [ ] Exclude capital cities (Sana'a, Aden)...`
  - Zone: both, Currencies: None
- Line 101: `- [ ] Duration (months under non-government control)...`
  - Zone: government, Currencies: None

#### research-methodology-package/06-implementation-guides/code-examples/network_proxy_implementation.md

- Line 66: `'Aden': (12.7855, 45.0187),          # Major port...`
  - Zone: government, Currencies: None
- Line 68: `"Sana'a": (15.3694, 44.1910),        # Capital...`
  - Zone: houthi, Currencies: None
- Line 114: `'Aden': 0.9,            # Major port city...`
  - Zone: government, Currencies: None
- *...and 1 more references*


### Exchange Rate Values Found

#### 535 YER/USD

- research-methodology-package/01-theoretical-foundation/RESEARCH_QUESTION_EVOLUTION.md:46
- research-methodology-package/00-overview/METHODOLOGICAL_TRANSPARENCY.md:13
- research-methodology-package/00-overview/QUICK_START.md:84
- research-methodology-package/10-context-for-implementation/CRITICAL_IMPLEMENTATION_CHECKLIST.md:16
- research-methodology-package/10-context-for-implementation/README_FOR_SRC_DEVELOPERS.md:13
- *...and 22 more locations*

#### 2,000 YER/USD

- research-methodology-package/01-theoretical-foundation/RESEARCH_QUESTION_EVOLUTION.md:47
- research-methodology-package/00-overview/METHODOLOGICAL_TRANSPARENCY.md:14
- research-methodology-package/08-publication-materials/paper-templates/executive-summary.md:17
- research-methodology-package/archive/pre-refactoring-backup/04-implementation/practical-guides/exchange-rate-data-pipeline.md:14
- research-methodology-package/06-implementation-guides/field-protocols/exchange-rate-data-pipeline.md:14

#### 539 YER/USD

- research-methodology-package/01-theoretical-foundation/PROJECT_CLAUDE.md:10
- research-methodology-package/03-econometric-methodology/identification-strategies/ECONOMETRIC_RESEARCH_PLAN_REVISED.md:7
- research-methodology-package/01-theoretical-foundation/literature-review/integrated-literature-review.md:10
- research-methodology-package/01-theoretical-foundation/literature-review/humanitarian-aid-effects.md:49
- research-methodology-package/01-theoretical-foundation/literature-review/humanitarian-aid-effects.md:277
- *...and 16 more locations*

#### 2,150 YER/USD

- research-methodology-package/01-theoretical-foundation/PROJECT_CLAUDE.md:11
- research-methodology-package/03-econometric-methodology/identification-strategies/ECONOMETRIC_RESEARCH_PLAN_REVISED.md:8
- research-methodology-package/01-theoretical-foundation/literature-review/integrated-literature-review.md:11
- research-methodology-package/01-theoretical-foundation/literature-review/humanitarian-aid-effects.md:48
- research-methodology-package/01-theoretical-foundation/literature-review/demand-destruction.md:94
- *...and 12 more locations*

#### 250 YER/USD

- research-methodology-package/01-theoretical-foundation/literature-review/humanitarian-aid-effects.md:40
- research-methodology-package/archive/pre-refactoring-backup/01-foundation/literature/humanitarian-aid-effects.md:40

#### 850 YER/USD

- research-methodology-package/01-theoretical-foundation/literature-review/humanitarian-aid-effects.md:44
- research-methodology-package/archive/pre-refactoring-backup/01-foundation/literature/humanitarian-aid-effects.md:44

#### 600 YER/USD

- research-methodology-package/01-theoretical-foundation/literature-review/humanitarian-aid-effects.md:45
- research-methodology-package/archive/pre-refactoring-backup/01-foundation/literature/humanitarian-aid-effects.md:45

#### 2,050 YER/USD

- research-methodology-package/01-theoretical-foundation/literature-review/humanitarian-aid-effects.md:61
- research-methodology-package/archive/pre-refactoring-backup/01-foundation/literature/humanitarian-aid-effects.md:61

#### 1,000 YER/USD

- research-methodology-package/archive/phase3-welfare/first_run_Consumer_Surplus_Analysis_Methodology.md:13

#### 3500 YER/USD

- research-methodology-package/archive/pre-refactoring-backup/04-implementation/practical-guides/exchange-rate-data-pipeline.md:68
- research-methodology-package/06-implementation-guides/field-protocols/exchange-rate-data-pipeline.md:68


## Recommendations

### Critical Actions Required

1. **Add exchange rate context** to zone-specific analysis sections
2. **Specify currency conversion method** when comparing prices across zones
3. **Include rate disclaimers** for time-sensitive exchange rate references
4. **Standardize zone terminology** for consistency

### Template Language for Currency Zone References

```markdown
**Northern areas (Houthi-controlled)**: Exchange rate ~535 YER/USD
**Southern areas (Government-controlled)**: Exchange rate ~2,000+ YER/USD

*Note: All price comparisons converted to USD using applicable regional rates*
```

### Files Requiring Immediate Attention

- research-methodology-package/archive/pre-refactoring-backup/05-results/main-findings/exchange-rate-impact.md (Risk Score: 161)
- research-methodology-package/07-results-templates/main-findings/exchange-rate-impact.md (Risk Score: 152)
- research-methodology-package/archive/pre-refactoring-backup/05-results/main-findings/wheat-analysis.md (Risk Score: 74)
- research-methodology-package/archive/pre-refactoring-backup/03-methodology/cross-country-validation/comprehensive-country-implementation-framework.md (Risk Score: 70)
- research-methodology-package/archive/pre-refactoring-backup/03-methodology/cross-country-validation/country-implementation-framework-phase2.md (Risk Score: 70)
- research-methodology-package/archive/pre-refactoring-backup/03-methodology/cross-country-validation/syria-lebanon-somalia-specifications.md (Risk Score: 70)
- research-methodology-package/07-results-templates/main-findings/wheat-analysis.md (Risk Score: 63)
- research-methodology-package/03-econometric-methodology/identification-strategies/ECONOMETRIC_RESEARCH_PLAN_REVISED.md (Risk Score: 50)
- research-methodology-package/01-theoretical-foundation/literature-review/humanitarian-aid-effects.md (Risk Score: 50)
- research-methodology-package/archive/pre-refactoring-backup/01-foundation/literature/humanitarian-aid-effects.md (Risk Score: 50)