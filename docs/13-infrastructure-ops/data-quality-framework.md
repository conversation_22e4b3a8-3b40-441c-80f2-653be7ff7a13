# Data Quality Framework

The Yemen Market Integration system implements a comprehensive data quality framework specifically designed for conflict settings with multiple currency zones.

## Overview

The data quality framework addresses critical challenges in conflict economics:
- **Currency conversion validation** across fragmented exchange rate regimes
- **Conflict-aware missing data imputation** for systematic reporting gaps
- **Zone-specific quality controls** for different territorial control areas
- **Dynamic exchange rate monitoring** for real-time validation

## Architecture

### Data Quality Orchestrator

The central orchestrator coordinates all data quality operations:

```python
from src.infrastructure.data_quality import DataQualityOrchestrator

class DataQualityOrchestrator:
    """Coordinates comprehensive data quality operations."""
    
    def __init__(
        self,
        exchange_rate_validator: DynamicExchangeRateValidator,
        zone_quality_control: ZoneSpecificQualityControl,
        currency_converter: CurrencyConversionTiming,
        imputation_engine: ConflictAwareImputation
    ):
        self.exchange_rate_validator = exchange_rate_validator
        self.zone_quality_control = zone_quality_control
        self.currency_converter = currency_converter
        self.imputation_engine = imputation_engine
    
    async def process_data_quality(
        self,
        raw_data: pd.DataFrame,
        priority: ProcessingPriority = ProcessingPriority.HIGH
    ) -> QualityProcessingResult:
        """Execute comprehensive data quality pipeline."""
        
        # Stage 1: Initial validation
        initial_validation = await self._validate_initial_data(raw_data)
        
        # Stage 2: Exchange rate validation
        exchange_validation = await self.exchange_rate_validator.validate_rates(
            raw_data, priority=priority
        )
        
        # Stage 3: Zone classification and quality control
        zone_quality = await self.zone_quality_control.apply_controls(
            raw_data, exchange_validation
        )
        
        # Stage 4: Currency conversion with timing validation
        converted_data = await self.currency_converter.convert_with_timing(
            zone_quality.data, exchange_validation.validated_rates
        )
        
        # Stage 5: Conflict-aware imputation
        final_data = await self.imputation_engine.impute_missing_data(
            converted_data.data
        )
        
        return QualityProcessingResult(
            processed_data=final_data.data,
            quality_report=self._generate_quality_report(
                initial_validation, exchange_validation, 
                zone_quality, converted_data, final_data
            )
        )
```

### Processing Stages

The framework processes data through eight distinct stages:

1. **Initial Validation**: Basic data structure and format checks
2. **Exchange Rate Validation**: Dynamic rate validation against multiple sources
3. **Zone Classification**: Territorial control area classification
4. **Zone Quality Control**: Zone-specific validation rules
5. **Currency Conversion**: Timing-aware currency conversion
6. **Missing Data Imputation**: Conflict-aware imputation strategies
7. **Final Validation**: Post-processing quality checks
8. **Quality Reporting**: Comprehensive quality metrics and alerts

## Dynamic Exchange Rate Validation

### Real-Time Rate Monitoring

```python
from src.infrastructure.data_quality import DynamicExchangeRateValidator

class DynamicExchangeRateValidator:
    """Validates exchange rates against multiple real-time sources."""
    
    async def validate_rates(
        self,
        data: pd.DataFrame,
        priority: ProcessingPriority = ProcessingPriority.HIGH
    ) -> ExchangeRateValidation:
        """Validate exchange rates dynamically."""
        
        # Fetch current rates from multiple sources
        current_rates = await self._fetch_current_rates()
        
        # Validate against historical patterns
        historical_validation = await self._validate_historical_patterns(data)
        
        # Cross-validate between sources
        cross_validation = await self._cross_validate_sources(current_rates)
        
        # Generate rate alerts for suspicious values
        alerts = self._generate_rate_alerts(data, current_rates)
        
        return ExchangeRateValidation(
            validated_rates=current_rates,
            historical_consistency=historical_validation,
            cross_source_validation=cross_validation,
            alerts=alerts,
            confidence_score=self._calculate_confidence_score(
                historical_validation, cross_validation, alerts
            )
        )
```

### Rate Source Integration

The validator integrates multiple exchange rate sources:

- **Central Bank of Yemen (Aden)**: Official government rates
- **Central Bank of Yemen (Sana'a)**: De facto authority rates  
- **Parallel market data**: Informal exchange rates
- **Commercial bank rates**: Banking sector rates
- **Money exchanger networks**: Street-level rates

```python
@dataclass
class ExchangeRateSource:
    """Configuration for exchange rate data sources."""
    
    source_id: str
    name: str
    api_endpoint: str
    currency_zone: str
    reliability_weight: float
    update_frequency: timedelta
    timeout_seconds: int = 30
    
    async def fetch_rate(self, currency_pair: str) -> Optional[ExchangeRate]:
        """Fetch current exchange rate from source."""
        pass
```

## Zone-Specific Quality Control

### Territorial Control-Based Validation

```python
from src.infrastructure.data_quality import ZoneSpecificQualityControl

class ZoneSpecificQualityControl:
    """Applies quality controls specific to territorial control zones."""
    
    def __init__(self, zone_configs: Dict[str, ZoneQualityConfig]):
        self.zone_configs = zone_configs
    
    async def apply_controls(
        self,
        data: pd.DataFrame,
        exchange_validation: ExchangeRateValidation
    ) -> ZoneQualityReport:
        """Apply zone-specific quality controls."""
        
        results = {}
        
        for zone_id, config in self.zone_configs.items():
            zone_data = data[data['currency_zone'] == zone_id]
            
            # Apply zone-specific validation rules
            validation_result = await self._validate_zone_data(
                zone_data, config, exchange_validation
            )
            
            results[zone_id] = validation_result
        
        return ZoneQualityReport(
            zone_results=results,
            overall_quality_score=self._calculate_overall_score(results),
            recommendations=self._generate_recommendations(results)
        )
```

### Zone-Specific Rules

Different zones require different validation approaches:

```python
@dataclass
class ZoneQualityConfig:
    """Quality control configuration for a specific zone."""
    
    zone_id: str
    name: str
    
    # Price validation bounds (zone-specific)
    min_price_usd: Dict[str, float]  # Per commodity
    max_price_usd: Dict[str, float]  # Per commodity
    
    # Exchange rate validation
    expected_rate_range: Tuple[float, float]
    rate_volatility_threshold: float
    
    # Missing data tolerance
    max_missing_percentage: float
    critical_commodities: List[str]
    
    # Conflict adjustments
    conflict_multiplier: float  # Price volatility multiplier during conflict
    security_discount: float    # Discount for security-related costs
```

## Currency Conversion with Timing

### Timing-Aware Conversion

```python
from src.infrastructure.data_quality import CurrencyConversionTiming

class CurrencyConversionTiming:
    """Handles currency conversion with proper timing validation."""
    
    async def convert_with_timing(
        self,
        data: pd.DataFrame,
        validated_rates: Dict[str, ExchangeRate]
    ) -> ConversionResult:
        """Convert currencies with timing validation."""
        
        converted_data = data.copy()
        conversion_errors = []
        
        for idx, row in data.iterrows():
            try:
                # Get appropriate exchange rate for date and zone
                rate = self._get_rate_for_date_and_zone(
                    row['date'], row['currency_zone'], validated_rates
                )
                
                # Validate timing consistency
                timing_check = self._validate_conversion_timing(row, rate)
                
                if timing_check.is_valid:
                    # Perform conversion
                    if row['currency'] == 'YER':
                        converted_data.loc[idx, 'price_usd'] = row['price'] / rate.rate
                    else:
                        converted_data.loc[idx, 'price_usd'] = row['price']
                else:
                    conversion_errors.append(f"Timing error at row {idx}: {timing_check.error}")
                    
            except Exception as e:
                conversion_errors.append(f"Conversion error at row {idx}: {str(e)}")
        
        return ConversionResult(
            data=converted_data,
            errors=conversion_errors,
            conversion_rate=len(conversion_errors) / len(data),
            metadata=self._generate_conversion_metadata(data, converted_data)
        )
```

## Conflict-Aware Imputation

### Missing Data Patterns

In conflict settings, missing data is not random:

```python
from src.infrastructure.data_quality import ConflictAwareImputation

class ConflictAwareImputation:
    """Imputes missing data accounting for conflict patterns."""
    
    async def impute_missing_data(self, data: pd.DataFrame) -> ImputationResult:
        """Impute missing data using conflict-aware methods."""
        
        # Analyze missing data patterns
        missing_analysis = self._analyze_missing_patterns(data)
        
        # Apply different strategies based on missingness type
        if missing_analysis.is_random:
            imputed_data = await self._standard_imputation(data)
        elif missing_analysis.is_conflict_driven:
            imputed_data = await self._conflict_aware_imputation(data)
        else:
            imputed_data = await self._hybrid_imputation(data, missing_analysis)
        
        return ImputationResult(
            data=imputed_data,
            imputation_summary=self._generate_imputation_summary(data, imputed_data),
            quality_impact=self._assess_quality_impact(data, imputed_data)
        )
    
    async def _conflict_aware_imputation(self, data: pd.DataFrame) -> pd.DataFrame:
        """Imputation accounting for conflict-driven missingness."""
        
        imputed_data = data.copy()
        
        for market_id in data['market_id'].unique():
            market_data = data[data['market_id'] == market_id]
            
            # Get conflict intensity for market
            conflict_level = self._get_conflict_level(market_id, market_data)
            
            # Apply conflict-adjusted imputation
            if conflict_level == "high":
                # Use regional averages with security adjustment
                imputed_values = await self._regional_security_adjusted_imputation(market_data)
            elif conflict_level == "medium":
                # Use time-series with conflict controls
                imputed_values = await self._time_series_conflict_imputation(market_data)
            else:
                # Standard time-series imputation
                imputed_values = await self._standard_time_series_imputation(market_data)
            
            # Update imputed data
            imputed_data.update(imputed_values)
        
        return imputed_data
```

### Imputation Strategies

Different strategies for different conflict contexts:

1. **High Conflict Areas**: Regional averages with security premium adjustments
2. **Medium Conflict Areas**: Time-series methods with conflict event controls
3. **Low Conflict Areas**: Standard econometric imputation methods
4. **Transition Zones**: Weighted combinations based on accessibility

## Quality Monitoring and Alerting

### Real-Time Quality Monitoring

```python
class QualityMonitor:
    """Monitors data quality in real-time."""
    
    async def monitor_quality_metrics(self) -> QualityDashboard:
        """Monitor key quality metrics."""
        
        metrics = {
            "currency_conversion_accuracy": await self._check_conversion_accuracy(),
            "missing_data_percentage": await self._calculate_missing_percentage(),
            "exchange_rate_anomalies": await self._detect_rate_anomalies(),
            "zone_coverage": await self._assess_zone_coverage(),
            "data_freshness": await self._check_data_freshness()
        }
        
        # Generate alerts for quality issues
        alerts = self._generate_quality_alerts(metrics)
        
        return QualityDashboard(
            metrics=metrics,
            alerts=alerts,
            overall_score=self._calculate_overall_quality_score(metrics)
        )
```

### Alert System

```python
@dataclass
class QualityAlert:
    """Represents a data quality alert."""
    
    alert_id: str
    severity: str  # "critical", "high", "medium", "low"
    category: str  # "currency", "missing_data", "outlier", "coverage"
    message: str
    affected_records: int
    timestamp: datetime
    recommended_action: str
    
    def should_notify_immediately(self) -> bool:
        """Check if alert requires immediate notification."""
        return self.severity in ["critical", "high"]
```

## Performance Optimization

### Caching Strategy

```python
class DataQualityCache:
    """Caches quality control results for performance."""
    
    async def get_cached_validation(
        self,
        data_hash: str,
        validation_type: str
    ) -> Optional[Any]:
        """Get cached validation result."""
        cache_key = f"quality:{validation_type}:{data_hash}"
        return await self.cache.get(cache_key)
    
    async def cache_validation(
        self,
        data_hash: str,
        validation_type: str,
        result: Any,
        ttl: int = 3600
    ) -> None:
        """Cache validation result."""
        cache_key = f"quality:{validation_type}:{data_hash}"
        await self.cache.set(cache_key, result, ttl)
```

### Parallel Processing

```python
async def process_quality_parallel(
    self,
    data_chunks: List[pd.DataFrame]
) -> List[QualityProcessingResult]:
    """Process data quality in parallel for large datasets."""
    
    tasks = [
        self.process_data_quality(chunk, priority=ProcessingPriority.HIGH)
        for chunk in data_chunks
    ]
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Handle any exceptions
    successful_results = [r for r in results if not isinstance(r, Exception)]
    failed_chunks = [i for i, r in enumerate(results) if isinstance(r, Exception)]
    
    if failed_chunks:
        logger.warning(f"Failed to process {len(failed_chunks)} chunks")
    
    return successful_results
```

## Configuration and Usage

### Configuration Example

```yaml
# config/data_quality.yaml
data_quality:
  exchange_rate_validation:
    sources:
      - source_id: "cby_aden"
        name: "Central Bank of Yemen (Aden)"
        currency_zone: "government"
        reliability_weight: 0.8
      - source_id: "parallel_market"
        name: "Parallel Market Data"
        currency_zone: "both"
        reliability_weight: 0.6
    
  zone_quality_control:
    government_zone:
      min_price_usd:
        wheat: 0.5
        rice: 0.6
      max_price_usd:
        wheat: 2.0
        rice: 2.5
      max_missing_percentage: 0.3
    
    houthi_zone:
      min_price_usd:
        wheat: 0.4
        rice: 0.5
      max_price_usd:
        wheat: 3.0
        rice: 3.5
      max_missing_percentage: 0.5
  
  imputation:
    conflict_aware: true
    max_imputation_percentage: 0.4
    validation_split: 0.2
```

### Usage Example

```python
from src.infrastructure.data_quality import DataQualityOrchestrator

# Initialize orchestrator
orchestrator = DataQualityOrchestrator.from_config("config/data_quality.yaml")

# Process data
result = await orchestrator.process_data_quality(
    raw_data=wfp_price_data,
    priority=ProcessingPriority.HIGH
)

# Check quality results
if result.quality_report.overall_score > 0.8:
    print("Data quality sufficient for analysis")
    clean_data = result.processed_data
else:
    print(f"Quality issues detected: {result.quality_report.summary}")
    # Handle quality issues or reject data
```

## Related Documentation

- [Data Processing Pipeline](./data-processing-pipeline.md)
- [Performance Monitoring](./performance-monitoring.md)
- [Caching Strategies](./caching-strategies.md)
- [Core Domain Models](../12-core-reference/domain-models.md)