# Comprehensive Data Pipeline V2: Production-Ready Integration Plan

## Executive Summary

This plan outlines the complete overhaul of the Yemen Market Integration data pipeline to incorporate all 10+ missing data sources identified in the methodology. The goal is a **fully automated, CLI-driven, production-ready system** with zero redundancy.

## 🎯 Objectives

1. **Complete Data Coverage**: Integrate all data sources required by methodology
2. **Full Automation**: CLI-driven pipeline with no manual scripts
3. **Production Quality**: Error handling, monitoring, validation at every step
4. **Zero Redundancy**: Remove all duplicate/simplified scripts
5. **Maintainability**: Clean architecture following DDD principles

## 📐 Architecture Overview

```
src/
├── core/
│   └── domain/
│       ├── conflict/
│       │   ├── entities.py          # ConflictEvent, ConflictMetrics
│       │   └── value_objects.py     # EventType, ActorType, IntensityLevel
│       ├── aid/
│       │   ├── entities.py          # AidDistribution, CashTransfer
│       │   └── value_objects.py     # AidModality, Organization
│       ├── climate/
│       │   ├── entities.py          # ClimateObservation, RainfallData
│       │   └── value_objects.py     # NDVIIndex, SeasonType
│       ├── infrastructure/
│       │   ├── entities.py          # TransportNetwork, MarketAccess
│       │   └── value_objects.py     # RoadQuality, CheckpointStatus
│       └── panel/
│           ├── entities.py          # EnhancedPanelData
│           └── value_objects.py     # DerivedMetrics, IntegrationMeasures
│
├── infrastructure/
│   ├── processors/
│   │   ├── base_processor.py        # Enhanced with validation
│   │   ├── conflict_processor.py    # ACLED with spatial calculations
│   │   ├── aid_processor.py         # OCHA 3W + FTS + Cash Consortium
│   │   ├── climate_processor.py     # Rainfall + NDVI + IPC
│   │   ├── market_processor.py      # Market characteristics builder
│   │   ├── population_processor.py  # WorldPop + IOM DTM
│   │   ├── global_prices_processor.py # FAO + World Bank
│   │   └── enhanced_panel_builder.py # Merges ALL data sources
│   │
│   ├── external_services/
│   │   ├── hdx_enhanced_client.py   # Handles all HDX datasets
│   │   ├── fao_client.py           # FAO GIEWS API
│   │   ├── worldbank_client.py     # World Bank data API
│   │   └── cache_manager.py        # Smart caching for all sources
│   │
│   └── data_quality/
│       ├── completeness_checker.py  # 88.4% target enforcement
│       ├── spatial_validator.py     # Validates geographic joins
│       └── temporal_aligner.py     # Handles different frequencies
│
├── application/
│   └── services/
│       ├── comprehensive_pipeline_orchestrator.py
│       ├── derived_variables_calculator.py
│       └── integration_metrics_service.py
│
└── interfaces/
    └── cli/
        └── commands/
            └── data_v2.py           # New comprehensive data commands
```

## 🔄 Data Flow Architecture

```mermaid
graph TD
    A[CLI Command] --> B[Pipeline Orchestrator]
    B --> C{Data Collection}
    
    C --> D1[HDX Client]
    C --> D2[FAO Client]
    C --> D3[World Bank Client]
    
    D1 --> E1[WFP Prices]
    D1 --> E2[ACLED Conflict]
    D1 --> E3[OCHA Aid]
    D1 --> E4[ACAPS Control]
    D1 --> E5[Rainfall Data]
    
    D2 --> E6[Global Prices]
    D3 --> E7[Macro Indicators]
    
    E1 --> F[Processors Layer]
    E2 --> F
    E3 --> F
    E4 --> F
    E5 --> F
    E6 --> F
    E7 --> F
    
    F --> G[Enhanced Panel Builder]
    G --> H[Derived Variables Calculator]
    H --> I[Integration Metrics]
    I --> J[Validation Layer]
    J --> K[Final Panel Dataset]
```

## 📊 Data Source Integration Details

### 1. Conflict Data Enhancement

```python
# src/infrastructure/processors/conflict_processor.py
class EnhancedConflictProcessor(BaseProcessor):
    """Processes ACLED data with spatial radius calculations."""
    
    async def process(self, hdx_dataset_id: str, markets: List[Market]) -> ConflictMetrics:
        # Download ACLED data
        events = await self.hdx_client.download_dataset(hdx_dataset_id)
        
        # For each market, calculate metrics within 50km radius
        for market in markets:
            nearby_events = self._filter_by_radius(events, market.location, radius_km=50)
            
            metrics = ConflictMetrics(
                market_id=market.id,
                conflict_events=len(nearby_events),
                battle_events=len([e for e in nearby_events if e.type == 'Battle']),
                fatalities=sum(e.fatalities for e in nearby_events),
                intensity_index=self._calculate_intensity(nearby_events),
                actors_present=self._extract_actors(nearby_events)
            )
            
            # Calculate lagged measures
            metrics.lag_1m = self._calculate_lag(nearby_events, months=1)
            metrics.lag_3m = self._calculate_lag(nearby_events, months=3)
            metrics.lag_6m = self._calculate_lag(nearby_events, months=6)
```

### 2. Aid Distribution Integration

```python
# src/infrastructure/processors/aid_processor.py
class AidDistributionProcessor(BaseProcessor):
    """Processes OCHA 3W, FTS, and Cash Consortium data."""
    
    async def process(self) -> AidDistributionData:
        # Multiple data sources
        ocha_3w = await self._process_3w_data()
        fts_funding = await self._process_fts_data()
        cash_consortium = await self._process_cash_data()
        
        # Merge and standardize
        return self._merge_aid_sources(ocha_3w, fts_funding, cash_consortium)
```

### 3. Market Characteristics Builder

```python
# src/infrastructure/processors/market_processor.py
class MarketCharacteristicsProcessor(BaseProcessor):
    """Builds comprehensive market profiles from multiple sources."""
    
    async def build_characteristics(self) -> MarketCharacteristics:
        # Combine data from:
        # - OSM for infrastructure
        # - OCHA for accessibility
        # - Population data for catchment
        # - Border/port calculations
        
        characteristics = {
            'urban_rural': self._classify_urban_rural(),
            'border_distance': self._calculate_border_distance(),
            'port_access': self._check_port_access(),
            'road_quality': self._assess_road_quality(),
            'population_catchment': self._estimate_catchment(),
            'market_type': self._classify_market_type()
        }
```

### 4. Enhanced Panel Builder

```python
# src/infrastructure/processors/enhanced_panel_builder.py
class EnhancedPanelBuilder:
    """Merges all data sources into comprehensive panel."""
    
    async def build_comprehensive_panel(self) -> pd.DataFrame:
        # Base price data
        base_panel = await self._build_price_panel()
        
        # Merge all additional sources
        panel = base_panel
        panel = self._merge_conflict_data(panel)
        panel = self._merge_aid_data(panel)
        panel = self._merge_climate_data(panel)
        panel = self._merge_market_characteristics(panel)
        panel = self._merge_population_data(panel)
        panel = self._merge_global_prices(panel)
        
        # Calculate derived variables
        panel = self._calculate_derived_variables(panel)
        panel = self._calculate_integration_metrics(panel)
        
        # Validate completeness
        self._validate_panel_completeness(panel)
        
        return panel
```

## 🎮 CLI Integration

### New Command Structure

```bash
# Main data command with subcommands
uv run python src/cli.py data-v2 [COMMAND]

# Commands:
download-all              # Download all data sources
process-all              # Process all downloaded data
build-panel              # Build comprehensive panel
validate                 # Run validation checks
update [SOURCE]          # Update specific data source
status                   # Show pipeline status
config                   # Configure data sources
```

### Implementation

```python
# src/interfaces/cli/commands/data_v2.py
@click.group(name="data-v2")
def data_v2_cli():
    """Comprehensive data pipeline management."""
    pass

@data_v2_cli.command()
@click.option('--parallel/--sequential', default=True)
@click.option('--cache/--no-cache', default=True)
def download_all(parallel: bool, cache: bool):
    """Download all required data sources."""
    orchestrator = ComprehensivePipelineOrchestrator()
    
    sources = [
        'wfp_prices',
        'acled_conflict',
        'ocha_aid',
        'acaps_control',
        'rainfall',
        'global_prices',
        'population'
    ]
    
    if parallel:
        asyncio.run(orchestrator.download_all_parallel(sources))
    else:
        orchestrator.download_all_sequential(sources)

@data_v2_cli.command()
@click.option('--target-coverage', default=88.4, help='Target data coverage %')
def build_panel(target_coverage: float):
    """Build comprehensive panel dataset."""
    builder = EnhancedPanelBuilder()
    panel = asyncio.run(builder.build_comprehensive_panel())
    
    # Validate coverage
    coverage = calculate_coverage(panel)
    if coverage < target_coverage:
        raise DataQualityException(f"Coverage {coverage}% below target {target_coverage}%")
```

## 🗑️ Scripts to Remove

### Delete These Files:
```
scripts/
├── download_data.py              # Replaced by CLI
├── process_panel_simple.py       # Replaced by pipeline
├── create_panel.py              # Replaced by EnhancedPanelBuilder
├── create_panel_final.py        # Duplicate
├── debug_panel_merge.py         # No longer needed
├── download_hdx_fallback.py     # Integrated into HDX client
├── test_*.py                    # Move to proper tests/
├── fix_*.py                     # All fixes in main code
├── simple_*.py                  # No simplified versions
└── run_data_pipeline.py         # Replaced by CLI
```

### Keep Only:
```
scripts/
├── analysis/                    # Analysis scripts only
│   ├── run_three_tier_models.py
│   └── run_specification_curve.py
└── validation/                  # Validation utilities
    └── validate_methodology_compliance.py
```

## 📋 Implementation Phases

### Phase 1: Core Infrastructure (Week 1)
- [ ] Create new domain entities for all data types
- [ ] Implement enhanced HDX client with all datasets
- [ ] Build base processor framework with validation
- [ ] Set up comprehensive CLI structure

### Phase 2: Data Processors (Week 2)
- [ ] Implement conflict processor with spatial calculations
- [ ] Build aid distribution processor
- [ ] Create climate data processor
- [ ] Develop market characteristics builder
- [ ] Implement population data processor

### Phase 3: Integration Layer (Week 3)
- [ ] Build enhanced panel builder
- [ ] Implement derived variables calculator
- [ ] Create integration metrics service
- [ ] Add comprehensive validation

### Phase 4: Testing & Documentation (Week 4)
- [ ] Unit tests for all processors (>95% coverage)
- [ ] Integration tests for full pipeline
- [ ] Performance optimization
- [ ] Complete documentation
- [ ] Remove all redundant scripts

## 🔍 Quality Assurance

### Data Quality Framework

```python
class ComprehensiveDataValidator:
    """Ensures all data meets methodology requirements."""
    
    def validate_panel(self, panel: pd.DataFrame) -> ValidationReport:
        checks = {
            'temporal_coverage': self._check_temporal_coverage(panel),
            'spatial_coverage': self._check_spatial_coverage(panel),
            'variable_completeness': self._check_all_variables_present(panel),
            'data_quality_scores': self._calculate_quality_scores(panel),
            'missing_patterns': self._analyze_missing_patterns(panel)
        }
        
        return ValidationReport(
            passed=all(checks.values()),
            coverage_pct=self._calculate_coverage(panel),
            details=checks
        )
```

### Monitoring & Logging

```python
class PipelineMonitor:
    """Real-time monitoring of data pipeline."""
    
    def __init__(self):
        self.metrics = {
            'download_times': {},
            'processing_times': {},
            'data_volumes': {},
            'error_counts': {},
            'coverage_metrics': {}
        }
    
    async def log_operation(self, operation: str, duration: float, status: str):
        # Log to structured logs
        # Update Prometheus metrics
        # Send alerts if needed
```

## 🚀 Production Deployment

### Configuration Management

```yaml
# config/data_pipeline.yaml
data_sources:
  wfp_prices:
    hdx_id: "98fc9c80-5c95-4670-8e5d-d37c62bb4bd6"
    update_frequency: "monthly"
    cache_ttl: 86400
    
  acled_conflict:
    hdx_id: "yemen-acled-conflict-data"
    update_frequency: "weekly"
    spatial_radius_km: 50
    
  ocha_aid:
    hdx_id: "yemen-3w-operational-presence"
    update_frequency: "monthly"
    
pipeline:
  target_coverage: 88.4
  parallel_downloads: true
  max_workers: 4
  cache_dir: "data/cache"
  
validation:
  min_markets: 300
  min_observations: 10000
  required_variables: [...list all...]
```

### Error Recovery

```python
class ResilientPipelineOrchestrator:
    """Production-ready orchestrator with error recovery."""
    
    async def run_with_recovery(self):
        checkpoint_manager = CheckpointManager()
        
        for step in self.pipeline_steps:
            try:
                if not checkpoint_manager.is_completed(step):
                    result = await step.execute()
                    checkpoint_manager.mark_completed(step, result)
            except Exception as e:
                if step.is_critical:
                    raise
                else:
                    self.logger.error(f"Non-critical step failed: {step}")
                    continue
```

## 📊 Expected Outcomes

### Data Coverage Improvements
- **Current**: Basic prices + exchange rates (~30% of requirements)
- **After V2**: All 10 data categories integrated (100% of requirements)
- **Panel Richness**: From 28 columns to 150+ columns
- **Temporal Coverage**: Consistent 2019-2024 for all sources
- **Spatial Coverage**: All 333 districts with characteristics

### Performance Targets
- **Full pipeline execution**: < 30 minutes
- **Incremental updates**: < 5 minutes
- **Memory usage**: < 8GB RAM
- **Disk cache**: < 50GB

### Quality Metrics
- **Data completeness**: ≥88.4%
- **Validation pass rate**: 100%
- **Test coverage**: ≥95%
- **Documentation**: 100% of public APIs

## 🎯 Success Criteria

1. **All data sources integrated** and downloading automatically
2. **Zero manual scripts** - everything through CLI
3. **Production-ready** with monitoring, caching, error handling
4. **Fully validated** panel meeting all methodology requirements
5. **Clean codebase** with no redundancy or duplication

This comprehensive plan transforms the data pipeline from a collection of scripts into a production-ready, fully automated system that meets all World Bank research standards.