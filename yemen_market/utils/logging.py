"""Simple logging utilities for Yemen Market Integration."""

import structlog
from contextlib import contextmanager
from time import time
from typing import Any, Dict, Optional

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.dev.ConsoleRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

# Get logger
logger = structlog.get_logger()

# Context storage
_context: Dict[str, Any] = {}

def bind(**kwargs):
    """Add context to logger."""
    global _context
    _context.update(kwargs)
    return logger.bind(**_context)

@contextmanager
def timer(operation: str):
    """Time an operation."""
    start = time()
    try:
        yield
    finally:
        duration = time() - start
        logger.info(f"{operation}_completed", duration=duration, **_context)

def progress(message: str, **kwargs):
    """Log progress message."""
    logger.info(message, **{**_context, **kwargs})

def log_metric(name: str, value: Any):
    """Log a metric."""
    logger.info("metric", metric_name=name, metric_value=value, **_context)

def info(message: str, **kwargs):
    """Log info message."""
    logger.info(message, **{**_context, **kwargs})

def warning(message: str, **kwargs):
    """Log warning message."""
    logger.warning(message, **{**_context, **kwargs})

def error(message: str, exc_info: bool = False, **kwargs):
    """Log error message."""
    logger.error(message, exc_info=exc_info, **{**_context, **kwargs})

def debug(message: str, **kwargs):
    """Log debug message."""
    logger.debug(message, **{**_context, **kwargs})