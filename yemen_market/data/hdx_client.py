"""HDX (Humanitarian Data Exchange) client for Yemen market data."""

import os
import zipfile
from pathlib import Path
from typing import List, Dict, Any, Optional
import pandas as pd
import geopandas as gpd

try:
    from hdx.api.configuration import Configuration
    from hdx.data.dataset import Dataset
    from hdx.data.resource import Resource
    HDX_AVAILABLE = True
except ImportError:
    HDX_AVAILABLE = False
    print("Warning: HDX Python package not installed. Install with: pip install hdx-python-api")

from ..utils.logging import info, warning, error, debug


class HDXClient:
    """Client for accessing HDX (Humanitarian Data Exchange) datasets."""
    
    # Common dataset IDs
    WFP_DATASET = "wfp-food-prices-for-yemen"
    ACAPS_DATASET = "yemen-areas-of-control"
    ADMIN_BOUNDARIES = "cod-ab-yem"
    ACLED_DATASET = "acled-data-for-yemen"
    
    def __init__(self, download_dir: Optional[Path] = None):
        """Initialize HDX client.
        
        Args:
            download_dir: Directory to save downloaded files
        """
        self.download_dir = download_dir or Path("data/raw/hdx")
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize HDX configuration
        if HDX_AVAILABLE:
            try:
                # Try to initialize with API key from environment
                hdx_key = os.environ.get('HDX_API_KEY', '')
                if hdx_key:
                    Configuration.create(hdx_site='prod', user_agent='YemenMarketIntegration', 
                                       hdx_key=hdx_key)
                else:
                    # Initialize without key for public datasets
                    Configuration.create(hdx_site='prod', user_agent='YemenMarketIntegration',
                                       hdx_read_only=True)
                info("HDX configuration initialized successfully")
            except Exception as e:
                error(f"Failed to initialize HDX configuration: {e}")
                # Don't raise - we can still work with manual downloads
                self.hdx_configured = False
            else:
                self.hdx_configured = True
        else:
            self.hdx_configured = False
    
    def get_dataset(self, dataset_id: str) -> Optional[Dataset]:
        """Get dataset from HDX.
        
        Args:
            dataset_id: HDX dataset identifier
            
        Returns:
            Dataset object or None if not found
        """
        if not HDX_AVAILABLE or not self.hdx_configured:
            error("HDX API not available or not configured")
            return None
            
        try:
            dataset = Dataset.read_from_hdx(dataset_id)
            if dataset:
                info(f"Found dataset: {dataset_id}")
                return dataset
            else:
                warning(f"Dataset not found: {dataset_id}")
                return None
        except Exception as e:
            error(f"Error fetching dataset {dataset_id}: {e}")
            return None
    
    def get_metadata(self, dataset_id: str) -> Optional[Dict[str, Any]]:
        """Get dataset metadata.
        
        Args:
            dataset_id: HDX dataset identifier
            
        Returns:
            Dictionary with metadata or None
        """
        dataset = self.get_dataset(dataset_id)
        if dataset:
            return {
                'title': dataset.get('title'),
                'name': dataset.get('name'),
                'updated': dataset.get('last_modified'),
                'organization': dataset.get('organization', {}).get('title'),
                'tags': [tag.get('name') for tag in dataset.get('tags', [])],
                'resources': len(dataset.get_resources())
            }
        return None
    
    def download_wfp_food_prices(self, force_download: bool = False) -> Optional[pd.DataFrame]:
        """Download WFP food price data.
        
        Args:
            force_download: Force re-download even if file exists
            
        Returns:
            DataFrame with WFP price data or None
        """
        dataset = self.get_dataset(self.WFP_DATASET)
        if not dataset:
            return None
        
        # Look for CSV resource
        csv_resources = [r for r in dataset.get_resources() if r.get('format', '').lower() == 'csv']
        if not csv_resources:
            error("No CSV resources found in WFP dataset")
            return None
        
        # Download the first CSV resource
        resource = csv_resources[0]
        file_path = self.download_dir / f"wfp_food_prices_{self.WFP_DATASET}.csv"
        
        if not file_path.exists() or force_download:
            try:
                url = resource.get('url')
                info(f"Downloading WFP data from {url}")
                _, download_path = resource.download(folder=str(self.download_dir))
                
                # Rename to standard name
                if Path(download_path).exists():
                    Path(download_path).rename(file_path)
                    info(f"Downloaded WFP data to {file_path}")
            except Exception as e:
                error(f"Failed to download WFP data: {e}")
                return None
        
        # Load and return DataFrame
        try:
            df = pd.read_csv(file_path)
            info(f"Loaded WFP data: {df.shape[0]} rows, {df.shape[1]} columns")
            return df
        except Exception as e:
            error(f"Failed to load WFP data: {e}")
            return None
    
    def download_admin_boundaries(self) -> Dict[str, Path]:
        """Download administrative boundary files.
        
        Returns:
            Dictionary mapping boundary names to file paths
        """
        dataset = self.get_dataset(self.ADMIN_BOUNDARIES)
        if not dataset:
            return {}
        
        boundary_files = {}
        
        # Download all GeoPackage or Shapefile resources
        for resource in dataset.get_resources():
            format_type = resource.get('format', '').lower()
            if format_type in ['gpkg', 'geopackage', 'shp', 'shapefile', 'zip']:
                try:
                    name = resource.get('name', 'boundary')
                    _, download_path = resource.download(folder=str(self.download_dir))
                    
                    # Handle zip files
                    if download_path.endswith('.zip'):
                        extract_dir = self.download_dir / f"boundaries_{name}"
                        extract_dir.mkdir(exist_ok=True)
                        
                        with zipfile.ZipFile(download_path, 'r') as zip_ref:
                            zip_ref.extractall(extract_dir)
                        
                        # Find shapefile or geopackage
                        for file in extract_dir.rglob('*.shp'):
                            boundary_files[name] = file
                            break
                        for file in extract_dir.rglob('*.gpkg'):
                            boundary_files[name] = file
                            break
                    else:
                        boundary_files[name] = Path(download_path)
                    
                    info(f"Downloaded boundary file: {name}")
                except Exception as e:
                    error(f"Failed to download boundary resource: {e}")
        
        return boundary_files
    
    def download_acled_conflict_data(self) -> Optional[Path]:
        """Download ACLED conflict data.
        
        Returns:
            Path to downloaded file or None
        """
        # Note: ACLED requires authentication, so this is a placeholder
        # In practice, ACLED data might need to be downloaded manually
        warning("ACLED data requires authentication. Please download manually from https://acleddata.com")
        
        # Check if manually downloaded file exists
        acled_file = self.download_dir.parent / "acled" / "acled_yemen_events_2019-01-01_to_2024-12-31.csv"
        if acled_file.exists():
            info(f"Found manually downloaded ACLED data: {acled_file}")
            return acled_file
        
        return None
    
    def search_datasets(self, query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Search for datasets on HDX.
        
        Args:
            query: Search query
            filters: Additional filters
            
        Returns:
            List of matching datasets
        """
        if not HDX_AVAILABLE:
            error("HDX Python API not available")
            return []
        
        try:
            # HDX search is more complex, this is simplified
            # In practice, you'd use the HDX search API
            datasets = Dataset.search_in_hdx(query, rows=100)
            
            results = []
            for dataset in datasets:
                dataset_dict = {
                    'name': dataset.get('name'),
                    'title': dataset.get('title'),
                    'organization': dataset.get('organization', {}).get('title'),
                    'updated': dataset.get('last_modified'),
                    'tags': [tag.get('name') for tag in dataset.get('tags', [])]
                }
                results.append(dataset_dict)
            
            info(f"Found {len(results)} datasets matching '{query}'")
            return results
        except Exception as e:
            error(f"Search failed: {e}")
            return []