"""Unit tests for cache manager."""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import <PERSON><PERSON>, AsyncMock, patch
import pickle
import json

from src.infrastructure.caching.cache_manager import (
    CacheManager, CacheEntry, CacheStrategy, TTLCache, SizeBasedCache
)


class TestCacheEntry:
    """Test cache entry functionality."""
    
    def test_cache_entry_creation(self):
        """Test creating cache entries."""
        data = {"key": "value"}
        entry = CacheEntry(
            key="test_key",
            data=data,
            ttl_seconds=300
        )
        
        assert entry.key == "test_key"
        assert entry.data == data
        assert not entry.is_expired()
    
    def test_cache_entry_expiration(self):
        """Test cache entry expiration."""
        # Create entry with short TTL
        entry = CacheEntry(
            key="test_key",
            data="test_data",
            ttl_seconds=0.1
        )
        
        # Should not be expired immediately
        assert not entry.is_expired()
        
        # Wait for expiration
        import time
        time.sleep(0.2)
        
        # Should now be expired
        assert entry.is_expired()
    
    def test_cache_entry_size_calculation(self):
        """Test size calculation for cache entries."""
        # Small data
        small_entry = CacheEntry(
            key="small",
            data="small data",
            ttl_seconds=300
        )
        
        # Large data
        large_data = {"items": list(range(1000))}
        large_entry = CacheEntry(
            key="large",
            data=large_data,
            ttl_seconds=300
        )
        
        assert large_entry.size_bytes > small_entry.size_bytes


class TestTTLCache:
    """Test TTL-based cache implementation."""
    
    @pytest.fixture
    def ttl_cache(self):
        """Create TTL cache with 5 second default TTL."""
        return TTLCache(default_ttl_seconds=5)
    
    @pytest.mark.asyncio
    async def test_set_and_get(self, ttl_cache):
        """Test basic set and get operations."""
        # Set value
        await ttl_cache.set("key1", "value1")
        
        # Get value
        result = await ttl_cache.get("key1")
        assert result == "value1"
    
    @pytest.mark.asyncio
    async def test_ttl_expiration(self, ttl_cache):
        """Test TTL expiration."""
        # Set with custom TTL
        await ttl_cache.set("key1", "value1", ttl_seconds=0.1)
        
        # Should exist immediately
        assert await ttl_cache.get("key1") == "value1"
        
        # Wait for expiration
        await asyncio.sleep(0.2)
        
        # Should be expired
        assert await ttl_cache.get("key1") is None
    
    @pytest.mark.asyncio
    async def test_delete(self, ttl_cache):
        """Test delete operation."""
        # Set value
        await ttl_cache.set("key1", "value1")
        assert await ttl_cache.get("key1") == "value1"
        
        # Delete
        await ttl_cache.delete("key1")
        assert await ttl_cache.get("key1") is None
    
    @pytest.mark.asyncio
    async def test_clear(self, ttl_cache):
        """Test clear operation."""
        # Set multiple values
        await ttl_cache.set("key1", "value1")
        await ttl_cache.set("key2", "value2")
        await ttl_cache.set("key3", "value3")
        
        # Clear all
        await ttl_cache.clear()
        
        # All should be gone
        assert await ttl_cache.get("key1") is None
        assert await ttl_cache.get("key2") is None
        assert await ttl_cache.get("key3") is None
    
    @pytest.mark.asyncio
    async def test_cleanup_expired(self, ttl_cache):
        """Test automatic cleanup of expired entries."""
        # Set values with different TTLs
        await ttl_cache.set("short", "value1", ttl_seconds=0.1)
        await ttl_cache.set("long", "value2", ttl_seconds=10)
        
        # Wait for short to expire
        await asyncio.sleep(0.2)
        
        # Cleanup
        await ttl_cache._cleanup_expired()
        
        # Short should be gone, long should remain
        assert await ttl_cache.get("short") is None
        assert await ttl_cache.get("long") == "value2"


class TestSizeBasedCache:
    """Test size-based cache implementation."""
    
    @pytest.fixture
    def size_cache(self):
        """Create size-based cache with 1KB limit."""
        return SizeBasedCache(max_size_bytes=1024)
    
    @pytest.mark.asyncio
    async def test_size_limit_enforcement(self, size_cache):
        """Test that size limits are enforced."""
        # Create data that's about 500 bytes
        large_data = "x" * 400
        
        # Add first item
        await size_cache.set("key1", large_data)
        assert await size_cache.get("key1") == large_data
        
        # Add second item (should fit)
        await size_cache.set("key2", large_data)
        assert await size_cache.get("key2") == large_data
        
        # Add third item (should evict oldest)
        await size_cache.set("key3", large_data)
        assert await size_cache.get("key3") == large_data
        
        # First item should be evicted
        assert await size_cache.get("key1") is None
    
    @pytest.mark.asyncio
    async def test_lru_eviction(self, size_cache):
        """Test LRU eviction policy."""
        # Add items
        await size_cache.set("key1", "value1")
        await size_cache.set("key2", "value2")
        await size_cache.set("key3", "value3")
        
        # Access key1 and key3 (making key2 least recently used)
        await size_cache.get("key1")
        await size_cache.get("key3")
        
        # Add large item that requires eviction
        large_data = "x" * 800
        await size_cache.set("key4", large_data)
        
        # Key2 should be evicted (least recently used)
        assert await size_cache.get("key2") is None
        assert await size_cache.get("key1") is not None
        assert await size_cache.get("key3") is not None


class TestCacheManager:
    """Test cache manager with multiple strategies."""
    
    @pytest.fixture
    def cache_manager(self):
        """Create cache manager with mixed strategies."""
        return CacheManager(
            default_strategy=CacheStrategy.TTL,
            ttl_seconds=300,
            max_size_mb=10
        )
    
    @pytest.mark.asyncio
    async def test_strategy_selection(self, cache_manager):
        """Test automatic strategy selection."""
        # Small, short-lived data should use TTL
        await cache_manager.set(
            "small_key",
            "small_value",
            strategy=CacheStrategy.TTL
        )
        
        # Large data should use size-based
        large_data = {"items": list(range(10000))}
        await cache_manager.set(
            "large_key",
            large_data,
            strategy=CacheStrategy.SIZE_BASED
        )
        
        # Verify both can be retrieved
        assert await cache_manager.get("small_key") == "small_value"
        assert await cache_manager.get("large_key") == large_data
    
    @pytest.mark.asyncio
    async def test_key_prefixing(self, cache_manager):
        """Test key prefixing for different data types."""
        # Set different data types
        await cache_manager.set("conflict:event1", {"type": "battle"})
        await cache_manager.set("price:market1", {"price": 100})
        await cache_manager.set("exchange:2024-01", {"rate": 535})
        
        # All should be retrievable
        assert await cache_manager.get("conflict:event1") is not None
        assert await cache_manager.get("price:market1") is not None
        assert await cache_manager.get("exchange:2024-01") is not None
    
    @pytest.mark.asyncio
    async def test_bulk_operations(self, cache_manager):
        """Test bulk set and get operations."""
        # Bulk set
        items = {
            f"key{i}": f"value{i}"
            for i in range(10)
        }
        
        await cache_manager.bulk_set(items)
        
        # Bulk get
        keys = list(items.keys())
        results = await cache_manager.bulk_get(keys)
        
        assert len(results) == len(items)
        assert all(results[k] == items[k] for k in keys)
    
    @pytest.mark.asyncio
    async def test_cache_statistics(self, cache_manager):
        """Test cache statistics tracking."""
        # Perform operations
        await cache_manager.set("key1", "value1")
        await cache_manager.get("key1")  # Hit
        await cache_manager.get("key2")  # Miss
        await cache_manager.get("key1")  # Hit
        
        stats = cache_manager.get_statistics()
        
        assert stats["hits"] == 2
        assert stats["misses"] == 1
        assert stats["hit_rate"] == 2/3
    
    @pytest.mark.asyncio
    async def test_persistence(self, cache_manager, tmp_path):
        """Test cache persistence to disk."""
        # Set up persistence
        cache_file = tmp_path / "cache.pkl"
        cache_manager.enable_persistence(cache_file)
        
        # Add data
        await cache_manager.set("key1", "value1")
        await cache_manager.set("key2", {"data": "value2"})
        
        # Save to disk
        await cache_manager.save_to_disk()
        
        # Create new cache manager and load
        new_manager = CacheManager()
        await new_manager.load_from_disk(cache_file)
        
        # Data should be restored
        assert await new_manager.get("key1") == "value1"
        assert await new_manager.get("key2") == {"data": "value2"}
    
    @pytest.mark.asyncio
    async def test_concurrent_access(self, cache_manager):
        """Test concurrent cache access."""
        async def writer(key_prefix, count):
            for i in range(count):
                await cache_manager.set(f"{key_prefix}_{i}", f"value_{i}")
        
        async def reader(key_prefix, count):
            results = []
            for i in range(count):
                value = await cache_manager.get(f"{key_prefix}_{i}")
                if value is not None:
                    results.append(value)
            return results
        
        # Run concurrent writes
        await asyncio.gather(
            writer("prefix1", 10),
            writer("prefix2", 10),
            writer("prefix3", 10)
        )
        
        # Run concurrent reads
        results = await asyncio.gather(
            reader("prefix1", 10),
            reader("prefix2", 10),
            reader("prefix3", 10)
        )
        
        # All reads should succeed
        assert all(len(r) == 10 for r in results)
    
    @pytest.mark.asyncio
    async def test_cache_warming(self, cache_manager):
        """Test cache warming functionality."""
        # Define data to warm
        warm_data = {
            "static:config": {"version": "1.0"},
            "static:zones": ["North", "South"],
            "reference:commodities": ["wheat", "sugar", "rice"]
        }
        
        # Warm cache
        await cache_manager.warm_cache(warm_data)
        
        # All data should be available
        for key, expected in warm_data.items():
            assert await cache_manager.get(key) == expected