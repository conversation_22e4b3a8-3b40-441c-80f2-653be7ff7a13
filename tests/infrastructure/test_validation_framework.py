"""Unit tests for validation framework."""

import pytest
from datetime import datetime
import pandas as pd
import numpy as np

from src.infrastructure.data_quality.validation_framework import (
    ValidationReport, ValidationLevel, ValidationFramework,
    SchemaValidator, ConstraintValidator, StatisticalValidator,
    BusinessLogicValidator, SourceSpecificValidator
)
from src.core.domain.shared.exceptions import DataQualityException


class TestValidationReport:
    """Test validation report functionality."""
    
    def test_add_validation(self):
        """Test adding validations to report."""
        report = ValidationReport(source="test")
        
        report.add_validation("field1", ValidationLevel.INFO, "Info message")
        report.add_validation("field2", ValidationLevel.WARNING, "Warning message")
        report.add_validation("field3", ValidationLevel.ERROR, "Error message")
        
        assert len(report.validations) == 3
        assert report.has_errors()
        assert report.has_warnings()
    
    def test_validation_levels(self):
        """Test validation level checks."""
        report = ValidationReport(source="test")
        
        # Only info
        report.add_validation("field", ValidationLevel.INFO, "Info")
        assert not report.has_errors()
        assert not report.has_warnings()
        
        # Add warning
        report.add_validation("field", ValidationLevel.WARNING, "Warning")
        assert not report.has_errors()
        assert report.has_warnings()
        
        # Add error
        report.add_validation("field", ValidationLevel.ERROR, "Error")
        assert report.has_errors()
        assert report.has_warnings()
    
    def test_get_summary(self):
        """Test report summary generation."""
        report = ValidationReport(source="test")
        
        report.add_validation("field1", ValidationLevel.INFO, "Info 1")
        report.add_validation("field1", ValidationLevel.INFO, "Info 2")
        report.add_validation("field2", ValidationLevel.WARNING, "Warning")
        report.add_validation("field3", ValidationLevel.ERROR, "Error")
        
        summary = report.get_summary()
        
        assert summary["source"] == "test"
        assert summary["error_count"] == 1
        assert summary["warning_count"] == 1
        assert summary["info_count"] == 2
        assert summary["fields_with_issues"] == ["field1", "field2", "field3"]


class TestSchemaValidator:
    """Test schema validation."""
    
    @pytest.fixture
    def validator(self):
        """Create schema validator."""
        schema = {
            "market_id": {"type": "string", "required": True},
            "price": {"type": "number", "required": True, "min": 0},
            "date": {"type": "datetime", "required": True},
            "quantity": {"type": "number", "required": False, "min": 0}
        }
        return SchemaValidator(schema)
    
    def test_valid_data(self, validator):
        """Test validation of valid data."""
        data = pd.DataFrame({
            "market_id": ["M001", "M002"],
            "price": [100.0, 150.0],
            "date": [datetime.now(), datetime.now()],
            "quantity": [10.0, 20.0]
        })
        
        is_valid, report = validator.validate(data)
        
        assert is_valid
        assert not report.has_errors()
    
    def test_missing_required_field(self, validator):
        """Test detection of missing required field."""
        data = pd.DataFrame({
            "market_id": ["M001", "M002"],
            "price": [100.0, 150.0]
            # Missing required 'date' field
        })
        
        is_valid, report = validator.validate(data)
        
        assert not is_valid
        assert report.has_errors()
        assert any("date" in v.field for v in report.validations)
    
    def test_type_validation(self, validator):
        """Test type validation."""
        data = pd.DataFrame({
            "market_id": ["M001", "M002"],
            "price": ["not_a_number", 150.0],  # Invalid type
            "date": [datetime.now(), datetime.now()]
        })
        
        is_valid, report = validator.validate(data)
        
        assert not is_valid
        assert report.has_errors()
    
    def test_min_value_constraint(self, validator):
        """Test minimum value constraints."""
        data = pd.DataFrame({
            "market_id": ["M001", "M002"],
            "price": [-100.0, 150.0],  # Negative price
            "date": [datetime.now(), datetime.now()]
        })
        
        is_valid, report = validator.validate(data)
        
        assert not is_valid
        assert report.has_errors()
        assert any("price" in v.field and "minimum" in v.message.lower() 
                  for v in report.validations)


class TestConstraintValidator:
    """Test constraint validation."""
    
    @pytest.fixture
    def validator(self):
        """Create constraint validator."""
        constraints = {
            "price": {"min": 0, "max": 10000},
            "exchange_rate": {"min": 100, "max": 3000},
            "date": {
                "min": datetime(2019, 1, 1),
                "max": datetime(2025, 12, 31)
            }
        }
        return ConstraintValidator(constraints)
    
    def test_valid_constraints(self, validator):
        """Test data within constraints."""
        data = pd.DataFrame({
            "price": [100.0, 500.0],
            "exchange_rate": [535.0, 2000.0],
            "date": [datetime(2024, 1, 1), datetime(2024, 6, 1)]
        })
        
        is_valid, report = validator.validate(data)
        
        assert is_valid
        assert not report.has_errors()
    
    def test_out_of_range_values(self, validator):
        """Test detection of out-of-range values."""
        data = pd.DataFrame({
            "price": [100.0, 15000.0],  # Above max
            "exchange_rate": [50.0, 2000.0],  # Below min
            "date": [datetime(2024, 1, 1), datetime(2024, 6, 1)]
        })
        
        is_valid, report = validator.validate(data)
        
        assert not is_valid
        assert report.has_errors()
        assert sum(1 for v in report.validations if v.level == ValidationLevel.ERROR) >= 2


class TestStatisticalValidator:
    """Test statistical validation."""
    
    @pytest.fixture
    def validator(self):
        """Create statistical validator."""
        rules = {
            "outlier_std": 3.0,
            "missing_threshold": 0.5,
            "min_unique_values": {
                "market_id": 2,
                "commodity": 1
            }
        }
        return StatisticalValidator(rules)
    
    def test_outlier_detection(self, validator):
        """Test outlier detection."""
        # Create data with outliers
        np.random.seed(42)
        prices = np.random.normal(100, 10, 100).tolist()
        prices.extend([1000, -50])  # Add outliers
        
        data = pd.DataFrame({
            "price": prices,
            "market_id": ["M001"] * 51 + ["M002"] * 51,
            "commodity": ["wheat"] * 102
        })
        
        is_valid, report = validator.validate(data)
        
        # Should have warnings about outliers
        assert report.has_warnings()
        assert any("outlier" in v.message.lower() for v in report.validations)
    
    def test_missing_data_threshold(self, validator):
        """Test missing data threshold validation."""
        data = pd.DataFrame({
            "price": [100, 150, np.nan, np.nan, np.nan, 200],  # 50% missing
            "market_id": ["M001", "M002", "M001", "M002", "M001", "M002"],
            "commodity": ["wheat"] * 6
        })
        
        is_valid, report = validator.validate(data)
        
        # Should warn about high missing percentage
        assert report.has_warnings()
        assert any("missing" in v.message.lower() for v in report.validations)
    
    def test_insufficient_unique_values(self, validator):
        """Test detection of insufficient unique values."""
        data = pd.DataFrame({
            "price": [100, 150, 200],
            "market_id": ["M001", "M001", "M001"],  # Only 1 unique value
            "commodity": ["wheat"] * 3
        })
        
        is_valid, report = validator.validate(data)
        
        # Should error on insufficient market diversity
        assert not is_valid
        assert any("unique" in v.message.lower() and "market_id" in v.field 
                  for v in report.validations)


class TestBusinessLogicValidator:
    """Test business logic validation."""
    
    @pytest.fixture
    def validator(self):
        """Create business logic validator."""
        rules = {
            "price_relationships": {
                "wheat_sugar_ratio": {"min": 0.5, "max": 2.0}
            },
            "temporal_consistency": {
                "max_price_change_pct": 50
            },
            "cross_market_limits": {
                "max_price_differential_pct": 100
            }
        }
        return BusinessLogicValidator(rules)
    
    def test_price_relationships(self, validator):
        """Test commodity price relationship validation."""
        data = pd.DataFrame({
            "commodity": ["wheat", "sugar", "wheat", "sugar"],
            "market_id": ["M001", "M001", "M002", "M002"],
            "price": [100, 150, 110, 400],  # Sugar too expensive in M002
            "date": [datetime.now()] * 4
        })
        
        is_valid, report = validator.validate(data)
        
        # Should detect unusual price ratio
        assert report.has_warnings()
    
    def test_temporal_consistency(self, validator):
        """Test temporal consistency validation."""
        dates = pd.date_range("2024-01-01", periods=4, freq="MS")
        
        data = pd.DataFrame({
            "commodity": ["wheat"] * 4,
            "market_id": ["M001"] * 4,
            "price": [100, 110, 120, 200],  # 67% jump in last period
            "date": dates
        })
        
        is_valid, report = validator.validate(data)
        
        # Should detect large price change
        assert report.has_warnings()
        assert any("temporal" in v.message.lower() or "change" in v.message.lower()
                  for v in report.validations)


class TestValidationFramework:
    """Test complete validation framework."""
    
    @pytest.fixture
    def framework(self):
        """Create validation framework with all validators."""
        schema = {
            "market_id": {"type": "string", "required": True},
            "price": {"type": "number", "required": True, "min": 0}
        }
        
        constraints = {
            "price": {"min": 0, "max": 10000}
        }
        
        statistical_rules = {
            "outlier_std": 3.0,
            "missing_threshold": 0.5
        }
        
        business_rules = {
            "temporal_consistency": {
                "max_price_change_pct": 50
            }
        }
        
        framework = ValidationFramework()
        framework.add_validator("schema", SchemaValidator(schema))
        framework.add_validator("constraints", ConstraintValidator(constraints))
        framework.add_validator("statistical", StatisticalValidator(statistical_rules))
        framework.add_validator("business", BusinessLogicValidator(business_rules))
        
        return framework
    
    def test_multi_stage_validation(self, framework):
        """Test multi-stage validation process."""
        data = pd.DataFrame({
            "market_id": ["M001", "M002"],
            "price": [100.0, 150.0]
        })
        
        is_valid, reports = framework.validate(data)
        
        assert is_valid
        assert len(reports) == 4  # One report per validator
        assert all(not r.has_errors() for r in reports.values())
    
    def test_early_termination_on_error(self, framework):
        """Test that validation stops on critical errors."""
        data = pd.DataFrame({
            "market_id": ["M001", "M002"]
            # Missing required 'price' field
        })
        
        is_valid, reports = framework.validate(data, stop_on_error=True)
        
        assert not is_valid
        assert "schema" in reports
        assert reports["schema"].has_errors()
        # Should stop after schema validation fails
        assert len(reports) == 1
    
    def test_source_specific_validators(self):
        """Test source-specific validation rules."""
        wfp_rules = {
            "required_fields": ["market", "commodity", "price", "currency"],
            "valid_currencies": ["YER", "USD"],
            "price_unit_check": True
        }
        
        validator = SourceSpecificValidator("WFP", wfp_rules)
        
        # Valid WFP data
        valid_data = pd.DataFrame({
            "market": ["Sana'a", "Aden"],
            "commodity": ["wheat", "wheat"],
            "price": [1000, 2000],
            "currency": ["YER", "YER"],
            "unit": ["50 kg", "50 kg"]
        })
        
        is_valid, report = validator.validate(valid_data)
        assert is_valid
        
        # Invalid currency
        invalid_data = valid_data.copy()
        invalid_data.loc[0, "currency"] = "EUR"
        
        is_valid, report = validator.validate(invalid_data)
        assert not is_valid
        assert any("currency" in v.message.lower() for v in report.validations)