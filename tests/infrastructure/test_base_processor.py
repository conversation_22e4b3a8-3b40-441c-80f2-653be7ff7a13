"""Unit tests for base processor framework."""

import asyncio
import pytest
from datetime import datetime, timedelta
from typing import Dict, List, Any
import pandas as pd
from unittest.mock import Mock, AsyncMock, patch
import httpx

from src.infrastructure.processors.base_processor import (
    BaseProcessor, DataFrameProcessor, SourceConfig, ProcessingProgress
)
from src.infrastructure.data_quality.validation_framework import (
    ValidationReport, ValidationLevel
)


class MockProcessor(BaseProcessor[Dict, str, List]):
    """Mock processor for testing base functionality."""
    
    async def _download_raw_data(self) -> Dict:
        """Mock download."""
        return {"data": ["item1", "item2"]}
    
    async def _extract_entities(self, raw_data: Dict) -> List[str]:
        """Mock extraction."""
        return raw_data.get("data", [])
    
    async def _transform_to_output(self, entities: List[str]) -> List:
        """Mock transformation."""
        return [f"transformed_{item}" for item in entities]
    
    async def _validate_output(self, output: List) -> ValidationReport:
        """Mock validation."""
        report = ValidationReport(source="mock")
        if output:
            report.add_validation(
                field="items",
                level=ValidationLevel.INFO,
                message=f"Validated {len(output)} items"
            )
        return report


class TestBaseProcessor:
    """Test base processor functionality."""
    
    @pytest.fixture
    def source_config(self):
        """Create test source config."""
        return SourceConfig(
            name="test_source",
            base_url="http://example.com",
            endpoints={"data": "/api/data"},
            rate_limit=10,
            timeout=30
        )
    
    @pytest.fixture
    def mock_cache_manager(self):
        """Create mock cache manager."""
        cache = AsyncMock()
        cache.get = AsyncMock(return_value=None)
        cache.set = AsyncMock()
        return cache
    
    @pytest.fixture
    def mock_validator(self):
        """Create mock validator."""
        validator = Mock()
        validator.validate = Mock(return_value=(True, ValidationReport("test")))
        return validator
    
    @pytest.fixture
    def processor(self, source_config, mock_cache_manager, mock_validator):
        """Create test processor."""
        return MockProcessor(source_config, mock_cache_manager, mock_validator)
    
    @pytest.mark.asyncio
    async def test_process_with_cache_miss(self, processor, mock_cache_manager):
        """Test processing when cache misses."""
        # Setup
        mock_cache_manager.get.return_value = None
        
        # Execute
        result = await processor.process()
        
        # Verify
        assert result == ["transformed_item1", "transformed_item2"]
        assert mock_cache_manager.get.called
        assert mock_cache_manager.set.called
    
    @pytest.mark.asyncio
    async def test_process_with_cache_hit(self, processor, mock_cache_manager):
        """Test processing when cache hits."""
        # Setup
        cached_data = ["cached_item1", "cached_item2"]
        mock_cache_manager.get.return_value = cached_data
        
        # Execute
        result = await processor.process()
        
        # Verify
        assert result == cached_data
        assert mock_cache_manager.get.called
        assert not mock_cache_manager.set.called
    
    @pytest.mark.asyncio
    async def test_retry_logic(self, processor):
        """Test retry logic on failure."""
        # Setup
        call_count = 0
        
        async def failing_download():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise httpx.TimeoutException("Timeout")
            return {"data": ["success"]}
        
        processor._download_raw_data = failing_download
        
        # Execute
        result = await processor.process()
        
        # Verify
        assert call_count == 3
        assert result == ["transformed_success"]
    
    @pytest.mark.asyncio
    async def test_validation_failure(self, processor, mock_validator):
        """Test handling of validation failure."""
        # Setup
        mock_validator.validate.return_value = (
            False,
            ValidationReport("test").add_validation(
                "data", ValidationLevel.ERROR, "Invalid data"
            )
        )
        
        # Execute & Verify
        with pytest.raises(ValueError, match="Validation failed"):
            await processor.process()
    
    @pytest.mark.asyncio
    async def test_progress_tracking(self, processor):
        """Test progress tracking during processing."""
        progress_updates = []
        
        def track_progress(progress: ProcessingProgress):
            progress_updates.append(progress.stage)
        
        # Execute
        await processor.process(progress_callback=track_progress)
        
        # Verify
        expected_stages = [
            "downloading",
            "extracting",
            "transforming",
            "validating",
            "complete"
        ]
        assert progress_updates == expected_stages
    
    @pytest.mark.asyncio
    async def test_empty_data_handling(self, processor):
        """Test handling of empty data."""
        # Setup
        processor._download_raw_data = AsyncMock(return_value={})
        
        # Execute
        result = await processor.process()
        
        # Verify
        assert result == []
    
    @pytest.mark.asyncio
    async def test_force_refresh(self, processor, mock_cache_manager):
        """Test force refresh bypasses cache."""
        # Setup
        mock_cache_manager.get.return_value = ["cached_data"]
        
        # Execute
        result = await processor.process(force_refresh=True)
        
        # Verify
        assert result == ["transformed_item1", "transformed_item2"]
        assert not mock_cache_manager.get.called
        assert mock_cache_manager.set.called


class TestDataFrameProcessor:
    """Test DataFrame processor specific functionality."""
    
    class MockDataFrameProcessor(DataFrameProcessor[pd.DataFrame, Dict, pd.DataFrame]):
        """Mock DataFrame processor."""
        
        async def _download_raw_data(self) -> pd.DataFrame:
            return pd.DataFrame({"col1": [1, 2], "col2": [3, 4]})
        
        async def _extract_entities(self, raw_data: pd.DataFrame) -> List[Dict]:
            return raw_data.to_dict('records')
        
        async def _transform_to_output(self, entities: List[Dict]) -> pd.DataFrame:
            df = pd.DataFrame(entities)
            df['transformed'] = True
            return df
        
        async def _validate_output(self, output: pd.DataFrame) -> ValidationReport:
            report = ValidationReport(source="dataframe")
            if not output.empty:
                report.add_validation(
                    field="dataframe",
                    level=ValidationLevel.INFO,
                    message=f"Validated {len(output)} rows"
                )
            return report
    
    @pytest.fixture
    def df_processor(self, source_config, mock_cache_manager, mock_validator):
        """Create test DataFrame processor."""
        return self.MockDataFrameProcessor(
            source_config, mock_cache_manager, mock_validator
        )
    
    @pytest.mark.asyncio
    async def test_dataframe_processing(self, df_processor):
        """Test DataFrame-specific processing."""
        # Execute
        result = await df_processor.process()
        
        # Verify
        assert isinstance(result, pd.DataFrame)
        assert 'transformed' in result.columns
        assert result['transformed'].all()
        assert len(result) == 2
    
    @pytest.mark.asyncio
    async def test_empty_dataframe_handling(self, df_processor):
        """Test handling of empty DataFrames."""
        # Setup
        df_processor._download_raw_data = AsyncMock(
            return_value=pd.DataFrame()
        )
        
        # Execute
        result = await df_processor.process()
        
        # Verify
        assert isinstance(result, pd.DataFrame)
        assert result.empty
    
    @pytest.mark.asyncio
    async def test_dataframe_validation(self, df_processor, mock_validator):
        """Test DataFrame validation integration."""
        # Setup
        def validate_df(df):
            report = ValidationReport("test")
            if 'transformed' not in df.columns:
                report.add_validation(
                    "columns", ValidationLevel.ERROR, "Missing transformed column"
                )
                return False, report
            return True, report
        
        mock_validator.validate = validate_df
        
        # Execute
        result = await df_processor.process()
        
        # Verify
        assert 'transformed' in result.columns