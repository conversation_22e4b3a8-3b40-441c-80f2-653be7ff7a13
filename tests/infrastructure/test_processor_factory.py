"""Unit tests for ProcessorFactory."""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from typing import List

from src.infrastructure.processors.processor_factory import (
    ProcessorFactory, ProcessorConfig, ProcessorType,
    create_price_processors, create_climate_processors
)
from src.infrastructure.processors.base_processor import SourceConfig, ValidationLevel
from src.infrastructure.processors.wfp_processor import WFPProcessor
from src.infrastructure.processors.conflict_processor import ConflictProcessor
from src.infrastructure.processors.geo_data_processor import CHIRPSProcessor
from src.core.domain.market.entities import Market
from src.core.domain.market.value_objects import Coordinates
from src.core.domain.shared.exceptions import YemenMarketException


class TestProcessorFactory:
    """Test suite for ProcessorFactory."""
    
    @pytest.fixture
    def cache_manager(self):
        """Mock cache manager."""
        cache = AsyncMock()
        cache.get = AsyncMock(return_value=None)
        cache.set = AsyncMock()
        return cache
    
    @pytest.fixture
    def validator(self):
        """Mock data validator."""
        return Mock()
    
    @pytest.fixture
    def spatial_service(self):
        """Mock spatial integration service."""
        service = AsyncMock()
        service.calculate_buffer = AsyncMock()
        service.point_in_polygon = AsyncMock()
        return service
    
    @pytest.fixture
    def temporal_service(self):
        """Mock temporal alignment service."""
        service = Mock()
        service.align_to_monthly = Mock()
        return service
    
    @pytest.fixture
    def hdx_client(self):
        """Mock HDX client."""
        client = AsyncMock()
        client.download_dataset = AsyncMock()
        return client
    
    @pytest.fixture
    def acled_client(self):
        """Mock ACLED client."""
        client = AsyncMock()
        client.get_events = AsyncMock()
        return client
    
    @pytest.fixture
    def markets(self) -> List[Market]:
        """Sample market list."""
        return [
            Market(
                id="M001",
                name="Sana'a Market",
                governorate="Sana'a",
                district="Old City",
                location=Coordinates(latitude=15.3694, longitude=44.1910),
                market_type="urban",
                is_accessible=True,
                active_since=None
            ),
            Market(
                id="M002",
                name="Aden Port Market",
                governorate="Aden",
                district="Crater",
                location=Coordinates(latitude=12.7855, longitude=45.0187),
                market_type="port",
                is_accessible=True,
                active_since=None
            )
        ]
    
    @pytest.fixture
    def factory(self, cache_manager, validator, spatial_service, temporal_service, 
                 hdx_client, acled_client, markets):
        """Create factory with all dependencies."""
        return ProcessorFactory(
            cache_manager=cache_manager,
            validator=validator,
            spatial_service=spatial_service,
            temporal_service=temporal_service,
            hdx_client=hdx_client,
            acled_client=acled_client,
            markets=markets
        )
    
    def test_factory_initialization(self, factory):
        """Test factory initializes with correct dependencies."""
        assert factory.cache_manager is not None
        assert factory.validator is not None
        assert factory.spatial_service is not None
        assert factory.temporal_service is not None
        assert factory.hdx_client is not None
        assert factory.acled_client is not None
        assert len(factory.markets) == 2
    
    def test_create_wfp_processor(self, factory):
        """Test creating a WFP price processor."""
        config = ProcessorConfig(
            processor_type=ProcessorType.WFP_PRICES,
            source_config=SourceConfig(
                source_id="wfp_food_prices",
                source_type="csv",
                update_frequency="weekly",
                cache_ttl=86400
            )
        )
        
        processor = factory.create_processor(config)
        
        assert isinstance(processor, WFPProcessor)
        assert processor.config.source_id == "wfp_food_prices"
        assert processor.cache == factory.cache_manager
        assert processor.validator == factory.validator
    
    def test_create_conflict_processor_requires_spatial_service(self, factory):
        """Test conflict processor creation requires spatial service."""
        config = ProcessorConfig(
            processor_type=ProcessorType.CONFLICT_EVENTS,
            source_config=SourceConfig(
                source_id="acled_conflict",
                source_type="api",
                update_frequency="weekly",
                cache_ttl=86400
            )
        )
        
        # Should work with spatial service
        processor = factory.create_processor(config)
        assert isinstance(processor, ConflictProcessor)
        
        # Should fail without spatial service
        factory.spatial_service = None
        with pytest.raises(YemenMarketException) as exc_info:
            factory.create_processor(config)
        assert "Spatial service required" in str(exc_info.value)
    
    def test_create_climate_processor_requires_markets(self, factory):
        """Test climate processor creation requires markets."""
        config = ProcessorConfig(
            processor_type=ProcessorType.CHIRPS_RAINFALL,
            source_config=SourceConfig(
                source_id="chirps_rainfall",
                source_type="netcdf",
                update_frequency="monthly",
                cache_ttl=2592000
            )
        )
        
        # Should work with markets
        processor = factory.create_processor(config)
        assert isinstance(processor, CHIRPSProcessor)
        
        # Should fail without markets
        factory.markets = []
        with pytest.raises(YemenMarketException) as exc_info:
            factory.create_processor(config)
        assert "Markets list required" in str(exc_info.value)
    
    def test_processor_config_string_to_enum_conversion(self):
        """Test ProcessorConfig converts string to ProcessorType."""
        config = ProcessorConfig(
            processor_type="wfp_prices",  # String instead of enum
            source_config=SourceConfig(
                source_id="test",
                source_type="csv",
                update_frequency="daily",
                cache_ttl=3600
            )
        )
        
        assert config.processor_type == ProcessorType.WFP_PRICES
    
    def test_processor_config_invalid_type_raises_error(self):
        """Test ProcessorConfig raises error for invalid type."""
        with pytest.raises(ValueError) as exc_info:
            ProcessorConfig(
                processor_type="invalid_processor",
                source_config=SourceConfig(
                    source_id="test",
                    source_type="csv",
                    update_frequency="daily",
                    cache_ttl=3600
                )
            )
        assert "Unknown processor type" in str(exc_info.value)
    
    def test_create_multiple_processors(self, factory):
        """Test creating multiple processors at once."""
        configs = [
            ProcessorConfig(
                processor_type=ProcessorType.WFP_PRICES,
                source_config=SourceConfig(
                    source_id="wfp_prices",
                    source_type="csv",
                    update_frequency="weekly",
                    cache_ttl=86400
                )
            ),
            ProcessorConfig(
                processor_type=ProcessorType.ACAPS_ZONES,
                source_config=SourceConfig(
                    source_id="acaps_zones",
                    source_type="shapefile",
                    update_frequency="monthly",
                    cache_ttl=2592000
                )
            )
        ]
        
        processors = factory.create_multiple_processors(configs)
        
        assert len(processors) == 2
        assert "wfp_prices" in processors
        assert "acaps_zones" in processors
        assert isinstance(processors["wfp_prices"], WFPProcessor)
    
    def test_create_multiple_processors_continues_on_error(self, factory):
        """Test that create_multiple continues even if one processor fails."""
        # Make conflict processor fail by removing spatial service
        factory.spatial_service = None
        
        configs = [
            ProcessorConfig(
                processor_type=ProcessorType.WFP_PRICES,
                source_config=SourceConfig(
                    source_id="wfp_prices",
                    source_type="csv",
                    update_frequency="weekly",
                    cache_ttl=86400
                )
            ),
            ProcessorConfig(
                processor_type=ProcessorType.CONFLICT_EVENTS,  # This will fail
                source_config=SourceConfig(
                    source_id="conflict_events",
                    source_type="api",
                    update_frequency="weekly",
                    cache_ttl=86400
                )
            )
        ]
        
        processors = factory.create_multiple_processors(configs)
        
        # Should still create the WFP processor
        assert len(processors) == 1
        assert "wfp_prices" in processors
        assert "conflict_events" not in processors
    
    def test_processor_params_passed_through(self, factory):
        """Test that processor_params are passed to the processor."""
        config = ProcessorConfig(
            processor_type=ProcessorType.WFP_PRICES,
            source_config=SourceConfig(
                source_id="wfp_prices",
                source_type="api",
                update_frequency="weekly",
                cache_ttl=86400
            ),
            processor_params={
                "api_key": "test_key",
                "base_url": "https://api.wfp.org"
            }
        )
        
        with patch('src.infrastructure.processors.wfp_processor.WFPProcessor.__init__', 
                   return_value=None) as mock_init:
            factory.create_processor(config)
            
            # Check that params were passed
            _, kwargs = mock_init.call_args
            assert kwargs.get('api_key') == "test_key"
            assert kwargs.get('base_url') == "https://api.wfp.org"
    
    def test_register_custom_processor(self, factory):
        """Test registering a custom processor type."""
        # Create a mock processor class
        class CustomProcessor(Mock):
            pass
        
        # Register it
        custom_type = ProcessorType.WFP_PRICES  # Reuse existing type for test
        factory.register_custom_processor(custom_type, CustomProcessor)
        
        # Create processor config
        config = ProcessorConfig(
            processor_type=custom_type,
            source_config=SourceConfig(
                source_id="custom",
                source_type="custom",
                update_frequency="daily",
                cache_ttl=3600
            )
        )
        
        # Should create custom processor
        processor = factory.create_processor(config)
        assert isinstance(processor, CustomProcessor)
    
    def test_default_raster_config_for_climate_processors(self, factory):
        """Test that climate processors get appropriate default raster configs."""
        # CHIRPS config
        config = ProcessorConfig(
            processor_type=ProcessorType.CHIRPS_RAINFALL,
            source_config=SourceConfig(
                source_id="chirps",
                source_type="netcdf",
                update_frequency="monthly",
                cache_ttl=2592000
            )
        )
        
        processor = factory.create_processor(config)
        assert processor.raster_config.aggregation_method == "sum"
        assert processor.raster_config.scale_factor == 1.0
        
        # MODIS config
        config = ProcessorConfig(
            processor_type=ProcessorType.MODIS_NDVI,
            source_config=SourceConfig(
                source_id="modis",
                source_type="geotiff",
                update_frequency="16_days",
                cache_ttl=1382400
            )
        )
        
        processor = factory.create_processor(config)
        assert processor.raster_config.aggregation_method == "mean"
        assert processor.raster_config.scale_factor == 0.0001
    
    def test_create_price_processors_helper(self, factory):
        """Test the create_price_processors helper function."""
        processors = create_price_processors(factory, include_currency_aware=True)
        
        assert len(processors) == 2
        assert "wfp_food_prices" in processors
        assert "wfp_currency_aware_prices" in processors
        
        # Test without currency aware
        processors = create_price_processors(factory, include_currency_aware=False)
        assert len(processors) == 1
        assert "wfp_food_prices" in processors
        assert "wfp_currency_aware_prices" not in processors
    
    def test_create_climate_processors_helper(self, factory, markets):
        """Test the create_climate_processors helper function."""
        processors = create_climate_processors(factory, markets)
        
        assert len(processors) == 2
        assert "chirps_rainfall" in processors
        assert "modis_ndvi" in processors
        
        # Check that markets were set
        assert factory.markets == markets
    
    def test_unsupported_processor_type_raises_error(self, factory):
        """Test that unsupported processor types raise appropriate error."""
        config = ProcessorConfig(
            processor_type=ProcessorType.OCHA_AID,  # Not in registry
            source_config=SourceConfig(
                source_id="ocha",
                source_type="excel",
                update_frequency="monthly",
                cache_ttl=2592000
            )
        )
        
        with pytest.raises(ValueError) as exc_info:
            factory.create_processor(config)
        assert "Unsupported processor type" in str(exc_info.value)
    
    def test_hdx_client_injection_for_hdx_sources(self, factory):
        """Test that HDX client is injected for HDX-based sources."""
        config = ProcessorConfig(
            processor_type=ProcessorType.WFP_PRICES,
            source_config=SourceConfig(
                source_id="wfp_hdx",
                source_type="hdx",  # HDX source type
                update_frequency="weekly",
                cache_ttl=86400
            )
        )
        
        with patch('src.infrastructure.processors.wfp_processor.WFPProcessor.__init__', 
                   return_value=None) as mock_init:
            factory.create_processor(config)
            
            # Check that HDX client was passed
            _, kwargs = mock_init.call_args
            assert 'hdx_client' in kwargs
            assert kwargs['hdx_client'] == factory.hdx_client