"""Unit tests for error handling and recovery."""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch

from src.infrastructure.processors.exceptions import (
    ProcessorException, DownloadException, APIException, TimeoutException,
    AuthenticationException, ValidationException, SchemaValidationException,
    DataQualityException, TransformationException, MappingException,
    ErrorContext, ErrorSeverity, ErrorCategory, create_processor_exception
)
from src.infrastructure.processors.error_recovery import (
    ErrorRecoveryManager, RecoveryConfig, RecoveryStrategy,
    CircuitState, with_retry, with_circuit_breaker
)


class TestProcessorExceptions:
    """Test suite for processor exceptions."""
    
    def test_base_exception_initialization(self):
        """Test ProcessorException initialization."""
        context = ErrorContext(
            source_id="test_source",
            processor_type="test_processor",
            stage="download"
        )
        
        exc = ProcessorException(
            "Test error",
            context=context,
            severity=ErrorSeverity.HIGH,
            category=ErrorCategory.NETWORK,
            is_retryable=True,
            recovery_suggestions=["Try again", "Check network"]
        )
        
        assert str(exc) == "Test error"
        assert exc.context == context
        assert exc.severity == ErrorSeverity.HIGH
        assert exc.category == ErrorCategory.NETWORK
        assert exc.is_retryable is True
        assert len(exc.recovery_suggestions) == 2
    
    def test_exception_context_addition(self):
        """Test adding context to exceptions."""
        exc = ProcessorException("Test error")
        exc.context = ErrorContext(
            source_id="test",
            processor_type="test",
            stage="validate"
        )
        
        exc.add_context(row_count=100, error_count=5)
        
        assert exc.context.metadata["row_count"] == 100
        assert exc.context.metadata["error_count"] == 5
    
    def test_exception_to_dict(self):
        """Test exception serialization."""
        context = ErrorContext(
            source_id="wfp_prices",
            processor_type="WFPProcessor",
            stage="download",
            retry_count=2
        )
        
        exc = APIException(
            "API rate limit exceeded",
            status_code=429,
            context=context
        )
        
        exc_dict = exc.to_dict()
        
        assert exc_dict["error_type"] == "APIException"
        assert exc_dict["message"] == "API rate limit exceeded"
        assert exc_dict["category"] == ErrorCategory.NETWORK.value
        assert exc_dict["is_retryable"] is True
        assert exc_dict["context"]["retry_count"] == 2
    
    def test_api_exception_retryable_status_codes(self):
        """Test APIException sets retryable based on status code."""
        # Retryable status codes
        for status in [429, 500, 502, 503, 504]:
            exc = APIException("Error", status_code=status)
            assert exc.is_retryable is True
            assert len(exc.recovery_suggestions) > 0
        
        # Non-retryable status codes
        for status in [400, 401, 403, 404]:
            exc = APIException("Error", status_code=status)
            assert exc.is_retryable is False
    
    def test_timeout_exception_defaults(self):
        """Test TimeoutException defaults."""
        exc = TimeoutException("Request timed out", timeout=30.0)
        
        assert exc.is_retryable is True
        assert exc.timeout == 30.0
        assert "Increase timeout beyond 30" in exc.recovery_suggestions[0]
    
    def test_schema_validation_exception(self):
        """Test SchemaValidationException with missing columns."""
        exc = SchemaValidationException(
            "Schema validation failed",
            missing_columns=["price", "date"],
            unexpected_columns=["extra_field"]
        )
        
        assert exc.context.metadata["missing_columns"] == ["price", "date"]
        assert exc.context.metadata["unexpected_columns"] == ["extra_field"]
        assert any("Add required columns: price, date" in s for s in exc.recovery_suggestions)
    
    def test_data_quality_exception_with_metrics(self):
        """Test DataQualityException with quality metrics."""
        metrics = {
            "null_percentage": 60,
            "duplicate_count": 100,
            "total_rows": 1000
        }
        
        exc = DataQualityException(
            "Poor data quality",
            quality_metrics=metrics
        )
        
        assert exc.quality_metrics == metrics
        assert exc.context.metadata["null_percentage"] == 60
        assert any("High null percentage" in s for s in exc.recovery_suggestions)
        assert any("Remove duplicate records" in s for s in exc.recovery_suggestions)
    
    def test_mapping_exception(self):
        """Test MappingException details."""
        exc = MappingException(
            "Field mapping failed",
            source_field="item_name",
            target_field="commodity_id"
        )
        
        assert exc.source_field == "item_name"
        assert exc.target_field == "commodity_id"
        assert exc.context.metadata["mapping"] == "item_name -> commodity_id"
    
    def test_create_processor_exception_timeout(self):
        """Test creating processor exception from TimeoutError."""
        context = ErrorContext(
            source_id="test",
            processor_type="TestProcessor",
            stage="download"
        )
        
        original_error = TimeoutError("Connection timed out")
        exc = create_processor_exception(original_error, context, "TestProcessor")
        
        assert isinstance(exc, TimeoutException)
        assert "Timeout in TestProcessor" in str(exc)
        assert exc.context == context
    
    def test_create_processor_exception_key_error(self):
        """Test creating processor exception from KeyError."""
        context = ErrorContext(
            source_id="test",
            processor_type="TestProcessor",
            stage="transform"
        )
        
        original_error = KeyError("missing_field")
        exc = create_processor_exception(original_error, context, "TestProcessor")
        
        assert isinstance(exc, MappingException)
        assert exc.source_field == "missing_field"


class TestErrorRecoveryManager:
    """Test suite for error recovery manager."""
    
    @pytest.fixture
    def recovery_manager(self):
        """Create recovery manager instance."""
        config = RecoveryConfig(
            max_retries=3,
            initial_backoff=0.1,  # Short for tests
            max_backoff=1.0,
            backoff_factor=2.0,
            circuit_breaker_threshold=3
        )
        return ErrorRecoveryManager(config)
    
    @pytest.mark.asyncio
    async def test_retry_on_recoverable_exception(self, recovery_manager):
        """Test retry logic for recoverable exceptions."""
        call_count = 0
        
        @recovery_manager.with_recovery(max_retries=3)
        async def flaky_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise APIException("Temporary failure")
            return "success"
        
        result = await flaky_function()
        
        assert result == "success"
        assert call_count == 3
    
    @pytest.mark.asyncio
    async def test_no_retry_on_non_recoverable_exception(self, recovery_manager):
        """Test no retry for non-recoverable exceptions."""
        call_count = 0
        
        @recovery_manager.with_recovery()
        async def failing_function():
            nonlocal call_count
            call_count += 1
            raise ValueError("Non-recoverable error")
        
        with pytest.raises(ValueError):
            await failing_function()
        
        assert call_count == 1  # No retries
    
    @pytest.mark.asyncio
    async def test_max_retries_exceeded(self, recovery_manager):
        """Test behavior when max retries exceeded."""
        call_count = 0
        
        @recovery_manager.with_recovery(max_retries=2)
        async def always_failing():
            nonlocal call_count
            call_count += 1
            raise APIException("Persistent failure")
        
        with pytest.raises(APIException):
            await always_failing()
        
        assert call_count == 2  # Max retries
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_opens_after_failures(self, recovery_manager):
        """Test circuit breaker opens after threshold failures."""
        failure_count = 0
        
        async def failing_service():
            nonlocal failure_count
            failure_count += 1
            raise APIException("Service error")
        
        # Fail enough times to open circuit
        for i in range(3):
            with pytest.raises(APIException):
                async with recovery_manager.circuit_breaker("test_service"):
                    await failing_service()
        
        # Circuit should now be open
        breaker = recovery_manager.circuit_breakers["test_service"]
        assert breaker.state == CircuitState.OPEN
        assert breaker.failure_count == 3
        
        # Next call should fail immediately
        with pytest.raises(APIException) as exc_info:
            async with recovery_manager.circuit_breaker("test_service"):
                await failing_service()
        
        assert "Circuit breaker OPEN" in str(exc_info.value)
        assert failure_count == 3  # No new calls made
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_half_open_recovery(self, recovery_manager):
        """Test circuit breaker recovery through half-open state."""
        # Open the circuit
        breaker = recovery_manager._get_or_create_breaker("test_service")
        breaker.state = CircuitState.OPEN
        breaker.last_failure_time = datetime.now() - timedelta(minutes=10)
        
        success_count = 0
        
        async def recovering_service():
            nonlocal success_count
            success_count += 1
            return "success"
        
        # First successful call in half-open state
        async with recovery_manager.circuit_breaker("test_service"):
            result = await recovering_service()
        
        assert result == "success"
        assert breaker.state == CircuitState.HALF_OPEN
        
        # Need 3 consecutive successes to close
        for _ in range(2):
            async with recovery_manager.circuit_breaker("test_service"):
                await recovering_service()
        
        assert breaker.state == CircuitState.CLOSED
        assert breaker.failure_count == 0
    
    @pytest.mark.asyncio
    async def test_fallback_mechanism(self, recovery_manager):
        """Test fallback to alternative function."""
        async def primary():
            raise APIException("Primary failed")
        
        async def fallback():
            return "fallback_result"
        
        result = await recovery_manager.with_fallback(
            primary,
            fallback,
            fallback_on=(APIException,)
        )
        
        assert result == "fallback_result"
    
    @pytest.mark.asyncio
    async def test_partial_processing_success(self, recovery_manager):
        """Test partial processing with acceptable failure rate."""
        items = list(range(10))
        
        async def process_item(item):
            if item in [3, 7]:  # 20% failure rate
                raise ValueError(f"Failed on {item}")
            return item * 2
        
        results, failures = await recovery_manager.partial_processing(
            items,
            process_item,
            threshold=0.7  # 70% success required
        )
        
        assert len(results) == 8
        assert len(failures) == 2
        assert failures[0][0] == 3
        assert failures[1][0] == 7
    
    @pytest.mark.asyncio
    async def test_partial_processing_threshold_failure(self, recovery_manager):
        """Test partial processing fails when below threshold."""
        items = list(range(10))
        
        async def process_item(item):
            if item < 7:  # 70% failure rate
                raise ValueError(f"Failed on {item}")
            return item * 2
        
        with pytest.raises(ProcessorException) as exc_info:
            await recovery_manager.partial_processing(
                items,
                process_item,
                threshold=0.5  # 50% success required
            )
        
        assert "30.0% success rate below threshold 50.0%" in str(exc_info.value)
    
    def test_error_queue_management(self, recovery_manager):
        """Test error queuing for later retry."""
        # Queue some errors
        for i in range(5):
            error = ProcessorException(f"Error {i}")
            recovery_manager.queue_error_for_retry(error)
        
        assert len(recovery_manager.error_queue) == 5
        
        # Test queue size limit
        for i in range(1000):
            recovery_manager.queue_error_for_retry(
                ProcessorException(f"Error {i}")
            )
        
        assert len(recovery_manager.error_queue) == 1000  # Limited
    
    @pytest.mark.asyncio
    async def test_retry_queued_errors(self, recovery_manager):
        """Test retrying queued errors."""
        # Queue some errors
        old_error = ProcessorException("Old error")
        recovery_manager.error_queue.append(
            (datetime.now() - timedelta(hours=25), old_error)
        )
        
        recent_errors = []
        for i in range(3):
            error = ProcessorException(f"Recent error {i}")
            recent_errors.append(error)
            recovery_manager.queue_error_for_retry(error)
        
        retry_count = 0
        
        async def retry_func(error):
            nonlocal retry_count
            retry_count += 1
            if "Recent error 1" in str(error):
                raise ValueError("Still failing")
            return "recovered"
        
        successful, failed = await recovery_manager.retry_queued_errors(
            retry_func,
            max_age=timedelta(hours=24)
        )
        
        assert successful == 2  # 2 recovered
        assert failed == 1      # 1 still failing
        assert retry_count == 3  # Only recent errors retried
    
    def test_recovery_report(self, recovery_manager):
        """Test recovery statistics report."""
        # Record some stats
        recovery_manager._record_recovery_stats("test_func", "success", 1)
        recovery_manager._record_recovery_stats("test_func", "failed", 3)
        
        # Set up circuit breaker
        breaker = recovery_manager._get_or_create_breaker("api_service")
        breaker.total_requests = 100
        breaker.total_failures = 10
        breaker.state = CircuitState.OPEN
        
        report = recovery_manager.get_recovery_report()
        
        assert report["recovery_stats"]["test_func"]["success"] == 1
        assert report["recovery_stats"]["test_func"]["failed"] == 1
        assert report["circuit_breakers"]["api_service"]["state"] == "open"
        assert report["circuit_breakers"]["api_service"]["failure_rate"] == 0.1


class TestRecoveryDecorators:
    """Test recovery decorator functions."""
    
    @pytest.mark.asyncio
    async def test_with_retry_decorator(self):
        """Test @with_retry decorator."""
        call_count = 0
        
        @with_retry(max_retries=2, backoff_factor=1.5)
        async def flaky_operation():
            nonlocal call_count
            call_count += 1
            if call_count < 2:
                raise TimeoutException("Timeout", timeout=5.0)
            return "success"
        
        result = await flaky_operation()
        
        assert result == "success"
        assert call_count == 2
    
    @pytest.mark.asyncio
    async def test_with_circuit_breaker_decorator(self):
        """Test @with_circuit_breaker decorator."""
        
        @with_circuit_breaker("protected_service")
        async def protected_operation():
            return "protected_result"
        
        result = await protected_operation()
        assert result == "protected_result"