"""Unit tests for DataFrame processor functionality."""

import asyncio
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Any
from unittest.mock import Mock, AsyncMock, patch
import io

from src.infrastructure.processors.base_processor import (
    DataFrameProcessor, SourceConfig, ProcessingResult, ValidationLevel
)
from src.infrastructure.data_quality.validation_framework import ValidationReport
from src.core.domain.market.entities import Price
from src.core.domain.market.value_objects import MarketId, CommodityId


class MockPrice:
    """Mock price entity for testing."""
    def __init__(self, market_id: str, commodity_id: str, value: float, date: datetime):
        self.market_id = market_id
        self.commodity_id = commodity_id
        self.value = Decimal(str(value))
        self.date = date
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            'market_id': self.market_id,
            'commodity_id': self.commodity_id,
            'value': float(self.value),
            'date': self.date
        }


class TestDataFrameProcessor(DataFrameProcessor[MockPrice]):
    """Test implementation of DataFrame processor."""
    
    def get_required_columns(self) -> List[str]:
        return ['date', 'market', 'commodity', 'price']
    
    async def validate_specific(self, raw_data: pd.DataFrame) -> ValidationReport:
        report = ValidationReport(source="test")
        
        # Check price ranges
        if 'price' in raw_data.columns:
            if (raw_data['price'] < 0).any():
                report.add_validation(
                    field="price",
                    level=ValidationLevel.ERROR,
                    message="Negative prices found"
                )
            if (raw_data['price'] > 10000).any():
                report.add_validation(
                    field="price",
                    level=ValidationLevel.WARNING,
                    message="Very high prices found"
                )
        
        return report
    
    async def download(self) -> pd.DataFrame:
        # Return test data
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        markets = ['Market_A', 'Market_B', 'Market_C']
        commodities = ['wheat', 'rice', 'sugar']
        
        data = []
        for date in dates:
            for market in markets:
                for commodity in commodities:
                    price = np.random.uniform(100, 1000)
                    data.append({
                        'date': date,
                        'market': market,
                        'commodity': commodity,
                        'price': price
                    })
        
        return pd.DataFrame(data)
    
    async def transform(self, raw_data: pd.DataFrame) -> List[MockPrice]:
        prices = []
        for _, row in raw_data.iterrows():
            price = MockPrice(
                market_id=row['market'],
                commodity_id=row['commodity'],
                value=row['price'],
                date=row['date']
            )
            prices.append(price)
        return prices
    
    async def aggregate(self, entities: List[MockPrice]) -> pd.DataFrame:
        # Convert to DataFrame
        df = pd.DataFrame([e.to_dict() for e in entities])
        
        # Monthly aggregation
        df['year_month'] = pd.to_datetime(df['date']).dt.to_period('M')
        
        monthly = df.groupby(['market_id', 'commodity_id', 'year_month']).agg({
            'value': ['mean', 'count']
        }).reset_index()
        
        monthly.columns = ['market_id', 'commodity_id', 'year_month', 'avg_price', 'n_observations']
        
        return monthly


class TestDataFrameProcessorUnit:
    """Unit tests for DataFrame processor."""
    
    @pytest.fixture
    def config(self):
        return SourceConfig(
            source_id="test_source",
            source_type="csv",
            update_frequency="daily",
            cache_ttl=3600,
            validation_level=ValidationLevel.STRICT
        )
    
    @pytest.fixture
    def processor(self, config):
        cache_manager = AsyncMock()
        cache_manager.get = AsyncMock(return_value=None)
        cache_manager.set = AsyncMock()
        
        validator = Mock()
        
        return TestDataFrameProcessor(config, cache_manager, validator)
    
    @pytest.mark.asyncio
    async def test_validation_catches_missing_columns(self, processor):
        """Test that validation detects missing required columns."""
        # DataFrame missing 'commodity' column
        df = pd.DataFrame({
            'date': pd.date_range('2024-01-01', periods=5),
            'market': ['Market_A'] * 5,
            'price': [100, 200, 150, 175, 180]
        })
        
        report = await processor.validate(df)
        
        assert not report.is_valid
        assert any('Missing required columns' in err for err in report.errors)
        assert "{'commodity'}" in str(report.errors)
    
    @pytest.mark.asyncio
    async def test_validation_detects_empty_dataframe(self, processor):
        """Test validation of empty DataFrame."""
        df = pd.DataFrame()
        
        report = await processor.validate(df)
        
        assert not report.is_valid
        assert any('empty' in err.lower() for err in report.errors)
    
    @pytest.mark.asyncio
    async def test_validation_warns_on_duplicates(self, processor):
        """Test that validation warns about duplicate rows."""
        df = pd.DataFrame({
            'date': ['2024-01-01'] * 5,
            'market': ['Market_A'] * 5,
            'commodity': ['wheat'] * 5,
            'price': [100] * 5
        })
        
        report = await processor.validate(df)
        
        assert report.is_valid  # Duplicates are warnings, not errors
        assert len(report.warnings) > 0
        assert any('duplicate' in warn.lower() for warn in report.warnings)
    
    @pytest.mark.asyncio
    async def test_specific_validation_checks_price_ranges(self, processor):
        """Test source-specific validation logic."""
        df = pd.DataFrame({
            'date': pd.date_range('2024-01-01', periods=3),
            'market': ['Market_A'] * 3,
            'commodity': ['wheat'] * 3,
            'price': [-10, 500, 15000]  # negative, normal, very high
        })
        
        report = await processor.validate(df)
        
        assert not report.is_valid  # Negative price is an error
        assert any('Negative prices' in str(report.errors))
        assert any('Very high prices' in str(report.warnings))
    
    @pytest.mark.asyncio
    async def test_memory_usage_tracking(self, processor):
        """Test that validation tracks memory usage."""
        df = pd.DataFrame({
            'date': pd.date_range('2024-01-01', periods=1000),
            'market': ['Market_A'] * 1000,
            'commodity': ['wheat'] * 1000,
            'price': np.random.uniform(100, 1000, 1000)
        })
        
        report = await processor.validate(df)
        
        assert 'memory_usage' in report.metrics
        assert report.metrics['memory_usage'] > 0
        assert report.metrics['row_count'] == 1000
        assert report.metrics['column_count'] == 4
    
    @pytest.mark.asyncio
    async def test_chunked_processing(self, processor):
        """Test processing large DataFrame in chunks."""
        # Create large DataFrame
        n_rows = 10000
        df = pd.DataFrame({
            'date': pd.date_range('2024-01-01', periods=n_rows, freq='H'),
            'market': np.random.choice(['Market_A', 'Market_B', 'Market_C'], n_rows),
            'commodity': np.random.choice(['wheat', 'rice', 'sugar'], n_rows),
            'price': np.random.uniform(100, 1000, n_rows)
        })
        
        # Mock download to return large DataFrame
        processor.download = AsyncMock(return_value=df)
        
        # Process
        result = await processor.process()
        
        assert result.success
        assert isinstance(result.data, pd.DataFrame)
        assert len(result.data) > 0  # Should have aggregated data
    
    @pytest.mark.asyncio
    async def test_transform_to_entities(self, processor):
        """Test transformation from DataFrame to entities."""
        df = pd.DataFrame({
            'date': pd.date_range('2024-01-01', periods=3),
            'market': ['Market_A', 'Market_B', 'Market_C'],
            'commodity': ['wheat', 'rice', 'sugar'],
            'price': [100, 200, 300]
        })
        
        entities = await processor.transform(df)
        
        assert len(entities) == 3
        assert all(isinstance(e, MockPrice) for e in entities)
        assert entities[0].market_id == 'Market_A'
        assert entities[0].commodity_id == 'wheat'
        assert entities[0].value == Decimal('100')
    
    @pytest.mark.asyncio
    async def test_monthly_aggregation(self, processor):
        """Test aggregation to monthly frequency."""
        # Create entities spanning multiple months
        entities = [
            MockPrice('Market_A', 'wheat', 100, datetime(2024, 1, 1)),
            MockPrice('Market_A', 'wheat', 110, datetime(2024, 1, 15)),
            MockPrice('Market_A', 'wheat', 120, datetime(2024, 1, 31)),
            MockPrice('Market_A', 'wheat', 130, datetime(2024, 2, 1)),
            MockPrice('Market_B', 'rice', 200, datetime(2024, 1, 1)),
            MockPrice('Market_B', 'rice', 210, datetime(2024, 1, 15)),
        ]
        
        result = await processor.aggregate(entities)
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 3  # Market_A wheat (Jan & Feb), Market_B rice (Jan)
        
        # Check aggregation values
        market_a_jan = result[
            (result['market_id'] == 'Market_A') & 
            (result['year_month'] == pd.Period('2024-01'))
        ].iloc[0]
        
        assert market_a_jan['avg_price'] == pytest.approx(110.0)  # (100+110+120)/3
        assert market_a_jan['n_observations'] == 3
    
    @pytest.mark.asyncio
    async def test_progress_callback(self, processor):
        """Test progress tracking during processing."""
        progress_updates = []
        
        def track_progress(stage: str, pct: float):
            progress_updates.append((stage, pct))
        
        # Small dataset for quick test
        processor.download = AsyncMock(return_value=pd.DataFrame({
            'date': ['2024-01-01'],
            'market': ['Market_A'],
            'commodity': ['wheat'],
            'price': [100]
        }))
        
        result = await processor.process(progress_callback=track_progress)
        
        assert result.success
        assert len(progress_updates) > 0
        assert progress_updates[-1][1] == 100  # Final progress is 100%
        
        # Check that key stages are reported
        stages = [stage for stage, _ in progress_updates]
        assert any('Download' in stage for stage in stages)
        assert any('Validat' in stage for stage in stages)
        assert any('Transform' in stage for stage in stages)
        assert any('Aggregat' in stage for stage in stages)
    
    @pytest.mark.asyncio
    async def test_csv_download_simulation(self, processor):
        """Test downloading CSV data."""
        # Simulate CSV download
        csv_data = """date,market,commodity,price
2024-01-01,Market_A,wheat,100
2024-01-02,Market_A,wheat,105
2024-01-01,Market_B,rice,200"""
        
        async def mock_download():
            return pd.read_csv(io.StringIO(csv_data))
        
        processor.download = mock_download
        
        result = await processor.process()
        
        assert result.success
        assert len(result.data) == 2  # Two market-commodity combinations
    
    @pytest.mark.asyncio
    async def test_error_handling_in_transform(self, processor):
        """Test error handling during transformation."""
        # DataFrame with invalid data
        df = pd.DataFrame({
            'date': ['invalid_date', '2024-01-02'],
            'market': ['Market_A', 'Market_B'],
            'commodity': ['wheat', 'rice'],
            'price': ['not_a_number', 200]
        })
        
        # Override transform to raise error
        async def failing_transform(raw_data):
            raise ValueError("Invalid date format")
        
        processor.transform = failing_transform
        processor.download = AsyncMock(return_value=df)
        
        result = await processor.process()
        
        assert not result.success
        assert "Invalid date format" in result.error_summary
    
    @pytest.mark.asyncio
    async def test_validation_level_enforcement(self, processor, config):
        """Test that validation level is respected."""
        # Set to LENIENT
        config.validation_level = ValidationLevel.LENIENT
        
        # DataFrame with validation errors
        df = pd.DataFrame({
            'date': pd.date_range('2024-01-01', periods=2),
            'market': ['Market_A'] * 2,
            'commodity': ['wheat'] * 2,
            'price': [-10, -20]  # All negative prices
        })
        
        processor.download = AsyncMock(return_value=df)
        
        # Should proceed despite validation errors in LENIENT mode
        result = await processor.process()
        
        # In LENIENT mode, it should still process
        assert result.success or not result.success  # Depends on implementation
        assert result.validation_report.has_errors()
    
    @pytest.mark.asyncio
    async def test_dataframe_dtypes_preservation(self, processor):
        """Test that DataFrame dtypes are preserved through processing."""
        df = pd.DataFrame({
            'date': pd.date_range('2024-01-01', periods=3),
            'market': pd.Categorical(['Market_A', 'Market_B', 'Market_A']),
            'commodity': ['wheat', 'rice', 'wheat'],
            'price': [100.5, 200.75, 150.25]
        })
        
        processor.download = AsyncMock(return_value=df)
        
        result = await processor.process()
        
        assert result.success
        # Check that result maintains proper dtypes
        assert pd.api.types.is_numeric_dtype(result.data['avg_price'])