"""Unit tests for HDX Enhanced Client."""

import pytest
import asyncio
from datetime import datetime
from pathlib import Path
import tempfile
import zipfile
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import httpx

from src.infrastructure.external_services.hdx_enhanced_client import (
    HDXEnhancedClient, DataSource, DownloadResult
)


class TestHDXEnhancedClient:
    """Test HDX Enhanced Client functionality."""
    
    @pytest.fixture
    def client(self):
        """Create HDX client."""
        return HDXEnhancedClient(
            hdx_api_key="test_key",
            max_concurrent_downloads=2
        )
    
    @pytest.fixture
    def mock_sources(self):
        """Create mock data sources."""
        return [
            DataSource(
                name="ACLED Conflict",
                dataset_id="acled-data-yemen",
                resource_pattern="xlsx",
                update_frequency="weekly"
            ),
            DataSource(
                name="WFP Prices",
                dataset_id="wfp-food-prices-yemen",
                resource_pattern="csv",
                update_frequency="monthly"
            ),
            DataSource(
                name="OCHA 3W",
                dataset_id="yemen-3w-operational-presence",
                resource_pattern="xlsx",
                update_frequency="monthly"
            )
        ]
    
    @pytest.mark.asyncio
    async def test_client_initialization(self, client):
        """Test client initialization."""
        assert client.hdx_api_key == "test_key"
        assert client.max_concurrent_downloads == 2
        assert client.download_dir.exists()
    
    @pytest.mark.asyncio
    async def test_download_single_source(self, client, mock_sources):
        """Test downloading a single data source."""
        source = mock_sources[0]
        
        # Mock HTTP response
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.content = b"test data content"
        mock_response.headers = {"content-type": "application/vnd.ms-excel"}
        
        with patch('httpx.AsyncClient.get', return_value=mock_response):
            result = await client.download_source(source)
        
        assert isinstance(result, DownloadResult)
        assert result.success
        assert result.source == source
        assert result.file_path.exists()
        assert result.file_size > 0
    
    @pytest.mark.asyncio
    async def test_download_with_retry(self, client, mock_sources):
        """Test download retry logic."""
        source = mock_sources[0]
        
        # Mock failing then succeeding
        call_count = 0
        
        async def mock_get(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            
            if call_count < 3:
                raise httpx.TimeoutException("Timeout")
            
            response = AsyncMock()
            response.status_code = 200
            response.content = b"success"
            response.headers = {"content-type": "text/csv"}
            return response
        
        with patch('httpx.AsyncClient.get', side_effect=mock_get):
            result = await client.download_source(source)
        
        assert result.success
        assert call_count == 3  # Two failures + one success
    
    @pytest.mark.asyncio
    async def test_concurrent_downloads(self, client, mock_sources):
        """Test concurrent download limiting."""
        # Track concurrent downloads
        concurrent_count = 0
        max_concurrent = 0
        
        async def mock_download(*args, **kwargs):
            nonlocal concurrent_count, max_concurrent
            concurrent_count += 1
            max_concurrent = max(max_concurrent, concurrent_count)
            
            # Simulate download time
            await asyncio.sleep(0.1)
            
            concurrent_count -= 1
            
            response = AsyncMock()
            response.status_code = 200
            response.content = b"data"
            response.headers = {"content-type": "text/csv"}
            return response
        
        with patch('httpx.AsyncClient.get', side_effect=mock_download):
            # Download all sources
            results = await client.download_all(mock_sources)
        
        assert len(results) == len(mock_sources)
        assert all(r.success for r in results)
        assert max_concurrent <= client.max_concurrent_downloads
    
    @pytest.mark.asyncio
    async def test_nested_zip_extraction(self, client):
        """Test extraction of nested zip files."""
        # Create nested zip structure
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create inner zip
            inner_zip_path = temp_path / "inner.zip"
            with zipfile.ZipFile(inner_zip_path, 'w') as inner_zip:
                inner_zip.writestr("data.csv", "col1,col2\nval1,val2")
                inner_zip.writestr("metadata.txt", "metadata content")
            
            # Create outer zip containing inner zip
            outer_zip_path = temp_path / "outer.zip"
            with zipfile.ZipFile(outer_zip_path, 'w') as outer_zip:
                outer_zip.write(inner_zip_path, "inner.zip")
                outer_zip.writestr("readme.txt", "readme content")
            
            # Test extraction
            extracted = await client._extract_nested_zip(
                outer_zip_path,
                temp_path / "extracted"
            )
            
            assert len(extracted) == 3  # data.csv, metadata.txt, readme.txt
            assert any(f.name == "data.csv" for f in extracted)
            assert all(f.exists() for f in extracted)
    
    @pytest.mark.asyncio
    async def test_resource_pattern_matching(self, client):
        """Test resource pattern matching."""
        resources = [
            {"name": "yemen-prices-2024.csv", "format": "CSV"},
            {"name": "yemen-prices-2024.xlsx", "format": "XLSX"},
            {"name": "documentation.pdf", "format": "PDF"},
            {"name": "yemen-prices.json", "format": "JSON"}
        ]
        
        # Test CSV pattern
        csv_matches = client._match_resources(resources, "csv")
        assert len(csv_matches) == 1
        assert csv_matches[0]["format"] == "CSV"
        
        # Test Excel pattern
        xlsx_matches = client._match_resources(resources, "xlsx")
        assert len(xlsx_matches) == 1
        assert xlsx_matches[0]["format"] == "XLSX"
        
        # Test regex pattern
        price_matches = client._match_resources(resources, r".*prices.*")
        assert len(price_matches) == 3  # CSV, XLSX, and JSON files
    
    @pytest.mark.asyncio
    async def test_cache_checking(self, client, mock_sources):
        """Test cache freshness checking."""
        source = mock_sources[0]
        
        # Create a recent cache file
        cache_file = client.download_dir / f"{source.name.lower().replace(' ', '_')}_latest.csv"
        cache_file.parent.mkdir(parents=True, exist_ok=True)
        cache_file.write_text("cached data")
        
        # Mock file modification time to be recent
        import os
        import time
        recent_time = time.time() - 3600  # 1 hour ago
        os.utime(cache_file, (recent_time, recent_time))
        
        # Check if download needed (should not be needed for weekly data)
        is_fresh = await client._is_cache_fresh(cache_file, source)
        assert is_fresh
        
        # Make cache old
        old_time = time.time() - (8 * 24 * 3600)  # 8 days ago
        os.utime(cache_file, (old_time, old_time))
        
        # Should need download now
        is_fresh = await client._is_cache_fresh(cache_file, source)
        assert not is_fresh
    
    @pytest.mark.asyncio
    async def test_error_handling(self, client, mock_sources):
        """Test error handling during downloads."""
        source = mock_sources[0]
        
        # Test 404 error
        mock_response = AsyncMock()
        mock_response.status_code = 404
        mock_response.raise_for_status.side_effect = httpx.HTTPStatusError(
            "Not found", request=Mock(), response=mock_response
        )
        
        with patch('httpx.AsyncClient.get', return_value=mock_response):
            result = await client.download_source(source)
        
        assert not result.success
        assert "404" in result.error or "Not found" in result.error
    
    @pytest.mark.asyncio
    async def test_progress_callback(self, client, mock_sources):
        """Test progress callback functionality."""
        progress_updates = []
        
        def progress_callback(source_name, progress_pct):
            progress_updates.append((source_name, progress_pct))
        
        # Mock successful downloads
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.content = b"test data"
        mock_response.headers = {"content-type": "text/csv"}
        
        with patch('httpx.AsyncClient.get', return_value=mock_response):
            results = await client.download_all(
                mock_sources,
                progress_callback=progress_callback
            )
        
        # Should have progress updates for each source
        assert len(progress_updates) >= len(mock_sources)
        
        # Progress should reach 100% for each source
        for source in mock_sources:
            source_updates = [
                p for name, p in progress_updates 
                if name == source.name
            ]
            assert any(p == 100 for p in source_updates)
    
    @pytest.mark.asyncio
    async def test_metadata_extraction(self, client):
        """Test metadata extraction from downloads."""
        # Create test file with metadata
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            metadata = {
                "dataset": "test-dataset",
                "last_updated": "2024-01-01",
                "rows": 1000,
                "columns": ["date", "value"]
            }
            json.dump(metadata, f)
            temp_file = Path(f.name)
        
        try:
            # Extract metadata
            extracted = await client._extract_metadata(temp_file)
            
            assert extracted["dataset"] == "test-dataset"
            assert extracted["rows"] == 1000
            assert "columns" in extracted
        finally:
            temp_file.unlink()
    
    @pytest.mark.asyncio
    async def test_download_summary(self, client, mock_sources):
        """Test download summary generation."""
        # Mock mixed results
        results = [
            DownloadResult(
                source=mock_sources[0],
                success=True,
                file_path=Path("file1.csv"),
                file_size=1024,
                download_time=1.5
            ),
            DownloadResult(
                source=mock_sources[1],
                success=False,
                error="Network error",
                download_time=0.5
            ),
            DownloadResult(
                source=mock_sources[2],
                success=True,
                file_path=Path("file3.xlsx"),
                file_size=2048,
                download_time=2.0
            )
        ]
        
        summary = client.generate_download_summary(results)
        
        assert summary["total_sources"] == 3
        assert summary["successful_downloads"] == 2
        assert summary["failed_downloads"] == 1
        assert summary["total_size_mb"] > 0
        assert summary["total_time_seconds"] == 4.0
        assert len(summary["failures"]) == 1
        assert mock_sources[1].name in summary["failures"][0]