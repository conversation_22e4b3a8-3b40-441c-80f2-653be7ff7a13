"""
Integration test suite for research methodology documentation.

This test suite validates:
1. Documentation consistency across all files
2. Changes made by agents 1-7
3. Forbidden phrases are not present
4. Proper hypothesis formats
5. Results are templates, not predetermined values
6. All cross-references are valid
"""

import os
import re
import json
from pathlib import Path
from typing import List, Dict, Set, Tuple
import pytest


class DocumentationIntegrationTests:
    """Comprehensive test suite for documentation integrity."""
    
    def __init__(self):
        self.base_path = Path(__file__).parent.parent / "docs" / "research-methodology-package"
        self.forbidden_phrases = [
            "significant market integration",
            "strong evidence",
            "clear impact",
            "proven effect",
            "definitive conclusion",
            "causal relationship established",
            "hypothesis confirmed",
            "results show",
            "we found that",
            "our analysis proves"
        ]
        self.required_templates = {
            "effect_size": r"\[EFFECT SIZE: .*?\]",
            "confidence_interval": r"\[CI: .*?\]",
            "p_value": r"\[p-value: .*?\]",
            "sample_size": r"\[N = .*?\]",
            "time_period": r"\[PERIOD: .*?\]",
            "robustness": r"\[ROBUSTNESS: .*?\]"
        }
        
    def get_all_markdown_files(self) -> List[Path]:
        """Get all markdown files in the documentation package."""
        return list(self.base_path.rglob("*.md"))
    
    def test_pre_analysis_plan_lock(self):
        """Test that pre-analysis plan is properly locked."""
        lock_file = self.base_path / ".pre-analysis-plan-lock"
        assert lock_file.exists(), "Pre-analysis plan lock file missing"
        
        with open(lock_file, 'r') as f:
            content = f.read()
        
        # Check for key components in the lock file
        assert "VERSION=" in content, "Lock file missing version"
        assert "LOCK_DATE=" in content, "Lock file missing lock date"
        assert "Files included in the lock:" in content, "Lock file missing file list"
        
        # Extract locked files from the content
        locked_files = []
        in_files_section = False
        for line in content.split('\n'):
            if "Files included in the lock:" in line:
                in_files_section = True
                continue
            if in_files_section and line.strip().startswith('# -'):
                file_path = line.strip()[3:].strip()
                locked_files.append(file_path)
            elif in_files_section and not line.strip().startswith('#'):
                break
        
        assert len(locked_files) > 0, "No locked files found in lock file"
        
        # Verify locked files exist
        for file_path in locked_files:
            full_path = self.base_path.parent.parent / file_path
            assert full_path.exists(), f"Locked file {file_path} does not exist"
    
    def test_no_forbidden_phrases(self):
        """Test that documentation doesn't contain forbidden phrases."""
        violations = []
        
        for file_path in self.get_all_markdown_files():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().lower()
                
            for phrase in self.forbidden_phrases:
                if phrase.lower() in content:
                    # Find line numbers for better reporting
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if phrase.lower() in line:
                            violations.append({
                                "file": str(file_path.relative_to(self.base_path)),
                                "line": i,
                                "phrase": phrase,
                                "context": line.strip()
                            })
        
        assert len(violations) == 0, \
            f"Found {len(violations)} forbidden phrases:\n" + \
            "\n".join([f"{v['file']}:{v['line']} - '{v['phrase']}' in: {v['context']}" 
                      for v in violations[:5]])  # Show first 5
    
    def test_hypothesis_format(self):
        """Test that all hypotheses follow proper format."""
        hypothesis_file = self.base_path / "01-theoretical-foundation" / "hypotheses" / "testable-hypotheses.md"
        assert hypothesis_file.exists(), "Testable hypotheses file missing"
        
        with open(hypothesis_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for all 10 hypotheses
        for i in range(1, 11):
            assert f"## H{i}:" in content, f"Hypothesis H{i} missing or improperly formatted"
        
        # Verify each hypothesis has required sections
        hypothesis_sections = re.split(r'## H\d+:', content)[1:]  # Skip content before first hypothesis
        
        for i, section in enumerate(hypothesis_sections, 1):
            # Check for statistical specification
            assert "**Statistical Specification:**" in section, \
                f"H{i} missing statistical specification"
            
            # Check for falsifiable predictions
            assert "**Falsifiable Predictions:**" in section or "**Falsifiable Prediction:**" in section, \
                f"H{i} missing falsifiable predictions"
            
            # Check for required data
            assert "**Required Data:**" in section, \
                f"H{i} missing required data section"
    
    def test_results_are_templates(self):
        """Test that results files contain templates, not predetermined values."""
        results_path = self.base_path / "07-results-templates"
        
        for file_path in results_path.rglob("*.md"):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for at least some template markers
            has_templates = any(re.search(pattern, content) 
                              for pattern in self.required_templates.values())
            
            # Special check for specific files that must have templates
            if "main-findings" in str(file_path) or "executive-summary" in str(file_path):
                assert has_templates, \
                    f"{file_path.relative_to(self.base_path)} missing template markers"
            
            # Check for hardcoded statistical values
            hardcoded_patterns = [
                r'p\s*[<=]\s*0\.0[0-9]+',  # p-values
                r'β\s*=\s*-?[0-9]+\.[0-9]+',  # coefficients
                r'R²\s*=\s*0\.[0-9]+',  # R-squared
                r'significant at [0-9]+%'  # significance claims
            ]
            
            for pattern in hardcoded_patterns:
                matches = re.findall(pattern, content)
                assert len(matches) == 0, \
                    f"{file_path.relative_to(self.base_path)} contains hardcoded values: {matches}"
    
    def test_cross_references_valid(self):
        """Test that all cross-references point to existing files."""
        invalid_refs = []
        
        for file_path in self.get_all_markdown_files():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find all markdown links
            links = re.findall(r'\[([^\]]+)\]\(([^)]+)\)', content)
            
            for link_text, link_target in links:
                # Skip external links and anchors
                if link_target.startswith(('http://', 'https://', '#')):
                    continue
                
                # Remove any anchors from internal links
                link_target = link_target.split('#')[0]
                
                # Resolve relative path
                if link_target:
                    target_path = (file_path.parent / link_target).resolve()
                    
                    # Check if file exists
                    if not target_path.exists():
                        invalid_refs.append({
                            "source": str(file_path.relative_to(self.base_path)),
                            "target": link_target,
                            "text": link_text
                        })
        
        assert len(invalid_refs) == 0, \
            f"Found {len(invalid_refs)} invalid cross-references:\n" + \
            "\n".join([f"{r['source']} -> {r['target']} ('{r['text']}')" 
                      for r in invalid_refs[:10]])  # Show first 10
    
    def test_methodology_consistency(self):
        """Test consistency of methodology descriptions across files."""
        methodology_terms = {
            "three-tier": "Three-tier",
            "VECM": "Vector Error Correction Model",
            "regime-switching": "regime-switching",
            "structural breaks": "structural break",
            "currency fragmentation": "currency fragmentation"
        }
        
        term_usage = {term: [] for term in methodology_terms}
        
        for file_path in self.get_all_markdown_files():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for term, canonical in methodology_terms.items():
                if term.lower() in content.lower():
                    # Check if canonical form is used
                    if canonical not in content and canonical.lower() not in content.lower():
                        term_usage[term].append(str(file_path.relative_to(self.base_path)))
        
        # Report inconsistencies
        inconsistencies = []
        for term, files in term_usage.items():
            if files:
                inconsistencies.append(f"{term}: used in {len(files)} files without canonical form")
        
        # Allow some variation but flag if too many
        assert len(inconsistencies) < 5, \
            f"Methodology term inconsistencies found:\n" + "\n".join(inconsistencies)
    
    def test_required_sections_present(self):
        """Test that key documents have required sections."""
        required_sections = {
            "00-overview/PRE_ANALYSIS_PLAN.md": [
                "# Pre-Analysis Plan",
                "## Research Questions",
                "## Hypotheses",
                "## Methodology",
                "## Data Requirements"
            ],
            "00-overview/METHODOLOGICAL_TRANSPARENCY.md": [
                "# Methodological Transparency",
                "## Initial Errors",
                "## Corrections Made",
                "## Lessons Learned"
            ],
            "07-results-templates/NULL_RESULTS_TEMPLATE.md": [
                "# Reporting Null Results",
                "## Statistical Power",
                "## Alternative Explanations"
            ]
        }
        
        for file_path, sections in required_sections.items():
            full_path = self.base_path / file_path
            assert full_path.exists(), f"Required file {file_path} missing"
            
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for section in sections:
                assert section in content, \
                    f"{file_path} missing required section: {section}"
    
    def test_statistical_testing_plan(self):
        """Test that statistical testing plan is properly documented."""
        testing_plan = self.base_path / "01-theoretical-foundation" / "hypotheses" / "statistical-testing-plan.md"
        assert testing_plan.exists(), "Statistical testing plan missing"
        
        with open(testing_plan, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for key components
        required_components = [
            "Multiple Testing Correction",
            "Power Analysis",
            "Robustness Checks",
            "Sample Size Requirements",
            "Effect Size Calculations"
        ]
        
        for component in required_components:
            assert component in content, f"Statistical testing plan missing: {component}"
    
    def test_alternative_explanations(self):
        """Test that alternative explanations are properly documented."""
        alt_dir = self.base_path / "03-econometric-methodology" / "alternative-explanations"
        assert alt_dir.exists(), "Alternative explanations directory missing"
        
        # Check for key alternative explanation files
        expected_files = [
            "selection-bias.md",
            "measurement-error.md",
            "omitted-variables.md",
            "reverse-causality.md"
        ]
        
        for file_name in expected_files:
            file_path = alt_dir / file_name
            assert file_path.exists(), f"Alternative explanation file missing: {file_name}"
            
            # Verify content structure
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            assert "## Description" in content
            assert "## Testing Approach" in content
            assert "## Mitigation Strategy" in content


class TestMethodologicalNarrative:
    """Test that research is properly framed as methodological correction."""
    
    def __init__(self):
        self.base_path = Path(__file__).parent.parent / "docs" / "research-methodology-package"
        
        # Phrases that indicate proper framing as methodological correction
        self.correction_indicators = [
            "methodological correction",
            "initial error",
            "measurement error", 
            "currency conversion error",
            "learned from",
            "lesson learned",
            "correction made",
            "revised approach",
            "methodological improvement",
            "analytical error"
        ]
        
        # Phrases that wrongly frame as discovery
        self.discovery_phrases = [
            "we discovered",
            "groundbreaking finding",
            "novel insight",
            "unprecedented result",
            "first to show",
            "unique contribution",
            "breakthrough in understanding"
        ]
        
        # Files that should explicitly acknowledge the error
        self.evolution_files = [
            "01-theoretical-foundation/RESEARCH_QUESTION_EVOLUTION.md",
            "00-overview/METHODOLOGICAL_TRANSPARENCY.md",
            "07-results-templates/main-findings/exchange-rate-impact.md",
            "08-publication-materials/paper-templates/executive-summary.md"
        ]
    
    def test_evolution_acknowledges_error(self):
        """Test that evolution files acknowledge initial error."""
        missing_acknowledgments = []
        
        for file_path in self.evolution_files:
            full_path = self.base_path / file_path
            if not full_path.exists():
                missing_acknowledgments.append(f"{file_path} - file missing")
                continue
                
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read().lower()
            
            # Check for at least one correction indicator
            has_correction = any(indicator in content for indicator in self.correction_indicators)
            
            if not has_correction:
                missing_acknowledgments.append(f"{file_path} - no error acknowledgment")
        
        assert len(missing_acknowledgments) == 0, \
            f"Files missing error acknowledgment:\n" + "\n".join(missing_acknowledgments)
    
    def test_no_discovery_framing(self):
        """Test that documentation doesn't frame as discovery."""
        violations = []
        
        for file_path in Path(self.base_path).rglob("*.md"):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            for i, line in enumerate(lines, 1):
                line_lower = line.lower()
                for phrase in self.discovery_phrases:
                    if phrase in line_lower:
                        violations.append({
                            "file": str(file_path.relative_to(self.base_path)),
                            "line": i,
                            "phrase": phrase,
                            "context": line.strip()
                        })
        
        assert len(violations) == 0, \
            f"Found {len(violations)} discovery framings:\n" + \
            "\n".join([f"{v['file']}:{v['line']} - '{v['phrase']}' in: {v['context']}" 
                      for v in violations[:10]])
    
    def test_currency_error_mentioned(self):
        """Test that key files mention currency conversion error."""
        key_files = [
            "01-theoretical-foundation/RESEARCH_QUESTION_EVOLUTION.md",
            "00-overview/METHODOLOGICAL_TRANSPARENCY.md"
        ]
        
        missing_currency_mention = []
        
        for file_path in key_files:
            full_path = self.base_path / file_path
            if not full_path.exists():
                continue
                
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read().lower()
            
            currency_terms = ["currency conversion", "exchange rate error", "yer/usd", "currency error"]
            has_currency_mention = any(term in content for term in currency_terms)
            
            if not has_currency_mention:
                missing_currency_mention.append(file_path)
        
        assert len(missing_currency_mention) == 0, \
            f"Key files not mentioning currency error:\n" + "\n".join(missing_currency_mention)
    
    def test_learning_narrative(self):
        """Test that methodology files frame as learning experience."""
        methodology_files = list((self.base_path / "03-econometric-methodology").rglob("*.md"))
        
        files_without_learning = []
        
        for file_path in methodology_files:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for learning/improvement language
            learning_terms = [
                "lesson", "learned", "improvement", "enhanced", "refined",
                "corrected", "updated", "revised", "better approach"
            ]
            
            has_learning = any(term in content.lower() for term in learning_terms)
            
            # Files about core methods should have learning narrative
            if "identification" in str(file_path) or "validation" in str(file_path):
                if not has_learning:
                    files_without_learning.append(str(file_path.relative_to(self.base_path)))
        
        # Allow some files to not have learning narrative, but flag if too many
        assert len(files_without_learning) < 3, \
            f"Too many methodology files without learning narrative:\n" + \
            "\n".join(files_without_learning)
    
    def test_proper_research_framing(self):
        """Test overall research framing as methodological contribution."""
        # Check main README and overview files
        overview_files = [
            self.base_path / "README.md",
            self.base_path / "00-overview" / "README.md",
            self.base_path / "00-overview" / "METHODOLOGY_INDEX.md"
        ]
        
        improper_framing = []
        
        for file_path in overview_files:
            if not file_path.exists():
                continue
                
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for proper methodological framing
            methodological_terms = [
                "methodological", "econometric approach", "analytical framework",
                "measurement", "identification strategy", "estimation"
            ]
            
            has_method_focus = any(term in content.lower() for term in methodological_terms)
            
            if not has_method_focus:
                improper_framing.append(str(file_path.relative_to(self.base_path)))
        
        assert len(improper_framing) == 0, \
            f"Overview files lacking methodological framing:\n" + "\n".join(improper_framing)


# Pytest fixtures and test functions
@pytest.fixture
def test_suite():
    """Create test suite instance."""
    return DocumentationIntegrationTests()


def test_pre_analysis_lock(test_suite):
    """Test pre-analysis plan lock."""
    test_suite.test_pre_analysis_plan_lock()


def test_forbidden_phrases(test_suite):
    """Test absence of forbidden phrases."""
    test_suite.test_no_forbidden_phrases()


def test_hypothesis_format(test_suite):
    """Test hypothesis formatting."""
    test_suite.test_hypothesis_format()


def test_results_templates(test_suite):
    """Test results are templates."""
    test_suite.test_results_are_templates()


def test_cross_references(test_suite):
    """Test cross-reference validity."""
    test_suite.test_cross_references_valid()


def test_methodology_consistency(test_suite):
    """Test methodology consistency."""
    test_suite.test_methodology_consistency()


def test_required_sections(test_suite):
    """Test required sections present."""
    test_suite.test_required_sections_present()


def test_statistical_plan(test_suite):
    """Test statistical testing plan."""
    test_suite.test_statistical_testing_plan()


def test_alternatives(test_suite):
    """Test alternative explanations."""
    test_suite.test_alternative_explanations()


# New test fixture for methodological narrative tests
@pytest.fixture
def narrative_test_suite():
    """Create methodological narrative test suite instance."""
    return TestMethodologicalNarrative()


def test_evolution_acknowledges_error(narrative_test_suite):
    """Test that evolution acknowledges error."""
    narrative_test_suite.test_evolution_acknowledges_error()


def test_no_discovery_framing(narrative_test_suite):
    """Test no discovery framing."""
    narrative_test_suite.test_no_discovery_framing()


def test_currency_error_mentioned(narrative_test_suite):
    """Test currency error mentioned."""
    narrative_test_suite.test_currency_error_mentioned()


def test_learning_narrative(narrative_test_suite):
    """Test learning narrative."""
    narrative_test_suite.test_learning_narrative()


def test_proper_research_framing(narrative_test_suite):
    """Test proper research framing."""
    narrative_test_suite.test_proper_research_framing()


if __name__ == "__main__":
    # Run all tests if executed directly
    suite = DocumentationIntegrationTests()
    narrative_suite = TestMethodologicalNarrative()
    
    print("Running documentation integration tests...\n")
    
    tests = [
        # Original documentation tests
        ("Pre-analysis plan lock", suite.test_pre_analysis_plan_lock),
        ("Forbidden phrases", suite.test_no_forbidden_phrases),
        ("Hypothesis format", suite.test_hypothesis_format),
        ("Results templates", suite.test_results_are_templates),
        ("Cross-references", suite.test_cross_references_valid),
        ("Methodology consistency", suite.test_methodology_consistency),
        ("Required sections", suite.test_required_sections_present),
        ("Statistical testing plan", suite.test_statistical_testing_plan),
        ("Alternative explanations", suite.test_alternative_explanations),
        # New methodological narrative tests
        ("Evolution acknowledges error", narrative_suite.test_evolution_acknowledges_error),
        ("No discovery framing", narrative_suite.test_no_discovery_framing),
        ("Currency error mentioned", narrative_suite.test_currency_error_mentioned),
        ("Learning narrative", narrative_suite.test_learning_narrative),
        ("Proper research framing", narrative_suite.test_proper_research_framing)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            test_func()
            print(f"✓ {test_name}")
            passed += 1
        except AssertionError as e:
            print(f"✗ {test_name}")
            print(f"  {str(e)}\n")
            failed += 1
        except Exception as e:
            print(f"✗ {test_name} (ERROR)")
            print(f"  {type(e).__name__}: {str(e)}\n")
            failed += 1
    
    print(f"\nSummary: {passed} passed, {failed} failed")
    
    if failed > 0:
        exit(1)