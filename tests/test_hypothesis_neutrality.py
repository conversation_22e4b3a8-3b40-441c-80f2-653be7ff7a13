"""
Tests to ensure hypothesis testing code maintains neutrality and avoids bias.
"""

import pytest
import inspect
import ast
import re
import pandas as pd
from pathlib import Path
from typing import List, Tuple

from src.core.models.hypothesis_testing import (
    HypothesisRegistry,
    HypothesisTest,
    HypothesisOutcome
)
from src.core.models.hypothesis_testing.bias_detection import BiasDetector
from src.core.models.hypothesis_testing.specification_curve import SpecificationCurve


class TestHypothesisNeutrality:
    """Test suite to ensure hypothesis tests are neutral and unbiased."""
    
    def test_no_predetermined_outcomes(self):
        """Ensure hypothesis tests don't have predetermined expected outcomes."""
        # Get all hypothesis test files
        hypothesis_dir = Path("src/core/models/hypothesis_testing")
        test_files = list(hypothesis_dir.glob("h*.py"))
        test_files.extend(hypothesis_dir.glob("s*.py"))
        test_files.extend(hypothesis_dir.glob("n*.py"))
        test_files.extend(hypothesis_dir.glob("p*.py"))
        
        biased_terms = [
            "expected", "should find", "will show", "discovery",
            "reveals", "proves", "truth", "paradox", "fully explains",
            "as predicted", "confirms", "fundamental finding"
        ]
        
        violations = []
        
        for file_path in test_files:
            with open(file_path, 'r') as f:
                content = f.read()
                
            # Check for biased terms in comments and docstrings
            for term in biased_terms:
                if term.lower() in content.lower():
                    # Find line numbers
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if term.lower() in line.lower():
                            violations.append((file_path.name, i+1, term, line.strip()))
                            
        # Report violations
        if violations:
            report = "Found biased language in hypothesis tests:\n"
            for file, line_num, term, line in violations:
                report += f"  {file}:{line_num} - '{term}' in: {line[:60]}...\n"
            pytest.fail(report)
            
    def test_neutral_outcome_enum(self):
        """Test that HypothesisOutcome enum uses neutral language."""
        # Check enum values
        outcome_values = [o.value for o in HypothesisOutcome]
        
        # These terms imply desired outcomes
        biased_outcomes = ["supported", "confirmed", "proven", "rejected", "failed"]
        
        for outcome in outcome_values:
            assert not any(term in outcome.lower() for term in biased_outcomes), \
                f"Outcome '{outcome}' uses biased language"
                
        # Check for required neutral outcomes
        assert any("null" in o.value for o in HypothesisOutcome), \
            "Missing null hypothesis reference in outcomes"
            
    def test_specification_robustness_implemented(self):
        """Ensure all hypothesis tests implement specification robustness."""
        # Get all registered hypothesis tests
        all_tests = HypothesisRegistry._tests
        
        for test_id, test_instance in all_tests.items():
            # Check if test has specification curve analysis
            run_test_method = getattr(test_instance, 'run_test', None)
            
            if run_test_method:
                # Inspect source code
                source = inspect.getsource(run_test_method)
                
                # Should include specification analysis
                assert any(term in source for term in [
                    'specification_curve', 'spec_curve', '_run_specification_curve'
                ]), f"Test {test_id} missing specification curve analysis"
                
    def test_power_analysis_included(self):
        """Ensure all tests include statistical power analysis."""
        all_tests = HypothesisRegistry._tests
        
        for test_id, test_instance in all_tests.items():
            # Check for power calculation method
            assert hasattr(test_instance, '_calculate_statistical_power'), \
                f"Test {test_id} missing power analysis method"
                
            # Check that run_test calls power analysis
            run_test_source = inspect.getsource(test_instance.run_test)
            assert '_calculate_statistical_power' in run_test_source or \
                   'statistical_power' in run_test_source, \
                f"Test {test_id} doesn't calculate power in run_test"
                
    def test_multiple_testing_correction(self):
        """Test that multiple testing correction is applied."""
        # Check HypothesisRegistry.run_all
        run_all_source = inspect.getsource(HypothesisRegistry.run_all)
        
        # Should import and use multiple testing correction
        assert 'multipletests' in run_all_source, \
            "Missing multiple testing correction import"
        assert 'fdr_bh' in run_all_source or 'bonferroni' in run_all_source, \
            "No multiple testing correction method specified"
        assert 'adjusted_p_value' in run_all_source, \
            "Not storing adjusted p-values"
            
    def test_no_cherry_picking_in_interpret_results(self):
        """Ensure interpret_results methods don't cherry-pick findings."""
        all_tests = HypothesisRegistry._tests
        
        for test_id, test_instance in all_tests.items():
            interpret_source = inspect.getsource(test_instance.interpret_results)
            
            # Check for conditional interpretation based on outcome
            assert 'if results.outcome ==' in interpret_source, \
                f"Test {test_id} missing outcome-based interpretation"
                
            # Check that all outcomes are handled
            assert 'HypothesisOutcome.NULL_REJECTED' in interpret_source or \
                   'NULL_REJECTED' in interpret_source, \
                f"Test {test_id} doesn't handle NULL_REJECTED outcome"
            assert 'HypothesisOutcome.FAIL_TO_REJECT_NULL' in interpret_source or \
                   'FAIL_TO_REJECT_NULL' in interpret_source, \
                f"Test {test_id} doesn't handle FAIL_TO_REJECT_NULL outcome"
                
    def test_effect_size_reporting(self):
        """Ensure all tests report effect sizes, not just p-values."""
        all_tests = HypothesisRegistry._tests
        
        for test_id, test_instance in all_tests.items():
            run_test_source = inspect.getsource(test_instance.run_test)
            
            # Should calculate and return effect size
            assert 'effect_size' in run_test_source, \
                f"Test {test_id} doesn't calculate effect size"
                
            # Should also calculate confidence intervals
            assert 'confidence_interval' in run_test_source or \
                   '_calculate_confidence_interval' in run_test_source, \
                f"Test {test_id} doesn't provide confidence intervals"
                
    def test_bias_detector_integration(self):
        """Test that bias detection tools can be used with hypothesis tests."""
        detector = BiasDetector()
        
        # Test logging functionality
        detector.log_analysis(
            hypothesis_id="H1",
            specification={"fixed_effects": "both"},
            p_value=0.048,
            effect_size=0.15,
            reported=True
        )
        
        # Multiple attempts with improving p-values (suspicious)
        for i, p in enumerate([0.12, 0.08, 0.055, 0.048]):
            detector.log_analysis(
                hypothesis_id="H1",
                specification={"fixed_effects": "both", "attempt": i},
                p_value=p,
                effect_size=0.15,
                reported=(p < 0.05)  # Only report significant
            )
            
        # Check p-hacking detection
        p_hack_check = detector.check_p_hacking("H1")
        assert p_hack_check['p_hacking_risk'], \
            "Should detect p-hacking risk from selective reporting"
            
        # Check specification searching
        spec_check = detector.check_specification_searching("H1")
        assert spec_check['specification_searching'], \
            "Should detect specification searching behavior"
            
    def test_pre_analysis_plan_enforcement(self):
        """Test pre-analysis plan registration and enforcement."""
        detector = BiasDetector()
        
        # Register plan
        plan = {
            'hypotheses': ['H1', 'H2', 'H3'],
            'primary_specifications': [
                {'fixed_effects': 'both', 'clustering': 'entity'},
                {'fixed_effects': 'time', 'clustering': 'none'}
            ],
            'decision_rules': {'alpha': 0.05}
        }
        
        registered_plan = detector.register_pre_analysis_plan(plan)
        assert registered_plan.hash is not None
        
        # Test compliance
        compliant = detector.enforce_pre_analysis_plan(
            'H1', 
            {'fixed_effects': 'both', 'clustering': 'entity'}
        )
        assert compliant['follows_plan']
        
        # Test non-compliance
        non_compliant = detector.enforce_pre_analysis_plan(
            'H4',  # Not registered
            {'fixed_effects': 'both', 'clustering': 'entity'}
        )
        assert not non_compliant['follows_plan']
        assert 'not in pre-analysis plan' in non_compliant['reason']
        
    def test_specification_curve_functionality(self):
        """Test specification curve analysis works correctly."""
        from src.core.models.hypothesis_testing.specification_curve import (
            SpecificationCurve, create_standard_specifications
        )
        
        # Create curve with standard specifications
        curve = create_standard_specifications()
        
        # Check specification generation
        specs = curve.generate_all_specifications()
        
        # Should have multiple specifications
        assert len(specs) > 10, "Too few specifications generated"
        
        # Each spec should have all dimensions
        expected_keys = {'fixed_effects', 'clustering', 'outlier_handling', 
                        'balanced_panel', 'time_trends'}
        
        for spec in specs:
            assert set(spec.keys()) == expected_keys, \
                f"Specification missing keys: {spec}"
                
    def test_no_default_outcomes(self):
        """Ensure TestResults doesn't default to any particular outcome."""
        from src.core.models.hypothesis_testing.hypothesis_framework import TestResults
        
        # Check dataclass definition doesn't have defaults for outcome
        import dataclasses
        fields = dataclasses.fields(TestResults)
        
        outcome_field = next(f for f in fields if f.name == 'outcome')
        assert outcome_field.default == dataclasses.MISSING, \
            "TestResults.outcome should not have a default value"
            
    def test_neutral_logging_messages(self):
        """Ensure logging messages are neutral."""
        all_tests = HypothesisRegistry._tests
        
        for test_id, test_instance in all_tests.items():
            source = inspect.getsource(test_instance.__class__)
            
            # Find logger calls
            logger_calls = re.findall(r'logger\.\w+\([^)]+\)', source)
            
            biased_phrases = [
                "as expected", "confirms", "proves", "should be",
                "discovery", "reveals truth", "paradox explained"
            ]
            
            for call in logger_calls:
                for phrase in biased_phrases:
                    assert phrase not in call.lower(), \
                        f"Biased logging in {test_id}: {call}"


class TestCodeStructurePreventsGaming:
    """Test that code structure makes it hard to game results."""
    
    def test_no_result_manipulation_possible(self):
        """Test that results can't be easily manipulated."""
        from src.core.models.hypothesis_testing.hypothesis_framework import TestResults
        
        # Try to create results with inconsistent values
        with pytest.raises(ValueError) as exc_info:
            # This should fail - can't have significant p-value but claim null not rejected
            results = TestResults(
                hypothesis_id="H1",
                outcome=HypothesisOutcome.FAIL_TO_REJECT_NULL,
                test_statistic=5.0,
                p_value=0.001,  # Very significant
                alpha=0.05
            )
        assert "Inconsistent results" in str(exc_info.value)
        
    def test_no_synthetic_data_generation(self):
        """Test that hypothesis tests don't generate fake data."""
        from src.core.models.hypothesis_testing.h1_exchange_rate import H1ExchangeRateMechanism
        
        h1_test = H1ExchangeRateMechanism()
        
        # Should raise error when no exchange rate data provided
        with pytest.raises(ValueError) as exc_info:
            empty_df = pd.DataFrame()
            h1_test._prepare_exchange_rates(empty_df)
        assert "required for H1 hypothesis test" in str(exc_info.value)
        
    def test_power_validation(self):
        """Test that low power triggers appropriate outcome."""
        from src.core.models.hypothesis_testing.hypothesis_framework import TestResults
        
        # Should validate power-based outcomes
        with pytest.raises(ValueError) as exc_info:
            results = TestResults(
                hypothesis_id="H1",
                outcome=HypothesisOutcome.INSUFFICIENT_POWER,
                test_statistic=1.0,
                p_value=0.10,
                alpha=0.05,
                statistical_power=0.85  # High power but claiming insufficient
            )
        assert "INSUFFICIENT_POWER but power is not low" in str(exc_info.value)
                
    def test_specification_choices_are_reasonable(self):
        """Test that only reasonable specification choices are allowed."""
        from src.core.models.hypothesis_testing.specification_curve import (
            create_standard_specifications
        )
        
        curve = create_standard_specifications()
        
        # Check that extreme specifications aren't included
        for choice in curve.specification_choices:
            if choice.name == 'outlier_handling':
                # Shouldn't allow extreme trimming
                assert not any(
                    'trim_10pct' in str(opt) or 'trim_25pct' in str(opt) 
                    for opt in choice.options
                ), "Extreme outlier handling allowed"
                
            if choice.name == 'fixed_effects':
                # Should have standard options
                assert set(choice.options) <= {'none', 'entity', 'time', 'both'}, \
                    "Non-standard fixed effects options"