"""Unit tests for specification curve analysis."""

import numpy as np
import pandas as pd
import pytest
from datetime import datetime, timedelta

from src.core.analysis.robustness.specification_curve import (
    SpecificationCurve,
    SpecificationGenerator,
    SpecificationResult,
    SpecificationCurveVisualizer,
)
from src.core.domain.models.currency_zone import CurrencyZone


class TestSpecificationGenerator:
    """Test specification generation."""
    
    def test_generate_basic_specifications(self):
        """Test basic specification generation."""
        base_spec = {"hypothesis_tests": ["H1", "H2"]}
        generator = SpecificationGenerator(base_spec)
        
        # Should generate reasonable number of specs with defaults
        specs = generator.generate_specifications()
        assert len(specs) > 100  # With defaults, should generate many specs
        assert all("specification_id" in spec for spec in specs)
        assert all("dependent_variable" in spec for spec in specs)
        
    def test_generate_limited_specifications(self):
        """Test limiting number of specifications."""
        base_spec = {"hypothesis_tests": ["H1"]}
        generator = SpecificationGenerator(base_spec)
        
        # Limit to 50 specifications
        specs = generator.generate_specifications(max_specs=50)
        assert len(specs) == 50
        
        # Check even distribution
        dep_vars = [s["dependent_variable"] for s in specs]
        assert "price_usd" in dep_vars
        assert "log_price_usd" in dep_vars
        
    def test_custom_specification_choices(self):
        """Test custom specification choices."""
        base_spec = {"hypothesis_tests": ["H1"]}
        generator = SpecificationGenerator(
            base_spec,
            dependent_vars=["price_usd"],
            fixed_effects=["entity"],
            clustering=["market"],
            control_sets=[["conflict_intensity"]],
            sample_periods=[("2020-01-01", "2020-12-31")],
            functional_forms=["linear"],
            estimation_methods=["fe"]
        )
        
        specs = generator.generate_specifications()
        assert len(specs) == 1  # Only one combination
        
        spec = specs[0]
        assert spec["dependent_variable"] == "price_usd"
        assert spec["fixed_effects"] == "entity"
        assert spec["clustering"] == "market"
        assert spec["controls"] == ["conflict_intensity"]
        assert spec["functional_form"] == "linear"
        assert spec["estimation_method"] == "fe"


class TestSpecificationCurve:
    """Test specification curve analysis."""
    
    @pytest.fixture
    def sample_panel_data(self):
        """Create sample panel data for testing."""
        np.random.seed(42)
        
        # Create balanced panel
        markets = [f"market_{i}" for i in range(50)]
        dates = pd.date_range("2020-01-01", "2020-12-31", freq="MS")
        
        data = []
        for market in markets:
            for date in dates:
                # Assign to currency zone based on market ID
                zone = CurrencyZone.HOUTHI if int(market.split("_")[1]) < 25 else CurrencyZone.GOVERNMENT
                exchange_rate = 535 if zone == CurrencyZone.HOUTHI else 2000
                
                # Generate price with exchange rate effect
                price_yer = np.random.normal(1000, 100)
                price_usd = price_yer / exchange_rate
                
                data.append({
                    "market_id": market,
                    "date": date,
                    "commodity": "wheat",
                    "price_yer": price_yer,
                    "price_usd": price_usd,
                    "log_price_usd": np.log(price_usd),
                    "currency_zone": zone.value,
                    "exchange_rate_used": exchange_rate,
                    "conflict_intensity": np.random.uniform(0, 10),
                    "aid_distribution": np.random.uniform(0, 5),
                    "population_density": np.random.uniform(10, 100),
                    "distance_to_border": np.random.uniform(0, 200)
                })
        
        return pd.DataFrame(data)
    
    def test_specification_curve_initialization(self):
        """Test specification curve initialization."""
        base_spec = {"hypothesis_tests": ["H1"]}
        curve = SpecificationCurve(base_spec)
        
        assert curve.base_specification == base_spec
        assert curve.specifications == []
        assert curve.results == []
        
    def test_add_specifications(self):
        """Test adding specifications."""
        base_spec = {"hypothesis_tests": ["H1"]}
        curve = SpecificationCurve(base_spec)
        
        specs = [
            {"specification_id": "spec_001", "dependent_variable": "price_usd"},
            {"specification_id": "spec_002", "dependent_variable": "log_price_usd"}
        ]
        
        curve.add_specifications(specs)
        assert len(curve.specifications) == 2
        
    def test_run_single_specification(self, sample_panel_data):
        """Test running a single specification."""
        base_spec = {"hypothesis_tests": ["H1"]}
        curve = SpecificationCurve(base_spec)
        
        spec = {
            "specification_id": "test_001",
            "dependent_variable": "price_usd",
            "fixed_effects": "entity",
            "clustering": "market",
            "controls": ["conflict_intensity"],
            "sample_start": "2020-01-01",
            "sample_end": "2020-12-31",
            "functional_form": "linear",
            "estimation_method": "ols"
        }
        
        result = curve._run_single_specification(spec, sample_panel_data)
        
        assert isinstance(result, SpecificationResult)
        assert result.specification_id == "test_001"
        assert result.methodology_valid is True
        assert result.n_observations == len(sample_panel_data)
        
    def test_methodology_validation_failure(self, sample_panel_data):
        """Test specification fails when methodology validation fails."""
        # Remove required fields to trigger validation failure
        bad_data = sample_panel_data.drop(columns=["price_usd"])
        
        base_spec = {"hypothesis_tests": ["H1"]}
        curve = SpecificationCurve(base_spec)
        
        spec = {
            "specification_id": "test_bad",
            "dependent_variable": "price_usd",
            "fixed_effects": "entity",
            "clustering": "market",
            "controls": [],
            "sample_start": "2020-01-01",
            "sample_end": "2020-12-31",
            "functional_form": "linear",
            "estimation_method": "ols"
        }
        
        result = curve._run_single_specification(spec, bad_data)
        
        assert result.methodology_valid is False
        assert result.error == "Methodology validation failed"
        assert np.isnan(result.coefficient)
        
    def test_analyze_stability(self):
        """Test stability analysis of results."""
        base_spec = {"hypothesis_tests": ["H1"]}
        curve = SpecificationCurve(base_spec)
        
        # Create mock results
        curve.results = [
            SpecificationResult(
                specification_id=f"spec_{i}",
                specification={},
                coefficient=0.8 + np.random.normal(0, 0.1),
                std_error=0.05,
                p_value=0.01,
                ci_lower=0.7,
                ci_upper=0.9,
                n_observations=1000,
                r_squared=0.65,
                methodology_valid=True
            )
            for i in range(100)
        ]
        
        # Add some invalid results
        curve.results.extend([
            SpecificationResult(
                specification_id=f"spec_invalid_{i}",
                specification={},
                coefficient=np.nan,
                std_error=np.nan,
                p_value=np.nan,
                ci_lower=np.nan,
                ci_upper=np.nan,
                n_observations=0,
                r_squared=np.nan,
                methodology_valid=False,
                error="Test error"
            )
            for i in range(10)
        ])
        
        stability = curve.analyze_stability()
        
        assert stability["n_valid"] == 100
        assert stability["n_total"] == 110
        assert stability["pct_valid"] == pytest.approx(90.91, rel=0.01)
        assert stability["coefficient_mean"] == pytest.approx(0.8, abs=0.1)
        assert stability["coefficient_cv"] < 0.2  # Low variation
        assert stability["pct_positive"] == 100  # All positive
        assert stability["pct_significant"] == 100  # All significant (p=0.01)


class TestSpecificationCurveVisualizer:
    """Test specification curve visualization."""
    
    @pytest.fixture
    def mock_results(self):
        """Create mock results for visualization."""
        np.random.seed(42)
        results = []
        
        for i in range(100):
            # Create variation in specifications
            dep_var = "log_price_usd" if i % 2 == 0 else "price_usd"
            fe = ["entity", "time", "twoway"][i % 3]
            method = ["ols", "fe", "ife", "bayesian"][i % 4]
            
            # Generate coefficient with some structure
            base_coef = 0.8
            if method == "bayesian":
                base_coef += 0.05
            if fe == "twoway":
                base_coef -= 0.02
            
            coefficient = base_coef + np.random.normal(0, 0.05)
            
            results.append(
                SpecificationResult(
                    specification_id=f"spec_{i:03d}",
                    specification={
                        "dependent_variable": dep_var,
                        "fixed_effects": fe,
                        "estimation_method": method,
                        "controls": [] if i % 3 == 0 else ["conflict_intensity"],
                        "sample_start": "2020-01-01" if i < 50 else "2021-01-01"
                    },
                    coefficient=coefficient,
                    std_error=0.05,
                    p_value=0.001 if abs(coefficient) > 0.1 else 0.1,
                    ci_lower=coefficient - 0.1,
                    ci_upper=coefficient + 0.1,
                    n_observations=1000 + i * 10,
                    r_squared=0.6 + np.random.uniform(-0.1, 0.1),
                    methodology_valid=True
                )
            )
        
        # Add some invalid results
        for i in range(10):
            results.append(
                SpecificationResult(
                    specification_id=f"spec_invalid_{i}",
                    specification={},
                    coefficient=np.nan,
                    std_error=np.nan,
                    p_value=np.nan,
                    ci_lower=np.nan,
                    ci_upper=np.nan,
                    n_observations=0,
                    r_squared=np.nan,
                    methodology_valid=False
                )
            )
        
        return results
    
    def test_visualizer_initialization(self, mock_results):
        """Test visualizer initialization."""
        viz = SpecificationCurveVisualizer(mock_results)
        
        assert len(viz.results) == 110
        assert len(viz.valid_results) == 100
        
    def test_create_robustness_table(self, mock_results):
        """Test robustness table creation."""
        viz = SpecificationCurveVisualizer(mock_results)
        table = viz.create_robustness_table()
        
        assert not table.empty
        assert "Specification Choice" in table.columns
        assert "Mean Coefficient" in table.columns
        assert "% Significant" in table.columns
        
        # Check that all specification types are represented
        spec_types = table["Specification Choice"].unique()
        assert "Dependent Variable" in spec_types
        assert "Fixed Effects" in spec_types
        assert "Estimation Method" in spec_types
        
    def test_plot_methods_dont_crash(self, mock_results, tmp_path):
        """Test that plot methods run without errors."""
        viz = SpecificationCurveVisualizer(mock_results)
        
        # Test specification curve plot
        fig1 = viz.plot_specification_curve(
            highlight_main="spec_050",
            save_path=tmp_path / "spec_curve.png"
        )
        assert fig1 is not None
        
        # Test coefficient distribution plot
        fig2 = viz.plot_coefficient_distribution(
            save_path=tmp_path / "coef_dist.png"
        )
        assert fig2 is not None
        
        # Close figures to avoid memory issues
        import matplotlib.pyplot as plt
        plt.close('all')