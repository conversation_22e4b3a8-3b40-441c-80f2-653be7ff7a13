"""
Tests for Currency-Aware Market Clustering

These tests ensure that:
1. Clustering respects currency zone boundaries
2. Methodology validation is enforced
3. Results are statistically meaningful
4. Integration with the analysis pipeline works correctly
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

from src.core.models.machine_learning.currency_aware_clustering import (
    CurrencyAwareMarketClustering,
    CurrencyAwareClusteringResults,
    apply_currency_aware_clustering
)
from src.core.domain.market.currency_zones import CurrencyZone
from src.core.validation.methodology_validator import MethodologyViolation


class TestCurrencyAwareMarketClustering:
    """Test suite for currency-aware market clustering."""
    
    @pytest.fixture
    def sample_panel_data(self):
        """Create sample panel data with multiple currency zones."""
        np.random.seed(42)
        
        # Create data for 3 zones with different characteristics
        data_list = []
        
        # HOUTHI zone markets (North, ~535 YER/USD)
        for i in range(20):
            market_id = f"north_{i}"
            dates = pd.date_range('2023-01-01', periods=60, freq='W')
            
            for date in dates:
                data_list.append({
                    'market_id': market_id,
                    'date': date,
                    'commodity': 'wheat',
                    'price_yer': np.random.normal(5000, 500),  # YER prices
                    'price_usd': np.random.normal(9.3, 0.9),   # ~5000/535
                    'exchange_rate_used': np.random.normal(535, 20),
                    'currency_zone': 'HOUTHI',
                    'governorate': 'Sanaa'
                })
        
        # GOVERNMENT zone markets (South, ~2000 YER/USD)
        for i in range(20):
            market_id = f"south_{i}"
            dates = pd.date_range('2023-01-01', periods=60, freq='W')
            
            for date in dates:
                data_list.append({
                    'market_id': market_id,
                    'date': date,
                    'commodity': 'wheat',
                    'price_yer': np.random.normal(12000, 1000),  # YER prices
                    'price_usd': np.random.normal(6.0, 0.5),     # ~12000/2000
                    'exchange_rate_used': np.random.normal(2000, 100),
                    'currency_zone': 'GOVERNMENT',
                    'governorate': 'Aden'
                })
        
        # CONTESTED zone markets (mixed control)
        for i in range(10):
            market_id = f"contested_{i}"
            dates = pd.date_range('2023-01-01', periods=60, freq='W')
            
            for date in dates:
                data_list.append({
                    'market_id': market_id,
                    'date': date,
                    'commodity': 'wheat',
                    'price_yer': np.random.normal(8000, 1500),
                    'price_usd': np.random.normal(7.5, 1.0),
                    'exchange_rate_used': np.random.normal(1200, 300),
                    'currency_zone': 'CONTESTED',
                    'governorate': 'Taiz'
                })
        
        return pd.DataFrame(data_list)
    
    @pytest.fixture
    def sample_conflict_data(self):
        """Create sample conflict data."""
        np.random.seed(42)
        
        conflict_list = []
        markets = [f"north_{i}" for i in range(20)] + \
                 [f"south_{i}" for i in range(20)] + \
                 [f"contested_{i}" for i in range(10)]
        
        for market_id in markets:
            # Higher conflict in contested areas
            if 'contested' in market_id:
                base_events = 5
            else:
                base_events = 1
            
            dates = pd.date_range('2023-01-01', periods=60, freq='W')
            for date in dates:
                conflict_list.append({
                    'market_id': market_id,
                    'date': date,
                    'event_count': np.random.poisson(base_events),
                    'control_change': np.random.random() < 0.05  # 5% chance
                })
        
        return pd.DataFrame(conflict_list)
    
    @pytest.fixture
    def sample_geographic_data(self):
        """Create sample geographic data."""
        np.random.seed(42)
        
        markets = [f"north_{i}" for i in range(20)] + \
                 [f"south_{i}" for i in range(20)] + \
                 [f"contested_{i}" for i in range(10)]
        
        geo_data = {}
        for market_id in markets:
            if 'north' in market_id:
                base_capital_dist = 50
            elif 'south' in market_id:
                base_capital_dist = 100
            else:
                base_capital_dist = 150
            
            geo_data[market_id] = {
                'distance_to_capital': base_capital_dist + np.random.normal(0, 20),
                'distance_to_port': np.random.uniform(50, 200),
                'distance_to_border': np.random.uniform(20, 150),
                'urban_rural': np.random.random(),
                'road_quality': np.random.random()
            }
        
        return pd.DataFrame.from_dict(geo_data, orient='index')
    
    def test_initialization(self):
        """Test proper initialization of clustering object."""
        clustering = CurrencyAwareMarketClustering(
            n_clusters_per_zone={'HOUTHI': 3, 'GOVERNMENT': 3},
            min_cluster_size=5,
            clustering_algorithm='kmeans',
            enforce_zone_constraints=True,
            validate_methodology=True
        )
        
        assert clustering.n_clusters_per_zone == {'HOUTHI': 3, 'GOVERNMENT': 3}
        assert clustering.min_cluster_size == 5
        assert clustering.clustering_algorithm == 'kmeans'
        assert clustering.enforce_zone_constraints is True
        assert clustering.validate_methodology is True
        assert not clustering.fitted
    
    def test_fit_with_valid_data(self, sample_panel_data, sample_conflict_data, sample_geographic_data):
        """Test fitting with valid data that passes methodology validation."""
        clustering = CurrencyAwareMarketClustering(
            n_clusters_per_zone={'HOUTHI': 2, 'GOVERNMENT': 2, 'CONTESTED': 1},
            validate_methodology=True
        )
        
        # Mock the validator to return valid
        with patch.object(clustering.validator, 'validate_analysis_inputs') as mock_validate:
            mock_validate.return_value = (True, Mock(critical_failures=[]))
            
            # Fit the model
            clustering.fit(
                sample_panel_data,
                sample_conflict_data,
                sample_geographic_data
            )
            
            assert clustering.fitted
            assert len(clustering.cluster_labels_) == 50  # 50 unique markets
            assert len(np.unique(clustering.cluster_labels_)) == 5  # 2+2+1 clusters
            
            # Check that validator was called
            mock_validate.assert_called_once()
    
    def test_fit_fails_without_usd_prices(self, sample_panel_data, sample_conflict_data):
        """Test that fitting fails when USD prices are missing."""
        # Remove USD prices
        panel_without_usd = sample_panel_data.drop('price_usd', axis=1)
        
        clustering = CurrencyAwareMarketClustering(validate_methodology=False)
        
        with pytest.raises(MethodologyViolation, match="missing USD prices"):
            clustering.fit(panel_without_usd, sample_conflict_data)
    
    def test_fit_fails_methodology_validation(self, sample_panel_data):
        """Test that fitting fails when methodology validation fails."""
        clustering = CurrencyAwareMarketClustering(validate_methodology=True)
        
        # Mock validator to return invalid
        with patch.object(clustering.validator, 'validate_analysis_inputs') as mock_validate:
            mock_validate.return_value = (False, Mock(critical_failures=['Missing exchange rates']))
            
            with pytest.raises(MethodologyViolation, match="Missing exchange rates"):
                clustering.fit(sample_panel_data)
    
    def test_zone_constraint_enforcement(self, sample_panel_data):
        """Test that clusters never span multiple currency zones."""
        clustering = CurrencyAwareMarketClustering(
            enforce_zone_constraints=True,
            validate_methodology=False  # Skip for this test
        )
        
        clustering.fit(sample_panel_data)
        
        # Check each cluster contains only one zone
        for cluster_id in np.unique(clustering.cluster_labels_):
            cluster_mask = clustering.cluster_labels_ == cluster_id
            cluster_features = clustering.features_df_[cluster_mask]
            zones_in_cluster = cluster_features['currency_zone'].unique()
            
            assert len(zones_in_cluster) == 1, \
                f"Cluster {cluster_id} spans multiple zones: {zones_in_cluster}"
    
    def test_automatic_cluster_selection(self, sample_panel_data):
        """Test automatic selection of optimal cluster numbers."""
        clustering = CurrencyAwareMarketClustering(
            n_clusters_per_zone=None,  # Let it choose automatically
            validate_methodology=False
        )
        
        clustering.fit(sample_panel_data)
        
        # Should have selected some clusters for each zone
        assert clustering.n_clusters_per_zone['HOUTHI'] >= 1
        assert clustering.n_clusters_per_zone['GOVERNMENT'] >= 1
        assert clustering.n_clusters_per_zone['CONTESTED'] >= 1
    
    def test_analyze_results(self, sample_panel_data, sample_conflict_data):
        """Test comprehensive result analysis."""
        clustering = CurrencyAwareMarketClustering(
            n_clusters_per_zone={'HOUTHI': 2, 'GOVERNMENT': 2, 'CONTESTED': 1},
            validate_methodology=False
        )
        
        clustering.fit(sample_panel_data, sample_conflict_data)
        results = clustering.analyze_results()
        
        # Check results structure
        assert isinstance(results, CurrencyAwareClusteringResults)
        assert results.algorithm == 'kmeans'
        assert results.total_clusters == 5
        assert results.clusters_by_zone == {'HOUTHI': 2, 'GOVERNMENT': 2, 'CONTESTED': 1}
        
        # Check quality metrics
        assert -1 <= results.overall_silhouette <= 1
        assert results.calinski_harabasz_score > 0
        assert results.davies_bouldin_score > 0
        
        # Check zone-specific metrics
        assert 'HOUTHI' in results.within_zone_silhouette
        assert 'GOVERNMENT' in results.within_zone_silhouette
        assert 'CONTESTED' in results.within_zone_silhouette
        
        # Check cluster profiles
        assert len(results.cluster_profiles) == 5
        for cluster_id, profile in results.cluster_profiles.items():
            assert 'size' in profile
            assert 'currency_zone' in profile
            assert 'avg_price_usd' in profile
            assert 'avg_exchange_rate' in profile
            assert 'conflict_intensity' in profile
        
        # Check methodology compliance
        assert results.methodology_compliance is True
    
    def test_feature_importance_by_zone(self, sample_panel_data):
        """Test that feature importance is calculated per zone."""
        clustering = CurrencyAwareMarketClustering(
            n_clusters_per_zone={'HOUTHI': 2, 'GOVERNMENT': 2, 'CONTESTED': 1},
            validate_methodology=False
        )
        
        clustering.fit(sample_panel_data)
        results = clustering.analyze_results()
        
        # Check feature importance structure
        assert 'HOUTHI' in results.feature_importance_by_zone
        assert 'GOVERNMENT' in results.feature_importance_by_zone
        assert 'CONTESTED' in results.feature_importance_by_zone
        
        # Check that importances sum to 1 for each zone
        for zone, importance in results.feature_importance_by_zone.items():
            total = sum(importance.values())
            assert abs(total - 1.0) < 0.01 or total == 0.0  # Either normalized or all zero
    
    def test_temporal_stability_assessment(self, sample_panel_data):
        """Test temporal stability calculation."""
        clustering = CurrencyAwareMarketClustering(validate_methodology=False)
        
        clustering.fit(sample_panel_data)
        results = clustering.analyze_results()
        
        # Check stability scores
        assert results.temporal_stability is not None
        for cluster_id, stability in results.temporal_stability.items():
            assert 0 <= stability <= 1, f"Invalid stability score for cluster {cluster_id}"
    
    def test_export_for_tier1(self, sample_panel_data):
        """Test export functionality for Tier 1 analysis integration."""
        clustering = CurrencyAwareMarketClustering(
            n_clusters_per_zone={'HOUTHI': 2, 'GOVERNMENT': 2, 'CONTESTED': 1},
            validate_methodology=False
        )
        
        clustering.fit(sample_panel_data)
        
        # Export with cluster assignments
        panel_with_clusters = clustering.export_for_tier1_analysis(sample_panel_data)
        
        # Check that clusters were added
        assert 'cluster' in panel_with_clusters.columns
        assert 'cluster_size' in panel_with_clusters.columns
        assert 'cluster_stability' in panel_with_clusters.columns
        
        # Check that all observations have cluster assignments
        assert panel_with_clusters['cluster'].notna().all()
        
        # Original data should be preserved
        assert len(panel_with_clusters) == len(sample_panel_data)
        assert 'price_usd' in panel_with_clusters.columns
        assert 'currency_zone' in panel_with_clusters.columns
    
    def test_different_algorithms(self, sample_panel_data):
        """Test different clustering algorithms."""
        # Test hierarchical clustering
        clustering_hier = CurrencyAwareMarketClustering(
            clustering_algorithm='hierarchical',
            n_clusters_per_zone={'HOUTHI': 2, 'GOVERNMENT': 2, 'CONTESTED': 1},
            validate_methodology=False
        )
        clustering_hier.fit(sample_panel_data)
        assert clustering_hier.fitted
        
        # Test DBSCAN
        clustering_dbscan = CurrencyAwareMarketClustering(
            clustering_algorithm='dbscan',
            min_cluster_size=3,
            validate_methodology=False
        )
        clustering_dbscan.fit(sample_panel_data)
        assert clustering_dbscan.fitted
    
    def test_visualization(self, sample_panel_data, tmp_path):
        """Test visualization functionality."""
        clustering = CurrencyAwareMarketClustering(
            n_clusters_per_zone={'HOUTHI': 2, 'GOVERNMENT': 2, 'CONTESTED': 1},
            validate_methodology=False
        )
        
        clustering.fit(sample_panel_data)
        
        # Create visualizations
        save_path = tmp_path / "clustering_viz"
        figures = clustering.visualize_results(str(save_path))
        
        # Check that figures were created
        assert 'zone_analysis' in figures
        assert 'feature_importance' in figures
        assert 'cluster_profiles' in figures
        
        # Check that files were saved
        assert (tmp_path / "clustering_viz_zone_analysis.png").exists()
        assert (tmp_path / "clustering_viz_feature_importance.png").exists()
        assert (tmp_path / "clustering_viz_cluster_profiles.png").exists()
    
    def test_prediction_on_new_markets(self, sample_panel_data):
        """Test prediction functionality for new markets."""
        clustering = CurrencyAwareMarketClustering(
            n_clusters_per_zone={'HOUTHI': 2, 'GOVERNMENT': 2, 'CONTESTED': 1},
            validate_methodology=False
        )
        
        # Fit on first 40 markets
        train_markets = sample_panel_data[
            sample_panel_data['market_id'].isin(
                sample_panel_data['market_id'].unique()[:40]
            )
        ]
        clustering.fit(train_markets)
        
        # Create features for remaining markets
        test_markets = sample_panel_data[
            sample_panel_data['market_id'].isin(
                sample_panel_data['market_id'].unique()[40:]
            )
        ]
        
        # Aggregate to market level
        test_features = clustering._create_market_features(test_markets, None, None)
        
        # Predict clusters
        predictions = clustering.predict(test_features)
        
        assert len(predictions) == 10  # 10 test markets
        assert all(p in clustering.cluster_labels_ for p in predictions)
    
    def test_integration_helper_function(self, sample_panel_data):
        """Test the helper function for easy integration."""
        panel_with_clusters, results = apply_currency_aware_clustering(
            panel_data=sample_panel_data,
            n_clusters_per_zone={'HOUTHI': 2, 'GOVERNMENT': 2, 'CONTESTED': 1},
            validate=False
        )
        
        # Check outputs
        assert 'cluster' in panel_with_clusters.columns
        assert isinstance(results, CurrencyAwareClusteringResults)
        assert results.total_clusters == 5
        assert results.methodology_compliance is True
    
    def test_small_zone_handling(self, sample_panel_data):
        """Test handling of zones with few markets."""
        # Create data with a very small zone
        small_zone_data = sample_panel_data[sample_panel_data['market_id'] == 'north_0'].copy()
        small_zone_data['currency_zone'] = 'TINY_ZONE'
        
        combined_data = pd.concat([sample_panel_data, small_zone_data])
        
        clustering = CurrencyAwareMarketClustering(
            min_cluster_size=5,
            validate_methodology=False
        )
        
        clustering.fit(combined_data)
        
        # Small zone should get single cluster
        results = clustering.analyze_results()
        assert results.clusters_by_zone['TINY_ZONE'] == 1
    
    def test_error_handling(self):
        """Test various error conditions."""
        clustering = CurrencyAwareMarketClustering()
        
        # Test prediction before fitting
        with pytest.raises(ValueError, match="Model must be fitted"):
            clustering.predict(pd.DataFrame())
        
        # Test analysis before fitting
        with pytest.raises(ValueError, match="Model must be fitted"):
            clustering.analyze_results()
        
        # Test export before fitting
        with pytest.raises(ValueError, match="Model must be fitted"):
            clustering.export_for_tier1_analysis(pd.DataFrame())
        
        # Test invalid algorithm
        with pytest.raises(ValueError, match="Unknown algorithm"):
            invalid_clustering = CurrencyAwareMarketClustering(
                clustering_algorithm='invalid_algo'
            )
            invalid_clustering.fit(pd.DataFrame({'market_id': [1], 'currency_zone': ['A']}))


class TestIntegrationWithTier1:
    """Test integration of clustering with Tier 1 analysis."""
    
    @pytest.fixture
    def mock_tier1_runner(self):
        """Create a mock Tier 1 runner."""
        mock = Mock()
        mock.run = Mock(return_value=Mock(
            coefficients={'cluster_2': 0.5, 'cluster_3': 0.3},
            p_values={'cluster_2': 0.01, 'cluster_3': 0.05}
        ))
        return mock
    
    def test_tier1_with_clusters(self, sample_panel_data, mock_tier1_runner):
        """Test that Tier 1 can use cluster information."""
        # Add clusters
        panel_with_clusters, results = apply_currency_aware_clustering(
            panel_data=sample_panel_data,
            validate=False
        )
        
        # Mock Tier 1 analysis
        tier1_results = mock_tier1_runner.run(panel_with_clusters)
        
        # Check that cluster coefficients exist
        assert 'cluster_2' in tier1_results.coefficients
        assert tier1_results.p_values['cluster_2'] < 0.05
    
    def test_cluster_fixed_effects(self, sample_panel_data):
        """Test using clusters as fixed effects."""
        panel_with_clusters, _ = apply_currency_aware_clustering(
            panel_data=sample_panel_data,
            n_clusters_per_zone={'HOUTHI': 3, 'GOVERNMENT': 3, 'CONTESTED': 2},
            validate=False
        )
        
        # Check that we can create dummy variables
        cluster_dummies = pd.get_dummies(
            panel_with_clusters['cluster'], 
            prefix='cluster'
        )
        
        assert cluster_dummies.shape[1] == 8  # 3+3+2 clusters
        assert cluster_dummies.sum().sum() == len(panel_with_clusters)  # Each row in one cluster


class TestMethodologyCompliance:
    """Test methodology compliance and validation."""
    
    def test_currency_zone_enforcement(self, sample_panel_data):
        """Test that currency zones are strictly enforced."""
        # Try to create clusters without zones
        data_no_zones = sample_panel_data.copy()
        data_no_zones['currency_zone'] = np.nan
        
        clustering = CurrencyAwareMarketClustering(
            enforce_zone_constraints=True,
            validate_methodology=False
        )
        
        # Should handle missing zones gracefully
        with pytest.raises(Exception):  # Could be various exceptions
            clustering.fit(data_no_zones)
    
    def test_exchange_rate_validation(self, sample_panel_data):
        """Test exchange rate validation in clustering."""
        # Create data with invalid exchange rates
        invalid_data = sample_panel_data.copy()
        invalid_data.loc[invalid_data['currency_zone'] == 'HOUTHI', 'exchange_rate_used'] = 50  # Too low
        
        clustering = CurrencyAwareMarketClustering(validate_methodology=True)
        
        with patch.object(clustering.validator, 'validate_analysis_inputs') as mock_validate:
            mock_validate.return_value = (False, Mock(critical_failures=['Invalid exchange rates']))
            
            with pytest.raises(MethodologyViolation, match="Invalid exchange rates"):
                clustering.fit(invalid_data)
    
    def test_minimum_data_requirements(self):
        """Test minimum data requirements for clustering."""
        # Create minimal data
        minimal_data = pd.DataFrame({
            'market_id': ['m1', 'm1', 'm2', 'm2'],
            'date': pd.date_range('2023-01-01', periods=2).tolist() * 2,
            'price_usd': [10, 11, 12, 13],
            'price_yer': [5350, 5885, 24000, 26000],
            'exchange_rate_used': [535, 535, 2000, 2000],
            'currency_zone': ['HOUTHI', 'HOUTHI', 'GOVERNMENT', 'GOVERNMENT']
        })
        
        clustering = CurrencyAwareMarketClustering(
            min_cluster_size=1,  # Allow small clusters
            validate_methodology=False
        )
        
        # Should work with minimal data
        clustering.fit(minimal_data)
        assert clustering.fitted
        assert len(np.unique(clustering.cluster_labels_)) >= 2