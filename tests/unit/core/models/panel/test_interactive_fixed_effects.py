"""Unit tests for Interactive Fixed Effects model."""

import pytest
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch

# Import without going through problematic __init__ files
import sys
import os

sys.path.insert(
    0,
    os.path.dirname(
        os.path.dirname(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        )
    ),
)

from src.core.models.panel.interactive_fixed_effects import (
    InteractiveFixedEffectsModel,
    IFEResults,
    estimate_optimal_factors,
)


class TestInteractiveFixedEffectsModel:
    """Test suite for IFE model."""

    @pytest.fixture
    def sample_panel_data(self):
        """Generate sample panel data with known factor structure."""
        np.random.seed(42)

        # Parameters
        n_entities = 20
        n_times = 30
        n_factors = 2

        # Generate factors and loadings
        true_factors = np.random.randn(n_times, n_factors)
        true_loadings = np.random.randn(n_entities, n_factors)

        # Create panel
        entities = [f"entity_{i}" for i in range(n_entities)]
        times = pd.date_range("2020-01-01", periods=n_times, freq="M")

        data = []
        for i, entity in enumerate(entities):
            for t, time in enumerate(times):
                # Generate data with factor structure
                x1 = np.random.randn()
                x2 = np.random.randn()

                # Y = X*beta + lambda*F + epsilon
                factor_effect = np.dot(true_loadings[i], true_factors[t])
                y = 0.5 * x1 - 0.3 * x2 + factor_effect + np.random.randn() * 0.2

                data.append(
                    {"entity": entity, "time": time, "y": y, "x1": x1, "x2": x2}
                )

        df = pd.DataFrame(data)
        df = df.set_index(["entity", "time"])

        return df, true_factors, true_loadings

    def test_initialization(self):
        """Test IFE model initialization."""
        model = InteractiveFixedEffectsModel(n_factors=3)

        assert model.n_factors == 3
        assert model.max_iterations == 1000
        assert model.tolerance == 1e-6
        assert model.standardize is True

    def test_input_validation(self, sample_panel_data):
        """Test input validation."""
        df, _, _ = sample_panel_data
        model = InteractiveFixedEffectsModel(n_factors=2)

        # Test with valid data
        X = df[["x1", "x2"]]
        y = df["y"]

        # Should not raise
        model._validate_inputs(X, y)

        # Test with non-MultiIndex
        X_bad = pd.DataFrame({"x1": [1, 2, 3], "x2": [4, 5, 6]})
        y_bad = pd.Series([7, 8, 9])

        with pytest.raises(ValueError, match="MultiIndex"):
            model._validate_inputs(X_bad, y_bad)

        # Test with missing values
        X_missing = X.copy()
        X_missing.iloc[0, 0] = np.nan

        with pytest.raises(ValueError, match="Missing values"):
            model._validate_inputs(X_missing, y)

    def test_fit_basic(self, sample_panel_data):
        """Test basic fitting functionality."""
        df, _, _ = sample_panel_data

        model = InteractiveFixedEffectsModel(n_factors=2)
        X = df[["x1", "x2"]]
        y = df["y"]

        results = model.fit(X, y)

        # Check results structure
        assert isinstance(results, IFEResults)
        assert "x1" in results.coefficients
        assert "x2" in results.coefficients
        assert results.n_factors == 2
        assert results.factors.shape[0] == X.index.get_level_values(1).nunique()
        assert results.factors.shape[1] == 2
        assert results.loadings.shape[0] == X.index.get_level_values(0).nunique()
        assert results.loadings.shape[1] == 2

        # Check convergence
        assert results.iterations < 1000

        # Check R-squared is reasonable
        assert 0 < results.r_squared < 1
        assert results.factor_contribution > 0

    def test_coefficient_recovery(self, sample_panel_data):
        """Test that IFE recovers true coefficients."""
        df, _, _ = sample_panel_data

        model = InteractiveFixedEffectsModel(n_factors=2)
        X = df[["x1", "x2"]]
        y = df["y"]

        results = model.fit(X, y)

        # Check coefficient signs and magnitudes
        # True values: beta1=0.5, beta2=-0.3
        assert 0.3 < results.coefficients["x1"] < 0.7
        assert -0.5 < results.coefficients["x2"] < -0.1

    def test_factor_extraction(self, sample_panel_data):
        """Test factor extraction quality."""
        df, true_factors, true_loadings = sample_panel_data

        model = InteractiveFixedEffectsModel(n_factors=2)
        X = df[["x1", "x2"]]
        y = df["y"]

        results = model.fit(X, y)

        # Factors should explain substantial variation
        assert results.factor_contribution > 0.3

        # Check factor dimensions
        assert results.factors.shape == true_factors.shape
        assert results.loadings.shape == true_loadings.shape

    def test_seasonal_effects_extraction(self, sample_panel_data):
        """Test seasonal effect extraction."""
        df, _, _ = sample_panel_data

        model = InteractiveFixedEffectsModel(n_factors=2)
        X = df[["x1", "x2"]]
        y = df["y"]

        results = model.fit(X, y)

        # Extract seasonal effects
        time_index = df.index.get_level_values("time").unique()
        seasonal = model.extract_seasonal_effects(time_index, results.factors)

        # Check structure
        assert "factor_1_monthly" in seasonal
        assert "factor_2_monthly" in seasonal
        assert "factor_1_ramadan_effect" in seasonal
        assert "factor_2_ramadan_effect" in seasonal

        # Monthly patterns should have 12 values
        assert len(seasonal["factor_1_monthly"]) == 12

    def test_prediction(self, sample_panel_data):
        """Test prediction functionality."""
        df, _, _ = sample_panel_data

        model = InteractiveFixedEffectsModel(n_factors=2)
        X = df[["x1", "x2"]]
        y = df["y"]

        results = model.fit(X, y)

        # Test prediction on new data
        X_new = pd.DataFrame({"x1": [0.5, -0.5], "x2": [1.0, -1.0]})

        predictions = model.predict_with_factors(X_new)

        assert len(predictions) == 2
        assert not np.any(np.isnan(predictions))

    def test_standardization(self, sample_panel_data):
        """Test standardization option."""
        df, _, _ = sample_panel_data

        # With standardization
        model_std = InteractiveFixedEffectsModel(n_factors=2, standardize=True)
        X = df[["x1", "x2"]]
        y = df["y"]

        results_std = model_std.fit(X, y)

        # Without standardization
        model_no_std = InteractiveFixedEffectsModel(n_factors=2, standardize=False)
        results_no_std = model_no_std.fit(X, y)

        # Results should differ
        assert not np.allclose(
            results_std.coefficients.values, results_no_std.coefficients.values
        )

    def test_convergence_failure(self, sample_panel_data):
        """Test handling of convergence failure."""
        df, _, _ = sample_panel_data

        # Set very low max iterations
        model = InteractiveFixedEffectsModel(
            n_factors=2, max_iterations=2, tolerance=1e-10
        )

        X = df[["x1", "x2"]]
        y = df["y"]

        results = model.fit(X, y)

        # Should not converge
        assert not results.converged
        assert results.iterations == 2


class TestOptimalFactorSelection:
    """Test optimal factor selection functionality."""

    def test_estimate_optimal_factors(self):
        """Test automatic factor selection."""
        np.random.seed(42)

        # Generate data with known 2-factor structure
        n_entities = 30
        n_times = 50
        true_n_factors = 2

        # Generate data
        entities = [f"entity_{i}" for i in range(n_entities)]
        times = range(n_times)

        # True factors and loadings
        true_factors = np.random.randn(n_times, true_n_factors)
        true_loadings = np.random.randn(n_entities, true_n_factors)

        data = []
        for i, entity in enumerate(entities):
            for t in times:
                x = np.random.randn()
                factor_effect = np.dot(true_loadings[i], true_factors[t])
                y = 0.5 * x + factor_effect + np.random.randn() * 0.1

                data.append({"entity": entity, "time": t, "y": y, "x": x})

        df = pd.DataFrame(data)
        df = df.set_index(["entity", "time"])

        X = df[["x"]]
        y = df["y"]

        # Estimate optimal factors
        optimal = estimate_optimal_factors(X, y, max_factors=5)

        # Should select close to true number
        assert 1 <= optimal <= 3


class TestInteractiveFixedEffectsWrapper:
    """Test IFE wrapper for tier integration."""

    @pytest.fixture
    def mock_specification(self):
        """Create mock model specification."""
        spec = Mock()
        spec.dependent_variable = "price_usd"
        spec.independent_variables = ["log_exchange_rate", "conflict_intensity"]
        spec.parameters = {
            "entity_var": "market_id",
            "time_var": "date",
            "n_factors": 3,
            "auto_select_factors": False,
        }
        return spec

    def test_wrapper_initialization(self, mock_specification):
        """Test wrapper initialization."""
        from src.core.models.panel.interactive_fixed_effects_wrapper import (
            InteractiveFixedEffectsModelWrapper,
        )

        wrapper = InteractiveFixedEffectsModelWrapper(mock_specification)

        assert wrapper.name == "Interactive Fixed Effects Model"
        assert wrapper.required_data_structure == "panel"
        assert wrapper.n_factors == 3
        assert wrapper.entity_var == "market_id"
        assert wrapper.time_var == "date"

    def test_wrapper_validation(self, mock_specification):
        """Test wrapper data validation."""
        from src.core.models.panel.interactive_fixed_effects_wrapper import (
            InteractiveFixedEffectsModelWrapper,
        )

        wrapper = InteractiveFixedEffectsModelWrapper(mock_specification)

        # Valid data - need at least 10 entities for IFE
        markets = [f"M{i}" for i in range(12)]  # 12 markets
        n_periods = 24
        valid_data = pd.DataFrame(
            {
                "market_id": markets * n_periods,
                "date": pd.date_range("2020-01-01", periods=n_periods).tolist() * 12,
                "price_usd": np.random.rand(12 * n_periods),
                "log_exchange_rate": np.random.rand(12 * n_periods),
                "conflict_intensity": np.random.rand(12 * n_periods),
            }
        )

        errors = wrapper.validate_data(valid_data)
        assert len(errors) == 0

        # Missing columns
        invalid_data = valid_data.drop(columns=["log_exchange_rate"])
        errors = wrapper.validate_data(invalid_data)
        assert len(errors) > 0
        assert any("Missing columns" in e for e in errors)

    def test_wrapper_diagnostics(self, mock_specification):
        """Test wrapper diagnostic list."""
        from src.core.models.panel.interactive_fixed_effects_wrapper import (
            InteractiveFixedEffectsModelWrapper,
        )

        wrapper = InteractiveFixedEffectsModelWrapper(mock_specification)
        diagnostics = wrapper.get_diagnostics()

        assert "factor_significance" in diagnostics
        assert "factor_stability" in diagnostics
        assert "residual_serial_correlation" in diagnostics


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
