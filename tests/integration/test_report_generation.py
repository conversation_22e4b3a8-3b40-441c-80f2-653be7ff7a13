"""
Integration tests for report generation service.

Tests the full pipeline from analysis results to report generation,
ensuring templates are properly filled and validated.
"""

import pytest
from unittest.mock import Mock, patch
import json

from src.application.services.report_generation_service import ReportGenerationService
from src.core.reporting import (
    ResultsTemplateGenerator,
    TestResult,
    IntegrationMetrics,
    TemplateType
)


class TestReportGenerationIntegration:
    """Integration tests for report generation pipeline."""

    def setup_method(self):
        """Set up test fixtures."""
        self.service = ReportGenerationService()
        
        # Sample analysis results mimicking three-tier output
        self.sample_results = {
            'analysis_type': 'three_tier',
            'start_date': '2019-01-01',
            'end_date': '2023-12-31',
            'n_observations': 10000,
            'descriptive_statistics': {
                'wheat_price_usd': {
                    'n': 5834,
                    'mean': 0.54,
                    'std': 0.12,
                    'min': 0.31,
                    'max': 0.89,
                    'cv': 0.22
                },
                'rice_price_usd': {
                    'n': 5621,
                    'mean': 0.78,
                    'std': 0.15,
                    'min': 0.48,
                    'max': 1.34,
                    'cv': 0.19
                }
            },
            'tier1_results': {
                'coefficients': {
                    'conflict_events': {
                        'coefficient': 0.003,
                        't_stat': 0.375,
                        'p_value': 0.708,
                        'ci_lower': -0.013,
                        'ci_upper': 0.019
                    },
                    'exchange_rate_diff': {
                        'coefficient': 0.45,
                        't_stat': 5.2,
                        'p_value': 0.0001,
                        'ci_lower': 0.30,
                        'ci_upper': 0.60
                    }
                }
            },
            'tier2_results': {
                'wheat': {
                    'coefficients': {
                        'global_price': {
                            'coefficient': 0.82,
                            't_stat': 12.5,
                            'p_value': 0.0000,
                            'ci_lower': 0.70,
                            'ci_upper': 0.94
                        }
                    },
                    'integration_metrics': {
                        'speed_of_adjustment': 0.35,
                        'half_life': 2.8,
                        'r_squared': 0.67,
                        'pass_through': 0.84,
                        'error_correction_coef': -0.35
                    }
                }
            },
            'robustness_checks': {
                'outlier_exclusion': {
                    'consistent': True,
                    'main_effect': 0.45,
                    'robust_effect': 0.43
                },
                'alternative_clustering': {
                    'consistent': False,
                    'main_effect': 0.45,
                    'robust_effect': 0.38,
                    'interpretation': 'Effect smaller but still significant with governorate clustering'
                }
            }
        }
    
    def test_full_report_generation(self):
        """Test complete report generation from analysis results."""
        report = self.service.generate_analysis_report(self.sample_results)
        
        # Check all sections present
        expected_sections = [
            'metadata',
            'executive_summary',
            'descriptive_statistics',
            'main_findings',
            'robustness',
            'policy_brief',
            'technical_appendix'
        ]
        
        for section in expected_sections:
            assert section in report
            assert report[section] is not None
            assert len(report[section]) > 0
    
    def test_no_predetermined_language_in_output(self):
        """Test that generated reports contain no predetermined language."""
        report = self.service.generate_analysis_report(self.sample_results)
        
        # Combine all sections
        full_text = " ".join(report.values())
        
        # Check for forbidden phrases
        forbidden_phrases = [
            "revolutionary discovery",
            "game-changing",
            "as expected",
            "confirms our hypothesis",
            "paradigm shift"
        ]
        
        for phrase in forbidden_phrases:
            assert phrase.lower() not in full_text.lower()
    
    def test_null_result_handling(self):
        """Test proper handling of null results."""
        # Conflict effect is null in sample data
        report = self.service.generate_analysis_report(self.sample_results)
        
        # Check that null result is properly reported
        assert "no statistically significant" in report['main_findings'].lower()
        assert "p = 0.708" in report['main_findings']
        assert "null result" in report['main_findings'].lower()
    
    def test_significant_result_handling(self):
        """Test proper handling of significant results."""
        report = self.service.generate_analysis_report(self.sample_results)
        
        # Exchange rate effect is significant in sample data
        assert "exchange_rate_diff" in report['main_findings']
        assert "p = 0.000" in report['main_findings']
        assert "45" in report['main_findings']  # coefficient value
    
    def test_descriptive_statistics_formatting(self):
        """Test descriptive statistics section formatting."""
        report = self.service.generate_analysis_report(self.sample_results)
        desc_section = report['descriptive_statistics']
        
        # Check table structure
        assert "| Variable | N | Mean | SD |" in desc_section
        assert "wheat_price_usd" in desc_section
        assert "5834" in desc_section  # sample size
        assert "0.54" in desc_section  # mean
    
    def test_robustness_section(self):
        """Test robustness checks reporting."""
        report = self.service.generate_analysis_report(self.sample_results)
        robust_section = report['robustness']
        
        # Check both consistent and inconsistent results reported
        assert "✓ Results consistent" in robust_section
        assert "⚠ Results differ" in robust_section
        assert "governorate clustering" in robust_section
    
    def test_policy_implications_conditional(self):
        """Test that policy implications are conditional on results."""
        report = self.service.generate_analysis_report(self.sample_results)
        policy_section = report['policy_brief']
        
        # Should have both null and significant result implications
        assert "no significant findings" not in policy_section  # Has some significant results
        assert "Implementation Considerations" in policy_section
    
    def test_currency_conversion_mentioned(self):
        """Test that currency conversion is acknowledged."""
        # Modify sample data to include currency info
        self.sample_results['currency_conversion'] = {
            'method': 'parallel_market_rates',
            'source': 'WFP',
            'validation': 'Prices fall within expected USD ranges'
        }
        
        report = self.service.generate_analysis_report(self.sample_results)
        
        # Check metadata mentions currency
        assert "USD" in report['metadata'] or "currency" in report['metadata'].lower()
    
    def test_integration_metrics_reporting(self):
        """Test proper reporting of integration metrics."""
        report = self.service.generate_analysis_report(self.sample_results)
        
        # Check tier2 results include integration assessment
        assert "Moderate market integration detected" in report['main_findings']
        assert "speed of adjustment" in report['main_findings'].lower()
    
    def test_technical_appendix_completeness(self):
        """Test technical appendix includes all specifications."""
        # Add specification info
        self.sample_results['tier1_specification'] = "log(P) = α + βX + γi + δt + ε"
        self.sample_results['diagnostics'] = {
            'hausman_test': {
                'statistic': 45.2,
                'p_value': 0.001,
                'interpretation': 'Use fixed effects'
            }
        }
        
        report = self.service.generate_analysis_report(self.sample_results)
        appendix = report['technical_appendix']
        
        assert "Model Specifications" in appendix
        assert "log(P)" in appendix
        assert "Diagnostic Test Results" in appendix
        assert "hausman_test" in appendix
    
    def test_handles_missing_sections_gracefully(self):
        """Test graceful handling of missing analysis sections."""
        # Remove some sections
        minimal_results = {
            'analysis_type': 'three_tier',
            'start_date': '2019-01-01',
            'end_date': '2023-12-31',
            'tier1_results': self.sample_results['tier1_results']
        }
        
        report = self.service.generate_analysis_report(minimal_results)
        
        # Should still generate report
        assert 'executive_summary' in report
        assert 'main_findings' in report
        assert '[No descriptive statistics available]' in report['descriptive_statistics']
    
    def test_save_report_functionality(self):
        """Test report saving to file."""
        import tempfile
        import os
        
        with tempfile.TemporaryDirectory() as tmpdir:
            output_path = os.path.join(tmpdir, 'test_report.md')
            
            report = self.service.generate_analysis_report(self.sample_results)
            self.service.save_report(report, output_path)
            
            # Check file created and contains content
            assert os.path.exists(output_path)
            
            with open(output_path, 'r') as f:
                content = f.read()
                assert '# Analysis Report' in content
                assert 'Executive Summary' in content
    
    def test_multiple_hypothesis_handling(self):
        """Test handling of multiple hypotheses results."""
        # Add tier3 results
        self.sample_results['tier3_results'] = {
            'validation_metrics': {
                'out_of_sample_r2': 0.58,
                'prediction_error': 0.15
            }
        }
        
        report = self.service.generate_analysis_report(self.sample_results)
        
        # Should handle all three tiers
        assert 'Tier1 Results' in report['main_findings']
        assert 'Tier2 Results' in report['main_findings']
        assert 'Tier3 Results' in report['main_findings']


class TestTemplateIntegrity:
    """Test template integrity in real usage scenarios."""
    
    def test_template_generator_integration(self):
        """Test that template generator properly integrates with service."""
        generator = ResultsTemplateGenerator()
        service = ReportGenerationService(template_generator=generator)
        
        # Test with predetermined language - should fail
        bad_result = TestResult(
            test_name="Bad Test",
            test_statistic=2.5,
            p_value=0.02,
            effect_size=0.5,
            confidence_interval=(0.1, 0.9),
            interpretation="This revolutionary discovery changes everything",
            robustness_checks=[]
        )
        
        bad_results = {
            'tier1_results': {
                'coefficients': {
                    'test_var': {
                        'coefficient': 0.5,
                        't_stat': 2.5,
                        'p_value': 0.02,
                        'ci_lower': 0.1,
                        'ci_upper': 0.9,
                        'interpretation': "This revolutionary discovery changes everything"
                    }
                }
            }
        }
        
        # Should log warning about predetermined language
        with patch('src.application.services.report_generation_service.logger') as mock_logger:
            report = service.generate_analysis_report(bad_results)
            assert mock_logger.warning.called


if __name__ == "__main__":
    pytest.main([__file__, "-v"])