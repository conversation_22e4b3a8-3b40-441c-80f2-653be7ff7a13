"""Test repository-level currency enforcement."""

import pytest
import pandas as pd
from datetime import datetime
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch

from src.core.domain.market.entities import Market, PriceObservation
from src.core.domain.market.value_objects import (
    MarketId, Coordinates, MarketType, Price, Currency, Commodity
)
from src.core.domain.shared.exceptions import ValidationException
from src.core.validation.methodology_validator import MethodologyViolation
from src.infrastructure.persistence import (
    InMemoryMarketRepository,
    InMemoryPriceRepository
)
from src.infrastructure.persistence.validated_repositories import (
    ValidatedPriceRepository,
    ValidatedMarketRepository
)


class TestValidatedPriceRepository:
    """Test that ValidatedPriceRepository enforces currency conversion."""
    
    @pytest.fixture
    def base_price_repo(self):
        """Create base in-memory price repository."""
        return InMemoryPriceRepository()
        
    @pytest.fixture
    def base_market_repo(self):
        """Create base in-memory market repository."""
        return InMemoryMarketRepository()
        
    @pytest.fixture
    def sample_market(self):
        """Create a sample market."""
        return Market(
            market_id=MarketId(value="sana-main"),
            name="Sana'a Main Market",
            coordinates=Coordinates(latitude=15.3694, longitude=44.1910),
            market_type=MarketType.URBAN,
            governorate="Sana'a City",  # Houthi zone
            district="Old City",
            active_since=datetime(2020, 1, 1),
            active_until=None
        )
        
    @pytest.fixture
    def sample_observation_yer(self):
        """Create sample price observation in YER."""
        return PriceObservation(
            market_id=MarketId(value="sana-main"),
            commodity=Commodity(code="WHEAT", name="Wheat", category="cereals", standard_unit="kg"),
            price=Price(
                amount=Decimal("1000"),
                currency=Currency.YER,
                unit="kg"
            ),
            observed_date=datetime(2024, 1, 15),
            source="WFP"
        )
        
    @pytest.mark.asyncio
    async def test_repository_blocks_retrieval_without_usd_conversion(
        self, base_price_repo, base_market_repo, sample_market, sample_observation_yer
    ):
        """Test that validated repository blocks data without USD conversion."""
        # Save market and observation to base repos
        await base_market_repo.save(sample_market)
        await base_price_repo.save(sample_observation_yer)
        
        # Create validated repository with mocked exchange rate collector
        mock_exchange_collector = AsyncMock()
        mock_exchange_collector.get_exchange_rate.return_value = None  # No rate available
        
        validated_repo = ValidatedPriceRepository(
            wrapped_repository=base_price_repo,
            market_repository=base_market_repo,
            exchange_rate_collector=mock_exchange_collector
        )
        
        # Attempt to retrieve observations
        try:
            result = await validated_repo.find_by_market_and_commodity(
                market_id=sample_market.market_id,
                commodity=sample_observation_yer.commodity,
                start_date=datetime(2024, 1, 1),
                end_date=datetime(2024, 1, 31)
            )
            # If we get here, check if the enriched dataframe has issues
            df = validated_repo.get_last_enriched_dataframe()
            print(f"Enriched DataFrame:\n{df}")
            print(f"Result: {result}")
            pytest.fail("Expected MethodologyViolation to be raised")
        except MethodologyViolation as e:
            assert "USD prices" in str(e) or "exchange rate" in str(e)
        
    @pytest.mark.asyncio
    async def test_repository_enriches_data_with_usd_prices(
        self, base_price_repo, base_market_repo, sample_market, sample_observation_yer
    ):
        """Test that validated repository enriches data with USD prices."""
        # Save market and observation
        await base_market_repo.save(sample_market)
        await base_price_repo.save(sample_observation_yer)
        
        # Create validated repository with mocked exchange rate collector
        mock_exchange_collector = AsyncMock()
        
        # Create mock zone exchange rate
        from src.core.domain.market.currency_zones import ZoneExchangeRate, CurrencyZone
        from src.core.domain.market.value_objects import Currency as Curr
        from datetime import datetime as dt
        from decimal import Decimal as Dec
        
        mock_rate = ZoneExchangeRate(
            zone=CurrencyZone.HOUTHI,
            from_currency=Curr.YER,
            to_currency=Curr.USD,
            rate=Dec('535.0'),
            date=dt(2024, 1, 15),
            rate_type='official_cby_sanaa',
            source='CBY_Sanaa',
            confidence=0.9
        )
        mock_exchange_collector.collect_daily_rates.return_value = [mock_rate]
        
        validated_repo = ValidatedPriceRepository(
            wrapped_repository=base_price_repo,
            market_repository=base_market_repo,
            exchange_rate_collector=mock_exchange_collector
        )
        
        # Retrieve observations
        observations = await validated_repo.find_by_market_and_commodity(
            market_id=sample_market.market_id,
            commodity=sample_observation_yer.commodity
        )
        
        # Should return observations (original objects)
        assert len(observations) == 1
        
        # Check enriched DataFrame
        enriched_df = validated_repo.get_last_enriched_dataframe()
        assert enriched_df is not None
        print(f"Enriched DataFrame columns: {enriched_df.columns.tolist()}")
        print(f"Enriched DataFrame:\n{enriched_df}")
        assert 'price_usd' in enriched_df.columns
        assert 'exchange_rate_used' in enriched_df.columns
        assert 'currency_zone' in enriched_df.columns
        
        # Verify calculations
        assert enriched_df.iloc[0]['price_usd'] == pytest.approx(1000 / 535.0, rel=0.01)
        assert enriched_df.iloc[0]['exchange_rate_used'] == 535.0
        assert enriched_df.iloc[0]['currency_zone'] == 'HOUTHI'
        
    @pytest.mark.asyncio
    async def test_repository_handles_multiple_currency_zones(
        self, base_price_repo, base_market_repo
    ):
        """Test repository handles markets in different currency zones."""
        # Create markets in different zones
        houthi_market = Market(
            market_id=MarketId(value="sana-main"),
            name="Sana'a Market",
            coordinates=Coordinates(latitude=15.3694, longitude=44.1910),
            market_type=MarketType.URBAN,
            governorate="Sana'a City",
            district="Old City",
            active_since=datetime(2020, 1, 1)
        )
        
        gov_market = Market(
            market_id=MarketId(value="aden-main"),
            name="Aden Market",
            coordinates=Coordinates(latitude=12.8628, longitude=44.9827),
            market_type=MarketType.PORT,
            governorate="Aden",
            district="Crater",
            active_since=datetime(2020, 1, 1)
        )
        
        # Save markets
        await base_market_repo.save(houthi_market)
        await base_market_repo.save(gov_market)
        
        # Create observations
        wheat = Commodity(code="WHEAT", name="Wheat", category="cereals", standard_unit="kg")
        
        obs1 = PriceObservation(
            market_id=houthi_market.market_id,
            commodity=wheat,
            price=Price(amount=Decimal("1000"), currency=Currency.YER, unit="kg"),
            observed_date=datetime(2024, 1, 15),
            source="WFP"
        )
        
        obs2 = PriceObservation(
            market_id=gov_market.market_id,
            commodity=wheat,
            price=Price(amount=Decimal("3000"), currency=Currency.YER, unit="kg"),
            observed_date=datetime(2024, 1, 15),
            source="WFP"
        )
        
        await base_price_repo.save(obs1)
        await base_price_repo.save(obs2)
        
        # Create validated repository
        mock_exchange_collector = AsyncMock()
        
        # Create mock zone exchange rates for both zones
        from src.core.domain.market.currency_zones import ZoneExchangeRate, CurrencyZone
        from src.core.domain.market.value_objects import Currency as Curr
        from datetime import datetime as dt
        from decimal import Decimal as Dec
        
        mock_rates = [
            ZoneExchangeRate(
                zone=CurrencyZone.HOUTHI,
                from_currency=Curr.YER,
                to_currency=Curr.USD,
                rate=Dec('535.0'),
                date=dt(2024, 1, 15),
                rate_type='official_cby_sanaa',
                source='CBY_Sanaa',
                confidence=0.9
            ),
            ZoneExchangeRate(
                zone=CurrencyZone.GOVERNMENT,
                from_currency=Curr.YER,
                to_currency=Curr.USD,
                rate=Dec('2000.0'),
                date=dt(2024, 1, 15),
                rate_type='official_cby_aden',
                source='CBY_Aden',
                confidence=0.9
            )
        ]
        mock_exchange_collector.collect_daily_rates.return_value = mock_rates
        
        validated_repo = ValidatedPriceRepository(
            wrapped_repository=base_price_repo,
            market_repository=base_market_repo,
            exchange_rate_collector=mock_exchange_collector
        )
        
        # Retrieve all observations
        observations = await validated_repo.find_by_date_range(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 1, 31)
        )
        
        assert len(observations) == 2
        
        # Check enriched data
        enriched_df = validated_repo.get_last_enriched_dataframe()
        
        # Sort by market_id for consistent testing
        enriched_df = enriched_df.sort_values('market_id')
        
        # Aden market (government zone)
        aden_row = enriched_df[enriched_df['market_id'] == 'aden-main'].iloc[0]
        assert aden_row['currency_zone'] == 'GOVERNMENT'
        assert aden_row['exchange_rate_used'] == 2000.0
        assert aden_row['price_usd'] == pytest.approx(3000 / 2000.0, rel=0.01)
        
        # Sana'a market (Houthi zone)
        sana_row = enriched_df[enriched_df['market_id'] == 'sana-main'].iloc[0]
        assert sana_row['currency_zone'] == 'HOUTHI'
        assert sana_row['exchange_rate_used'] == 535.0
        assert sana_row['price_usd'] == pytest.approx(1000 / 535.0, rel=0.01)
        
    @pytest.mark.asyncio
    async def test_repository_blocks_save_without_exchange_rate(
        self, base_price_repo, base_market_repo, sample_market
    ):
        """Test that repository blocks saving YER prices without available exchange rates."""
        # Save market
        await base_market_repo.save(sample_market)
        
        # Create validated repository with no exchange rates
        mock_exchange_collector = AsyncMock()
        mock_exchange_collector.get_exchange_rate.return_value = None
        
        validated_repo = ValidatedPriceRepository(
            wrapped_repository=base_price_repo,
            market_repository=base_market_repo,
            exchange_rate_collector=mock_exchange_collector
        )
        
        # Try to save YER observation
        observation = PriceObservation(
            market_id=sample_market.market_id,
            commodity=Commodity(code="SUGAR", name="Sugar", category="imported", standard_unit="kg"),
            price=Price(amount=Decimal("2000"), currency=Currency.YER, unit="kg"),
            observed_date=datetime(2024, 1, 20),
            source="WFP"
        )
        
        with pytest.raises(ValidationException) as exc_info:
            await validated_repo.save(observation)
            
        assert "exchange rate" in str(exc_info.value).lower()
        
    @pytest.mark.asyncio
    async def test_repository_allows_usd_prices_directly(
        self, base_price_repo, base_market_repo, sample_market
    ):
        """Test that repository allows saving USD prices directly."""
        # Save market
        await base_market_repo.save(sample_market)
        
        # Create mocked exchange rate collector
        mock_exchange_collector = AsyncMock()
        mock_exchange_collector.collect_daily_rates.return_value = []
        
        # Create validated repository
        validated_repo = ValidatedPriceRepository(
            wrapped_repository=base_price_repo,
            market_repository=base_market_repo,
            exchange_rate_collector=mock_exchange_collector
        )
        
        # Save USD observation
        observation = PriceObservation(
            market_id=sample_market.market_id,
            commodity=Commodity(code="RICE", name="Rice", category="cereals", standard_unit="kg"),
            price=Price(amount=Decimal("2.5"), currency=Currency.USD, unit="kg"),
            observed_date=datetime(2024, 1, 20),
            source="WFP"
        )
        
        # Should save without error
        await validated_repo.save(observation)
        
        # Verify it was saved
        retrieved = await validated_repo.find_by_market_and_commodity(
            market_id=sample_market.market_id,
            commodity=observation.commodity
        )
        
        assert len(retrieved) == 1


class TestValidatedMarketRepository:
    """Test that ValidatedMarketRepository enforces required fields."""
    
    @pytest.fixture
    def base_market_repo(self):
        """Create base in-memory market repository."""
        return InMemoryMarketRepository()
        
    @pytest.mark.asyncio
    async def test_repository_validates_governorate_requirement(self, base_market_repo):
        """Test that repository validates governorate is present."""
        validated_repo = ValidatedMarketRepository(base_market_repo)
        
        # Create market with governorate first
        market = Market(
            market_id=MarketId(value="test-market"),
            name="Test Market",
            coordinates=Coordinates(latitude=15.0, longitude=44.0),
            market_type=MarketType.RURAL,
            governorate="Test Gov",  # Valid governorate
            district="Test District",
            active_since=datetime(2020, 1, 1)
        )
        
        # Override the governorate to empty after creation to bypass entity validation
        market.governorate = ""
        
        with pytest.raises(ValidationException) as exc_info:
            await validated_repo.save(market)
            
        assert "governorate" in str(exc_info.value).lower()
        
    @pytest.mark.asyncio  
    async def test_repository_validates_coordinates_requirement(self, base_market_repo):
        """Test that repository validates coordinates are present."""
        validated_repo = ValidatedMarketRepository(base_market_repo)
        
        # Create market without coordinates
        market = Market(
            market_id=MarketId(value="test-market"),
            name="Test Market",
            coordinates=None,  # No coordinates
            market_type=MarketType.RURAL,
            governorate="Test Gov",
            district="Test District",
            active_since=datetime(2020, 1, 1)
        )
        
        # Override coordinates to None after creation
        market.coordinates = None
        
        with pytest.raises(ValidationException) as exc_info:
            await validated_repo.save(market)
            
        assert "coordinates" in str(exc_info.value).lower()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])