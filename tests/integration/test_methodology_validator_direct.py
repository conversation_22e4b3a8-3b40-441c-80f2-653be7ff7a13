"""Direct tests for methodology validator to ensure it blocks invalid analyses."""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.core.validation.methodology_validator import (
    MethodologyValidator, 
    MethodologyViolation, 
    AnalysisType,
    ValidationReport
)


class TestMethodologyValidatorDirect:
    """Test the MethodologyValidator directly."""
    
    def test_validator_blocks_missing_usd_prices(self):
        """Test that validator blocks data without USD prices."""
        # Create data missing USD prices
        data = pd.DataFrame({
            'market_id': ['market_1'] * 10,
            'date': pd.date_range('2023-01-01', periods=10),
            'commodity': ['wheat'] * 10,
            'price_yer': [1000, 1010, 1020, 1030, 1040, 1050, 1060, 1070, 1080, 1090],
            # Missing: price_usd, exchange_rate_used, currency_zone
        })
        
        validator = MethodologyValidator()
        is_valid, report = validator.validate_analysis_inputs(
            observations=data,
            analysis_type=AnalysisType.PANEL_ANALYSIS,
            hypothesis_tests=['H1', 'H2']
        )
        
        # Should fail validation
        assert not is_valid
        assert len(report.critical_failures) > 0
        assert any('currency' in failure.lower() for failure in report.critical_failures)
    
    def test_validator_blocks_missing_exchange_rates(self):
        """Test that validator blocks data without exchange rates."""
        data = pd.DataFrame({
            'market_id': ['market_1'] * 10,
            'date': pd.date_range('2023-01-01', periods=10),
            'commodity': ['wheat'] * 10,
            'price_yer': [1000] * 10,
            'price_usd': [2.0] * 10,  # Has USD price
            'currency_zone': ['HOUTHI'] * 10,
            # Missing: exchange_rate_used
        })
        
        validator = MethodologyValidator()
        is_valid, report = validator.validate_analysis_inputs(
            observations=data,
            analysis_type=AnalysisType.PANEL_ANALYSIS
        )
        
        # Should fail due to missing exchange rate
        assert not is_valid
        assert any('exchange_rate_used' in failure for failure in report.critical_failures)
    
    def test_validator_blocks_missing_currency_zones(self):
        """Test that validator blocks data without currency zone classification."""
        data = pd.DataFrame({
            'market_id': ['market_1'] * 10,
            'date': pd.date_range('2023-01-01', periods=10),
            'commodity': ['wheat'] * 10,
            'price_yer': [1000] * 10,
            'price_usd': [2.0] * 10,
            'exchange_rate_used': [535.0] * 10,
            # Missing: currency_zone
        })
        
        validator = MethodologyValidator()
        is_valid, report = validator.validate_analysis_inputs(
            observations=data,
            analysis_type=AnalysisType.PANEL_ANALYSIS
        )
        
        # Should fail due to missing currency zone
        assert not is_valid
        assert any('currency_zone' in failure for failure in report.critical_failures)
    
    def test_validator_passes_with_complete_data(self):
        """Test that validator passes with all required fields."""
        data = pd.DataFrame({
            'market_id': ['market_1'] * 10,
            'date': pd.date_range('2023-01-01', periods=10),
            'commodity': ['wheat'] * 10,
            'price_yer': [1000] * 10,
            'price_usd': [1.87] * 10,
            'exchange_rate_used': [535.0] * 10,
            'currency_zone': ['HOUTHI'] * 10,
            'exchange_rate_source': ['CBY_Sanaa'] * 10
        })
        
        validator = MethodologyValidator()
        is_valid, report = validator.validate_analysis_inputs(
            observations=data,
            analysis_type=AnalysisType.PANEL_ANALYSIS
        )
        
        # Should pass validation
        assert is_valid
        assert len(report.critical_failures) == 0
        assert report.currency_conversion_compliance == 1.0
    
    def test_validator_warns_on_suspicious_exchange_rates(self):
        """Test that validator warns about suspicious exchange rates."""
        data = pd.DataFrame({
            'market_id': ['market_1', 'market_2'] * 5,
            'date': pd.date_range('2023-01-01', periods=10),
            'commodity': ['wheat'] * 10,
            'price_yer': [1000] * 10,
            'price_usd': [1000.0, 0.5] * 5,  # Implies rates of 1 and 2000
            'exchange_rate_used': [1.0, 2000.0] * 5,  # Suspicious: 1.0 is too low
            'currency_zone': ['HOUTHI', 'GOVERNMENT'] * 5,
            'exchange_rate_source': ['test'] * 10
        })
        
        validator = MethodologyValidator()
        is_valid, report = validator.validate_analysis_inputs(
            observations=data,
            analysis_type=AnalysisType.PANEL_ANALYSIS
        )
        
        # Should have warnings about exchange rates
        assert len(report.warnings) > 0
        assert any('exchange rate' in warning.lower() for warning in report.warnings)
    
    def test_validator_checks_statistical_power(self):
        """Test that validator checks for adequate statistical power."""
        # Create minimal data (insufficient observations)
        data = pd.DataFrame({
            'market_id': ['market_1'],
            'date': [datetime(2023, 1, 1)],
            'commodity': ['wheat'],
            'price_yer': [1000],
            'price_usd': [1.87],
            'exchange_rate_used': [535.0],
            'currency_zone': ['HOUTHI'],
            'exchange_rate_source': ['CBY_Sanaa']
        })
        
        validator = MethodologyValidator()
        is_valid, report = validator.validate_analysis_inputs(
            observations=data,
            analysis_type=AnalysisType.PANEL_ANALYSIS
        )
        
        # Should have errors about insufficient data
        assert not report.statistical_power_adequate
        assert any('observations' in error for error in report.errors)
    
    def test_methodology_violation_exception(self):
        """Test that MethodologyViolation exception works properly."""
        # Create invalid data
        data = pd.DataFrame({
            'market_id': ['market_1'],
            'date': [datetime(2023, 1, 1)],
            'price': [1000],  # Wrong column name
        })
        
        validator = MethodologyValidator()
        is_valid, report = validator.validate_analysis_inputs(
            observations=data,
            analysis_type=AnalysisType.PANEL_ANALYSIS
        )
        
        # Test exception
        with pytest.raises(MethodologyViolation) as exc_info:
            raise MethodologyViolation("Test failure", report=report)
        
        assert "Test failure" in str(exc_info.value)
        assert exc_info.value.report == report
    
    def test_multiple_testing_corrections_required(self):
        """Test that validator identifies when multiple testing corrections are needed."""
        data = pd.DataFrame({
            'market_id': ['market_1'] * 100,
            'date': pd.date_range('2023-01-01', periods=100),
            'commodity': ['wheat'] * 100,
            'price_yer': np.random.normal(1000, 50, 100),
            'price_usd': np.random.normal(2, 0.1, 100),
            'exchange_rate_used': [535.0] * 100,
            'currency_zone': ['HOUTHI'] * 100,
            'exchange_rate_source': ['CBY_Sanaa'] * 100
        })
        
        validator = MethodologyValidator()
        is_valid, report = validator.validate_analysis_inputs(
            observations=data,
            analysis_type=AnalysisType.HYPOTHESIS_TESTING,
            hypothesis_tests=['H1', 'H2', 'H3', 'H4', 'H5']  # Primary hypotheses
        )
        
        # Should include info about Bonferroni correction
        assert any('bonferroni' in str(issue).lower() for issue in report.issues)
        assert any('0.01' in str(issue) for issue in report.issues)  # α = 0.01
    
    def test_currency_zone_distribution_check(self):
        """Test that validator checks currency zone representation."""
        # Data from only one zone
        data = pd.DataFrame({
            'market_id': [f'market_{i}' for i in range(100)],
            'date': [datetime(2023, 1, 1)] * 100,
            'commodity': ['wheat'] * 100,
            'price_yer': np.random.normal(1000, 50, 100),
            'price_usd': np.random.normal(2, 0.1, 100),
            'exchange_rate_used': [535.0] * 100,
            'currency_zone': ['HOUTHI'] * 100,  # Only one zone
            'exchange_rate_source': ['CBY_Sanaa'] * 100
        })
        
        validator = MethodologyValidator()
        is_valid, report = validator.validate_analysis_inputs(
            observations=data,
            analysis_type=AnalysisType.PANEL_ANALYSIS
        )
        
        # Should warn about limited zone representation
        assert any('zone' in warning.lower() and 'represented' in warning.lower() 
                  for warning in report.warnings)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])