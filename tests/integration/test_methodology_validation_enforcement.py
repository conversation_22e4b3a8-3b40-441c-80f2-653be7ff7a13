"""Integration tests for methodology validation enforcement in tier runners."""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

from src.application.analysis_tiers.tier1_runner import Tier1Runner
from src.application.analysis_tiers.tier2_runner import Tier2Runner
from src.application.analysis_tiers.tier3_runner import Tier3Runner
from src.core.validation.methodology_validator import MethodologyViolation


@pytest.fixture
def mock_repositories():
    """Create mock repositories for testing."""
    market_repo = AsyncMock()
    price_repo = AsyncMock()
    orchestrator = AsyncMock()
    estimator_service = AsyncMock()
    
    # Mock market data
    mock_market = MagicMock()
    mock_market.market_id = MagicMock(value="market_1")
    mock_market.name = "Test Market"
    mock_market.governorate = "Aden"
    mock_market.district = "Test District"
    mock_market.coordinates = MagicMock(latitude=12.7855, longitude=45.0187)
    mock_market.market_type = MagicMock(value="retail")
    mock_market.is_accessible = True
    
    market_repo.find_all.return_value = [mock_market]
    market_repo.find_by_ids.return_value = [mock_market]
    
    # Mock price data without USD prices (to test validation failure)
    mock_price = MagicMock()
    mock_price.market_id = MagicMock(value="market_1")
    mock_price.date = datetime.now()
    mock_price.price_per_unit = 1000.0
    mock_price.commodity_code = "wheat"
    mock_price.source = "WFP"
    mock_price.unit_of_measure = "kg"
    mock_price.quality_grade = "standard"
    
    price_repo.find_by_date_range.return_value = [mock_price]
    
    return {
        'market_repo': market_repo,
        'price_repo': price_repo,
        'orchestrator': orchestrator,
        'estimator_service': estimator_service
    }


@pytest.fixture
def mock_command():
    """Create mock command for testing."""
    command = MagicMock()
    command.start_date = datetime(2023, 1, 1)
    command.end_date = datetime(2023, 12, 31)
    command.market_ids = None
    command.commodity_codes = ["wheat"]
    command.hypotheses = ["H1", "H2"]
    command.tier1_config = {"log_transform": True, "include_weather": False}
    command.tier2_config = {"min_obs": 30, "n_regimes": 2}
    command.tier3_config = {"validation_methods": ["cross_validation"], "factor_analysis": False, "pca_analysis": False}
    command.run_diagnostics = False
    command.apply_corrections = False
    
    return command


class TestTier1ValidationEnforcement:
    """Test methodology validation enforcement in Tier 1 runner."""
    
    @pytest.mark.asyncio
    async def test_tier1_blocks_missing_usd_prices(self, mock_repositories, mock_command):
        """Test that Tier 1 analysis is blocked when USD prices cannot be created due to missing governorate."""
        # Modify market to have missing governorate (needed for currency zone classification)
        mock_market = MagicMock()
        mock_market.market_id = MagicMock(value="market_1")
        mock_market.name = "Test Market"
        mock_market.governorate = None  # Missing governorate prevents currency zone classification
        mock_market.district = "Test District"
        mock_market.coordinates = MagicMock(latitude=12.7855, longitude=45.0187)
        mock_market.market_type = MagicMock(value="retail")
        mock_market.is_accessible = True
        
        mock_repositories['market_repo'].find_all.return_value = [mock_market]
        mock_repositories['market_repo'].find_by_ids.return_value = [mock_market]
        
        # Create runner
        runner = Tier1Runner(**mock_repositories)
        
        # Run should raise MethodologyViolation due to inability to create USD prices
        with pytest.raises(MethodologyViolation) as exc_info:
            await runner.run(mock_command, "test_analysis_id")
        
        # Check error message
        assert "Critical validation failures" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_tier1_blocks_missing_exchange_rates(self, mock_repositories, mock_command):
        """Test that Tier 1 analysis is blocked when exchange rates cannot be determined."""
        # Modify market to have missing governorate so exchange rates can't be determined
        mock_market = MagicMock()
        mock_market.market_id = MagicMock(value="market_1")
        mock_market.name = "Test Market"
        mock_market.governorate = None  # Missing governorate prevents exchange rate determination
        mock_market.district = "Test District"
        mock_market.coordinates = MagicMock(latitude=12.7855, longitude=45.0187)
        mock_market.market_type = MagicMock(value="retail")
        mock_market.is_accessible = True
        
        mock_repositories['market_repo'].find_all.return_value = [mock_market]
        mock_repositories['market_repo'].find_by_ids.return_value = [mock_market]
        
        runner = Tier1Runner(**mock_repositories)
        
        with pytest.raises(MethodologyViolation) as exc_info:
            await runner.run(mock_command, "test_analysis_id")
        
        assert "Critical validation failures" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_tier1_passes_with_valid_data(self, mock_repositories, mock_command):
        """Test that Tier 1 analysis proceeds with properly converted data."""
        # This test would need proper mocking of the entire pipeline
        # For now, we just verify the structure is in place
        runner = Tier1Runner(**mock_repositories)
        
        # Verify the runner has validation logic
        assert hasattr(runner, '_logger')
        
        # The actual run will fail due to missing model implementations,
        # but the validation check is what we're testing
        with pytest.raises(Exception) as exc_info:
            await runner.run(mock_command, "test_analysis_id")
        
        # Should not be a MethodologyViolation if we have proper data
        # (will fail later in the pipeline)
        # This assertion depends on mock setup


class TestTier2ValidationEnforcement:
    """Test methodology validation enforcement in Tier 2 runner."""
    
    @pytest.mark.asyncio
    async def test_tier2_validates_each_commodity(self, mock_repositories, mock_command):
        """Test that Tier 2 validates data for each commodity and fails when markets lack governorate."""
        # Mock multiple markets without governorate to prevent USD price creation
        mock_markets = []
        for i in range(2):  # Need at least 2 markets for tier2 pairs
            mock_market = MagicMock()
            mock_market.market_id = MagicMock(value=f"market_{i+1}")
            mock_market.name = f"Test Market {i+1}"
            mock_market.governorate = None  # Missing governorate prevents USD price creation
            mock_market.district = "Test District"
            # Place markets close together (within 300km) for pairing
            mock_market.coordinates = MagicMock(latitude=12.7855 + i*0.1, longitude=45.0187 + i*0.1)
            mock_market.market_type = MagicMock(value="retail")
            mock_market.is_accessible = True
            mock_markets.append(mock_market)
        
        mock_repositories['market_repo'].find_all.return_value = mock_markets
        mock_repositories['market_repo'].find_by_ids.return_value = mock_markets
        
        # Mock commodity object
        mock_commodity = MagicMock()
        mock_commodity.code = "wheat"
        mock_commodity.name = "Wheat"
        mock_commodity.category = "food"
        mock_commodity.standard_unit = "kg"
        mock_repositories['price_repo'].find_commodity_by_code.return_value = mock_commodity
        
        # Mock price observations with sufficient data (>100 observations for tier2)
        mock_observations = []
        for i in range(105):  # More than min_obs threshold
            mock_obs = MagicMock()
            mock_obs.observed_date = datetime.now()
            mock_obs.price = MagicMock(amount=1000.0 + i)
            mock_observations.append(mock_obs)
        
        mock_repositories['price_repo'].find_by_market_and_commodity.return_value = mock_observations
        
        # Set commodity to force tier2 to attempt analysis
        mock_command.commodity_ids = ["wheat"]
        
        runner = Tier2Runner(**mock_repositories)
        
        # Should raise validation error when USD prices can't be created
        with pytest.raises(MethodologyViolation) as exc_info:
            await runner.run(mock_command, "test_analysis_id", {})
        
        assert "Critical validation failures" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_tier2_validates_methodology_compliance(self, mock_repositories, mock_command):
        """Test that Tier 2 validates methodology compliance and fails with invalid data."""
        # Set commodity to force tier2 to attempt analysis
        mock_command.commodity_ids = ["wheat"]
        
        # Mock commodity object
        mock_commodity = MagicMock()
        mock_commodity.code = "wheat"
        mock_commodity.name = "Wheat"
        mock_commodity.category = "food"
        mock_commodity.standard_unit = "kg"
        mock_repositories['price_repo'].find_commodity_by_code.return_value = mock_commodity
        
        # Mock markets 
        mock_markets = []
        for i in range(2):  # Need at least 2 markets for tier2 pairs
            mock_market = MagicMock()
            mock_market.market_id = MagicMock(value=f"market_{i+1}")
            mock_market.name = f"Test Market {i+1}"
            mock_market.governorate = "Aden"  # Valid governorate
            mock_market.district = "Test District"
            # Place markets close together (within 300km) for pairing
            mock_market.coordinates = MagicMock(latitude=12.7855 + i*0.1, longitude=45.0187 + i*0.1)
            mock_market.market_type = MagicMock(value="retail")
            mock_market.is_accessible = True
            mock_markets.append(mock_market)
        
        mock_repositories['market_repo'].find_all.return_value = mock_markets
        mock_repositories['market_repo'].find_by_ids.return_value = mock_markets
        
        # Mock observations that create pairs but lack USD prices (methodology violation)
        observations = []
        for i in range(35):  # Sufficient observations to create pairs
            mock_obs = MagicMock()
            mock_obs.observed_date = datetime.now() + timedelta(days=i)  # Different dates for proper merging
            mock_obs.price = MagicMock(amount=1000.0 + i)
            observations.append(mock_obs)
        
        # Return observations for each market (creates valid pairs but lacks USD prices)
        mock_repositories['price_repo'].find_by_market_and_commodity.return_value = observations
        
        runner = Tier2Runner(**mock_repositories)
        
        # Should raise methodology validation error due to missing USD prices/currency zones
        with pytest.raises(MethodologyViolation) as exc_info:
            await runner.run(mock_command, "test_analysis_id", {})
        
        # Check error message indicates methodology validation failure
        assert "Critical validation failures" in str(exc_info.value)


class TestTier3ValidationEnforcement:
    """Test methodology validation enforcement in Tier 3 runner."""
    
    @pytest.mark.asyncio
    async def test_tier3_validates_panel_data(self, mock_repositories, mock_command):
        """Test that Tier 3 validates panel data before analysis and fails when markets lack governorate."""
        # Modify market to have missing governorate (needed for currency zone classification)
        mock_market = MagicMock()
        mock_market.market_id = MagicMock(value="market_1")
        mock_market.name = "Test Market"
        mock_market.governorate = None  # Missing governorate prevents USD price creation
        mock_market.district = "Test District"
        mock_market.coordinates = MagicMock(latitude=12.7855, longitude=45.0187)
        mock_market.market_type = MagicMock(value="retail")
        mock_market.is_accessible = True
        
        mock_repositories['market_repo'].find_all.return_value = [mock_market]
        mock_repositories['market_repo'].find_by_ids.return_value = [mock_market]
        
        runner = Tier3Runner(**mock_repositories)
        
        # Mock tier results
        tier1_result = {
            "result": MagicMock(
                model=MagicMock(
                    specification=MagicMock(
                        dependent_variable="log_price",
                        independent_variables=["conflict_intensity"]
                    )
                )
            )
        }
        tier2_results = {}
        
        # Should raise validation error due to missing governorate preventing USD price creation
        with pytest.raises(MethodologyViolation) as exc_info:
            await runner.run(mock_command, "test_analysis_id", tier1_result, tier2_results)
        
        assert "Critical validation failures" in str(exc_info.value)


class TestValidDataScenario:
    """Test scenarios with valid data that should pass validation."""
    
    @pytest.mark.asyncio
    async def test_valid_data_structure(self):
        """Test the structure of valid data that passes validation."""
        # Create sample valid data
        valid_data = pd.DataFrame({
            'market_id': ['market_1'] * 10,
            'date': pd.date_range('2023-01-01', periods=10),
            'commodity': ['wheat'] * 10,
            'price_yer': [1000, 1010, 1020, 1030, 1040, 1050, 1060, 1070, 1080, 1090],
            'price_usd': [1.87, 1.89, 1.91, 1.93, 1.94, 1.96, 1.98, 2.00, 2.02, 2.04],
            'exchange_rate_used': [535.0] * 10,
            'currency_zone': ['HOUTHI'] * 10,
            'governorate': ['Sana\'a'] * 10
        })
        
        # Verify required columns exist
        required_cols = ['price_usd', 'price_yer', 'exchange_rate_used', 'currency_zone']
        for col in required_cols:
            assert col in valid_data.columns
        
        # Verify USD conversion is correct
        assert np.allclose(
            valid_data['price_usd'], 
            valid_data['price_yer'] / valid_data['exchange_rate_used'],
            rtol=0.01
        )
        
        # Verify exchange rates are in valid range
        assert (valid_data['exchange_rate_used'] >= 100).all()
        assert (valid_data['exchange_rate_used'] <= 3000).all()
        
        # Verify currency zones are valid
        valid_zones = {'HOUTHI', 'GOVERNMENT', 'CONTESTED'}
        assert set(valid_data['currency_zone'].unique()).issubset(valid_zones)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])