#!/usr/bin/env python3
"""Test zone classification mapping."""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.processors.currency_zone_classifier import CurrencyZoneClassifier
from src.core.domain.market.currency_zones import CurrencyZone


def test_zone_mapping():
    """Test that zone name mapping works correctly."""
    print("=" * 80)
    print("Testing Zone Name Mapping")
    print("=" * 80)
    
    classifier = CurrencyZoneClassifier()
    
    # Test cases: (input_name, expected_zone)
    test_cases = [
        # Direct mappings
        ('DFA', CurrencyZone.HOUTHI),
        ('IRG', CurrencyZone.GOVERNMENT),
        ('De Facto Authority', CurrencyZone.HOUTHI),
        ('Internationally Recognized Government', CurrencyZone.GOVERNMENT),
        ('Contested', CurrencyZone.CONTESTED),
        
        # Variations
        ('<PERSON><PERSON><PERSON>', CurrencyZone.HOUTHI),
        ('Government', CurrencyZone.GOVERNMENT),
        ('STC', CurrencyZone.GOVERNMENT),
        
        # Fuzzy matches
        ('DFA controlled', CurrencyZone.HOUTHI),
        ('IRG areas', CurrencyZone.GOVERNMENT),
        ('de facto authorities', CurrencyZone.HOUTHI),
        
        # Unknown/null
        (None, CurrencyZone.UNKNOWN),
        ('Unknown String', CurrencyZone.UNKNOWN),
        ('', CurrencyZone.UNKNOWN)
    ]
    
    passed = 0
    failed = 0
    
    print("\nRunning test cases:")
    for input_name, expected in test_cases:
        result = classifier.standardize_zone_name(input_name)
        status = "✓" if result == expected else "✗"
        
        if result == expected:
            passed += 1
        else:
            failed += 1
            
        print(f"{status} Input: '{input_name}' -> Expected: {expected.value}, Got: {result.value}")
    
    print("\n" + "=" * 80)
    print(f"RESULTS: {passed} passed, {failed} failed")
    print("=" * 80)
    
    if failed == 0:
        print("\n✓ SUCCESS: All zone mappings work correctly!")
    else:
        print("\n✗ FAILURE: Some zone mappings failed!")
        
    return failed == 0


def test_governorate_classification():
    """Test governorate-based classification."""
    print("\n" + "=" * 80)
    print("Testing Governorate-Based Classification")
    print("=" * 80)
    
    classifier = CurrencyZoneClassifier()
    
    test_governorates = [
        # Northern (Houthi)
        ("Sana'a", CurrencyZone.HOUTHI),
        ("Sa'ada", CurrencyZone.HOUTHI),
        ("Amran", CurrencyZone.HOUTHI),
        ("Dhamar", CurrencyZone.HOUTHI),
        
        # Southern (Government)
        ("Aden", CurrencyZone.GOVERNMENT),
        ("Lahj", CurrencyZone.GOVERNMENT),
        ("Hadramaut", CurrencyZone.GOVERNMENT),
        ("Socotra", CurrencyZone.GOVERNMENT),
        
        # Contested
        ("Taiz", CurrencyZone.CONTESTED),
        ("Marib", CurrencyZone.CONTESTED),
        ("Al Hudaydah", CurrencyZone.CONTESTED),
        
        # Unknown
        ("Random Place", CurrencyZone.UNKNOWN)
    ]
    
    print("\nTesting governorate classifications:")
    for governorate, expected in test_governorates:
        result = classifier._classify_by_governorate(governorate)
        status = "✓" if result == expected else "✗"
        print(f"{status} {governorate:20s} -> Expected: {expected.value:12s}, Got: {result.value}")
    
    print("\n✓ Governorate classification tested")


if __name__ == "__main__":
    # Run tests
    success = test_zone_mapping()
    test_governorate_classification()
    
    if success:
        print("\n✓ ALL TESTS PASSED!")
    else:
        print("\n✗ SOME TESTS FAILED!")
        sys.exit(1)