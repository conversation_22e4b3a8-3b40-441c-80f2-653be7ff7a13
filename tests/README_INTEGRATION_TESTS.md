# Documentation Integration Test Suite

## Overview

The `test_documentation_integration.py` file contains a comprehensive test suite that validates the integrity and consistency of the research methodology documentation package.

## Test Categories

### 1. Pre-Analysis Plan Lock (`test_pre_analysis_lock`)
- Verifies the `.pre-analysis-plan-lock` file exists
- Checks for required components (VERSION, LOCK_DATE, file list)
- Validates that all locked files exist

### 2. Forbidden Phrases (`test_forbidden_phrases`)
- Scans all documentation for predetermined conclusion phrases
- Flags usage of terms like "significant market integration", "strong evidence", etc.
- Reports file, line number, and context for violations

### 3. Hypothesis Format (`test_hypothesis_format`)
- Ensures all 10 hypotheses (H1-H10) are properly formatted
- Verifies each hypothesis has:
  - Statistical Specification
  - Falsifiable Predictions
  - Required Data sections

### 4. Results Templates (`test_results_templates`)
- Confirms results files use template markers, not hardcoded values
- Checks for presence of template patterns like `[EFFECT SIZE: ...]`
- Detects hardcoded statistical values (p-values, coefficients, R²)

### 5. Cross-References (`test_cross_references`)
- Validates all internal markdown links point to existing files
- Skips external links and anchor-only links
- Reports broken references with source and target information

### 6. Methodology Consistency (`test_methodology_consistency`)
- Checks consistent usage of key methodology terms
- Ensures canonical forms are used (e.g., "Three-tier" not "three-tier")
- Allows some variation but flags excessive inconsistencies

### 7. Required Sections (`test_required_sections`)
- Verifies key documents contain expected sections
- Checks structure of:
  - Pre-Analysis Plan
  - Methodological Transparency
  - Null Results Template

### 8. Statistical Testing Plan (`test_statistical_plan`)
- Confirms statistical testing plan exists
- Validates presence of key components:
  - Multiple Testing Correction
  - Power Analysis
  - Robustness Checks
  - Sample Size Requirements

### 9. Alternative Explanations (`test_alternatives`)
- Checks for comprehensive alternative explanation documentation
- Verifies files exist for:
  - Selection bias
  - Measurement error
  - Omitted variables
  - Reverse causality

## Running the Tests

### Option 1: Using pytest
```bash
pytest tests/test_documentation_integration.py -v
```

### Option 2: Direct execution
```bash
python tests/test_documentation_integration.py
```

### Option 3: Using the runner script
```bash
python scripts/run_documentation_tests.py
```

## Interpreting Results

- **✓** indicates a passing test
- **✗** indicates a failing test with details about the failure
- Tests report specific violations to help with remediation

## Expected Test Results

Based on the current state of the documentation:

1. **Pre-analysis lock**: Should pass if lock file exists with correct format
2. **Forbidden phrases**: May find some violations in templates/examples
3. **Hypothesis format**: Should pass if all hypotheses properly structured
4. **Results templates**: Should pass for main results files
5. **Cross-references**: May find broken links from refactoring
6. **Methodology consistency**: Minor variations expected
7. **Required sections**: Should pass for key documents
8. **Statistical plan**: Should pass if plan properly documented
9. **Alternative explanations**: Should pass if directory populated

## Continuous Integration

These tests should be run:
- Before any major documentation updates
- As part of the CI/CD pipeline
- During documentation reviews
- Before publication or external sharing

## Maintenance

The test suite should be updated when:
- New forbidden phrases are identified
- Documentation structure changes
- New required sections are added
- Template formats are modified