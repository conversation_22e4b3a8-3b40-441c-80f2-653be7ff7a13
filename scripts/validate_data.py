#!/usr/bin/env python3
"""Validate data quality and exchange rate application.

This script validates:
1. Exchange rate extraction from WFP data
2. USD price calculations
3. Panel data completeness
4. Coverage metrics
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.validation.methodology_validator import MethodologyValidator


def validate_exchange_rates():
    """Validate exchange rate extraction from WFP data."""
    print("=" * 80)
    print("Exchange Rate Validation")
    print("=" * 80)
    
    # Load WFP raw data
    wfp_path = project_root / 'data/raw/hdx/wfp_food_prices_wfp-food-prices-for-yemen.csv'
    wfp_df = pd.read_csv(wfp_path)
    
    # Extract exchange rate observations
    er_data = wfp_df[wfp_df['commodity'].str.contains('Exchange rate', case=False, na=False)].copy()
    print(f"\nFound {len(er_data):,} exchange rate observations")
    
    if len(er_data) > 0:
        er_data['date'] = pd.to_datetime(er_data['date'])
        er_data['price'] = pd.to_numeric(er_data['price'], errors='coerce')
        
        # Show summary by governorate
        print("\nExchange rates by governorate:")
        summary = er_data.groupby('admin1')['price'].agg(['mean', 'min', 'max', 'count'])
        print(summary.round(0))
        
        # Check for North/South pattern
        north_govs = ['Sana\'a', 'Amanat Al Asimah', 'Dhamar', 'Al Mahwit', 'Sa\'ada']
        south_govs = ['Aden', 'Abyan', 'Lahj', 'Al Dhale\'e', 'Hadramaut']
        
        north_rate = er_data[er_data['admin1'].isin(north_govs)]['price'].mean()
        south_rate = er_data[er_data['admin1'].isin(south_govs)]['price'].mean()
        
        print(f"\nNorth average: {north_rate:.0f} YER/USD")
        print(f"South average: {south_rate:.0f} YER/USD")
        print(f"Ratio (South/North): {south_rate/north_rate:.2f}x")
    
    return len(er_data) > 0


def validate_usd_conversions():
    """Validate USD price calculations."""
    print("\n" + "=" * 80)
    print("USD Conversion Validation")
    print("=" * 80)
    
    # Check if corrected panel exists
    panel_path = Path("data/processed/integrated_panel/yemen_integrated_balanced_panel_corrected.parquet")
    
    if panel_path.exists():
        df = pd.read_parquet(panel_path)
        
        # Check for exchange rate columns
        if 'exchange_rate_used' in df.columns:
            print(f"\n✓ Exchange rates properly integrated")
            print(f"  Range: {df['exchange_rate_used'].min():.0f} - {df['exchange_rate_used'].max():.0f}")
            
            # Check by zone
            if 'currency_zone' in df.columns:
                zone_rates = df.groupby('currency_zone')['exchange_rate_used'].mean()
                print("\nAverage rates by zone:")
                for zone, rate in zone_rates.items():
                    print(f"  {zone}: {rate:.0f} YER/USD")
        
        # Check USD price correction
        if 'usdprice_wfp_original' in df.columns and 'usdprice' in df.columns:
            discrepancy = abs(df['usdprice'] - df['usdprice_wfp_original']) / df['usdprice_wfp_original']
            print(f"\n✓ USD prices corrected")
            print(f"  Average correction: {discrepancy.mean()*100:.1f}%")
            print(f"  WFP average: ${df['usdprice_wfp_original'].mean():.2f}")
            print(f"  Corrected average: ${df['usdprice'].mean():.2f}")
    else:
        print("✗ Corrected panel not found. Run create_panel.py first.")
    
    return panel_path.exists()


def validate_panel_completeness():
    """Validate panel data completeness."""
    print("\n" + "=" * 80)
    print("Panel Completeness Validation")
    print("=" * 80)
    
    # Use methodology validator
    validator = MethodologyValidator()
    
    # Check multiple panel files
    panel_files = [
        "data/processed/integrated_panel/yemen_integrated_balanced_panel_corrected.parquet",
        "data/processed/integrated_panel/yemen_integrated_balanced_panel.parquet",
        "data/processed/modeling_ready/yemen_panel_ready.parquet"
    ]
    
    for panel_file in panel_files:
        path = Path(panel_file)
        if path.exists():
            print(f"\nValidating: {path.name}")
            df = pd.read_parquet(path)
            
            # Basic stats
            print(f"  Shape: {df.shape}")
            print(f"  Date range: {df['date'].min()} to {df['date'].max()}")
            
            # Check for required columns
            required_cols = ['price', 'usdprice', 'currency_zone', 'exchange_rate_used']
            missing_cols = [col for col in required_cols if col not in df.columns]
            
            if missing_cols:
                print(f"  ✗ Missing columns: {missing_cols}")
            else:
                print(f"  ✓ All required columns present")
                
                # Run methodology validation
                is_valid, report = validator.validate_analysis_inputs(
                    observations=df.to_dict('records')[:100],  # Sample for speed
                    analysis_type="panel_analysis"
                )
                
                if is_valid:
                    print(f"  ✓ Methodology validation passed")
                else:
                    print(f"  ✗ Methodology validation failed: {report.critical_failures}")
    
    return True


def main():
    """Run all validations."""
    print("Running comprehensive data validation...\n")
    
    # Run validations
    er_valid = validate_exchange_rates()
    usd_valid = validate_usd_conversions()
    panel_valid = validate_panel_completeness()
    
    # Summary
    print("\n" + "=" * 80)
    print("VALIDATION SUMMARY")
    print("=" * 80)
    print(f"Exchange rate extraction: {'✓ PASS' if er_valid else '✗ FAIL'}")
    print(f"USD price correction: {'✓ PASS' if usd_valid else '✗ FAIL'}")
    print(f"Panel completeness: {'✓ PASS' if panel_valid else '✗ FAIL'}")
    
    if all([er_valid, usd_valid, panel_valid]):
        print("\n✓ All validations passed - data ready for analysis!")
    else:
        print("\n✗ Some validations failed - review and fix issues")
    
    return all([er_valid, usd_valid, panel_valid])


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)