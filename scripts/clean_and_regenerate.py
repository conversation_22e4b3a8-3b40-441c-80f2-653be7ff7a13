#!/usr/bin/env python3
"""Clean all results and panel data, then regenerate with correct exchange rates.

This script:
1. Deletes all existing panel data and results
2. Runs the complete data pipeline with proper exchange rate handling
3. Validates the new panel meets methodology requirements
"""

import shutil
from pathlib import Path
import subprocess
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def clean_directories():
    """Delete all processed data and results."""
    print("🗑️  Cleaning existing data and results...")
    
    # Directories to clean
    dirs_to_clean = [
        "data/processed/integrated_panel",
        "data/processed/modeling_ready",
        "data/interim",
        "results",
        "validation_results",
        "test_output"
    ]
    
    for dir_path in dirs_to_clean:
        path = Path(dir_path)
        if path.exists():
            print(f"  Deleting {path}...")
            shutil.rmtree(path)
            path.mkdir(parents=True, exist_ok=True)
        else:
            print(f"  Creating {path}...")
            path.mkdir(parents=True, exist_ok=True)
    
    print("✓ Cleanup complete\n")


def run_pipeline():
    """Run the complete data pipeline."""
    print("🔄 Running complete data pipeline...")
    
    # Run the final panel creation script
    result = subprocess.run([
        sys.executable,
        "scripts/create_final_panel.py"
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print("❌ Pipeline failed!")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)
        return False
    
    print("✓ Pipeline completed successfully\n")
    return True


def validate_results():
    """Validate the generated panel data."""
    print("🔍 Validating results...")
    
    panel_path = Path("data/processed/integrated_panel/yemen_integrated_balanced_panel.parquet")
    
    if not panel_path.exists():
        print("❌ Panel data not found!")
        return False
    
    # Check file size
    size_mb = panel_path.stat().st_size / (1024 * 1024)
    print(f"  Panel size: {size_mb:.1f} MB")
    
    # Load and check basic statistics
    try:
        import pandas as pd
        df = pd.read_parquet(panel_path)
        
        print(f"  Total observations: {len(df):,}")
        print(f"  Date range: {df['date'].min()} to {df['date'].max()}")
        print(f"  Markets: {df['market'].nunique()}")
        print(f"  Commodities: {df['commodity'].nunique()}")
        
        # Check exchange rates
        if 'exchange_rate_used' in df.columns:
            zone_stats = df.groupby('currency_zone')['exchange_rate_used'].agg(['mean', 'min', 'max'])
            print("\n  Exchange rates by zone:")
            for zone, stats in zone_stats.iterrows():
                print(f"    {zone}: mean={stats['mean']:.0f}, range={stats['min']:.0f}-{stats['max']:.0f}")
        
        # Check USD prices
        if 'usdprice' in df.columns:
            usd_coverage = df['usdprice'].notna().sum() / len(df)
            print(f"\n  USD price coverage: {usd_coverage:.1%}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error validating panel: {e}")
        return False


def main():
    """Main execution."""
    print("=" * 80)
    print("Yemen Market Integration - Clean and Regenerate Panel Data")
    print("=" * 80)
    print()
    
    # Step 1: Clean
    clean_directories()
    
    # Step 2: Regenerate
    if not run_pipeline():
        print("\n❌ Pipeline failed. Please check the errors above.")
        return 1
    
    # Step 3: Validate
    if not validate_results():
        print("\n❌ Validation failed. Please check the panel data.")
        return 1
    
    print("\n" + "=" * 80)
    print("✅ SUCCESS: Panel data regenerated with correct exchange rates!")
    print("=" * 80)
    print("\nNext steps:")
    print("1. Review exchange rate statistics above")
    print("2. Check that HOUTHI zones show ~525 YER/USD")
    print("3. Check that GOVERNMENT zones show ~1935 YER/USD")
    print("4. Run analysis with: python scripts/run_analysis.py")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())