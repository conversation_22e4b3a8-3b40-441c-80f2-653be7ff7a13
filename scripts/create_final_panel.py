#!/usr/bin/env python3
"""Create final integrated panel dataset with proper exchange rate handling.

This script creates the production-ready panel dataset with:
1. Correct exchange rates extracted from WFP commodity data
2. Proper currency zone classification (DFA→HOUTHI, IRG→GOVERNMENT)
3. Zone-specific USD price calculations
4. Integration of conflict and control zone data
5. Full methodology validation
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path
import pandas as pd
import numpy as np
import sys
import os
import json

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.infrastructure.processors.currency_aware_wfp_processor import CurrencyAwareWFPProcessor
from src.infrastructure.processors.currency_zone_classifier import CurrencyZoneClassifier
from src.infrastructure.processors.panel_builder import PanelBuilder
from src.core.domain.market.entities import Market, PriceObservation
from src.core.domain.market.value_objects import MarketId, Commodity, Price, Currency
from src.core.domain.market.currency_zones import CurrencyZone
from src.core.validation.methodology_validator import MethodologyValidator
from src.core.utils.logging import get_logger

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = get_logger(__name__)


class IntegratedPanelCreator:
    """Creates integrated panel dataset with proper exchange rate handling."""
    
    def __init__(self):
        self.zone_classifier = CurrencyZoneClassifier()
        self.methodology_validator = MethodologyValidator()
        self.logger = logger
        
    def extract_exchange_rates_from_wfp(self, wfp_path: Path) -> pd.DataFrame:
        """Extract exchange rate data from WFP commodity data."""
        self.logger.info(f"Extracting exchange rates from {wfp_path}")
        
        # Load WFP data
        df = pd.read_csv(wfp_path) if wfp_path.suffix == '.csv' else pd.read_parquet(wfp_path)
        
        # Filter for exchange rate commodity
        er_df = df[df['commodity'].str.contains('Exchange rate', case=False, na=False)].copy()
        
        if len(er_df) == 0:
            raise ValueError("No exchange rate data found in WFP commodity data")
        
        self.logger.info(f"Found {len(er_df)} exchange rate observations")
        
        # Clean and process
        er_df['date'] = pd.to_datetime(er_df['date'])
        er_df['exchange_rate'] = pd.to_numeric(er_df['price'], errors='coerce')
        er_df = er_df[er_df['exchange_rate'] > 0]
        
        # Standardize governorate names for zone classification
        governorate_mappings = {
            "Al Dhale'e": "Ad Dale'",
            "Al Hudaydah": "Al Hodeidah",
            "Amanat Al Asimah": "Sana'a City",
            "Hadramaut": "Hadramawt",
            "Sa'ada": "Sa'dah",
            "Taizz": "Ta'iz"
        }
        er_df['governorate'] = er_df['admin1'].map(lambda x: governorate_mappings.get(x, x))
        
        # Apply zone classification
        er_df['currency_zone'] = er_df['governorate'].apply(
            lambda gov: self.zone_classifier._classify_by_governorate(gov).value
        )
        
        # Log statistics by zone
        zone_stats = er_df.groupby('currency_zone')['exchange_rate'].agg(['mean', 'min', 'max', 'count'])
        self.logger.info("Exchange rate statistics by zone:")
        for zone, stats in zone_stats.iterrows():
            self.logger.info(f"  {zone}: mean={stats['mean']:.1f}, range={stats['min']:.0f}-{stats['max']:.0f}, n={stats['count']}")
        
        return er_df[['governorate', 'date', 'exchange_rate', 'currency_zone', 'market']]
    
    def create_panel_with_exchange_rates(
        self, 
        wfp_path: Path,
        start_date: str = "2019-01-01",
        end_date: str = "2025-03-01",
        commodities: list = None
    ) -> pd.DataFrame:
        """Create integrated panel with proper exchange rates."""
        
        if commodities is None:
            commodities = [
                "Wheat flour", "Rice", "Sugar", "Vegetable oil",
                "Diesel", "Petrol", "Red beans", "Tomatoes"
            ]
        
        # Step 1: Extract exchange rates
        exchange_rates = self.extract_exchange_rates_from_wfp(wfp_path)
        
        # Step 2: Load price data (excluding exchange rate commodity)
        self.logger.info("Loading price data")
        df = pd.read_csv(wfp_path) if wfp_path.suffix == '.csv' else pd.read_parquet(wfp_path)
        price_df = df[~df['commodity'].str.contains('Exchange rate', case=False, na=False)].copy()
        
        # Filter commodities and dates
        price_df = price_df[price_df['commodity'].isin(commodities)]
        price_df['date'] = pd.to_datetime(price_df['date'])
        price_df = price_df[(price_df['date'] >= start_date) & (price_df['date'] <= end_date)]
        
        # Clean prices
        price_df['price'] = pd.to_numeric(price_df['price'], errors='coerce')
        price_df = price_df[price_df['price'] > 0]
        
        # Standardize governorate names
        governorate_mappings = {
            "Al Dhale'e": "Ad Dale'",
            "Al Hudaydah": "Al Hodeidah",
            "Amanat Al Asimah": "Sana'a City",
            "Hadramaut": "Hadramawt",
            "Sa'ada": "Sa'dah",
            "Taizz": "Ta'iz"
        }
        price_df['governorate'] = price_df['admin1'].map(lambda x: governorate_mappings.get(x, x))
        
        # Add currency zones to prices
        price_df['currency_zone'] = price_df['governorate'].apply(
            lambda gov: self.zone_classifier._classify_by_governorate(gov).value
        )
        
        self.logger.info(f"Processing {len(price_df)} price observations")
        
        # Step 3: Create balanced panel structure
        markets = price_df['market'].unique()
        commodities_in_data = price_df['commodity'].unique()
        date_range = pd.date_range(start=start_date, end=end_date, freq='MS')
        
        # Create all combinations
        from itertools import product
        panel_index = pd.DataFrame(
            list(product(markets, commodities_in_data, date_range)),
            columns=['market', 'commodity', 'date']
        )
        
        # Merge with price data
        panel_df = panel_index.merge(
            price_df,
            on=['market', 'commodity', 'date'],
            how='left'
        )
        
        # Step 4: Integrate exchange rates
        self.logger.info("Integrating exchange rates")
        
        # First try direct market-date match
        panel_df = panel_df.merge(
            exchange_rates[['market', 'date', 'exchange_rate']].rename(columns={'exchange_rate': 'exchange_rate_market'}),
            on=['market', 'date'],
            how='left'
        )
        
        # Then try governorate-date match for missing values
        gov_rates = exchange_rates.groupby(['governorate', 'date'])['exchange_rate'].mean().reset_index()
        panel_df = panel_df.merge(
            gov_rates.rename(columns={'exchange_rate': 'exchange_rate_gov'}),
            on=['governorate', 'date'],
            how='left'
        )
        
        # Finally use zone averages for remaining missing
        zone_rates = exchange_rates.groupby(['currency_zone', 'date'])['exchange_rate'].mean().reset_index()
        panel_df = panel_df.merge(
            zone_rates.rename(columns={'exchange_rate': 'exchange_rate_zone'}),
            on=['currency_zone', 'date'],
            how='left'
        )
        
        # Combine exchange rates (prioritize market > governorate > zone)
        panel_df['exchange_rate_used'] = panel_df['exchange_rate_market'].fillna(
            panel_df['exchange_rate_gov']
        ).fillna(
            panel_df['exchange_rate_zone']
        )
        
        # Drop intermediate columns
        panel_df.drop(['exchange_rate_market', 'exchange_rate_gov', 'exchange_rate_zone'], axis=1, inplace=True)
        
        # Step 5: Calculate proper USD prices
        panel_df['price_usd_calculated'] = panel_df['price'] / panel_df['exchange_rate_used']
        
        # Compare with WFP's USD price if available
        if 'usdprice' in panel_df.columns:
            panel_df['usdprice_wfp_original'] = panel_df['usdprice']
            mask = panel_df['price_usd_calculated'].notna() & panel_df['usdprice_wfp_original'].notna()
            panel_df.loc[mask, 'usd_discrepancy_pct'] = abs(
                panel_df.loc[mask, 'price_usd_calculated'] - panel_df.loc[mask, 'usdprice_wfp_original']
            ) / panel_df.loc[mask, 'usdprice_wfp_original'] * 100
            
            avg_discrepancy = panel_df.loc[mask, 'usd_discrepancy_pct'].mean()
            self.logger.info(f"Average USD price discrepancy vs WFP: {avg_discrepancy:.1f}%")
        
        # Use our calculated USD price
        panel_df['usdprice'] = panel_df['price_usd_calculated']
        
        # Step 6: Add derived features
        panel_df['log_price'] = np.log(panel_df['price'].replace(0, np.nan))
        panel_df['log_usdprice'] = np.log(panel_df['usdprice'].replace(0, np.nan))
        
        # Sort for time series operations
        panel_df = panel_df.sort_values(['market', 'commodity', 'date'])
        
        # Price changes
        panel_df['price_change_pct'] = panel_df.groupby(['market', 'commodity'])['price'].pct_change()
        panel_df['log_price_change'] = panel_df.groupby(['market', 'commodity'])['log_price'].diff()
        
        # Time features
        panel_df['year'] = panel_df['date'].dt.year
        panel_df['month'] = panel_df['date'].dt.month
        panel_df['quarter'] = panel_df['date'].dt.quarter
        
        # Step 7: Calculate coverage and quality metrics
        total_possible = len(panel_index)
        total_with_prices = panel_df['price'].notna().sum()
        total_with_exchange_rates = panel_df['exchange_rate_used'].notna().sum()
        total_with_usd_prices = panel_df['usdprice'].notna().sum()
        
        coverage_metrics = {
            'total_observations': len(panel_df),
            'observations_with_prices': total_with_prices,
            'observations_with_exchange_rates': total_with_exchange_rates,
            'observations_with_usd_prices': total_with_usd_prices,
            'price_coverage_pct': (total_with_prices / total_possible) * 100,
            'exchange_rate_coverage_pct': (total_with_exchange_rates / total_with_prices) * 100 if total_with_prices > 0 else 0,
            'usd_price_coverage_pct': (total_with_usd_prices / total_possible) * 100
        }
        
        self.logger.info("Coverage metrics:")
        for metric, value in coverage_metrics.items():
            if 'pct' in metric:
                self.logger.info(f"  {metric}: {value:.1f}%")
            else:
                self.logger.info(f"  {metric}: {value:,}")
        
        return panel_df, coverage_metrics
    
    def validate_panel(self, panel_df: pd.DataFrame) -> tuple:
        """Validate panel against methodology requirements."""
        self.logger.info("Validating panel against methodology requirements")
        
        # Run methodology validation
        is_valid, report = self.methodology_validator.validate_analysis_inputs(
            observations=panel_df,
            analysis_type="panel_analysis"
        )
        
        if is_valid:
            self.logger.info("✓ Panel passes all methodology validation checks")
        else:
            self.logger.warning("✗ Panel failed methodology validation:")
            for failure in report.critical_failures:
                self.logger.warning(f"  - {failure}")
        
        # Additional validation: Check exchange rate ranges
        zone_stats = panel_df.groupby('currency_zone')['exchange_rate_used'].agg(['mean', 'min', 'max', 'count'])
        
        validation_results = {
            'methodology_valid': is_valid,
            'critical_failures': report.critical_failures if not is_valid else [],
            'warnings': report.warnings,
            'exchange_rate_stats': zone_stats.to_dict(),
            'zones_present': panel_df['currency_zone'].unique().tolist()
        }
        
        return is_valid, validation_results


async def main():
    """Main execution function."""
    logger.info("=" * 80)
    logger.info("Creating Final Integrated Panel Dataset with Correct Exchange Rates")
    logger.info("=" * 80)
    
    # Configuration
    wfp_path = Path("data/raw/hdx/wfp_food_prices_wfp-food-prices-for-yemen.csv")
    output_dir = Path("data/processed/integrated_panel")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Check if WFP data exists
    if not wfp_path.exists():
        logger.error(f"WFP data not found at {wfp_path}")
        logger.info("Please run: python scripts/download_data.py")
        return
    
    try:
        # Create panel
        creator = IntegratedPanelCreator()
        panel_df, coverage_metrics = creator.create_panel_with_exchange_rates(
            wfp_path=wfp_path,
            start_date="2019-01-01",
            end_date="2025-03-01"
        )
        
        # Validate panel
        is_valid, validation_results = creator.validate_panel(panel_df)
        
        # Save panel
        output_path = output_dir / "yemen_integrated_balanced_panel.parquet"
        panel_df.to_parquet(output_path, index=False)
        logger.info(f"Saved panel to {output_path}")
        
        # Save CSV for inspection
        csv_path = output_dir / "yemen_integrated_balanced_panel.csv"
        sample_df = panel_df.head(10000)  # Save sample for inspection
        sample_df.to_csv(csv_path, index=False)
        logger.info(f"Saved CSV sample to {csv_path}")
        
        # Save metadata
        metadata = {
            'created_at': datetime.now().isoformat(),
            'coverage_metrics': coverage_metrics,
            'validation_results': validation_results,
            'shape': {'rows': len(panel_df), 'columns': len(panel_df.columns)},
            'date_range': {
                'start': str(panel_df['date'].min()),
                'end': str(panel_df['date'].max())
            },
            'markets': panel_df['market'].nunique(),
            'commodities': panel_df['commodity'].unique().tolist()
        }
        
        with open(output_dir / "panel_metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
        
        logger.info("\n" + "=" * 80)
        logger.info("✓ PANEL CREATION COMPLETED SUCCESSFULLY")
        logger.info("=" * 80)
        
        # Display exchange rate summary
        logger.info("\nExchange Rate Summary by Zone:")
        zone_stats = panel_df.groupby('currency_zone')['exchange_rate_used'].agg(['mean', 'min', 'max'])
        for zone, stats in zone_stats.iterrows():
            logger.info(f"  {zone}: mean={stats['mean']:.1f}, range={stats['min']:.0f}-{stats['max']:.0f} YER/USD")
        
        # Check if rates match expectations
        houthi_mean = zone_stats.loc['HOUTHI', 'mean'] if 'HOUTHI' in zone_stats.index else 0
        gov_mean = zone_stats.loc['GOVERNMENT', 'mean'] if 'GOVERNMENT' in zone_stats.index else 0
        
        if 500 <= houthi_mean <= 600 and 1500 <= gov_mean <= 2500:
            logger.info("\n✓ Exchange rates are within expected ranges!")
        else:
            logger.warning("\n⚠ Exchange rates may be outside expected ranges")
            logger.warning(f"  Expected: HOUTHI ~525, GOVERNMENT ~1935")
            logger.warning(f"  Actual: HOUTHI {houthi_mean:.0f}, GOVERNMENT {gov_mean:.0f}")
        
    except Exception as e:
        logger.error(f"Panel creation failed: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    asyncio.run(main())