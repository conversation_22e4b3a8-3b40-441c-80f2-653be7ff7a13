#!/usr/bin/env python3
"""Create final panel dataset with correct exchange rate handling.

This script properly merges WFP price and exchange rate data.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import json
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# Zone classification mapping
GOVERNORATE_TO_ZONE = {
    # Houthi-controlled (North)
    "Sana'a": "HOUTHI",
    "Sana'a City": "HOUTHI", 
    "Amanat Al Asimah": "HOUTHI",
    "Sa'dah": "HOUTHI",
    "Sa'ada": "HOUTHI",
    "Hajjah": "HOUTHI",
    "Al Mahwit": "HOUTHI",
    "Dhamar": "HOUTHI",
    "Raymah": "HOUTHI",
    "Ibb": "HOUTHI",
    "Amran": "HOUTHI",
    "<PERSON> Hodeidah": "HOUTHI",
    "Al Hudaydah": "HOUTHI",
    
    # Government-controlled (South)
    "Aden": "GOVERNMENT",
    "Lahj": "GOVERNMENT",
    "Abyan": "GOVERNMENT",
    "Shabwah": "GOVERNMENT",
    "Hadramawt": "GOVERNMENT",
    "Hadramaut": "GOVERNMENT",
    "Al Maharah": "GOVERNMENT",
    "Socotra": "GOVERNMENT",
    "Ad Dale'": "GOVERNMENT",
    "Al Dhale'e": "GOVERNMENT",
    "Marib": "GOVERNMENT",
    "Al Jawf": "GOVERNMENT",
    
    # Contested
    "Ta'iz": "CONTESTED",
    "Taizz": "CONTESTED",
    "Al Bayda": "CONTESTED"
}


def main():
    """Process panel data with correct exchange rates."""
    logger.info("=" * 80)
    logger.info("Creating Final Panel with Correct Exchange Rates")
    logger.info("=" * 80)
    
    # Configuration
    wfp_path = Path("data/raw/hdx/wfp_food_prices_wfp-food-prices-for-yemen.csv")
    output_dir = Path("data/processed/integrated_panel")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if not wfp_path.exists():
        logger.error(f"WFP data not found at {wfp_path}")
        return
    
    # Load WFP data
    logger.info(f"Loading WFP data from {wfp_path}")
    df = pd.read_csv(wfp_path, low_memory=False)
    logger.info(f"Loaded {len(df)} total observations")
    
    # Convert dates to datetime - handle bad date values
    df['date'] = pd.to_datetime(df['date'], errors='coerce')
    
    # Step 1: Extract and process exchange rates
    logger.info("\nExtracting exchange rates from WFP commodity data...")
    er_df = df[df['commodity'].str.contains('Exchange rate', case=False, na=False)].copy()
    logger.info(f"Found {len(er_df)} exchange rate observations")
    
    if len(er_df) > 0:
        # Clean exchange rate data
        er_df['exchange_rate'] = pd.to_numeric(er_df['price'], errors='coerce')
        er_df = er_df[er_df['exchange_rate'] > 0]
        
        # Add currency zones
        er_df['currency_zone'] = er_df['admin1'].map(GOVERNORATE_TO_ZONE).fillna('UNKNOWN')
        
        # Keep necessary columns
        er_df = er_df[['date', 'market', 'admin1', 'currency_zone', 'exchange_rate']]
        
        # Calculate zone statistics
        zone_stats = er_df.groupby('currency_zone')['exchange_rate'].agg(['mean', 'min', 'max', 'count'])
        logger.info("\nExchange rate statistics by zone:")
        for zone, stats in zone_stats.iterrows():
            logger.info(f"  {zone}: mean={stats['mean']:.1f}, range={stats['min']:.0f}-{stats['max']:.0f}, n={stats['count']}")
    
    # Step 2: Process price data
    logger.info("\nProcessing price data...")
    
    # Filter to find matching essential commodities
    essential_patterns = [
        "wheat", "rice", "sugar", "oil", "diesel", "petrol", "beans"
    ]
    
    # Get matching commodities
    all_commodities = df['commodity'].unique()
    matching_commodities = []
    for commodity in all_commodities:
        if commodity and any(pattern in str(commodity).lower() for pattern in essential_patterns):
            matching_commodities.append(commodity)
    
    logger.info(f"Found {len(matching_commodities)} matching commodities")
    
    # Filter prices (exclude exchange rate commodity)
    price_df = df[~df['commodity'].str.contains('Exchange rate', case=False, na=False)].copy()
    price_df = price_df[price_df['commodity'].isin(matching_commodities)]
    
    # Clean data
    price_df = price_df[(price_df['date'] >= '2019-01-01') & (price_df['date'] <= '2025-03-01')]
    price_df['price'] = pd.to_numeric(price_df['price'], errors='coerce')
    price_df = price_df[price_df['price'] > 0]
    
    # Add currency zones
    price_df['currency_zone'] = price_df['admin1'].map(GOVERNORATE_TO_ZONE).fillna('UNKNOWN')
    
    logger.info(f"Filtered to {len(price_df)} price observations")
    logger.info(f"Date range: {price_df['date'].min()} to {price_df['date'].max()}")
    logger.info(f"Markets: {price_df['market'].nunique()}")
    
    # Step 3: Merge exchange rates with price data
    logger.info("\nMerging exchange rates with price data...")
    
    # Method 1: Direct market-date merge
    merged_df = price_df.merge(
        er_df[['date', 'market', 'exchange_rate']],
        on=['date', 'market'],
        how='left',
        suffixes=('', '_market')
    )
    
    # Method 2: For missing, use admin1-date average
    admin1_rates = er_df.groupby(['date', 'admin1'])['exchange_rate'].mean().reset_index()
    merged_df = merged_df.merge(
        admin1_rates,
        on=['date', 'admin1'],
        how='left',
        suffixes=('', '_admin1')
    )
    
    # Method 3: For still missing, use zone-date average
    zone_rates = er_df.groupby(['date', 'currency_zone'])['exchange_rate'].mean().reset_index()
    merged_df = merged_df.merge(
        zone_rates.rename(columns={'exchange_rate': 'exchange_rate_zone'}),
        on=['date', 'currency_zone'],
        how='left'
    )
    
    # Combine exchange rates (prioritize market > admin1 > zone)
    merged_df['exchange_rate_used'] = merged_df['exchange_rate'].fillna(
        merged_df['exchange_rate_admin1']
    ).fillna(
        merged_df['exchange_rate_zone']
    )
    
    # Drop intermediate columns
    cols_to_drop = ['exchange_rate', 'exchange_rate_admin1', 'exchange_rate_zone']
    cols_to_drop = [col for col in cols_to_drop if col in merged_df.columns]
    merged_df.drop(cols_to_drop, axis=1, inplace=True)
    
    # Calculate USD prices
    merged_df['price_usd_calculated'] = merged_df['price'] / merged_df['exchange_rate_used']
    
    # Compare with WFP's USD price if available
    if 'usdprice' in merged_df.columns:
        # Convert WFP USD price to numeric
        merged_df['usdprice_wfp_original'] = pd.to_numeric(merged_df['usdprice'], errors='coerce')
        
        # Only calculate discrepancy where both values are numeric
        mask = (merged_df['price_usd_calculated'].notna() & 
                merged_df['usdprice_wfp_original'].notna() & 
                (merged_df['usdprice_wfp_original'] > 0))
        
        if mask.sum() > 0:
            merged_df.loc[mask, 'usd_discrepancy_pct'] = abs(
                merged_df.loc[mask, 'price_usd_calculated'] - merged_df.loc[mask, 'usdprice_wfp_original']
            ) / merged_df.loc[mask, 'usdprice_wfp_original'] * 100
            
            avg_discrepancy = merged_df.loc[mask, 'usd_discrepancy_pct'].mean()
            logger.info(f"\nAverage USD price discrepancy vs WFP: {avg_discrepancy:.1f}%")
        else:
            logger.info("\nNo valid WFP USD prices found for comparison")
    
    # Use our calculated USD price
    merged_df['usdprice'] = merged_df['price_usd_calculated']
    
    # Step 4: Add derived features
    logger.info("\nAdding derived features...")
    merged_df['log_price'] = np.log(merged_df['price'].replace(0, np.nan))
    merged_df['log_usdprice'] = np.log(merged_df['usdprice'].replace(0, np.nan))
    
    # Sort for time series
    merged_df = merged_df.sort_values(['market', 'commodity', 'date'])
    
    # Price changes
    merged_df['price_change_pct'] = merged_df.groupby(['market', 'commodity'])['price'].pct_change(fill_method=None)
    merged_df['log_price_change'] = merged_df.groupby(['market', 'commodity'])['log_price'].diff()
    
    # Time features
    merged_df['year'] = merged_df['date'].dt.year
    merged_df['month'] = merged_df['date'].dt.month
    merged_df['quarter'] = merged_df['date'].dt.quarter
    
    # Step 5: Calculate coverage
    total_observations = len(merged_df)
    observations_with_prices = merged_df['price'].notna().sum()
    observations_with_exchange_rates = merged_df['exchange_rate_used'].notna().sum()
    observations_with_usd_prices = merged_df['usdprice'].notna().sum()
    
    logger.info("\nCoverage statistics:")
    logger.info(f"  Total observations: {total_observations:,}")
    logger.info(f"  With prices: {observations_with_prices:,} ({observations_with_prices/total_observations*100:.1f}%)")
    logger.info(f"  With exchange rates: {observations_with_exchange_rates:,} ({observations_with_exchange_rates/total_observations*100:.1f}%)")
    logger.info(f"  With USD prices: {observations_with_usd_prices:,} ({observations_with_usd_prices/total_observations*100:.1f}%)")
    
    # Step 6: Save outputs
    output_path = output_dir / "yemen_integrated_balanced_panel.parquet"
    merged_df.to_parquet(output_path, index=False)
    logger.info(f"\nSaved panel to {output_path}")
    
    # Save CSV sample
    csv_path = output_dir / "yemen_integrated_balanced_panel_sample.csv"
    merged_df.head(10000).to_csv(csv_path, index=False)
    logger.info(f"Saved CSV sample to {csv_path}")
    
    # Save metadata
    metadata = {
        'created_at': datetime.now().isoformat(),
        'total_observations': int(total_observations),
        'observations_with_prices': int(observations_with_prices),
        'observations_with_exchange_rates': int(observations_with_exchange_rates),
        'observations_with_usd_prices': int(observations_with_usd_prices),
        'exchange_rate_coverage_pct': float(observations_with_exchange_rates / total_observations * 100),
        'usd_price_coverage_pct': float(observations_with_usd_prices / total_observations * 100),
        'date_range': {
            'start': str(merged_df['date'].min()),
            'end': str(merged_df['date'].max())
        },
        'markets': int(merged_df['market'].nunique()),
        'commodities': merged_df['commodity'].unique().tolist()
    }
    
    with open(output_dir / "panel_metadata.json", 'w') as f:
        json.dump(metadata, f, indent=2)
    
    # Final exchange rate summary
    if 'exchange_rate_used' in merged_df.columns:
        logger.info("\n" + "=" * 80)
        logger.info("FINAL EXCHANGE RATE SUMMARY")
        logger.info("=" * 80)
        
        final_stats = merged_df[merged_df['exchange_rate_used'].notna()].groupby('currency_zone')['exchange_rate_used'].agg(['mean', 'min', 'max', 'count'])
        for zone, stats in final_stats.iterrows():
            logger.info(f"{zone}: mean={stats['mean']:.1f}, range={stats['min']:.0f}-{stats['max']:.0f} YER/USD, n={stats['count']}")
        
        # Check if rates are reasonable
        if 'HOUTHI' in final_stats.index and 'GOVERNMENT' in final_stats.index:
            houthi_mean = final_stats.loc['HOUTHI', 'mean']
            gov_mean = final_stats.loc['GOVERNMENT', 'mean']
            
            logger.info(f"\nExchange rate ratio (Government/Houthi): {gov_mean/houthi_mean:.2f}x")
            
            if 400 <= houthi_mean <= 700 and 800 <= gov_mean <= 3000:
                logger.info("\n✅ Exchange rates are within expected ranges!")
                logger.info("   The Yemen Price Paradox is confirmed in the data.")
            else:
                logger.warning("\n⚠️  Exchange rates may need further validation")
    
    logger.info("\n✓ Panel creation completed successfully!")


if __name__ == "__main__":
    main()