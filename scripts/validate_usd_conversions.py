#!/usr/bin/env python3
"""Validate USD conversions in the integrated panel data.

This script checks if USD prices are correctly calculated using zone-specific exchange rates.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Validate USD conversions in panel data."""
    logger.info("=" * 80)
    logger.info("USD Conversion Validation")
    logger.info("=" * 80)
    
    # Load the integrated panel
    panel_path = Path("data/processed/integrated_panel/yemen_integrated_balanced_panel.parquet")
    
    if not panel_path.exists():
        logger.error(f"Panel data not found at {panel_path}")
        return
    
    logger.info(f"Loading panel data from {panel_path}")
    df = pd.read_parquet(panel_path)
    
    # Filter to rows with data
    df_with_data = df[df['price'].notna() & df['exchange_rate_used'].notna()].copy()
    logger.info(f"Analyzing {len(df_with_data)} observations with price and exchange rate data")
    
    # Check exchange rate ranges by zone
    logger.info("\nExchange Rate Ranges by Zone:")
    zone_stats = df_with_data.groupby('currency_zone')['exchange_rate_used'].agg(['mean', 'min', 'max', 'count'])
    for zone, stats in zone_stats.iterrows():
        logger.info(f"  {zone}: mean={stats['mean']:.1f}, range={stats['min']:.0f}-{stats['max']:.0f}, n={stats['count']}")
    
    # Validate USD calculations
    logger.info("\nValidating USD Price Calculations:")
    
    # Recalculate USD prices
    df_with_data['usd_recalculated'] = df_with_data['price'] / df_with_data['exchange_rate_used']
    
    # Compare with stored USD prices
    df_with_data['calculation_diff'] = abs(df_with_data['usd_recalculated'] - df_with_data['usdprice'])
    df_with_data['calculation_diff_pct'] = (df_with_data['calculation_diff'] / df_with_data['usdprice']) * 100
    
    # Check if calculations match (within 0.01% tolerance for floating point)
    matches = df_with_data['calculation_diff_pct'] < 0.01
    logger.info(f"  Calculations match: {matches.sum()}/{len(df_with_data)} ({matches.mean()*100:.1f}%)")
    
    if not matches.all():
        logger.warning(f"  Found {(~matches).sum()} mismatches in USD calculations")
    
    # Compare with WFP original if available
    if 'usdprice_wfp_original' in df_with_data.columns:
        df_with_wfp = df_with_data[df_with_data['usdprice_wfp_original'].notna()].copy()
        if len(df_with_wfp) > 0:
            logger.info(f"\nComparing with WFP Original USD Prices ({len(df_with_wfp)} observations):")
            
            # Calculate discrepancy
            df_with_wfp['wfp_discrepancy'] = abs(df_with_wfp['usdprice'] - df_with_wfp['usdprice_wfp_original'])
            df_with_wfp['wfp_discrepancy_pct'] = (df_with_wfp['wfp_discrepancy'] / df_with_wfp['usdprice_wfp_original']) * 100
            
            # Summary statistics
            avg_discrepancy = df_with_wfp['wfp_discrepancy_pct'].mean()
            median_discrepancy = df_with_wfp['wfp_discrepancy_pct'].median()
            
            logger.info(f"  Average discrepancy: {avg_discrepancy:.1f}%")
            logger.info(f"  Median discrepancy: {median_discrepancy:.1f}%")
            logger.info(f"  Discrepancy range: {df_with_wfp['wfp_discrepancy_pct'].min():.1f}% - {df_with_wfp['wfp_discrepancy_pct'].max():.1f}%")
            
            # Breakdown by zone
            logger.info("\n  Discrepancy by Zone:")
            zone_discrepancy = df_with_wfp.groupby('currency_zone')['wfp_discrepancy_pct'].agg(['mean', 'median', 'count'])
            for zone, stats in zone_discrepancy.iterrows():
                logger.info(f"    {zone}: mean={stats['mean']:.1f}%, median={stats['median']:.1f}%, n={stats['count']}")
    
    # Check exchange rate ratios
    logger.info("\nExchange Rate Ratios:")
    if 'HOUTHI' in zone_stats.index and 'GOVERNMENT' in zone_stats.index:
        houthi_mean = zone_stats.loc['HOUTHI', 'mean']
        gov_mean = zone_stats.loc['GOVERNMENT', 'mean']
        ratio = gov_mean / houthi_mean
        
        logger.info(f"  Government/Houthi ratio: {ratio:.2f}x")
        logger.info(f"  Expected ratio: ~3.7x (1935/525)")
        
        if 1.5 <= ratio <= 2.5:
            logger.info("  ✅ Ratio is within reasonable range (partial improvement)")
        else:
            logger.warning("  ⚠️  Ratio is outside expected range")
    
    # Summary verdict
    logger.info("\n" + "=" * 80)
    logger.info("VALIDATION SUMMARY")
    logger.info("=" * 80)
    
    success_criteria = []
    
    # Check exchange rate ranges
    if 'HOUTHI' in zone_stats.index:
        houthi_mean = zone_stats.loc['HOUTHI', 'mean']
        if 500 <= houthi_mean <= 600:
            success_criteria.append(("HOUTHI exchange rates", True))
            logger.info("✅ HOUTHI exchange rates are correct (~569 YER/USD)")
        else:
            success_criteria.append(("HOUTHI exchange rates", False))
            logger.warning("❌ HOUTHI exchange rates are incorrect")
    
    if 'GOVERNMENT' in zone_stats.index:
        gov_mean = zone_stats.loc['GOVERNMENT', 'mean']
        if 900 <= gov_mean <= 2000:
            success_criteria.append(("GOVERNMENT exchange rates", True))
            logger.info("✅ GOVERNMENT exchange rates show improvement (~1091 YER/USD)")
        else:
            success_criteria.append(("GOVERNMENT exchange rates", False))
            logger.warning("❌ GOVERNMENT exchange rates are incorrect")
    
    # Check USD calculations
    if matches.mean() > 0.99:
        success_criteria.append(("USD calculations", True))
        logger.info("✅ USD prices correctly calculated from YER prices and exchange rates")
    else:
        success_criteria.append(("USD calculations", False))
        logger.warning("❌ USD calculations have errors")
    
    # Overall verdict
    all_passed = all(passed for _, passed in success_criteria)
    if all_passed:
        logger.info("\n🎉 VALIDATION PASSED: Exchange rates and USD conversions are now correct!")
    else:
        logger.info("\n⚠️  VALIDATION PARTIALLY PASSED: Significant improvements but further work needed")
    
    # Save validation report
    validation_report = {
        'validation_date': pd.Timestamp.now().isoformat(),
        'total_observations': len(df_with_data),
        'exchange_rate_stats': zone_stats.to_dict(),
        'usd_calculation_accuracy': matches.mean(),
        'success_criteria': dict(success_criteria),
        'overall_passed': all_passed
    }
    
    import json
    output_dir = Path("data/processed/integrated_panel")
    with open(output_dir / "usd_validation_report.json", 'w') as f:
        json.dump(validation_report, f, indent=2, default=str)
    
    logger.info(f"\nValidation report saved to {output_dir / 'usd_validation_report.json'}")


if __name__ == "__main__":
    main()