#!/usr/bin/env python3
"""Debug panel merge issues."""

import pandas as pd
from pathlib import Path

# Load WFP data
wfp_path = Path("data/raw/hdx/wfp_food_prices_wfp-food-prices-for-yemen.csv")
df = pd.read_csv(wfp_path, low_memory=False)

# Check exchange rate data
er_df = df[df['commodity'].str.contains('Exchange rate', case=False, na=False)].copy()
print(f"Exchange rate observations: {len(er_df)}")
print("\nExchange rate columns:")
print(er_df.columns.tolist())
print("\nSample exchange rate data:")
print(er_df[['market', 'admin1', 'date', 'price']].head())

# Check price data
price_df = df[~df['commodity'].str.contains('Exchange rate', case=False, na=False)].copy()
price_df = price_df[price_df['commodity'].str.contains('wheat|rice|sugar|oil|diesel|petrol|beans', case=False, na=False)]
print(f"\n\nPrice observations: {len(price_df)}")
print("\nPrice columns:")
print(price_df.columns.tolist())
print("\nSample price data:")
print(price_df[['market', 'admin1', 'date', 'commodity', 'price']].head())

# Check date formats
print("\n\nDate format check:")
print(f"Exchange rate date type: {er_df['date'].dtype}")
print(f"Price date type: {price_df['date'].dtype}")
print(f"Exchange rate date sample: {er_df['date'].iloc[0]}")
print(f"Price date sample: {price_df['date'].iloc[0]}")

# Check market names
print("\n\nMarket overlap:")
er_markets = set(er_df['market'].unique())
price_markets = set(price_df['market'].unique())
overlap = er_markets.intersection(price_markets)
print(f"Exchange rate markets: {len(er_markets)}")
print(f"Price markets: {len(price_markets)}")
print(f"Overlapping markets: {len(overlap)}")
print(f"Sample overlapping markets: {list(overlap)[:5]}")

# Check admin1 names
print("\n\nAdmin1 overlap:")
er_admin1 = set(er_df['admin1'].unique())
price_admin1 = set(price_df['admin1'].unique())
admin1_overlap = er_admin1.intersection(price_admin1)
print(f"Exchange rate admin1: {len(er_admin1)}")
print(f"Price admin1: {len(price_admin1)}")
print(f"Overlapping admin1: {len(admin1_overlap)}")
print(f"Sample overlapping admin1: {list(admin1_overlap)[:5]}")