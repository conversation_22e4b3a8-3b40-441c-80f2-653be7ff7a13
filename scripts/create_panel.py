#!/usr/bin/env python3
"""Create integrated balanced panel with proper exchange rates.

This script creates the final panel dataset with:
1. Actual exchange rates from WFP data (not the constant 250 rate)
2. Correctly calculated USD prices
3. All required features for econometric analysis
"""

import asyncio
from datetime import datetime
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.cli import cli


def main():
    """Run panel creation through CLI."""
    # Use the CLI's data pipeline command
    sys.argv = [
        "yemen-market", 
        "data", 
        "run-pipeline",
        "--start-date", "2019-01-01",
        "--end-date", datetime.now().strftime("%Y-%m-%d"),
        "--commodities", "wheat,sugar,fuel,rice,beans,oil,salt",
        "--create-balanced-panel"
    ]
    
    cli()


if __name__ == "__main__":
    main()