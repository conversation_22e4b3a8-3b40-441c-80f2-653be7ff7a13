#!/usr/bin/env python3
"""Run the complete data pipeline with proper exchange rate integration.

This is a thin wrapper that calls the src/ architecture components.
"""

import asyncio
from datetime import datetime
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.application.services.data_pipeline_orchestrator import (
    DataPipelineOrchestrator,
    PipelineConfig
)
from src.shared.container import get_container


async def main():
    """Run the complete data pipeline."""
    
    # Initialize container
    container = get_container()
    
    # Create pipeline config
    config = PipelineConfig(
        start_date=datetime(2019, 1, 1),
        end_date=datetime.now(),
        force_refresh=False,
        include_wfp=True,
        include_acled=True,
        include_acaps=True,
        include_hdx=True,
        validate_currency=True,
        enforce_coverage=True,
        create_balanced_panel=True,
        commodities=["wheat", "sugar", "fuel", "rice", "beans", "oil", "salt"],
        output_dir=Path("data/processed")
    )
    
    # Get orchestrator from container
    orchestrator = container.data_pipeline_orchestrator()
    
    # Progress callback
    def progress_callback(stage, progress, message):
        print(f"[{stage}] {progress:.1%} - {message}")
    
    # Run pipeline
    print("Starting data pipeline with proper exchange rate integration...")
    status = await orchestrator.run_pipeline(config, progress_callback)
    
    # Report results
    print("\nPipeline completed!")
    print(f"- Panel created: {status.panel_created}")
    print(f"- Coverage achieved: {status.coverage_achieved:.1%}")
    print(f"- Analysis ready: {status.analysis_ready}")
    
    if status.panel_path:
        print(f"- Panel saved to: {status.panel_path}")
    
    if status.errors:
        print("\nErrors:")
        for error in status.errors:
            print(f"  - {error}")
    
    if status.warnings:
        print("\nWarnings:")
        for warning in status.warnings:
            print(f"  - {warning}")


if __name__ == "__main__":
    asyncio.run(main())