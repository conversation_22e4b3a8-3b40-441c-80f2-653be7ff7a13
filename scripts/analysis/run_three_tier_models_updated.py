#!/usr/bin/env python3
"""Re-run three-tier econometric analysis with corrected exchange rates.

This script runs the complete three-tier analysis using the corrected panel data
after the Week 2 exchange rate fix. It compares results with the previous
(invalid) analysis to document the impact of proper currency conversion.
"""

import sys
import json
import logging
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cli import cli

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Run analysis with corrected exchange rates."""
    logger.info("=" * 80)
    logger.info("WEEK 3 RE-ANALYSIS: Three-Tier Models with Corrected Exchange Rates")
    logger.info("=" * 80)
    logger.info("")
    logger.info("CRITICAL CONTEXT:")
    logger.info("- Previous results used WFP's constant 250 YER/USD rate")
    logger.info("- Now using actual zone-specific rates:")
    logger.info("  - HOUTHI (North): ~569 YER/USD")
    logger.info("  - GOVERNMENT (South): ~1,091 YER/USD")
    logger.info("  - Ratio: 1.92x (improved from 1.0x)")
    logger.info("")
    logger.info("EXPECTED CHANGES:")
    logger.info("1. Yemen Price Paradox should be STRONGER")
    logger.info("2. Exchange rate pass-through coefficients should INCREASE")
    logger.info("3. Zone heterogeneity should be MORE PRONOUNCED")
    logger.info("4. Welfare impacts will be MORE SEVERE")
    logger.info("")
    logger.info("Starting three-tier analysis...")
    logger.info("=" * 80)
    
    # Create output directory for this run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"results/analysis/week3_reanalysis_{timestamp}"
    
    # Run the analysis through CLI
    sys.argv = [
        "yemen-market", 
        "analysis", 
        "run",
        "--tier", "all",
        "--validate-methodology",
        "--hypothesis", "H1", "H2", "H3", "H4", "H5",
        "--output-dir", output_dir,
        "--verbose"
    ]
    
    try:
        cli()
        
        logger.info("")
        logger.info("=" * 80)
        logger.info("ANALYSIS COMPLETE!")
        logger.info(f"Results saved to: {output_dir}")
        logger.info("")
        logger.info("NEXT STEPS:")
        logger.info("1. Compare coefficients with previous results")
        logger.info("2. Document stronger Yemen Price Paradox")
        logger.info("3. Update specification curve analysis")
        logger.info("4. Regenerate World Bank deliverables")
        logger.info("=" * 80)
        
        # Save analysis metadata
        metadata = {
            "timestamp": timestamp,
            "exchange_rates": {
                "HOUTHI": "~569 YER/USD",
                "GOVERNMENT": "~1,091 YER/USD",
                "ratio": 1.92
            },
            "context": "Week 3 re-analysis after exchange rate correction",
            "expected_changes": [
                "Stronger Yemen Price Paradox",
                "Higher exchange rate pass-through",
                "More pronounced zone heterogeneity",
                "More severe welfare impacts"
            ]
        }
        
        metadata_path = Path(output_dir) / "analysis_metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
            
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        raise


if __name__ == "__main__":
    main()