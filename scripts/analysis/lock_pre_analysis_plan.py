#!/usr/bin/env python3
"""
Pre-Analysis Plan Locking Script

This script creates cryptographic locks for the pre-analysis plan to ensure
research transparency and prevent post-hoc modifications.

Usage: python scripts/analysis/lock_pre_analysis_plan.py

Version: 1.0
Status: LOCKED - NO MODIFICATIONS PERMITTED
"""

import hashlib
import json
import subprocess
from datetime import datetime
from pathlib import Path
import sys
import os

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from core.models.pre_analysis.locked_specifications import LOCKED_SPECS


class PreAnalysisPlanLocker:
    """Creates cryptographic locks for the pre-analysis plan"""
    
    def __init__(self, project_root: Path = None):
        """Initialize the plan locker"""
        self.project_root = project_root or Path(__file__).parent.parent.parent
        self.lock_file = self.project_root / "docs/research-methodology-package/.pre-analysis-plan-lock"
        
        # Files to include in the lock
        self.plan_files = [
            "docs/research-methodology-package/00-overview/PRE_ANALYSIS_PLAN.md",
            "docs/research-methodology-package/03-econometric-methodology/identification-strategies/power-analysis.md",
            "docs/research-methodology-package/00-overview/ANALYSIS_WORKFLOW.md",
            "src/core/models/pre_analysis/locked_specifications.py",
            "src/core/models/pre_analysis/plan_enforcement.py",
            "src/core/models/pre_analysis/validation_framework.py"
        ]
        
    def calculate_file_hashes(self) -> dict:
        """Calculate MD5 hashes for all plan files"""
        file_hashes = {}
        
        for file_path in self.plan_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                with open(full_path, 'rb') as f:
                    content = f.read()
                    file_hashes[file_path] = hashlib.md5(content).hexdigest()
            else:
                print(f"Warning: File not found: {file_path}")
                file_hashes[file_path] = "FILE_NOT_FOUND"
        
        return file_hashes
    
    def get_git_commit_hash(self) -> str:
        """Get current Git commit hash"""
        try:
            result = subprocess.run(
                ["git", "rev-parse", "HEAD"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError:
            return "GIT_NOT_AVAILABLE"
    
    def generate_plan_hash(self, file_hashes: dict) -> str:
        """Generate overall plan hash from file hashes"""
        # Create deterministic hash from all file hashes
        combined_content = json.dumps(file_hashes, sort_keys=True)
        return hashlib.sha256(combined_content.encode()).hexdigest()
    
    def create_lock_metadata(self) -> dict:
        """Create comprehensive lock metadata"""
        file_hashes = self.calculate_file_hashes()
        git_hash = self.get_git_commit_hash()
        plan_hash = self.generate_plan_hash(file_hashes)
        
        metadata = {
            "version": "1.0",
            "lock_timestamp": datetime.now().isoformat(),
            "git_commit_hash": git_hash,
            "plan_hash": plan_hash,
            "file_hashes": file_hashes,
            "locked_specifications_hash": LOCKED_SPECS.plan_hash,
            "lock_integrity": "VALID"
        }
        
        return metadata
    
    def update_lock_file(self, metadata: dict) -> None:
        """Update the lock file with computed hashes"""
        lock_content = f"""# Pre-Analysis Plan Lock File
# This file provides cryptographic proof of pre-commitment to the analysis plan
# ANY MODIFICATION of this file invalidates the pre-commitment

VERSION={metadata['version']}
LOCK_DATE={metadata['lock_timestamp']}
LOCK_COMMIT_HASH={metadata['git_commit_hash']}
PLAN_MD5_HASH={metadata['plan_hash'][:32]}  # Truncated for display
PLAN_SHA256_HASH={metadata['plan_hash']}

# Files included in the lock:
"""
        
        for file_path, file_hash in metadata['file_hashes'].items():
            lock_content += f"# - {file_path} (MD5: {file_hash[:8]}...)\n"
        
        lock_content += f"""
# Locked specifications hash: {metadata['locked_specifications_hash']}

# Cryptographic verification
LOCK_INTEGRITY={metadata['lock_integrity']}
CRYPTOGRAPHIC_TIMESTAMP={metadata['lock_timestamp']}

# This lock file prevents:
# 1. Modification of hypothesis specifications after seeing data
# 2. Addition of new hypotheses not in the original plan
# 3. Changes to sample criteria or variable definitions
# 4. Alterations to multiple testing correction procedures
# 5. Modifications to the analysis workflow sequence

# Any changes to locked components require:
# 1. Formal amendment process with external oversight
# 2. Re-registration of modified plan
# 3. Disclosure of all changes in final publication
# 4. Acknowledgment that changes may compromise pre-commitment

# Full metadata (JSON format for programmatic verification):
# {json.dumps(metadata, indent=2)}
"""
        
        with open(self.lock_file, 'w') as f:
            f.write(lock_content)
        
        print(f"Lock file updated: {self.lock_file}")
    
    def verify_git_status(self) -> bool:
        """Verify that Git working directory is clean"""
        try:
            result = subprocess.run(
                ["git", "status", "--porcelain"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True
            )
            
            # Check if there are any uncommitted changes to plan files
            status_lines = result.stdout.strip().split('\n') if result.stdout.strip() else []
            
            for line in status_lines:
                if len(line) > 3:
                    file_path = line[3:]  # Skip status indicators
                    if any(plan_file in file_path for plan_file in self.plan_files):
                        print(f"Warning: Uncommitted changes in plan file: {file_path}")
                        return False
            
            return True
            
        except subprocess.CalledProcessError:
            print("Warning: Could not verify Git status")
            return True  # Allow locking even if Git is not available
    
    def create_git_tag(self, plan_hash: str) -> None:
        """Create Git tag for the locked plan"""
        tag_name = f"pre-analysis-plan-lock-{plan_hash[:8]}"
        
        try:
            subprocess.run(
                ["git", "tag", "-a", tag_name, "-m", "Pre-analysis plan locked"],
                cwd=self.project_root,
                check=True
            )
            print(f"Created Git tag: {tag_name}")
        except subprocess.CalledProcessError:
            print("Warning: Could not create Git tag")
    
    def lock_plan(self, force: bool = False) -> bool:
        """Execute the plan locking process"""
        print("Starting pre-analysis plan locking process...")
        
        # Verify Git status
        if not force and not self.verify_git_status():
            print("ERROR: Uncommitted changes detected in plan files.")
            print("Please commit all changes before locking, or use --force to override.")
            return False
        
        # Check if already locked
        if self.lock_file.exists() and not force:
            print("ERROR: Plan already locked. Use --force to override.")
            return False
        
        # Create lock metadata
        print("Calculating file hashes...")
        metadata = self.create_lock_metadata()
        
        # Update lock file
        print("Creating lock file...")
        self.update_lock_file(metadata)
        
        # Create Git tag
        print("Creating Git tag...")
        self.create_git_tag(metadata['plan_hash'])
        
        # Verify lock integrity
        print("Verifying lock integrity...")
        if self.verify_lock_integrity():
            print("✓ Pre-analysis plan successfully locked!")
            print(f"✓ Plan hash: {metadata['plan_hash']}")
            print(f"✓ Git commit: {metadata['git_commit_hash']}")
            print(f"✓ Lock timestamp: {metadata['lock_timestamp']}")
            return True
        else:
            print("ERROR: Lock integrity verification failed!")
            return False
    
    def verify_lock_integrity(self) -> bool:
        """Verify the integrity of the locked plan"""
        if not self.lock_file.exists():
            print("No lock file found")
            return False
        
        # Recalculate current hashes
        current_hashes = self.calculate_file_hashes()
        current_plan_hash = self.generate_plan_hash(current_hashes)
        
        # Read lock file to extract original hash
        with open(self.lock_file, 'r') as f:
            lock_content = f.read()
        
        # Extract plan hash from lock file
        for line in lock_content.split('\n'):
            if line.startswith('PLAN_SHA256_HASH='):
                original_hash = line.split('=')[1]
                break
        else:
            print("Could not find plan hash in lock file")
            return False
        
        # Compare hashes
        if current_plan_hash == original_hash:
            print("✓ Lock integrity verified")
            return True
        else:
            print(f"✗ Lock integrity compromised!")
            print(f"  Original hash: {original_hash}")
            print(f"  Current hash:  {current_plan_hash}")
            return False
    
    def generate_verification_report(self) -> dict:
        """Generate a comprehensive verification report"""
        current_hashes = self.calculate_file_hashes()
        current_plan_hash = self.generate_plan_hash(current_hashes)
        git_hash = self.get_git_commit_hash()
        
        report = {
            "verification_timestamp": datetime.now().isoformat(),
            "current_plan_hash": current_plan_hash,
            "current_git_hash": git_hash,
            "current_file_hashes": current_hashes,
            "lock_file_exists": self.lock_file.exists(),
            "integrity_status": "VERIFIED" if self.verify_lock_integrity() else "COMPROMISED"
        }
        
        return report


def main():
    """Main function for command-line usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Lock the pre-analysis plan")
    parser.add_argument("--force", action="store_true", help="Force lock even with uncommitted changes")
    parser.add_argument("--verify", action="store_true", help="Verify existing lock integrity")
    parser.add_argument("--report", action="store_true", help="Generate verification report")
    
    args = parser.parse_args()
    
    locker = PreAnalysisPlanLocker()
    
    if args.verify:
        if locker.verify_lock_integrity():
            print("Lock integrity verified ✓")
        else:
            print("Lock integrity compromised ✗")
            sys.exit(1)
    
    elif args.report:
        report = locker.generate_verification_report()
        print(json.dumps(report, indent=2))
    
    else:
        if locker.lock_plan(force=args.force):
            print("Plan locked successfully")
        else:
            print("Plan locking failed")
            sys.exit(1)


if __name__ == "__main__":
    main()