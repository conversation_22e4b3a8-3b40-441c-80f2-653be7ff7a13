#!/usr/bin/env python3
"""
Pre-Analysis Plan Validation Script

This script validates consistency across all pre-analysis plan documents
to ensure complete alignment before locking.

Usage: python scripts/analysis/validate_pre_analysis_plan.py

Version: 1.0
"""

import re
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Any


class PreAnalysisPlanValidator:
    """Validates consistency across pre-analysis plan documents"""
    
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent.parent
        self.issues = []
        
        # Key documents to validate
        self.documents = {
            'main_plan': 'docs/research-methodology-package/00-overview/PRE_ANALYSIS_PLAN.md',
            'workflow': 'docs/research-methodology-package/00-overview/ANALYSIS_WORKFLOW.md',
            'power_analysis': 'docs/research-methodology-package/03-econometric-methodology/identification-strategies/power-analysis.md',
            'testable_hypotheses': 'docs/research-methodology-package/01-theoretical-foundation/hypotheses/testable-hypotheses.md',
            'locked_specs': 'src/core/models/pre_analysis/locked_specifications.py'
        }
        
    def validate_all(self) -> bool:
        """Run all validation checks"""
        print("Running Pre-Analysis Plan Validation...")
        print("=" * 60)
        
        # Check primary hypotheses consistency
        self.check_primary_hypotheses()
        
        # Check multiple testing corrections
        self.check_multiple_testing()
        
        # Check significance levels
        self.check_significance_levels()
        
        # Check test directions
        self.check_test_directions()
        
        # Check hypothesis numbering
        self.check_hypothesis_numbering()
        
        # Check sample size consistency
        self.check_sample_sizes()
        
        # Report results
        if self.issues:
            print("\n❌ VALIDATION FAILED - Issues found:")
            for issue in self.issues:
                print(f"  - {issue}")
            return False
        else:
            print("\n✅ VALIDATION PASSED - All documents consistent!")
            return True
    
    def read_file(self, doc_key: str) -> str:
        """Read document content"""
        file_path = self.project_root / self.documents[doc_key]
        if not file_path.exists():
            self.issues.append(f"Missing file: {doc_key}")
            return ""
        with open(file_path, 'r') as f:
            return f.read()
    
    def check_primary_hypotheses(self):
        """Validate primary hypotheses are consistent"""
        print("\nChecking primary hypotheses...")
        
        # Expected primary hypotheses
        expected_primary = ['H1', 'H3', 'H4']
        
        # Check main plan
        main_content = self.read_file('main_plan')
        if "H1, H3, H4" not in main_content:
            self.issues.append("Main plan doesn't list correct primary hypotheses (H1, H3, H4)")
        
        # Check workflow
        workflow_content = self.read_file('workflow')
        if "H1, H3, H4 estimation" not in workflow_content:
            self.issues.append("Workflow doesn't reference correct primary hypotheses")
        
        # Check Python code
        code_content = self.read_file('locked_specs')
        for hyp in expected_primary:
            if f'hypothesis_id="{hyp}"' not in code_content:
                self.issues.append(f"Locked specifications missing {hyp}")
        
        print(f"  Primary hypotheses: {', '.join(expected_primary)}")
    
    def check_multiple_testing(self):
        """Validate multiple testing corrections"""
        print("\nChecking multiple testing corrections...")
        
        main_content = self.read_file('main_plan')
        
        # Check primary correction
        if "Bonferroni correction" not in main_content:
            self.issues.append("Primary hypotheses should use Bonferroni correction")
        
        # Check secondary count
        if "0.01/10 = 0.001" not in main_content:
            self.issues.append("Secondary hypotheses should have 10 tests (0.01/10)")
        
        print("  Primary: Bonferroni (α/3)")
        print("  Secondary: Bonferroni (α/10)")
    
    def check_significance_levels(self):
        """Validate significance levels are consistent"""
        print("\nChecking significance levels...")
        
        # Expected values
        expected = {
            'primary': 0.0167,
            'secondary': 0.001
        }
        
        main_content = self.read_file('main_plan')
        
        # Check primary α
        if "α = 0.0167" not in main_content:
            self.issues.append(f"Primary α should be {expected['primary']}")
        
        # Check secondary α
        if "α = 0.001" not in main_content:
            self.issues.append(f"Secondary α should be {expected['secondary']}")
        
        print(f"  Primary α: {expected['primary']}")
        print(f"  Secondary α: {expected['secondary']}")
    
    def check_test_directions(self):
        """Validate all tests are two-tailed and no directional expectations"""
        print("\nChecking test directions...")
        
        main_content = self.read_file('main_plan')
        workflow_content = self.read_file('workflow')
        
        # Check that all primary tests are two-tailed
        if "H1:** Two-tailed" not in main_content:
            self.issues.append("H1 should be two-tailed test")
        
        if "H3:** Two-tailed" not in main_content:
            self.issues.append("H3 should be two-tailed test")
        
        if "H4:** Two-tailed" not in main_content:
            self.issues.append("H4 should be two-tailed test")
        
        # Check for problematic directional expectations
        if "expected)" in workflow_content.lower() or "β₁ < 0" in workflow_content:
            self.issues.append("Workflow contains directional expectations - should be neutral")
        
        print("  All primary hypotheses: Two-tailed")
        print("  No directional expectations: ✓")
    
    def check_hypothesis_numbering(self):
        """Validate hypothesis numbering consistency"""
        print("\nChecking hypothesis numbering...")
        
        # Check that H4 is arbitrage (not H5)
        power_content = self.read_file('power_analysis')
        if "### H4: Cross-Border Arbitrage" not in power_content:
            self.issues.append("Power analysis should have H4 as arbitrage hypothesis")
        
        workflow_content = self.read_file('workflow')
        if "Step 3.3: H4 - Cross-Border Arbitrage" not in workflow_content:
            self.issues.append("Workflow should have H4 as arbitrage hypothesis")
        
        print("  H4 = Cross-Border Arbitrage ✓")
    
    def check_sample_sizes(self):
        """Validate sample size consistency"""
        print("\nChecking sample sizes...")
        
        power_content = self.read_file('power_analysis')
        
        # Check market count
        if "~150" in power_content:
            print("  Markets: ~150 ✓")
        else:
            self.issues.append("Power analysis should specify ~150 markets")
        
        # Check observations
        if "~45,000" in power_content:
            print("  Observations: ~45,000 ✓")
        else:
            self.issues.append("Power analysis should specify ~45,000 observations")
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """Generate validation summary"""
        return {
            'validation_passed': len(self.issues) == 0,
            'issues_found': len(self.issues),
            'issues': self.issues,
            'documents_checked': list(self.documents.keys()),
            'critical_parameters': {
                'primary_hypotheses': ['H1', 'H3', 'H4'],
                'primary_alpha': 0.0167,
                'secondary_alpha': 0.001,
                'n_primary_tests': 3,
                'n_secondary_tests': 10,
                'multiple_testing_method': 'Bonferroni',
                'all_tests_two_tailed': True
            }
        }


def main():
    """Main validation function"""
    validator = PreAnalysisPlanValidator()
    
    # Run validation
    passed = validator.validate_all()
    
    # Generate report
    report = validator.generate_summary_report()
    
    print("\n" + "=" * 60)
    print("VALIDATION SUMMARY")
    print("=" * 60)
    print(f"Documents checked: {len(report['documents_checked'])}")
    print(f"Issues found: {report['issues_found']}")
    print(f"Status: {'PASSED' if passed else 'FAILED'}")
    
    if passed:
        print("\n✅ Pre-analysis plan is consistent and ready for locking!")
        sys.exit(0)
    else:
        print("\n❌ Please fix the issues above before locking the plan.")
        sys.exit(1)


if __name__ == "__main__":
    main()