#!/usr/bin/env python3
"""Run the complete data pipeline with proper exchange rate handling.

This script orchestrates the full data pipeline from raw data collection
through to final panel dataset creation, ensuring proper currency zone
classification and exchange rate application.
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path
import pandas as pd
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.shared.container import Container
from src.application.services.data_pipeline_orchestrator import (
    DataPipelineOrchestrator, PipelineConfig, PipelineStage
)
from src.core.utils.logging import get_logger
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn


# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = get_logger(__name__)
console = Console()


async def validate_exchange_rates(panel_path: Path) -> dict:
    """Validate that exchange rates are correctly applied."""
    df = pd.read_parquet(panel_path)
    
    # Check exchange rate ranges by zone
    zone_stats = df.groupby('currency_zone')['exchange_rate_used'].agg(['mean', 'min', 'max', 'count'])
    
    # Calculate USD conversion accuracy
    if 'usdprice_wfp_original' in df.columns and 'usdprice' in df.columns:
        df['usd_discrepancy'] = abs(df['usdprice'] - df['usdprice_wfp_original']) / df['usdprice_wfp_original']
        avg_discrepancy = df['usd_discrepancy'].mean()
    else:
        avg_discrepancy = None
    
    return {
        'zone_stats': zone_stats,
        'avg_discrepancy': avg_discrepancy,
        'total_observations': len(df),
        'zones_found': df['currency_zone'].unique().tolist()
    }


async def main():
    """Run the complete data pipeline."""
    console.print("[bold blue]Yemen Market Integration - Complete Data Pipeline[/bold blue]")
    console.print("[yellow]This process will:[/yellow]")
    console.print("1. Download/update all data sources (WFP, ACLED, ACAPS, HDX)")
    console.print("2. Extract exchange rates from WFP commodity data")
    console.print("3. Classify markets into currency zones")
    console.print("4. Apply zone-specific exchange rates")
    console.print("5. Create integrated panel dataset")
    console.print("6. Validate data quality and coverage\n")
    
    # Configuration
    config = PipelineConfig(
        start_date=datetime(2019, 1, 1),
        end_date=datetime(2025, 3, 1),
        force_refresh=False,  # Set to True to force re-download
        include_wfp=True,
        include_acled=True,
        include_acaps=True,
        include_hdx=True,
        validate_currency=True,
        enforce_coverage=True,
        target_coverage=0.884,
        create_balanced_panel=True,
        commodities=["wheat flour", "sugar", "diesel", "rice", "vegetable oil"],
        output_dir=Path("data/processed"),
        save_intermediate=True
    )
    
    # Get container and services
    container = Container()
    ingestion_service = container.application.data_ingestion_service()
    ingestion_orchestrator = container.application.ingestion_orchestrator()
    panel_builder_service = container.application.panel_builder_service()
    
    # Create orchestrator
    orchestrator = DataPipelineOrchestrator(
        ingestion_service=ingestion_service,
        ingestion_orchestrator=ingestion_orchestrator,
        panel_builder_service=panel_builder_service
    )
    
    # Progress tracking
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        console=console
    ) as progress:
        
        task = progress.add_task("[cyan]Running pipeline...", total=100)
        
        async def update_progress(status):
            # Calculate overall progress
            stages = [
                PipelineStage.COLLECTION,
                PipelineStage.PROCESSING,
                PipelineStage.INTEGRATION,
                PipelineStage.VALIDATION,
                PipelineStage.PANEL_BUILDING
            ]
            
            completed_stages = sum(1 for stage in stages if status.stages_completed.get(stage, False))
            stage_progress = status.current_stage_progress / len(stages)
            overall_progress = (completed_stages / len(stages) + stage_progress) * 100
            
            progress.update(task, completed=overall_progress)
            
            # Update description
            stage_names = {
                PipelineStage.COLLECTION: "Collecting data",
                PipelineStage.PROCESSING: "Processing data",
                PipelineStage.INTEGRATION: "Integrating datasets",
                PipelineStage.VALIDATION: "Validating data",
                PipelineStage.PANEL_BUILDING: "Building panel"
            }
            
            desc = stage_names.get(status.stage, status.stage.value)
            progress.update(task, description=f"[cyan]{desc}...")
        
        try:
            # Run pipeline
            status = await orchestrator.run_full_pipeline(config, update_progress)
            
            # Show results
            progress.update(task, completed=100, description="[green]Pipeline completed!")
            
        except Exception as e:
            console.print(f"[bold red]Pipeline failed: {e}[/bold red]")
            raise
    
    # Display results
    if status.stage == PipelineStage.COMPLETED:
        console.print("\n[bold green]✓ Pipeline completed successfully![/bold green]\n")
        
        # Create results table
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Metric", style="cyan", width=30)
        table.add_column("Value", style="green")
        
        # Basic metrics
        table.add_row("Pipeline ID", str(status.pipeline_id))
        table.add_row("Duration", f"{(status.completed_at - status.started_at).total_seconds():.1f} seconds")
        table.add_row("Validation Passed", "✓" if status.validation_passed else "✗")
        table.add_row("Coverage Achieved", f"{status.coverage_achieved:.1%}")
        
        # Data collected
        table.add_row("", "")  # Separator
        table.add_row("[bold]Data Collected[/bold]", "")
        for source, count in status.records_collected.items():
            table.add_row(f"  {source}", f"{count:,} records")
        
        # Panel info
        if status.panel_path:
            table.add_row("", "")  # Separator
            table.add_row("Panel Dataset", str(status.panel_path))
            
            # Validate exchange rates
            console.print("\n[yellow]Validating exchange rate application...[/yellow]")
            validation = await validate_exchange_rates(status.panel_path)
            
            table.add_row("", "")  # Separator
            table.add_row("[bold]Exchange Rate Validation[/bold]", "")
            table.add_row("Total Observations", f"{validation['total_observations']:,}")
            table.add_row("Currency Zones Found", ", ".join(validation['zones_found']))
            
            if validation['zone_stats'] is not None:
                for zone, stats in validation['zone_stats'].iterrows():
                    table.add_row(
                        f"  {zone} Zone",
                        f"Mean: {stats['mean']:.1f}, Range: {stats['min']:.0f}-{stats['max']:.0f} YER/USD"
                    )
            
            if validation['avg_discrepancy'] is not None:
                table.add_row("USD Conversion Accuracy", f"{(1 - validation['avg_discrepancy']) * 100:.1f}%")
        
        console.print(table)
        
        # Warnings
        if status.warnings:
            console.print("\n[yellow]Warnings:[/yellow]")
            for warning in status.warnings:
                console.print(f"  ⚠ {warning}")
        
        # Next steps
        console.print("\n[bold blue]Next Steps:[/bold blue]")
        console.print("1. Review the generated panel dataset")
        console.print("2. Check exchange rate ranges match expectations:")
        console.print("   - Northern zones (Houthi): ~500-600 YER/USD")
        console.print("   - Southern zones (Government): ~1500-2500 YER/USD")
        console.print("3. Run three-tier analysis with: python scripts/run_analysis.py")
        
    else:
        console.print(f"\n[bold red]✗ Pipeline failed at stage: {status.stage.value}[/bold red]")
        if status.errors:
            console.print("\n[red]Errors:[/red]")
            for error in status.errors:
                console.print(f"  ✗ {error}")


if __name__ == "__main__":
    asyncio.run(main())