#!/usr/bin/env python3
"""Download all required data sources.

This script downloads data from:
- WFP (World Food Programme) - food prices
- ACLED (Armed Conflict Location & Event Data) - conflict events
- HDX (Humanitarian Data Exchange) - geographic boundaries
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.cli import cli


def main():
    """Download data through CLI."""
    # Use the CLI's data download command
    sys.argv = [
        "yemen-market", 
        "data", 
        "update-data",
        "all",  # Download all sources
        "--force"  # Force re-download
    ]
    
    cli()


if __name__ == "__main__":
    main()