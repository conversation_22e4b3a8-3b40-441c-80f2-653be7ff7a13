#!/usr/bin/env python3
"""Simple panel creation script with correct exchange rate handling.

This script directly processes the WFP data without complex imports.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import json
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# Zone classification mapping
GOVERNORATE_TO_ZONE = {
    # Houthi-controlled (North)
    "Sana'a": "HOUTHI",
    "Sana'a City": "HOUTHI", 
    "Amanat Al Asimah": "HOUTHI",
    "Sa'dah": "HOUTHI",
    "Sa'ada": "HOUTHI",
    "Hajjah": "HOUTHI",
    "Al Mahwit": "HOUTHI",
    "Dhamar": "HOUTHI",
    "Raymah": "HOUTHI",
    "Ibb": "HOUTHI",
    "Amran": "HOUTHI",
    "<PERSON>": "HOUTHI",
    "Al Hudaydah": "HOUTHI",
    
    # Government-controlled (South)
    "Aden": "GOVERNMENT",
    "Lahj": "GOVERNMENT",
    "Abyan": "GOVERNMENT",
    "Shabwah": "GOVERNMENT",
    "Hadramawt": "GOVERNMENT",
    "Hadramaut": "GOVERNMENT",
    "Al Maharah": "GOVERNMENT",
    "Socotra": "GOVERNMENT",
    "Ad Dale'": "GOVERNMENT",
    "Al Dhale'e": "GOVERNMENT",
    "Marib": "GOVERNMENT",
    "Al Jawf": "GOVERNMENT",
    
    # Contested
    "Ta'iz": "CONTESTED",
    "Taizz": "CONTESTED",
    "Al Bayda": "CONTESTED"
}


def main():
    """Process panel data with correct exchange rates."""
    logger.info("=" * 80)
    logger.info("Simple Panel Creation with Correct Exchange Rates")
    logger.info("=" * 80)
    
    # Configuration
    wfp_path = Path("data/raw/hdx/wfp_food_prices_wfp-food-prices-for-yemen.csv")
    output_dir = Path("data/processed/integrated_panel")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if not wfp_path.exists():
        logger.error(f"WFP data not found at {wfp_path}")
        return
    
    # Load WFP data
    logger.info(f"Loading WFP data from {wfp_path}")
    df = pd.read_csv(wfp_path)
    logger.info(f"Loaded {len(df)} total observations")
    
    # Step 1: Extract exchange rates
    logger.info("\nExtracting exchange rates from WFP commodity data...")
    er_df = df[df['commodity'].str.contains('Exchange rate', case=False, na=False)].copy()
    logger.info(f"Found {len(er_df)} exchange rate observations")
    
    if len(er_df) > 0:
        # Clean exchange rate data
        er_df['date'] = pd.to_datetime(er_df['date'])
        er_df['exchange_rate'] = pd.to_numeric(er_df['price'], errors='coerce')
        er_df = er_df[er_df['exchange_rate'] > 0]
        
        # Add currency zones
        er_df['currency_zone'] = er_df['admin1'].map(GOVERNORATE_TO_ZONE).fillna('UNKNOWN')
        
        # Calculate zone statistics
        zone_stats = er_df.groupby('currency_zone')['exchange_rate'].agg(['mean', 'min', 'max', 'count'])
        logger.info("\nExchange rate statistics by zone:")
        for zone, stats in zone_stats.iterrows():
            logger.info(f"  {zone}: mean={stats['mean']:.1f}, range={stats['min']:.0f}-{stats['max']:.0f}, n={stats['count']}")
    
    # Step 2: Process price data
    logger.info("\nProcessing price data...")
    
    # Essential commodities - check what's actually in the data
    all_commodities = df['commodity'].unique()
    logger.info(f"Available commodities: {len(all_commodities)}")
    
    # Filter to find matching essential commodities (case-insensitive)
    essential_patterns = [
        "wheat", "rice", "sugar", "oil", "diesel", "petrol", "beans"
    ]
    
    matching_commodities = []
    for commodity in all_commodities:
        if commodity and any(pattern in commodity.lower() for pattern in essential_patterns):
            matching_commodities.append(commodity)
    
    logger.info(f"Found {len(matching_commodities)} matching commodities")
    for comm in matching_commodities[:10]:  # Show first 10
        logger.info(f"  - {comm}")
    
    # Filter prices (exclude exchange rate commodity)
    price_df = df[~df['commodity'].str.contains('Exchange rate', case=False, na=False)].copy()
    
    # Use matching commodities or all if none match
    if matching_commodities:
        price_df = price_df[price_df['commodity'].isin(matching_commodities)]
    
    # Clean data
    price_df['date'] = pd.to_datetime(price_df['date'])
    price_df = price_df[(price_df['date'] >= '2019-01-01') & (price_df['date'] <= '2025-03-01')]
    price_df['price'] = pd.to_numeric(price_df['price'], errors='coerce')
    price_df = price_df[price_df['price'] > 0]
    
    # Add currency zones
    price_df['currency_zone'] = price_df['admin1'].map(GOVERNORATE_TO_ZONE).fillna('UNKNOWN')
    
    logger.info(f"Filtered to {len(price_df)} price observations")
    logger.info(f"Date range: {price_df['date'].min()} to {price_df['date'].max()}")
    logger.info(f"Markets: {price_df['market'].nunique()}")
    
    # Step 3: Create balanced panel structure
    logger.info("\nCreating balanced panel structure...")
    markets = price_df['market'].unique()
    commodities_in_data = price_df['commodity'].unique()
    date_range = pd.date_range(start='2019-01-01', end='2025-03-01', freq='MS')
    
    # Create all combinations
    from itertools import product
    panel_index = pd.DataFrame(
        list(product(markets, commodities_in_data, date_range)),
        columns=['market', 'commodity', 'date']
    )
    logger.info(f"Created panel structure with {len(panel_index)} potential observations")
    
    # Merge with price data
    panel_df = panel_index.merge(
        price_df,
        on=['market', 'commodity', 'date'],
        how='left'
    )
    
    # Step 4: Integrate exchange rates
    logger.info("\nIntegrating exchange rates...")
    
    if len(er_df) > 0:
        # Calculate zone averages by date
        zone_rates = er_df.groupby(['currency_zone', 'date'])['exchange_rate'].mean().reset_index()
        
        # Merge exchange rates
        panel_df = panel_df.merge(
            zone_rates.rename(columns={'exchange_rate': 'exchange_rate_used'}),
            on=['currency_zone', 'date'],
            how='left'
        )
        
        # Calculate USD prices
        panel_df['price_usd_calculated'] = panel_df['price'] / panel_df['exchange_rate_used']
        
        # Compare with WFP's USD price
        if 'usdprice' in panel_df.columns:
            panel_df['usdprice_wfp_original'] = panel_df['usdprice']
            mask = panel_df['price_usd_calculated'].notna() & panel_df['usdprice_wfp_original'].notna()
            panel_df.loc[mask, 'usd_discrepancy_pct'] = abs(
                panel_df.loc[mask, 'price_usd_calculated'] - panel_df.loc[mask, 'usdprice_wfp_original']
            ) / panel_df.loc[mask, 'usdprice_wfp_original'] * 100
            
            avg_discrepancy = panel_df.loc[mask, 'usd_discrepancy_pct'].mean()
            logger.info(f"\nAverage USD price discrepancy vs WFP: {avg_discrepancy:.1f}%")
        
        # Use our calculated USD price
        panel_df['usdprice'] = panel_df['price_usd_calculated']
    
    # Step 5: Add derived features
    logger.info("\nAdding derived features...")
    panel_df['log_price'] = np.log(panel_df['price'].replace(0, np.nan))
    panel_df['log_usdprice'] = np.log(panel_df['usdprice'].replace(0, np.nan))
    
    # Sort for time series
    panel_df = panel_df.sort_values(['market', 'commodity', 'date'])
    
    # Price changes
    panel_df['price_change_pct'] = panel_df.groupby(['market', 'commodity'])['price'].pct_change()
    panel_df['log_price_change'] = panel_df.groupby(['market', 'commodity'])['log_price'].diff()
    
    # Time features
    panel_df['year'] = panel_df['date'].dt.year
    panel_df['month'] = panel_df['date'].dt.month
    panel_df['quarter'] = panel_df['date'].dt.quarter
    
    # Step 6: Calculate coverage
    total_observations = len(panel_df)
    observations_with_prices = panel_df['price'].notna().sum()
    observations_with_exchange_rates = panel_df['exchange_rate_used'].notna().sum()
    observations_with_usd_prices = panel_df['usdprice'].notna().sum()
    
    logger.info("\nCoverage statistics:")
    logger.info(f"  Total observations: {total_observations:,}")
    logger.info(f"  With prices: {observations_with_prices:,} ({observations_with_prices/total_observations*100:.1f}%)")
    logger.info(f"  With exchange rates: {observations_with_exchange_rates:,}")
    logger.info(f"  With USD prices: {observations_with_usd_prices:,} ({observations_with_usd_prices/total_observations*100:.1f}%)")
    
    # Step 7: Save outputs
    output_path = output_dir / "yemen_integrated_balanced_panel.parquet"
    panel_df.to_parquet(output_path, index=False)
    logger.info(f"\nSaved panel to {output_path}")
    
    # Save CSV sample
    csv_path = output_dir / "yemen_integrated_balanced_panel_sample.csv"
    panel_df.head(10000).to_csv(csv_path, index=False)
    logger.info(f"Saved CSV sample to {csv_path}")
    
    # Save metadata
    metadata = {
        'created_at': datetime.now().isoformat(),
        'total_observations': int(total_observations),
        'observations_with_prices': int(observations_with_prices),
        'observations_with_exchange_rates': int(observations_with_exchange_rates),
        'observations_with_usd_prices': int(observations_with_usd_prices),
        'coverage_pct': float(observations_with_prices / total_observations * 100),
        'date_range': {
            'start': str(panel_df['date'].min()),
            'end': str(panel_df['date'].max())
        },
        'markets': int(panel_df['market'].nunique()),
        'commodities': panel_df['commodity'].unique().tolist()
    }
    
    with open(output_dir / "panel_metadata.json", 'w') as f:
        json.dump(metadata, f, indent=2)
    
    # Final exchange rate summary
    if 'exchange_rate_used' in panel_df.columns:
        logger.info("\n" + "=" * 80)
        logger.info("FINAL EXCHANGE RATE SUMMARY")
        logger.info("=" * 80)
        
        final_stats = panel_df.groupby('currency_zone')['exchange_rate_used'].agg(['mean', 'min', 'max'])
        for zone, stats in final_stats.iterrows():
            logger.info(f"{zone}: mean={stats['mean']:.1f}, range={stats['min']:.0f}-{stats['max']:.0f} YER/USD")
        
        # Check if rates are reasonable
        if 'HOUTHI' in final_stats.index and 'GOVERNMENT' in final_stats.index:
            houthi_mean = final_stats.loc['HOUTHI', 'mean']
            gov_mean = final_stats.loc['GOVERNMENT', 'mean']
            
            if 400 <= houthi_mean <= 700 and 1000 <= gov_mean <= 3000:
                logger.info("\n✅ Exchange rates are within expected ranges!")
            else:
                logger.warning("\n⚠️  Exchange rates may be outside expected ranges")
                logger.warning(f"   Expected: HOUTHI ~525, GOVERNMENT ~1935")
    
    logger.info("\n✓ Panel creation completed successfully!")


if __name__ == "__main__":
    main()