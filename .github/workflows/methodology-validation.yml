name: Methodology Validation

on:
  push:
    paths:
      - 'docs/research-methodology-package/**/*.md'
      - 'src/**/*.py'
  pull_request:
    paths:
      - 'docs/research-methodology-package/**/*.md'
      - 'src/**/*.py'

jobs:
  validate-methodology:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Run Methodology Validation
      run: |
        python src/core/validation/methodology_validator.py docs/research-methodology-package --output validation-report.md
      continue-on-error: true

    - name: Check Template Integrity
      run: |
        python -c "
        from src.core.reporting import ResultsTemplateGenerator
        generator = ResultsTemplateGenerator()

        # Test templates for predetermined language
        import os
        templates_dir = 'docs/research-methodology-package/07-results-templates'
        violations = []

        for root, dirs, files in os.walk(templates_dir):
            if 'examples' in root or 'archive' in root:
                continue
            for file in files:
                if file.endswith('.md'):
                    path = os.path.join(root, file)
                    try:
                        with open(path, 'r') as f:
                            content = f.read()
                        if not generator.validate_template_integrity(content):
                            violations.append(path)
                    except:
                        pass

        if violations:
            print('❌ Template integrity violations found:')
            for v in violations:
                print(f'  - {v}')
            exit(1)
        else:
            print('✅ All templates pass integrity checks')
        "

    - name: Check for Predetermined Language
      run: |
        # Check for forbidden phrases in methodology files
        FORBIDDEN_PHRASES="revolutionary|paradigm.shift|breakthrough|game.changing|validates.our|confirms.our|proves.that|establishes.that|key.discovery"

        echo "Checking for predetermined language..."
        violations=$(grep -r -i -E "$FORBIDDEN_PHRASES" docs/research-methodology-package/ \
          --include="*.md" \
          --exclude-dir=archive \
          --exclude-dir=examples || true)

        if [ ! -z "$violations" ]; then
          echo "❌ Predetermined language found:"
          echo "$violations"
          exit 1
        else
          echo "✅ No predetermined language detected"
        fi

    - name: Verify Currency Conversion Checklists
      run: |
        # Check that price analysis files have currency verification
        echo "Checking for currency conversion checklists..."

        price_files=$(grep -r -l -i "price\|cost\|exchange.rate" docs/research-methodology-package/07-results-templates/ \
          --include="*.md" \
          --exclude-dir=examples || true)

        missing_currency_check=""
        for file in $price_files; do
          if ! grep -q -i "currency.*conversion\|exchange.*rate.*check" "$file"; then
            missing_currency_check="$missing_currency_check\n  - $file"
          fi
        done

        if [ ! -z "$missing_currency_check" ]; then
          echo "❌ Files missing currency conversion checks:"
          echo -e "$missing_currency_check"
          exit 1
        else
          echo "✅ All price analysis files have currency verification"
        fi

    - name: Test Template Generator
      run: |
        python -c "
        from src.core.reporting.template_generator import ResultsTemplateGenerator, TestResult, TemplateType

        # Test that generator rejects predetermined language
        generator = ResultsTemplateGenerator()

        try:
            bad_result = TestResult(
                test_name='Test',
                test_statistic=2.0,
                p_value=0.05,
                effect_size=0.3,
                confidence_interval=(0.1, 0.5),
                interpretation='This revolutionary discovery proves our hypothesis',
                robustness_checks=[]
            )
            generator.generate_results_section([bad_result], 'H1', TemplateType.EXCHANGE_RATE)
            print('❌ Template generator failed to reject predetermined language')
            exit(1)
        except ValueError as e:
            if 'predetermined language' in str(e).lower():
                print('✅ Template generator correctly rejects predetermined language')
            else:
                print(f'❌ Unexpected error: {e}')
                exit(1)
        except Exception as e:
            print(f'❌ Unexpected error: {e}')
            exit(1)
        "

    - name: Upload Validation Report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: methodology-validation-report
        path: validation-report.md

    - name: Comment on PR
      if: github.event_name == 'pull_request' && failure()
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          let report = 'Validation report not available.';
          try {
            report = fs.readFileSync('validation-report.md', 'utf8');
          } catch (error) {
            console.log('Could not read validation report:', error.message);
          }

          const commentBody = '## ❌ Methodology Validation Failed\n\n' +
            'Predetermined language or template violations detected. Please review and fix the issues.\n\n' +
            '**Validation Report:**\n```\n' + report + '\n```\n\n' +
            '**Required Actions:**\n' +
            '- Remove any predetermined conclusions\n' +
            '- Ensure all price analysis includes currency verification\n' +
            '- Use placeholders like [TO BE DETERMINED] instead of specific findings\n' +
            '- Test templates using the provided examples\n\n' +
            'See the Template Usage Guide for details: docs/research-methodology-package/07-results-templates/TEMPLATE_USAGE_GUIDE.md';

          await github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: commentBody
          });
