name: Documentation Quality Checks

on:
  push:
    branches: [ main, develop ]
    paths: 
      - 'docs/**'
      - '*.md'
  pull_request:
    branches: [ main ]
    paths:
      - 'docs/**'
      - '*.md'

jobs:
  link-check:
    name: Check Documentation Links
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: Install markdown-link-check
      run: npm install -g markdown-link-check
      
    - name: Check documentation links
      run: |
        # Check all markdown files for broken links
        find . -name "*.md" -not -path "./node_modules/*" -not -path "./.git/*" | \
        xargs -I {} markdown-link-check {} --config .github/markdown-link-check-config.json
        
    - name: Check internal cross-references
      run: |
        # Custom script to validate internal documentation links
        python scripts/validate_internal_links.py

  documentation-structure:
    name: Validate Documentation Structure
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        
    - name: Install dependencies
      run: |
        pip install pyyaml markdown
        
    - name: Validate documentation structure
      run: |
        python scripts/validate_docs_structure.py
        
    - name: Check README coverage
      run: |
        python scripts/check_readme_coverage.py
        
    - name: Validate SRC alignment
      run: |
        python scripts/validate_src_alignment.py

  research-methodology-integrity:
    name: Verify Research Methodology Package Integrity
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Count research methodology files
      run: |
        file_count=$(find docs/research-methodology-package -name "*.md" | wc -l)
        echo "Research methodology files: $file_count"
        
        # Verify we have the expected number of files (264)
        if [ "$file_count" -lt 260 ]; then
          echo "ERROR: Research methodology package appears incomplete"
          echo "Expected ~264 files, found $file_count"
          exit 1
        fi
        
    - name: Validate methodology cross-references
      run: |
        # Check for broken links within research methodology package
        find docs/research-methodology-package -name "*.md" | \
        xargs grep -l "\[.*\](" | \
        head -20 | \
        xargs -I {} markdown-link-check {} --config .github/markdown-link-check-config.json

  world-bank-standards:
    name: Validate World Bank Standards Compliance
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Check for World Bank standard references
      run: |
        # Verify World Bank standards are maintained
        if ! grep -r "World Bank" docs/research-methodology-package/; then
          echo "WARNING: World Bank standards references not found"
        fi
        
    - name: Check academic rigor indicators
      run: |
        # Check for academic quality indicators
        academic_indicators=(
          "hypothesis"
          "econometric"
          "methodology"
          "validation"
          "robustness"
        )
        
        for indicator in "${academic_indicators[@]}"; do
          count=$(grep -r -i "$indicator" docs/research-methodology-package/ | wc -l)
          echo "$indicator references: $count"
        done