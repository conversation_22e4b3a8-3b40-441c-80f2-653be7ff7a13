"""Data pipeline CLI commands."""

from datetime import datetime
from pathlib import Path
from typing import List, Optional

import typer
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich.table import Table

from ...application.services.data_pipeline_orchestrator import (
    DataPipelineOrchestrator, PipelineConfig, PipelineStage
)
from ...shared.container import Container


app = typer.Typer(
    name="data",
    help="Data pipeline management commands"
)
console = Console()


@app.command()
def run_pipeline(
    start_date: str = typer.Option("2019-01-01", help="Start date (YYYY-MM-DD)"),
    end_date: str = typer.Option("2025-03-01", help="End date (YYYY-MM-DD)"),
    force_refresh: bool = typer.Option(False, "--force", "-f", help="Force re-download of data"),
    include_wfp: bool = typer.Option(True, help="Include WFP price data"),
    include_acled: bool = typer.Option(True, help="Include ACLED conflict data"),
    include_acaps: bool = typer.Option(True, help="Include ACAPS control zones"),
    include_hdx: bool = typer.Option(True, help="Include HDX boundaries"),
    commodities: Optional[List[str]] = typer.Option(None, help="Specific commodities to include"),
    output_dir: Optional[Path] = typer.Option(None, help="Custom output directory"),
    validate: bool = typer.Option(True, help="Run validation after processing")
):
    """Run the complete data pipeline from collection to panel building."""
    console.print(f"[bold blue]Starting data pipeline...[/bold blue]")
    
    # Parse dates
    start = datetime.fromisoformat(start_date)
    end = datetime.fromisoformat(end_date)
    
    # Create pipeline config
    config = PipelineConfig(
        start_date=start,
        end_date=end,
        force_refresh=force_refresh,
        include_wfp=include_wfp,
        include_acled=include_acled,
        include_acaps=include_acaps,
        include_hdx=include_hdx,
        validate_currency=validate,
        enforce_coverage=validate
    )
    
    if commodities:
        config.commodities = commodities
    if output_dir:
        config.output_dir = output_dir
    
    # Get container and services
    container = Container()
    ingestion_service = container.application.data_ingestion_service()
    ingestion_orchestrator = container.application.ingestion_orchestrator()
    panel_builder_service = container.application.panel_builder_service()
    
    # Create orchestrator
    orchestrator = DataPipelineOrchestrator(
        ingestion_service=ingestion_service,
        ingestion_orchestrator=ingestion_orchestrator,
        panel_builder_service=panel_builder_service
    )
    
    # Progress callback
    def update_progress(status):
        stage_names = {
            PipelineStage.COLLECTION: "Collecting data",
            PipelineStage.PROCESSING: "Processing data",
            PipelineStage.INTEGRATION: "Integrating datasets",
            PipelineStage.VALIDATION: "Validating data",
            PipelineStage.PANEL_BUILDING: "Building panel"
        }
        
        console.print(f"[yellow]{stage_names.get(status.stage, status.stage.value)}:[/yellow] "
                     f"{status.current_stage_progress:.0%}")
    
    try:
        # Run pipeline with progress tracking
        import asyncio
        status = asyncio.run(orchestrator.run_full_pipeline(config, update_progress))
        
        # Show results
        if status.stage == PipelineStage.COMPLETED:
            console.print(f"[bold green]Pipeline completed successfully![/bold green]")
            
            # Create results table
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="green")
            
            table.add_row("Pipeline ID", str(status.pipeline_id))
            table.add_row("Duration", f"{(status.completed_at - status.started_at).total_seconds():.1f}s")
            table.add_row("Coverage Achieved", f"{status.coverage_achieved:.1%}")
            table.add_row("Panel Created", "✓" if status.panel_created else "✗")
            
            if status.panel_path:
                table.add_row("Panel Location", str(status.panel_path))
            
            # Add record counts
            for source, count in status.records_collected.items():
                table.add_row(f"{source} Records", f"{count:,}")
            
            console.print(table)
            
            if status.warnings:
                console.print(f"[yellow]Warnings:[/yellow]")
                for warning in status.warnings:
                    console.print(f"  - {warning}")
        else:
            console.print(f"[bold red]Pipeline failed![/bold red]")
            console.print(f"Stage: {status.stage.value}")
            if status.errors:
                console.print(f"[red]Errors:[/red]")
                for error in status.errors:
                    console.print(f"  - {error}")
            raise typer.Exit(1)
            
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {str(e)}")
        raise typer.Exit(1)


@app.command()
def update_data(
    sources: List[str] = typer.Argument(..., help="Data sources to update (wfp, acled, acaps, hdx)"),
    force: bool = typer.Option(False, "--force", "-f", help="Force re-download")
):
    """Update specific data sources."""
    console.print(f"[bold blue]Updating data sources: {', '.join(sources)}[/bold blue]")
    
    # Validate sources
    valid_sources = {"wfp", "acled", "acaps", "hdx"}
    invalid = set(sources) - valid_sources
    if invalid:
        console.print(f"[bold red]Invalid sources:[/bold red] {', '.join(invalid)}")
        console.print(f"Valid sources are: {', '.join(valid_sources)}")
        raise typer.Exit(1)
    
    # Get container and orchestrator
    container = Container()
    ingestion_service = container.application.data_ingestion_service()
    ingestion_orchestrator = container.application.ingestion_orchestrator()
    panel_builder_service = container.application.panel_builder_service()
    
    orchestrator = DataPipelineOrchestrator(
        ingestion_service=ingestion_service,
        ingestion_orchestrator=ingestion_orchestrator,
        panel_builder_service=panel_builder_service
    )
    
    try:
        # Run updates
        import asyncio
        results = asyncio.run(orchestrator.run_data_update(sources, force_refresh=force))
        
        # Show results
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Source", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Records", style="yellow")
        table.add_column("Time (s)", style="blue")
        
        for source, result in results.items():
            status = "✓ Success" if result.success else "✗ Failed"
            table.add_row(
                source,
                status,
                f"{result.records_saved:,}" if result.success else "-",
                f"{result.processing_time_seconds:.1f}"
            )
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {str(e)}")
        raise typer.Exit(1)


@app.command()
def validate_data(
    data_path: Optional[Path] = typer.Option(None, help="Path to data to validate"),
    check_currency: bool = typer.Option(True, help="Check currency conversion"),
    check_zones: bool = typer.Option(True, help="Check zone classification"),
    check_coverage: bool = typer.Option(True, help="Check data coverage")
):
    """Validate data quality and methodology compliance."""
    console.print(f"[bold blue]Validating data...[/bold blue]")
    
    # Default to processed data
    if not data_path:
        data_path = Path("data/processed/integrated_panel/yemen_integrated_balanced_panel.parquet")
    
    if not data_path.exists():
        console.print(f"[bold red]Error:[/bold red] Data file not found: {data_path}")
        raise typer.Exit(1)
    
    # Load data
    import pandas as pd
    df = pd.read_parquet(data_path)
    console.print(f"Loaded {len(df):,} observations")
    
    # Run validation
    from ...core.validation.methodology_validator import MethodologyValidator
    validator = MethodologyValidator()
    
    is_valid, report = validator.validate_analysis_inputs(
        observations=df,
        analysis_type="panel_analysis"
    )
    
    # Show results
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Check", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Details", style="yellow")
    
    # Currency check
    if check_currency:
        currency_valid = report.currency_status.all_prices_in_usd
        table.add_row(
            "Currency Conversion",
            "✓ Pass" if currency_valid else "✗ Fail",
            f"{report.currency_status.usd_count}/{report.currency_status.total_count} in USD"
        )
    
    # Zone check
    if check_zones:
        zone_valid = report.zone_status.all_zones_classified
        table.add_row(
            "Zone Classification",
            "✓ Pass" if zone_valid else "✗ Fail",
            f"{report.zone_status.classified_count}/{report.zone_status.total_count} classified"
        )
    
    # Coverage check
    if check_coverage:
        coverage = len(df) / (300 * 36 * 12)  # markets * months * commodities
        coverage_valid = coverage >= 0.884
        table.add_row(
            "Data Coverage",
            "✓ Pass" if coverage_valid else "✗ Fail",
            f"{coverage:.1%} (target: 88.4%)"
        )
    
    console.print(table)
    
    if report.critical_failures:
        console.print(f"[bold red]Critical failures:[/bold red]")
        for failure in report.critical_failures:
            console.print(f"  - {failure}")
    
    if report.warnings:
        console.print(f"[yellow]Warnings:[/yellow]")
        for warning in report.warnings:
            console.print(f"  - {warning}")
    
    if is_valid:
        console.print(f"[bold green]Data validation passed![/bold green]")
    else:
        console.print(f"[bold red]Data validation failed![/bold red]")
        raise typer.Exit(1)


if __name__ == "__main__":
    app()