"""
Processor for geospatial and raster data sources.

This processor handles data from sources like:
- Climate data (CHIRPS rainfall, MODIS NDVI)
- Elevation models (SRTM)
- Population density (WorldPop)
- Land cover classifications

It extracts values at market locations and aggregates temporally.
"""

import asyncio
from abc import abstractmethod
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any
import numpy as np
import pandas as pd
import rasterio
from rasterio.mask import mask
from rasterio.warp import calculate_default_transform, reproject, Resampling
import xarray as xr
import geopandas as gpd
from shapely.geometry import Point, box
import structlog

from src.infrastructure.processors.base_processor import BaseProcessor, SourceConfig
from src.infrastructure.data_quality.validation_framework import ValidationReport, ValidationLevel
from src.core.domain.market.entities import Market
from src.core.domain.market.value_objects import Coordinates
from src.core.domain.climate.entities import ClimateObservation, ClimateMetrics
from src.core.domain.shared.value_objects import TemporalKey


logger = structlog.get_logger()


@dataclass
class RasterConfig:
    """Configuration for raster data processing."""
    crs: str = "EPSG:4326"  # Coordinate reference system
    buffer_radius_m: float = 10000  # Buffer around points in meters
    aggregation_method: str = "mean"  # mean, sum, max, min
    nodata_value: Optional[float] = None
    scale_factor: float = 1.0
    resampling_method: str = "bilinear"


@dataclass
class RasterMetadata:
    """Metadata extracted from raster files."""
    crs: str
    bounds: Tuple[float, float, float, float]
    resolution: Tuple[float, float]
    shape: Tuple[int, int]
    dtype: str
    nodata_value: Optional[float]
    temporal_info: Optional[Dict[str, Any]] = None


class GeoDataProcessor(BaseProcessor[Union[Path, xr.Dataset], ClimateObservation, pd.DataFrame]):
    """
    Base processor for geospatial raster data.
    
    Handles extraction of values at specific locations (markets) and
    temporal aggregation for time series raster data.
    
    Features:
        - Point extraction with optional buffering
        - CRS transformation and alignment
        - Temporal aggregation for multi-band rasters
        - NetCDF/GeoTIFF support
        - Memory-efficient chunked processing
    """
    
    def __init__(
        self,
        source_config: SourceConfig,
        cache_manager,
        validator,
        markets: List[Market],
        raster_config: Optional[RasterConfig] = None
    ):
        super().__init__(source_config, cache_manager, validator)
        self.markets = markets
        self.raster_config = raster_config or RasterConfig()
        self.market_geometries = self._prepare_market_geometries()
    
    def _prepare_market_geometries(self) -> gpd.GeoDataFrame:
        """Prepare market locations as GeoDataFrame with buffers."""
        geometries = []
        
        for market in self.markets:
            point = Point(market.location.longitude, market.location.latitude)
            geometries.append({
                'market_id': market.id,
                'geometry': point,
                'buffer': point.buffer(
                    self.raster_config.buffer_radius_m / 111000  # Rough degrees conversion
                )
            })
        
        gdf = gpd.GeoDataFrame(geometries, crs=self.raster_config.crs)
        return gdf
    
    async def extract_values_at_points(
        self,
        raster_path: Path,
        use_buffer: bool = True
    ) -> Dict[str, float]:
        """
        Extract raster values at market locations.
        
        Args:
            raster_path: Path to raster file
            use_buffer: Whether to use buffer around points
            
        Returns:
            Dictionary mapping market_id to extracted value
        """
        values = {}
        
        with rasterio.open(raster_path) as src:
            # Check if reprojection needed
            if src.crs != self.raster_config.crs:
                # Reproject points to raster CRS
                gdf_reprojected = self.market_geometries.to_crs(src.crs)
            else:
                gdf_reprojected = self.market_geometries
            
            for _, row in gdf_reprojected.iterrows():
                market_id = row['market_id']
                
                if use_buffer:
                    geom = row['buffer']
                else:
                    geom = row['geometry']
                
                try:
                    # Extract values within geometry
                    out_image, out_transform = mask(
                        src,
                        [geom],
                        crop=True,
                        nodata=self.raster_config.nodata_value
                    )
                    
                    # Get first band
                    data = out_image[0]
                    
                    # Filter out nodata
                    if self.raster_config.nodata_value is not None:
                        valid_data = data[data != self.raster_config.nodata_value]
                    else:
                        valid_data = data[~np.isnan(data)]
                    
                    if valid_data.size > 0:
                        # Aggregate based on method
                        if self.raster_config.aggregation_method == "mean":
                            value = float(np.mean(valid_data))
                        elif self.raster_config.aggregation_method == "sum":
                            value = float(np.sum(valid_data))
                        elif self.raster_config.aggregation_method == "max":
                            value = float(np.max(valid_data))
                        elif self.raster_config.aggregation_method == "min":
                            value = float(np.min(valid_data))
                        else:
                            value = float(np.mean(valid_data))
                        
                        # Apply scale factor
                        value *= self.raster_config.scale_factor
                        values[market_id] = value
                    else:
                        self.logger.warning(
                            "no_valid_data",
                            market_id=market_id,
                            raster=str(raster_path)
                        )
                        
                except Exception as e:
                    self.logger.error(
                        "extraction_error",
                        market_id=market_id,
                        error=str(e)
                    )
        
        return values
    
    async def process_netcdf(self, nc_path: Path) -> pd.DataFrame:
        """
        Process NetCDF file with time dimension.
        
        Args:
            nc_path: Path to NetCDF file
            
        Returns:
            DataFrame with extracted time series
        """
        ds = xr.open_dataset(nc_path)
        
        # Find time and data variables
        time_var = None
        data_var = None
        
        for var in ds.dims:
            if var in ['time', 'date', 't']:
                time_var = var
                break
        
        # Get data variable (exclude coordinates)
        for var in ds.data_vars:
            data_var = var
            break
        
        if not time_var or not data_var:
            raise ValueError("Could not identify time or data variables in NetCDF")
        
        results = []
        
        # Process each time step
        for time_idx in range(len(ds[time_var])):
            time_value = pd.to_datetime(ds[time_var].values[time_idx])
            
            # Extract data for this time
            data_array = ds[data_var].isel({time_var: time_idx})
            
            # Convert to temporary raster for extraction
            # This is a simplified approach - real implementation would be more sophisticated
            for market in self.markets:
                lat = market.location.latitude
                lon = market.location.longitude
                
                # Find nearest grid cell
                lat_idx = np.abs(ds.lat.values - lat).argmin()
                lon_idx = np.abs(ds.lon.values - lon).argmin()
                
                value = float(data_array.values[lat_idx, lon_idx])
                
                results.append({
                    'market_id': market.id,
                    'date': time_value,
                    'value': value * self.raster_config.scale_factor,
                    'variable': data_var
                })
        
        ds.close()
        return pd.DataFrame(results)
    
    async def get_raster_metadata(self, raster_path: Path) -> RasterMetadata:
        """Extract metadata from raster file."""
        with rasterio.open(raster_path) as src:
            return RasterMetadata(
                crs=str(src.crs),
                bounds=src.bounds,
                resolution=(src.res[0], src.res[1]),
                shape=(src.height, src.width),
                dtype=str(src.dtypes[0]),
                nodata_value=src.nodata
            )
    
    async def validate(self, raw_data: Union[Path, xr.Dataset]) -> ValidationReport:
        """Validate raster data."""
        report = ValidationReport(source=self.config.source_id)
        
        if isinstance(raw_data, Path):
            if not raw_data.exists():
                report.add_validation(
                    "file",
                    ValidationLevel.ERROR,
                    f"File not found: {raw_data}"
                )
                return report
            
            try:
                metadata = await self.get_raster_metadata(raw_data)
                
                # Check CRS
                if metadata.crs != self.raster_config.crs:
                    report.add_validation(
                        "crs",
                        ValidationLevel.WARNING,
                        f"CRS mismatch: {metadata.crs} vs expected {self.raster_config.crs}"
                    )
                
                # Check bounds overlap with Yemen
                yemen_bounds = (42.0, 12.0, 54.0, 19.0)  # Approximate
                if not self._bounds_overlap(metadata.bounds, yemen_bounds):
                    report.add_validation(
                        "bounds",
                        ValidationLevel.ERROR,
                        "Raster does not overlap with Yemen"
                    )
                
                # Check resolution
                if metadata.resolution[0] > 0.1:  # More than ~10km
                    report.add_validation(
                        "resolution",
                        ValidationLevel.WARNING,
                        f"Low resolution: {metadata.resolution[0]} degrees"
                    )
                    
            except Exception as e:
                report.add_validation(
                    "file",
                    ValidationLevel.ERROR,
                    f"Cannot read raster: {str(e)}"
                )
        
        return report
    
    def _bounds_overlap(
        self,
        bounds1: Tuple[float, float, float, float],
        bounds2: Tuple[float, float, float, float]
    ) -> bool:
        """Check if two bounding boxes overlap."""
        return not (
            bounds1[2] < bounds2[0] or  # bounds1 max x < bounds2 min x
            bounds1[0] > bounds2[2] or  # bounds1 min x > bounds2 max x
            bounds1[3] < bounds2[1] or  # bounds1 max y < bounds2 min y
            bounds1[1] > bounds2[3]     # bounds1 min y > bounds2 max y
        )
    
    async def transform(self, raw_data: Union[Path, xr.Dataset]) -> List[ClimateObservation]:
        """Transform raster data to climate observations."""
        observations = []
        
        if isinstance(raw_data, Path):
            if raw_data.suffix == '.nc':
                # NetCDF with time series
                df = await self.process_netcdf(raw_data)
                
                for _, row in df.iterrows():
                    market = next(m for m in self.markets if m.id == row['market_id'])
                    
                    obs = ClimateObservation(
                        observation_date=row['date'],
                        location=market.location,
                        data_source=self.config.source_id
                    )
                    
                    # Map variable to appropriate field
                    if 'rainfall' in row['variable'].lower():
                        obs.rainfall_mm = row['value']
                    elif 'ndvi' in row['variable'].lower():
                        obs.ndvi = row['value']
                    elif 'temp' in row['variable'].lower():
                        obs.temperature_celsius = row['value']
                    
                    observations.append(obs)
            else:
                # Single raster (GeoTIFF)
                values = await self.extract_values_at_points(raw_data)
                
                # Assume current date if not encoded in filename
                obs_date = datetime.now()
                
                for market_id, value in values.items():
                    market = next(m for m in self.markets if m.id == market_id)
                    
                    obs = ClimateObservation(
                        observation_date=obs_date,
                        location=market.location,
                        data_source=self.config.source_id
                    )
                    
                    # Determine which field based on source type
                    if 'rainfall' in self.config.source_id.lower():
                        obs.rainfall_mm = value
                    elif 'ndvi' in self.config.source_id.lower():
                        obs.ndvi = value
                    elif 'temp' in self.config.source_id.lower():
                        obs.temperature_celsius = value
                    
                    observations.append(obs)
        
        return observations
    
    async def aggregate(self, entities: List[ClimateObservation]) -> pd.DataFrame:
        """Aggregate climate observations to monthly panel."""
        # Convert to DataFrame
        records = []
        for obs in entities:
            # Find market ID from location
            market = next(
                (m for m in self.markets 
                 if m.location.latitude == obs.location.latitude 
                 and m.location.longitude == obs.location.longitude),
                None
            )
            
            if market:
                record = {
                    'market_id': market.id,
                    'date': obs.observation_date,
                    'year': obs.observation_date.year,
                    'month': obs.observation_date.month,
                    'rainfall_mm': obs.rainfall_mm,
                    'temperature_celsius': obs.temperature_celsius,
                    'ndvi': obs.ndvi,
                    'data_source': obs.data_source
                }
                records.append(record)
        
        df = pd.DataFrame(records)
        
        if df.empty:
            return df
        
        # Create temporal key
        df['temporal_key'] = df.apply(
            lambda r: f"{r['year']}-{r['month']:02d}",
            axis=1
        )
        
        # Aggregate to monthly
        agg_dict = {}
        for col in ['rainfall_mm', 'temperature_celsius', 'ndvi']:
            if col in df.columns:
                if col == 'rainfall_mm':
                    agg_dict[col] = 'sum'  # Total monthly rainfall
                else:
                    agg_dict[col] = 'mean'  # Average temperature/NDVI
        
        agg_dict['date'] = 'count'  # Number of observations
        
        monthly = df.groupby(['market_id', 'temporal_key', 'year', 'month']).agg(
            agg_dict
        ).reset_index()
        
        monthly.rename(columns={'date': 'n_observations'}, inplace=True)
        
        return monthly


class CHIRPSProcessor(GeoDataProcessor):
    """
    Processor for CHIRPS rainfall data.
    
    CHIRPS (Climate Hazards Group InfraRed Precipitation with Station) provides
    quasi-global rainfall data at 0.05° resolution.
    """
    
    async def download(self) -> Path:
        """Download CHIRPS data for Yemen."""
        # In practice, this would download from:
        # https://data.chc.ucsb.edu/products/CHIRPS-2.0/
        
        # For now, return a mock path
        return Path("/data/chirps/yemen_rainfall_2024.nc")


class MODISNDVIProcessor(GeoDataProcessor):
    """
    Processor for MODIS NDVI data.
    
    Processes Normalized Difference Vegetation Index data for vegetation
    health monitoring.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # NDVI specific config
        self.raster_config.scale_factor = 0.0001  # MODIS scale factor
        self.raster_config.nodata_value = -3000
    
    async def download(self) -> Path:
        """Download MODIS NDVI data."""
        # Would download from NASA Earthdata
        return Path("/data/modis/yemen_ndvi_2024.tif")