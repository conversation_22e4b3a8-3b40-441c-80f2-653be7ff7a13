"""
Error recovery strategies for data pipeline processors.

This module provides sophisticated error recovery mechanisms that work
with the exception hierarchy to automatically recover from common errors
without manual intervention.

Key Features:
    - Automatic retry with exponential backoff
    - Circuit breaker pattern for failing services
    - Fallback data sources
    - Partial processing recovery
    - Error aggregation and reporting

Example Usage:
    ```python
    recovery_manager = ErrorRecoveryManager()
    
    @recovery_manager.with_recovery(
        max_retries=3,
        backoff_factor=2.0,
        recoverable_exceptions=(APIException, TimeoutException)
    )
    async def download_data():
        return await client.get_data()
    
    # Or use specific recovery strategies
    async with recovery_manager.circuit_breaker("wfp_api"):
        data = await download_data()
    ```
"""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Callable, Any, Type, Set, Tuple
import structlog
from functools import wraps
import random

from src.infrastructure.processors.exceptions import (
    ProcessorException, ErrorSeverity, APIException, 
    TimeoutException, ValidationException, ErrorContext
)


logger = structlog.get_logger()


class RecoveryStrategy(Enum):
    """Available recovery strategies."""
    RETRY = "retry"
    CIRCUIT_BREAKER = "circuit_breaker"
    FALLBACK = "fallback"
    SKIP = "skip"
    PARTIAL = "partial"
    QUEUE = "queue"


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"     # Normal operation
    OPEN = "open"         # Failing, reject requests
    HALF_OPEN = "half_open"  # Testing recovery


@dataclass
class RecoveryConfig:
    """Configuration for error recovery."""
    max_retries: int = 3
    initial_backoff: float = 1.0
    max_backoff: float = 60.0
    backoff_factor: float = 2.0
    jitter: bool = True
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: timedelta = timedelta(minutes=5)
    partial_processing_threshold: float = 0.8  # Process if 80% successful
    recoverable_exceptions: Tuple[Type[Exception], ...] = (
        APIException, TimeoutException
    )


@dataclass
class RecoveryResult:
    """Result of a recovery attempt."""
    success: bool
    strategy_used: RecoveryStrategy
    attempts: int
    duration: float
    final_error: Optional[ProcessorException] = None
    partial_data: Optional[Any] = None
    recovery_metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CircuitBreakerState:
    """State tracking for circuit breaker."""
    state: CircuitState = CircuitState.CLOSED
    failure_count: int = 0
    last_failure_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None
    consecutive_successes: int = 0
    total_requests: int = 0
    total_failures: int = 0


class ErrorRecoveryManager:
    """
    Manages error recovery strategies for processors.
    
    Provides multiple recovery patterns that can be combined:
    - Exponential backoff retry
    - Circuit breaker for failing services
    - Fallback to alternative sources
    - Partial processing when some data fails
    - Error queuing for later retry
    """
    
    def __init__(self, default_config: Optional[RecoveryConfig] = None):
        self.config = default_config or RecoveryConfig()
        self.circuit_breakers: Dict[str, CircuitBreakerState] = {}
        self.error_queue: List[Tuple[datetime, ProcessorException]] = []
        self.recovery_stats: Dict[str, Dict[str, int]] = {}
        self.logger = logger.bind(component="ErrorRecoveryManager")
    
    def with_recovery(
        self,
        max_retries: Optional[int] = None,
        backoff_factor: Optional[float] = None,
        recoverable_exceptions: Optional[Tuple[Type[Exception], ...]] = None
    ):
        """
        Decorator for automatic error recovery.
        
        Args:
            max_retries: Maximum retry attempts
            backoff_factor: Exponential backoff multiplier
            recoverable_exceptions: Exceptions that trigger retry
            
        Returns:
            Decorated function with automatic recovery
        """
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                config = RecoveryConfig(
                    max_retries=max_retries or self.config.max_retries,
                    backoff_factor=backoff_factor or self.config.backoff_factor,
                    recoverable_exceptions=recoverable_exceptions or self.config.recoverable_exceptions
                )
                
                return await self._execute_with_recovery(
                    func, args, kwargs, config
                )
            
            return wrapper
        return decorator
    
    async def _execute_with_recovery(
        self,
        func: Callable,
        args: tuple,
        kwargs: dict,
        config: RecoveryConfig
    ) -> Any:
        """Execute function with recovery logic."""
        attempts = 0
        backoff = config.initial_backoff
        last_error = None
        
        while attempts < config.max_retries:
            try:
                result = await func(*args, **kwargs)
                
                # Record success
                self._record_recovery_stats(func.__name__, "success", attempts)
                
                return result
                
            except config.recoverable_exceptions as e:
                attempts += 1
                last_error = e
                
                self.logger.warning(
                    "recoverable_error",
                    function=func.__name__,
                    attempt=attempts,
                    max_attempts=config.max_retries,
                    error=str(e),
                    backoff_seconds=backoff
                )
                
                if attempts < config.max_retries:
                    # Apply backoff with optional jitter
                    sleep_time = backoff
                    if config.jitter:
                        sleep_time *= (0.5 + random.random())
                    
                    await asyncio.sleep(min(sleep_time, config.max_backoff))
                    
                    # Exponential backoff
                    backoff *= config.backoff_factor
                else:
                    # Final attempt failed
                    self._record_recovery_stats(func.__name__, "failed", attempts)
                    raise
            
            except Exception as e:
                # Non-recoverable error
                self.logger.error(
                    "non_recoverable_error",
                    function=func.__name__,
                    error=str(e)
                )
                self._record_recovery_stats(func.__name__, "non_recoverable", attempts)
                raise
        
        # Should not reach here, but for safety
        if last_error:
            raise last_error
    
    async def circuit_breaker(self, service_id: str):
        """
        Context manager for circuit breaker pattern.
        
        Args:
            service_id: Identifier for the service/endpoint
            
        Usage:
            ```python
            async with recovery_manager.circuit_breaker("wfp_api"):
                data = await download_from_wfp()
            ```
        """
        breaker = self._get_or_create_breaker(service_id)
        
        # Check circuit state
        if breaker.state == CircuitState.OPEN:
            if self._should_attempt_reset(breaker):
                breaker.state = CircuitState.HALF_OPEN
                self.logger.info("circuit_half_open", service=service_id)
            else:
                raise APIException(
                    f"Circuit breaker OPEN for {service_id}",
                    context=ErrorContext(
                        source_id=service_id,
                        processor_type="circuit_breaker",
                        stage="pre_check"
                    )
                )
        
        class CircuitBreakerContext:
            def __init__(context_self):
                context_self.breaker = breaker
                context_self.manager = self
                context_self.service_id = service_id
            
            async def __aenter__(context_self):
                breaker.total_requests += 1
                return context_self
            
            async def __aexit__(context_self, exc_type, exc_val, exc_tb):
                if exc_type is None:
                    # Success
                    context_self.manager._record_success(service_id)
                else:
                    # Failure
                    context_self.manager._record_failure(service_id, exc_val)
                
                return False  # Don't suppress exceptions
        
        return CircuitBreakerContext()
    
    def _get_or_create_breaker(self, service_id: str) -> CircuitBreakerState:
        """Get or create circuit breaker for service."""
        if service_id not in self.circuit_breakers:
            self.circuit_breakers[service_id] = CircuitBreakerState()
        return self.circuit_breakers[service_id]
    
    def _should_attempt_reset(self, breaker: CircuitBreakerState) -> bool:
        """Check if circuit breaker should attempt reset."""
        if not breaker.last_failure_time:
            return True
        
        time_since_failure = datetime.now() - breaker.last_failure_time
        return time_since_failure > self.config.circuit_breaker_timeout
    
    def _record_success(self, service_id: str):
        """Record successful request."""
        breaker = self.circuit_breakers[service_id]
        breaker.last_success_time = datetime.now()
        breaker.consecutive_successes += 1
        
        if breaker.state == CircuitState.HALF_OPEN:
            if breaker.consecutive_successes >= 3:
                breaker.state = CircuitState.CLOSED
                breaker.failure_count = 0
                breaker.consecutive_successes = 0
                self.logger.info("circuit_closed", service=service_id)
    
    def _record_failure(self, service_id: str, error: Exception):
        """Record failed request."""
        breaker = self.circuit_breakers[service_id]
        breaker.failure_count += 1
        breaker.total_failures += 1
        breaker.last_failure_time = datetime.now()
        breaker.consecutive_successes = 0
        
        if breaker.failure_count >= self.config.circuit_breaker_threshold:
            if breaker.state != CircuitState.OPEN:
                breaker.state = CircuitState.OPEN
                self.logger.error(
                    "circuit_opened",
                    service=service_id,
                    failure_count=breaker.failure_count,
                    total_failures=breaker.total_failures
                )
    
    def _record_recovery_stats(self, function: str, outcome: str, attempts: int):
        """Record recovery statistics."""
        if function not in self.recovery_stats:
            self.recovery_stats[function] = {
                "success": 0,
                "failed": 0,
                "non_recoverable": 0,
                "total_attempts": 0
            }
        
        self.recovery_stats[function][outcome] += 1
        self.recovery_stats[function]["total_attempts"] += attempts
    
    async def with_fallback(
        self,
        primary_func: Callable,
        fallback_func: Callable,
        fallback_on: Tuple[Type[Exception], ...] = (ProcessorException,)
    ) -> Any:
        """
        Execute with fallback option.
        
        Args:
            primary_func: Primary function to execute
            fallback_func: Fallback function if primary fails
            fallback_on: Exceptions that trigger fallback
            
        Returns:
            Result from primary or fallback function
        """
        try:
            return await primary_func()
        except fallback_on as e:
            self.logger.warning(
                "using_fallback",
                primary_error=str(e),
                fallback_func=fallback_func.__name__
            )
            return await fallback_func()
    
    async def partial_processing(
        self,
        items: List[Any],
        process_func: Callable[[Any], Any],
        threshold: Optional[float] = None
    ) -> Tuple[List[Any], List[Tuple[Any, Exception]]]:
        """
        Process items with partial failure tolerance.
        
        Args:
            items: Items to process
            process_func: Function to process each item
            threshold: Minimum success rate (0-1) to consider successful
            
        Returns:
            Tuple of (successful_results, failed_items_with_errors)
            
        Raises:
            ProcessingException: If success rate below threshold
        """
        threshold = threshold or self.config.partial_processing_threshold
        successful_results = []
        failed_items = []
        
        for item in items:
            try:
                result = await process_func(item)
                successful_results.append(result)
            except Exception as e:
                failed_items.append((item, e))
                self.logger.warning(
                    "partial_processing_item_failed",
                    item=str(item)[:100],
                    error=str(e)
                )
        
        success_rate = len(successful_results) / len(items) if items else 0
        
        self.logger.info(
            "partial_processing_complete",
            total_items=len(items),
            successful=len(successful_results),
            failed=len(failed_items),
            success_rate=success_rate
        )
        
        if success_rate < threshold:
            from src.infrastructure.processors.exceptions import ProcessingException
            raise ProcessingException(
                f"Partial processing failed: {success_rate:.1%} success rate "
                f"below threshold {threshold:.1%}"
            )
        
        return successful_results, failed_items
    
    def queue_error_for_retry(self, error: ProcessorException):
        """Queue an error for later retry."""
        self.error_queue.append((datetime.now(), error))
        
        # Limit queue size
        if len(self.error_queue) > 1000:
            self.error_queue = self.error_queue[-1000:]
    
    async def retry_queued_errors(
        self,
        retry_func: Callable[[ProcessorException], Any],
        max_age: timedelta = timedelta(hours=24)
    ) -> Tuple[int, int]:
        """
        Retry queued errors.
        
        Args:
            retry_func: Function to retry with error context
            max_age: Maximum age of errors to retry
            
        Returns:
            Tuple of (successful_retries, failed_retries)
        """
        current_time = datetime.now()
        errors_to_retry = [
            (timestamp, error) for timestamp, error in self.error_queue
            if current_time - timestamp < max_age
        ]
        
        successful = 0
        failed = 0
        
        for timestamp, error in errors_to_retry:
            try:
                await retry_func(error)
                successful += 1
                self.error_queue.remove((timestamp, error))
            except Exception as e:
                failed += 1
                self.logger.error(
                    "queued_retry_failed",
                    original_error=str(error),
                    retry_error=str(e)
                )
        
        return successful, failed
    
    def get_recovery_report(self) -> Dict[str, Any]:
        """Get comprehensive recovery statistics."""
        return {
            "recovery_stats": self.recovery_stats,
            "circuit_breakers": {
                service_id: {
                    "state": state.state.value,
                    "failure_count": state.failure_count,
                    "total_requests": state.total_requests,
                    "total_failures": state.total_failures,
                    "failure_rate": (
                        state.total_failures / state.total_requests 
                        if state.total_requests > 0 else 0
                    )
                }
                for service_id, state in self.circuit_breakers.items()
            },
            "error_queue_size": len(self.error_queue),
            "oldest_queued_error": (
                self.error_queue[0][0].isoformat() if self.error_queue else None
            )
        }


# Global recovery manager instance
recovery_manager = ErrorRecoveryManager()


# Convenience decorators

def with_retry(
    max_retries: int = 3,
    backoff_factor: float = 2.0,
    on_exceptions: Tuple[Type[Exception], ...] = (APIException, TimeoutException)
):
    """Decorator for automatic retry with backoff."""
    return recovery_manager.with_recovery(
        max_retries=max_retries,
        backoff_factor=backoff_factor,
        recoverable_exceptions=on_exceptions
    )


def with_circuit_breaker(service_id: str):
    """Decorator for circuit breaker protection."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            async with recovery_manager.circuit_breaker(service_id):
                return await func(*args, **kwargs)
        return wrapper
    return decorator