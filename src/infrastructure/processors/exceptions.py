"""
Domain-specific exceptions for data pipeline processors.

This module provides a comprehensive exception hierarchy for handling
various error conditions that can occur during data processing. Each
exception type includes context information to aid in debugging and
error recovery.

Exception Hierarchy:
    ProcessorException (base)
    ├── DownloadException
    │   ├── APIException
    │   ├── TimeoutException
    │   └── AuthenticationException
    ├── ValidationException
    │   ├── SchemaValidationException
    │   ├── DataQualityException
    │   └── BusinessRuleException
    ├── TransformationException
    │   ├── MappingException
    │   └── ConversionException
    ├── AggregationException
    └── ProcessingException

Example Usage:
    ```python
    try:
        response = await client.get_data()
    except TimeoutException as e:
        logger.error("Download timeout", 
                     source=e.source_id,
                     retry_count=e.retry_count,
                     timeout=e.timeout)
        raise
    except APIException as e:
        if e.is_retryable:
            return await retry_with_backoff()
        raise
    ```
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum


class ErrorSeverity(Enum):
    """Severity levels for processor errors."""
    LOW = "low"          # Can continue processing
    MEDIUM = "medium"    # May affect results
    HIGH = "high"        # Should stop processing
    CRITICAL = "critical" # Must stop immediately


class ErrorCategory(Enum):
    """Categories of processor errors."""
    NETWORK = "network"
    DATA_QUALITY = "data_quality"
    CONFIGURATION = "configuration"
    RESOURCE = "resource"
    BUSINESS_LOGIC = "business_logic"
    SYSTEM = "system"


@dataclass
class ErrorContext:
    """
    Context information for processor errors.
    
    Provides detailed information about where and when an error occurred,
    which is crucial for debugging and monitoring.
    """
    source_id: str
    processor_type: str
    stage: str  # download, validate, transform, aggregate
    timestamp: datetime = field(default_factory=datetime.now)
    retry_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging."""
        return {
            "source_id": self.source_id,
            "processor_type": self.processor_type,
            "stage": self.stage,
            "timestamp": self.timestamp.isoformat(),
            "retry_count": self.retry_count,
            **self.metadata
        }


class ProcessorException(Exception):
    """
    Base exception for all processor errors.
    
    Provides common functionality for error tracking, context preservation,
    and recovery suggestions.
    """
    
    def __init__(
        self,
        message: str,
        context: Optional[ErrorContext] = None,
        severity: ErrorSeverity = ErrorSeverity.HIGH,
        category: ErrorCategory = ErrorCategory.SYSTEM,
        is_retryable: bool = False,
        recovery_suggestions: Optional[List[str]] = None,
        cause: Optional[Exception] = None
    ):
        super().__init__(message)
        self.context = context
        self.severity = severity
        self.category = category
        self.is_retryable = is_retryable
        self.recovery_suggestions = recovery_suggestions or []
        self.cause = cause
        self.timestamp = datetime.now()
    
    def add_context(self, **kwargs) -> 'ProcessorException':
        """Add additional context to the exception."""
        if self.context:
            self.context.metadata.update(kwargs)
        return self
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for structured logging."""
        return {
            "error_type": self.__class__.__name__,
            "message": str(self),
            "severity": self.severity.value,
            "category": self.category.value,
            "is_retryable": self.is_retryable,
            "timestamp": self.timestamp.isoformat(),
            "context": self.context.to_dict() if self.context else None,
            "recovery_suggestions": self.recovery_suggestions,
            "cause": str(self.cause) if self.cause else None
        }


# Download Exceptions

class DownloadException(ProcessorException):
    """Base exception for download-related errors."""
    
    def __init__(self, message: str, url: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.NETWORK,
            **kwargs
        )
        if url:
            self.add_context(url=url)


class APIException(DownloadException):
    """Exception for API-related errors."""
    
    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_body: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        if status_code:
            self.add_context(status_code=status_code)
            # Determine if retryable based on status code
            if status_code in [429, 500, 502, 503, 504]:
                self.is_retryable = True
                self.recovery_suggestions = [
                    "Wait and retry with exponential backoff",
                    "Check API rate limits",
                    "Verify API service status"
                ]
        if response_body:
            self.add_context(response_preview=response_body[:200])


class TimeoutException(DownloadException):
    """Exception for timeout errors."""
    
    def __init__(self, message: str, timeout: float, **kwargs):
        super().__init__(
            message,
            is_retryable=True,
            **kwargs
        )
        self.timeout = timeout
        self.add_context(timeout_seconds=timeout)
        self.recovery_suggestions = [
            f"Increase timeout beyond {timeout} seconds",
            "Check network connectivity",
            "Try downloading in smaller chunks"
        ]


class AuthenticationException(DownloadException):
    """Exception for authentication/authorization errors."""
    
    def __init__(self, message: str, auth_type: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            is_retryable=False,
            severity=ErrorSeverity.CRITICAL,
            **kwargs
        )
        if auth_type:
            self.add_context(auth_type=auth_type)
        self.recovery_suggestions = [
            "Verify API credentials are correct",
            "Check if API key has expired",
            "Ensure proper permissions are granted"
        ]


# Validation Exceptions

class ValidationException(ProcessorException):
    """Base exception for validation errors."""
    
    def __init__(
        self,
        message: str,
        validation_errors: Optional[List[str]] = None,
        **kwargs
    ):
        super().__init__(
            message,
            category=ErrorCategory.DATA_QUALITY,
            **kwargs
        )
        self.validation_errors = validation_errors or []
        if validation_errors:
            self.add_context(error_count=len(validation_errors))


class SchemaValidationException(ValidationException):
    """Exception for schema validation failures."""
    
    def __init__(
        self,
        message: str,
        missing_columns: Optional[List[str]] = None,
        unexpected_columns: Optional[List[str]] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        if missing_columns:
            self.add_context(missing_columns=missing_columns)
            self.recovery_suggestions.append(
                f"Add required columns: {', '.join(missing_columns)}"
            )
        if unexpected_columns:
            self.add_context(unexpected_columns=unexpected_columns)


class DataQualityException(ValidationException):
    """Exception for data quality issues."""
    
    def __init__(
        self,
        message: str,
        quality_metrics: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.quality_metrics = quality_metrics or {}
        if quality_metrics:
            self.add_context(**quality_metrics)
            
            # Add specific suggestions based on metrics
            if quality_metrics.get('null_percentage', 0) > 50:
                self.recovery_suggestions.append(
                    "High null percentage detected - verify data source"
                )
            if quality_metrics.get('duplicate_count', 0) > 0:
                self.recovery_suggestions.append(
                    "Remove duplicate records before processing"
                )


class BusinessRuleException(ValidationException):
    """Exception for business rule violations."""
    
    def __init__(self, message: str, rule_name: str, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.BUSINESS_LOGIC,
            **kwargs
        )
        self.rule_name = rule_name
        self.add_context(violated_rule=rule_name)


# Transformation Exceptions

class TransformationException(ProcessorException):
    """Base exception for transformation errors."""
    
    def __init__(self, message: str, record_info: Optional[Dict[str, Any]] = None, **kwargs):
        super().__init__(message, **kwargs)
        if record_info:
            self.add_context(failed_record=record_info)


class MappingException(TransformationException):
    """Exception for field mapping errors."""
    
    def __init__(
        self,
        message: str,
        source_field: str,
        target_field: str,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.source_field = source_field
        self.target_field = target_field
        self.add_context(
            mapping=f"{source_field} -> {target_field}"
        )
        self.recovery_suggestions = [
            f"Verify '{source_field}' exists in source data",
            f"Check mapping configuration for '{target_field}'",
            "Review field transformation logic"
        ]


class ConversionException(TransformationException):
    """Exception for data type conversion errors."""
    
    def __init__(
        self,
        message: str,
        from_type: str,
        to_type: str,
        value: Any,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.add_context(
            conversion=f"{from_type} -> {to_type}",
            value=str(value)[:100]  # Truncate large values
        )
        self.recovery_suggestions = [
            f"Verify value '{value}' can be converted to {to_type}",
            "Check for null or missing values",
            "Review data type expectations"
        ]


# Aggregation Exceptions

class AggregationException(ProcessorException):
    """Exception for aggregation errors."""
    
    def __init__(
        self,
        message: str,
        group_by: Optional[List[str]] = None,
        aggregation_func: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        if group_by:
            self.add_context(group_by_fields=group_by)
        if aggregation_func:
            self.add_context(aggregation_function=aggregation_func)


# Processing Exceptions

class ProcessingException(ProcessorException):
    """General processing exception for unclassified errors."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, **kwargs)
        self.recovery_suggestions = [
            "Check system resources (memory, disk space)",
            "Review processing logs for details",
            "Verify all dependencies are available"
        ]


# Resource Exceptions

class ResourceException(ProcessorException):
    """Exception for resource-related issues."""
    
    def __init__(
        self,
        message: str,
        resource_type: str,
        current_usage: Optional[float] = None,
        limit: Optional[float] = None,
        **kwargs
    ):
        super().__init__(
            message,
            category=ErrorCategory.RESOURCE,
            severity=ErrorSeverity.CRITICAL,
            **kwargs
        )
        self.resource_type = resource_type
        self.add_context(
            resource_type=resource_type,
            current_usage=current_usage,
            limit=limit
        )
        
        if resource_type == "memory":
            self.recovery_suggestions = [
                "Process data in smaller chunks",
                "Increase available memory",
                "Enable disk-based processing"
            ]
        elif resource_type == "disk":
            self.recovery_suggestions = [
                "Free up disk space",
                "Use compression for cached data",
                "Clean up temporary files"
            ]


# Configuration Exceptions

class ConfigurationException(ProcessorException):
    """Exception for configuration errors."""
    
    def __init__(
        self,
        message: str,
        config_key: str,
        expected_type: Optional[str] = None,
        **kwargs
    ):
        super().__init__(
            message,
            category=ErrorCategory.CONFIGURATION,
            severity=ErrorSeverity.CRITICAL,
            is_retryable=False,
            **kwargs
        )
        self.config_key = config_key
        self.add_context(config_key=config_key)
        if expected_type:
            self.add_context(expected_type=expected_type)
            self.recovery_suggestions.append(
                f"Ensure '{config_key}' is of type {expected_type}"
            )


# Helper function for creating contextualized exceptions

def create_processor_exception(
    error: Exception,
    context: ErrorContext,
    processor_type: str
) -> ProcessorException:
    """
    Create a processor exception from a generic exception.
    
    This helper function wraps generic exceptions with processor-specific
    context, making debugging easier.
    
    Args:
        error: The original exception
        context: Error context information
        processor_type: Type of processor where error occurred
        
    Returns:
        Appropriate ProcessorException subclass
    """
    error_message = str(error)
    
    # Map common error types to specific exceptions
    if isinstance(error, TimeoutError):
        return TimeoutException(
            f"Timeout in {processor_type}: {error_message}",
            timeout=30.0,  # Default, should be overridden
            context=context
        )
    elif isinstance(error, ConnectionError):
        return APIException(
            f"Connection error in {processor_type}: {error_message}",
            context=context,
            is_retryable=True
        )
    elif isinstance(error, ValueError) and "invalid" in error_message.lower():
        return ValidationException(
            f"Validation error in {processor_type}: {error_message}",
            context=context
        )
    elif isinstance(error, KeyError):
        return MappingException(
            f"Missing field in {processor_type}: {error_message}",
            source_field=str(error).strip("'\""),
            target_field="unknown",
            context=context
        )
    elif isinstance(error, MemoryError):
        return ResourceException(
            f"Memory error in {processor_type}: {error_message}",
            resource_type="memory",
            context=context
        )
    else:
        return ProcessingException(
            f"Unexpected error in {processor_type}: {error_message}",
            context=context,
            cause=error
        )