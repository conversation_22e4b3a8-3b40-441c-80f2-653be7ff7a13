# Data Processors Framework

This directory contains the data processing framework for integrating various data sources into the Yemen Market Integration analysis platform.

## Overview

The processor framework provides a consistent, robust approach to:
- Downloading data from various sources (APIs, files, databases)
- Validating data quality and integrity
- Transforming raw data into domain entities
- Aggregating data to the required panel frequency
- Caching results to minimize API calls
- Handling errors with retry logic

## Architecture

```
BaseProcessor<T, E, A>
├── Download (with retry)
├── Validate (multi-level)
├── Transform (to entities)
├── Aggregate (to panel)
└── Cache (intelligent)

DataFrameProcessor extends BaseProcessor
└── Specialized for tabular data
```

## Key Components

### BaseProcessor

The abstract base class that all processors extend:

```python
class BaseProcessor(ABC, Generic[T, E, A]):
    """
    T = Raw data type (e.g., DataFrame, Dict, Response)
    E = Entity type (e.g., Price, ConflictEvent)
    A = Aggregated type (e.g., DataFrame, PanelData)
    """
```

### DataFrameProcessor

Specialized processor for tabular data sources:

```python
class DataFrameProcessor(BaseProcessor[pd.DataFrame, E, pd.DataFrame]):
    """Handles CSV, Excel, and other tabular formats"""
```

### SourceConfig

Configuration for each data source:

```python
config = SourceConfig(
    source_id="wfp_prices",
    source_type="csv",
    update_frequency="weekly",
    cache_ttl=86400,  # 24 hours
    validation_level=ValidationLevel.STRICT,
    retry_attempts=3,
    timeout=300
)
```

## Creating a New Processor

### 1. Define Your Processor Class

```python
from typing import List
import pandas as pd
from src.infrastructure.processors.base_processor import DataFrameProcessor
from src.core.domain.market.entities import Price

class WFPPriceProcessor(DataFrameProcessor[Price]):
    """Processor for WFP food price data."""
    
    def __init__(self, config, cache_manager, validator, wfp_client):
        super().__init__(config, cache_manager, validator)
        self.client = wfp_client
```

### 2. Implement Required Methods

```python
async def download(self) -> pd.DataFrame:
    """Download price data from WFP API."""
    return await self.client.get_prices(
        country="Yemen",
        start_date=self.start_date,
        end_date=self.end_date
    )

async def transform(self, raw_data: pd.DataFrame) -> List[Price]:
    """Convert DataFrame rows to Price entities."""
    prices = []
    for _, row in raw_data.iterrows():
        price = Price(
            market_id=row['market_code'],
            commodity_id=row['commodity_code'],
            value=Decimal(str(row['price'])),
            currency=row['currency'],
            date=pd.to_datetime(row['date']),
            unit=row['unit']
        )
        prices.append(price)
    return prices

async def aggregate(self, entities: List[Price]) -> pd.DataFrame:
    """Aggregate to monthly panel data."""
    df = pd.DataFrame([e.to_dict() for e in entities])
    
    # Create temporal key
    df['year_month'] = df['date'].dt.to_period('M')
    
    # Aggregate by market-commodity-month
    monthly = df.groupby(['market_id', 'commodity_id', 'year_month']).agg({
        'value': 'mean',
        'observations': 'count'
    }).reset_index()
    
    return monthly

def get_required_columns(self) -> List[str]:
    """Specify required columns for validation."""
    return ['date', 'market_code', 'commodity_code', 'price', 'currency', 'unit']

async def validate_specific(self, raw_data: pd.DataFrame) -> ValidationReport:
    """WFP-specific validation."""
    report = ValidationReport(source=self.config.source_id)
    
    # Check currency values
    valid_currencies = ['YER', 'USD']
    invalid = ~raw_data['currency'].isin(valid_currencies)
    if invalid.any():
        report.add_error(f"Invalid currencies: {raw_data[invalid]['currency'].unique()}")
    
    # Check price ranges
    if (raw_data['price'] < 0).any():
        report.add_error("Negative prices found")
    
    if (raw_data['price'] > 1000000).any():
        report.add_warning("Extremely high prices found - possible data error")
    
    return report
```

### 3. Use Your Processor

```python
# Configure
config = SourceConfig(
    source_id="wfp_prices",
    source_type="api",
    update_frequency="weekly",
    cache_ttl=86400
)

# Initialize
processor = WFPPriceProcessor(
    config=config,
    cache_manager=cache_manager,
    validator=validator,
    wfp_client=wfp_client
)

# Process with progress tracking
def print_progress(stage: str, pct: float):
    print(f"[{pct:3.0f}%] {stage}")

result = await processor.process(
    force_refresh=False,
    progress_callback=print_progress
)

if result.success:
    print(f"Success! Processed {len(result.data)} records")
    print(f"Warnings: {result.validation_report.warnings}")
else:
    print(f"Failed: {result.error_summary}")
```

## Existing Processors

### ConflictProcessor
- **Source**: ACLED conflict events
- **Entity**: ConflictEvent
- **Features**: Spatial buffering, temporal aggregation

### WFPProcessor
- **Source**: WFP food prices
- **Entity**: Price
- **Features**: Currency conversion, unit standardization

### ACAPSProcessor
- **Source**: ACAPS control zones
- **Entity**: ControlZone
- **Features**: Shapefile processing, nested zip handling

### CurrencyAwareWFPProcessor
- **Source**: WFP with exchange rates
- **Entity**: Price with exchange rates
- **Features**: Zone-specific rate extraction

## Best Practices

### 1. Validation

Always implement comprehensive validation:
```python
- Schema validation (required fields)
- Type validation (correct data types)
- Range validation (reasonable values)
- Consistency validation (logical relationships)
- Completeness validation (missing data patterns)
```

### 2. Error Handling

Use structured logging and meaningful errors:
```python
self.logger.error(
    "validation_failed",
    source=self.config.source_id,
    errors=report.errors,
    row_count=len(raw_data)
)
```

### 3. Performance

For large datasets:
```python
# Process in chunks
async def download(self) -> pd.DataFrame:
    chunks = []
    for offset in range(0, total_records, chunk_size):
        chunk = await self.client.get_data(offset=offset, limit=chunk_size)
        chunks.append(chunk)
    return pd.concat(chunks)
```

### 4. Testing

Write comprehensive tests:
```python
async def test_processor_handles_empty_data():
    processor = MyProcessor(config, cache, validator)
    
    # Mock empty response
    processor.download = AsyncMock(return_value=pd.DataFrame())
    
    result = await processor.process()
    assert not result.success
    assert "empty" in result.error_summary.lower()
```

## Progress Tracking

All processors support progress callbacks:

```python
from rich.progress import Progress

with Progress() as progress:
    task = progress.add_task("Processing...", total=100)
    
    def update_progress(stage: str, pct: float):
        progress.update(task, completed=pct, description=stage)
    
    result = await processor.process(progress_callback=update_progress)
```

## Caching Strategy

Processors use intelligent caching:

1. **TTL-based**: Data expires after configured time
2. **Version-aware**: New source versions invalidate cache
3. **Force refresh**: Bypass cache when needed
4. **Compression**: Large datasets are compressed

Configure cache behavior:
```python
config = SourceConfig(
    source_id="my_source",
    cache_ttl=3600,  # 1 hour
    # ...
)

# Force fresh download
result = await processor.process(force_refresh=True)
```

## Monitoring

All processors emit structured logs:

```json
{
    "event": "processing_complete",
    "processor": "WFPPriceProcessor",
    "source": "wfp_prices",
    "processing_time": 12.5,
    "records_processed": 15420,
    "validation_warnings": 3,
    "cache_hit": false
}
```

## Future Enhancements

1. **Streaming Processing**: For very large datasets
2. **Parallel Processing**: Multiple sources concurrently
3. **Schema Evolution**: Handle changing source formats
4. **Data Lineage**: Track data transformations
5. **Quality Metrics**: Automated data quality scoring