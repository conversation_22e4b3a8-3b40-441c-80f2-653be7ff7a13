"""
Processor for aid distribution data from OCHA and other humanitarian sources.

This processor handles multiple aid data formats including:
- OCHA 3W (Who, What, Where) data
- Financial Tracking Service (FTS) data
- Cash Consortium Yemen reports
- Humanitarian Response Plan (HRP) data

The processor standardizes these diverse sources into a common format
for integration with market analysis.
"""

import asyncio
from datetime import datetime
from decimal import Decimal
from pathlib import Path
from typing import List, Optional, Dict, Any, Union
import pandas as pd
import numpy as np
from dataclasses import dataclass
import structlog

from src.infrastructure.processors.base_processor import (
    DataFrameProcessor, SourceConfig, ValidationLevel
)
from src.infrastructure.data_quality.validation_framework import (
    ValidationReport, ValidationLevel as ValLevel
)
from src.core.domain.aid.entities import AidDistribution, AidMetrics
from src.core.domain.market.value_objects import Coordinates
from src.core.domain.shared.value_objects import Money, TemporalKey
from src.infrastructure.processors.exceptions import (
    SchemaValidationException, DataQualityException,
    TransformationException
)


logger = structlog.get_logger()


@dataclass
class AidSourceConfig:
    """Configuration specific to aid data sources."""
    source_format: str  # "3w", "fts", "cash_consortium", "hrp"
    organization_column: str = "organization"
    location_columns: List[str] = None
    date_column: str = "date"
    amount_column: Optional[str] = None
    beneficiary_column: Optional[str] = None
    modality_column: Optional[str] = None
    cluster_column: Optional[str] = None
    
    def __post_init__(self):
        if self.location_columns is None:
            self.location_columns = ["governorate", "district"]


class AidDistributionProcessor(DataFrameProcessor[AidDistribution]):
    """
    Processor for humanitarian aid distribution data.
    
    Handles various aid data formats and standardizes them into
    AidDistribution entities for analysis. Supports OCHA 3W,
    FTS financial data, and other humanitarian data sources.
    
    Features:
        - Multi-format support (3W, FTS, Cash Consortium)
        - Automatic geocoding for text locations
        - Modality classification (cash, voucher, in-kind)
        - Cluster/sector standardization
        - Beneficiary deduplication
    """
    
    def __init__(
        self,
        source_config: SourceConfig,
        cache_manager,
        validator,
        aid_config: AidSourceConfig,
        geocoder: Optional[Any] = None  # Geocoding service
    ):
        super().__init__(source_config, cache_manager, validator)
        self.aid_config = aid_config
        self.geocoder = geocoder
        self.logger = logger.bind(
            processor="AidDistributionProcessor",
            source_format=aid_config.source_format
        )
        
        # Standard cluster mappings
        self.cluster_mappings = {
            "food security": "Food Security",
            "food": "Food Security",
            "wash": "WASH",
            "water sanitation": "WASH",
            "health": "Health",
            "nutrition": "Nutrition",
            "shelter": "Shelter/NFI",
            "nfi": "Shelter/NFI",
            "protection": "Protection",
            "education": "Education",
            "cash": "Multi-Purpose Cash",
            "multipurpose": "Multi-Purpose Cash"
        }
        
        # Modality mappings
        self.modality_mappings = {
            "cash": "Cash",
            "voucher": "Voucher",
            "in-kind": "In-Kind",
            "inkind": "In-Kind",
            "service": "Service",
            "mixed": "Mixed"
        }
    
    def get_required_columns(self) -> List[str]:
        """Get required columns based on source format."""
        required = [self.aid_config.date_column]
        required.extend(self.aid_config.location_columns)
        
        if self.aid_config.source_format == "3w":
            required.extend([
                self.aid_config.organization_column,
                self.aid_config.cluster_column or "cluster"
            ])
        elif self.aid_config.source_format == "fts":
            required.extend([
                self.aid_config.organization_column,
                self.aid_config.amount_column or "amount"
            ])
        
        return required
    
    async def download(self) -> pd.DataFrame:
        """Download aid data from configured source."""
        # This would be implemented based on specific data source
        # For now, return mock data for testing
        
        if self.aid_config.source_format == "3w":
            return await self._download_3w_data()
        elif self.aid_config.source_format == "fts":
            return await self._download_fts_data()
        elif self.aid_config.source_format == "cash_consortium":
            return await self._download_cash_consortium_data()
        else:
            raise ValueError(f"Unknown source format: {self.aid_config.source_format}")
    
    async def _download_3w_data(self) -> pd.DataFrame:
        """Download OCHA 3W data."""
        # In production, this would connect to HDX or OCHA API
        # Mock data for demonstration
        data = {
            'date': pd.date_range('2024-01-01', periods=100, freq='W'),
            'organization': np.random.choice(['WFP', 'UNICEF', 'UNHCR', 'Save the Children'], 100),
            'governorate': np.random.choice(['Sana\'a', 'Aden', 'Taiz', 'Hodeidah'], 100),
            'district': np.random.choice(['District_A', 'District_B', 'District_C'], 100),
            'cluster': np.random.choice(['Food Security', 'WASH', 'Health', 'Protection'], 100),
            'beneficiaries': np.random.randint(100, 5000, 100),
            'modality': np.random.choice(['Cash', 'Voucher', 'In-Kind'], 100),
            'latitude': 15.0 + np.random.random(100) * 5,
            'longitude': 44.0 + np.random.random(100) * 10
        }
        
        return pd.DataFrame(data)
    
    async def _download_fts_data(self) -> pd.DataFrame:
        """Download Financial Tracking Service data."""
        # Mock FTS data
        data = {
            'date': pd.date_range('2024-01-01', periods=50, freq='M'),
            'organization': np.random.choice(['WFP', 'UNICEF', 'WHO', 'UNDP'], 50),
            'governorate': np.random.choice(['Sana\'a', 'Aden', 'Taiz'], 50),
            'district': 'All',
            'amount': np.random.randint(100000, 5000000, 50),
            'currency': 'USD',
            'project_name': [f'Project_{i}' for i in range(50)]
        }
        
        return pd.DataFrame(data)
    
    async def _download_cash_consortium_data(self) -> pd.DataFrame:
        """Download Cash Consortium Yemen data."""
        # Mock cash distribution data
        data = {
            'distribution_date': pd.date_range('2024-01-01', periods=200, freq='D'),
            'implementing_partner': np.random.choice(['Partner_A', 'Partner_B', 'Partner_C'], 200),
            'gov': np.random.choice(['Sana\'a', 'Aden', 'Taiz', 'Hodeidah'], 200),
            'dist': np.random.choice(['District_1', 'District_2', 'District_3'], 200),
            'households': np.random.randint(50, 500, 200),
            'transfer_value': np.random.choice([50000, 75000, 100000], 200),  # YER
            'transfer_modality': 'Cash'
        }
        
        return pd.DataFrame(data)
    
    async def validate_specific(self, raw_data: pd.DataFrame) -> ValidationReport:
        """Validate aid data specifics."""
        report = ValidationReport(source=self.config.source_id)
        
        # Check date validity
        date_col = self.aid_config.date_column
        if date_col in raw_data.columns:
            try:
                dates = pd.to_datetime(raw_data[date_col])
                future_dates = dates > pd.Timestamp.now()
                if future_dates.any():
                    report.add_validation(
                        "dates",
                        ValLevel.WARNING,
                        f"Found {future_dates.sum()} future dates"
                    )
            except Exception as e:
                report.add_validation(
                    "dates",
                    ValLevel.ERROR,
                    f"Invalid date format: {str(e)}"
                )
        
        # Check location completeness
        for loc_col in self.aid_config.location_columns:
            if loc_col in raw_data.columns:
                null_count = raw_data[loc_col].isnull().sum()
                if null_count > 0:
                    report.add_validation(
                        f"location_{loc_col}",
                        ValLevel.WARNING,
                        f"{null_count} missing {loc_col} values"
                    )
        
        # Check beneficiary numbers if present
        if self.aid_config.beneficiary_column and self.aid_config.beneficiary_column in raw_data.columns:
            ben_col = raw_data[self.aid_config.beneficiary_column]
            if (ben_col < 0).any():
                report.add_validation(
                    "beneficiaries",
                    ValLevel.ERROR,
                    "Negative beneficiary numbers found"
                )
            
            # Check for suspiciously high numbers
            if (ben_col > 100000).any():
                report.add_validation(
                    "beneficiaries",
                    ValLevel.WARNING,
                    "Very high beneficiary numbers (>100k) found"
                )
        
        # Validate amounts if present
        if self.aid_config.amount_column and self.aid_config.amount_column in raw_data.columns:
            amounts = raw_data[self.aid_config.amount_column]
            if (amounts < 0).any():
                report.add_validation(
                    "amounts",
                    ValLevel.ERROR,
                    "Negative amounts found"
                )
        
        return report
    
    async def transform(self, raw_data: pd.DataFrame) -> List[AidDistribution]:
        """Transform raw aid data to AidDistribution entities."""
        distributions = []
        
        for _, row in raw_data.iterrows():
            try:
                # Extract location
                location = await self._extract_location(row)
                
                # Extract date
                date = self._extract_date(row)
                
                # Create distribution
                distribution = AidDistribution(
                    distribution_date=date,
                    location=location,
                    organization=self._extract_organization(row),
                    cluster=self._standardize_cluster(
                        row.get(self.aid_config.cluster_column, "Unknown")
                    ),
                    beneficiaries=self._extract_beneficiaries(row),
                    modality=self._standardize_modality(
                        row.get(self.aid_config.modality_column, "Unknown")
                    ),
                    amount=self._extract_amount(row),
                    metadata={
                        "source_format": self.aid_config.source_format,
                        "source_id": self.config.source_id
                    }
                )
                
                distributions.append(distribution)
                
            except Exception as e:
                self.logger.warning(
                    "transform_row_failed",
                    row_index=_,
                    error=str(e)
                )
                # Continue with other rows
        
        return distributions
    
    async def _extract_location(self, row: pd.Series) -> Coordinates:
        """Extract location coordinates from row."""
        # Check if we have direct coordinates
        if 'latitude' in row and 'longitude' in row:
            return Coordinates(
                latitude=float(row['latitude']),
                longitude=float(row['longitude'])
            )
        
        # Otherwise, geocode from location names
        location_parts = []
        for col in self.aid_config.location_columns:
            if col in row and pd.notna(row[col]):
                location_parts.append(str(row[col]))
        
        if location_parts:
            # In production, use geocoding service
            # For now, return approximate coordinates
            location_string = ", ".join(location_parts)
            
            # Mock geocoding based on governorate
            governorate = location_parts[0] if location_parts else "Unknown"
            mock_coords = {
                "Sana'a": (15.3694, 44.1910),
                "Aden": (12.7855, 45.0187),
                "Taiz": (13.5795, 44.0178),
                "Hodeidah": (14.7982, 42.9509),
                "Sa'ada": (16.9251, 43.7612),
                "Al Bayda": (13.9834, 45.5729)
            }
            
            lat, lon = mock_coords.get(governorate, (15.0, 45.0))
            
            # Add some random offset for district-level variation
            lat += np.random.uniform(-0.1, 0.1)
            lon += np.random.uniform(-0.1, 0.1)
            
            return Coordinates(latitude=lat, longitude=lon)
        
        # Default coordinates if nothing found
        return Coordinates(latitude=15.0, longitude=45.0)
    
    def _extract_date(self, row: pd.Series) -> datetime:
        """Extract date from row."""
        date_value = row.get(self.aid_config.date_column)
        
        # Handle different column names based on source
        if pd.isna(date_value) and 'distribution_date' in row:
            date_value = row['distribution_date']
        
        if pd.isna(date_value):
            raise ValueError("No valid date found")
        
        return pd.to_datetime(date_value)
    
    def _extract_organization(self, row: pd.Series) -> str:
        """Extract organization name."""
        org = row.get(self.aid_config.organization_column, "Unknown")
        
        # Handle alternative column names
        if pd.isna(org) or org == "Unknown":
            org = row.get('implementing_partner', row.get('agency', "Unknown"))
        
        return str(org)
    
    def _extract_beneficiaries(self, row: pd.Series) -> int:
        """Extract beneficiary count."""
        if self.aid_config.beneficiary_column:
            ben = row.get(self.aid_config.beneficiary_column, 0)
        else:
            # Try common column names
            ben = row.get('beneficiaries', row.get('households', 0))
            
            # Convert households to individuals if needed
            if 'households' in row and 'beneficiaries' not in row:
                ben = int(ben) * 7  # Average household size in Yemen
        
        return int(ben) if pd.notna(ben) else 0
    
    def _extract_amount(self, row: pd.Series) -> Decimal:
        """Extract aid amount."""
        if self.aid_config.amount_column:
            amount = row.get(self.aid_config.amount_column, 0)
        else:
            # Try common column names
            amount = row.get('amount', row.get('transfer_value', 0))
        
        # Convert to decimal
        if pd.notna(amount):
            return Decimal(str(amount))
        
        # If no amount but we have beneficiaries and transfer value
        if 'transfer_value' in row and 'households' in row:
            transfer = row.get('transfer_value', 0)
            households = row.get('households', 0)
            if pd.notna(transfer) and pd.notna(households):
                return Decimal(str(transfer * households))
        
        return Decimal('0')
    
    def _standardize_cluster(self, cluster: str) -> str:
        """Standardize cluster/sector names."""
        if pd.isna(cluster):
            return "Unknown"
        
        cluster_lower = str(cluster).lower().strip()
        
        # Check mappings
        for key, value in self.cluster_mappings.items():
            if key in cluster_lower:
                return value
        
        # Return original if no mapping found
        return str(cluster).title()
    
    def _standardize_modality(self, modality: str) -> str:
        """Standardize aid modality."""
        if pd.isna(modality):
            return "Unknown"
        
        modality_lower = str(modality).lower().strip()
        
        # Check mappings
        for key, value in self.modality_mappings.items():
            if key in modality_lower:
                return value
        
        # Return original if no mapping found
        return str(modality).title()
    
    async def aggregate(self, entities: List[AidDistribution]) -> pd.DataFrame:
        """Aggregate aid distributions to monthly panel."""
        if not entities:
            return pd.DataFrame()
        
        # Convert to dataframe
        records = []
        for dist in entities:
            record = {
                'date': dist.distribution_date,
                'year': dist.distribution_date.year,
                'month': dist.distribution_date.month,
                'governorate': self._get_governorate_from_coords(dist.location),
                'organization': dist.organization,
                'cluster': dist.cluster,
                'modality': dist.modality,
                'beneficiaries': dist.beneficiaries,
                'amount': float(dist.amount),
                'latitude': dist.location.latitude,
                'longitude': dist.location.longitude
            }
            records.append(record)
        
        df = pd.DataFrame(records)
        
        # Create temporal key
        df['temporal_key'] = df['year'].astype(str) + '-' + df['month'].astype(str).str.zfill(2)
        
        # Aggregate by governorate, cluster, and month
        aggregated = df.groupby(['governorate', 'cluster', 'temporal_key', 'year', 'month']).agg({
            'beneficiaries': 'sum',
            'amount': 'sum',
            'organization': 'nunique',  # Number of organizations
            'modality': lambda x: x.mode()[0] if len(x) > 0 else 'Unknown',  # Most common modality
            'date': 'count'  # Number of distributions
        }).reset_index()
        
        aggregated.rename(columns={
            'organization': 'n_organizations',
            'date': 'n_distributions',
            'modality': 'primary_modality'
        }, inplace=True)
        
        # Calculate metrics
        aggregated['avg_distribution_size'] = (
            aggregated['beneficiaries'] / aggregated['n_distributions']
        ).fillna(0)
        
        aggregated['amount_per_beneficiary'] = (
            aggregated['amount'] / aggregated['beneficiaries']
        ).replace([np.inf, -np.inf], 0).fillna(0)
        
        return aggregated
    
    def _get_governorate_from_coords(self, coords: Coordinates) -> str:
        """Get governorate name from coordinates."""
        # In production, use reverse geocoding
        # For now, use simple distance-based assignment
        
        governorate_centers = {
            "Sana'a": (15.3694, 44.1910),
            "Aden": (12.7855, 45.0187),
            "Taiz": (13.5795, 44.0178),
            "Hodeidah": (14.7982, 42.9509),
            "Sa'ada": (16.9251, 43.7612),
            "Al Bayda": (13.9834, 45.5729),
            "Ibb": (13.9667, 44.1833),
            "Dhamar": (14.5430, 44.4010),
            "Hajjah": (15.6943, 43.6058),
            "Amran": (15.6594, 43.9439)
        }
        
        min_distance = float('inf')
        closest_gov = "Unknown"
        
        for gov, (lat, lon) in governorate_centers.items():
            # Simple Euclidean distance
            distance = ((coords.latitude - lat) ** 2 + (coords.longitude - lon) ** 2) ** 0.5
            if distance < min_distance:
                min_distance = distance
                closest_gov = gov
        
        return closest_gov