"""
Processor factory for creating data processors based on configuration.

This factory provides a centralized way to instantiate data processors with
proper dependency injection and configuration. It supports all processor types
in the data pipeline and handles their specific dependencies.

Example Usage:
    ```python
    # Create factory with dependencies
    factory = ProcessorFactory(
        cache_manager=cache_manager,
        validator=validator,
        spatial_service=spatial_service,
        temporal_service=temporal_service
    )
    
    # Create processor from config
    config = ProcessorConfig(
        processor_type="wfp_prices",
        source_config=SourceConfig(
            source_id="wfp_food_prices",
            source_type="csv",
            update_frequency="weekly",
            cache_ttl=86400
        ),
        processor_params={
            "api_key": "xxx",
            "base_url": "https://api.wfp.org"
        }
    )
    
    processor = factory.create_processor(config)
    result = await processor.process()
    ```
"""

from dataclasses import dataclass
from enum import Enum
from typing import Dict, Any, Optional, Type, Union, List
import structlog

from src.core.domain.market.entities import Market
from src.infrastructure.processors.base_processor import (
    BaseProcessor, SourceConfig, DataFrameProcessor
)
from src.infrastructure.processors.conflict_processor import ConflictProcessor
from src.infrastructure.processors.wfp_processor import WFPProcessor
from src.infrastructure.processors.acled_processor import ACLEDProcessor
from src.infrastructure.processors.acaps_processor import ACAPSProcessor
from src.infrastructure.processors.currency_aware_wfp_processor import CurrencyAwareWFPProcessor
from src.infrastructure.processors.geo_data_processor import (
    GeoDataProcessor, CHIRPSProcessor, MODISNDVIProcessor, RasterConfig
)
from src.infrastructure.caching.cache_manager import CacheManager
from src.infrastructure.data_quality.validation_framework import DataValidator
from src.infrastructure.spatial.spatial_integration_service import SpatialIntegrationService
from src.infrastructure.temporal.temporal_alignment_service import TemporalAlignmentService
from src.infrastructure.external_services.hdx_client import HDXClient
from src.infrastructure.external_services.acled_client import ACLEDClient
from src.core.domain.shared.exceptions import YemenMarketException


logger = structlog.get_logger()


class ProcessorType(Enum):
    """Supported processor types."""
    # Price data
    WFP_PRICES = "wfp_prices"
    WFP_CURRENCY_AWARE = "wfp_currency_aware"
    
    # Conflict data
    ACLED_CONFLICT = "acled_conflict"
    CONFLICT_EVENTS = "conflict_events"
    
    # Control zones
    ACAPS_ZONES = "acaps_zones"
    
    # Climate data
    CHIRPS_RAINFALL = "chirps_rainfall"
    MODIS_NDVI = "modis_ndvi"
    CLIMATE_GENERAL = "climate_general"
    
    # Aid data
    OCHA_AID = "ocha_aid"
    
    # Infrastructure
    OSM_ROADS = "osm_roads"
    
    # Population
    WORLDPOP = "worldpop"
    
    # Markets
    MARKET_CHARACTERISTICS = "market_characteristics"
    
    # IPC
    IPC_FOOD_SECURITY = "ipc_food_security"


@dataclass
class ProcessorConfig:
    """Configuration for creating a processor."""
    processor_type: Union[ProcessorType, str]
    source_config: SourceConfig
    processor_params: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Convert string to ProcessorType if needed."""
        if isinstance(self.processor_type, str):
            try:
                self.processor_type = ProcessorType(self.processor_type)
            except ValueError:
                raise ValueError(f"Unknown processor type: {self.processor_type}")


class ProcessorFactory:
    """
    Factory for creating data processors with dependency injection.
    
    This factory centralizes processor creation and ensures all required
    dependencies are properly injected based on processor type.
    """
    
    def __init__(
        self,
        cache_manager: CacheManager,
        validator: DataValidator,
        spatial_service: Optional[SpatialIntegrationService] = None,
        temporal_service: Optional[TemporalAlignmentService] = None,
        hdx_client: Optional[HDXClient] = None,
        acled_client: Optional[ACLEDClient] = None,
        markets: Optional[List[Market]] = None
    ):
        """
        Initialize factory with common dependencies.
        
        Args:
            cache_manager: Cache management for all processors
            validator: Data validation framework
            spatial_service: Optional spatial operations service
            temporal_service: Optional temporal alignment service
            hdx_client: Optional HDX API client
            acled_client: Optional ACLED API client
            markets: Optional list of markets for geo processors
        """
        self.cache_manager = cache_manager
        self.validator = validator
        self.spatial_service = spatial_service
        self.temporal_service = temporal_service
        self.hdx_client = hdx_client
        self.acled_client = acled_client
        self.markets = markets or []
        self.logger = logger.bind(factory="ProcessorFactory")
        
        # Registry of processor types to classes
        self._processor_registry: Dict[ProcessorType, Type[BaseProcessor]] = {
            ProcessorType.WFP_PRICES: WFPProcessor,
            ProcessorType.WFP_CURRENCY_AWARE: CurrencyAwareWFPProcessor,
            ProcessorType.ACLED_CONFLICT: ACLEDProcessor,
            ProcessorType.CONFLICT_EVENTS: ConflictProcessor,
            ProcessorType.ACAPS_ZONES: ACAPSProcessor,
            ProcessorType.CHIRPS_RAINFALL: CHIRPSProcessor,
            ProcessorType.MODIS_NDVI: MODISNDVIProcessor,
        }
    
    def create_processor(self, config: ProcessorConfig) -> BaseProcessor:
        """
        Create a processor instance based on configuration.
        
        Args:
            config: Processor configuration including type and parameters
            
        Returns:
            Configured processor instance ready for use
            
        Raises:
            ValueError: If processor type is not supported
            YemenMarketException: If required dependencies are missing
        """
        self.logger.info(
            "creating_processor",
            processor_type=config.processor_type.value,
            source_id=config.source_config.source_id
        )
        
        # Get processor class
        processor_class = self._processor_registry.get(config.processor_type)
        if not processor_class:
            raise ValueError(f"Unsupported processor type: {config.processor_type}")
        
        # Build kwargs based on processor type
        kwargs = self._build_processor_kwargs(config)
        
        try:
            # Create processor instance
            processor = processor_class(
                source_config=config.source_config,
                cache_manager=self.cache_manager,
                validator=self.validator,
                **kwargs
            )
            
            self.logger.info(
                "processor_created",
                processor_type=config.processor_type.value,
                processor_class=processor_class.__name__
            )
            
            return processor
            
        except Exception as e:
            self.logger.error(
                "processor_creation_failed",
                processor_type=config.processor_type.value,
                error=str(e)
            )
            raise YemenMarketException(
                f"Failed to create processor {config.processor_type.value}: {str(e)}"
            )
    
    def _build_processor_kwargs(self, config: ProcessorConfig) -> Dict[str, Any]:
        """
        Build processor-specific keyword arguments.
        
        Different processor types require different dependencies. This method
        maps processor types to their required dependencies.
        """
        kwargs = {}
        
        # Add processor-specific parameters
        if config.processor_params:
            kwargs.update(config.processor_params)
        
        # Add dependencies based on processor type
        if config.processor_type in [
            ProcessorType.CONFLICT_EVENTS,
            ProcessorType.ACLED_CONFLICT
        ]:
            # Conflict processors need spatial service and ACLED client
            if not self.spatial_service:
                raise YemenMarketException(
                    f"Spatial service required for {config.processor_type.value}"
                )
            kwargs['spatial_service'] = self.spatial_service
            
            if config.processor_type == ProcessorType.ACLED_CONFLICT and self.acled_client:
                kwargs['acled_client'] = self.acled_client
        
        elif config.processor_type in [
            ProcessorType.CHIRPS_RAINFALL,
            ProcessorType.MODIS_NDVI,
            ProcessorType.CLIMATE_GENERAL
        ]:
            # Geo processors need markets and raster config
            if not self.markets:
                raise YemenMarketException(
                    f"Markets list required for {config.processor_type.value}"
                )
            kwargs['markets'] = self.markets
            
            # Add raster config if not provided
            if 'raster_config' not in kwargs:
                kwargs['raster_config'] = self._get_default_raster_config(
                    config.processor_type
                )
        
        elif config.processor_type == ProcessorType.WFP_CURRENCY_AWARE:
            # Currency-aware processor needs temporal service
            if self.temporal_service:
                kwargs['temporal_service'] = self.temporal_service
        
        # All HDX-based processors need HDX client
        if config.source_config.source_type == "hdx" and self.hdx_client:
            kwargs['hdx_client'] = self.hdx_client
        
        return kwargs
    
    def _get_default_raster_config(self, processor_type: ProcessorType) -> RasterConfig:
        """Get default raster configuration for geo processors."""
        if processor_type == ProcessorType.CHIRPS_RAINFALL:
            return RasterConfig(
                aggregation_method="sum",  # Total rainfall
                scale_factor=1.0,
                nodata_value=-9999
            )
        elif processor_type == ProcessorType.MODIS_NDVI:
            return RasterConfig(
                aggregation_method="mean",  # Average NDVI
                scale_factor=0.0001,  # MODIS scale factor
                nodata_value=-3000
            )
        else:
            return RasterConfig()  # Default config
    
    def create_multiple_processors(
        self,
        configs: List[ProcessorConfig]
    ) -> Dict[str, BaseProcessor]:
        """
        Create multiple processors at once.
        
        Args:
            configs: List of processor configurations
            
        Returns:
            Dictionary mapping source_id to processor instance
        """
        processors = {}
        
        for config in configs:
            try:
                processor = self.create_processor(config)
                processors[config.source_config.source_id] = processor
            except Exception as e:
                self.logger.error(
                    "failed_to_create_processor",
                    source_id=config.source_config.source_id,
                    error=str(e)
                )
                # Continue with other processors
        
        return processors
    
    def register_custom_processor(
        self,
        processor_type: ProcessorType,
        processor_class: Type[BaseProcessor]
    ) -> None:
        """
        Register a custom processor type.
        
        This allows extending the factory with new processor types without
        modifying the factory code.
        
        Args:
            processor_type: New processor type to register
            processor_class: Class that implements the processor
        """
        self.logger.info(
            "registering_processor",
            processor_type=processor_type.value,
            processor_class=processor_class.__name__
        )
        
        self._processor_registry[processor_type] = processor_class
    
    @classmethod
    def from_config(cls, factory_config: Dict[str, Any]) -> 'ProcessorFactory':
        """
        Create factory from configuration dictionary.
        
        Args:
            factory_config: Configuration with dependency settings
            
        Returns:
            Configured ProcessorFactory instance
        """
        # This would create dependencies from config
        # For now, just return a basic instance
        return cls(
            cache_manager=factory_config.get('cache_manager'),
            validator=factory_config.get('validator')
        )


# Convenience functions for common processor creation patterns

def create_price_processors(
    factory: ProcessorFactory,
    include_currency_aware: bool = True
) -> Dict[str, BaseProcessor]:
    """Create all price-related processors."""
    configs = [
        ProcessorConfig(
            processor_type=ProcessorType.WFP_PRICES,
            source_config=SourceConfig(
                source_id="wfp_food_prices",
                source_type="csv",
                update_frequency="weekly",
                cache_ttl=86400
            )
        )
    ]
    
    if include_currency_aware:
        configs.append(
            ProcessorConfig(
                processor_type=ProcessorType.WFP_CURRENCY_AWARE,
                source_config=SourceConfig(
                    source_id="wfp_currency_aware_prices",
                    source_type="csv",
                    update_frequency="weekly",
                    cache_ttl=86400
                )
            )
        )
    
    return factory.create_multiple_processors(configs)


def create_climate_processors(
    factory: ProcessorFactory,
    markets: List[Market]
) -> Dict[str, BaseProcessor]:
    """Create all climate-related processors."""
    # Update factory with markets
    factory.markets = markets
    
    configs = [
        ProcessorConfig(
            processor_type=ProcessorType.CHIRPS_RAINFALL,
            source_config=SourceConfig(
                source_id="chirps_rainfall",
                source_type="netcdf",
                update_frequency="monthly",
                cache_ttl=30 * 86400  # 30 days
            )
        ),
        ProcessorConfig(
            processor_type=ProcessorType.MODIS_NDVI,
            source_config=SourceConfig(
                source_id="modis_ndvi",
                source_type="geotiff",
                update_frequency="16_days",
                cache_ttl=16 * 86400
            )
        )
    ]
    
    return factory.create_multiple_processors(configs)