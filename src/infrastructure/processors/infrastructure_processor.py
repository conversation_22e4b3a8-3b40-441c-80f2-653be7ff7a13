"""
Processor for infrastructure data including roads, ports, and market accessibility.

This processor extracts infrastructure features from OpenStreetMap and other sources
to calculate market accessibility and connectivity metrics.
"""

import asyncio
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple, Set
import numpy as np
import pandas as pd
import geopandas as gpd
from shapely.geometry import Point, LineString, Polygon
from shapely.ops import nearest_points
import networkx as nx
import osmnx as ox
import structlog

from src.infrastructure.processors.base_processor import (
    DataFrameProcessor, SourceConfig, ValidationLevel
)
from src.infrastructure.data_quality.validation_framework import (
    ValidationReport, ValidationLevel as ValLevel
)
from src.core.domain.infrastructure.entities import (
    RoadNetwork, MarketAccessibility, InfrastructureMetrics
)
from src.core.domain.infrastructure.value_objects import (
    RoadType, PortType, CheckpointType, AccessibilityLevel
)
from src.core.domain.market.value_objects import Coordinates
from src.infrastructure.processors.exceptions import (
    DataQualityException, TransformationException
)


logger = structlog.get_logger()


class InfrastructureProcessor(DataFrameProcessor[MarketAccessibility]):
    """
    Processor for infrastructure data affecting market connectivity.
    
    Features:
        - OSM road network extraction
        - Port and border crossing identification
        - Checkpoint and barrier mapping
        - Market accessibility scoring
        - Travel time estimation
        - Network resilience analysis
    """
    
    # Infrastructure types
    ROAD_HIERARCHY = {
        'motorway': {'speed_kmh': 80, 'quality': 1.0},
        'trunk': {'speed_kmh': 60, 'quality': 0.9},
        'primary': {'speed_kmh': 50, 'quality': 0.8},
        'secondary': {'speed_kmh': 40, 'quality': 0.7},
        'tertiary': {'speed_kmh': 30, 'quality': 0.6},
        'unclassified': {'speed_kmh': 20, 'quality': 0.4},
        'residential': {'speed_kmh': 15, 'quality': 0.3},
        'track': {'speed_kmh': 10, 'quality': 0.2}
    }
    
    # Key infrastructure locations
    PORTS = {
        'Hodeidah': {'coords': (42.9545, 14.7982), 'type': 'major', 'capacity': 'high'},
        'Aden': {'coords': (45.0187, 12.7855), 'type': 'major', 'capacity': 'high'},
        'Mukalla': {'coords': (49.1242, 14.5425), 'type': 'secondary', 'capacity': 'medium'},
        'Mokha': {'coords': (43.2517, 13.3228), 'type': 'secondary', 'capacity': 'low'},
        'Saleef': {'coords': (43.0983, 15.3103), 'type': 'secondary', 'capacity': 'medium'}
    }
    
    BORDER_CROSSINGS = {
        'Al-Wadiah': {'coords': (47.9667, 17.0333), 'country': 'Saudi Arabia', 'status': 'open'},
        'Haradh': {'coords': (43.1333, 16.6167), 'country': 'Saudi Arabia', 'status': 'restricted'},
        'Al-Buq': {'coords': (44.4667, 17.5833), 'country': 'Saudi Arabia', 'status': 'closed'},
        'Shahan': {'coords': (52.6000, 17.5167), 'country': 'Oman', 'status': 'open'}
    }
    
    def __init__(
        self,
        source_config: SourceConfig,
        cache_manager,
        validator,
        market_locations: Optional[gpd.GeoDataFrame] = None,
        control_zones: Optional[gpd.GeoDataFrame] = None,
        conflict_data: Optional[gpd.GeoDataFrame] = None
    ):
        super().__init__(source_config, cache_manager, validator)
        self.market_locations = market_locations
        self.control_zones = control_zones
        self.conflict_data = conflict_data
        
        self.logger = logger.bind(
            processor="InfrastructureProcessor"
        )
        
        # Cache for road network graph
        self._road_graph = None
    
    async def download(self) -> Dict[str, Any]:
        """Download infrastructure data from various sources."""
        infrastructure_data = {}
        
        # 1. Download OSM road network
        try:
            infrastructure_data['roads'] = await self._download_road_network()
        except Exception as e:
            self.logger.warning("Failed to download road network", error=str(e))
            infrastructure_data['roads'] = self._create_synthetic_road_network()
        
        # 2. Get checkpoint data (from control zones if available)
        infrastructure_data['checkpoints'] = await self._identify_checkpoints()
        
        # 3. Port and border data (static for now)
        infrastructure_data['ports'] = self._get_ports_data()
        infrastructure_data['borders'] = self._get_border_crossings_data()
        
        return infrastructure_data
    
    async def _download_road_network(self) -> gpd.GeoDataFrame:
        """Download road network from OpenStreetMap."""
        # In production, use osmnx or Overpass API
        # For Yemen: bbox approximately (42, 12, 54, 20)
        
        try:
            # Download road network for Yemen
            G = ox.graph_from_bbox(
                north=20, south=12, east=54, west=42,
                network_type='drive',
                simplify=True
            )
            
            # Convert to GeoDataFrame
            nodes, edges = ox.graph_to_gdfs(G)
            
            # Keep only edges (roads)
            roads = edges[['highway', 'length', 'geometry']].copy()
            roads = roads.reset_index()
            
            return roads
            
        except Exception as e:
            self.logger.error("OSM download failed", error=str(e))
            raise
    
    def _create_synthetic_road_network(self) -> gpd.GeoDataFrame:
        """Create synthetic road network for testing."""
        # Major road connections between cities
        road_connections = [
            # Sana'a connections
            {'from': "Sana'a", 'to': 'Hodeidah', 'type': 'primary', 'length_km': 226},
            {'from': "Sana'a", 'to': 'Aden', 'type': 'trunk', 'length_km': 350},
            {'from': "Sana'a", 'to': 'Taiz', 'type': 'primary', 'length_km': 256},
            {'from': "Sana'a", 'to': 'Marib', 'type': 'primary', 'length_km': 173},
            
            # Coastal road
            {'from': 'Hodeidah', 'to': 'Mokha', 'type': 'secondary', 'length_km': 180},
            {'from': 'Mokha', 'to': 'Aden', 'type': 'primary', 'length_km': 200},
            {'from': 'Aden', 'to': 'Mukalla', 'type': 'trunk', 'length_km': 580},
            
            # Interior connections
            {'from': 'Taiz', 'to': 'Ibb', 'type': 'primary', 'length_km': 77},
            {'from': 'Ibb', 'to': 'Dhamar', 'type': 'primary', 'length_km': 100},
            {'from': 'Dhamar', 'to': "Sana'a", 'type': 'primary', 'length_km': 100},
            
            # Eastern routes
            {'from': 'Marib', 'to': 'Al-Wadiah', 'type': 'secondary', 'length_km': 400},
            {'from': 'Mukalla', 'to': 'Shahan', 'type': 'secondary', 'length_km': 450}
        ]
        
        # City coordinates
        city_coords = {
            "Sana'a": (44.2065, 15.3547),
            'Aden': (45.0187, 12.7855),
            'Taiz': (44.0178, 13.5795),
            'Hodeidah': (42.9545, 14.7982),
            'Ibb': (44.1833, 13.9667),
            'Marib': (45.3256, 15.4625),
            'Dhamar': (44.4010, 14.5430),
            'Mukalla': (49.1242, 14.5425),
            'Mokha': (43.2517, 13.3228)
        }
        
        # Add border/port locations
        city_coords.update({
            'Al-Wadiah': (47.9667, 17.0333),
            'Shahan': (52.6000, 17.5167)
        })
        
        # Create road geometries
        roads = []
        for road in road_connections:
            from_coord = city_coords[road['from']]
            to_coord = city_coords[road['to']]
            
            # Create simple line (in reality would follow actual road path)
            geometry = LineString([from_coord, to_coord])
            
            roads.append({
                'from_city': road['from'],
                'to_city': road['to'],
                'highway': road['type'],
                'length': road['length_km'] * 1000,  # Convert to meters
                'geometry': geometry
            })
        
        return gpd.GeoDataFrame(roads, crs='EPSG:4326')
    
    async def _identify_checkpoints(self) -> gpd.GeoDataFrame:
        """Identify likely checkpoint locations."""
        checkpoints = []
        
        if self.control_zones is not None:
            # Find boundaries between different control zones
            for zone_type in ['DFA', 'IRG']:
                zone_data = self.control_zones[
                    self.control_zones.get('control_type', '') == zone_type
                ]
                
                if not zone_data.empty:
                    # Get zone boundary
                    boundary = zone_data.unary_union.boundary
                    
                    # Sample points along boundary (potential checkpoints)
                    if boundary.geom_type == 'MultiLineString':
                        for line in boundary.geoms:
                            # Sample every 50km along boundary
                            distances = np.arange(0, line.length, 50000)
                            for dist in distances:
                                point = line.interpolate(dist)
                                checkpoints.append({
                                    'type': 'zone_boundary',
                                    'control_zones': f"{zone_type}_boundary",
                                    'severity': 'high',
                                    'geometry': Point(point.x, point.y)
                                })
        
        # Add known checkpoint locations
        known_checkpoints = [
            {'location': 'Dhale-Ibb', 'coords': (44.73, 13.70), 'type': 'major'},
            {'location': 'Bayhan', 'coords': (45.72, 14.79), 'type': 'major'},
            {'location': 'Al-Anad', 'coords': (44.78, 13.17), 'type': 'military'}
        ]
        
        for cp in known_checkpoints:
            checkpoints.append({
                'type': cp['type'],
                'location': cp['location'],
                'severity': 'high' if cp['type'] == 'major' else 'medium',
                'geometry': Point(cp['coords'])
            })
        
        if checkpoints:
            return gpd.GeoDataFrame(checkpoints, crs='EPSG:4326')
        else:
            return gpd.GeoDataFrame(columns=['type', 'severity', 'geometry'])
    
    def _get_ports_data(self) -> gpd.GeoDataFrame:
        """Get port locations and characteristics."""
        port_data = []
        
        for port_name, info in self.PORTS.items():
            port_data.append({
                'name': port_name,
                'type': info['type'],
                'capacity': info['capacity'],
                'operational': True,  # In reality, check current status
                'geometry': Point(info['coords'])
            })
        
        return gpd.GeoDataFrame(port_data, crs='EPSG:4326')
    
    def _get_border_crossings_data(self) -> gpd.GeoDataFrame:
        """Get border crossing data."""
        border_data = []
        
        for crossing_name, info in self.BORDER_CROSSINGS.items():
            border_data.append({
                'name': crossing_name,
                'country': info['country'],
                'status': info['status'],
                'type': 'land',
                'geometry': Point(info['coords'])
            })
        
        return gpd.GeoDataFrame(border_data, crs='EPSG:4326')
    
    async def validate_specific(self, raw_data: Dict[str, Any]) -> ValidationReport:
        """Validate infrastructure data specifics."""
        report = ValidationReport(source=self.config.source_id)
        
        # Validate road network
        if 'roads' in raw_data:
            roads = raw_data['roads']
            
            if roads.empty:
                report.add_validation(
                    "roads",
                    ValLevel.ERROR,
                    "No road data available"
                )
            else:
                # Check geometry validity
                invalid_geoms = (~roads.geometry.is_valid).sum()
                if invalid_geoms > 0:
                    report.add_validation(
                        "road_geometry",
                        ValLevel.WARNING,
                        f"{invalid_geoms} invalid road geometries"
                    )
                
                # Check for required columns
                if 'highway' not in roads.columns:
                    report.add_validation(
                        "road_type",
                        ValLevel.WARNING,
                        "Road type classification missing"
                    )
        
        # Validate ports
        if 'ports' in raw_data:
            ports = raw_data['ports']
            if len(ports) < 2:
                report.add_validation(
                    "ports",
                    ValLevel.WARNING,
                    "Insufficient port data"
                )
        
        # Validate checkpoints
        if 'checkpoints' in raw_data:
            checkpoints = raw_data['checkpoints']
            self.logger.info(f"Identified {len(checkpoints)} checkpoints")
        
        return report
    
    async def transform(self, raw_data: Dict[str, Any]) -> List[MarketAccessibility]:
        """Transform infrastructure data to MarketAccessibility entities."""
        if self.market_locations is None:
            raise ValueError("Market locations required for accessibility calculation")
        
        # Build road network graph if needed
        if self._road_graph is None and 'roads' in raw_data:
            self._road_graph = self._build_road_graph(raw_data['roads'])
        
        accessibility_data = []
        
        for _, market in self.market_locations.iterrows():
            try:
                market_id = market.get('market_id', 'unknown')
                location = Coordinates(
                    latitude=market.geometry.y,
                    longitude=market.geometry.x
                )
                
                # Calculate various accessibility metrics
                metrics = {}
                
                # 1. Distance to nearest port
                if 'ports' in raw_data:
                    port_access = self._calculate_port_access(
                        market.geometry,
                        raw_data['ports']
                    )
                    metrics.update(port_access)
                
                # 2. Distance to border crossings
                if 'borders' in raw_data:
                    border_access = self._calculate_border_access(
                        market.geometry,
                        raw_data['borders']
                    )
                    metrics.update(border_access)
                
                # 3. Road quality and connectivity
                if 'roads' in raw_data:
                    road_metrics = self._calculate_road_metrics(
                        market.geometry,
                        raw_data['roads']
                    )
                    metrics.update(road_metrics)
                
                # 4. Checkpoint burden
                if 'checkpoints' in raw_data and not raw_data['checkpoints'].empty:
                    checkpoint_burden = self._calculate_checkpoint_burden(
                        market.geometry,
                        raw_data['checkpoints']
                    )
                    metrics.update(checkpoint_burden)
                
                # 5. Overall accessibility score
                accessibility_score = self._calculate_accessibility_score(metrics)
                
                # Determine accessibility level
                if accessibility_score > 0.8:
                    level = AccessibilityLevel.HIGH
                elif accessibility_score > 0.6:
                    level = AccessibilityLevel.MEDIUM
                elif accessibility_score > 0.4:
                    level = AccessibilityLevel.LOW
                else:
                    level = AccessibilityLevel.VERY_LOW
                
                # Create entity
                accessibility = MarketAccessibility(
                    market_id=market_id,
                    location=location,
                    accessibility_level=level,
                    accessibility_score=accessibility_score,
                    nearest_port_km=metrics.get('nearest_port_km', 999),
                    nearest_border_km=metrics.get('nearest_border_km', 999),
                    road_density_km_per_km2=metrics.get('road_density', 0),
                    avg_road_quality=metrics.get('avg_road_quality', 0),
                    checkpoint_count_50km=metrics.get('checkpoints_50km', 0),
                    travel_time_capital_hrs=metrics.get('travel_time_capital', 999),
                    network_centrality=metrics.get('centrality', 0),
                    metadata={
                        'nearest_port': metrics.get('nearest_port_name', 'Unknown'),
                        'nearest_border': metrics.get('nearest_border_name', 'Unknown'),
                        'primary_road_access': metrics.get('has_primary_road', False)
                    }
                )
                
                accessibility_data.append(accessibility)
                
            except Exception as e:
                self.logger.warning(
                    "Failed to calculate accessibility for market",
                    market_id=market_id,
                    error=str(e)
                )
        
        return accessibility_data
    
    def _build_road_graph(self, roads: gpd.GeoDataFrame) -> nx.Graph:
        """Build network graph from road data."""
        G = nx.Graph()
        
        # Add edges from road segments
        for _, road in roads.iterrows():
            # Get start and end points
            coords = list(road.geometry.coords)
            start = coords[0]
            end = coords[-1]
            
            # Get road attributes
            road_type = road.get('highway', 'unclassified')
            length = road.get('length', road.geometry.length * 111000)  # Convert degrees to meters
            
            # Calculate travel time based on road type
            speed_kmh = self.ROAD_HIERARCHY.get(road_type, {}).get('speed_kmh', 20)
            travel_time = (length / 1000) / speed_kmh * 60  # Minutes
            
            # Add edge
            G.add_edge(
                start, end,
                length=length,
                travel_time=travel_time,
                road_type=road_type,
                quality=self.ROAD_HIERARCHY.get(road_type, {}).get('quality', 0.5)
            )
        
        return G
    
    def _calculate_port_access(
        self, 
        market_location: Point,
        ports: gpd.GeoDataFrame
    ) -> Dict[str, Any]:
        """Calculate port accessibility metrics."""
        # Find nearest port
        distances = ports.geometry.distance(market_location)
        nearest_idx = distances.idxmin()
        nearest_port = ports.loc[nearest_idx]
        
        # Convert to km
        nearest_distance_km = distances.min() * 111  # Rough conversion
        
        # Check if major port is accessible
        major_ports = ports[ports['type'] == 'major']
        major_distances = major_ports.geometry.distance(market_location)
        nearest_major_km = major_distances.min() * 111 if not major_ports.empty else 999
        
        return {
            'nearest_port_km': nearest_distance_km,
            'nearest_port_name': nearest_port['name'],
            'nearest_major_port_km': nearest_major_km,
            'port_capacity': nearest_port.get('capacity', 'unknown')
        }
    
    def _calculate_border_access(
        self,
        market_location: Point,
        borders: gpd.GeoDataFrame
    ) -> Dict[str, Any]:
        """Calculate border crossing accessibility."""
        # Find nearest border crossing
        distances = borders.geometry.distance(market_location)
        nearest_idx = distances.idxmin()
        nearest_border = borders.loc[nearest_idx]
        
        # Convert to km
        nearest_distance_km = distances.min() * 111
        
        # Check open borders only
        open_borders = borders[borders['status'] == 'open']
        if not open_borders.empty:
            open_distances = open_borders.geometry.distance(market_location)
            nearest_open_km = open_distances.min() * 111
        else:
            nearest_open_km = 999
        
        return {
            'nearest_border_km': nearest_distance_km,
            'nearest_border_name': nearest_border['name'],
            'nearest_open_border_km': nearest_open_km,
            'border_country': nearest_border.get('country', 'Unknown')
        }
    
    def _calculate_road_metrics(
        self,
        market_location: Point,
        roads: gpd.GeoDataFrame
    ) -> Dict[str, Any]:
        """Calculate road network metrics."""
        # Create buffer around market (25km)
        buffer = market_location.buffer(0.225)  # ~25km
        
        # Find roads within buffer
        nearby_roads = roads[roads.geometry.intersects(buffer)]
        
        if nearby_roads.empty:
            return {
                'road_density': 0,
                'avg_road_quality': 0,
                'has_primary_road': False,
                'nearest_road_km': 999
            }
        
        # Calculate total road length in buffer
        total_length_km = nearby_roads['length'].sum() / 1000
        buffer_area_km2 = np.pi * 25 ** 2
        road_density = total_length_km / buffer_area_km2
        
        # Average road quality
        if 'highway' in nearby_roads.columns:
            qualities = [
                self.ROAD_HIERARCHY.get(rt, {}).get('quality', 0.5)
                for rt in nearby_roads['highway']
            ]
            avg_quality = np.mean(qualities)
        else:
            avg_quality = 0.5
        
        # Check for primary roads
        primary_types = ['motorway', 'trunk', 'primary']
        has_primary = any(
            rt in primary_types 
            for rt in nearby_roads.get('highway', [])
        )
        
        # Distance to nearest road
        road_distances = nearby_roads.geometry.distance(market_location)
        nearest_road_km = road_distances.min() * 111
        
        return {
            'road_density': road_density,
            'avg_road_quality': avg_quality,
            'has_primary_road': has_primary,
            'nearest_road_km': nearest_road_km,
            'road_count': len(nearby_roads)
        }
    
    def _calculate_checkpoint_burden(
        self,
        market_location: Point,
        checkpoints: gpd.GeoDataFrame
    ) -> Dict[str, Any]:
        """Calculate checkpoint burden metrics."""
        # Count checkpoints within different radii
        radii_km = [10, 25, 50]
        checkpoint_counts = {}
        
        for radius in radii_km:
            buffer = market_location.buffer(radius / 111)  # Convert km to degrees
            count = checkpoints.geometry.within(buffer).sum()
            checkpoint_counts[f'checkpoints_{radius}km'] = count
        
        # Calculate severity-weighted burden
        if not checkpoints.empty and 'severity' in checkpoints.columns:
            severity_weights = {'high': 3, 'medium': 2, 'low': 1}
            
            # Within 50km
            buffer_50km = market_location.buffer(50 / 111)
            nearby_checkpoints = checkpoints[checkpoints.geometry.within(buffer_50km)]
            
            weighted_burden = sum(
                severity_weights.get(sev, 1)
                for sev in nearby_checkpoints.get('severity', [])
            )
            
            checkpoint_counts['checkpoint_burden_score'] = weighted_burden
        else:
            checkpoint_counts['checkpoint_burden_score'] = 0
        
        return checkpoint_counts
    
    def _calculate_accessibility_score(self, metrics: Dict[str, Any]) -> float:
        """Calculate overall accessibility score (0-1)."""
        score_components = []
        
        # Port access (closer is better)
        if 'nearest_port_km' in metrics:
            port_score = max(0, 1 - metrics['nearest_port_km'] / 500)  # 500km max
            score_components.append(('port', port_score, 0.25))
        
        # Border access
        if 'nearest_open_border_km' in metrics:
            border_score = max(0, 1 - metrics['nearest_open_border_km'] / 1000)
            score_components.append(('border', border_score, 0.15))
        
        # Road quality
        if 'avg_road_quality' in metrics:
            score_components.append(('road_quality', metrics['avg_road_quality'], 0.3))
        
        # Road density
        if 'road_density' in metrics:
            density_score = min(1, metrics['road_density'] / 0.5)  # 0.5 km/km² is good
            score_components.append(('road_density', density_score, 0.2))
        
        # Checkpoint burden (fewer is better)
        if 'checkpoint_burden_score' in metrics:
            checkpoint_score = max(0, 1 - metrics['checkpoint_burden_score'] / 10)
            score_components.append(('checkpoints', checkpoint_score, 0.1))
        
        # Calculate weighted average
        if score_components:
            total_weight = sum(weight for _, _, weight in score_components)
            weighted_sum = sum(score * weight for _, score, weight in score_components)
            return weighted_sum / total_weight if total_weight > 0 else 0.5
        else:
            return 0.5  # Default middle score
    
    async def aggregate(
        self, 
        entities: List[MarketAccessibility]
    ) -> pd.DataFrame:
        """Aggregate accessibility data to panel format."""
        if not entities:
            return pd.DataFrame()
        
        # Convert to dataframe
        records = []
        for access in entities:
            record = {
                'market_id': access.market_id,
                'latitude': access.location.latitude,
                'longitude': access.location.longitude,
                'accessibility_level': access.accessibility_level.value,
                'accessibility_score': access.accessibility_score,
                'nearest_port_km': access.nearest_port_km,
                'nearest_port': access.metadata.get('nearest_port_name', 'Unknown'),
                'port_capacity': access.metadata.get('port_capacity', 'unknown'),
                'nearest_border_km': access.nearest_border_km,
                'nearest_border': access.metadata.get('nearest_border_name', 'Unknown'),
                'road_density_km_per_km2': access.road_density_km_per_km2,
                'avg_road_quality': access.avg_road_quality,
                'has_primary_road': access.metadata.get('primary_road_access', False),
                'checkpoint_count_50km': access.checkpoint_count_50km,
                'travel_time_capital_hrs': access.travel_time_capital_hrs,
                'network_centrality': access.network_centrality
            }
            records.append(record)
        
        df = pd.DataFrame(records)
        
        # Add derived features
        # Market remoteness index
        df['remoteness_index'] = (
            df['nearest_port_km'] / 100 +  # Normalize by 100km
            df['nearest_border_km'] / 200 +
            (1 - df['road_density_km_per_km2']) * 2 +
            (1 - df['avg_road_quality']) * 2
        ) / 6  # Average of components
        
        # Trade potential score
        df['trade_potential'] = (
            (1 - df['nearest_port_km'] / 500) * 0.4 +
            (1 - df['nearest_border_km'] / 1000) * 0.2 +
            df['avg_road_quality'] * 0.2 +
            df['has_primary_road'].astype(float) * 0.2
        )
        
        # Checkpoint impact
        df['checkpoint_impact'] = df['checkpoint_count_50km'] / 10  # Normalized
        
        return df
    
    async def calculate_metrics(self, data: pd.DataFrame) -> InfrastructureMetrics:
        """Calculate infrastructure metrics for the dataset."""
        metrics = {}
        
        # Accessibility distribution
        if 'accessibility_level' in data.columns:
            level_counts = data['accessibility_level'].value_counts()
            total_markets = len(data)
            
            for level in ['high', 'medium', 'low', 'very_low']:
                count = level_counts.get(level, 0)
                metrics[f'{level}_accessibility_percent'] = float(count / total_markets * 100)
        
        # Average metrics
        numeric_cols = [
            'accessibility_score', 'nearest_port_km', 'nearest_border_km',
            'road_density_km_per_km2', 'avg_road_quality', 'checkpoint_count_50km'
        ]
        
        for col in numeric_cols:
            if col in data.columns:
                metrics[f'avg_{col}'] = float(data[col].mean())
        
        # Port access
        if 'nearest_port_km' in data.columns:
            metrics['markets_within_100km_port'] = int((data['nearest_port_km'] <= 100).sum())
            metrics['markets_within_200km_port'] = int((data['nearest_port_km'] <= 200).sum())
        
        # Road quality
        if 'has_primary_road' in data.columns:
            metrics['markets_with_primary_road_percent'] = float(
                data['has_primary_road'].sum() / len(data) * 100
            )
        
        # Trade potential
        if 'trade_potential' in data.columns:
            metrics['avg_trade_potential'] = float(data['trade_potential'].mean())
            metrics['high_trade_potential_markets'] = int((data['trade_potential'] > 0.7).sum())
        
        return InfrastructureMetrics(**metrics)