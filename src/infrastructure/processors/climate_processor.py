"""
Processor for climate data including rainfall (CHIRPS) and vegetation indices (MODIS).

This processor handles raster climate data and extracts values for market locations.
"""

import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple
import numpy as np
import pandas as pd
import xarray as xr
import rasterio
from rasterio.mask import mask
import geopandas as gpd
from shapely.geometry import Point
import structlog

from src.infrastructure.processors.geo_processor import GeoDataProcessor
from src.infrastructure.processors.base_processor import (
    SourceConfig, ValidationLevel
)
from src.infrastructure.data_quality.validation_framework import (
    ValidationReport, ValidationLevel as ValLevel
)
from src.core.domain.climate.entities import ClimateObservation, ClimateMetrics
from src.core.domain.climate.value_objects import ClimateVariable, TemporalResolution
from src.core.domain.market.value_objects import Coordinates
from src.infrastructure.processors.exceptions import (
    DataQualityException, TransformationException
)


logger = structlog.get_logger()


class ClimateDataProcessor(GeoDataProcessor[ClimateObservation]):
    """
    Processor for climate data from CHIRPS (rainfall) and MODIS (vegetation).
    
    Features:
        - Handles both NetCDF and GeoTIFF formats
        - Extracts values at market locations
        - Temporal aggregation (daily to monthly)
        - Anomaly detection for extreme events
        - Missing data interpolation
    """
    
    CHIRPS_VARIABLES = {
        'precipitation': {
            'name': 'precip',
            'units': 'mm',
            'scale_factor': 1.0,
            'fill_value': -9999
        }
    }
    
    MODIS_VARIABLES = {
        'ndvi': {
            'name': 'NDVI',
            'units': 'index',
            'scale_factor': 0.0001,
            'valid_range': (-2000, 10000),
            'fill_value': -3000
        },
        'evi': {
            'name': 'EVI',
            'units': 'index',
            'scale_factor': 0.0001,
            'valid_range': (-2000, 10000),
            'fill_value': -3000
        }
    }
    
    def __init__(
        self,
        source_config: SourceConfig,
        cache_manager,
        validator,
        climate_source: str,  # 'chirps' or 'modis'
        variable: str,  # 'precipitation', 'ndvi', 'evi'
        temporal_resolution: TemporalResolution = TemporalResolution.MONTHLY,
        market_locations: Optional[gpd.GeoDataFrame] = None
    ):
        super().__init__(source_config, cache_manager, validator)
        self.climate_source = climate_source.lower()
        self.variable = variable.lower()
        self.temporal_resolution = temporal_resolution
        self.market_locations = market_locations
        
        self.logger = logger.bind(
            processor="ClimateDataProcessor",
            source=climate_source,
            variable=variable
        )
        
        # Get variable configuration
        if self.climate_source == 'chirps':
            self.var_config = self.CHIRPS_VARIABLES.get(variable)
        else:
            self.var_config = self.MODIS_VARIABLES.get(variable)
            
        if not self.var_config:
            raise ValueError(f"Unknown variable {variable} for {climate_source}")
    
    async def download(self) -> xr.Dataset:
        """Download climate data from source."""
        # In production, this would download from:
        # - CHIRPS: https://data.chc.ucsb.edu/products/CHIRPS-2.0/
        # - MODIS: https://ladsweb.modaps.eosdis.nasa.gov/
        
        # For now, create sample data
        if self.climate_source == 'chirps':
            return self._create_sample_chirps_data()
        else:
            return self._create_sample_modis_data()
    
    def _create_sample_chirps_data(self) -> xr.Dataset:
        """Create sample CHIRPS precipitation data."""
        # Yemen approximate bounds
        lon_range = np.arange(42, 54, 0.05)  # 0.05 degree resolution
        lat_range = np.arange(12, 20, 0.05)
        time_range = pd.date_range('2024-01-01', '2024-12-31', freq='D')
        
        # Create synthetic precipitation data
        # More rain in highlands (Sana'a area) and monsoon areas
        lon_grid, lat_grid = np.meshgrid(lon_range, lat_range)
        
        # Base precipitation pattern
        base_precip = np.zeros((len(time_range), len(lat_range), len(lon_range)))
        
        for t_idx, date in enumerate(time_range):
            # Seasonal pattern (monsoon July-September)
            seasonal_factor = 1.0
            if date.month in [7, 8, 9]:
                seasonal_factor = 3.0
            elif date.month in [3, 4, 5]:
                seasonal_factor = 2.0
            
            # Spatial pattern (more rain in highlands)
            elevation_effect = np.exp(-((lat_grid - 15.3) ** 2 + (lon_grid - 44.2) ** 2) / 10)
            
            # Daily variability
            daily_noise = np.random.exponential(0.5, (len(lat_range), len(lon_range)))
            
            # Combine factors
            base_precip[t_idx] = seasonal_factor * elevation_effect * daily_noise * 10
        
        # Create dataset
        ds = xr.Dataset(
            {
                'precip': (['time', 'latitude', 'longitude'], base_precip)
            },
            coords={
                'time': time_range,
                'latitude': lat_range,
                'longitude': lon_range
            },
            attrs={
                'title': 'CHIRPS Daily Precipitation',
                'units': 'mm',
                'source': 'Climate Hazards Group'
            }
        )
        
        return ds
    
    def _create_sample_modis_data(self) -> xr.Dataset:
        """Create sample MODIS NDVI data."""
        # MODIS has coarser temporal resolution (16-day composites)
        lon_range = np.arange(42, 54, 0.01)  # ~1km resolution
        lat_range = np.arange(12, 20, 0.01)
        time_range = pd.date_range('2024-01-01', '2024-12-31', freq='16D')
        
        # Create synthetic NDVI data
        lon_grid, lat_grid = np.meshgrid(lon_range, lat_range)
        
        # Base NDVI pattern
        base_ndvi = np.zeros((len(time_range), len(lat_range), len(lon_range)))
        
        for t_idx, date in enumerate(time_range):
            # Seasonal greenness pattern
            seasonal_factor = 0.3 + 0.2 * np.sin(2 * np.pi * date.dayofyear / 365)
            
            # Spatial pattern (greener in highlands and valleys)
            vegetation_pattern = 0.2 + 0.5 * np.exp(-((lat_grid - 14) ** 2 + (lon_grid - 44) ** 2) / 20)
            
            # Add noise
            noise = np.random.normal(0, 0.05, (len(lat_range), len(lon_range)))
            
            # Combine (NDVI ranges from -1 to 1, typically 0.1-0.8 for vegetation)
            base_ndvi[t_idx] = np.clip(seasonal_factor + vegetation_pattern + noise, 0, 0.9)
        
        # Convert to MODIS scale (multiply by 10000)
        base_ndvi = (base_ndvi * 10000).astype(np.int16)
        
        # Create dataset
        ds = xr.Dataset(
            {
                'NDVI': (['time', 'latitude', 'longitude'], base_ndvi)
            },
            coords={
                'time': time_range,
                'latitude': lat_range,
                'longitude': lon_range
            },
            attrs={
                'title': 'MODIS 16-day NDVI',
                'scale_factor': 0.0001,
                'valid_range': '[-2000, 10000]',
                'source': 'NASA MODIS'
            }
        )
        
        return ds
    
    async def validate_specific(self, raw_data: xr.Dataset) -> ValidationReport:
        """Validate climate data specifics."""
        report = ValidationReport(source=self.config.source_id)
        
        # Check required variables
        var_name = self.var_config['name']
        if var_name not in raw_data.data_vars:
            report.add_validation(
                "variables",
                ValLevel.ERROR,
                f"Required variable '{var_name}' not found in dataset"
            )
            return report
        
        # Check coordinates
        required_coords = ['time', 'latitude', 'longitude']
        missing_coords = [c for c in required_coords if c not in raw_data.coords]
        if missing_coords:
            report.add_validation(
                "coordinates",
                ValLevel.ERROR,
                f"Missing required coordinates: {missing_coords}"
            )
        
        # Check data quality
        data_array = raw_data[var_name]
        
        # Check for fill values
        fill_value = self.var_config.get('fill_value', -9999)
        fill_count = (data_array == fill_value).sum().item()
        total_count = data_array.size
        fill_percentage = (fill_count / total_count) * 100
        
        if fill_percentage > 50:
            report.add_validation(
                "fill_values",
                ValLevel.ERROR,
                f"Too many fill values: {fill_percentage:.1f}%"
            )
        elif fill_percentage > 20:
            report.add_validation(
                "fill_values",
                ValLevel.WARNING,
                f"High percentage of fill values: {fill_percentage:.1f}%"
            )
        
        # Check valid range for MODIS
        if 'valid_range' in self.var_config:
            min_val, max_val = self.var_config['valid_range']
            out_of_range = ((data_array < min_val) | (data_array > max_val)).sum().item()
            if out_of_range > 0:
                report.add_validation(
                    "value_range",
                    ValLevel.WARNING,
                    f"{out_of_range} values outside valid range [{min_val}, {max_val}]"
                )
        
        # Check temporal coverage
        time_values = pd.to_datetime(raw_data.time.values)
        expected_freq = '16D' if self.climate_source == 'modis' else 'D'
        
        # Check for gaps
        time_diff = time_values[1:] - time_values[:-1]
        expected_delta = pd.Timedelta(expected_freq)
        gaps = np.where(time_diff > expected_delta * 1.5)[0]
        
        if len(gaps) > 0:
            report.add_validation(
                "temporal_gaps",
                ValLevel.WARNING,
                f"Found {len(gaps)} temporal gaps in data"
            )
        
        return report
    
    async def transform(self, raw_data: xr.Dataset) -> List[ClimateObservation]:
        """Transform raw climate data to ClimateObservation entities."""
        if self.market_locations is None:
            raise ValueError("Market locations required for climate data extraction")
        
        observations = []
        var_name = self.var_config['name']
        data_array = raw_data[var_name]
        
        # Apply scale factor if needed
        scale_factor = self.var_config.get('scale_factor', 1.0)
        if scale_factor != 1.0:
            data_array = data_array * scale_factor
        
        # Extract values at market locations
        for _, market in self.market_locations.iterrows():
            try:
                # Get market coordinates
                lat = market.geometry.y
                lon = market.geometry.x
                market_id = market.get('market_id', f"market_{lat:.2f}_{lon:.2f}")
                
                # Extract time series at location
                # Find nearest grid point
                lat_idx = np.abs(raw_data.latitude.values - lat).argmin()
                lon_idx = np.abs(raw_data.longitude.values - lon).argmin()
                
                # Extract values
                values = data_array.isel(latitude=lat_idx, longitude=lon_idx)
                
                # Create observations for each time step
                for time_idx, time_val in enumerate(raw_data.time.values):
                    value = float(values.isel(time=time_idx).values)
                    
                    # Skip fill values
                    if value == self.var_config.get('fill_value', -9999):
                        continue
                    
                    # Create observation
                    obs = ClimateObservation(
                        observation_date=pd.to_datetime(time_val),
                        location=Coordinates(latitude=lat, longitude=lon),
                        variable=self._get_climate_variable(),
                        value=value,
                        unit=self.var_config['units'],
                        quality_flag=self._assess_quality(value),
                        metadata={
                            'market_id': market_id,
                            'source': self.climate_source,
                            'grid_resolution': self._get_resolution(raw_data)
                        }
                    )
                    
                    observations.append(obs)
                    
            except Exception as e:
                self.logger.warning(
                    "Failed to extract climate data for market",
                    market_id=market_id,
                    error=str(e)
                )
        
        return observations
    
    def _get_climate_variable(self) -> ClimateVariable:
        """Map variable name to ClimateVariable enum."""
        mapping = {
            'precipitation': ClimateVariable.PRECIPITATION,
            'precip': ClimateVariable.PRECIPITATION,
            'ndvi': ClimateVariable.NDVI,
            'evi': ClimateVariable.EVI,
            'temperature': ClimateVariable.TEMPERATURE,
            'temp': ClimateVariable.TEMPERATURE
        }
        
        return mapping.get(self.variable.lower(), ClimateVariable.OTHER)
    
    def _assess_quality(self, value: float) -> int:
        """Assess data quality (0-100)."""
        # Simple quality assessment based on value ranges
        if self.climate_source == 'chirps':
            # Precipitation quality
            if value < 0:
                return 0  # Invalid
            elif value > 500:  # Very high daily rainfall
                return 50  # Suspicious
            else:
                return 90  # Good
        else:
            # NDVI/EVI quality
            if -0.2 <= value <= 1.0:
                return 90  # Good
            else:
                return 50  # Suspicious
    
    def _get_resolution(self, dataset: xr.Dataset) -> Dict[str, float]:
        """Get spatial resolution of dataset."""
        lat_res = float(np.abs(dataset.latitude[1] - dataset.latitude[0]))
        lon_res = float(np.abs(dataset.longitude[1] - dataset.longitude[0]))
        
        return {
            'latitude_degrees': lat_res,
            'longitude_degrees': lon_res,
            'approx_km': lat_res * 111  # Rough conversion
        }
    
    async def aggregate(self, entities: List[ClimateObservation]) -> pd.DataFrame:
        """Aggregate climate observations to specified temporal resolution."""
        if not entities:
            return pd.DataFrame()
        
        # Convert to dataframe
        records = []
        for obs in entities:
            record = {
                'date': obs.observation_date,
                'year': obs.observation_date.year,
                'month': obs.observation_date.month,
                'market_id': obs.metadata.get('market_id', 'unknown'),
                'latitude': obs.location.latitude,
                'longitude': obs.location.longitude,
                'variable': obs.variable.value,
                'value': obs.value,
                'unit': obs.unit,
                'quality': obs.quality_flag
            }
            records.append(record)
        
        df = pd.DataFrame(records)
        
        # Aggregate based on temporal resolution
        if self.temporal_resolution == TemporalResolution.MONTHLY:
            # Monthly aggregation
            aggregated = df.groupby(['market_id', 'year', 'month', 'variable']).agg({
                'value': ['mean', 'sum', 'min', 'max', 'std'],
                'quality': 'mean',
                'latitude': 'first',
                'longitude': 'first',
                'date': 'count'
            }).reset_index()
            
            # Flatten column names
            aggregated.columns = ['_'.join(col).strip() if col[1] else col[0] 
                                  for col in aggregated.columns.values]
            
            # Rename columns
            aggregated.rename(columns={
                'value_mean': f'{self.variable}_mean',
                'value_sum': f'{self.variable}_sum',
                'value_min': f'{self.variable}_min',
                'value_max': f'{self.variable}_max',
                'value_std': f'{self.variable}_std',
                'quality_mean': 'quality_score',
                'date_count': 'n_observations',
                'latitude_first': 'latitude',
                'longitude_first': 'longitude'
            }, inplace=True)
            
            # Add temporal key
            aggregated['temporal_key'] = (
                aggregated['year'].astype(str) + '-' + 
                aggregated['month'].astype(str).str.zfill(2)
            )
            
            # Calculate anomalies
            if self.climate_source == 'chirps':
                # Precipitation anomaly (% of normal)
                monthly_avg = aggregated.groupby(['market_id', 'month'])[f'{self.variable}_mean'].mean()
                aggregated = aggregated.merge(
                    monthly_avg.rename('monthly_normal'),
                    on=['market_id', 'month']
                )
                aggregated['anomaly_percent'] = (
                    (aggregated[f'{self.variable}_mean'] / aggregated['monthly_normal'] - 1) * 100
                )
            
        elif self.temporal_resolution == TemporalResolution.WEEKLY:
            # Weekly aggregation
            df['week'] = df['date'].dt.isocalendar().week
            aggregated = df.groupby(['market_id', 'year', 'week', 'variable']).agg({
                'value': ['mean', 'sum', 'min', 'max'],
                'quality': 'mean',
                'latitude': 'first',
                'longitude': 'first'
            }).reset_index()
            
        else:
            # Daily - no aggregation needed
            aggregated = df
        
        return aggregated
    
    async def calculate_metrics(self, data: pd.DataFrame) -> ClimateMetrics:
        """Calculate climate metrics for the dataset."""
        metrics = {}
        
        if self.climate_source == 'chirps':
            # Precipitation metrics
            metrics['total_rainfall'] = float(data[f'{self.variable}_sum'].sum())
            metrics['avg_rainfall'] = float(data[f'{self.variable}_mean'].mean())
            metrics['max_daily_rainfall'] = float(data[f'{self.variable}_max'].max())
            metrics['dry_days_percent'] = float(
                (data[f'{self.variable}_mean'] < 1).sum() / len(data) * 100
            )
            
            # Drought indicators
            if 'anomaly_percent' in data.columns:
                metrics['drought_months'] = int((data['anomaly_percent'] < -50).sum())
                metrics['flood_risk_days'] = int((data[f'{self.variable}_max'] > 50).sum())
                
        else:
            # Vegetation metrics
            metrics['avg_ndvi'] = float(data[f'{self.variable}_mean'].mean())
            metrics['min_ndvi'] = float(data[f'{self.variable}_min'].min())
            metrics['vegetation_stress_percent'] = float(
                (data[f'{self.variable}_mean'] < 0.3).sum() / len(data) * 100
            )
            
        return ClimateMetrics(**metrics)