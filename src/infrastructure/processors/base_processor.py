"""
Base processor framework for all data source processors.

This module provides the foundation for building data processors that handle
the complete pipeline from raw data download to validated, aggregated output.
The framework ensures consistency across all data sources while allowing
flexibility for source-specific requirements.

Example Usage:
    ```python
    # Define a processor for WFP price data
    class WFPProcessor(DataFrameProcessor[pd.DataFrame, Price, pd.DataFrame]):
        async def download(self) -> pd.DataFrame:
            # Download CSV from WFP API
            return await self.client.get_prices()
        
        async def transform(self, raw_data: pd.DataFrame) -> List[Price]:
            # Convert rows to Price entities
            return [Price.from_row(row) for _, row in raw_data.iterrows()]
        
        async def aggregate(self, entities: List[Price]) -> pd.DataFrame:
            # Aggregate to monthly frequency
            return aggregate_prices_monthly(entities)
    
    # Use the processor
    config = SourceConfig(
        source_id="wfp_prices",
        source_type="csv",
        update_frequency="weekly",
        cache_ttl=86400  # 24 hours
    )
    
    processor = WFPProcessor(config, cache_manager, validator)
    result = await processor.process()
    
    if result.success:
        print(f"Processed {len(result.data)} price records")
    else:
        print(f"Processing failed: {result.errors}")
    ```

Key Features:
    - Automatic retry with exponential backoff
    - Intelligent caching to reduce API calls
    - Multi-level validation (schema, constraints, business rules)
    - Progress tracking and structured logging
    - Generic types for flexibility
    - Async/await for concurrent processing

Extension Points:
    - download(): Implement source-specific data retrieval
    - validate(): Add custom validation rules
    - transform(): Convert raw data to domain entities
    - aggregate(): Reduce data to panel frequency
"""
import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, TypeVar, Generic, Callable
import pandas as pd
import structlog
from tenacity import retry, stop_after_attempt, wait_exponential

from src.core.domain.shared.value_objects import TemporalKey
from src.infrastructure.data_quality.validation_framework import (
    ValidationReport, DataValidator, ValidationLevel
)
from src.infrastructure.caching.cache_manager import CacheManager
from src.infrastructure.processors.exceptions import (
    ProcessorException, DownloadException, ValidationException,
    TransformationException, AggregationException, ErrorContext,
    create_processor_exception
)
from src.infrastructure.processors.error_recovery import (
    ErrorRecoveryManager, with_retry, with_circuit_breaker
)


logger = structlog.get_logger()

T = TypeVar('T')  # Raw data type
E = TypeVar('E')  # Entity type
A = TypeVar('A')  # Aggregated type


@dataclass
class SourceConfig:
    """
    Configuration for a data source processor.
    
    This configuration controls how data is fetched, validated, and cached.
    Each data source should have its own configuration instance.
    
    Attributes:
        source_id: Unique identifier for the data source (e.g., "wfp_prices")
        source_type: Type of data source (e.g., "csv", "api", "shapefile")
        update_frequency: How often the source updates ("daily", "weekly", "monthly")
        cache_ttl: Time-to-live for cached data in seconds
        validation_level: Strictness of validation (STRICT, WARN, LENIENT)
        retry_attempts: Number of retry attempts for failed downloads
        timeout: Maximum time in seconds for download operations
        
    Example:
        ```python
        config = SourceConfig(
            source_id="acled_conflict",
            source_type="api",
            update_frequency="weekly",
            cache_ttl=7 * 24 * 3600,  # 1 week
            validation_level=ValidationLevel.STRICT,
            retry_attempts=5,
            timeout=600
        )
        ```
    """
    source_id: str
    source_type: str
    update_frequency: str  # daily, weekly, monthly
    cache_ttl: int  # seconds
    validation_level: ValidationLevel = ValidationLevel.STRICT
    retry_attempts: int = 3
    timeout: int = 300  # seconds


@dataclass
class ProcessingResult(Generic[A]):
    """
    Result of processing a data source.
    
    Encapsulates the complete outcome of a processing operation, including
    the processed data, validation results, and performance metrics.
    
    Attributes:
        success: Whether processing completed successfully
        data: The processed and aggregated data (None if failed)
        validation_report: Detailed validation results and metrics
        processing_time: Total processing time in seconds
        source_version: Version/timestamp of the source data
        errors: List of error messages if processing failed
        
    Type Parameters:
        A: Type of the aggregated output data
    """
    success: bool
    data: Optional[A]
    validation_report: ValidationReport
    processing_time: float
    source_version: str
    errors: List[str] = None
    
    @property
    def has_warnings(self) -> bool:
        """Check if there are any validation warnings."""
        return len(self.validation_report.warnings) > 0
    
    @property
    def error_summary(self) -> str:
        """Get a summary of all errors."""
        if self.errors:
            return "; ".join(self.errors)
        return "No errors"


class BaseProcessor(ABC, Generic[T, E, A]):
    """
    Base class for all data processors.
    
    Provides common functionality for downloading, validating, transforming,
    and aggregating data from various sources.
    """
    
    def __init__(
        self,
        source_config: SourceConfig,
        cache_manager: CacheManager,
        validator: DataValidator,
        recovery_manager: Optional[ErrorRecoveryManager] = None
    ):
        self.config = source_config
        self.cache = cache_manager
        self.validator = validator
        self.recovery_manager = recovery_manager or ErrorRecoveryManager()
        self.logger = logger.bind(processor=self.__class__.__name__)
        
    async def process(
        self, 
        force_refresh: bool = False,
        progress_callback: Optional[Callable[[str, float], None]] = None
    ) -> ProcessingResult[A]:
        """
        Main processing pipeline for data source.
        
        This is the primary entry point that orchestrates the complete data
        processing workflow from download to final aggregation.
        
        Args:
            force_refresh: If True, bypass cache and download fresh data
            progress_callback: Optional callback for progress updates.
                              Called with (stage_name, progress_percentage)
        
        Returns:
            ProcessingResult containing the processed data or error information
            
        Raises:
            ValueError: If validation fails with STRICT validation level
            Exception: Any unhandled errors are caught and returned in result
            
        Processing Steps:
            1. Check cache (unless force_refresh)
            2. Download raw data with retry logic
            3. Validate raw data quality
            4. Transform to domain entities
            5. Aggregate to panel frequency
            6. Cache successful results
            
        Example:
            ```python
            # Basic usage
            result = await processor.process()
            
            # Force fresh download
            result = await processor.process(force_refresh=True)
            
            # With progress tracking
            def track_progress(stage: str, pct: float):
                print(f"{stage}: {pct:.0f}%")
                
            result = await processor.process(progress_callback=track_progress)
            ```
        """
        start_time = datetime.now()
        
        try:
            # Progress tracking helper
            async def update_progress(stage: str, percentage: float):
                if progress_callback:
                    progress_callback(stage, percentage)
            
            # Check cache first
            if not force_refresh:
                await update_progress("Checking cache", 5)
                cached_data = await self._get_from_cache()
                if cached_data is not None:
                    self.logger.info("Using cached data", source=self.config.source_id)
                    await update_progress("Complete (from cache)", 100)
                    return cached_data
            
            # Download raw data
            await update_progress("Downloading", 10)
            self.logger.info("Downloading data", source=self.config.source_id)
            raw_data = await self._download_with_retry()
            await update_progress("Download complete", 30)
            
            # Validate raw data
            await update_progress("Validating", 40)
            self.logger.info("Validating raw data")
            validation_report = await self.validate(raw_data)
            await update_progress("Validation complete", 50)
            
            if not validation_report.is_valid and self.config.validation_level == ValidationLevel.STRICT:
                raise ValueError(f"Data validation failed: {validation_report.errors}")
            
            # Transform to entities
            await update_progress("Transforming", 60)
            self.logger.info("Transforming to domain entities")
            entities = await self.transform(raw_data)
            await update_progress("Transformation complete", 80)
            
            # Aggregate to panel frequency
            await update_progress("Aggregating", 90)
            self.logger.info("Aggregating to panel frequency")
            aggregated_data = await self.aggregate(entities)
            await update_progress("Aggregation complete", 95)
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Create result
            result = ProcessingResult(
                success=True,
                data=aggregated_data,
                validation_report=validation_report,
                processing_time=processing_time,
                source_version=await self._get_source_version(raw_data)
            )
            
            # Cache successful result
            await self._save_to_cache(result)
            
            # Final progress update
            await update_progress("Complete", 100)
            
            self.logger.info(
                "Processing complete",
                source=self.config.source_id,
                processing_time=processing_time
            )
            
            return result
            
        except Exception as e:
            # Create contextualized exception
            error_context = ErrorContext(
                source_id=self.config.source_id,
                processor_type=self.__class__.__name__,
                stage="unknown",
                retry_count=0
            )
            
            # Determine stage where error occurred
            if 'download' in str(e).lower():
                error_context.stage = "download"
            elif 'validat' in str(e).lower():
                error_context.stage = "validate"
            elif 'transform' in str(e).lower():
                error_context.stage = "transform"
            elif 'aggregat' in str(e).lower():
                error_context.stage = "aggregate"
            
            # Create appropriate exception type
            processor_exc = create_processor_exception(
                e, error_context, self.__class__.__name__
            )
            
            self.logger.error(
                "processing_failed",
                source=self.config.source_id,
                stage=error_context.stage,
                error=str(e),
                error_type=type(e).__name__,
                is_retryable=processor_exc.is_retryable
            )
            
            # Queue for retry if appropriate
            if processor_exc.is_retryable:
                self.recovery_manager.queue_error_for_retry(processor_exc)
            
            return ProcessingResult(
                success=False,
                data=None,
                validation_report=ValidationReport(
                    source=self.config.source_id,
                    errors=[str(e)],
                    warnings=processor_exc.recovery_suggestions if hasattr(processor_exc, 'recovery_suggestions') else []
                ),
                processing_time=(datetime.now() - start_time).total_seconds(),
                source_version="unknown",
                errors=[str(e)]
            )
    
    async def _download_with_retry(self) -> T:
        """Download with exponential backoff retry and circuit breaker."""
        # Use circuit breaker for the source
        async with self.recovery_manager.circuit_breaker(self.config.source_id):
            # Apply retry decorator dynamically
            @with_retry(
                max_retries=self.config.retry_attempts,
                on_exceptions=(DownloadException, TimeoutError, ConnectionError)
            )
            async def download_with_recovery():
                try:
                    return await self.download()
                except Exception as e:
                    # Wrap in appropriate exception type
                    if not isinstance(e, ProcessorException):
                        context = ErrorContext(
                            source_id=self.config.source_id,
                            processor_type=self.__class__.__name__,
                            stage="download"
                        )
                        raise create_processor_exception(e, context, self.__class__.__name__)
                    raise
            
            return await download_with_recovery()
    
    async def _get_from_cache(self) -> Optional[ProcessingResult[A]]:
        """Get processed data from cache if available."""
        cache_key = f"{self.config.source_id}_processed"
        return await self.cache.get(cache_key)
    
    async def _save_to_cache(self, result: ProcessingResult[A]) -> None:
        """Save processed data to cache."""
        cache_key = f"{self.config.source_id}_processed"
        await self.cache.set(cache_key, result, ttl=self.config.cache_ttl)
    
    async def _get_source_version(self, raw_data: T) -> str:
        """Extract version/timestamp from raw data."""
        # Default implementation - override in subclasses
        return datetime.now().isoformat()
    
    @abstractmethod
    async def download(self) -> T:
        """
        Download raw data from the source.
        
        This method should handle the specifics of connecting to the data source
        and retrieving the raw data. Authentication, request formatting, and
        response parsing should all be handled here.
        
        Returns:
            T: Raw data in the source's native format
            
        Raises:
            DownloadError: If the download fails
            TimeoutError: If the download exceeds the configured timeout
            
        Example Implementation:
            ```python
            async def download(self) -> pd.DataFrame:
                async with httpx.AsyncClient() as client:
                    response = await client.get(
                        self.api_url,
                        headers={"Authorization": f"Bearer {self.api_key}"},
                        timeout=self.config.timeout
                    )
                    response.raise_for_status()
                    return pd.read_csv(io.StringIO(response.text))
            ```
        """
        pass
    
    @abstractmethod
    async def validate(self, raw_data: T) -> ValidationReport:
        """
        Validate the quality and integrity of raw data.
        
        Implement data quality checks specific to your source. This should
        catch data issues early before they propagate through the pipeline.
        
        Args:
            raw_data: The raw data to validate
            
        Returns:
            ValidationReport with errors, warnings, and metrics
            
        Validation Checks Should Include:
            - Schema validation (required fields present)
            - Data type validation
            - Range/constraint validation
            - Completeness checks
            - Consistency checks
            
        Example Implementation:
            ```python
            async def validate(self, raw_data: pd.DataFrame) -> ValidationReport:
                report = ValidationReport(source=self.config.source_id)
                
                # Check required columns
                required = ['date', 'market', 'price', 'commodity']
                missing = set(required) - set(raw_data.columns)
                if missing:
                    report.add_error(f"Missing columns: {missing}")
                
                # Check for nulls
                null_counts = raw_data[required].isnull().sum()
                if null_counts.any():
                    report.add_warning(f"Null values found: {null_counts.to_dict()}")
                
                # Check price ranges
                if (raw_data['price'] < 0).any():
                    report.add_error("Negative prices found")
                    
                return report
            ```
        """
        pass
    
    @abstractmethod
    async def transform(self, raw_data: T) -> List[E]:
        """
        Transform raw data into domain entities.
        
        This method converts source-specific data formats into standardized
        domain entities that can be used throughout the application.
        
        Args:
            raw_data: Validated raw data
            
        Returns:
            List of domain entities
            
        Transformation Should:
            - Map source fields to entity attributes
            - Convert data types appropriately
            - Apply business logic transformations
            - Handle source-specific quirks
            
        Example Implementation:
            ```python
            async def transform(self, raw_data: pd.DataFrame) -> List[Price]:
                entities = []
                
                for _, row in raw_data.iterrows():
                    price = Price(
                        market_id=row['market_code'],
                        commodity_id=self._map_commodity(row['item']),
                        value=Decimal(str(row['price'])),
                        currency=row.get('currency', 'YER'),
                        date=pd.to_datetime(row['date']),
                        unit=row.get('unit', 'kg')
                    )
                    entities.append(price)
                    
                return entities
            ```
        """
        pass
    
    @abstractmethod
    async def aggregate(self, entities: List[E]) -> A:
        """
        Aggregate entities to the desired panel frequency.
        
        This method reduces the granular entity data to the frequency needed
        for analysis (typically monthly for the Yemen project).
        
        Args:
            entities: List of transformed domain entities
            
        Returns:
            Aggregated data structure ready for analysis
            
        Aggregation Should Consider:
            - Temporal aggregation (daily -> monthly)
            - Spatial aggregation if needed
            - Appropriate aggregation functions (mean, sum, etc.)
            - Handling of missing periods
            
        Example Implementation:
            ```python
            async def aggregate(self, entities: List[Price]) -> pd.DataFrame:
                # Convert to DataFrame
                df = pd.DataFrame([e.to_dict() for e in entities])
                
                # Set temporal key
                df['year_month'] = df['date'].dt.to_period('M')
                
                # Aggregate to monthly
                monthly = df.groupby(['market_id', 'commodity_id', 'year_month']).agg({
                    'value': 'mean',  # Average price
                    'observations': 'count'  # Number of observations
                }).reset_index()
                
                return monthly
            ```
        """
        pass


class DataFrameProcessor(BaseProcessor[pd.DataFrame, E, pd.DataFrame]):
    """
    Base processor for DataFrame-based data sources.
    
    This specialized processor provides common functionality for sources that
    provide data in tabular formats (CSV, Excel, etc.). It handles standard
    DataFrame validation while allowing subclasses to add source-specific checks.
    
    Type Parameters:
        E: Entity type that rows will be transformed into
        
    Features:
        - Automatic DataFrame validation (empty, duplicates, columns)
        - Memory usage tracking
        - Chunked processing support for large files
        - Column type inference and validation
        
    Example:
        ```python
        class PriceDataProcessor(DataFrameProcessor[Price]):
            def get_required_columns(self) -> List[str]:
                return ['date', 'market', 'commodity', 'price', 'currency']
            
            async def validate_specific(self, df: pd.DataFrame) -> ValidationReport:
                report = ValidationReport(source="prices")
                
                # Check date format
                try:
                    pd.to_datetime(df['date'])
                except:
                    report.add_error("Invalid date format")
                    
                # Check price ranges
                if df['price'].min() < 0:
                    report.add_error("Negative prices found")
                    
                return report
        ```
    """
    
    async def validate(self, raw_data: pd.DataFrame) -> ValidationReport:
        """Common DataFrame validation."""
        errors = []
        warnings = []
        
        # Check if empty
        if raw_data.empty:
            errors.append("DataFrame is empty")
        
        # Check for required columns
        required_columns = self.get_required_columns()
        missing_columns = set(required_columns) - set(raw_data.columns)
        if missing_columns:
            errors.append(f"Missing required columns: {missing_columns}")
        
        # Check for duplicates
        if raw_data.duplicated().any():
            warnings.append(f"Found {raw_data.duplicated().sum()} duplicate rows")
        
        # Subclass-specific validation
        subclass_report = await self.validate_specific(raw_data)
        errors.extend(subclass_report.errors)
        warnings.extend(subclass_report.warnings)
        
        return ValidationReport(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            metrics={
                'row_count': len(raw_data),
                'column_count': len(raw_data.columns),
                'memory_usage': raw_data.memory_usage(deep=True).sum()
            }
        )
    
    @abstractmethod
    def get_required_columns(self) -> List[str]:
        """Return list of required columns for this data source."""
        pass
    
    @abstractmethod
    async def validate_specific(self, raw_data: pd.DataFrame) -> ValidationReport:
        """Subclass-specific validation logic."""
        pass