"""World Bank publication-quality LaTeX table generator.

This module creates publication-ready LaTeX tables following World Bank
standards for econometric results presentation.
"""

import re
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd

import logging

logger = logging.getLogger(__name__)


@dataclass
class ModelResult:
    """Container for model results to include in tables."""
    
    name: str
    coefficients: Dict[str, float]
    std_errors: Dict[str, float]
    p_values: Dict[str, float]
    n_observations: int
    r_squared: float
    additional_stats: Dict[str, Union[float, str]] = None
    fixed_effects: List[str] = None
    clustering: str = None
    
    def __post_init__(self):
        if self.additional_stats is None:
            self.additional_stats = {}
        if self.fixed_effects is None:
            self.fixed_effects = []


class WorldBankTableGenerator:
    """Generate publication-quality LaTeX tables with World Bank standards."""
    
    def __init__(self):
        """Initialize table generator with default settings."""
        self.star_levels = {
            0.01: "***",
            0.05: "**",
            0.10: "*"
        }
        self.decimal_places = 3
        self.se_format = "parentheses"  # or "brackets"
        
    def format_coefficient(self, coef: float, p_value: float) -> str:
        """Format coefficient with significance stars.
        
        Args:
            coef: Coefficient value
            p_value: P-value for significance
            
        Returns:
            Formatted string with stars if significant
        """
        # Format coefficient
        coef_str = f"{coef:.{self.decimal_places}f}"
        
        # Add significance stars
        stars = ""
        for threshold, star in sorted(self.star_levels.items()):
            if p_value < threshold:
                stars = star
                
        return f"{coef_str}{stars}"
    
    def format_std_error(self, se: float) -> str:
        """Format standard error with parentheses or brackets.
        
        Args:
            se: Standard error value
            
        Returns:
            Formatted string with parentheses/brackets
        """
        se_str = f"{se:.{self.decimal_places}f}"
        
        if self.se_format == "parentheses":
            return f"({se_str})"
        else:
            return f"[{se_str}]"
    
    def create_main_results_table(self, 
                                  models: List[ModelResult],
                                  main_vars: List[str],
                                  control_vars: Optional[List[str]] = None,
                                  title: str = "Main Results",
                                  label: str = "tab:main_results",
                                  notes: Optional[str] = None) -> str:
        """Create main results table comparing multiple models.
        
        Args:
            models: List of model results to include
            main_vars: Main variables to display prominently
            control_vars: Control variables to include
            title: Table title
            label: LaTeX label for referencing
            notes: Table notes
            
        Returns:
            LaTeX table as string
        """
        if control_vars is None:
            control_vars = []
            
        # Start building table
        lines = []
        lines.append("\\begin{table}[htbp]")
        lines.append("\\centering")
        lines.append(f"\\caption{{{title}}}")
        lines.append(f"\\label{{{label}}}")
        
        # Create column specification
        col_spec = "l" + "c" * len(models)
        lines.append(f"\\begin{{tabular}}{{{col_spec}}}")
        lines.append("\\toprule")
        
        # Header row with model names
        header = " & " + " & ".join([f"({i+1})" for i in range(len(models))]) + " \\\\"
        lines.append(header)
        
        model_names = " & " + " & ".join([m.name for m in models]) + " \\\\"
        lines.append(model_names)
        lines.append("\\midrule")
        
        # Main variables section
        all_main_vars = set()
        for model in models:
            all_main_vars.update(v for v in main_vars if v in model.coefficients)
        
        for var in main_vars:
            if var not in all_main_vars:
                continue
                
            # Variable name row
            var_display = self._format_variable_name(var)
            row = [var_display]
            
            # Coefficients for each model
            for model in models:
                if var in model.coefficients:
                    coef = model.coefficients[var]
                    p_val = model.p_values[var]
                    row.append(self.format_coefficient(coef, p_val))
                else:
                    row.append("")
            
            lines.append(" & ".join(row) + " \\\\")
            
            # Standard errors row
            se_row = [""]
            for model in models:
                if var in model.std_errors:
                    se = model.std_errors[var]
                    se_row.append(self.format_std_error(se))
                else:
                    se_row.append("")
            
            lines.append(" & ".join(se_row) + " \\\\")
            lines.append("")  # Empty line for spacing
        
        # Control variables section (if any)
        if control_vars:
            lines.append("\\midrule")
            
            all_controls = set()
            for model in models:
                all_controls.update(v for v in control_vars if v in model.coefficients)
            
            if all_controls:
                lines.append("\\textit{Controls} & & & & \\\\")
                
                for var in control_vars:
                    if var not in all_controls:
                        continue
                        
                    var_display = self._format_variable_name(var)
                    row = [f"\\quad {var_display}"]
                    
                    for model in models:
                        if var in model.coefficients:
                            coef = model.coefficients[var]
                            p_val = model.p_values[var]
                            row.append(self.format_coefficient(coef, p_val))
                        else:
                            row.append("")
                    
                    lines.append(" & ".join(row) + " \\\\")
                    
                    # Standard errors
                    se_row = [""]
                    for model in models:
                        if var in model.std_errors:
                            se = model.std_errors[var]
                            se_row.append(self.format_std_error(se))
                        else:
                            se_row.append("")
                    
                    lines.append(" & ".join(se_row) + " \\\\")
        
        # Model statistics section
        lines.append("\\midrule")
        
        # Fixed effects indicators
        fe_types = ["Market FE", "Time FE", "Commodity FE"]
        for fe_type in fe_types:
            row = [fe_type]
            for model in models:
                if fe_type.lower().replace(" fe", "") in [fe.lower() for fe in model.fixed_effects]:
                    row.append("Yes")
                else:
                    row.append("No")
            lines.append(" & ".join(row) + " \\\\")
        
        # Clustering
        row = ["Clustering"]
        for model in models:
            row.append(model.clustering if model.clustering else "None")
        lines.append(" & ".join(row) + " \\\\")
        
        lines.append("\\midrule")
        
        # Observations
        row = ["Observations"]
        for model in models:
            row.append(f"{model.n_observations:,}")
        lines.append(" & ".join(row) + " \\\\")
        
        # R-squared
        row = ["R-squared"]
        for model in models:
            row.append(f"{model.r_squared:.{self.decimal_places}f}")
        lines.append(" & ".join(row) + " \\\\")
        
        # Additional statistics
        all_stats = set()
        for model in models:
            all_stats.update(model.additional_stats.keys())
        
        for stat in sorted(all_stats):
            row = [self._format_variable_name(stat)]
            for model in models:
                if stat in model.additional_stats:
                    value = model.additional_stats[stat]
                    if isinstance(value, float):
                        row.append(f"{value:.{self.decimal_places}f}")
                    else:
                        row.append(str(value))
                else:
                    row.append("")
            lines.append(" & ".join(row) + " \\\\")
        
        lines.append("\\bottomrule")
        lines.append("\\end{tabular}")
        
        # Add notes
        if notes is None:
            notes = self._default_notes()
        
        # Split notes into multiple lines if too long
        note_lines = self._wrap_notes(notes, max_width=80)
        for note_line in note_lines:
            lines.append(f"\\multicolumn{{{len(models)+1}}}{{l}}{{\\footnotesize {note_line}}} \\\\")
        
        lines.append("\\end{table}")
        
        return "\n".join(lines)
    
    def create_robustness_table(self,
                               base_model: ModelResult,
                               robustness_models: List[ModelResult],
                               main_var: str,
                               title: str = "Robustness Checks",
                               label: str = "tab:robustness") -> str:
        """Create robustness checks table.
        
        Args:
            base_model: Baseline model result
            robustness_models: Alternative specifications
            main_var: Main variable of interest
            title: Table title
            label: LaTeX label
            
        Returns:
            LaTeX table as string
        """
        all_models = [base_model] + robustness_models
        
        lines = []
        lines.append("\\begin{table}[htbp]")
        lines.append("\\centering")
        lines.append(f"\\caption{{{title}}}")
        lines.append(f"\\label{{{label}}}")
        
        # Create column specification
        col_spec = "l" + "c" * len(all_models)
        lines.append(f"\\begin{{tabular}}{{{col_spec}}}")
        lines.append("\\toprule")
        
        # Header
        header = " & " + " & ".join([f"({i+1})" for i in range(len(all_models))]) + " \\\\"
        lines.append(header)
        
        model_names = " & Baseline & " + " & ".join([m.name for m in robustness_models]) + " \\\\"
        lines.append(model_names)
        lines.append("\\midrule")
        
        # Main variable coefficient
        var_display = self._format_variable_name(main_var)
        row = [var_display]
        
        for model in all_models:
            if main_var in model.coefficients:
                coef = model.coefficients[main_var]
                p_val = model.p_values[main_var]
                row.append(self.format_coefficient(coef, p_val))
            else:
                row.append("")
        
        lines.append(" & ".join(row) + " \\\\")
        
        # Standard errors
        se_row = [""]
        for model in all_models:
            if main_var in model.std_errors:
                se = model.std_errors[main_var]
                se_row.append(self.format_std_error(se))
            else:
                se_row.append("")
        
        lines.append(" & ".join(se_row) + " \\\\")
        
        # Model details
        lines.append("\\midrule")
        lines.append("\\textit{Specification Details} & & & & \\\\")
        
        # Sample period
        row = ["Sample Period"]
        for i, model in enumerate(all_models):
            if i == 0:
                row.append("Full")
            else:
                period = model.additional_stats.get("sample_period", "Full")
                row.append(period)
        lines.append(" & ".join(row) + " \\\\")
        
        # Functional form
        row = ["Functional Form"]
        for i, model in enumerate(all_models):
            form = model.additional_stats.get("functional_form", "Linear")
            row.append(form)
        lines.append(" & ".join(row) + " \\\\")
        
        # Estimation method
        row = ["Estimation"]
        for model in all_models:
            method = model.additional_stats.get("estimation_method", "OLS")
            row.append(method.upper())
        lines.append(" & ".join(row) + " \\\\")
        
        lines.append("\\midrule")
        
        # Observations and R-squared
        row = ["Observations"]
        for model in all_models:
            row.append(f"{model.n_observations:,}")
        lines.append(" & ".join(row) + " \\\\")
        
        row = ["R-squared"]
        for model in all_models:
            row.append(f"{model.r_squared:.{self.decimal_places}f}")
        lines.append(" & ".join(row) + " \\\\")
        
        lines.append("\\bottomrule")
        lines.append("\\end{tabular}")
        
        # Notes
        notes = "Notes: " + self._default_notes()
        note_lines = self._wrap_notes(notes, max_width=80)
        for note_line in note_lines:
            lines.append(f"\\multicolumn{{{len(all_models)+1}}}{{l}}{{\\footnotesize {note_line}}} \\\\")
        
        lines.append("\\end{table}")
        
        return "\n".join(lines)
    
    def create_heterogeneity_table(self,
                                  subgroup_results: Dict[str, ModelResult],
                                  main_var: str,
                                  title: str = "Heterogeneous Effects",
                                  label: str = "tab:heterogeneity") -> str:
        """Create heterogeneity analysis table.
        
        Args:
            subgroup_results: Dictionary mapping subgroup names to results
            main_var: Main variable of interest
            title: Table title
            label: LaTeX label
            
        Returns:
            LaTeX table as string
        """
        lines = []
        lines.append("\\begin{table}[htbp]")
        lines.append("\\centering")
        lines.append(f"\\caption{{{title}}}")
        lines.append(f"\\label{{{label}}}")
        
        lines.append("\\begin{tabular}{lcccc}")
        lines.append("\\toprule")
        lines.append("Subgroup & Coefficient & Std. Error & P-value & N \\\\")
        lines.append("\\midrule")
        
        for subgroup, result in subgroup_results.items():
            if main_var in result.coefficients:
                coef = result.coefficients[main_var]
                se = result.std_errors[main_var]
                p_val = result.p_values[main_var]
                n_obs = result.n_observations
                
                coef_str = self.format_coefficient(coef, p_val)
                
                lines.append(f"{subgroup} & {coef_str} & {se:.{self.decimal_places}f} & "
                           f"{p_val:.{self.decimal_places}f} & {n_obs:,} \\\\")
        
        lines.append("\\bottomrule")
        lines.append("\\end{tabular}")
        
        # Notes
        notes = self._default_notes()
        lines.append(f"\\begin{{tablenotes}}")
        lines.append(f"\\footnotesize")
        lines.append(f"\\item {notes}")
        lines.append(f"\\end{{tablenotes}}")
        
        lines.append("\\end{table}")
        
        return "\n".join(lines)
    
    def create_summary_statistics_table(self,
                                       data: pd.DataFrame,
                                       variables: List[str],
                                       by_group: Optional[str] = None,
                                       title: str = "Summary Statistics",
                                       label: str = "tab:summary_stats") -> str:
        """Create summary statistics table.
        
        Args:
            data: DataFrame with data
            variables: Variables to include
            by_group: Optional grouping variable
            title: Table title
            label: LaTeX label
            
        Returns:
            LaTeX table as string
        """
        lines = []
        lines.append("\\begin{table}[htbp]")
        lines.append("\\centering")
        lines.append(f"\\caption{{{title}}}")
        lines.append(f"\\label{{{label}}}")
        
        if by_group:
            # Grouped summary statistics
            groups = sorted(data[by_group].unique())
            col_spec = "l" + "ccc" * len(groups)
            lines.append(f"\\begin{{tabular}}{{{col_spec}}}")
            lines.append("\\toprule")
            
            # Header
            header = " & " + " & ".join([f"\\multicolumn{{3}}{{c}}{{{g}}}" for g in groups]) + " \\\\"
            lines.append(header)
            lines.append("\\cmidrule(lr){2-4} \\cmidrule(lr){5-7}")
            
            subheader = "Variable & " + " & ".join(["Mean & SD & N"] * len(groups)) + " \\\\"
            lines.append(subheader)
            lines.append("\\midrule")
            
            # Variable rows
            for var in variables:
                row = [self._format_variable_name(var)]
                
                for group in groups:
                    group_data = data[data[by_group] == group][var].dropna()
                    mean = group_data.mean()
                    std = group_data.std()
                    n = len(group_data)
                    
                    row.extend([
                        f"{mean:.{self.decimal_places}f}",
                        f"{std:.{self.decimal_places}f}",
                        f"{n:,}"
                    ])
                
                lines.append(" & ".join(row) + " \\\\")
            
        else:
            # Simple summary statistics
            lines.append("\\begin{tabular}{lccccc}")
            lines.append("\\toprule")
            lines.append("Variable & Mean & SD & Min & Max & N \\\\")
            lines.append("\\midrule")
            
            for var in variables:
                var_data = data[var].dropna()
                
                row = [
                    self._format_variable_name(var),
                    f"{var_data.mean():.{self.decimal_places}f}",
                    f"{var_data.std():.{self.decimal_places}f}",
                    f"{var_data.min():.{self.decimal_places}f}",
                    f"{var_data.max():.{self.decimal_places}f}",
                    f"{len(var_data):,}"
                ]
                
                lines.append(" & ".join(row) + " \\\\")
        
        lines.append("\\bottomrule")
        lines.append("\\end{tabular}")
        lines.append("\\end{table}")
        
        return "\n".join(lines)
    
    def _format_variable_name(self, var_name: str) -> str:
        """Format variable name for display.
        
        Args:
            var_name: Raw variable name
            
        Returns:
            Formatted name for LaTeX
        """
        # Common replacements
        replacements = {
            "log_": "Log ",
            "_usd": " (USD)",
            "_yer": " (YER)",
            "_": " ",
            "exchange_rate": "Exchange Rate",
            "conflict_intensity": "Conflict Intensity",
            "aid_distribution": "Aid Distribution",
            "population_density": "Population Density",
            "distance_to_border": "Distance to Border"
        }
        
        formatted = var_name
        for old, new in replacements.items():
            formatted = formatted.replace(old, new)
        
        # Title case
        formatted = formatted.title()
        
        return formatted
    
    def _default_notes(self) -> str:
        """Generate default table notes."""
        return (f"Standard errors in {self.se_format}. "
                f"*** p<0.01, ** p<0.05, * p<0.1")
    
    def _wrap_notes(self, notes: str, max_width: int = 80) -> List[str]:
        """Wrap long notes into multiple lines.
        
        Args:
            notes: Notes text
            max_width: Maximum characters per line
            
        Returns:
            List of wrapped lines
        """
        words = notes.split()
        lines = []
        current_line = []
        current_length = 0
        
        for word in words:
            word_length = len(word) + 1  # +1 for space
            if current_length + word_length > max_width and current_line:
                lines.append(" ".join(current_line))
                current_line = [word]
                current_length = word_length
            else:
                current_line.append(word)
                current_length += word_length
        
        if current_line:
            lines.append(" ".join(current_line))
        
        return lines