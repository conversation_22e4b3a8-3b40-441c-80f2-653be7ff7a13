"""Temporal alignment service for time series data integration."""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np
from dateutil.relativedelta import relativedelta
import structlog

from src.core.domain.shared.value_objects import TemporalKey


logger = structlog.get_logger()


class TemporalAlignmentService:
    """
    Service for temporal data alignment and aggregation.
    
    Key features:
    - Monthly aggregation anchored on the 15th
    - Flow vs stock data handling
    - Missing period detection
    - Interpolation strategies
    - Temporal lag calculations
    """
    
    def __init__(self, anchor_day: int = 15):
        """
        Initialize temporal service.
        
        Args:
            anchor_day: Day of month to anchor aggregations (default: 15th)
        """
        self.anchor_day = anchor_day
    
    def create_monthly_key(self, date: datetime) -> TemporalKey:
        """
        Create a monthly temporal key from a date.
        
        Args:
            date: Input date
            
        Returns:
            TemporalKey for the month containing the date
        """
        # Anchor to the 15th of the month
        anchored_date = date.replace(day=self.anchor_day)
        
        return TemporalKey(
            year=anchored_date.year,
            month=anchored_date.month,
            week=None,  # Monthly aggregation
            date=anchored_date
        )
    
    async def aggregate_to_monthly(
        self,
        data: pd.DataFrame,
        date_column: str,
        value_columns: List[str],
        aggregation_methods: Dict[str, str],
        group_columns: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        Aggregate data to monthly frequency.
        
        Args:
            data: Input DataFrame
            date_column: Name of date column
            value_columns: Columns to aggregate
            aggregation_methods: Dict mapping column names to methods
                                ('sum' for flow, 'mean' for stock, etc.)
            group_columns: Additional columns to group by
            
        Returns:
            Monthly aggregated DataFrame
        """
        # Create a copy to avoid modifying original
        df = data.copy()
        
        # Create monthly key
        df['temporal_key'] = df[date_column].apply(
            lambda x: self.create_monthly_key(pd.to_datetime(x))
        )
        df['year_month'] = df['temporal_key'].apply(lambda k: f"{k.year}-{k.month:02d}")
        
        # Define grouping columns
        if group_columns:
            group_by = group_columns + ['year_month']
        else:
            group_by = ['year_month']
        
        # Aggregate based on specified methods
        agg_dict = {
            col: aggregation_methods.get(col, 'mean')
            for col in value_columns
        }
        
        # Add count for data availability tracking
        agg_dict[date_column] = 'count'
        
        # Perform aggregation
        monthly_df = df.groupby(group_by).agg(agg_dict).reset_index()
        
        # Rename count column
        monthly_df.rename(columns={date_column: 'n_observations'}, inplace=True)
        
        # Add temporal metadata
        monthly_df['year'] = monthly_df['year_month'].apply(lambda x: int(x.split('-')[0]))
        monthly_df['month'] = monthly_df['year_month'].apply(lambda x: int(x.split('-')[1]))
        monthly_df['date'] = monthly_df.apply(
            lambda row: datetime(row['year'], row['month'], self.anchor_day),
            axis=1
        )
        
        logger.info(
            "monthly_aggregation_complete",
            input_rows=len(data),
            output_rows=len(monthly_df),
            value_columns=value_columns,
            group_columns=group_columns
        )
        
        return monthly_df
    
    async def detect_missing_periods(
        self,
        data: pd.DataFrame,
        date_column: str,
        group_columns: Optional[List[str]] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Detect missing time periods in the data.
        
        Args:
            data: Input DataFrame
            date_column: Name of date column
            group_columns: Columns to group by
            start_date: Expected start date
            end_date: Expected end date
            
        Returns:
            DataFrame with missing periods identified
        """
        # Determine date range
        if start_date is None:
            start_date = data[date_column].min()
        if end_date is None:
            end_date = data[date_column].max()
        
        # Create complete date range
        date_range = pd.date_range(
            start=start_date,
            end=end_date,
            freq='MS'  # Month start
        )
        date_range = [d.replace(day=self.anchor_day) for d in date_range]
        
        if group_columns:
            # Check missing periods for each group
            missing_records = []
            
            for group_vals, group_df in data.groupby(group_columns):
                existing_dates = set(pd.to_datetime(group_df[date_column]))
                
                for date in date_range:
                    if date not in existing_dates:
                        # Create missing record
                        record = {col: val for col, val in zip(group_columns, group_vals)}
                        record[date_column] = date
                        record['is_missing'] = True
                        missing_records.append(record)
            
            missing_df = pd.DataFrame(missing_records)
        else:
            # Check overall missing periods
            existing_dates = set(pd.to_datetime(data[date_column]))
            missing_dates = [d for d in date_range if d not in existing_dates]
            
            missing_df = pd.DataFrame({
                date_column: missing_dates,
                'is_missing': True
            })
        
        logger.info(
            "missing_periods_detected",
            total_expected=len(date_range),
            total_missing=len(missing_df),
            date_range=(start_date, end_date)
        )
        
        return missing_df
    
    async def interpolate_missing(
        self,
        data: pd.DataFrame,
        date_column: str,
        value_columns: List[str],
        method: str = 'linear',
        limit: int = 2,
        group_columns: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        Interpolate missing values in time series.
        
        Args:
            data: Input DataFrame
            date_column: Name of date column
            value_columns: Columns to interpolate
            method: Interpolation method ('linear', 'time', 'nearest', 'zero')
            limit: Maximum number of consecutive periods to interpolate
            group_columns: Columns to group by
            
        Returns:
            DataFrame with interpolated values
        """
        df = data.copy()
        
        # Set date as index for interpolation
        df[date_column] = pd.to_datetime(df[date_column])
        
        if group_columns:
            # Interpolate within each group
            interpolated_dfs = []
            
            for group_vals, group_df in df.groupby(group_columns):
                group_df = group_df.set_index(date_column).sort_index()
                
                # Interpolate each value column
                for col in value_columns:
                    if col in group_df.columns:
                        if method == 'time':
                            group_df[col] = group_df[col].interpolate(
                                method='time',
                                limit=limit
                            )
                        else:
                            group_df[col] = group_df[col].interpolate(
                                method=method,
                                limit=limit
                            )
                
                # Mark interpolated values
                for col in value_columns:
                    if col in group_df.columns:
                        group_df[f'{col}_interpolated'] = (
                            group_df[col].notna() & 
                            group_df[col].shift(1).isna()
                        )
                
                group_df = group_df.reset_index()
                interpolated_dfs.append(group_df)
            
            result_df = pd.concat(interpolated_dfs, ignore_index=True)
        else:
            # Interpolate without grouping
            df = df.set_index(date_column).sort_index()
            
            for col in value_columns:
                if col in df.columns:
                    if method == 'time':
                        df[col] = df[col].interpolate(method='time', limit=limit)
                    else:
                        df[col] = df[col].interpolate(method=method, limit=limit)
                    
                    # Mark interpolated values
                    df[f'{col}_interpolated'] = (
                        df[col].notna() & 
                        df[col].shift(1).isna()
                    )
            
            result_df = df.reset_index()
        
        # Count interpolated values
        n_interpolated = sum(
            result_df[f'{col}_interpolated'].sum()
            for col in value_columns
            if f'{col}_interpolated' in result_df.columns
        )
        
        logger.info(
            "interpolation_complete",
            method=method,
            limit=limit,
            n_interpolated=n_interpolated,
            value_columns=value_columns
        )
        
        return result_df
    
    async def create_temporal_features(
        self,
        data: pd.DataFrame,
        date_column: str,
        lag_periods: List[int] = [1, 3, 6, 12],
        window_sizes: List[int] = [3, 6, 12],
        value_columns: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        Create temporal features including lags and rolling windows.
        
        Args:
            data: Input DataFrame
            date_column: Name of date column
            lag_periods: List of lag periods to create
            window_sizes: List of window sizes for rolling statistics
            value_columns: Columns to create features for
            
        Returns:
            DataFrame with temporal features added
        """
        df = data.copy()
        df[date_column] = pd.to_datetime(df[date_column])
        df = df.sort_values(date_column)
        
        if value_columns is None:
            # Use all numeric columns
            value_columns = df.select_dtypes(include=[np.number]).columns.tolist()
        
        # Create lag features
        for col in value_columns:
            for lag in lag_periods:
                df[f'{col}_lag{lag}'] = df[col].shift(lag)
        
        # Create rolling window features
        for col in value_columns:
            for window in window_sizes:
                # Rolling mean
                df[f'{col}_roll_mean_{window}'] = (
                    df[col].rolling(window=window, min_periods=1).mean()
                )
                # Rolling std
                df[f'{col}_roll_std_{window}'] = (
                    df[col].rolling(window=window, min_periods=2).std()
                )
                # Rolling min/max
                df[f'{col}_roll_min_{window}'] = (
                    df[col].rolling(window=window, min_periods=1).min()
                )
                df[f'{col}_roll_max_{window}'] = (
                    df[col].rolling(window=window, min_periods=1).max()
                )
        
        # Create temporal indicators
        df['year'] = df[date_column].dt.year
        df['month'] = df[date_column].dt.month
        df['quarter'] = df[date_column].dt.quarter
        df['is_ramadan'] = df[date_column].apply(self._is_ramadan_period)
        df['is_harvest'] = df['month'].isin([4, 5, 10, 11])  # Yemen harvest seasons
        
        logger.info(
            "temporal_features_created",
            n_lag_features=len(lag_periods) * len(value_columns),
            n_rolling_features=len(window_sizes) * len(value_columns) * 4,
            total_features=df.shape[1] - data.shape[1]
        )
        
        return df
    
    def _is_ramadan_period(self, date: datetime) -> bool:
        """
        Check if date falls within Ramadan period.
        
        Note: This is a simplified implementation. In production,
        use a proper Islamic calendar library.
        """
        # Approximate Ramadan dates (would need proper calculation)
        ramadan_periods = [
            (datetime(2019, 5, 6), datetime(2019, 6, 4)),
            (datetime(2020, 4, 24), datetime(2020, 5, 23)),
            (datetime(2021, 4, 13), datetime(2021, 5, 12)),
            (datetime(2022, 4, 2), datetime(2022, 5, 1)),
            (datetime(2023, 3, 23), datetime(2023, 4, 21)),
            (datetime(2024, 3, 11), datetime(2024, 4, 9)),
            (datetime(2025, 3, 1), datetime(2025, 3, 30)),
        ]
        
        for start, end in ramadan_periods:
            if start <= date <= end:
                return True
        return False
    
    async def align_multiple_series(
        self,
        series_dict: Dict[str, pd.DataFrame],
        date_columns: Dict[str, str],
        join_type: str = 'outer'
    ) -> pd.DataFrame:
        """
        Align multiple time series with different frequencies.
        
        Args:
            series_dict: Dict mapping series name to DataFrame
            date_columns: Dict mapping series name to date column name
            join_type: How to join ('outer', 'inner', 'left', 'right')
            
        Returns:
            Aligned DataFrame with all series
        """
        aligned_series = []
        
        for name, df in series_dict.items():
            date_col = date_columns.get(name, 'date')
            
            # Standardize to monthly
            monthly_df = await self.aggregate_to_monthly(
                df,
                date_column=date_col,
                value_columns=[col for col in df.columns if col != date_col],
                aggregation_methods={col: 'mean' for col in df.columns if col != date_col}
            )
            
            # Add series prefix to columns
            monthly_df = monthly_df.add_prefix(f'{name}_')
            monthly_df.rename(columns={f'{name}_date': 'date'}, inplace=True)
            
            aligned_series.append(monthly_df)
        
        # Merge all series
        result = aligned_series[0]
        for df in aligned_series[1:]:
            result = pd.merge(
                result,
                df,
                on='date',
                how=join_type
            )
        
        logger.info(
            "series_alignment_complete",
            n_series=len(series_dict),
            join_type=join_type,
            result_shape=result.shape
        )
        
        return result