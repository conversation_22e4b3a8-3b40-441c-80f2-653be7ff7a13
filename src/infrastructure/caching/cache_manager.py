"""Cache management for data pipeline."""
import asyncio
import json
import pickle
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Optional, Union
import aiofiles
import pandas as pd
import structlog
from dataclasses import dataclass, asdict


logger = structlog.get_logger()


@dataclass
class CacheEntry:
    """Metadata for a cache entry."""
    key: str
    created_at: datetime
    expires_at: datetime
    size_bytes: int
    version: str = "1.0"
    
    def is_expired(self) -> bool:
        """Check if cache entry has expired."""
        return datetime.now() > self.expires_at
    
    def to_dict(self) -> dict:
        """Convert to dictionary for serialization."""
        return {
            **asdict(self),
            'created_at': self.created_at.isoformat(),
            'expires_at': self.expires_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'CacheEntry':
        """Create from dictionary."""
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['expires_at'] = datetime.fromisoformat(data['expires_at'])
        return cls(**data)


class CacheManager:
    """
    Manages caching for data pipeline.
    
    Features:
    - TTL-based expiration
    - Async file I/O
    - Automatic cleanup of expired entries
    - Support for different serialization formats
    """
    
    def __init__(self, cache_dir: Path, max_size_gb: float = 50.0):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_size_bytes = int(max_size_gb * 1024 * 1024 * 1024)
        self.metadata_file = self.cache_dir / "cache_metadata.json"
        self._metadata_lock = asyncio.Lock()
        self._metadata = {}
        
    async def initialize(self) -> None:
        """Initialize cache manager."""
        await self._load_metadata()
        await self._cleanup_expired()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get item from cache."""
        async with self._metadata_lock:
            if key not in self._metadata:
                logger.debug("Cache miss", key=key)
                return None
            
            entry = self._metadata[key]
            if entry.is_expired():
                logger.debug("Cache expired", key=key)
                await self._remove_entry(key)
                return None
        
        # Load data
        file_path = self._get_file_path(key)
        if not file_path.exists():
            logger.warning("Cache file missing", key=key, path=str(file_path))
            async with self._metadata_lock:
                del self._metadata[key]
            return None
        
        try:
            data = await self._load_data(file_path)
            logger.debug("Cache hit", key=key)
            return data
        except Exception as e:
            logger.error("Failed to load cache", key=key, error=str(e))
            await self._remove_entry(key)
            return None
    
    async def set(self, key: str, value: Any, ttl: int = 3600) -> None:
        """
        Set item in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
        """
        file_path = self._get_file_path(key)
        
        # Save data
        await self._save_data(file_path, value)
        
        # Update metadata
        size_bytes = file_path.stat().st_size
        entry = CacheEntry(
            key=key,
            created_at=datetime.now(),
            expires_at=datetime.now() + timedelta(seconds=ttl),
            size_bytes=size_bytes
        )
        
        async with self._metadata_lock:
            self._metadata[key] = entry
            await self._save_metadata()
        
        logger.debug("Cache set", key=key, ttl=ttl, size_bytes=size_bytes)
        
        # Check size limit
        await self._enforce_size_limit()
    
    async def delete(self, key: str) -> bool:
        """Delete item from cache."""
        return await self._remove_entry(key)
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        async with self._metadata_lock:
            for key in list(self._metadata.keys()):
                await self._remove_entry(key)
    
    async def get_stats(self) -> dict:
        """Get cache statistics."""
        async with self._metadata_lock:
            total_size = sum(e.size_bytes for e in self._metadata.values())
            expired = sum(1 for e in self._metadata.values() if e.is_expired())
            
            return {
                'total_entries': len(self._metadata),
                'expired_entries': expired,
                'total_size_bytes': total_size,
                'total_size_gb': total_size / (1024**3),
                'max_size_gb': self.max_size_bytes / (1024**3),
                'utilization': total_size / self.max_size_bytes
            }
    
    def _get_file_path(self, key: str) -> Path:
        """Get file path for cache key."""
        # Use first 2 chars of key for directory sharding
        shard = key[:2] if len(key) >= 2 else "00"
        shard_dir = self.cache_dir / shard
        shard_dir.mkdir(exist_ok=True)
        
        # Replace problematic characters in filename
        safe_key = key.replace('/', '_').replace('\\', '_')
        return shard_dir / f"{safe_key}.cache"
    
    async def _load_data(self, file_path: Path) -> Any:
        """Load data from cache file."""
        # Check extension to determine format
        if file_path.suffix == '.parquet':
            return pd.read_parquet(file_path)
        elif file_path.suffix == '.json':
            async with aiofiles.open(file_path, 'r') as f:
                content = await f.read()
                return json.loads(content)
        else:
            # Default to pickle
            async with aiofiles.open(file_path, 'rb') as f:
                content = await f.read()
                return pickle.loads(content)
    
    async def _save_data(self, file_path: Path, data: Any) -> None:
        """Save data to cache file."""
        # Determine format based on data type
        if isinstance(data, pd.DataFrame):
            # Use parquet for DataFrames
            data.to_parquet(file_path.with_suffix('.parquet'))
        elif isinstance(data, (dict, list)):
            # Use JSON for simple structures
            async with aiofiles.open(file_path.with_suffix('.json'), 'w') as f:
                await f.write(json.dumps(data, default=str))
        else:
            # Use pickle for everything else
            async with aiofiles.open(file_path.with_suffix('.cache'), 'wb') as f:
                await f.write(pickle.dumps(data))
    
    async def _load_metadata(self) -> None:
        """Load metadata from disk."""
        if not self.metadata_file.exists():
            self._metadata = {}
            return
        
        try:
            async with aiofiles.open(self.metadata_file, 'r') as f:
                content = await f.read()
                data = json.loads(content)
                self._metadata = {
                    k: CacheEntry.from_dict(v) for k, v in data.items()
                }
        except Exception as e:
            logger.error("Failed to load cache metadata", error=str(e))
            self._metadata = {}
    
    async def _save_metadata(self) -> None:
        """Save metadata to disk."""
        data = {k: v.to_dict() for k, v in self._metadata.items()}
        
        async with aiofiles.open(self.metadata_file, 'w') as f:
            await f.write(json.dumps(data, indent=2))
    
    async def _remove_entry(self, key: str) -> bool:
        """Remove cache entry."""
        async with self._metadata_lock:
            if key not in self._metadata:
                return False
            
            # Remove file
            file_path = self._get_file_path(key)
            try:
                if file_path.exists():
                    file_path.unlink()
                # Also try with different extensions
                for ext in ['.parquet', '.json', '.cache']:
                    alt_path = file_path.with_suffix(ext)
                    if alt_path.exists():
                        alt_path.unlink()
            except Exception as e:
                logger.error("Failed to remove cache file", key=key, error=str(e))
            
            # Remove metadata
            del self._metadata[key]
            await self._save_metadata()
            
            return True
    
    async def _cleanup_expired(self) -> None:
        """Remove expired cache entries."""
        async with self._metadata_lock:
            expired_keys = [
                k for k, v in self._metadata.items() if v.is_expired()
            ]
            
            for key in expired_keys:
                await self._remove_entry(key)
            
            if expired_keys:
                logger.info("Cleaned expired cache entries", count=len(expired_keys))
    
    async def _enforce_size_limit(self) -> None:
        """Remove oldest entries if size limit exceeded."""
        async with self._metadata_lock:
            total_size = sum(e.size_bytes for e in self._metadata.values())
            
            if total_size <= self.max_size_bytes:
                return
            
            # Sort by creation time (oldest first)
            sorted_entries = sorted(
                self._metadata.items(),
                key=lambda x: x[1].created_at
            )
            
            # Remove oldest until under limit
            removed = 0
            for key, entry in sorted_entries:
                if total_size <= self.max_size_bytes:
                    break
                
                await self._remove_entry(key)
                total_size -= entry.size_bytes
                removed += 1
            
            logger.info(
                "Enforced cache size limit",
                removed=removed,
                new_size_gb=total_size / (1024**3)
            )