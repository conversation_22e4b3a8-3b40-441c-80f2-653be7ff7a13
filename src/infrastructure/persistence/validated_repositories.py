"""
Validated repository implementations that enforce methodology requirements.

These repositories wrap the underlying repositories and ensure that all data
includes proper currency conversion before being returned to the application layer.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import pandas as pd
from decimal import Decimal
import logging

from ...core.domain.market.entities import Market, PriceObservation
from ...core.domain.market.repositories import MarketRepository, PriceRepository
from ...core.domain.market.value_objects import MarketId, Commodity, Price, Currency
from ...core.validation.methodology_validator import (
    MethodologyValidator, 
    MethodologyViolation,
    AnalysisType
)
from ..external_services.exchange_rate_collector_v2 import ExchangeRateCollectorV2
from ...core.domain.shared.exceptions import ValidationException

logger = logging.getLogger(__name__)


class ValidatedPriceRepository(PriceRepository):
    """
    Repository wrapper that ensures all price observations include USD conversion.
    
    This is a critical component that prevents any analysis from proceeding
    without proper currency conversion. It enriches price observations with:
    - USD prices calculated from YER prices and exchange rates
    - Exchange rate used for conversion
    - Currency zone classification
    """
    
    def __init__(
        self, 
        wrapped_repository: PriceRepository,
        market_repository: MarketRepository,
        exchange_rate_collector: Optional[ExchangeRateCollectorV2] = None
    ):
        """
        Initialize validated repository.
        
        Args:
            wrapped_repository: The underlying repository to wrap
            market_repository: Repository to get market information
            exchange_rate_collector: Service to get exchange rates
        """
        self._wrapped = wrapped_repository
        self._market_repo = market_repository
        if exchange_rate_collector is None:
            # Create a minimal config for ExchangeRateCollectorV2
            minimal_config = {
                'cache_ttl': 3600,
                'timeout': 30,
                'wfp_data_directory': '/tmp/wfp_data',
                'central_bank_scrapers': []
            }
            self._exchange_rate_collector = ExchangeRateCollectorV2(minimal_config)
        else:
            self._exchange_rate_collector = exchange_rate_collector
        self._validator = MethodologyValidator()
        
        # Zone classification mapping (from tier runners)
        self._houthi_governorates = {
            'Sana\'a', 'Amran', 'Dhamar', 'Hajjah', 'Al Mahwit', 
            'Raymah', 'Sa\'ada', 'Sana\'a City', 'Ibb'
        }
        self._government_governorates = {
            'Aden', 'Lahj', 'Abyan', 'Al Dhale\'e', 'Shabwah',
            'Hadramaut', 'Al Maharah', 'Socotra'
        }
        
    async def find_by_market_and_commodity(
        self,
        market_id: MarketId,
        commodity: Commodity,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[PriceObservation]:
        """Find price observations with enforced USD conversion."""
        # Get raw observations
        observations = await self._wrapped.find_by_market_and_commodity(
            market_id, commodity, start_date, end_date
        )
        
        # Enrich with USD prices
        enriched = await self._enrich_observations(observations)
        
        # Validate before returning
        self._validate_observations(enriched)
        
        return enriched
        
    async def find_by_date_range(
        self,
        start_date: datetime,
        end_date: datetime,
        market_ids: Optional[List[str]] = None,
        commodity_codes: Optional[List[str]] = None
    ) -> List[PriceObservation]:
        """Find price observations by date range with USD conversion."""
        # Get raw observations
        observations = await self._wrapped.find_by_date_range(
            start_date, end_date, market_ids, commodity_codes
        )
        
        # Enrich with USD prices
        enriched = await self._enrich_observations(observations)
        
        # Validate before returning
        self._validate_observations(enriched)
        
        return enriched
        
    async def _enrich_observations(
        self, 
        observations: List[PriceObservation]
    ) -> List[PriceObservation]:
        """
        Enrich observations with USD prices and exchange rate data.
        
        This is where the critical currency conversion happens.
        """
        if not observations:
            return observations
            
        # Group by market to get zone classifications
        market_zones = {}
        for obs in observations:
            if obs.market_id.value not in market_zones:
                market = await self._market_repo.find_by_id(obs.market_id)
                if market:
                    zone = self._classify_currency_zone(market.governorate)
                    market_zones[obs.market_id.value] = zone
                else:
                    market_zones[obs.market_id.value] = 'UNKNOWN'
        
        # Convert to DataFrame for easier processing
        df = self._observations_to_dataframe(observations, market_zones)
        
        # Add exchange rates
        df = await self._add_exchange_rates(df)
        
        # Calculate USD prices
        df['price_usd'] = df.apply(self._calculate_usd_price, axis=1)
        
        # Debug output
        logger.info(f"DataFrame after calculation:\n{df[['price', 'currency', 'exchange_rate_used', 'price_usd']]}")
        
        # Validate the enriched data
        is_valid, report = self._validator.validate_analysis_inputs(
            observations=df,
            analysis_type=AnalysisType.PANEL_ANALYSIS
        )
        
        if not is_valid:
            raise MethodologyViolation(
                f"Enriched data failed validation: {report.critical_failures}",
                report=report
            )
        
        # Convert back to observations (keeping original objects but enriched)
        # Note: In a real implementation, we'd modify the PriceObservation class
        # to include these fields. For now, we return the original observations
        # but ensure the calling code has access to the enriched DataFrame.
        
        # Store enriched data for retrieval
        self._last_enriched_df = df
        
        return observations
        
    def get_last_enriched_dataframe(self) -> Optional[pd.DataFrame]:
        """Get the last enriched DataFrame with USD prices."""
        return getattr(self, '_last_enriched_df', None)
        
    def _observations_to_dataframe(
        self, 
        observations: List[PriceObservation],
        market_zones: Dict[str, str]
    ) -> pd.DataFrame:
        """Convert observations to DataFrame with zone information."""
        data = []
        for obs in observations:
            row = {
                'market_id': obs.market_id.value,
                'date': obs.observed_date,
                'commodity': obs.commodity.code,
                'price_yer': float(obs.price.amount) if obs.price.currency == Currency.YER else None,
                'price': float(obs.price.amount),
                'currency': obs.price.currency.value,
                'currency_zone': market_zones.get(obs.market_id.value, 'UNKNOWN'),
                'source': obs.source
            }
            data.append(row)
            
        return pd.DataFrame(data)
        
    async def _add_exchange_rates(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add exchange rates to the DataFrame."""
        # Get unique date-zone combinations
        date_zones = df[['date', 'currency_zone']].drop_duplicates()
        
        # Fetch exchange rates
        exchange_rates = {}
        for _, row in date_zones.iterrows():
            date = row['date']
            zone = row['currency_zone']
            
            # Get appropriate exchange rate based on zone
            source = 'CBY_Sanaa' if zone == 'HOUTHI' else 'CBY_Aden'
            
            # Collect all rates for the date
            daily_rates = await self._exchange_rate_collector.collect_daily_rates(date)
            
            # Find rate for this zone
            rate_found = False
            for zone_rate in daily_rates:
                # Handle both string and enum comparisons
                zone_name = zone_rate.zone.value if hasattr(zone_rate.zone, 'value') else str(zone_rate.zone)
                # Case-insensitive comparison
                if zone_name.upper() == zone.upper():
                    exchange_rates[(date, zone)] = float(zone_rate.rate)
                    rate_found = True
                    break
            
            if not rate_found:
                # Mark as missing - will fail validation
                exchange_rates[(date, zone)] = None
                
        # Apply exchange rates to DataFrame
        df['exchange_rate_used'] = df.apply(
            lambda row: exchange_rates.get((row['date'], row['currency_zone']), 1000.0),
            axis=1
        )
        
        return df
        
    def _calculate_usd_price(self, row: pd.Series) -> float:
        """Calculate USD price from YER price and exchange rate."""
        # Currency in dataframe is already string from _observations_to_dataframe
        if row['currency'] == 'USD':
            return row['price']
        elif row['currency'] == 'YER' and row.get('exchange_rate_used') is not None:
            return row['price'] / row['exchange_rate_used']
        else:
            # This should trigger validation failure
            return None
            
    def _classify_currency_zone(self, governorate: str) -> str:
        """Classify governorate into currency zone."""
        if governorate in self._houthi_governorates:
            return 'HOUTHI'
        elif governorate in self._government_governorates:
            return 'GOVERNMENT'
        else:
            return 'CONTESTED'
            
    def _validate_observations(self, observations: List[PriceObservation]) -> None:
        """Validate that observations meet methodology requirements."""
        if not observations:
            return
            
        # Get enriched DataFrame if available
        df = getattr(self, '_last_enriched_df', None)
        if df is None:
            raise ValidationException(
                "Cannot validate observations without enriched data"
            )
            
        # Check critical fields
        missing_usd = df['price_usd'].isna().sum()
        if missing_usd > 0:
            raise MethodologyViolation(
                f"Cannot return observations: {missing_usd} observations missing USD prices"
            )
            
        # Only check exchange rates for YER prices
        yer_prices = df[df['currency'] == 'YER']
        if len(yer_prices) > 0:
            missing_rates = yer_prices['exchange_rate_used'].isna().sum()
            if missing_rates > 0:
                raise MethodologyViolation(
                    f"Cannot return observations: {missing_rates} YER observations missing exchange rates"
                )
            
        missing_zones = (df['currency_zone'] == 'UNKNOWN').sum()
        if missing_zones > 0:
            raise MethodologyViolation(
                f"Cannot return observations: {missing_zones} observations missing zone classification"
            )
    
    # Delegate other methods to wrapped repository
    async def find_commodity_by_code(self, code: str) -> Optional[Commodity]:
        """Find commodity by code."""
        return await self._wrapped.find_commodity_by_code(code)
        
    async def get_distinct_commodities(self) -> List[Commodity]:
        """Get all distinct commodities."""
        return await self._wrapped.get_distinct_commodities()
        
    async def get_distinct_markets(self) -> List[MarketId]:
        """Get all distinct market IDs."""
        return await self._wrapped.get_distinct_markets()
        
    async def save(self, observation: PriceObservation) -> None:
        """
        Save price observation.
        
        Note: Saving should include validation that USD conversion is possible.
        """
        # Validate that we can convert this observation
        if observation.price.currency == Currency.YER:
            # Check that we have exchange rate data for this date/market
            market = await self._market_repo.find_by_id(observation.market_id)
            if not market:
                raise ValidationException(
                    f"Cannot save observation: Market {observation.market_id} not found"
                )
                
            zone = self._classify_currency_zone(market.governorate)
            source = 'CBY_Sanaa' if zone == 'HOUTHI' else 'CBY_Aden'
            
            rate_data = await self._exchange_rate_collector.get_exchange_rate(
                from_currency='YER',
                to_currency='USD',
                date=observation.observed_date.strftime('%Y-%m-%d'),
                source=source
            )
            
            if not rate_data or 'rate' not in rate_data:
                raise ValidationException(
                    f"Cannot save observation: No exchange rate available for {observation.observed_date}"
                )
        
        await self._wrapped.save(observation)
        
    async def save_batch(self, observations: List[PriceObservation]) -> None:
        """Save multiple price observations with validation."""
        # Validate all observations first
        for obs in observations:
            if obs.price.currency == Currency.YER:
                market = await self._market_repo.find_by_id(obs.market_id)
                if not market:
                    raise ValidationException(
                        f"Cannot save observation: Market {obs.market_id} not found"
                    )
        
        await self._wrapped.save_batch(observations)
        
    async def delete_by_market_and_date(
        self,
        market_id: MarketId,
        date: datetime
    ) -> None:
        """Delete observations by market and date."""
        await self._wrapped.delete_by_market_and_date(market_id, date)
    
    async def delete_by_date_range(
        self,
        market_id: MarketId,
        start_date: datetime,
        end_date: datetime
    ) -> int:
        """Delete observations within date range."""
        return await self._wrapped.delete_by_date_range(market_id, start_date, end_date)
    
    async def find_by_markets(
        self,
        market_ids: List[MarketId],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[PriceObservation]:
        """Find price observations for multiple markets."""
        observations = await self._wrapped.find_by_markets(market_ids, start_date, end_date)
        enriched = await self._enrich_observations(observations)
        self._validate_observations(enriched)
        return enriched
    
    async def find_by_commodities(
        self,
        commodities: List[Commodity],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[PriceObservation]:
        """Find price observations for multiple commodities."""
        observations = await self._wrapped.find_by_commodities(commodities, start_date, end_date)
        enriched = await self._enrich_observations(observations)
        self._validate_observations(enriched)
        return enriched
    
    async def find_by_markets_and_commodity(
        self,
        market_ids: List[MarketId],
        commodity: Commodity,
        start_date: datetime,
        end_date: datetime
    ) -> List[PriceObservation]:
        """Find price observations for multiple markets and a commodity."""
        observations = await self._wrapped.find_by_markets_and_commodity(
            market_ids, commodity, start_date, end_date
        )
        enriched = await self._enrich_observations(observations)
        self._validate_observations(enriched)
        return enriched


class ValidatedMarketRepository(MarketRepository):
    """
    Repository wrapper that ensures markets have proper zone classification.
    """
    
    def __init__(self, wrapped_repository: MarketRepository):
        """Initialize validated repository."""
        self._wrapped = wrapped_repository
        
    async def find_by_id(self, market_id: MarketId) -> Optional[Market]:
        """Find market by ID with validation."""
        market = await self._wrapped.find_by_id(market_id)
        if market:
            self._validate_market(market)
        return market
        
    async def find_by_ids(self, market_ids: List[str]) -> List[Market]:
        """Find markets by list of IDs with validation."""
        markets = await self._wrapped.find_by_ids(market_ids)
        for market in markets:
            self._validate_market(market)
        return markets
        
    async def find_all(self) -> List[Market]:
        """Find all markets with validation."""
        markets = await self._wrapped.find_all()
        for market in markets:
            self._validate_market(market)
        return markets
        
    async def find_by_governorate(self, governorate: str) -> List[Market]:
        """Find markets by governorate with validation."""
        markets = await self._wrapped.find_by_governorate(governorate)
        for market in markets:
            self._validate_market(market)
        return markets
        
    async def find_by_market_type(self, market_type: str) -> List[Market]:
        """Find markets by type with validation."""
        markets = await self._wrapped.find_by_market_type(market_type)
        for market in markets:
            self._validate_market(market)
        return markets
        
    def _validate_market(self, market: Market) -> None:
        """Validate that market has required fields for analysis."""
        if not market.governorate:
            raise ValidationException(
                f"Market {market.market_id} missing governorate for zone classification"
            )
        if not market.coordinates:
            raise ValidationException(
                f"Market {market.market_id} missing coordinates"
            )
    
    async def find_active_at(self, date: datetime) -> List[Market]:
        """Find all markets active at a given date."""
        markets = await self._wrapped.find_active_at(date)
        for market in markets:
            self._validate_market(market)
        return markets
    
    # Delegate save/delete to wrapped repository
    async def save(self, market: Market) -> None:
        """Save market with validation."""
        self._validate_market(market)
        await self._wrapped.save(market)
        
    async def delete(self, market_id: MarketId) -> None:
        """Delete market."""
        await self._wrapped.delete(market_id)