"""PostgreSQL implementation of commodity repository."""

from typing import List, Optional

import asyncpg

from .....core.domain.market.repositories import CommodityRepository
from .....core.domain.market.value_objects import Commodity


class PostgresCommodityRepository(CommodityRepository):
    """PostgreSQL implementation of commodity repository."""
    
    def __init__(self, connection: asyncpg.Connection):
        """Initialize with database connection."""
        self.connection = connection
    
    async def find_by_code(self, code: str) -> Optional[Commodity]:
        """Find commodity by code."""
        row = await self.connection.fetchrow(
            "SELECT code, name, category, standard_unit FROM commodities WHERE code = $1",
            code
        )
        
        if row:
            return Commodity(
                code=row['code'],
                name=row['name'],
                category=row['category'],
                standard_unit=row['standard_unit']
            )
        return None
    
    async def find_all(self) -> List[Commodity]:
        """Find all commodities."""
        rows = await self.connection.fetch(
            "SELECT code, name, category, standard_unit FROM commodities ORDER BY name"
        )
        
        return [Commodity(
            code=row['code'],
            name=row['name'],
            category=row['category'],
            standard_unit=row['standard_unit']
        ) for row in rows]
    
    async def find_by_category(self, category: str) -> List[Commodity]:
        """Find commodities by category."""
        rows = await self.connection.fetch(
            "SELECT code, name, category, standard_unit FROM commodities WHERE category = $1 ORDER BY name",
            category
        )
        
        return [Commodity(
            code=row['code'],
            name=row['name'],
            category=row['category'],
            standard_unit=row['standard_unit']
        ) for row in rows]
    
    async def save(self, commodity: Commodity) -> None:
        """Save commodity."""
        await self.connection.execute(
            """
            INSERT INTO commodities (code, name, category, standard_unit)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (code) DO UPDATE SET
                name = EXCLUDED.name,
                category = EXCLUDED.category,
                standard_unit = EXCLUDED.standard_unit
            """,
            commodity.code,
            commodity.name,
            commodity.category,
            commodity.standard_unit
        )
    
    async def save_batch(self, commodities: List[Commodity]) -> None:
        """Save multiple commodities."""
        # Prepare data for batch insert
        data = [
            (c.code, c.name, c.category, c.standard_unit)
            for c in commodities
        ]
        
        # Use COPY for efficient batch insert
        await self.connection.copy_records_to_table(
            'commodities',
            records=data,
            columns=['code', 'name', 'category', 'standard_unit']
        )
    
    async def delete(self, code: str) -> None:
        """Delete commodity by code."""
        await self.connection.execute(
            "DELETE FROM commodities WHERE code = $1",
            code
        )
    
    async def get_distinct_categories(self) -> List[str]:
        """Get all distinct commodity categories."""
        rows = await self.connection.fetch(
            "SELECT DISTINCT category FROM commodities ORDER BY category"
        )
        
        return [row['category'] for row in rows]