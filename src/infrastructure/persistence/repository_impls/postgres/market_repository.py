"""PostgreSQL implementation of market repository."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

import asyncpg

from .....core.domain.market.entities import Market
from .....core.domain.market.repositories import MarketRepository
from .....core.domain.market.value_objects import MarketId, Coordinates, MarketType


class PostgresMarketRepository(MarketRepository):
    """PostgreSQL implementation of market repository."""
    
    def __init__(self, connection: asyncpg.Connection):
        """Initialize with database connection."""
        self.connection = connection
    
    async def find_by_id(self, market_id: MarketId) -> Optional[Market]:
        """Find market by ID."""
        row = await self.connection.fetchrow(
            """
            SELECT id, market_id, name, latitude, longitude, market_type,
                   governorate, district, active_since, active_until
            FROM markets
            WHERE market_id = $1
            """,
            market_id.value
        )
        
        if row:
            return self._to_domain(row)
        return None
    
    async def find_by_ids(self, market_ids: List[MarketId]) -> List[Market]:
        """Find multiple markets by IDs."""
        rows = await self.connection.fetch(
            """
            SELECT id, market_id, name, latitude, longitude, market_type,
                   governorate, district, active_since, active_until
            FROM markets
            WHERE market_id = ANY($1::text[])
            """,
            [mid.value for mid in market_ids]
        )
        
        return [self._to_domain(row) for row in rows]
    
    async def find_by_governorate(self, governorate: str) -> List[Market]:
        """Find all markets in a governorate."""
        rows = await self.connection.fetch(
            """
            SELECT id, market_id, name, latitude, longitude, market_type,
                   governorate, district, active_since, active_until
            FROM markets
            WHERE governorate = $1
            ORDER BY district, name
            """,
            governorate
        )
        
        return [self._to_domain(row) for row in rows]
    
    async def find_active_at(self, date: datetime) -> List[Market]:
        """Find all markets active at a given date."""
        rows = await self.connection.fetch(
            """
            SELECT id, market_id, name, latitude, longitude, market_type,
                   governorate, district, active_since, active_until
            FROM markets
            WHERE active_since <= $1
              AND (active_until IS NULL OR active_until > $1)
            ORDER BY governorate, district, name
            """,
            date
        )
        
        return [self._to_domain(row) for row in rows]
    
    async def save(self, market: Market) -> None:
        """Save market aggregate."""
        # Extract events before saving
        events = market.events
        
        await self.connection.execute(
            """
            INSERT INTO markets (
                id, market_id, name, latitude, longitude, market_type,
                governorate, district, active_since, active_until, version
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                active_until = EXCLUDED.active_until,
                version = EXCLUDED.version
            WHERE markets.version = $11 - 1  -- Optimistic locking
            """,
            market.id,
            market.market_id.value,
            market.name,
            market.coordinates.latitude,
            market.coordinates.longitude,
            market.market_type.value,
            market.governorate,
            market.district,
            market.active_since,
            market.active_until,
            market.version
        )
        
        # Save events
        if events:
            await self._save_events(market.id, events)
        
        # Clear events after saving
        market.clear_events()
        market.increment_version()
    
    async def delete(self, market_id: MarketId) -> None:
        """Delete market by ID."""
        await self.connection.execute(
            "DELETE FROM markets WHERE market_id = $1",
            market_id.value
        )
    
    async def find_all(self) -> List[Market]:
        """Find all markets."""
        rows = await self.connection.fetch(
            """
            SELECT id, market_id, name, latitude, longitude, market_type,
                   governorate, district, active_since, active_until
            FROM markets
            ORDER BY governorate, district, name
            """
        )
        
        return [self._to_domain(row) for row in rows]
    
    async def find_within_radius(
        self, 
        latitude: float, 
        longitude: float, 
        radius_km: float
    ) -> List[Market]:
        """Find markets within radius of a location."""
        rows = await self.connection.fetch(
            """
            SELECT id, market_id, name, latitude, longitude, market_type,
                   governorate, district, active_since, active_until,
                   ST_Distance(
                       ST_GeogFromWKB(ST_MakePoint(longitude, latitude)::geometry),
                       ST_GeogFromWKB(ST_MakePoint($2, $1)::geometry)
                   ) / 1000 as distance_km
            FROM markets
            WHERE ST_DWithin(
                ST_GeogFromWKB(ST_MakePoint(longitude, latitude)::geometry),
                ST_GeogFromWKB(ST_MakePoint($2, $1)::geometry),
                $3 * 1000
            )
            ORDER BY distance_km
            """,
            latitude, longitude, radius_km
        )
        
        return [self._to_domain(row) for row in rows]
    
    async def find_by_control_zone(self, control_zone: str) -> List[Market]:
        """Find markets by control zone (based on governorate mappings)."""
        # Define control zone mappings
        houthi_governorates = [
            "Sana'a", "Sa'dah", "Hajjah", "Al Mahwit", "Dhamar",
            "Raymah", "Ibb", "Amran", "Al Hodeidah"
        ]
        
        government_governorates = [
            "Aden", "Lahj", "Abyan", "Shabwah", "Hadramawt",
            "Al Maharah", "Socotra", "Ad Dale'", "Marib", "Al Jawf"
        ]
        
        contested_governorates = ["Ta'iz"]
        
        if control_zone.lower() == 'houthi':
            governorates = houthi_governorates
        elif control_zone.lower() == 'government':
            governorates = government_governorates
        elif control_zone.lower() == 'contested':
            governorates = contested_governorates
        else:
            raise ValueError(f"Unknown control zone: {control_zone}")
        
        rows = await self.connection.fetch(
            """
            SELECT id, market_id, name, latitude, longitude, market_type,
                   governorate, district, active_since, active_until
            FROM markets
            WHERE governorate = ANY($1::text[])
            ORDER BY governorate, district, name
            """,
            governorates
        )
        
        return [self._to_domain(row) for row in rows]
    
    def _to_domain(self, row: asyncpg.Record) -> Market:
        """Convert database row to domain entity."""
        market = Market(
            market_id=MarketId(row['market_id']),
            name=row['name'],
            coordinates=Coordinates(
                latitude=row['latitude'],
                longitude=row['longitude']
            ),
            market_type=MarketType(row['market_type']),
            governorate=row['governorate'],
            district=row['district'],
            active_since=row['active_since'],
            active_until=row['active_until']
        )
        
        # Set internal fields
        market.id = row['id']
        market.version = row.get('version', 0)
        
        return market
    
    async def _save_events(self, aggregate_id: UUID, events: List) -> None:
        """Save domain events."""
        for event in events:
            await self.connection.execute(
                """
                INSERT INTO domain_events (
                    event_id, aggregate_id, event_name, event_data, occurred_at
                ) VALUES ($1, $2, $3, $4, $5)
                """,
                event.event_id,
                aggregate_id,
                event.event_name,
                event.to_dict(),
                event.occurred_at
            )