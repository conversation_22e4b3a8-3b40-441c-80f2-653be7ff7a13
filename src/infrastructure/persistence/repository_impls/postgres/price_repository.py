"""PostgreSQL implementation of price repository."""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional, Dict
from uuid import UUID

import asyncpg

from .....core.domain.market.entities import PriceObservation
from .....core.domain.market.repositories import PriceRepository
from .....core.domain.market.value_objects import MarketId, Commodity, Price


class PostgresPriceRepository(PriceRepository):
    """PostgreSQL implementation of price repository."""
    
    def __init__(self, connection: asyncpg.Connection):
        """Initialize with database connection."""
        self.connection = connection
    
    async def find_by_market_and_commodity(
        self,
        market_id: MarketId,
        commodity: Commodity,
        start_date: datetime,
        end_date: datetime
    ) -> List[PriceObservation]:
        """Find price observations for a market and commodity within date range."""
        rows = await self.connection.fetch(
            """
            SELECT po.id, po.market_id, po.commodity_code, po.price_amount,
                   po.price_currency, po.price_unit, po.observed_date,
                   po.source, po.quality, po.observations_count,
                   c.name as commodity_name, c.category, c.standard_unit
            FROM price_observations po
            JOIN commodities c ON po.commodity_code = c.code
            WHERE po.market_id = $1
              AND po.commodity_code = $2
              AND po.observed_date >= $3
              AND po.observed_date <= $4
            ORDER BY po.observed_date
            """,
            market_id.value,
            commodity.code,
            start_date,
            end_date
        )
        
        return [self._to_domain(row) for row in rows]
    
    async def find_by_markets_and_commodity(
        self,
        market_ids: List[MarketId],
        commodity: Commodity,
        start_date: datetime,
        end_date: datetime
    ) -> List[PriceObservation]:
        """Find price observations for multiple markets and a commodity."""
        rows = await self.connection.fetch(
            """
            SELECT po.id, po.market_id, po.commodity_code, po.price_amount,
                   po.price_currency, po.price_unit, po.observed_date,
                   po.source, po.quality, po.observations_count,
                   c.name as commodity_name, c.category, c.standard_unit
            FROM price_observations po
            JOIN commodities c ON po.commodity_code = c.code
            WHERE po.market_id = ANY($1::text[])
              AND po.commodity_code = $2
              AND po.observed_date >= $3
              AND po.observed_date <= $4
            ORDER BY po.market_id, po.observed_date
            """,
            [mid.value for mid in market_ids],
            commodity.code,
            start_date,
            end_date
        )
        
        return [self._to_domain(row) for row in rows]
    
    async def save(self, observation: PriceObservation) -> None:
        """Save price observation."""
        await self.connection.execute(
            """
            INSERT INTO price_observations (
                id, market_id, commodity_code, price_amount, price_currency,
                price_unit, observed_date, source, quality, observations_count
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            ON CONFLICT (market_id, commodity_code, observed_date, source) 
            DO UPDATE SET
                price_amount = EXCLUDED.price_amount,
                price_currency = EXCLUDED.price_currency,
                price_unit = EXCLUDED.price_unit,
                quality = EXCLUDED.quality,
                observations_count = EXCLUDED.observations_count
            """,
            observation.id,
            observation.market_id.value,
            observation.commodity.code,
            observation.price.amount,
            observation.price.currency,
            observation.price.unit,
            observation.observed_date,
            observation.source,
            observation.quality,
            observation.observations_count
        )
    
    async def save_batch(self, observations: List[PriceObservation]) -> None:
        """Save multiple price observations."""
        # Prepare data for batch insert
        data = [
            (
                obs.id,
                obs.market_id.value,
                obs.commodity.code,
                obs.price.amount,
                obs.price.currency,
                obs.price.unit,
                obs.observed_date,
                obs.source,
                obs.quality,
                obs.observations_count
            )
            for obs in observations
        ]
        
        # Use COPY for efficient batch insert
        await self.connection.copy_records_to_table(
            'price_observations',
            records=data,
            columns=[
                'id', 'market_id', 'commodity_code', 'price_amount',
                'price_currency', 'price_unit', 'observed_date', 'source',
                'quality', 'observations_count'
            ]
        )
    
    async def delete_by_date_range(
        self,
        market_id: MarketId,
        start_date: datetime,
        end_date: datetime
    ) -> int:
        """Delete observations within date range. Returns count of deleted records."""
        result = await self.connection.execute(
            """
            DELETE FROM price_observations
            WHERE market_id = $1
              AND observed_date >= $2
              AND observed_date <= $3
            """,
            market_id.value,
            start_date,
            end_date
        )
        
        # Extract count from result
        count_str = result.split()[-1]  # "DELETE n"
        return int(count_str)
    
    def _to_domain(self, row: asyncpg.Record) -> PriceObservation:
        """Convert database row to domain entity."""
        # Create commodity from row data
        commodity = Commodity(
            code=row['commodity_code'],
            name=row['commodity_name'],
            category=row['category'],
            standard_unit=row['standard_unit']
        )
        
        # Create price value object
        price = Price(
            amount=Decimal(str(row['price_amount'])),
            currency=row['price_currency'],
            unit=row['price_unit']
        )
        
        # Create observation
        observation = PriceObservation(
            market_id=MarketId(row['market_id']),
            commodity=commodity,
            price=price,
            observed_date=row['observed_date'],
            source=row['source'],
            quality=row['quality'],
            observations_count=row['observations_count']
        )
        
        # Set internal ID
        observation.id = row['id']
        
        return observation
    
    async def find_by_date_range(
        self,
        start_date: datetime,
        end_date: datetime,
        market_ids: Optional[List[MarketId]] = None,
        commodity_codes: Optional[List[str]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[PriceObservation]:
        """Find price observations within date range with optional filters."""
        query = """
            SELECT po.id, po.market_id, po.commodity_code, po.price_amount,
                   po.price_currency, po.price_unit, po.observed_date,
                   po.source, po.quality, po.observations_count,
                   c.name as commodity_name, c.category, c.standard_unit
            FROM price_observations po
            JOIN commodities c ON po.commodity_code = c.code
            WHERE po.observed_date >= $1 AND po.observed_date <= $2
        """
        
        params = [start_date, end_date]
        param_count = 2
        
        if market_ids:
            param_count += 1
            query += f" AND po.market_id = ANY(${param_count}::text[])"
            params.append([mid.value for mid in market_ids])
        
        if commodity_codes:
            param_count += 1
            query += f" AND po.commodity_code = ANY(${param_count}::text[])"
            params.append(commodity_codes)
        
        query += " ORDER BY po.observed_date, po.market_id, po.commodity_code"
        
        if limit:
            param_count += 1
            query += f" LIMIT ${param_count}"
            params.append(limit)
        
        if offset:
            param_count += 1
            query += f" OFFSET ${param_count}"
            params.append(offset)
        
        rows = await self.connection.fetch(query, *params)
        return [self._to_domain(row) for row in rows]
    
    async def find_by_markets(
        self,
        market_ids: List[MarketId],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[PriceObservation]:
        """Find price observations for multiple markets."""
        query = """
            SELECT po.id, po.market_id, po.commodity_code, po.price_amount,
                   po.price_currency, po.price_unit, po.observed_date,
                   po.source, po.quality, po.observations_count,
                   c.name as commodity_name, c.category, c.standard_unit
            FROM price_observations po
            JOIN commodities c ON po.commodity_code = c.code
            WHERE po.market_id = ANY($1::text[])
        """
        
        params = [[mid.value for mid in market_ids]]
        param_count = 1
        
        if start_date:
            param_count += 1
            query += f" AND po.observed_date >= ${param_count}"
            params.append(start_date)
        
        if end_date:
            param_count += 1
            query += f" AND po.observed_date <= ${param_count}"
            params.append(end_date)
        
        query += " ORDER BY po.market_id, po.observed_date"
        
        rows = await self.connection.fetch(query, *params)
        return [self._to_domain(row) for row in rows]
    
    async def find_by_commodities(
        self,
        commodities: List[Commodity],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[PriceObservation]:
        """Find price observations for multiple commodities."""
        query = """
            SELECT po.id, po.market_id, po.commodity_code, po.price_amount,
                   po.price_currency, po.price_unit, po.observed_date,
                   po.source, po.quality, po.observations_count,
                   c.name as commodity_name, c.category, c.standard_unit
            FROM price_observations po
            JOIN commodities c ON po.commodity_code = c.code
            WHERE po.commodity_code = ANY($1::text[])
        """
        
        params = [[c.code for c in commodities]]
        param_count = 1
        
        if start_date:
            param_count += 1
            query += f" AND po.observed_date >= ${param_count}"
            params.append(start_date)
        
        if end_date:
            param_count += 1
            query += f" AND po.observed_date <= ${param_count}"
            params.append(end_date)
        
        query += " ORDER BY po.commodity_code, po.observed_date"
        
        rows = await self.connection.fetch(query, *params)
        return [self._to_domain(row) for row in rows]
    
    async def find_commodity_by_code(self, code: str) -> Optional[Commodity]:
        """Find commodity by code."""
        row = await self.connection.fetchrow(
            "SELECT code, name, category, standard_unit FROM commodities WHERE code = $1",
            code
        )
        
        if row:
            return Commodity(
                code=row['code'],
                name=row['name'],
                category=row['category'],
                standard_unit=row['standard_unit']
            )
        return None
    
    async def get_distinct_commodities(self) -> List[Commodity]:
        """Get all distinct commodities in the system."""
        rows = await self.connection.fetch(
            "SELECT code, name, category, standard_unit FROM commodities ORDER BY name"
        )
        
        return [Commodity(
            code=row['code'],
            name=row['name'],
            category=row['category'],
            standard_unit=row['standard_unit']
        ) for row in rows]
    
    async def get_price_statistics(
        self,
        market_id: MarketId,
        commodity: Commodity,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, float]:
        """Get price statistics for a market and commodity."""
        row = await self.connection.fetchrow(
            """
            SELECT 
                COUNT(*) as count,
                AVG(price_amount) as mean_price,
                STDDEV(price_amount) as std_price,
                MIN(price_amount) as min_price,
                MAX(price_amount) as max_price,
                PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY price_amount) as median_price
            FROM price_observations
            WHERE market_id = $1
              AND commodity_code = $2
              AND observed_date >= $3
              AND observed_date <= $4
            """,
            market_id.value,
            commodity.code,
            start_date,
            end_date
        )
        
        return {
            'count': int(row['count']),
            'mean': float(row['mean_price']) if row['mean_price'] else 0.0,
            'std': float(row['std_price']) if row['std_price'] else 0.0,
            'min': float(row['min_price']) if row['min_price'] else 0.0,
            'max': float(row['max_price']) if row['max_price'] else 0.0,
            'median': float(row['median_price']) if row['median_price'] else 0.0
        }