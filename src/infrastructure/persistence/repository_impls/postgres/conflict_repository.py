"""PostgreSQL implementation of conflict repository."""

from datetime import datetime
from typing import Dict, List, Optional
from uuid import UUID

import asyncpg

from .....core.domain.conflict.entities import ConflictEvent
from .....core.domain.conflict.repositories import ConflictEventRepository
from .....core.domain.conflict.value_objects import (
    ConflictEventId, EventType, ConflictLocation, FatalityCount
)


class PostgresConflictEventRepository(ConflictEventRepository):
    """PostgreSQL implementation of conflict event repository."""
    
    def __init__(self, connection: asyncpg.Connection):
        """Initialize with database connection."""
        self.connection = connection
    
    async def find_by_id(self, event_id: str) -> Optional[ConflictEvent]:
        """Find conflict event by ID."""
        row = await self.connection.fetchrow(
            """
            SELECT id, event_id, event_type, sub_event_type, event_date,
                   latitude, longitude, governorate, district, fatalities,
                   notes, source, source_scale, created_at
            FROM conflict_events
            WHERE event_id = $1
            """,
            event_id
        )
        
        if row:
            return self._to_domain(row)
        return None
    
    async def find_by_governorate_and_date_range(
        self,
        governorate: str,
        start_date: datetime,
        end_date: datetime
    ) -> List[ConflictEvent]:
        """Find conflict events in a governorate within date range."""
        rows = await self.connection.fetch(
            """
            SELECT id, event_id, event_type, sub_event_type, event_date,
                   latitude, longitude, governorate, district, fatalities,
                   notes, source, source_scale, created_at
            FROM conflict_events
            WHERE governorate = $1
              AND event_date >= $2
              AND event_date <= $3
            ORDER BY event_date, event_id
            """,
            governorate,
            start_date,
            end_date
        )
        
        return [self._to_domain(row) for row in rows]
    
    async def find_by_location_radius(
        self,
        latitude: float,
        longitude: float,
        radius_km: float,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[ConflictEvent]:
        """Find conflict events within radius of a location."""
        query = """
            SELECT id, event_id, event_type, sub_event_type, event_date,
                   latitude, longitude, governorate, district, fatalities,
                   notes, source, source_scale, created_at,
                   ST_Distance(
                       ST_GeogFromWKB(ST_MakePoint(longitude, latitude)::geometry),
                       ST_GeogFromWKB(ST_MakePoint($2, $1)::geometry)
                   ) / 1000 as distance_km
            FROM conflict_events
            WHERE ST_DWithin(
                ST_GeogFromWKB(ST_MakePoint(longitude, latitude)::geometry),
                ST_GeogFromWKB(ST_MakePoint($2, $1)::geometry),
                $3 * 1000
            )
        """
        
        params = [latitude, longitude, radius_km]
        param_count = 3
        
        if start_date:
            param_count += 1
            query += f" AND event_date >= ${param_count}"
            params.append(start_date)
        
        if end_date:
            param_count += 1
            query += f" AND event_date <= ${param_count}"
            params.append(end_date)
        
        query += " ORDER BY distance_km, event_date"
        
        rows = await self.connection.fetch(query, *params)
        return [self._to_domain(row) for row in rows]
    
    async def find_by_date_range(
        self,
        start_date: datetime,
        end_date: datetime,
        event_types: Optional[List[str]] = None,
        min_fatalities: Optional[int] = None
    ) -> List[ConflictEvent]:
        """Find conflict events within date range with optional filters."""
        query = """
            SELECT id, event_id, event_type, sub_event_type, event_date,
                   latitude, longitude, governorate, district, fatalities,
                   notes, source, source_scale, created_at
            FROM conflict_events
            WHERE event_date >= $1 AND event_date <= $2
        """
        
        params = [start_date, end_date]
        param_count = 2
        
        if event_types:
            param_count += 1
            query += f" AND event_type = ANY(${param_count}::text[])"
            params.append(event_types)
        
        if min_fatalities is not None:
            param_count += 1
            query += f" AND fatalities >= ${param_count}"
            params.append(min_fatalities)
        
        query += " ORDER BY event_date, governorate"
        
        rows = await self.connection.fetch(query, *params)
        return [self._to_domain(row) for row in rows]
    
    async def save(self, event: ConflictEvent) -> None:
        """Save conflict event."""
        await self.connection.execute(
            """
            INSERT INTO conflict_events (
                id, event_id, event_type, sub_event_type, event_date,
                latitude, longitude, governorate, district, fatalities,
                notes, source, source_scale
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
            ON CONFLICT (event_id) DO UPDATE SET
                event_type = EXCLUDED.event_type,
                sub_event_type = EXCLUDED.sub_event_type,
                fatalities = EXCLUDED.fatalities,
                notes = EXCLUDED.notes
            """,
            event.id,
            event.event_id.value,
            event.event_type.value,
            event.sub_event_type,
            event.event_date,
            event.location.latitude,
            event.location.longitude,
            event.location.governorate,
            event.location.district,
            event.fatality_count.value,
            event.notes,
            event.source,
            event.source_scale
        )
        
        # Clear events after saving
        event.clear_events()
    
    async def save_batch(self, events: List[ConflictEvent]) -> None:
        """Save multiple conflict events."""
        # Prepare data for batch insert
        data = [
            (
                event.id,
                event.event_id.value,
                event.event_type.value,
                event.sub_event_type,
                event.event_date,
                event.location.latitude,
                event.location.longitude,
                event.location.governorate,
                event.location.district,
                event.fatality_count.value,
                event.notes,
                event.source,
                event.source_scale
            )
            for event in events
        ]
        
        # Use COPY for efficient batch insert
        await self.connection.copy_records_to_table(
            'conflict_events',
            records=data,
            columns=[
                'id', 'event_id', 'event_type', 'sub_event_type', 'event_date',
                'latitude', 'longitude', 'governorate', 'district', 'fatalities',
                'notes', 'source', 'source_scale'
            ]
        )
        
        # Clear events after saving
        for event in events:
            event.clear_events()
    
    async def delete(self, event_id: str) -> None:
        """Delete conflict event by ID."""
        await self.connection.execute(
            "DELETE FROM conflict_events WHERE event_id = $1",
            event_id
        )
    
    async def get_aggregated_statistics(
        self,
        governorate: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        groupby_period: str = 'month'
    ) -> List[Dict]:
        """Get aggregated conflict statistics."""
        date_trunc = {
            'day': 'day',
            'week': 'week', 
            'month': 'month',
            'quarter': 'quarter',
            'year': 'year'
        }.get(groupby_period, 'month')
        
        query = f"""
            SELECT 
                DATE_TRUNC('{date_trunc}', event_date) as period,
                governorate,
                COUNT(*) as event_count,
                SUM(fatalities) as total_fatalities,
                AVG(fatalities) as avg_fatalities,
                COUNT(DISTINCT event_type) as distinct_event_types
            FROM conflict_events
            WHERE 1=1
        """
        
        params = []
        param_count = 0
        
        if governorate:
            param_count += 1
            query += f" AND governorate = ${param_count}"
            params.append(governorate)
        
        if start_date:
            param_count += 1
            query += f" AND event_date >= ${param_count}"
            params.append(start_date)
        
        if end_date:
            param_count += 1
            query += f" AND event_date <= ${param_count}"
            params.append(end_date)
        
        query += " GROUP BY period, governorate ORDER BY period, governorate"
        
        rows = await self.connection.fetch(query, *params)
        
        return [
            {
                'period': row['period'],
                'governorate': row['governorate'],
                'event_count': row['event_count'],
                'total_fatalities': row['total_fatalities'],
                'avg_fatalities': float(row['avg_fatalities']),
                'distinct_event_types': row['distinct_event_types']
            }
            for row in rows
        ]
    
    def _to_domain(self, row: asyncpg.Record) -> ConflictEvent:
        """Convert database row to domain entity."""
        event = ConflictEvent(
            event_id=ConflictEventId(row['event_id']),
            event_type=EventType(row['event_type']),
            sub_event_type=row['sub_event_type'],
            event_date=row['event_date'],
            location=ConflictLocation(
                latitude=row['latitude'],
                longitude=row['longitude'],
                governorate=row['governorate'],
                district=row['district']
            ),
            fatality_count=FatalityCount(row['fatalities']),
            notes=row['notes'],
            source=row['source'],
            source_scale=row['source_scale']
        )
        
        # Set internal ID
        event.id = row['id']
        
        return event