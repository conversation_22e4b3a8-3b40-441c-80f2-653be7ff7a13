"""Spatial integration service for geographic operations."""

import asyncio
from typing import Dict, List, Tuple, Optional, Any
import numpy as np
from shapely.geometry import Point, Polygon, MultiPolygon
from shapely.ops import transform
import geopandas as gpd
import pyproj
from rtree import index
import structlog

from src.core.domain.market.value_objects import Coordinates
from src.core.domain.market.entities import Market
from src.core.domain.geography.entities import GeographicZone


logger = structlog.get_logger()


class SpatialIntegrationService:
    """
    Service for spatial operations and integration.
    
    Key features:
    - Point-in-polygon operations for zone mapping
    - Buffer calculations (10km, 25km, 50km)
    - CRS transformations between WGS84 and local projections
    - Spatial indices for performance
    - Distance calculations between points
    """
    
    def __init__(self, zones_shapefile_path: str = None):
        """Initialize spatial service with optional zones shapefile."""
        self.zones_gdf = None
        self.spatial_index = None
        self.wgs84 = pyproj.CRS('EPSG:4326')
        self.yemen_utm = pyproj.CRS('EPSG:32638')  # UTM Zone 38N for Yemen
        
        if zones_shapefile_path:
            self._load_zones(zones_shapefile_path)
    
    def _load_zones(self, shapefile_path: str):
        """Load geographic zones from shapefile."""
        try:
            self.zones_gdf = gpd.read_file(shapefile_path)
            # Ensure CRS is WGS84
            if self.zones_gdf.crs != self.wgs84:
                self.zones_gdf = self.zones_gdf.to_crs(self.wgs84)
            
            # Create spatial index
            self._create_spatial_index()
            
            logger.info(
                "zones_loaded",
                count=len(self.zones_gdf),
                crs=str(self.zones_gdf.crs)
            )
        except Exception as e:
            logger.error("zones_load_error", error=str(e))
            raise
    
    def _create_spatial_index(self):
        """Create R-tree spatial index for fast lookups."""
        self.spatial_index = index.Index()
        for idx, zone in self.zones_gdf.iterrows():
            self.spatial_index.insert(idx, zone.geometry.bounds)
    
    async def find_zone_for_point(
        self,
        coords: Coordinates
    ) -> Optional[GeographicZone]:
        """Find which zone contains a given point."""
        if self.zones_gdf is None:
            return None
        
        point = Point(coords.longitude, coords.latitude)
        
        # Use spatial index to find candidate zones
        candidates = list(self.spatial_index.intersection(point.bounds))
        
        for idx in candidates:
            zone = self.zones_gdf.iloc[idx]
            if zone.geometry.contains(point):
                return GeographicZone(
                    id=str(zone.get('zone_id', idx)),
                    name=zone.get('zone_name', f'Zone_{idx}'),
                    control_group=zone.get('control', 'Unknown'),
                    geometry=zone.geometry
                )
        
        return None
    
    async def calculate_buffer(
        self,
        coords: Coordinates,
        radius_km: float
    ) -> Polygon:
        """
        Calculate buffer around a point in kilometers.
        
        Args:
            coords: Center point coordinates
            radius_km: Buffer radius in kilometers
            
        Returns:
            Buffer polygon in WGS84 coordinates
        """
        # Create point in WGS84
        point = Point(coords.longitude, coords.latitude)
        
        # Transform to projected CRS for accurate buffer
        transformer_to_utm = pyproj.Transformer.from_crs(
            self.wgs84, self.yemen_utm, always_xy=True
        )
        point_utm = transform(transformer_to_utm.transform, point)
        
        # Create buffer in meters
        buffer_utm = point_utm.buffer(radius_km * 1000)
        
        # Transform back to WGS84
        transformer_to_wgs = pyproj.Transformer.from_crs(
            self.yemen_utm, self.wgs84, always_xy=True
        )
        buffer_wgs = transform(transformer_to_wgs.transform, buffer_utm)
        
        return buffer_wgs
    
    async def find_points_in_buffer(
        self,
        center: Coordinates,
        radius_km: float,
        points: List[Tuple[str, Coordinates]]
    ) -> List[str]:
        """
        Find all points within a buffer radius.
        
        Args:
            center: Center point
            radius_km: Radius in kilometers
            points: List of (id, coordinates) tuples
            
        Returns:
            List of point IDs within the buffer
        """
        buffer_poly = await self.calculate_buffer(center, radius_km)
        
        points_in_buffer = []
        for point_id, coords in points:
            point = Point(coords.longitude, coords.latitude)
            if buffer_poly.contains(point):
                points_in_buffer.append(point_id)
        
        logger.debug(
            "buffer_search_complete",
            center=(center.latitude, center.longitude),
            radius_km=radius_km,
            total_points=len(points),
            found=len(points_in_buffer)
        )
        
        return points_in_buffer
    
    async def calculate_distance_matrix(
        self,
        points: List[Tuple[str, Coordinates]]
    ) -> Dict[Tuple[str, str], float]:
        """
        Calculate pairwise distances between all points.
        
        Args:
            points: List of (id, coordinates) tuples
            
        Returns:
            Dictionary mapping (id1, id2) to distance in km
        """
        distances = {}
        
        for i, (id1, coords1) in enumerate(points):
            for j, (id2, coords2) in enumerate(points[i+1:], i+1):
                # Use Haversine formula for distance
                distance = self._haversine_distance(coords1, coords2)
                distances[(id1, id2)] = distance
                distances[(id2, id1)] = distance  # Symmetric
        
        return distances
    
    def _haversine_distance(
        self,
        coords1: Coordinates,
        coords2: Coordinates
    ) -> float:
        """Calculate distance between two points using Haversine formula."""
        R = 6371  # Earth's radius in km
        
        lat1, lon1 = np.radians([coords1.latitude, coords1.longitude])
        lat2, lon2 = np.radians([coords2.latitude, coords2.longitude])
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = np.sin(dlat/2)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon/2)**2
        c = 2 * np.arcsin(np.sqrt(a))
        
        return R * c
    
    async def create_market_neighbors(
        self,
        markets: List[Market],
        max_distance_km: float = 100
    ) -> Dict[str, List[str]]:
        """
        Create neighbor graph for markets within distance threshold.
        
        Args:
            markets: List of markets
            max_distance_km: Maximum distance to consider as neighbors
            
        Returns:
            Dictionary mapping market_id to list of neighbor market_ids
        """
        # Prepare points
        points = [(m.id, m.location) for m in markets]
        
        # Calculate distances
        distances = await self.calculate_distance_matrix(points)
        
        # Build neighbor graph
        neighbors = {m.id: [] for m in markets}
        
        for (id1, id2), distance in distances.items():
            if distance <= max_distance_km:
                neighbors[id1].append(id2)
        
        logger.info(
            "neighbor_graph_created",
            n_markets=len(markets),
            max_distance_km=max_distance_km,
            avg_neighbors=np.mean([len(n) for n in neighbors.values()])
        )
        
        return neighbors
    
    async def aggregate_to_grid(
        self,
        points: List[Tuple[Coordinates, float]],
        resolution_km: float = 10
    ) -> gpd.GeoDataFrame:
        """
        Aggregate point data to regular grid cells.
        
        Args:
            points: List of (coordinates, value) tuples
            resolution_km: Grid cell size in kilometers
            
        Returns:
            GeoDataFrame with grid cells and aggregated values
        """
        if not points:
            return gpd.GeoDataFrame()
        
        # Get bounds
        lons = [p[0].longitude for p in points]
        lats = [p[0].latitude for p in points]
        
        min_lon, max_lon = min(lons), max(lons)
        min_lat, max_lat = min(lats), max(lats)
        
        # Create grid in projected coordinates
        transformer_to_utm = pyproj.Transformer.from_crs(
            self.wgs84, self.yemen_utm, always_xy=True
        )
        
        min_x, min_y = transformer_to_utm.transform(min_lon, min_lat)
        max_x, max_y = transformer_to_utm.transform(max_lon, max_lat)
        
        # Create grid cells
        resolution_m = resolution_km * 1000
        x_coords = np.arange(min_x, max_x + resolution_m, resolution_m)
        y_coords = np.arange(min_y, max_y + resolution_m, resolution_m)
        
        grid_cells = []
        for x in x_coords[:-1]:
            for y in y_coords[:-1]:
                # Create cell polygon
                cell = Polygon([
                    (x, y),
                    (x + resolution_m, y),
                    (x + resolution_m, y + resolution_m),
                    (x, y + resolution_m)
                ])
                grid_cells.append(cell)
        
        # Transform grid back to WGS84
        transformer_to_wgs = pyproj.Transformer.from_crs(
            self.yemen_utm, self.wgs84, always_xy=True
        )
        
        grid_cells_wgs = [
            transform(transformer_to_wgs.transform, cell)
            for cell in grid_cells
        ]
        
        # Create GeoDataFrame
        grid_gdf = gpd.GeoDataFrame(
            geometry=grid_cells_wgs,
            crs=self.wgs84
        )
        
        # Aggregate point values to grid cells
        values = []
        for cell in grid_gdf.geometry:
            cell_values = []
            for coords, value in points:
                point = Point(coords.longitude, coords.latitude)
                if cell.contains(point):
                    cell_values.append(value)
            
            # Aggregate (mean for now, could be sum, max, etc.)
            if cell_values:
                values.append(np.mean(cell_values))
            else:
                values.append(np.nan)
        
        grid_gdf['value'] = values
        
        return grid_gdf