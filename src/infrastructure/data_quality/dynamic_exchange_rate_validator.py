"""
Dynamic Exchange Rate Validation System.

Replaces hard-coded exchange rate multipliers with data-driven validation
that adapts to actual market conditions and temporal variations.
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from decimal import Decimal, InvalidOperation
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass
from enum import Enum

from ...core.domain.market.currency_zones import CurrencyZone
from ...core.domain.market.value_objects import Currency, ExchangeRate
from ...core.utils.logging import get_logger
from ..external_services.exchange_rate_collector import ExchangeRateCollector

logger = get_logger(__name__)


class RateValidityLevel(Enum):
    """Exchange rate validity assessment levels."""
    VALID = "valid"
    SUSPICIOUS = "suspicious"
    INVALID = "invalid"
    MISSING = "missing"


@dataclass
class ExchangeRateValidation:
    """Result of exchange rate validation."""
    zone: CurrencyZone
    rate: Decimal
    date: datetime
    validity: RateValidityLevel
    confidence: float
    validation_flags: List[str]
    suggested_rate: Optional[Decimal] = None
    data_sources: List[str] = None


@dataclass
class ZoneRateConstraints:
    """Dynamic constraints for exchange rates by zone."""
    zone: CurrencyZone
    min_rate: Decimal
    max_rate: Decimal
    max_daily_change: float  # Maximum daily percentage change
    baseline_rate: Optional[Decimal]  # Reference rate for relative validation
    volatility_threshold: float  # Standard deviations for outlier detection
    last_updated: datetime


class DynamicExchangeRateValidator:
    """
    Validates exchange rates using dynamic, data-driven approaches.

    This system replaces hard-coded multipliers with adaptive validation
    that learns from actual exchange rate patterns and market conditions.
    """

    def __init__(
        self,
        exchange_rate_collector: Optional[ExchangeRateCollector] = None,
        validation_window_days: int = 30,
        confidence_threshold: float = 0.7
    ):
        """
        Initialize dynamic validator.

        Args:
            exchange_rate_collector: Service to collect external rate data
            validation_window_days: Days of historical data to use for validation
            confidence_threshold: Minimum confidence for accepting rates
        """
        self.rate_collector = exchange_rate_collector or ExchangeRateCollector()
        self.validation_window_days = validation_window_days
        self.confidence_threshold = confidence_threshold

        # Dynamic constraints learned from data
        self._zone_constraints: Dict[CurrencyZone, ZoneRateConstraints] = {}

        # Historical rate cache for validation
        self._rate_history: Dict[Tuple[CurrencyZone, datetime], List[Decimal]] = {}

        # Market-based validation parameters
        self._market_volatility_cache: Dict[CurrencyZone, float] = {}

        # Initialize constraints
        self._initialize_dynamic_constraints()

    def _initialize_dynamic_constraints(self) -> None:
        """Initialize dynamic constraints based on historical patterns."""
        logger.info("Initializing dynamic exchange rate constraints")

        # Base constraints from known historical bounds (as starting point)
        base_constraints = {
            CurrencyZone.HOUTHI: {
                'min_rate': Decimal('400'),
                'max_rate': Decimal('700'),
                'max_daily_change': 0.05,  # 5% max daily change
                'volatility_threshold': 2.0,
                'baseline_rate': Decimal('535')  # Historical Sana'a rate
            },
            CurrencyZone.GOVERNMENT: {
                'min_rate': Decimal('800'),
                'max_rate': Decimal('3000'),
                'max_daily_change': 0.15,  # Higher volatility in government areas
                'volatility_threshold': 2.5,
                'baseline_rate': Decimal('1800')  # Historical Aden rate
            },
            CurrencyZone.CONTESTED: {
                'min_rate': Decimal('600'),
                'max_rate': Decimal('2500'),
                'max_daily_change': 0.20,  # Highest volatility in contested areas
                'volatility_threshold': 3.0,
                'baseline_rate': Decimal('1200')  # Estimated contested rate
            },
            CurrencyZone.UNKNOWN: {
                'min_rate': Decimal('500'),
                'max_rate': Decimal('2000'),
                'max_daily_change': 0.25,
                'volatility_threshold': 3.5,
                'baseline_rate': Decimal('1000')  # Conservative estimate
            }
        }

        for zone, constraints in base_constraints.items():
            self._zone_constraints[zone] = ZoneRateConstraints(
                zone=zone,
                min_rate=constraints['min_rate'],
                max_rate=constraints['max_rate'],
                max_daily_change=constraints['max_daily_change'],
                baseline_rate=constraints['baseline_rate'],
                volatility_threshold=constraints['volatility_threshold'],
                last_updated=datetime.now()
            )

    async def validate_exchange_rate(
        self,
        zone: CurrencyZone,
        rate: Union[Decimal, float],
        date: datetime,
        source: str = "unknown"
    ) -> ExchangeRateValidation:
        """
        Validate an exchange rate using dynamic criteria.

        Args:
            zone: Currency zone for the rate
            rate: Exchange rate value (YER per USD)
            date: Date of the rate observation
            source: Data source identifier

        Returns:
            Validation result with confidence and flags
        """
        logger.debug(f"Validating {zone.value} rate {rate} for {date}")

        # Convert to Decimal for precision
        try:
            rate_decimal = Decimal(str(rate))
        except (InvalidOperation, ValueError):
            return ExchangeRateValidation(
                zone=zone,
                rate=Decimal('0'),
                date=date,
                validity=RateValidityLevel.INVALID,
                confidence=0.0,
                validation_flags=['invalid_numeric_format'],
                data_sources=[source]
            )

        validation_flags = []
        confidence = 1.0

        # Get constraints for this zone
        constraints = self._zone_constraints.get(zone)
        if not constraints:
            validation_flags.append('no_constraints_available')
            confidence *= 0.5

        # 1. Basic bounds validation
        if constraints:
            if rate_decimal < constraints.min_rate:
                validation_flags.append('below_minimum_bound')
                confidence *= 0.3
            elif rate_decimal > constraints.max_rate:
                validation_flags.append('above_maximum_bound')
                confidence *= 0.3

        # 2. Historical consistency validation
        historical_validation = await self._validate_against_history(
            zone, rate_decimal, date
        )
        validation_flags.extend(historical_validation['flags'])
        confidence *= historical_validation['confidence_multiplier']

        # 3. Cross-zone consistency validation
        cross_zone_validation = await self._validate_cross_zone_consistency(
            zone, rate_decimal, date
        )
        validation_flags.extend(cross_zone_validation['flags'])
        confidence *= cross_zone_validation['confidence_multiplier']

        # 4. Temporal consistency validation
        temporal_validation = await self._validate_temporal_consistency(
            zone, rate_decimal, date
        )
        validation_flags.extend(temporal_validation['flags'])
        confidence *= temporal_validation['confidence_multiplier']

        # 5. Market condition validation
        market_validation = await self._validate_market_conditions(
            zone, rate_decimal, date
        )
        validation_flags.extend(market_validation['flags'])
        confidence *= market_validation['confidence_multiplier']

        # Determine validity level
        validity = self._determine_validity_level(confidence, validation_flags)

        # Suggest corrected rate if needed
        suggested_rate = None
        if validity in [RateValidityLevel.SUSPICIOUS, RateValidityLevel.INVALID]:
            suggested_rate = await self._suggest_corrected_rate(zone, date)

        return ExchangeRateValidation(
            zone=zone,
            rate=rate_decimal,
            date=date,
            validity=validity,
            confidence=max(0.0, min(1.0, confidence)),
            validation_flags=validation_flags,
            suggested_rate=suggested_rate,
            data_sources=[source]
        )

    async def _validate_against_history(
        self,
        zone: CurrencyZone,
        rate: Decimal,
        date: datetime
    ) -> Dict[str, Any]:
        """Validate rate against historical patterns."""
        flags = []
        confidence_multiplier = 1.0

        # Get historical rates for this zone
        history_key = (zone, date)
        historical_rates = self._get_historical_rates(zone, date)

        if not historical_rates:
            flags.append('no_historical_data')
            confidence_multiplier = 0.7
            return {'flags': flags, 'confidence_multiplier': confidence_multiplier}

        # Statistical validation against history
        hist_mean = np.mean(historical_rates)
        hist_std = np.std(historical_rates)

        if hist_std > 0:
            z_score = abs(float(rate) - hist_mean) / hist_std

            if z_score > 3.0:
                flags.append('extreme_historical_deviation')
                confidence_multiplier *= 0.4
            elif z_score > 2.0:
                flags.append('significant_historical_deviation')
                confidence_multiplier *= 0.7

        # Trend validation
        if len(historical_rates) >= 7:  # Need at least a week of data
            recent_trend = np.mean(historical_rates[-7:])
            rate_float = float(rate)

            trend_deviation = abs(rate_float - recent_trend) / recent_trend

            if trend_deviation > 0.3:  # More than 30% from recent trend
                flags.append('breaks_recent_trend')
                confidence_multiplier *= 0.6

        return {'flags': flags, 'confidence_multiplier': confidence_multiplier}

    async def _validate_cross_zone_consistency(
        self,
        zone: CurrencyZone,
        rate: Decimal,
        date: datetime
    ) -> Dict[str, Any]:
        """Validate rate consistency across currency zones."""
        flags = []
        confidence_multiplier = 1.0

        # Get rates for other zones on the same date
        zone_rates = {}
        for other_zone in CurrencyZone:
            if other_zone != zone and other_zone != CurrencyZone.UNKNOWN:
                other_rates = self._get_historical_rates(other_zone, date, days=1)
                if other_rates:
                    zone_rates[other_zone] = np.mean(other_rates)

        if not zone_rates:
            return {'flags': flags, 'confidence_multiplier': confidence_multiplier}

        rate_float = float(rate)

        # Check spreads are reasonable
        for other_zone, other_rate in zone_rates.items():
            spread_ratio = max(rate_float, other_rate) / min(rate_float, other_rate)

            # Maximum reasonable spread based on zone combinations
            max_spreads = {
                (CurrencyZone.HOUTHI, CurrencyZone.GOVERNMENT): 5.0,
                (CurrencyZone.HOUTHI, CurrencyZone.CONTESTED): 3.0,
                (CurrencyZone.GOVERNMENT, CurrencyZone.CONTESTED): 2.5
            }

            zone_pair = tuple(sorted([zone, other_zone], key=lambda x: x.value))
            max_spread = max_spreads.get(zone_pair, 6.0)  # Default max

            if spread_ratio > max_spread:
                flags.append(f'excessive_spread_with_{other_zone.value}')
                confidence_multiplier *= 0.5

        # Validate relative positioning makes sense
        # Government zones should generally have higher rates than Houthi zones
        if zone == CurrencyZone.GOVERNMENT and CurrencyZone.HOUTHI in zone_rates:
            houthi_rate = zone_rates[CurrencyZone.HOUTHI]
            if rate_float <= houthi_rate:
                flags.append('government_rate_not_higher_than_houthi')
                confidence_multiplier *= 0.4

        return {'flags': flags, 'confidence_multiplier': confidence_multiplier}

    async def _validate_temporal_consistency(
        self,
        zone: CurrencyZone,
        rate: Decimal,
        date: datetime
    ) -> Dict[str, Any]:
        """Validate temporal consistency of the rate."""
        flags = []
        confidence_multiplier = 1.0

        # Get previous day's rate
        prev_date = date - timedelta(days=1)
        prev_rates = self._get_historical_rates(zone, prev_date, days=1)

        if not prev_rates:
            return {'flags': flags, 'confidence_multiplier': confidence_multiplier}

        prev_rate = np.mean(prev_rates)
        rate_float = float(rate)

        # Check daily change
        daily_change = abs(rate_float - prev_rate) / prev_rate

        constraints = self._zone_constraints.get(zone)
        max_change = constraints.max_daily_change if constraints else 0.20

        if daily_change > max_change:
            flags.append('excessive_daily_change')
            confidence_multiplier *= 0.3
        elif daily_change > max_change * 0.7:
            flags.append('high_daily_change')
            confidence_multiplier *= 0.7

        # Check for impossible patterns (e.g., repeated exact values)
        recent_rates = self._get_historical_rates(zone, date, days=5)
        if len(recent_rates) >= 3:
            if all(abs(r - rate_float) < 0.01 for r in recent_rates):
                flags.append('suspicious_constant_rate')
                confidence_multiplier *= 0.5

        return {'flags': flags, 'confidence_multiplier': confidence_multiplier}

    async def _validate_market_conditions(
        self,
        zone: CurrencyZone,
        rate: Decimal,
        date: datetime
    ) -> Dict[str, Any]:
        """Validate rate against known market conditions."""
        flags = []
        confidence_multiplier = 1.0

        # This would ideally integrate with conflict data, news, etc.
        # For now, we implement basic seasonal and event-based validation

        # Check for weekend/holiday patterns
        if date.weekday() in [4, 5]:  # Friday/Saturday (weekend in Yemen)
            # Rates might be stale on weekends
            if zone != CurrencyZone.HOUTHI:  # Government areas more affected
                flags.append('weekend_rate_uncertainty')
                confidence_multiplier *= 0.9

        # Check for Ramadan effects (rough approximation)
        ramadan_months = [3, 4, 5]  # Approximate Ramadan timing
        if date.month in ramadan_months:
            flags.append('ramadan_period_potential_volatility')
            confidence_multiplier *= 0.95

        # Check for end-of-month/quarter effects
        if date.day >= 28 or date.day <= 3:
            flags.append('month_end_potential_volatility')
            confidence_multiplier *= 0.95

        return {'flags': flags, 'confidence_multiplier': confidence_multiplier}

    def _get_historical_rates(
        self,
        zone: CurrencyZone,
        date: datetime,
        days: int = None
    ) -> List[float]:
        """Get historical rates for a zone around a specific date."""
        if days is None:
            days = self.validation_window_days

        rates = []

        # Look for rates in the specified window
        for i in range(days):
            check_date = date - timedelta(days=i)
            history_key = (zone, check_date)

            if history_key in self._rate_history:
                rates.extend([float(r) for r in self._rate_history[history_key]])

        return rates

    def _determine_validity_level(
        self,
        confidence: float,
        flags: List[str]
    ) -> RateValidityLevel:
        """Determine validity level based on confidence and flags."""

        # Critical flags that indicate invalid data
        critical_flags = [
            'invalid_numeric_format',
            'below_minimum_bound',
            'above_maximum_bound',
            'extreme_historical_deviation',
            'excessive_spread',
            'government_rate_not_higher_than_houthi'
        ]

        has_critical_flag = any(
            any(critical in flag for critical in critical_flags)
            for flag in flags
        )

        if has_critical_flag or confidence < 0.3:
            return RateValidityLevel.INVALID
        elif confidence < self.confidence_threshold:
            return RateValidityLevel.SUSPICIOUS
        else:
            return RateValidityLevel.VALID

    async def _suggest_corrected_rate(
        self,
        zone: CurrencyZone,
        date: datetime
    ) -> Optional[Decimal]:
        """Suggest a corrected rate based on available data."""

        # Try multiple approaches for rate correction

        # 1. Recent historical average
        recent_rates = self._get_historical_rates(zone, date, days=7)
        if recent_rates:
            suggested = Decimal(str(np.median(recent_rates)))
            logger.info(f"Suggesting rate {suggested} for {zone.value} based on recent median")
            return suggested

        # 2. Cross-zone interpolation
        zone_rates = {}
        for other_zone in CurrencyZone:
            if other_zone != zone and other_zone != CurrencyZone.UNKNOWN:
                other_rates = self._get_historical_rates(other_zone, date, days=3)
                if other_rates:
                    zone_rates[other_zone] = np.mean(other_rates)

        if zone_rates:
            # Use known multipliers as fallback for interpolation
            base_multipliers = {
                CurrencyZone.HOUTHI: Decimal('1.0'),
                CurrencyZone.GOVERNMENT: Decimal('3.5'),
                CurrencyZone.CONTESTED: Decimal('2.0')
            }

            if CurrencyZone.HOUTHI in zone_rates:
                base_rate = zone_rates[CurrencyZone.HOUTHI]
                multiplier = base_multipliers.get(zone, Decimal('2.0'))
                suggested = Decimal(str(base_rate)) * multiplier
                logger.info(f"Suggesting rate {suggested} for {zone.value} based on cross-zone interpolation")
                return suggested

        # 3. Constraint baseline as last resort
        constraints = self._zone_constraints.get(zone)
        if constraints and constraints.baseline_rate:
            logger.info(f"Suggesting baseline rate {constraints.baseline_rate} for {zone.value}")
            return constraints.baseline_rate

        return None

    def add_historical_rate(
        self,
        zone: CurrencyZone,
        rate: Decimal,
        date: datetime
    ) -> None:
        """Add a historical rate to the validation cache."""
        history_key = (zone, date)

        if history_key not in self._rate_history:
            self._rate_history[history_key] = []

        self._rate_history[history_key].append(rate)

    async def update_dynamic_constraints(
        self,
        zone: CurrencyZone,
        recent_rates: List[Tuple[datetime, Decimal]]
    ) -> None:
        """Update dynamic constraints based on recent rate observations."""
        if not recent_rates:
            return

        logger.info(f"Updating dynamic constraints for {zone.value} with {len(recent_rates)} observations")

        rates = [float(rate) for _, rate in recent_rates]

        # Calculate new statistical bounds
        rate_mean = np.mean(rates)
        rate_std = np.std(rates)

        # Update constraints with adaptive bounds
        new_min = max(Decimal('100'), Decimal(str(rate_mean - 3 * rate_std)))
        new_max = min(Decimal('5000'), Decimal(str(rate_mean + 3 * rate_std)))

        # Calculate daily volatility
        if len(recent_rates) > 1:
            daily_changes = []
            sorted_rates = sorted(recent_rates, key=lambda x: x[0])

            for i in range(1, len(sorted_rates)):
                prev_rate = float(sorted_rates[i-1][1])
                curr_rate = float(sorted_rates[i][1])
                if prev_rate > 0:
                    daily_change = abs(curr_rate - prev_rate) / prev_rate
                    daily_changes.append(daily_change)

            if daily_changes:
                max_daily_change = np.percentile(daily_changes, 95)  # 95th percentile
            else:
                max_daily_change = 0.1  # Default 10%
        else:
            max_daily_change = 0.1

        # Update constraints
        if zone in self._zone_constraints:
            constraints = self._zone_constraints[zone]
            constraints.min_rate = new_min
            constraints.max_rate = new_max
            constraints.max_daily_change = min(max_daily_change, 0.5)  # Cap at 50%
            constraints.last_updated = datetime.now()

            logger.info(
                f"Updated {zone.value} constraints: "
                f"min={new_min}, max={new_max}, max_daily_change={max_daily_change:.3f}"
            )

    async def validate_batch_rates(
        self,
        rate_observations: List[Tuple[CurrencyZone, Decimal, datetime, str]]
    ) -> List[ExchangeRateValidation]:
        """Validate a batch of exchange rate observations."""
        logger.info(f"Validating batch of {len(rate_observations)} exchange rates")

        validations = []

        for zone, rate, date, source in rate_observations:
            validation = await self.validate_exchange_rate(zone, rate, date, source)
            validations.append(validation)

            # Add valid rates to history for future validation
            if validation.validity == RateValidityLevel.VALID:
                self.add_historical_rate(zone, rate, date)

        # Update constraints based on valid observations
        zone_observations = {}
        for validation in validations:
            if validation.validity == RateValidityLevel.VALID:
                zone = validation.zone
                if zone not in zone_observations:
                    zone_observations[zone] = []
                zone_observations[zone].append((validation.date, validation.rate))

        for zone, observations in zone_observations.items():
            await self.update_dynamic_constraints(zone, observations)

        return validations

    def get_validation_summary(
        self,
        validations: List[ExchangeRateValidation]
    ) -> Dict[str, Any]:
        """Generate summary statistics for a set of validations."""

        if not validations:
            return {'total': 0, 'valid': 0, 'suspicious': 0, 'invalid': 0}

        validity_counts = {level: 0 for level in RateValidityLevel}
        zone_validity = {zone: {level: 0 for level in RateValidityLevel} for zone in CurrencyZone}

        confidence_scores = []
        common_flags = {}

        for validation in validations:
            validity_counts[validation.validity] += 1
            zone_validity[validation.zone][validation.validity] += 1
            confidence_scores.append(validation.confidence)

            for flag in validation.validation_flags:
                common_flags[flag] = common_flags.get(flag, 0) + 1

        # Most common issues
        top_flags = sorted(common_flags.items(), key=lambda x: x[1], reverse=True)[:5]

        return {
            'total_validations': len(validations),
            'validity_distribution': {level.value: count for level, count in validity_counts.items()},
            'zone_breakdown': {
                zone.value: {level.value: count for level, count in zone_counts.items()}
                for zone, zone_counts in zone_validity.items()
            },
            'average_confidence': np.mean(confidence_scores) if confidence_scores else 0,
            'confidence_std': np.std(confidence_scores) if confidence_scores else 0,
            'top_validation_issues': top_flags,
            'data_quality_score': (
                validity_counts[RateValidityLevel.VALID] / len(validations) * 100
                if validations else 0
            )
        }
