"""
Zone-Specific Quality Control for Yemen Market Data.

Implements quality control bounds and validation rules that are specific
to different currency zones, accounting for the unique characteristics
of Houthi-controlled vs Government-controlled areas.
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass, field
from enum import Enum

from ...core.domain.market.currency_zones import CurrencyZone
from ...core.domain.market.value_objects import Currency, Price
from ...core.domain.market.entities import Market, PriceObservation
from ...core.utils.logging import get_logger

logger = get_logger(__name__)


class QualityIssueType(Enum):
    """Types of quality issues specific to conflict zones."""
    PRICE_OUT_OF_BOUNDS = "price_out_of_bounds"
    SUSPICIOUS_PRICE_PATTERN = "suspicious_price_pattern"
    CURRENCY_MISMATCH = "currency_mismatch"
    CONFLICT_REPORTING_GAP = "conflict_reporting_gap"
    ZONE_INCONSISTENCY = "zone_inconsistency"
    SEASONAL_ANOMALY = "seasonal_anomaly"
    IMPLAUSIBLE_PRICE_LEVEL = "implausible_price_level"
    TRADER_SURVIVOR_BIAS = "trader_survivor_bias"


@dataclass
class ZoneQualityBounds:
    """Quality control bounds specific to a currency zone."""
    zone: CurrencyZone
    commodity: str

    # Price bounds (in USD after conversion)
    min_price_usd: Decimal
    max_price_usd: Decimal

    # Price bounds (in local YER before conversion)
    min_price_yer: Decimal
    max_price_yer: Decimal

    # Volatility thresholds
    max_daily_change_pct: float
    max_weekly_change_pct: float
    max_monthly_change_pct: float

    # Reporting patterns
    typical_reporting_frequency: int  # Days between reports
    max_reporting_gap: int  # Maximum days without reports before flagging

    # Conflict-specific adjustments
    conflict_volatility_multiplier: float  # Higher in contested areas
    siege_price_multiplier: float  # Price increases during sieges

    # Seasonal adjustments
    ramadan_price_multiplier: float
    harvest_season_multiplier: float

    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class QualityIssue:
    """A detected quality issue in zone-specific data."""
    issue_type: QualityIssueType
    zone: CurrencyZone
    market_id: str
    commodity: str
    date: datetime
    severity: str  # 'low', 'medium', 'high', 'critical'
    description: str
    suggested_action: str
    confidence: float
    raw_value: Optional[Union[Decimal, str]] = None
    expected_range: Optional[Tuple[Decimal, Decimal]] = None


@dataclass
class ZoneQualityReport:
    """Quality report for a specific zone."""
    zone: CurrencyZone
    period_start: datetime
    period_end: datetime
    total_observations: int
    issues_detected: List[QualityIssue]
    quality_score: float  # 0-100
    coverage_gaps: List[Dict[str, Any]]
    recommendations: List[str]


class ZoneSpecificQualityControl:
    """
    Implements quality control bounds and validation specific to currency zones.

    This system recognizes that quality control must be adapted to the unique
    characteristics of different territorial control areas in Yemen.
    """

    def __init__(self):
        """Initialize zone-specific quality control system."""

        # Zone-specific quality bounds by commodity
        self._zone_bounds: Dict[Tuple[CurrencyZone, str], ZoneQualityBounds] = {}

        # Conflict intensity data for adjustments
        self._conflict_intensity: Dict[CurrencyZone, float] = {}

        # Initialize default bounds for common commodities
        self._initialize_default_bounds()

        # Price validation cache
        self._validation_cache: Dict[str, Any] = {}

    def _initialize_default_bounds(self) -> None:
        """Initialize default quality bounds for common commodities by zone."""
        logger.info("Initializing zone-specific quality control bounds")

        # Common commodities in Yemen market analysis
        commodities = [
            'wheat_flour', 'rice', 'sugar', 'vegetable_oil', 'fuel_petrol',
            'fuel_diesel', 'wheat', 'beans', 'lentils', 'onions', 'potatoes'
        ]

        # Base bounds in USD (after proper currency conversion)
        base_bounds_usd = {
            'wheat_flour': (0.30, 2.50),  # USD per kg
            'rice': (0.40, 3.00),
            'sugar': (0.35, 2.80),
            'vegetable_oil': (0.90, 8.00),
            'fuel_petrol': (0.50, 3.00),  # USD per liter
            'fuel_diesel': (0.40, 2.50),
            'wheat': (0.25, 2.00),
            'beans': (0.60, 4.00),
            'lentils': (0.70, 5.00),
            'onions': (0.20, 1.50),
            'potatoes': (0.15, 1.20)
        }

        for zone in CurrencyZone:
            if zone == CurrencyZone.UNKNOWN:
                continue

            # Zone-specific adjustments
            zone_adjustments = self._get_zone_adjustments(zone)

            for commodity in commodities:
                if commodity in base_bounds_usd:
                    base_min, base_max = base_bounds_usd[commodity]

                    # Apply zone-specific adjustments
                    adjusted_min = Decimal(str(base_min * zone_adjustments['price_multiplier']))
                    adjusted_max = Decimal(str(base_max * zone_adjustments['price_multiplier']))

                    # YER bounds (approximate, will be refined with dynamic rates)
                    yer_rate = zone_adjustments['typical_exchange_rate']
                    yer_min = adjusted_min * Decimal(str(yer_rate))
                    yer_max = adjusted_max * Decimal(str(yer_rate))

                    bounds = ZoneQualityBounds(
                        zone=zone,
                        commodity=commodity,
                        min_price_usd=adjusted_min,
                        max_price_usd=adjusted_max,
                        min_price_yer=yer_min,
                        max_price_yer=yer_max,
                        max_daily_change_pct=zone_adjustments['max_daily_change'],
                        max_weekly_change_pct=zone_adjustments['max_weekly_change'],
                        max_monthly_change_pct=zone_adjustments['max_monthly_change'],
                        typical_reporting_frequency=zone_adjustments['reporting_frequency'],
                        max_reporting_gap=zone_adjustments['max_reporting_gap'],
                        conflict_volatility_multiplier=zone_adjustments['conflict_multiplier'],
                        siege_price_multiplier=zone_adjustments['siege_multiplier'],
                        ramadan_price_multiplier=zone_adjustments['ramadan_multiplier'],
                        harvest_season_multiplier=zone_adjustments['harvest_multiplier']
                    )

                    self._zone_bounds[(zone, commodity)] = bounds

    def _get_zone_adjustments(self, zone: CurrencyZone) -> Dict[str, float]:
        """Get zone-specific adjustment factors."""

        adjustments = {
            CurrencyZone.HOUTHI: {
                'price_multiplier': 1.0,  # Baseline
                'typical_exchange_rate': 535,
                'max_daily_change': 0.08,  # 8% daily max
                'max_weekly_change': 0.20,
                'max_monthly_change': 0.50,
                'reporting_frequency': 7,  # Weekly
                'max_reporting_gap': 21,  # 3 weeks
                'conflict_multiplier': 1.2,
                'siege_multiplier': 1.5,
                'ramadan_multiplier': 1.15,
                'harvest_multiplier': 0.85
            },
            CurrencyZone.GOVERNMENT: {
                'price_multiplier': 1.1,  # Slightly higher due to import costs
                'typical_exchange_rate': 1800,
                'max_daily_change': 0.15,  # Higher volatility
                'max_weekly_change': 0.35,
                'max_monthly_change': 0.80,
                'reporting_frequency': 7,
                'max_reporting_gap': 14,  # Better infrastructure
                'conflict_multiplier': 1.4,
                'siege_multiplier': 2.0,  # More affected by blockades
                'ramadan_multiplier': 1.20,
                'harvest_multiplier': 0.90
            },
            CurrencyZone.CONTESTED: {
                'price_multiplier': 1.25,  # Highest prices due to uncertainty
                'typical_exchange_rate': 1200,
                'max_daily_change': 0.25,  # Highest volatility
                'max_weekly_change': 0.50,
                'max_monthly_change': 1.00,
                'reporting_frequency': 14,  # Less frequent reporting
                'max_reporting_gap': 35,  # Longest gaps due to conflict
                'conflict_multiplier': 1.8,
                'siege_multiplier': 2.5,
                'ramadan_multiplier': 1.30,
                'harvest_multiplier': 0.80
            }
        }

        return adjustments.get(zone, adjustments[CurrencyZone.HOUTHI])

    async def validate_price_observation(
        self,
        observation: PriceObservation,
        market: Market,
        zone: CurrencyZone
    ) -> List[QualityIssue]:
        """
        Validate a price observation against zone-specific quality bounds.

        Args:
            observation: Price observation to validate
            market: Market where observation was made
            zone: Currency zone of the market

        Returns:
            List of quality issues detected
        """
        issues = []
        commodity = observation.commodity.name.lower().replace(' ', '_')

        # Get bounds for this zone/commodity combination
        bounds_key = (zone, commodity)
        bounds = self._zone_bounds.get(bounds_key)

        if not bounds:
            # Try to find similar commodity or use general bounds
            bounds = self._get_fallback_bounds(zone, commodity)
            if not bounds:
                issues.append(QualityIssue(
                    issue_type=QualityIssueType.ZONE_INCONSISTENCY,
                    zone=zone,
                    market_id=str(observation.market_id.value),
                    commodity=commodity,
                    date=observation.observed_date,
                    severity='medium',
                    description=f"No quality bounds defined for {commodity} in {zone.value}",
                    suggested_action="Define quality bounds for this commodity/zone combination",
                    confidence=0.8
                ))
                return issues

        # 1. Basic price bounds validation
        price_value = observation.price.amount
        currency = observation.price.currency

        # Check currency consistency
        expected_currency = Currency.YER if zone != CurrencyZone.UNKNOWN else None
        if expected_currency and currency != expected_currency:
            issues.append(QualityIssue(
                issue_type=QualityIssueType.CURRENCY_MISMATCH,
                zone=zone,
                market_id=str(observation.market_id.value),
                commodity=commodity,
                date=observation.observed_date,
                severity='high',
                description=f"Expected {expected_currency.value} but got {currency.value}",
                suggested_action="Convert to appropriate currency for zone",
                confidence=0.9,
                raw_value=price_value
            ))

        # Validate against bounds (use YER bounds for YER prices, USD for USD)
        if currency == Currency.YER:
            min_bound, max_bound = bounds.min_price_yer, bounds.max_price_yer
        else:  # USD
            min_bound, max_bound = bounds.min_price_usd, bounds.max_price_usd

        if price_value < min_bound:
            issues.append(QualityIssue(
                issue_type=QualityIssueType.PRICE_OUT_OF_BOUNDS,
                zone=zone,
                market_id=str(observation.market_id.value),
                commodity=commodity,
                date=observation.observed_date,
                severity='high' if price_value < min_bound * Decimal('0.5') else 'medium',
                description=f"Price {price_value} below minimum {min_bound}",
                suggested_action="Verify data source and check for errors",
                confidence=0.8,
                raw_value=price_value,
                expected_range=(min_bound, max_bound)
            ))

        elif price_value > max_bound:
            # Adjust max bound for conflict conditions
            adjusted_max = max_bound * Decimal(str(bounds.conflict_volatility_multiplier))

            if price_value > adjusted_max:
                issues.append(QualityIssue(
                    issue_type=QualityIssueType.PRICE_OUT_OF_BOUNDS,
                    zone=zone,
                    market_id=str(observation.market_id.value),
                    commodity=commodity,
                    date=observation.observed_date,
                    severity='high' if price_value > adjusted_max * Decimal('1.5') else 'medium',
                    description=f"Price {price_value} exceeds maximum {adjusted_max} (conflict-adjusted)",
                    suggested_action="Check for siege conditions or data errors",
                    confidence=0.7,
                    raw_value=price_value,
                    expected_range=(min_bound, adjusted_max)
                ))

        # 2. Seasonal adjustment validation
        seasonal_issues = self._validate_seasonal_patterns(
            observation, bounds, zone, commodity
        )
        issues.extend(seasonal_issues)

        # 3. Conflict-specific validation
        conflict_issues = await self._validate_conflict_patterns(
            observation, market, zone, bounds
        )
        issues.extend(conflict_issues)

        # 4. Reporting pattern validation
        reporting_issues = self._validate_reporting_patterns(
            observation, bounds, zone
        )
        issues.extend(reporting_issues)

        return issues

    def _validate_seasonal_patterns(
        self,
        observation: PriceObservation,
        bounds: ZoneQualityBounds,
        zone: CurrencyZone,
        commodity: str
    ) -> List[QualityIssue]:
        """Validate against expected seasonal patterns."""
        issues = []
        date = observation.observed_date
        price = observation.price.amount

        # Ramadan effects (approximate)
        ramadan_months = [3, 4, 5]  # Varies by year, but rough approximation
        if date.month in ramadan_months:
            expected_multiplier = Decimal(str(bounds.ramadan_price_multiplier))

            # Prices should be higher during Ramadan
            ramadan_min = bounds.min_price_usd * expected_multiplier
            if observation.price.currency == Currency.YER:
                ramadan_min = bounds.min_price_yer * expected_multiplier

            # If price is unexpectedly low during Ramadan
            if price < ramadan_min * Decimal('0.9'):  # 10% tolerance
                issues.append(QualityIssue(
                    issue_type=QualityIssueType.SEASONAL_ANOMALY,
                    zone=zone,
                    market_id=str(observation.market_id.value),
                    commodity=commodity,
                    date=date,
                    severity='medium',
                    description=f"Price {price} unexpectedly low for Ramadan period",
                    suggested_action="Verify price or check for supply improvements",
                    confidence=0.6,
                    raw_value=price
                ))

        # Harvest season effects
        harvest_months = {
            'wheat': [5, 6, 7],  # May-July
            'wheat_flour': [6, 7, 8],  # After wheat harvest
            'vegetable_oil': [9, 10, 11],  # Oil crop harvest
            'rice': [8, 9, 10]  # Rice harvest
        }

        if commodity in harvest_months and date.month in harvest_months[commodity]:
            harvest_multiplier = Decimal(str(bounds.harvest_season_multiplier))

            # Prices should be lower during harvest
            harvest_max = bounds.max_price_usd * harvest_multiplier
            if observation.price.currency == Currency.YER:
                harvest_max = bounds.max_price_yer * harvest_multiplier

            if price > harvest_max * Decimal('1.1'):  # 10% tolerance
                issues.append(QualityIssue(
                    issue_type=QualityIssueType.SEASONAL_ANOMALY,
                    zone=zone,
                    market_id=str(observation.market_id.value),
                    commodity=commodity,
                    date=date,
                    severity='medium',
                    description=f"Price {price} unexpectedly high for harvest season",
                    suggested_action="Check for supply chain disruptions",
                    confidence=0.7,
                    raw_value=price
                ))

        return issues

    async def _validate_conflict_patterns(
        self,
        observation: PriceObservation,
        market: Market,
        zone: CurrencyZone,
        bounds: ZoneQualityBounds
    ) -> List[QualityIssue]:
        """Validate against expected conflict-related patterns."""
        issues = []

        # Check for trader survivor bias
        # In conflict zones, only resilient traders remain, potentially skewing prices
        if zone in [CurrencyZone.CONTESTED, CurrencyZone.GOVERNMENT]:
            # Check if reporting frequency is much lower than expected
            # This would be implemented with actual reporting history

            # For now, flag if price seems "too normal" in high-conflict areas
            price = observation.price.amount
            expected_conflict_min = bounds.min_price_usd * Decimal(str(bounds.conflict_volatility_multiplier))

            if observation.price.currency == Currency.YER:
                expected_conflict_min = bounds.min_price_yer * Decimal(str(bounds.conflict_volatility_multiplier))

            # If price is surprisingly low in conflict zone
            if price < expected_conflict_min:
                issues.append(QualityIssue(
                    issue_type=QualityIssueType.TRADER_SURVIVOR_BIAS,
                    zone=zone,
                    market_id=str(observation.market_id.value),
                    commodity=observation.commodity.name,
                    date=observation.observed_date,
                    severity='medium',
                    description="Price lower than expected for conflict-affected area",
                    suggested_action="Verify with multiple sources, check for subsidies",
                    confidence=0.5,
                    raw_value=price
                ))

        return issues

    def _validate_reporting_patterns(
        self,
        observation: PriceObservation,
        bounds: ZoneQualityBounds,
        zone: CurrencyZone
    ) -> List[QualityIssue]:
        """Validate reporting frequency and patterns."""
        issues = []

        # This would require historical data to implement fully
        # For now, we can flag potential issues based on metadata

        # Check observation count (if very low, might indicate poor coverage)
        if hasattr(observation, 'observations_count') and observation.observations_count:
            if observation.observations_count < 3:  # Very few observations
                issues.append(QualityIssue(
                    issue_type=QualityIssueType.CONFLICT_REPORTING_GAP,
                    zone=zone,
                    market_id=str(observation.market_id.value),
                    commodity=observation.commodity.name,
                    date=observation.observed_date,
                    severity='medium',
                    description=f"Low observation count: {observation.observations_count}",
                    suggested_action="Verify data completeness and reporting coverage",
                    confidence=0.6
                ))

        return issues

    def _get_fallback_bounds(
        self,
        zone: CurrencyZone,
        commodity: str
    ) -> Optional[ZoneQualityBounds]:
        """Get fallback bounds for unmapped commodity/zone combinations."""

        # Try to find similar commodity bounds
        commodity_similarities = {
            'flour': 'wheat_flour',
            'oil': 'vegetable_oil',
            'gasoline': 'fuel_petrol',
            'gas': 'fuel_petrol',
            'diesel': 'fuel_diesel',
            'bean': 'beans',
            'lentil': 'lentils',
            'onion': 'onions',
            'potato': 'potatoes'
        }

        for partial, full_name in commodity_similarities.items():
            if partial in commodity.lower():
                bounds_key = (zone, full_name)
                if bounds_key in self._zone_bounds:
                    return self._zone_bounds[bounds_key]

        # Try general food bounds if no specific match
        general_key = (zone, 'wheat_flour')  # Use wheat flour as general food proxy
        return self._zone_bounds.get(general_key)

    async def validate_batch_observations(
        self,
        observations: List[Tuple[PriceObservation, Market, CurrencyZone]]
    ) -> ZoneQualityReport:
        """Validate a batch of price observations and generate zone quality report."""

        all_issues = []
        zone_observations = {}

        # Group by zone for analysis
        for observation, market, zone in observations:
            if zone not in zone_observations:
                zone_observations[zone] = []
            zone_observations[zone].append((observation, market))

            # Validate individual observation
            obs_issues = await self.validate_price_observation(observation, market, zone)
            all_issues.extend(obs_issues)

        # Generate reports for each zone
        zone_reports = {}

        for zone, zone_obs in zone_observations.items():
            zone_issues = [issue for issue in all_issues if issue.zone == zone]

            # Calculate quality score
            total_obs = len(zone_obs)
            critical_issues = len([i for i in zone_issues if i.severity == 'critical'])
            high_issues = len([i for i in zone_issues if i.severity == 'high'])
            medium_issues = len([i for i in zone_issues if i.severity == 'medium'])

            # Quality score: 100 - penalties for issues
            quality_score = max(0, 100 - (critical_issues * 30) - (high_issues * 15) - (medium_issues * 5))

            # Find coverage gaps
            coverage_gaps = self._identify_coverage_gaps(zone_obs, zone)

            # Generate recommendations
            recommendations = self._generate_zone_recommendations(zone_issues, zone)

            zone_reports[zone] = ZoneQualityReport(
                zone=zone,
                period_start=min(obs.observed_date for obs, _ in zone_obs),
                period_end=max(obs.observed_date for obs, _ in zone_obs),
                total_observations=total_obs,
                issues_detected=zone_issues,
                quality_score=quality_score,
                coverage_gaps=coverage_gaps,
                recommendations=recommendations
            )

        return zone_reports

    def _identify_coverage_gaps(
        self,
        observations: List[Tuple[PriceObservation, Market]],
        zone: CurrencyZone
    ) -> List[Dict[str, Any]]:
        """Identify coverage gaps in data by commodity and time."""
        gaps = []

        # Group by commodity
        commodity_obs = {}
        for obs, market in observations:
            commodity = obs.commodity.name
            if commodity not in commodity_obs:
                commodity_obs[commodity] = []
            commodity_obs[commodity].append(obs.observed_date)

        # Check for temporal gaps
        for commodity, dates in commodity_obs.items():
            sorted_dates = sorted(dates)

            for i in range(1, len(sorted_dates)):
                gap_days = (sorted_dates[i] - sorted_dates[i-1]).days

                # Get expected reporting frequency for this zone
                bounds_key = (zone, commodity.lower().replace(' ', '_'))
                bounds = self._zone_bounds.get(bounds_key)
                max_gap = bounds.max_reporting_gap if bounds else 21  # Default 3 weeks

                if gap_days > max_gap:
                    gaps.append({
                        'type': 'temporal_gap',
                        'commodity': commodity,
                        'gap_start': sorted_dates[i-1],
                        'gap_end': sorted_dates[i],
                        'gap_days': gap_days,
                        'expected_max': max_gap
                    })

        return gaps

    def _generate_zone_recommendations(
        self,
        issues: List[QualityIssue],
        zone: CurrencyZone
    ) -> List[str]:
        """Generate recommendations based on detected issues."""
        recommendations = []

        # Count issue types
        issue_counts = {}
        for issue in issues:
            issue_type = issue.issue_type
            issue_counts[issue_type] = issue_counts.get(issue_type, 0) + 1

        # Generate targeted recommendations
        if issue_counts.get(QualityIssueType.CURRENCY_MISMATCH, 0) > 0:
            recommendations.append(
                f"Standardize currency reporting for {zone.value} zone - "
                "ensure all prices use appropriate local currency"
            )

        if issue_counts.get(QualityIssueType.PRICE_OUT_OF_BOUNDS, 0) > 5:
            recommendations.append(
                f"Review price bounds for {zone.value} zone - "
                "multiple observations outside expected ranges"
            )

        if issue_counts.get(QualityIssueType.CONFLICT_REPORTING_GAP, 0) > 0:
            recommendations.append(
                f"Improve data coverage in {zone.value} zone - "
                "gaps likely due to conflict-related reporting disruptions"
            )

        if issue_counts.get(QualityIssueType.TRADER_SURVIVOR_BIAS, 0) > 0:
            recommendations.append(
                f"Account for trader survivor bias in {zone.value} zone - "
                "remaining traders may not represent full market"
            )

        if issue_counts.get(QualityIssueType.SEASONAL_ANOMALY, 0) > 0:
            recommendations.append(
                "Investigate seasonal price anomalies - "
                "check for supply chain disruptions or policy changes"
            )

        return recommendations

    def update_zone_bounds(
        self,
        zone: CurrencyZone,
        commodity: str,
        new_bounds: ZoneQualityBounds
    ) -> None:
        """Update quality bounds for a specific zone/commodity combination."""
        bounds_key = (zone, commodity)
        new_bounds.last_updated = datetime.now()
        self._zone_bounds[bounds_key] = new_bounds

        logger.info(f"Updated quality bounds for {commodity} in {zone.value}")

    def get_zone_summary(self, zone: CurrencyZone) -> Dict[str, Any]:
        """Get summary of quality bounds and settings for a zone."""
        zone_bounds = {
            commodity: bounds for (z, commodity), bounds in self._zone_bounds.items()
            if z == zone
        }

        if not zone_bounds:
            return {'zone': zone.value, 'commodities': 0, 'bounds': {}}

        return {
            'zone': zone.value,
            'commodities': len(zone_bounds),
            'bounds': {
                commodity: {
                    'min_price_usd': float(bounds.min_price_usd),
                    'max_price_usd': float(bounds.max_price_usd),
                    'max_daily_change': bounds.max_daily_change_pct,
                    'conflict_multiplier': bounds.conflict_volatility_multiplier,
                    'last_updated': bounds.last_updated.isoformat()
                }
                for commodity, bounds in zone_bounds.items()
            }
        }
