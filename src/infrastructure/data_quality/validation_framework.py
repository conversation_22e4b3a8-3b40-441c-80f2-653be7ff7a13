"""Data validation framework for ensuring data quality."""
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, Protocol
import pandas as pd
import numpy as np
from datetime import datetime


class ValidationLevel(Enum):
    """Validation strictness levels."""
    STRICT = "strict"      # Any error stops processing
    MODERATE = "moderate"  # Critical errors stop, warnings continue
    LENIENT = "lenient"    # Only catastrophic errors stop


@dataclass
class ValidationReport:
    """Report from data validation."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metrics: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def add_error(self, error: str) -> None:
        """Add an error to the report."""
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str) -> None:
        """Add a warning to the report."""
        self.warnings.append(warning)
    
    def merge(self, other: 'ValidationReport') -> 'ValidationReport':
        """Merge another report into this one."""
        return ValidationReport(
            is_valid=self.is_valid and other.is_valid,
            errors=self.errors + other.errors,
            warnings=self.warnings + other.warnings,
            metrics={**self.metrics, **other.metrics}
        )


class DataValidator(Protocol):
    """Protocol for data validators."""
    
    def validate(self, data: Any) -> ValidationReport:
        """Validate data and return report."""
        ...


class PanelDataValidator:
    """Validator for panel data structures."""
    
    def __init__(self, required_columns: List[str], index_columns: List[str]):
        self.required_columns = required_columns
        self.index_columns = index_columns
    
    def validate(self, df: pd.DataFrame) -> ValidationReport:
        """Validate panel data DataFrame."""
        report = ValidationReport(is_valid=True)
        
        # Check required columns
        missing_cols = set(self.required_columns) - set(df.columns)
        if missing_cols:
            report.add_error(f"Missing required columns: {missing_cols}")
        
        # Check index uniqueness
        if df[self.index_columns].duplicated().any():
            n_dups = df[self.index_columns].duplicated().sum()
            report.add_error(f"Found {n_dups} duplicate index combinations")
        
        # Check for all-null columns
        null_cols = [col for col in df.columns if df[col].isna().all()]
        if null_cols:
            report.add_warning(f"Columns with all null values: {null_cols}")
        
        # Calculate coverage metrics
        report.metrics['total_observations'] = len(df)
        report.metrics['complete_observations'] = len(df.dropna())
        report.metrics['coverage_rate'] = len(df.dropna()) / len(df) if len(df) > 0 else 0
        
        return report


class PriceDataValidator:
    """Validator specific to price data."""
    
    def __init__(self, min_price: float = 0.01, max_price: float = 1000000):
        self.min_price = min_price
        self.max_price = max_price
    
    def validate(self, df: pd.DataFrame, price_column: str = 'price') -> ValidationReport:
        """Validate price data."""
        report = ValidationReport(is_valid=True)
        
        if price_column not in df.columns:
            report.add_error(f"Price column '{price_column}' not found")
            return report
        
        prices = df[price_column]
        
        # Check for negative prices
        if (prices < 0).any():
            n_negative = (prices < 0).sum()
            report.add_error(f"Found {n_negative} negative prices")
        
        # Check for outliers
        if (prices < self.min_price).any():
            n_too_low = (prices < self.min_price).sum()
            report.add_warning(f"Found {n_too_low} prices below {self.min_price}")
        
        if (prices > self.max_price).any():
            n_too_high = (prices > self.max_price).sum()
            report.add_warning(f"Found {n_too_high} prices above {self.max_price}")
        
        # Statistical checks
        report.metrics['price_mean'] = prices.mean()
        report.metrics['price_std'] = prices.std()
        report.metrics['price_min'] = prices.min()
        report.metrics['price_max'] = prices.max()
        
        # Check for sudden jumps
        price_changes = prices.pct_change()
        large_changes = price_changes.abs() > 2.0  # 200% change
        if large_changes.any():
            n_jumps = large_changes.sum()
            report.add_warning(f"Found {n_jumps} price changes >200%")
        
        return report


class ExchangeRateValidator:
    """Validator for exchange rate data."""
    
    def __init__(self):
        # Yemen-specific bounds based on historical data
        self.min_rate = 100    # YER/USD
        self.max_rate = 3000   # YER/USD
    
    def validate(self, df: pd.DataFrame, rate_column: str = 'exchange_rate') -> ValidationReport:
        """Validate exchange rate data."""
        report = ValidationReport(is_valid=True)
        
        if rate_column not in df.columns:
            report.add_error(f"Exchange rate column '{rate_column}' not found")
            return report
        
        rates = df[rate_column]
        
        # Check bounds
        if (rates < self.min_rate).any():
            n_low = (rates < self.min_rate).sum()
            report.add_error(f"Found {n_low} exchange rates below {self.min_rate}")
        
        if (rates > self.max_rate).any():
            n_high = (rates > self.max_rate).sum()
            report.add_error(f"Found {n_high} exchange rates above {self.max_rate}")
        
        # Check for zone consistency
        if 'currency_zone' in df.columns:
            for zone in df['currency_zone'].unique():
                zone_rates = df[df['currency_zone'] == zone][rate_column]
                zone_std = zone_rates.std()
                zone_mean = zone_rates.mean()
                
                # Check if variation within zone is reasonable
                if zone_std / zone_mean > 0.5:  # CV > 50%
                    report.add_warning(
                        f"High exchange rate variation in zone {zone}: "
                        f"CV={zone_std/zone_mean:.2f}"
                    )
        
        report.metrics['rate_mean'] = rates.mean()
        report.metrics['rate_std'] = rates.std()
        report.metrics['rate_min'] = rates.min()
        report.metrics['rate_max'] = rates.max()
        
        return report


class ConflictDataValidator:
    """Validator for conflict event data."""
    
    def validate(self, df: pd.DataFrame) -> ValidationReport:
        """Validate conflict data."""
        report = ValidationReport(is_valid=True)
        
        # Check required columns
        required = ['date', 'latitude', 'longitude', 'event_type', 'fatalities']
        missing = set(required) - set(df.columns)
        if missing:
            report.add_error(f"Missing required columns: {missing}")
            return report
        
        # Check coordinates
        if (df['latitude'].abs() > 90).any():
            report.add_error("Invalid latitude values (>90 or <-90)")
        
        if (df['longitude'].abs() > 180).any():
            report.add_error("Invalid longitude values (>180 or <-180)")
        
        # Check Yemen bounds (approximate)
        yemen_bounds = {
            'lat_min': 12.0, 'lat_max': 19.0,
            'lon_min': 42.0, 'lon_max': 54.0
        }
        
        outside_yemen = (
            (df['latitude'] < yemen_bounds['lat_min']) |
            (df['latitude'] > yemen_bounds['lat_max']) |
            (df['longitude'] < yemen_bounds['lon_min']) |
            (df['longitude'] > yemen_bounds['lon_max'])
        )
        
        if outside_yemen.any():
            n_outside = outside_yemen.sum()
            report.add_warning(f"Found {n_outside} events outside Yemen bounds")
        
        # Check fatalities
        if (df['fatalities'] < 0).any():
            report.add_error("Negative fatalities found")
        
        # Check for duplicates
        dup_cols = ['date', 'latitude', 'longitude', 'event_type']
        if df[dup_cols].duplicated().any():
            n_dups = df[dup_cols].duplicated().sum()
            report.add_warning(f"Found {n_dups} potential duplicate events")
        
        report.metrics['total_events'] = len(df)
        report.metrics['total_fatalities'] = df['fatalities'].sum()
        report.metrics['event_types'] = df['event_type'].value_counts().to_dict()
        
        return report


class CompositeValidator:
    """Combines multiple validators."""
    
    def __init__(self, validators: List[DataValidator]):
        self.validators = validators
    
    def validate(self, data: Any) -> ValidationReport:
        """Run all validators and merge reports."""
        reports = [v.validate(data) for v in self.validators]
        
        # Start with first report
        combined = reports[0]
        
        # Merge others
        for report in reports[1:]:
            combined = combined.merge(report)
        
        return combined