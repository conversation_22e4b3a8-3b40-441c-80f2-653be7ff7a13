"""Interactive Fixed Effects estimator implementation."""

from typing import Dict, Any
import pandas as pd
import numpy as np
from datetime import datetime

from ....core.models.interfaces import Estimator, EstimationResult, DiagnosticResult
from ....core.models.panel import InteractiveFixedEffectsModelWrapper
from ....core.utils.logging import get_logger


logger = get_logger(__name__)


class InteractiveFixedEffectsEstimator(Estimator):
    """Estimator for Interactive Fixed Effects models."""

    def estimate(
        self, model: InteractiveFixedEffectsModelWrapper, data: pd.DataFrame
    ) -> EstimationResult:
        """
        Estimate IFE model parameters.

        Args:
            model: IFE model wrapper instance
            data: Prepared panel data

        Returns:
            EstimationResult with IFE estimates
        """
        logger.info("Estimating Interactive Fixed Effects model")

        # Fit the model (the wrapper handles the actual IFE estimation)
        model.fit(data)

        # Get results from the fitted model
        results_dict = model.get_results_dict()

        # Create EstimationResult
        result = EstimationResult(
            model_type="interactive_fixed_effects",
            estimation_date=datetime.now(),
            n_observations=results_dict["n_observations"],
            parameters=results_dict["coefficients"],
            standard_errors=results_dict["standard_errors"],
            t_statistics=results_dict["t_statistics"],
            p_values=results_dict["p_values"],
            r_squared=results_dict["r_squared"],
            adjusted_r_squared=results_dict["adjusted_r_squared"],
            residuals=results_dict["residuals"],
            metadata={
                "n_factors": results_dict["n_factors"],
                "converged": results_dict["converged"],
                "iterations": results_dict["iterations"],
                "factor_contribution": results_dict["factor_contribution"],
                "factors_shape": (
                    len(results_dict["factors"]),
                    results_dict["n_factors"],
                ),
                "loadings_shape": results_dict["loadings_shape"],
            },
        )

        logger.info(
            f"IFE estimation complete: R² = {result.r_squared:.4f}, "
            f"Factors = {results_dict['n_factors']}, "
            f"Converged = {results_dict['converged']}"
        )

        return result

    def predict(
        self,
        model: InteractiveFixedEffectsModelWrapper,
        result: EstimationResult,
        data: pd.DataFrame,
    ) -> np.ndarray:
        """
        Generate predictions using fitted IFE model.

        Note: IFE predictions for new entities/times are challenging since they
        require factor estimates. This returns linear predictions without factors.
        """
        return model.predict(data)

    def residuals(
        self,
        model: InteractiveFixedEffectsModelWrapper,
        result: EstimationResult,
        data: pd.DataFrame,
    ) -> np.ndarray:
        """Calculate residuals."""
        # Residuals are already stored in the result
        return result.residuals

    def diagnose(
        self, model: InteractiveFixedEffectsModelWrapper, result: EstimationResult
    ) -> Dict[str, DiagnosticResult]:
        """
        Run diagnostic tests for IFE model.

        Tests include:
        - Factor significance
        - Factor stability over time
        - Residual diagnostics
        """
        diagnostics = {}

        # Factor significance test
        factor_r2 = result.metadata.get("factor_contribution", 0)
        factor_sig = DiagnosticResult(
            test_name="Factor Significance",
            statistic=factor_r2,
            p_value=None,  # Would need bootstrap for p-value
            passed=factor_r2 > 0.05,  # Factors explain >5% of variation
            message=f"Factors explain {factor_r2:.1%} of total variation",
        )
        diagnostics["factor_significance"] = factor_sig

        # Convergence check
        converged = result.metadata.get("converged", False)
        convergence_diag = DiagnosticResult(
            test_name="Algorithm Convergence",
            statistic=float(converged),
            p_value=None,
            passed=converged,
            message=f"Algorithm {'converged' if converged else 'did not converge'} after {result.metadata.get('iterations', 0)} iterations",
        )
        diagnostics["convergence"] = convergence_diag

        # Model selection diagnostic
        n_factors = result.metadata.get("n_factors", 0)
        n_obs = result.n_observations
        factor_params = n_factors * sum(result.metadata.get("factors_shape", [0, 0]))

        # Simple BIC-like criterion
        bic_penalty = factor_params * np.log(n_obs) / n_obs
        model_selection = DiagnosticResult(
            test_name="Model Selection",
            statistic=bic_penalty,
            p_value=None,
            passed=bic_penalty < 0.1,  # Arbitrary threshold
            message=f"Using {n_factors} factors with BIC penalty = {bic_penalty:.4f}",
        )
        diagnostics["model_selection"] = model_selection

        return diagnostics
