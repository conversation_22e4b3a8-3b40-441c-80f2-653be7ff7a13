"""
Progress tracking UI components for data pipeline.

This module provides rich console UI components for tracking the progress
of long-running data processing operations. It uses the Rich library to
create beautiful, informative progress displays.

Features:
    - Multi-stage progress tracking
    - Real-time updates with ETA
    - Error summaries and warnings
    - Resource usage monitoring
    - Beautiful console output

Example Usage:
    ```python
    async with PipelineProgressTracker("WFP Price Processing") as tracker:
        # Track download stage
        await tracker.start_stage("download", total_size=1024000)
        async for chunk in download_chunks():
            await tracker.update_progress(len(chunk))
        
        # Track processing stage
        await tracker.start_stage("processing", total_items=1000)
        for item in items:
            process(item)
            await tracker.increment_progress()
    ```
"""

import asyncio
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Callable
import psutil
import humanize

from rich.console import Console
from rich.progress import (
    Progress, SpinnerColumn, TextColumn, BarColumn,
    TimeElapsedColumn, TimeRemainingColumn, MofNCompleteColumn,
    ProgressColumn, Task
)
from rich.table import Table
from rich.panel import Panel
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.columns import Columns
import structlog


logger = structlog.get_logger()


class StageStatus(Enum):
    """Status of a processing stage."""
    PENDING = "⏳"
    IN_PROGRESS = "🔄"
    COMPLETED = "✅"
    FAILED = "❌"
    SKIPPED = "⏭️"
    WARNING = "⚠️"


@dataclass
class StageInfo:
    """Information about a processing stage."""
    name: str
    status: StageStatus = StageStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    total_items: Optional[int] = None
    completed_items: int = 0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration(self) -> Optional[timedelta]:
        """Get stage duration."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        elif self.start_time:
            return datetime.now() - self.start_time
        return None
    
    @property
    def progress_percentage(self) -> float:
        """Get progress percentage."""
        if self.total_items and self.total_items > 0:
            return (self.completed_items / self.total_items) * 100
        return 0.0


class TransferSpeedColumn(ProgressColumn):
    """Column showing data transfer speed."""
    
    def render(self, task: Task) -> Text:
        """Render transfer speed."""
        speed = task.fields.get("speed", 0)
        if speed > 0:
            return Text(f"{humanize.naturalsize(speed)}/s", style="cyan")
        return Text("--", style="dim")


class ResourceColumn(ProgressColumn):
    """Column showing resource usage."""
    
    def render(self, task: Task) -> Text:
        """Render resource usage."""
        cpu = psutil.cpu_percent(interval=None)
        memory = psutil.virtual_memory().percent
        return Text(f"CPU: {cpu:>4.1f}% | RAM: {memory:>4.1f}%", style="dim")


class PipelineProgressTracker:
    """
    Rich progress tracker for data pipeline operations.
    
    Provides a comprehensive progress display with multiple stages,
    resource monitoring, and error tracking.
    """
    
    def __init__(
        self,
        title: str,
        console: Optional[Console] = None,
        auto_refresh: bool = True,
        show_resources: bool = True
    ):
        """
        Initialize progress tracker.
        
        Args:
            title: Title for the progress display
            console: Rich console instance
            auto_refresh: Whether to auto-refresh display
            show_resources: Whether to show resource usage
        """
        self.title = title
        self.console = console or Console()
        self.auto_refresh = auto_refresh
        self.show_resources = show_resources
        self.stages: Dict[str, StageInfo] = {}
        self.current_stage: Optional[str] = None
        self.start_time = datetime.now()
        self._progress = None
        self._live = None
        self._task_ids: Dict[str, Any] = {}
        self.logger = logger.bind(tracker=title)
    
    async def __aenter__(self):
        """Enter async context."""
        self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit async context."""
        self.stop()
    
    def start(self):
        """Start progress tracking."""
        # Create progress instance with custom columns
        columns = [
            SpinnerColumn(),
            TextColumn("[bold blue]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeElapsedColumn(),
            TimeRemainingColumn(),
        ]
        
        if self.show_resources:
            columns.extend([
                TransferSpeedColumn(),
                ResourceColumn()
            ])
        
        self._progress = Progress(*columns, console=self.console)
        
        # Create layout
        layout = self._create_layout()
        
        # Start live display
        self._live = Live(
            layout,
            console=self.console,
            refresh_per_second=2 if self.auto_refresh else 0.5
        )
        self._live.start()
        
        self.logger.info("progress_tracking_started", title=self.title)
    
    def stop(self):
        """Stop progress tracking."""
        if self._live:
            self._live.stop()
        
        # Show final summary
        self._show_final_summary()
        
        self.logger.info(
            "progress_tracking_completed",
            title=self.title,
            duration=(datetime.now() - self.start_time).total_seconds()
        )
    
    def _create_layout(self) -> Layout:
        """Create the display layout."""
        layout = Layout()
        
        # Title panel
        title_panel = Panel(
            f"[bold cyan]{self.title}[/bold cyan]\n"
            f"Started: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}",
            style="bold on navy_blue"
        )
        
        # Progress panel
        progress_panel = Panel(
            self._progress,
            title="Progress",
            border_style="green"
        )
        
        # Status panel
        status_panel = Panel(
            self._create_status_table(),
            title="Stage Status",
            border_style="blue"
        )
        
        # Arrange layout
        layout.split_column(
            Layout(title_panel, size=4),
            Layout(progress_panel, size=6),
            Layout(status_panel)
        )
        
        return layout
    
    def _create_status_table(self) -> Table:
        """Create status table for all stages."""
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Stage", style="cyan", no_wrap=True)
        table.add_column("Status", justify="center")
        table.add_column("Progress", justify="right")
        table.add_column("Duration", justify="right")
        table.add_column("Errors", justify="center", style="red")
        table.add_column("Warnings", justify="center", style="yellow")
        
        for stage_name, stage in self.stages.items():
            # Format progress
            if stage.total_items:
                progress = f"{stage.completed_items}/{stage.total_items} ({stage.progress_percentage:.0f}%)"
            else:
                progress = "--"
            
            # Format duration
            duration = "--"
            if stage.duration:
                duration = str(stage.duration).split('.')[0]  # Remove microseconds
            
            # Style based on status
            style = None
            if stage.status == StageStatus.FAILED:
                style = "red"
            elif stage.status == StageStatus.WARNING:
                style = "yellow"
            elif stage.status == StageStatus.COMPLETED:
                style = "green"
            
            table.add_row(
                stage_name,
                f"{stage.status.value} {stage.status.name}",
                progress,
                duration,
                str(len(stage.errors)) if stage.errors else "-",
                str(len(stage.warnings)) if stage.warnings else "-",
                style=style
            )
        
        return table
    
    async def add_stage(
        self,
        name: str,
        total_items: Optional[int] = None,
        description: Optional[str] = None
    ):
        """
        Add a new stage to track.
        
        Args:
            name: Stage name
            total_items: Total items to process (for progress bar)
            description: Stage description
        """
        stage = StageInfo(
            name=name,
            total_items=total_items
        )
        self.stages[name] = stage
        
        # Add progress task
        desc = description or f"Stage: {name}"
        if total_items:
            task_id = self._progress.add_task(desc, total=total_items)
        else:
            task_id = self._progress.add_task(desc, total=None)
        
        self._task_ids[name] = task_id
        
        self.logger.info(
            "stage_added",
            stage=name,
            total_items=total_items
        )
    
    async def start_stage(self, name: str):
        """Start processing a stage."""
        if name not in self.stages:
            await self.add_stage(name)
        
        stage = self.stages[name]
        stage.status = StageStatus.IN_PROGRESS
        stage.start_time = datetime.now()
        self.current_stage = name
        
        # Update progress task
        if name in self._task_ids:
            self._progress.update(
                self._task_ids[name],
                description=f"[bold yellow]▶ {name}[/bold yellow]"
            )
        
        self.logger.info("stage_started", stage=name)
    
    async def update_progress(
        self,
        advance: int = 1,
        stage: Optional[str] = None,
        **fields
    ):
        """
        Update progress for current or specified stage.
        
        Args:
            advance: Number of items completed
            stage: Stage name (uses current if not specified)
            **fields: Additional fields (e.g., speed)
        """
        stage_name = stage or self.current_stage
        if not stage_name or stage_name not in self.stages:
            return
        
        stage_info = self.stages[stage_name]
        stage_info.completed_items += advance
        
        # Update progress bar
        if stage_name in self._task_ids:
            self._progress.update(
                self._task_ids[stage_name],
                advance=advance,
                **fields
            )
    
    async def complete_stage(
        self,
        name: str,
        status: StageStatus = StageStatus.COMPLETED
    ):
        """Complete a stage."""
        if name not in self.stages:
            return
        
        stage = self.stages[name]
        stage.status = status
        stage.end_time = datetime.now()
        
        # Update progress task
        if name in self._task_ids:
            style = "green" if status == StageStatus.COMPLETED else "red"
            self._progress.update(
                self._task_ids[name],
                description=f"[{style}]{status.value} {name}[/{style}]"
            )
        
        self.logger.info(
            "stage_completed",
            stage=name,
            status=status.name,
            duration=stage.duration.total_seconds() if stage.duration else 0
        )
    
    async def add_error(self, error: str, stage: Optional[str] = None):
        """Add error to current or specified stage."""
        stage_name = stage or self.current_stage
        if stage_name and stage_name in self.stages:
            self.stages[stage_name].errors.append(error)
            self.logger.error("stage_error", stage=stage_name, error=error)
    
    async def add_warning(self, warning: str, stage: Optional[str] = None):
        """Add warning to current or specified stage."""
        stage_name = stage or self.current_stage
        if stage_name and stage_name in self.stages:
            self.stages[stage_name].warnings.append(warning)
            self.logger.warning("stage_warning", stage=stage_name, warning=warning)
    
    def _show_final_summary(self):
        """Show final summary after completion."""
        # Calculate totals
        total_duration = datetime.now() - self.start_time
        total_errors = sum(len(s.errors) for s in self.stages.values())
        total_warnings = sum(len(s.warnings) for s in self.stages.values())
        completed_stages = sum(1 for s in self.stages.values() 
                             if s.status == StageStatus.COMPLETED)
        
        # Create summary panel
        summary_lines = [
            f"[bold]Pipeline: {self.title}[/bold]",
            f"Total Duration: {str(total_duration).split('.')[0]}",
            f"Stages: {completed_stages}/{len(self.stages)} completed",
        ]
        
        if total_errors > 0:
            summary_lines.append(f"[red]Errors: {total_errors}[/red]")
        if total_warnings > 0:
            summary_lines.append(f"[yellow]Warnings: {total_warnings}[/yellow]")
        
        # Determine overall status
        if total_errors > 0:
            panel_style = "red"
            status_icon = "❌"
        elif total_warnings > 0:
            panel_style = "yellow"
            status_icon = "⚠️"
        else:
            panel_style = "green"
            status_icon = "✅"
        
        summary_panel = Panel(
            "\n".join(summary_lines),
            title=f"{status_icon} Pipeline Complete",
            border_style=panel_style
        )
        
        self.console.print(summary_panel)
        
        # Show detailed errors/warnings if any
        if total_errors > 0 or total_warnings > 0:
            self._show_error_details()
    
    def _show_error_details(self):
        """Show detailed error and warning information."""
        for stage_name, stage in self.stages.items():
            if stage.errors or stage.warnings:
                self.console.print(f"\n[bold]{stage_name}:[/bold]")
                
                if stage.errors:
                    self.console.print("  [red]Errors:[/red]")
                    for error in stage.errors[:5]:  # Show first 5
                        self.console.print(f"    • {error}")
                    if len(stage.errors) > 5:
                        self.console.print(f"    ... and {len(stage.errors) - 5} more")
                
                if stage.warnings:
                    self.console.print("  [yellow]Warnings:[/yellow]")
                    for warning in stage.warnings[:5]:  # Show first 5
                        self.console.print(f"    • {warning}")
                    if len(stage.warnings) > 5:
                        self.console.print(f"    ... and {len(stage.warnings) - 5} more")


# Convenience function for simple progress tracking

@asynccontextmanager
async def track_progress(
    title: str,
    stages: List[str],
    show_resources: bool = True
):
    """
    Simple context manager for progress tracking.
    
    Example:
        ```python
        async with track_progress("Data Processing", ["download", "validate", "transform"]) as tracker:
            await tracker.start_stage("download")
            # ... download logic ...
            await tracker.complete_stage("download")
            
            await tracker.start_stage("validate")
            # ... validation logic ...
            await tracker.complete_stage("validate")
        ```
    """
    tracker = PipelineProgressTracker(title, show_resources=show_resources)
    
    # Add all stages
    for stage in stages:
        await tracker.add_stage(stage)
    
    try:
        tracker.start()
        yield tracker
    finally:
        tracker.stop()