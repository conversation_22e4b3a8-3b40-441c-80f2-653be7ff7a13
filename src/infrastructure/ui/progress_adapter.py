"""
Progress adapter for integrating UI progress tracking with processors.

This module provides adapters to connect the BaseProcessor progress
callbacks with the rich UI progress tracking components.

Example Usage:
    ```python
    # Create progress tracker
    tracker = PipelineProgressTracker("WFP Processing")
    adapter = ProgressAdapter(tracker)
    
    # Use with processor
    result = await processor.process(
        progress_callback=adapter.create_callback()
    )
    ```
"""

import asyncio
from typing import Optional, Callable, Dict, Any
import structlog

from src.infrastructure.ui.progress_tracker import (
    PipelineProgressTracker, StageStatus
)


logger = structlog.get_logger()


class ProgressAdapter:
    """
    Adapter to connect processor progress callbacks with UI tracker.
    
    This adapter translates simple progress callbacks (stage, percentage)
    into rich UI updates with proper stage management.
    """
    
    def __init__(
        self,
        tracker: PipelineProgressTracker,
        total_items: Optional[Dict[str, int]] = None
    ):
        """
        Initialize progress adapter.
        
        Args:
            tracker: Pipeline progress tracker instance
            total_items: Optional mapping of stage names to total items
        """
        self.tracker = tracker
        self.total_items = total_items or {}
        self._current_stage = None
        self._stage_progress = {}
        self._initialized_stages = set()
        self.logger = logger.bind(adapter="ProgressAdapter")
    
    def create_callback(self) -> Callable[[str, float], None]:
        """
        Create a progress callback function for processors.
        
        Returns:
            Callback function that accepts (stage_name, percentage)
        """
        def callback(stage: str, percentage: float):
            # Run async method in sync context
            asyncio.create_task(self._update_progress(stage, percentage))
        
        return callback
    
    async def _update_progress(self, stage: str, percentage: float):
        """Update progress for a stage."""
        try:
            # Initialize stage if needed
            if stage not in self._initialized_stages:
                await self._initialize_stage(stage)
            
            # Start stage if it's new
            if self._current_stage != stage:
                # Complete previous stage if any
                if self._current_stage:
                    await self.tracker.complete_stage(
                        self._current_stage,
                        StageStatus.COMPLETED
                    )
                
                # Start new stage
                await self.tracker.start_stage(stage)
                self._current_stage = stage
            
            # Update progress
            if stage in self.total_items and self.total_items[stage] > 0:
                # Calculate items completed from percentage
                total = self.total_items[stage]
                new_completed = int(total * percentage / 100)
                
                # Calculate advance
                old_completed = self._stage_progress.get(stage, 0)
                advance = new_completed - old_completed
                
                if advance > 0:
                    await self.tracker.update_progress(advance)
                    self._stage_progress[stage] = new_completed
            
            # Complete stage if at 100%
            if percentage >= 100 and stage == self._current_stage:
                await self.tracker.complete_stage(stage)
                self._current_stage = None
                
        except Exception as e:
            self.logger.error(
                "progress_update_failed",
                stage=stage,
                percentage=percentage,
                error=str(e)
            )
    
    async def _initialize_stage(self, stage: str):
        """Initialize a stage in the tracker."""
        # Determine total items for stage
        total = self.total_items.get(stage)
        
        # Common stage mappings
        stage_descriptions = {
            "Checking cache": "Checking cache for existing data",
            "Downloading": "Downloading from source",
            "Download complete": "Download completed",
            "Validating": "Validating data quality",
            "Validation complete": "Validation completed",
            "Transforming": "Transforming to domain entities",
            "Transformation complete": "Transformation completed",
            "Aggregating": "Aggregating to panel frequency",
            "Aggregation complete": "Aggregation completed",
            "Complete": "Processing complete",
            "Complete (from cache)": "Loaded from cache"
        }
        
        description = stage_descriptions.get(stage, stage)
        
        await self.tracker.add_stage(
            name=stage,
            total_items=total,
            description=description
        )
        
        self._initialized_stages.add(stage)
    
    def set_stage_total(self, stage: str, total: int):
        """Set total items for a stage."""
        self.total_items[stage] = total
    
    async def add_error(self, error: str, stage: Optional[str] = None):
        """Add error to current or specified stage."""
        await self.tracker.add_error(error, stage or self._current_stage)
    
    async def add_warning(self, warning: str, stage: Optional[str] = None):
        """Add warning to current or specified stage."""
        await self.tracker.add_warning(warning, stage or self._current_stage)


def create_rich_progress_callback(
    title: str,
    estimated_totals: Optional[Dict[str, int]] = None
) -> tuple[PipelineProgressTracker, Callable[[str, float], None]]:
    """
    Create a rich progress tracker and callback function.
    
    This is a convenience function to quickly set up progress tracking
    for a processor.
    
    Args:
        title: Title for the progress display
        estimated_totals: Optional mapping of stage names to total items
        
    Returns:
        Tuple of (tracker, callback_function)
        
    Example:
        ```python
        tracker, callback = create_rich_progress_callback(
            "WFP Price Processing",
            {"Downloading": 1000, "Transforming": 5000}
        )
        
        tracker.start()
        try:
            result = await processor.process(progress_callback=callback)
        finally:
            tracker.stop()
        ```
    """
    tracker = PipelineProgressTracker(title)
    adapter = ProgressAdapter(tracker, estimated_totals)
    
    return tracker, adapter.create_callback()


class SimpleProgressBar:
    """
    Simple progress bar for non-async contexts.
    
    This provides a basic progress display without the full
    rich UI components.
    """
    
    def __init__(self, title: str, total: Optional[int] = None):
        self.title = title
        self.total = total
        self.current = 0
        self.logger = logger.bind(progress_bar=title)
    
    def update(self, advance: int = 1):
        """Update progress."""
        self.current += advance
        
        if self.total:
            percentage = (self.current / self.total) * 100
            bar_length = 40
            filled = int(bar_length * self.current / self.total)
            bar = "█" * filled + "░" * (bar_length - filled)
            
            print(f"\r{self.title}: [{bar}] {percentage:.1f}%", end="", flush=True)
            
            if self.current >= self.total:
                print()  # New line when complete
    
    def __enter__(self):
        """Enter context."""
        self.logger.info("progress_started", title=self.title)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context."""
        if self.total and self.current < self.total:
            print()  # Ensure new line
        self.logger.info(
            "progress_completed",
            title=self.title,
            completed=self.current,
            total=self.total
        )