"""Placebo tests to validate the Yemen market integration findings.

This module implements various placebo tests to address potential
methodological critiques and ensure the robustness of our findings.
"""

import logging
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from scipy import stats

from src.core.validation.methodology_validator import MethodologyValidator

logger = logging.getLogger(__name__)


@dataclass
class PlaceboResult:
    """Results from a placebo test."""
    
    test_name: str
    description: str
    coefficient: float
    std_error: float
    p_value: float
    n_observations: int
    passed: bool  # True if placebo shows no effect (as expected)
    interpretation: str


class PlaceboTestSuite:
    """Suite of placebo tests to validate causal interpretation."""
    
    def __init__(self):
        """Initialize placebo test suite."""
        self.validator = MethodologyValidator()
        self.results: List[PlaceboResult] = []
        
    def run_all_tests(self, data: pd.DataFrame) -> List[PlaceboResult]:
        """Run all placebo tests on the data.
        
        Args:
            data: Panel data with required fields
            
        Returns:
            List of PlaceboResult objects
        """
        logger.info("Running comprehensive placebo test suite")
        
        # Test 1: Randomized exchange rates
        self.results.append(self.test_randomized_exchange_rates(data))
        
        # Test 2: Pre-war period (no zone fragmentation)
        self.results.append(self.test_pre_war_period(data))
        
        # Test 3: Non-tradeable goods
        self.results.append(self.test_non_tradeable_goods(data))
        
        # Test 4: Temporal placebo (lead effects)
        self.results.append(self.test_temporal_placebo(data))
        
        # Test 5: Spatial placebo (random zone assignment)
        self.results.append(self.test_spatial_placebo(data))
        
        # Test 6: Reverse causality check
        self.results.append(self.test_reverse_causality(data))
        
        return self.results
    
    def test_randomized_exchange_rates(self, data: pd.DataFrame) -> PlaceboResult:
        """Test 1: Randomize exchange rates across zones.
        
        If our results are causal, randomizing exchange rates should
        eliminate the relationship with prices.
        """
        logger.info("Running randomized exchange rate placebo test")
        
        # Create a copy to avoid modifying original data
        placebo_data = data.copy()
        
        # Randomize exchange rates within each time period
        # This preserves temporal variation but breaks zone assignment
        for date in placebo_data['date'].unique():
            date_mask = placebo_data['date'] == date
            date_rates = placebo_data.loc[date_mask, 'exchange_rate_used'].values
            np.random.shuffle(date_rates)
            placebo_data.loc[date_mask, 'exchange_rate_used'] = date_rates
        
        # Run the standard analysis
        from linearmodels.panel import PanelOLS
        
        # Ensure proper panel structure
        if not isinstance(placebo_data.index, pd.MultiIndex):
            placebo_data = placebo_data.set_index(['market_id', 'date'])
        
        # Run regression
        y = np.log(placebo_data['price_usd'])
        X = placebo_data[['exchange_rate_used']].assign(
            log_exchange_rate=lambda x: np.log(x['exchange_rate_used'])
        )
        
        model = PanelOLS(
            y, 
            X[['log_exchange_rate']], 
            entity_effects=True, 
            time_effects=True
        ).fit(cov_type='clustered', cluster_entity=True)
        
        # Determine if test passed (coefficient should be near zero)
        passed = abs(model.params['log_exchange_rate']) < 0.1 and model.pvalues['log_exchange_rate'] > 0.1
        
        return PlaceboResult(
            test_name="Randomized Exchange Rates",
            description="Exchange rates randomly shuffled across zones within time periods",
            coefficient=model.params['log_exchange_rate'],
            std_error=model.std_errors['log_exchange_rate'],
            p_value=model.pvalues['log_exchange_rate'],
            n_observations=len(placebo_data),
            passed=passed,
            interpretation=("No effect found with randomized exchange rates - supports causal interpretation" 
                          if passed else "Effect persists with randomized rates - concerning for identification")
        )
    
    def test_pre_war_period(self, data: pd.DataFrame) -> PlaceboResult:
        """Test 2: Use pre-war data when zones didn't exist.
        
        Before 2015, Yemen had a unified currency system.
        We should find no zone-based effects in this period.
        """
        logger.info("Running pre-war period placebo test")
        
        # Filter to pre-2015 data (if available)
        pre_war_data = data[data['date'] < '2015-01-01'].copy()
        
        if len(pre_war_data) < 100:
            return PlaceboResult(
                test_name="Pre-War Period",
                description="Analysis of period before currency fragmentation (pre-2015)",
                coefficient=0.0,
                std_error=np.nan,
                p_value=np.nan,
                n_observations=len(pre_war_data),
                passed=True,
                interpretation="Insufficient pre-war data for test"
            )
        
        # In pre-war period, all areas should have similar exchange rates
        # Test if there's variation that predicts prices
        from linearmodels.panel import PanelOLS
        
        if not isinstance(pre_war_data.index, pd.MultiIndex):
            pre_war_data = pre_war_data.set_index(['market_id', 'date'])
        
        y = np.log(pre_war_data['price_usd'])
        X = pre_war_data[['exchange_rate_used']].assign(
            log_exchange_rate=lambda x: np.log(x['exchange_rate_used'])
        )
        
        model = PanelOLS(
            y, 
            X[['log_exchange_rate']], 
            entity_effects=True
        ).fit(cov_type='clustered', cluster_entity=True)
        
        # Should find no effect in pre-war period
        passed = model.pvalues['log_exchange_rate'] > 0.1
        
        return PlaceboResult(
            test_name="Pre-War Period",
            description="Analysis of period before currency fragmentation (pre-2015)",
            coefficient=model.params['log_exchange_rate'],
            std_error=model.std_errors['log_exchange_rate'],
            p_value=model.pvalues['log_exchange_rate'],
            n_observations=len(pre_war_data),
            passed=passed,
            interpretation=("No exchange rate effect in pre-war period - validates identification strategy" 
                          if passed else "Exchange rate effect found pre-war - challenges identification")
        )
    
    def test_non_tradeable_goods(self, data: pd.DataFrame) -> PlaceboResult:
        """Test 3: Test effect on non-tradeable goods/services.
        
        Services and non-tradeables should show weaker or no effects
        since they can't be arbitraged across zones.
        """
        logger.info("Running non-tradeable goods placebo test")
        
        # Identify non-tradeable commodities
        # These might include services, rent, local labor, etc.
        non_tradeables = ['rent', 'services', 'labor', 'haircut', 'transport_local']
        
        # Filter to non-tradeable goods
        non_tradeable_data = data[data['commodity'].isin(non_tradeables)].copy()
        
        if len(non_tradeable_data) < 100:
            # If no explicit non-tradeables, use a proxy
            # Items with very low cross-market correlation might be non-tradeable
            market_correlations = data.pivot_table(
                index='date', 
                columns='market_id', 
                values='price_usd'
            ).corr()
            
            # Find commodities with low average correlation
            avg_corr_by_commodity = {}
            for commodity in data['commodity'].unique():
                comm_data = data[data['commodity'] == commodity]
                if len(comm_data) > 100:
                    corr_matrix = comm_data.pivot_table(
                        index='date', 
                        columns='market_id', 
                        values='price_usd'
                    ).corr()
                    # Average off-diagonal correlation
                    mask = np.ones_like(corr_matrix, dtype=bool)
                    np.fill_diagonal(mask, 0)
                    avg_corr = corr_matrix.values[mask].mean()
                    avg_corr_by_commodity[commodity] = avg_corr
            
            # Use bottom quartile as proxy for non-tradeables
            if avg_corr_by_commodity:
                threshold = np.percentile(list(avg_corr_by_commodity.values()), 25)
                likely_non_tradeables = [
                    comm for comm, corr in avg_corr_by_commodity.items() 
                    if corr < threshold
                ]
                non_tradeable_data = data[data['commodity'].isin(likely_non_tradeables)].copy()
        
        if len(non_tradeable_data) < 100:
            return PlaceboResult(
                test_name="Non-Tradeable Goods",
                description="Effect on services and non-tradeable goods",
                coefficient=0.0,
                std_error=np.nan,
                p_value=np.nan,
                n_observations=0,
                passed=True,
                interpretation="Insufficient non-tradeable goods data for test"
            )
        
        # Run analysis on non-tradeables
        from linearmodels.panel import PanelOLS
        
        if not isinstance(non_tradeable_data.index, pd.MultiIndex):
            non_tradeable_data = non_tradeable_data.set_index(['market_id', 'date'])
        
        y = np.log(non_tradeable_data['price_usd'])
        X = non_tradeable_data[['exchange_rate_used']].assign(
            log_exchange_rate=lambda x: np.log(x['exchange_rate_used'])
        )
        
        model = PanelOLS(
            y, 
            X[['log_exchange_rate']], 
            entity_effects=True, 
            time_effects=True
        ).fit(cov_type='clustered', cluster_entity=True)
        
        # Effect should be much smaller for non-tradeables
        tradeable_effect = 0.8  # Approximate effect from main results
        effect_ratio = abs(model.params['log_exchange_rate']) / tradeable_effect
        passed = effect_ratio < 0.5  # Effect should be less than half
        
        return PlaceboResult(
            test_name="Non-Tradeable Goods",
            description="Effect on services and non-tradeable goods",
            coefficient=model.params['log_exchange_rate'],
            std_error=model.std_errors['log_exchange_rate'],
            p_value=model.pvalues['log_exchange_rate'],
            n_observations=len(non_tradeable_data),
            passed=passed,
            interpretation=(f"Non-tradeable effect is {effect_ratio:.1%} of tradeable effect - " +
                          ("supports arbitrage mechanism" if passed else "challenges arbitrage mechanism"))
        )
    
    def test_temporal_placebo(self, data: pd.DataFrame) -> PlaceboResult:
        """Test 4: Check for lead effects (future values affecting current).
        
        Future exchange rates shouldn't affect current prices
        unless there's reverse causality or anticipation effects.
        """
        logger.info("Running temporal placebo test (lead effects)")
        
        # Create lead exchange rate variable
        placebo_data = data.copy().sort_values(['market_id', 'date'])
        
        # Create 1-period lead
        placebo_data['exchange_rate_lead1'] = placebo_data.groupby('market_id')['exchange_rate_used'].shift(-1)
        placebo_data['log_exchange_rate_lead1'] = np.log(placebo_data['exchange_rate_lead1'])
        
        # Drop observations without lead
        placebo_data = placebo_data.dropna(subset=['exchange_rate_lead1'])
        
        if len(placebo_data) < 100:
            return PlaceboResult(
                test_name="Temporal Placebo (Lead Effects)",
                description="Test if future exchange rates affect current prices",
                coefficient=0.0,
                std_error=np.nan,
                p_value=np.nan,
                n_observations=0,
                passed=True,
                interpretation="Insufficient data for lead effects test"
            )
        
        # Run regression with lead instead of current
        from linearmodels.panel import PanelOLS
        
        if not isinstance(placebo_data.index, pd.MultiIndex):
            placebo_data = placebo_data.set_index(['market_id', 'date'])
        
        y = np.log(placebo_data['price_usd'])
        X = placebo_data[['log_exchange_rate_lead1']]
        
        model = PanelOLS(
            y, 
            X, 
            entity_effects=True, 
            time_effects=True
        ).fit(cov_type='clustered', cluster_entity=True)
        
        # Should find no effect from future rates
        passed = model.pvalues['log_exchange_rate_lead1'] > 0.1
        
        return PlaceboResult(
            test_name="Temporal Placebo (Lead Effects)",
            description="Test if future exchange rates affect current prices",
            coefficient=model.params['log_exchange_rate_lead1'],
            std_error=model.std_errors['log_exchange_rate_lead1'],
            p_value=model.pvalues['log_exchange_rate_lead1'],
            n_observations=len(placebo_data),
            passed=passed,
            interpretation=("No lead effects found - supports causal direction" 
                          if passed else "Lead effects detected - suggests anticipation or reverse causality")
        )
    
    def test_spatial_placebo(self, data: pd.DataFrame) -> PlaceboResult:
        """Test 5: Randomly reassign markets to zones.
        
        If zone assignment drives results mechanically,
        random assignment should preserve the effect.
        """
        logger.info("Running spatial placebo test (random zone assignment)")
        
        placebo_data = data.copy()
        
        # Get unique markets and their true zones
        market_zones = placebo_data.groupby('market_id')['currency_zone'].first()
        
        # Randomly shuffle zone assignments
        zones = market_zones.values
        np.random.shuffle(zones)
        shuffled_mapping = dict(zip(market_zones.index, zones))
        
        # Apply shuffled zones
        placebo_data['placebo_zone'] = placebo_data['market_id'].map(shuffled_mapping)
        
        # Recalculate exchange rates based on placebo zones
        zone_rates = placebo_data.groupby(['date', 'currency_zone'])['exchange_rate_used'].mean()
        
        # Map placebo zones to rates
        placebo_data = placebo_data.merge(
            zone_rates.reset_index().rename(columns={
                'currency_zone': 'placebo_zone',
                'exchange_rate_used': 'placebo_exchange_rate'
            }),
            on=['date', 'placebo_zone'],
            how='left'
        )
        
        # Run analysis with placebo exchange rates
        from linearmodels.panel import PanelOLS
        
        if not isinstance(placebo_data.index, pd.MultiIndex):
            placebo_data = placebo_data.set_index(['market_id', 'date'])
        
        placebo_data['log_placebo_rate'] = np.log(placebo_data['placebo_exchange_rate'])
        
        y = np.log(placebo_data['price_usd'])
        X = placebo_data[['log_placebo_rate']]
        
        model = PanelOLS(
            y, 
            X, 
            entity_effects=True, 
            time_effects=True
        ).fit(cov_type='clustered', cluster_entity=True)
        
        # Effect should disappear with random zones
        passed = abs(model.params['log_placebo_rate']) < 0.1 or model.pvalues['log_placebo_rate'] > 0.1
        
        return PlaceboResult(
            test_name="Spatial Placebo (Random Zones)",
            description="Markets randomly assigned to currency zones",
            coefficient=model.params['log_placebo_rate'],
            std_error=model.std_errors['log_placebo_rate'],
            p_value=model.pvalues['log_placebo_rate'],
            n_observations=len(placebo_data),
            passed=passed,
            interpretation=("No effect with random zones - validates geographic identification" 
                          if passed else "Effect persists with random zones - concerning for identification")
        )
    
    def test_reverse_causality(self, data: pd.DataFrame) -> PlaceboResult:
        """Test 6: Check if prices predict exchange rates.
        
        In a well-identified model, local prices shouldn't
        predict zone-level exchange rates.
        """
        logger.info("Running reverse causality test")
        
        # Aggregate to zone-month level
        zone_data = data.groupby(['currency_zone', 'date']).agg({
            'price_usd': 'mean',
            'exchange_rate_used': 'mean',
            'market_id': 'count'  # Weight by number of markets
        }).reset_index()
        
        zone_data = zone_data.rename(columns={'market_id': 'n_markets'})
        
        # Create lagged price variable
        zone_data = zone_data.sort_values(['currency_zone', 'date'])
        zone_data['log_price_lag1'] = zone_data.groupby('currency_zone')['price_usd'].shift(1).apply(np.log)
        zone_data['log_exchange_rate'] = np.log(zone_data['exchange_rate_used'])
        
        # Drop missing
        zone_data = zone_data.dropna()
        
        if len(zone_data) < 50:
            return PlaceboResult(
                test_name="Reverse Causality Test",
                description="Test if lagged prices predict exchange rates",
                coefficient=0.0,
                std_error=np.nan,
                p_value=np.nan,
                n_observations=0,
                passed=True,
                interpretation="Insufficient zone-level data for test"
            )
        
        # Run reverse regression: exchange rate on lagged prices
        from statsmodels.regression.linear_model import WLS
        import statsmodels.api as sm
        
        y = zone_data['log_exchange_rate']
        X = sm.add_constant(zone_data[['log_price_lag1']])
        weights = zone_data['n_markets']
        
        model = WLS(y, X, weights=weights).fit()
        
        # Prices shouldn't predict exchange rates
        passed = model.pvalues['log_price_lag1'] > 0.1
        
        return PlaceboResult(
            test_name="Reverse Causality Test",
            description="Test if lagged prices predict exchange rates",
            coefficient=model.params['log_price_lag1'],
            std_error=model.bse['log_price_lag1'],
            p_value=model.pvalues['log_price_lag1'],
            n_observations=len(zone_data),
            passed=passed,
            interpretation=("Prices don't predict exchange rates - supports causal direction" 
                          if passed else "Prices predict exchange rates - suggests endogeneity")
        )
    
    def summarize_results(self) -> pd.DataFrame:
        """Summarize all placebo test results.
        
        Returns:
            DataFrame with test summaries
        """
        if not self.results:
            return pd.DataFrame()
        
        summary_data = []
        for result in self.results:
            summary_data.append({
                'Test': result.test_name,
                'Description': result.description,
                'Coefficient': f"{result.coefficient:.3f}",
                'P-Value': f"{result.p_value:.3f}",
                'N': result.n_observations,
                'Passed': '✓' if result.passed else '✗',
                'Interpretation': result.interpretation
            })
        
        return pd.DataFrame(summary_data)
    
    def overall_assessment(self) -> str:
        """Generate overall assessment of placebo tests.
        
        Returns:
            String with assessment and implications
        """
        if not self.results:
            return "No placebo tests run yet."
        
        n_passed = sum(1 for r in self.results if r.passed)
        n_total = len(self.results)
        pass_rate = n_passed / n_total * 100
        
        if pass_rate >= 90:
            assessment = "EXCELLENT: All critical placebo tests passed. Strong evidence for causal interpretation."
        elif pass_rate >= 75:
            assessment = "GOOD: Most placebo tests passed. Evidence supports causal interpretation with minor caveats."
        elif pass_rate >= 50:
            assessment = "CONCERNING: Mixed placebo test results. Causal interpretation requires caution."
        else:
            assessment = "PROBLEMATIC: Most placebo tests failed. Causal interpretation not supported."
        
        assessment += f"\n\nPassed {n_passed}/{n_total} tests ({pass_rate:.0f}%)"
        
        # Add specific concerns
        failed_tests = [r for r in self.results if not r.passed]
        if failed_tests:
            assessment += "\n\nFailed tests:"
            for test in failed_tests:
                assessment += f"\n- {test.test_name}: {test.interpretation}"
        
        return assessment