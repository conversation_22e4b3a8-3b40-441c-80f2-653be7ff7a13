"""Test alternative explanations for the Yemen price patterns.

This module implements tests for competing explanations to ensure
our exchange rate mechanism is the primary driver of price differences.
"""

import logging
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from linearmodels.panel import PanelOLS
import statsmodels.api as sm

logger = logging.getLogger(__name__)


@dataclass
class AlternativeExplanationResult:
    """Results from testing an alternative explanation."""
    
    explanation: str
    description: str
    variables_tested: List[str]
    exchange_rate_coef: float
    exchange_rate_pval: float
    alternative_coef: Dict[str, float]
    alternative_pval: Dict[str, float]
    r_squared_base: float
    r_squared_with_alternative: float
    variance_explained_by_alternative: float
    dominates_exchange_rate: bool
    interpretation: str


class AlternativeExplanationTests:
    """Test suite for alternative explanations of price patterns."""
    
    def __init__(self):
        """Initialize alternative explanation tests."""
        self.results: List[AlternativeExplanationResult] = []
        
    def run_all_tests(self, data: pd.DataFrame) -> List[AlternativeExplanationResult]:
        """Run all alternative explanation tests.
        
        Args:
            data: Panel data with price and exchange rate information
            
        Returns:
            List of test results
        """
        logger.info("Testing alternative explanations for price patterns")
        
        # Prepare base data
        base_data = self._prepare_base_data(data)
        
        # Run base model for comparison
        base_results = self._run_base_model(base_data)
        
        # Test each alternative explanation
        self.results.append(self.test_transport_costs(base_data, base_results))
        self.results.append(self.test_market_power(base_data, base_results))
        self.results.append(self.test_quality_differences(base_data, base_results))
        self.results.append(self.test_security_costs(base_data, base_results))
        self.results.append(self.test_information_frictions(base_data, base_results))
        self.results.append(self.test_supply_chain_disruption(base_data, base_results))
        
        return self.results
    
    def _prepare_base_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for alternative explanation tests."""
        # Ensure required columns exist
        required_cols = ['market_id', 'date', 'price_usd', 'exchange_rate_used', 
                        'currency_zone', 'governorate']
        
        missing = set(required_cols) - set(data.columns)
        if missing:
            logger.warning(f"Missing columns for alternative tests: {missing}")
        
        # Create log variables
        clean_data = data.copy()
        clean_data['log_price_usd'] = np.log(clean_data['price_usd'])
        clean_data['log_exchange_rate'] = np.log(clean_data['exchange_rate_used'])
        
        # Set index for panel models
        if not isinstance(clean_data.index, pd.MultiIndex):
            clean_data = clean_data.set_index(['market_id', 'date'])
        
        return clean_data
    
    def _run_base_model(self, data: pd.DataFrame) -> Dict:
        """Run base model with exchange rates only."""
        y = data['log_price_usd']
        X = data[['log_exchange_rate']]
        
        model = PanelOLS(y, X, entity_effects=True, time_effects=True)
        results = model.fit(cov_type='clustered', cluster_entity=True)
        
        return {
            'coefficient': results.params['log_exchange_rate'],
            'p_value': results.pvalues['log_exchange_rate'],
            'r_squared': results.rsquared
        }
    
    def test_transport_costs(self, data: pd.DataFrame, base_results: Dict) -> AlternativeExplanationResult:
        """Test if transport costs explain price differences.
        
        Alternative hypothesis: Price differences are due to varying
        transport costs from ports/borders, not exchange rates.
        """
        logger.info("Testing transport cost explanation")
        
        # Create transport cost proxies
        test_data = data.copy()
        
        # Distance to nearest port (if available)
        if 'distance_to_port' not in test_data.columns:
            # Create proxy based on governorate
            # Coastal governorates have lower transport costs
            coastal_govs = ['aden', 'hodeidah', 'hadramawt', 'mahrah', 'socotra']
            test_data['coastal'] = test_data.reset_index()['governorate'].str.lower().isin(coastal_govs).values
            test_data['distance_proxy'] = (~test_data['coastal']).astype(int)
        else:
            test_data['distance_proxy'] = test_data['distance_to_port'] / 100  # Scale
        
        # Fuel prices affect transport costs
        if 'fuel_price' in test_data.columns:
            test_data['log_fuel_price'] = np.log(test_data['fuel_price'])
            transport_vars = ['distance_proxy', 'log_fuel_price']
        else:
            # Use time trends as proxy for fuel price changes
            test_data['time_trend'] = pd.factorize(test_data.index.get_level_values('date'))[0]
            test_data['transport_cost_proxy'] = test_data['distance_proxy'] * test_data['time_trend'] / 100
            transport_vars = ['distance_proxy', 'transport_cost_proxy']
        
        # Run model with transport costs
        y = test_data['log_price_usd']
        X = test_data[['log_exchange_rate'] + transport_vars]
        
        model = PanelOLS(y, X, entity_effects=True, time_effects=True)
        results = model.fit(cov_type='clustered', cluster_entity=True)
        
        # Calculate variance explained by transport
        r2_improvement = results.rsquared - base_results['r_squared']
        
        # Check if transport dominates exchange rate
        exchange_rate_still_significant = results.pvalues['log_exchange_rate'] < 0.05
        transport_significant = any(results.pvalues[var] < 0.05 for var in transport_vars)
        
        # Coefficient reduction
        coef_reduction = abs(base_results['coefficient'] - results.params['log_exchange_rate']) / abs(base_results['coefficient'])
        
        dominates = (coef_reduction > 0.5) or (not exchange_rate_still_significant and transport_significant)
        
        return AlternativeExplanationResult(
            explanation="Transport Costs",
            description="Distance to ports and fuel prices drive price differences",
            variables_tested=transport_vars,
            exchange_rate_coef=results.params['log_exchange_rate'],
            exchange_rate_pval=results.pvalues['log_exchange_rate'],
            alternative_coef={var: results.params[var] for var in transport_vars},
            alternative_pval={var: results.pvalues[var] for var in transport_vars},
            r_squared_base=base_results['r_squared'],
            r_squared_with_alternative=results.rsquared,
            variance_explained_by_alternative=r2_improvement,
            dominates_exchange_rate=dominates,
            interpretation=(
                f"Transport costs explain {r2_improvement*100:.1f}% additional variance. "
                f"Exchange rate coefficient reduced by {coef_reduction*100:.1f}%. "
                f"{'Transport costs do NOT dominate' if not dominates else 'WARNING: Transport costs may dominate'}"
            )
        )
    
    def test_market_power(self, data: pd.DataFrame, base_results: Dict) -> AlternativeExplanationResult:
        """Test if market concentration explains price differences.
        
        Alternative hypothesis: Monopolistic markets in certain zones
        lead to higher prices, not exchange rates.
        """
        logger.info("Testing market power explanation")
        
        test_data = data.copy()
        
        # Calculate market concentration measures
        # Herfindahl-Hirschman Index proxy
        if 'n_sellers' in test_data.columns:
            # Assume equal market shares as proxy
            test_data['hhi_proxy'] = 10000 / test_data['n_sellers']  # Max HHI when n_sellers = 1
        else:
            # Use market size as proxy (smaller markets likely more concentrated)
            market_sizes = test_data.reset_index().groupby('market_id').size()
            size_mapping = dict(zip(market_sizes.index, market_sizes.values))
            test_data['market_size'] = test_data.reset_index()['market_id'].map(size_mapping).values
            test_data['concentration_proxy'] = 1 / np.log(test_data['market_size'] + 1)
        
        # Add competition variables
        if 'hhi_proxy' in test_data.columns:
            market_power_vars = ['hhi_proxy']
        else:
            market_power_vars = ['concentration_proxy']
        
        # Zone-level competition might differ
        zone_dummies = pd.get_dummies(test_data.reset_index()['currency_zone'], prefix='zone')
        for col in zone_dummies.columns:
            test_data[col] = zone_dummies[col].values
            if 'HOUTHI' in col:  # Test if Houthi areas have different market structure
                test_data[f'{col}_concentration'] = test_data[col] * test_data.get('concentration_proxy', 1)
                market_power_vars.append(f'{col}_concentration')
        
        # Run model with market power variables
        y = test_data['log_price_usd']
        X = test_data[['log_exchange_rate'] + market_power_vars[:3]]  # Limit variables to avoid overfitting
        
        model = PanelOLS(y, X, entity_effects=True, time_effects=True)
        results = model.fit(cov_type='clustered', cluster_entity=True)
        
        # Assess dominance
        r2_improvement = results.rsquared - base_results['r_squared']
        coef_reduction = abs(base_results['coefficient'] - results.params['log_exchange_rate']) / abs(base_results['coefficient'])
        
        market_power_significant = any(
            results.pvalues.get(var, 1) < 0.05 for var in market_power_vars[:3]
        )
        
        dominates = (coef_reduction > 0.5) or (results.pvalues['log_exchange_rate'] > 0.1 and market_power_significant)
        
        return AlternativeExplanationResult(
            explanation="Market Power",
            description="Market concentration and monopolistic behavior drive prices",
            variables_tested=market_power_vars[:3],
            exchange_rate_coef=results.params['log_exchange_rate'],
            exchange_rate_pval=results.pvalues['log_exchange_rate'],
            alternative_coef={var: results.params.get(var, np.nan) for var in market_power_vars[:3]},
            alternative_pval={var: results.pvalues.get(var, np.nan) for var in market_power_vars[:3]},
            r_squared_base=base_results['r_squared'],
            r_squared_with_alternative=results.rsquared,
            variance_explained_by_alternative=r2_improvement,
            dominates_exchange_rate=dominates,
            interpretation=(
                f"Market power explains {r2_improvement*100:.1f}% additional variance. "
                f"Exchange rate effect {'remains strong' if results.pvalues['log_exchange_rate'] < 0.05 else 'weakened'}. "
                f"{'Market power does NOT dominate' if not dominates else 'WARNING: Market power effects detected'}"
            )
        )
    
    def test_quality_differences(self, data: pd.DataFrame, base_results: Dict) -> AlternativeExplanationResult:
        """Test if quality differences explain price patterns.
        
        Alternative hypothesis: Different zones receive different
        quality goods, explaining price differences.
        """
        logger.info("Testing quality differences explanation")
        
        test_data = data.copy()
        
        # Quality proxies
        quality_vars = []
        
        # Import share (imported goods often higher quality)
        if 'import_share' in test_data.columns:
            test_data['import_share_scaled'] = test_data['import_share']
            quality_vars.append('import_share_scaled')
        
        # Price dispersion within commodity-zone as quality proxy
        # Higher dispersion suggests quality differentiation
        price_dispersion = test_data.reset_index().groupby(
            ['commodity', 'currency_zone', 'date']
        )['price_usd'].std()
        
        dispersion_mapping = price_dispersion.to_dict()
        test_data['price_dispersion'] = test_data.reset_index().set_index(
            ['commodity', 'currency_zone', 'date']
        ).index.map(dispersion_mapping).values
        test_data['price_dispersion_scaled'] = test_data['price_dispersion'] / test_data['price_usd']
        quality_vars.append('price_dispersion_scaled')
        
        # Zone-specific quality (North might get lower quality)
        test_data['north_zone'] = (test_data.reset_index()['currency_zone'] == 'HOUTHI').astype(int).values
        quality_vars.append('north_zone')
        
        # Run model
        y = test_data['log_price_usd']
        X = test_data[['log_exchange_rate'] + quality_vars]
        
        model = PanelOLS(y, X, entity_effects=True, time_effects=True)
        results = model.fit(cov_type='clustered', cluster_entity=True)
        
        # Assess impact
        r2_improvement = results.rsquared - base_results['r_squared']
        coef_reduction = abs(base_results['coefficient'] - results.params['log_exchange_rate']) / abs(base_results['coefficient'])
        
        quality_significant = any(results.pvalues.get(var, 1) < 0.05 for var in quality_vars)
        
        dominates = (coef_reduction > 0.5) or (results.pvalues['log_exchange_rate'] > 0.1 and quality_significant)
        
        return AlternativeExplanationResult(
            explanation="Quality Differences",
            description="Systematic quality variation across zones drives price differences",
            variables_tested=quality_vars,
            exchange_rate_coef=results.params['log_exchange_rate'],
            exchange_rate_pval=results.pvalues['log_exchange_rate'],
            alternative_coef={var: results.params.get(var, np.nan) for var in quality_vars},
            alternative_pval={var: results.pvalues.get(var, np.nan) for var in quality_vars},
            r_squared_base=base_results['r_squared'],
            r_squared_with_alternative=results.rsquared,
            variance_explained_by_alternative=r2_improvement,
            dominates_exchange_rate=dominates,
            interpretation=(
                f"Quality differences explain {r2_improvement*100:.1f}% additional variance. "
                f"{'North zone shows different prices' if results.params.get('north_zone', 0) != 0 else 'No zone quality difference'}. "
                f"{'Quality does NOT dominate exchange rates' if not dominates else 'WARNING: Quality differences matter'}"
            )
        )
    
    def test_security_costs(self, data: pd.DataFrame, base_results: Dict) -> AlternativeExplanationResult:
        """Test if security/conflict costs explain price patterns.
        
        Alternative hypothesis: Conflict intensity and checkpoints
        create price differences, not exchange rates.
        """
        logger.info("Testing security costs explanation")
        
        test_data = data.copy()
        security_vars = []
        
        # Conflict intensity
        if 'conflict_intensity' in test_data.columns:
            test_data['log_conflict'] = np.log(test_data['conflict_intensity'] + 1)
            security_vars.append('log_conflict')
        
        # Checkpoint density
        if 'checkpoint_density' in test_data.columns:
            security_vars.append('checkpoint_density')
        elif 'n_checkpoints' in test_data.columns:
            test_data['checkpoint_density'] = test_data['n_checkpoints'] / 100
            security_vars.append('checkpoint_density')
        
        # Attack risk
        if 'attack_risk' in test_data.columns:
            security_vars.append('attack_risk')
        elif 'conflict_events' in test_data.columns:
            # Use recent conflict events as proxy
            test_data['attack_risk_proxy'] = test_data['conflict_events'] / 10
            security_vars.append('attack_risk_proxy')
        
        # Zone-conflict interaction (conflict might affect zones differently)
        if security_vars:
            test_data['north_zone'] = (test_data.reset_index()['currency_zone'] == 'HOUTHI').astype(int).values
            test_data['conflict_north_interaction'] = test_data.get(security_vars[0], 0) * test_data['north_zone']
            security_vars.append('conflict_north_interaction')
        
        if not security_vars:
            # No security variables available
            return AlternativeExplanationResult(
                explanation="Security Costs",
                description="Conflict intensity and security risks drive price differences",
                variables_tested=[],
                exchange_rate_coef=base_results['coefficient'],
                exchange_rate_pval=base_results['p_value'],
                alternative_coef={},
                alternative_pval={},
                r_squared_base=base_results['r_squared'],
                r_squared_with_alternative=base_results['r_squared'],
                variance_explained_by_alternative=0,
                dominates_exchange_rate=False,
                interpretation="No security cost variables available for testing"
            )
        
        # Run model
        y = test_data['log_price_usd']
        X = test_data[['log_exchange_rate'] + security_vars[:3]]  # Limit to avoid overfitting
        
        model = PanelOLS(y, X, entity_effects=True, time_effects=True)
        results = model.fit(cov_type='clustered', cluster_entity=True)
        
        # Assess impact
        r2_improvement = results.rsquared - base_results['r_squared']
        coef_reduction = abs(base_results['coefficient'] - results.params['log_exchange_rate']) / abs(base_results['coefficient'])
        
        security_significant = any(results.pvalues.get(var, 1) < 0.05 for var in security_vars[:3])
        
        dominates = (coef_reduction > 0.5) or (results.pvalues['log_exchange_rate'] > 0.1 and security_significant)
        
        return AlternativeExplanationResult(
            explanation="Security Costs",
            description="Conflict intensity and security risks drive price differences",
            variables_tested=security_vars[:3],
            exchange_rate_coef=results.params['log_exchange_rate'],
            exchange_rate_pval=results.pvalues['log_exchange_rate'],
            alternative_coef={var: results.params.get(var, np.nan) for var in security_vars[:3]},
            alternative_pval={var: results.pvalues.get(var, np.nan) for var in security_vars[:3]},
            r_squared_base=base_results['r_squared'],
            r_squared_with_alternative=results.rsquared,
            variance_explained_by_alternative=r2_improvement,
            dominates_exchange_rate=dominates,
            interpretation=(
                f"Security costs explain {r2_improvement*100:.1f}% additional variance. "
                f"Exchange rate coefficient reduced by {coef_reduction*100:.1f}%. "
                f"{'Security costs do NOT dominate' if not dominates else 'WARNING: Security costs have substantial impact'}"
            )
        )
    
    def test_information_frictions(self, data: pd.DataFrame, base_results: Dict) -> AlternativeExplanationResult:
        """Test if information frictions explain price patterns.
        
        Alternative hypothesis: Poor information flow between zones
        prevents arbitrage, creating persistent price gaps.
        """
        logger.info("Testing information frictions explanation")
        
        test_data = data.copy()
        info_vars = []
        
        # Phone/internet coverage
        if 'phone_coverage' in test_data.columns:
            test_data['low_phone_coverage'] = (test_data['phone_coverage'] < 0.5).astype(int)
            info_vars.append('low_phone_coverage')
        
        if 'internet_access' in test_data.columns:
            test_data['low_internet'] = (test_data['internet_access'] < 0.2).astype(int)
            info_vars.append('low_internet')
        
        # Price synchronization as proxy for information flow
        # Calculate correlation of price changes with other markets
        price_changes = test_data.reset_index().groupby('market_id')['price_usd'].pct_change()
        
        # For simplicity, use variance of price changes as proxy
        # Higher variance = less synchronized = more friction
        price_var_by_market = price_changes.groupby(test_data.reset_index()['market_id']).var()
        var_mapping = price_var_by_market.to_dict()
        
        test_data['price_variance'] = test_data.reset_index()['market_id'].map(var_mapping).values
        test_data['info_friction_proxy'] = np.log(test_data['price_variance'] + 0.001)
        info_vars.append('info_friction_proxy')
        
        # Cross-zone information barriers
        test_data['cross_zone_pair'] = (
            test_data.reset_index()['currency_zone'].isin(['HOUTHI', 'GOVERNMENT'])
        ).astype(int).values
        info_vars.append('cross_zone_pair')
        
        if not info_vars:
            return AlternativeExplanationResult(
                explanation="Information Frictions",
                description="Poor information flow and communication barriers drive price gaps",
                variables_tested=[],
                exchange_rate_coef=base_results['coefficient'],
                exchange_rate_pval=base_results['p_value'],
                alternative_coef={},
                alternative_pval={},
                r_squared_base=base_results['r_squared'],
                r_squared_with_alternative=base_results['r_squared'],
                variance_explained_by_alternative=0,
                dominates_exchange_rate=False,
                interpretation="No information friction variables available"
            )
        
        # Run model
        y = test_data['log_price_usd']
        X = test_data[['log_exchange_rate'] + info_vars[:3]]
        
        model = PanelOLS(y, X, entity_effects=True, time_effects=True)
        results = model.fit(cov_type='clustered', cluster_entity=True)
        
        # Assess impact
        r2_improvement = results.rsquared - base_results['r_squared']
        coef_reduction = abs(base_results['coefficient'] - results.params['log_exchange_rate']) / abs(base_results['coefficient'])
        
        info_significant = any(results.pvalues.get(var, 1) < 0.05 for var in info_vars[:3])
        
        dominates = (coef_reduction > 0.5) or (results.pvalues['log_exchange_rate'] > 0.1 and info_significant)
        
        return AlternativeExplanationResult(
            explanation="Information Frictions",
            description="Poor information flow and communication barriers drive price gaps",
            variables_tested=info_vars[:3],
            exchange_rate_coef=results.params['log_exchange_rate'],
            exchange_rate_pval=results.pvalues['log_exchange_rate'],
            alternative_coef={var: results.params.get(var, np.nan) for var in info_vars[:3]},
            alternative_pval={var: results.pvalues.get(var, np.nan) for var in info_vars[:3]},
            r_squared_base=base_results['r_squared'],
            r_squared_with_alternative=results.rsquared,
            variance_explained_by_alternative=r2_improvement,
            dominates_exchange_rate=dominates,
            interpretation=(
                f"Information frictions explain {r2_improvement*100:.1f}% additional variance. "
                f"{'Cross-zone barriers significant' if 'cross_zone_pair' in info_vars and results.pvalues.get('cross_zone_pair', 1) < 0.05 else 'Limited information effects'}. "
                f"{'Information frictions do NOT dominate' if not dominates else 'WARNING: Information barriers matter'}"
            )
        )
    
    def test_supply_chain_disruption(self, data: pd.DataFrame, base_results: Dict) -> AlternativeExplanationResult:
        """Test if supply chain disruptions explain price patterns.
        
        Alternative hypothesis: Zone-specific supply chain issues
        create price differences, not exchange rates.
        """
        logger.info("Testing supply chain disruption explanation")
        
        test_data = data.copy()
        supply_vars = []
        
        # Port access disruption
        if 'port_accessible' in test_data.columns:
            test_data['port_disrupted'] = (~test_data['port_accessible']).astype(int)
            supply_vars.append('port_disrupted')
        
        # Import delays
        if 'import_delay_days' in test_data.columns:
            test_data['log_import_delay'] = np.log(test_data['import_delay_days'] + 1)
            supply_vars.append('log_import_delay')
        
        # Storage capacity constraints
        if 'storage_capacity' in test_data.columns:
            test_data['storage_constraint'] = (test_data['storage_capacity'] < 0.5).astype(int)
            supply_vars.append('storage_constraint')
        
        # Zone-specific supply routes
        test_data['north_zone'] = (test_data.reset_index()['currency_zone'] == 'HOUTHI').astype(int).values
        test_data['south_zone'] = (test_data.reset_index()['currency_zone'] == 'GOVERNMENT').astype(int).values
        
        # North relies more on land routes, South on ports
        test_data['north_supply_vulnerability'] = test_data['north_zone']
        test_data['south_port_dependency'] = test_data['south_zone']
        supply_vars.extend(['north_supply_vulnerability', 'south_port_dependency'])
        
        # Run model
        y = test_data['log_price_usd']
        X = test_data[['log_exchange_rate'] + supply_vars[:3]]
        
        model = PanelOLS(y, X, entity_effects=True, time_effects=True)
        results = model.fit(cov_type='clustered', cluster_entity=True)
        
        # Assess impact
        r2_improvement = results.rsquared - base_results['r_squared']
        coef_reduction = abs(base_results['coefficient'] - results.params['log_exchange_rate']) / abs(base_results['coefficient'])
        
        supply_significant = any(results.pvalues.get(var, 1) < 0.05 for var in supply_vars[:3])
        
        dominates = (coef_reduction > 0.5) or (results.pvalues['log_exchange_rate'] > 0.1 and supply_significant)
        
        return AlternativeExplanationResult(
            explanation="Supply Chain Disruption",
            description="Zone-specific supply chain constraints and disruptions drive prices",
            variables_tested=supply_vars[:3],
            exchange_rate_coef=results.params['log_exchange_rate'],
            exchange_rate_pval=results.pvalues['log_exchange_rate'],
            alternative_coef={var: results.params.get(var, np.nan) for var in supply_vars[:3]},
            alternative_pval={var: results.pvalues.get(var, np.nan) for var in supply_vars[:3]},
            r_squared_base=base_results['r_squared'],
            r_squared_with_alternative=results.rsquared,
            variance_explained_by_alternative=r2_improvement,
            dominates_exchange_rate=dominates,
            interpretation=(
                f"Supply chain factors explain {r2_improvement*100:.1f}% additional variance. "
                f"Exchange rate effect {'persists strongly' if results.pvalues['log_exchange_rate'] < 0.01 else 'reduced but present'}. "
                f"{'Supply chains do NOT dominate exchange rates' if not dominates else 'WARNING: Supply chain effects detected'}"
            )
        )
    
    def summarize_results(self) -> pd.DataFrame:
        """Create summary table of all alternative explanation tests.
        
        Returns:
            DataFrame with test summaries
        """
        if not self.results:
            return pd.DataFrame()
        
        summary_data = []
        for result in self.results:
            summary_data.append({
                'Alternative Explanation': result.explanation,
                'Exchange Rate Coef': f"{result.exchange_rate_coef:.3f}",
                'Exchange Rate P-val': f"{result.exchange_rate_pval:.3f}",
                'Additional R²': f"{result.variance_explained_by_alternative*100:.1f}%",
                'Dominates?': 'Yes' if result.dominates_exchange_rate else 'No',
                'Key Finding': result.interpretation.split('.')[0]
            })
        
        return pd.DataFrame(summary_data)
    
    def horse_race_analysis(self) -> Dict:
        """Conduct horse race between all explanations.
        
        Returns:
            Dictionary with horse race results and interpretation
        """
        if not self.results:
            return {"error": "No results to analyze"}
        
        # Rank by additional variance explained
        ranked_by_r2 = sorted(
            self.results, 
            key=lambda x: x.variance_explained_by_alternative, 
            reverse=True
        )
        
        # Check which alternatives threaten exchange rate dominance
        threatening_alternatives = [
            r for r in self.results 
            if r.dominates_exchange_rate or r.exchange_rate_pval > 0.05
        ]
        
        # Calculate average coefficient reduction
        coef_reductions = []
        base_coef = self.results[0].r_squared_base if self.results else 0
        
        for result in self.results:
            if result.exchange_rate_coef != 0:
                reduction = 1 - abs(result.exchange_rate_coef / base_coef)
                coef_reductions.append(reduction)
        
        avg_coef_reduction = np.mean(coef_reductions) if coef_reductions else 0
        
        # Generate interpretation
        if not threatening_alternatives and avg_coef_reduction < 0.2:
            interpretation = (
                "STRONG SUPPORT: Exchange rate mechanism dominates all alternative explanations. "
                "No alternative explanation threatens the main finding."
            )
        elif len(threatening_alternatives) <= 1 and avg_coef_reduction < 0.3:
            interpretation = (
                "GOOD SUPPORT: Exchange rate mechanism remains primary driver. "
                f"{threatening_alternatives[0].explanation if threatening_alternatives else 'Minor factors'} "
                "show some influence but don't dominate."
            )
        else:
            interpretation = (
                "MIXED EVIDENCE: Multiple factors contribute to price patterns. "
                f"{len(threatening_alternatives)} alternative explanations show substantial effects. "
                "Exchange rate mechanism is important but not sole driver."
            )
        
        return {
            "ranking": [(r.explanation, r.variance_explained_by_alternative) for r in ranked_by_r2],
            "threatening_alternatives": [r.explanation for r in threatening_alternatives],
            "average_coefficient_reduction": avg_coef_reduction,
            "interpretation": interpretation,
            "strongest_alternative": ranked_by_r2[0].explanation if ranked_by_r2 else None,
            "exchange_rate_robust": len(threatening_alternatives) == 0
        }