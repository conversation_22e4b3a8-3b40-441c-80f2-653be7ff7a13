"""Advanced analytics for specification curve analysis.

This module provides deeper insights into specification curve results,
including fragility analysis, vibration ratios, and specification clustering.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from scipy import stats
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

from src.core.analysis.robustness.specification_curve import SpecificationResult

logger = logging.getLogger(__name__)


class SpecificationCurveAnalytics:
    """Advanced analytics for specification curve results."""
    
    def __init__(self, results: List[SpecificationResult]):
        """Initialize with specification results.
        
        Args:
            results: List of SpecificationResult objects
        """
        self.results = results
        self.valid_results = [r for r in results if r.methodology_valid and not np.isnan(r.coefficient)]
        
        # Convert to DataFrame for easier analysis
        self.results_df = self._results_to_dataframe()
        
    def _results_to_dataframe(self) -> pd.DataFrame:
        """Convert results to DataFrame for analysis."""
        data = []
        for r in self.valid_results:
            row = {
                'specification_id': r.specification_id,
                'coefficient': r.coefficient,
                'std_error': r.std_error,
                'p_value': r.p_value,
                'ci_lower': r.ci_lower,
                'ci_upper': r.ci_upper,
                'n_observations': r.n_observations,
                'r_squared': r.r_squared,
                'significant': r.significant,
                **r.specification  # Unpack specification details
            }
            data.append(row)
        
        return pd.DataFrame(data)
    
    def identify_fragile_specifications(self, threshold: float = 2.0) -> pd.DataFrame:
        """Find specifications that drive instability.
        
        Args:
            threshold: Z-score threshold for identifying outliers
            
        Returns:
            DataFrame with fragile specifications and their characteristics
        """
        if self.results_df.empty:
            return pd.DataFrame()
        
        # Calculate z-scores for coefficients
        coef_mean = self.results_df['coefficient'].mean()
        coef_std = self.results_df['coefficient'].std()
        self.results_df['coef_zscore'] = (self.results_df['coefficient'] - coef_mean) / coef_std
        
        # Identify outliers
        fragile = self.results_df[np.abs(self.results_df['coef_zscore']) > threshold].copy()
        
        # Analyze what makes them fragile
        fragile_analysis = []
        
        for idx, row in fragile.iterrows():
            # Find what's unique about this specification
            unique_features = []
            
            # Check each specification dimension
            for col in ['estimation_method', 'fixed_effects', 'functional_form', 
                       'outlier_method', 'exchange_rate_source', 'time_trend']:
                if col in self.results_df.columns:
                    # What % of specs have this value?
                    value = row[col]
                    pct_with_value = (self.results_df[col] == value).mean() * 100
                    
                    if pct_with_value < 20:  # Rare choice
                        unique_features.append(f"{col}={value} ({pct_with_value:.1f}%)")
            
            fragile_analysis.append({
                'specification_id': row['specification_id'],
                'coefficient': row['coefficient'],
                'z_score': row['coef_zscore'],
                'unique_features': '; '.join(unique_features) if unique_features else 'None identified',
                'estimation_method': row.get('estimation_method', 'unknown'),
                'n_observations': row['n_observations']
            })
        
        return pd.DataFrame(fragile_analysis)
    
    def calculate_vibration_ratio(self) -> Dict[str, float]:
        """Calculate Gelman & Loken (2014) vibration of effects.
        
        Returns:
            Dictionary with vibration metrics
        """
        if self.results_df.empty:
            return {'vibration_ratio': np.nan, 'message': 'No valid results'}
        
        # Only use significant results for vibration ratio
        sig_results = self.results_df[self.results_df['significant']].copy()
        
        if len(sig_results) < 2:
            return {'vibration_ratio': np.nan, 'message': 'Too few significant results'}
        
        coef_max = sig_results['coefficient'].max()
        coef_min = sig_results['coefficient'].min()
        
        vibration_ratio = coef_max / coef_min if coef_min > 0 else np.inf
        
        # Also calculate percentile-based vibration (more robust)
        coef_95 = sig_results['coefficient'].quantile(0.95)
        coef_05 = sig_results['coefficient'].quantile(0.05)
        robust_vibration = coef_95 / coef_05 if coef_05 > 0 else np.inf
        
        return {
            'vibration_ratio': vibration_ratio,
            'robust_vibration_ratio': robust_vibration,
            'coefficient_range': [coef_min, coef_max],
            'coefficient_percentiles': [coef_05, coef_95],
            'n_significant': len(sig_results),
            'pct_significant': len(sig_results) / len(self.results_df) * 100
        }
    
    def test_specification_clustering(self, n_clusters: int = 5) -> Dict:
        """Test if results cluster by methodology choices.
        
        Args:
            n_clusters: Number of clusters to identify
            
        Returns:
            Dictionary with clustering results and interpretation
        """
        if len(self.results_df) < n_clusters * 10:
            return {'clusters': None, 'message': 'Insufficient data for clustering'}
        
        # Create feature matrix from specification choices
        feature_cols = []
        feature_data = []
        
        # One-hot encode categorical variables
        for col in ['estimation_method', 'fixed_effects', 'functional_form', 
                   'clustering', 'outlier_method', 'time_trend']:
            if col in self.results_df.columns:
                dummies = pd.get_dummies(self.results_df[col], prefix=col)
                feature_data.append(dummies)
                feature_cols.extend(dummies.columns)
        
        if not feature_data:
            return {'clusters': None, 'message': 'No categorical features found'}
        
        features = pd.concat(feature_data, axis=1)
        
        # Add binary indicators
        for col in ['significant', 'positive_coefficient']:
            if col == 'positive_coefficient':
                features[col] = (self.results_df['coefficient'] > 0).astype(int)
            elif col in self.results_df.columns:
                features[col] = self.results_df[col].astype(int)
        
        # Standardize features
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # Perform clustering
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        clusters = kmeans.fit_predict(features_scaled)
        
        # Analyze clusters
        self.results_df['cluster'] = clusters
        
        cluster_analysis = []
        for i in range(n_clusters):
            cluster_data = self.results_df[self.results_df['cluster'] == i]
            
            # Coefficient statistics by cluster
            cluster_info = {
                'cluster': i,
                'n_specs': len(cluster_data),
                'mean_coefficient': cluster_data['coefficient'].mean(),
                'std_coefficient': cluster_data['coefficient'].std(),
                'pct_significant': (cluster_data['significant'].mean() * 100),
                'mean_r_squared': cluster_data['r_squared'].mean()
            }
            
            # Dominant characteristics
            if 'estimation_method' in cluster_data.columns:
                cluster_info['dominant_method'] = cluster_data['estimation_method'].mode().iloc[0]
            if 'fixed_effects' in cluster_data.columns:
                cluster_info['dominant_fe'] = cluster_data['fixed_effects'].mode().iloc[0]
            
            cluster_analysis.append(cluster_info)
        
        # Test if clusters have significantly different coefficients
        cluster_groups = [self.results_df[self.results_df['cluster'] == i]['coefficient'].values 
                         for i in range(n_clusters)]
        f_stat, p_value = stats.f_oneway(*cluster_groups)
        
        return {
            'n_clusters': n_clusters,
            'cluster_summary': pd.DataFrame(cluster_analysis),
            'coefficient_variation_test': {
                'f_statistic': f_stat,
                'p_value': p_value,
                'significant_clustering': p_value < 0.05
            },
            'interpretation': self._interpret_clustering(cluster_analysis, p_value)
        }
    
    def _interpret_clustering(self, cluster_analysis: List[Dict], p_value: float) -> str:
        """Interpret clustering results."""
        if p_value < 0.05:
            # Find which clusters are driving differences
            coef_by_cluster = [c['mean_coefficient'] for c in cluster_analysis]
            max_cluster = np.argmax(coef_by_cluster)
            min_cluster = np.argmin(coef_by_cluster)
            
            interpretation = (
                f"Significant clustering detected (p={p_value:.3f}). "
                f"Cluster {max_cluster} (n={cluster_analysis[max_cluster]['n_specs']}) "
                f"has highest coefficients (μ={cluster_analysis[max_cluster]['mean_coefficient']:.3f}), "
                f"dominated by {cluster_analysis[max_cluster].get('dominant_method', 'unknown')} estimation. "
                f"Cluster {min_cluster} has lowest (μ={cluster_analysis[min_cluster]['mean_coefficient']:.3f})."
            )
        else:
            interpretation = (
                f"No significant clustering by methodology (p={p_value:.3f}). "
                "Results are stable across specification choices."
            )
        
        return interpretation
    
    def compute_specification_weights(self, 
                                    quality_metrics: Optional[List[str]] = None) -> pd.Series:
        """Weight specifications by quality metrics.
        
        Args:
            quality_metrics: List of metrics to use for weighting
                           (default: ['n_observations', 'r_squared', 'precision'])
        
        Returns:
            Series with specification weights
        """
        if quality_metrics is None:
            quality_metrics = ['n_observations', 'r_squared', 'precision']
        
        weights_df = self.results_df[['specification_id']].copy()
        
        # Calculate individual metric scores
        if 'n_observations' in quality_metrics:
            # More observations = higher weight
            weights_df['n_obs_score'] = (
                self.results_df['n_observations'] / self.results_df['n_observations'].max()
            )
        
        if 'r_squared' in quality_metrics:
            # Higher R² = higher weight
            weights_df['r2_score'] = self.results_df['r_squared']
        
        if 'precision' in quality_metrics:
            # Lower standard error = higher precision = higher weight
            weights_df['precision_score'] = (
                1 / (1 + self.results_df['std_error'])  # Add 1 to avoid division issues
            )
            weights_df['precision_score'] /= weights_df['precision_score'].max()
        
        if 'balance' in quality_metrics:
            # Check if sample is balanced across zones
            # This would require additional data about zone representation
            # For now, use a proxy based on sample size consistency
            median_n = self.results_df['n_observations'].median()
            weights_df['balance_score'] = 1 - np.abs(
                self.results_df['n_observations'] - median_n
            ) / median_n
            weights_df['balance_score'] = weights_df['balance_score'].clip(0, 1)
        
        # Combine scores (equal weighting for now)
        score_cols = [c for c in weights_df.columns if c.endswith('_score')]
        weights_df['combined_weight'] = weights_df[score_cols].mean(axis=1)
        
        # Normalize to sum to 1
        weights_df['normalized_weight'] = (
            weights_df['combined_weight'] / weights_df['combined_weight'].sum()
        )
        
        # Calculate weighted average effect
        weighted_coefficient = (
            self.results_df['coefficient'] * weights_df['normalized_weight']
        ).sum()
        
        logger.info(f"Weighted average coefficient: {weighted_coefficient:.3f}")
        logger.info(f"Unweighted average: {self.results_df['coefficient'].mean():.3f}")
        
        return weights_df.set_index('specification_id')['normalized_weight']
    
    def analyze_by_dimension(self, dimension: str) -> pd.DataFrame:
        """Analyze results broken down by a specific dimension.
        
        Args:
            dimension: Specification dimension to analyze
                      (e.g., 'estimation_method', 'outlier_method')
        
        Returns:
            DataFrame with analysis by dimension value
        """
        if dimension not in self.results_df.columns:
            logger.warning(f"Dimension '{dimension}' not found in results")
            return pd.DataFrame()
        
        analysis = []
        
        for value in self.results_df[dimension].unique():
            subset = self.results_df[self.results_df[dimension] == value]
            
            if len(subset) > 0:
                analysis.append({
                    dimension: value,
                    'n_specs': len(subset),
                    'mean_coefficient': subset['coefficient'].mean(),
                    'std_coefficient': subset['coefficient'].std(),
                    'median_coefficient': subset['coefficient'].median(),
                    'pct_significant': subset['significant'].mean() * 100,
                    'mean_r_squared': subset['r_squared'].mean(),
                    'ci_width': (subset['ci_upper'] - subset['ci_lower']).mean()
                })
        
        return pd.DataFrame(analysis).sort_values('mean_coefficient', ascending=False)
    
    def generate_robustness_report(self) -> Dict:
        """Generate comprehensive robustness report.
        
        Returns:
            Dictionary with all robustness metrics and interpretations
        """
        report = {
            'summary_statistics': self.analyze_stability(),
            'vibration_analysis': self.calculate_vibration_ratio(),
            'fragile_specifications': self.identify_fragile_specifications(),
            'clustering_analysis': self.test_specification_clustering(),
            'dimension_breakdown': {}
        }
        
        # Analyze key dimensions
        for dimension in ['estimation_method', 'outlier_method', 'exchange_rate_source', 
                         'time_trend', 'fixed_effects']:
            if dimension in self.results_df.columns:
                report['dimension_breakdown'][dimension] = self.analyze_by_dimension(dimension)
        
        # Overall assessment
        report['overall_assessment'] = self._generate_assessment(report)
        
        return report
    
    def _generate_assessment(self, report: Dict) -> str:
        """Generate overall assessment of robustness."""
        vibration = report['vibration_analysis'].get('robust_vibration_ratio', np.inf)
        n_fragile = len(report['fragile_specifications'])
        pct_significant = report['vibration_analysis'].get('pct_significant', 0)
        
        if vibration < 2 and n_fragile < 5 and pct_significant > 90:
            assessment = "EXCELLENT: Results are highly robust across specifications."
        elif vibration < 3 and n_fragile < 10 and pct_significant > 80:
            assessment = "GOOD: Results show strong robustness with minor sensitivity."
        elif vibration < 5 and pct_significant > 70:
            assessment = "ACCEPTABLE: Results are generally robust but show some sensitivity."
        else:
            assessment = "CONCERNING: Results show substantial sensitivity to specifications."
        
        assessment += f"\n- Vibration ratio: {vibration:.2f}"
        assessment += f"\n- Fragile specifications: {n_fragile}"
        assessment += f"\n- Significant results: {pct_significant:.1f}%"
        
        return assessment
    
    def analyze_stability(self) -> Dict[str, Any]:
        """Analyze stability of results across specifications.
        
        Returns:
            Dictionary with stability metrics
        """
        if self.results_df.empty:
            return {'n_valid': 0, 'stability_metrics': None}
        
        coefficients = self.results_df['coefficient'].values
        significant = self.results_df['significant'].values
        
        return {
            'n_valid': len(self.results_df),
            'n_total': len(self.results),
            'pct_valid': len(self.results_df) / len(self.results) * 100,
            'coefficient_mean': np.mean(coefficients),
            'coefficient_std': np.std(coefficients),
            'coefficient_cv': np.std(coefficients) / np.mean(coefficients) if np.mean(coefficients) != 0 else np.inf,
            'coefficient_min': np.min(coefficients),
            'coefficient_max': np.max(coefficients),
            'coefficient_median': np.median(coefficients),
            'coefficient_iqr': np.percentile(coefficients, 75) - np.percentile(coefficients, 25),
            'pct_significant': np.mean(significant) * 100,
            'pct_positive': np.mean(coefficients > 0) * 100,
            'median_r_squared': np.median(self.results_df['r_squared'])
        }