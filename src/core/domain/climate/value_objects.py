"""Climate data value objects."""

from enum import Enum
from dataclasses import dataclass
from typing import Optional


class ClimateDataSource(Enum):
    """Sources of climate data."""
    CHIRPS = "CHIRPS"  # Rainfall
    MODIS = "MODIS"    # NDVI, Temperature
    ERA5 = "ERA5"      # Reanalysis data
    GPM = "GPM"        # Global Precipitation Measurement
    STATION = "Station" # Ground station data


class DroughtSeverity(Enum):
    """Drought severity classifications."""
    NONE = "None"
    MILD = "Mild"
    MODERATE = "Moderate"
    SEVERE = "Severe"
    EXTREME = "Extreme"
    
    @classmethod
    def from_spi(cls, spi_value: float) -> 'DroughtSeverity':
        """
        Classify drought severity from Standardized Precipitation Index.
        
        SPI ranges:
        - > 0: No drought
        - 0 to -0.99: Mild drought
        - -1.0 to -1.49: Moderate drought
        - -1.5 to -1.99: Severe drought
        - < -2.0: Extreme drought
        """
        if spi_value > 0:
            return cls.NONE
        elif spi_value > -1.0:
            return cls.MILD
        elif spi_value > -1.5:
            return cls.MODERATE
        elif spi_value > -2.0:
            return cls.SEVERE
        else:
            return cls.EXTREME


@dataclass
class RainfallAnomaly:
    """Rainfall anomaly information."""
    current_rainfall_mm: float
    historical_mean_mm: float
    anomaly_percent: float
    
    @property
    def is_below_normal(self) -> bool:
        """Check if rainfall is below normal."""
        return self.anomaly_percent < -20  # 20% below normal
    
    @property
    def is_above_normal(self) -> bool:
        """Check if rainfall is above normal."""
        return self.anomaly_percent > 20  # 20% above normal
    
    @classmethod
    def calculate(cls, current: float, historical_mean: float) -> 'RainfallAnomaly':
        """Calculate rainfall anomaly."""
        if historical_mean == 0:
            anomaly_percent = 0 if current == 0 else 100
        else:
            anomaly_percent = ((current - historical_mean) / historical_mean) * 100
        
        return cls(
            current_rainfall_mm=current,
            historical_mean_mm=historical_mean,
            anomaly_percent=anomaly_percent
        )


@dataclass
class SeasonalCalendar:
    """Yemen agricultural seasonal calendar."""
    
    @staticmethod
    def get_season(month: int) -> str:
        """Get agricultural season for a given month."""
        if month in [3, 4, 5]:
            return "First Rainy Season"
        elif month in [7, 8, 9]:
            return "Second Rainy Season"
        elif month in [10, 11]:
            return "Main Harvest"
        elif month in [4, 5]:
            return "First Harvest"
        else:
            return "Dry Season"
    
    @staticmethod
    def is_planting_season(month: int) -> bool:
        """Check if month is planting season."""
        return month in [3, 4, 7, 8]
    
    @staticmethod
    def is_harvest_season(month: int) -> bool:
        """Check if month is harvest season."""
        return month in [4, 5, 10, 11]