"""Climate data domain entities."""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional

from src.core.domain.market.value_objects import Coordinates
from src.core.domain.shared.value_objects import TemporalKey
from src.core.domain.shared.entities import Entity


@dataclass
class ClimateObservation(Entity):
    """
    Represents climate observation data.
    
    Attributes:
        observation_date: Date of observation
        location: Geographic coordinates
        rainfall_mm: Rainfall in millimeters
        temperature_celsius: Temperature in Celsius
        ndvi: Normalized Difference Vegetation Index
        drought_index: Standardized Precipitation Index or similar
        data_source: Source of climate data (e.g., CHIRPS, MODIS)
    """
    observation_date: datetime
    location: Coordinates
    rainfall_mm: Optional[float] = None
    temperature_celsius: Optional[float] = None
    ndvi: Optional[float] = None
    drought_index: Optional[float] = None
    data_source: str = "CHIRPS"
    
    def __post_init__(self):
        """Validate climate data."""
        if self.rainfall_mm is not None and self.rainfall_mm < 0:
            raise ValueError("Rainfall cannot be negative")
        if self.ndvi is not None and not (-1 <= self.ndvi <= 1):
            raise ValueError("NDVI must be between -1 and 1")


@dataclass
class ClimateMetrics:
    """
    Aggregated climate metrics for a market-month.
    
    Attributes:
        temporal_key: Month/year identifier
        market_id: Market identifier
        avg_rainfall_mm: Average monthly rainfall
        total_rainfall_mm: Total monthly rainfall
        avg_temperature: Average temperature
        min_temperature: Minimum temperature
        max_temperature: Maximum temperature
        avg_ndvi: Average NDVI
        drought_severity: Drought severity classification
        n_observations: Number of observations
    """
    temporal_key: TemporalKey
    market_id: str
    avg_rainfall_mm: float
    total_rainfall_mm: float
    avg_temperature: Optional[float] = None
    min_temperature: Optional[float] = None
    max_temperature: Optional[float] = None
    avg_ndvi: Optional[float] = None
    drought_severity: Optional[str] = None
    n_observations: int = 0
    
    @property
    def is_drought_affected(self) -> bool:
        """Check if area is experiencing drought."""
        if self.drought_severity:
            return self.drought_severity in ["Moderate", "Severe", "Extreme"]
        # Simple heuristic based on rainfall
        return self.avg_rainfall_mm < 10.0  # mm per month
    
    @property
    def vegetation_stress(self) -> Optional[str]:
        """Classify vegetation stress based on NDVI."""
        if self.avg_ndvi is None:
            return None
        
        if self.avg_ndvi < 0.2:
            return "Severe"
        elif self.avg_ndvi < 0.3:
            return "Moderate"
        elif self.avg_ndvi < 0.4:
            return "Mild"
        else:
            return "None"