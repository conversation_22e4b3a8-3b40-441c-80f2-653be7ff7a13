"""Infrastructure domain entities."""

from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional

from src.core.domain.market.value_objects import Coordinates
from src.core.domain.shared.entities import Entity


@dataclass
class Road(Entity):
    """
    Represents a road segment.
    
    Attributes:
        osm_id: OpenStreetMap ID
        name: Road name
        road_type: Type of road (primary, secondary, etc.)
        surface: Surface type (paved, unpaved, etc.)
        condition: Road condition
        coordinates: List of coordinates defining the road
        length_km: Length in kilometers
        connects: List of location names connected
        last_updated: Last update date
    """
    osm_id: str
    name: Optional[str]
    road_type: str
    surface: Optional[str]
    condition: Optional[str]
    coordinates: List[Coordinates]
    length_km: float
    connects: List[str]
    last_updated: datetime
    
    @property
    def is_paved(self) -> bool:
        """Check if road is paved."""
        return self.surface in ["paved", "asphalt", "concrete"]
    
    @property
    def is_major_road(self) -> bool:
        """Check if this is a major road."""
        return self.road_type in ["primary", "trunk", "motorway"]


@dataclass
class Port(Entity):
    """
    Represents a port facility.
    
    Attributes:
        port_id: Unique port identifier
        name: Port name
        location: Geographic coordinates
        port_type: Type (seaport, dry port)
        capacity: Handling capacity
        operational_status: Current operational status
        controlled_by: Controlling authority
    """
    port_id: str
    name: str
    location: Coordinates
    port_type: str
    capacity: Optional[float] = None  # TEUs or tons per year
    operational_status: str = "operational"
    controlled_by: Optional[str] = None
    
    @property
    def is_operational(self) -> bool:
        """Check if port is operational."""
        return self.operational_status.lower() in ["operational", "partially operational"]


@dataclass
class MarketInfrastructure(Entity):
    """
    Infrastructure characteristics of a market.
    
    Attributes:
        market_id: Market identifier
        has_storage: Has storage facilities
        storage_capacity: Storage capacity in MT
        has_electricity: Has reliable electricity
        has_water: Has water access
        road_access: Type of road access
        distance_to_port_km: Distance to nearest port
        distance_to_border_km: Distance to nearest border
        mobile_coverage: Mobile network coverage level
        banking_facilities: Has banking facilities
    """
    market_id: str
    has_storage: bool
    storage_capacity: Optional[float] = None
    has_electricity: bool = False
    has_water: bool = False
    road_access: str = "unpaved"
    distance_to_port_km: Optional[float] = None
    distance_to_border_km: Optional[float] = None
    mobile_coverage: str = "none"  # none, 2G, 3G, 4G
    banking_facilities: bool = False
    
    @property
    def infrastructure_score(self) -> float:
        """
        Calculate infrastructure quality score (0-1).
        
        Higher scores indicate better infrastructure.
        """
        score = 0.0
        
        # Storage (20%)
        if self.has_storage:
            score += 0.1
            if self.storage_capacity and self.storage_capacity > 100:
                score += 0.1
        
        # Utilities (20%)
        if self.has_electricity:
            score += 0.1
        if self.has_water:
            score += 0.1
        
        # Road access (20%)
        road_scores = {
            "paved": 0.2,
            "gravel": 0.1,
            "unpaved": 0.05,
            "none": 0.0
        }
        score += road_scores.get(self.road_access, 0.0)
        
        # Connectivity (20%)
        if self.distance_to_port_km and self.distance_to_port_km < 100:
            score += 0.1
        if self.distance_to_border_km and self.distance_to_border_km < 50:
            score += 0.1
        
        # Communications & Banking (20%)
        mobile_scores = {
            "4G": 0.1,
            "3G": 0.07,
            "2G": 0.03,
            "none": 0.0
        }
        score += mobile_scores.get(self.mobile_coverage, 0.0)
        
        if self.banking_facilities:
            score += 0.1
        
        return min(score, 1.0)