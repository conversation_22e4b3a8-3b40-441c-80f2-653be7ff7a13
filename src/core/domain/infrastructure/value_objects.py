"""Infrastructure value objects."""

from enum import Enum
from dataclasses import dataclass
from typing import Optional


class RoadType(Enum):
    """Road classification types."""
    MOTORWAY = "motorway"
    TRUNK = "trunk"
    PRIMARY = "primary"
    SECONDARY = "secondary"
    TERTIARY = "tertiary"
    UNCLASSIFIED = "unclassified"
    RESIDENTIAL = "residential"
    SERVICE = "service"
    TRACK = "track"
    
    @property
    def is_major(self) -> bool:
        """Check if this is a major road type."""
        return self in [self.MOTORWAY, self.TRUNK, self.PRIMARY]


class RoadSurface(Enum):
    """Road surface types."""
    PAVED = "paved"
    ASPHALT = "asphalt"
    CONCRETE = "concrete"
    GRAVEL = "gravel"
    DIRT = "dirt"
    SAND = "sand"
    UNPAVED = "unpaved"
    UNKNOWN = "unknown"
    
    @property
    def is_all_weather(self) -> bool:
        """Check if road is usable in all weather conditions."""
        return self in [self.PAVED, self.ASPHALT, self.CONCRETE]


class RoadCondition(Enum):
    """Road condition assessment."""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    IMPASSABLE = "impassable"
    DAMAGED = "damaged"
    UNDER_REPAIR = "under_repair"
    
    @property
    def is_passable(self) -> bool:
        """Check if road is passable."""
        return self not in [self.IMPASSABLE]


class PortType(Enum):
    """Types of ports."""
    SEAPORT = "seaport"
    DRY_PORT = "dry_port"
    RIVER_PORT = "river_port"
    AIRPORT = "airport"
    BORDER_CROSSING = "border_crossing"


@dataclass
class NetworkConnectivity:
    """Mobile network connectivity information."""
    coverage_2g: bool = False
    coverage_3g: bool = False
    coverage_4g: bool = False
    provider: Optional[str] = None
    signal_strength: Optional[str] = None  # weak, moderate, strong
    
    @property
    def best_available(self) -> str:
        """Get best available network type."""
        if self.coverage_4g:
            return "4G"
        elif self.coverage_3g:
            return "3G"
        elif self.coverage_2g:
            return "2G"
        else:
            return "none"
    
    @property
    def has_data_capability(self) -> bool:
        """Check if network supports data."""
        return self.coverage_3g or self.coverage_4g


@dataclass
class AccessibilityScore:
    """Market accessibility scoring."""
    road_quality: float  # 0-1
    distance_factor: float  # 0-1
    infrastructure: float  # 0-1
    security: float  # 0-1
    
    @property
    def overall_score(self) -> float:
        """Calculate overall accessibility score."""
        # Weighted average
        weights = {
            "road": 0.3,
            "distance": 0.3,
            "infrastructure": 0.2,
            "security": 0.2
        }
        
        return (
            self.road_quality * weights["road"] +
            self.distance_factor * weights["distance"] +
            self.infrastructure * weights["infrastructure"] +
            self.security * weights["security"]
        )
    
    @property
    def classification(self) -> str:
        """Classify accessibility level."""
        score = self.overall_score
        
        if score >= 0.8:
            return "Highly Accessible"
        elif score >= 0.6:
            return "Accessible"
        elif score >= 0.4:
            return "Moderately Accessible"
        elif score >= 0.2:
            return "Poorly Accessible"
        else:
            return "Isolated"