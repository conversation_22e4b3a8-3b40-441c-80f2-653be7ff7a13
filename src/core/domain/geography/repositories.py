"""Geographic repository interfaces."""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict
from datetime import datetime

from .entities import Governorate, District


class GeographyRepository(ABC):
    """Repository interface for geographic data."""
    
    @abstractmethod
    async def get_governorate(self, code: str) -> Optional[Governorate]:
        """Get governorate by code."""
        pass
    
    @abstractmethod
    async def get_all_governorates(self) -> List[Governorate]:
        """Get all governorates."""
        pass
    
    @abstractmethod
    async def save_governorate(self, governorate: Governorate) -> None:
        """Save or update governorate."""
        pass
    
    @abstractmethod
    async def get_district(self, code: str) -> Optional[District]:
        """Get district by code."""
        pass
    
    @abstractmethod
    async def get_districts_by_governorate(self, governorate_code: str) -> List[District]:
        """Get all districts in a governorate."""
        pass
    
    @abstractmethod
    async def save_district(self, district: District) -> None:
        """Save or update district."""
        pass
    
    @abstractmethod
    async def save_administrative_unit(self, unit: Dict) -> None:
        """Save administrative unit data."""
        pass