"""Aid distribution value objects."""

from enum import Enum
from dataclasses import dataclass
from typing import Optional


class HumanitarianCluster(Enum):
    """Humanitarian cluster types."""
    FOOD_SECURITY = "Food Security"
    HEALTH = "Health"
    WASH = "Water, Sanitation & Hygiene"
    SHELTER_NFI = "Shelter & Non-Food Items"
    NUTRITION = "Nutrition"
    PROTECTION = "Protection"
    EDUCATION = "Education"
    CAMP_MANAGEMENT = "Camp Coordination & Management"
    LOGISTICS = "Logistics"
    EMERGENCY_TELECOM = "Emergency Telecommunications"
    MULTI_CLUSTER = "Multi-Cluster"


class DistributionModality(Enum):
    """Aid distribution modalities."""
    CASH = "cash"
    IN_KIND = "in-kind"
    VOUCHER = "voucher"
    MIXED = "mixed"


class ActivityType(Enum):
    """Types of aid activities."""
    EMERGENCY_RESPONSE = "Emergency Response"
    REGULAR_PROGRAMME = "Regular Programme"
    RESILIENCE_BUILDING = "Resilience Building"
    CAPACITY_BUILDING = "Capacity Building"
    INFRASTRUCTURE = "Infrastructure Support"


@dataclass
class OrganizationType:
    """Organization type information."""
    category: str  # UN, INGO, NNGO, Government
    is_local: bool
    cluster_lead: Optional[str] = None
    
    @classmethod
    def from_string(cls, org_type: str) -> 'OrganizationType':
        """Create from string representation."""
        category = org_type.upper()
        is_local = category in ["NNGO", "GOVERNMENT"]
        
        return cls(
            category=category,
            is_local=is_local
        )