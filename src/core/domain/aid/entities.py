"""Aid distribution domain entities."""

from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from typing import List, Optional

from src.core.domain.market.value_objects import Coordinates
from src.core.domain.shared.value_objects import TemporalKey
from src.core.domain.shared.entities import Entity


@dataclass
class AidDistribution(Entity):
    """
    Represents an aid distribution event.
    
    Attributes:
        distribution_date: Date of distribution
        location: Geographic coordinates
        organization: Implementing organization
        cluster: Humanitarian cluster (Food Security, Health, etc.)
        beneficiaries: Number of beneficiaries
        modality: Distribution modality (cash, in-kind, voucher)
        amount: Amount distributed (USD for cash, quantity for in-kind)
        commodity: Commodity type for in-kind distributions
        activity_type: Type of activity (emergency, regular, etc.)
    """
    distribution_date: datetime
    location: Coordinates
    organization: str
    cluster: str
    beneficiaries: int
    modality: str
    amount: Decimal
    commodity: Optional[str] = None
    activity_type: Optional[str] = None
    
    def __post_init__(self):
        """Validate aid distribution data."""
        if self.beneficiaries < 0:
            raise ValueError("Beneficiaries cannot be negative")
        if self.amount < 0:
            raise ValueError("Amount cannot be negative")
        if self.modality not in ["cash", "in-kind", "voucher", "mixed"]:
            raise ValueError(f"Invalid modality: {self.modality}")


@dataclass
class AidMetrics:
    """
    Aggregated aid metrics for a market-month.
    
    Attributes:
        temporal_key: Month/year identifier
        market_id: Market identifier
        total_beneficiaries: Total beneficiaries reached
        cash_distributed_usd: Total cash distributed in USD
        in_kind_value_usd: Estimated value of in-kind aid
        n_distributions: Number of distribution events
        active_organizations: List of active organizations
        main_modality: Primary distribution modality
    """
    temporal_key: TemporalKey
    market_id: str
    total_beneficiaries: int
    cash_distributed_usd: Decimal
    in_kind_value_usd: Decimal
    n_distributions: int
    active_organizations: List[str]
    main_modality: str
    
    @property
    def total_aid_value_usd(self) -> Decimal:
        """Calculate total aid value."""
        return self.cash_distributed_usd + self.in_kind_value_usd
    
    @property
    def aid_per_beneficiary(self) -> Decimal:
        """Calculate average aid per beneficiary."""
        if self.total_beneficiaries == 0:
            return Decimal("0")
        return self.total_aid_value_usd / Decimal(self.total_beneficiaries)