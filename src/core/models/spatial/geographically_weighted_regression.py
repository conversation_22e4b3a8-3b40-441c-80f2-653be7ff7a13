"""
Geographically Weighted Regression (GWR) for Local Spatial Effects.

Implements GWR to analyze spatially varying relationships in market integration,
allowing coefficients to vary smoothly across geographic space.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Callable
from dataclasses import dataclass
from enum import Enum
import logging
from scipy.spatial.distance import cdist
from scipy.linalg import inv, solve
from scipy.optimize import minimize_scalar
from sklearn.metrics.pairwise import haversine_distances

logger = logging.getLogger(__name__)


class KernelType(Enum):
    """Types of spatial kernels for GWR."""
    GAUSSIAN = "gaussian"
    EXPONENTIAL = "exponential"
    BISQUARE = "bisquare"
    TRICUBE = "tricube"


class BandwidthSelection(Enum):
    """Methods for bandwidth selection."""
    CV = "cross_validation"
    AIC = "aic"
    BIC = "bic"
    FIXED_DISTANCE = "fixed_distance"
    ADAPTIVE_NEIGHBORS = "adaptive_neighbors"


@dataclass
class GWRResults:
    """Results from Geographically Weighted Regression."""
    
    # Model specification
    kernel_type: KernelType
    bandwidth_method: BandwidthSelection
    optimal_bandwidth: float
    
    # Local coefficients and diagnostics
    local_coefficients: pd.DataFrame
    local_standard_errors: pd.DataFrame
    local_t_statistics: pd.DataFrame
    local_p_values: pd.DataFrame
    local_r_squared: pd.Series
    
    # Global model comparison
    global_r_squared: float
    local_r_squared_improvement: float
    
    # Model fit and selection
    aic: float
    aicc: float  # Corrected AIC for small samples
    bic: float
    log_likelihood: float
    effective_degrees_freedom: float
    
    # Spatial diagnostics
    moran_i_residuals: float
    moran_i_p_value: float
    spatial_autocorr_local_coeffs: Dict[str, float]
    
    # Bandwidth optimization
    bandwidth_search_results: Optional[pd.DataFrame] = None
    
    # Local model diagnostics
    local_collinearity: pd.Series = None
    local_heteroskedasticity: pd.Series = None


class GeographicallyWeightedRegression:
    """
    Geographically Weighted Regression for Spatially Varying Relationships.
    
    Estimates local regression models at each location using spatially weighted
    observations, allowing coefficients to vary smoothly across space.
    
    Model: Y_i = β₀(u_i,v_i) + Σ_k β_k(u_i,v_i) X_ki + ε_i
    
    Where β_k(u_i,v_i) are location-specific coefficients at coordinates (u_i,v_i).
    """
    
    def __init__(self,
                 data: pd.DataFrame,
                 coordinates: pd.DataFrame,
                 outcome_var: str,
                 exog_vars: List[str],
                 market_id_var: str = 'market_id'):
        """
        Initialize GWR model.
        
        Args:
            data: Market data
            coordinates: Market coordinates (latitude, longitude)
            outcome_var: Dependent variable
            exog_vars: Explanatory variables
            market_id_var: Market identifier
        """
        self.data = data.copy()
        self.coordinates = coordinates.copy()
        self.outcome_var = outcome_var
        self.exog_vars = exog_vars
        self.market_id_var = market_id_var
        
        self.results = None
        
        # Validate and prepare data
        self._validate_inputs()
        self._prepare_gwr_data()
        
    def _validate_inputs(self):
        """Validate inputs for GWR estimation."""
        # Check data columns
        required_data_cols = [self.outcome_var, self.market_id_var] + self.exog_vars
        missing_data_cols = [col for col in required_data_cols if col not in self.data.columns]
        if missing_data_cols:
            raise ValueError(f"Missing columns in data: {missing_data_cols}")
            
        # Check coordinates
        required_coord_cols = [self.market_id_var, 'latitude', 'longitude']
        missing_coord_cols = [col for col in required_coord_cols if col not in self.coordinates.columns]
        if missing_coord_cols:
            raise ValueError(f"Missing columns in coordinates: {missing_coord_cols}")
            
        # Check for sufficient variation
        if len(self.data) < len(self.exog_vars) * 3:
            raise ValueError(f"Insufficient observations for GWR: {len(self.data)} obs, {len(self.exog_vars)} vars")
            
    def _prepare_gwr_data(self):
        """Prepare data for GWR estimation."""
        # Merge data with coordinates
        self.merged_data = self.data.merge(
            self.coordinates[[self.market_id_var, 'latitude', 'longitude']],
            on=self.market_id_var,
            how='inner'
        )
        
        # Remove missing values
        self.merged_data = self.merged_data.dropna(
            subset=[self.outcome_var] + self.exog_vars + ['latitude', 'longitude']
        )
        
        # Extract coordinates matrix
        self.coords = self.merged_data[['latitude', 'longitude']].values
        
        # Convert to radians for haversine distance
        self.coords_rad = np.radians(self.coords)
        
        # Calculate distance matrix
        self.distance_matrix = haversine_distances(self.coords_rad) * 6371  # Earth radius in km
        
        logger.info(f"GWR data prepared: {len(self.merged_data)} observations, "
                   f"{len(self.exog_vars)} variables")
                   
    def fit(self,
            kernel_type: KernelType = KernelType.GAUSSIAN,
            bandwidth_method: BandwidthSelection = BandwidthSelection.CV,
            bandwidth_range: Optional[Tuple[float, float]] = None) -> GWRResults:
        """
        Estimate GWR model.
        
        Args:
            kernel_type: Type of spatial kernel
            bandwidth_method: Method for bandwidth selection
            bandwidth_range: Range for bandwidth search (min, max) in km
            
        Returns:
            GWRResults object
        """
        logger.info(f"Estimating GWR with {kernel_type.value} kernel and {bandwidth_method.value} bandwidth")
        
        # Set default bandwidth range if not provided
        if bandwidth_range is None:
            max_distance = np.max(self.distance_matrix)
            bandwidth_range = (max_distance * 0.05, max_distance * 0.5)
            
        # Select optimal bandwidth
        optimal_bandwidth = self._select_bandwidth(kernel_type, bandwidth_method, bandwidth_range)
        
        # Estimate local models
        gwr_results = self._estimate_local_models(kernel_type, optimal_bandwidth)
        
        # Calculate model diagnostics
        gwr_results = self._calculate_model_diagnostics(gwr_results)
        
        # Run spatial diagnostics
        gwr_results = self._run_spatial_diagnostics(gwr_results)
        
        self.results = gwr_results
        return gwr_results
        
    def _select_bandwidth(self,
                         kernel_type: KernelType,
                         bandwidth_method: BandwidthSelection,
                         bandwidth_range: Tuple[float, float]) -> float:
        """Select optimal bandwidth using specified method."""
        
        logger.info(f"Selecting bandwidth using {bandwidth_method.value} method")
        
        if bandwidth_method == BandwidthSelection.FIXED_DISTANCE:
            # Use middle of range as default
            return (bandwidth_range[0] + bandwidth_range[1]) / 2
            
        elif bandwidth_method == BandwidthSelection.ADAPTIVE_NEIGHBORS:
            # Use fraction of observations as neighbors
            n_neighbors = max(len(self.exog_vars) * 2, int(len(self.merged_data) * 0.1))
            return self._neighbors_to_bandwidth(n_neighbors)
            
        elif bandwidth_method in [BandwidthSelection.CV, BandwidthSelection.AIC, BandwidthSelection.BIC]:
            # Optimize bandwidth using specified criterion
            return self._optimize_bandwidth(kernel_type, bandwidth_method, bandwidth_range)
            
        else:
            raise NotImplementedError(f"Bandwidth method {bandwidth_method} not implemented")
            
    def _neighbors_to_bandwidth(self, n_neighbors: int) -> float:
        """Convert number of neighbors to adaptive bandwidth."""
        # For each location, find distance to k-th nearest neighbor
        adaptive_bandwidths = []
        
        for i in range(len(self.merged_data)):
            distances = self.distance_matrix[i, :]
            kth_distance = np.partition(distances, n_neighbors)[n_neighbors]
            adaptive_bandwidths.append(kth_distance)
            
        # Return median adaptive bandwidth
        return np.median(adaptive_bandwidths)
        
    def _optimize_bandwidth(self,
                           kernel_type: KernelType,
                           bandwidth_method: BandwidthSelection,
                           bandwidth_range: Tuple[float, float]) -> float:
        """Optimize bandwidth using specified criterion."""
        
        def criterion_function(bandwidth):
            try:
                if bandwidth_method == BandwidthSelection.CV:
                    return self._cross_validation_score(kernel_type, bandwidth)
                elif bandwidth_method == BandwidthSelection.AIC:
                    return self._calculate_aic(kernel_type, bandwidth)
                elif bandwidth_method == BandwidthSelection.BIC:
                    return self._calculate_bic(kernel_type, bandwidth)
            except Exception as e:
                logger.debug(f"Bandwidth {bandwidth} failed: {e}")
                return np.inf
                
        # Golden section search for optimal bandwidth
        result = minimize_scalar(
            criterion_function,
            bounds=bandwidth_range,
            method='bounded'
        )
        
        if result.success:
            logger.info(f"Optimal bandwidth: {result.x:.2f} km")
            return result.x
        else:
            logger.warning("Bandwidth optimization failed, using middle of range")
            return (bandwidth_range[0] + bandwidth_range[1]) / 2
            
    def _cross_validation_score(self, kernel_type: KernelType, bandwidth: float) -> float:
        """Calculate leave-one-out cross-validation score."""
        
        cv_errors = []
        
        for i in range(len(self.merged_data)):
            # Leave-one-out: exclude observation i
            weights = self._calculate_weights(kernel_type, bandwidth, i)
            weights[i] = 0  # Exclude observation i
            
            if np.sum(weights) == 0:
                cv_errors.append(np.inf)
                continue
                
            # Fit local model without observation i
            try:
                local_coeffs = self._fit_local_model(i, weights)
                
                # Predict observation i
                X_i = np.concatenate([[1], self.merged_data.iloc[i][self.exog_vars].values])
                y_pred = X_i @ local_coeffs
                y_actual = self.merged_data.iloc[i][self.outcome_var]
                
                cv_errors.append((y_actual - y_pred) ** 2)
                
            except Exception:
                cv_errors.append(np.inf)
                
        return np.mean(cv_errors)
        
    def _calculate_aic(self, kernel_type: KernelType, bandwidth: float) -> float:
        """Calculate AIC for given bandwidth."""
        try:
            # Estimate model with this bandwidth
            temp_results = self._estimate_local_models(kernel_type, bandwidth)
            return temp_results.aic
        except Exception:
            return np.inf
            
    def _calculate_bic(self, kernel_type: KernelType, bandwidth: float) -> float:
        """Calculate BIC for given bandwidth."""
        try:
            # Estimate model with this bandwidth
            temp_results = self._estimate_local_models(kernel_type, bandwidth)
            return temp_results.bic
        except Exception:
            return np.inf
            
    def _estimate_local_models(self, kernel_type: KernelType, bandwidth: float) -> GWRResults:
        """Estimate local regression models at each location."""
        
        n_locations = len(self.merged_data)
        n_vars = len(self.exog_vars) + 1  # +1 for intercept
        
        # Initialize result arrays
        local_coefficients = np.zeros((n_locations, n_vars))
        local_standard_errors = np.zeros((n_locations, n_vars))
        local_t_statistics = np.zeros((n_locations, n_vars))
        local_p_values = np.zeros((n_locations, n_vars))
        local_r_squared = np.zeros(n_locations)
        local_collinearity = np.zeros(n_locations)
        
        logger.info(f"Estimating {n_locations} local models")
        
        for i in range(n_locations):
            # Calculate weights for location i
            weights = self._calculate_weights(kernel_type, bandwidth, i)
            
            # Fit local weighted least squares
            try:
                coeffs, se, t_stats, p_vals, r_sq, condition_number = self._fit_local_model_full(i, weights)
                
                local_coefficients[i, :] = coeffs
                local_standard_errors[i, :] = se
                local_t_statistics[i, :] = t_stats
                local_p_values[i, :] = p_vals
                local_r_squared[i] = r_sq
                local_collinearity[i] = condition_number
                
            except Exception as e:
                logger.debug(f"Failed to estimate model at location {i}: {e}")
                # Fill with NaN for failed locations
                local_coefficients[i, :] = np.nan
                local_standard_errors[i, :] = np.nan
                local_t_statistics[i, :] = np.nan
                local_p_values[i, :] = np.nan
                local_r_squared[i] = np.nan
                local_collinearity[i] = np.nan
                
        # Create coefficient dataframes
        var_names = ['intercept'] + self.exog_vars
        
        coeff_df = pd.DataFrame(
            local_coefficients,
            columns=var_names,
            index=self.merged_data.index
        )
        
        se_df = pd.DataFrame(
            local_standard_errors,
            columns=var_names,
            index=self.merged_data.index
        )
        
        t_df = pd.DataFrame(
            local_t_statistics,
            columns=var_names,
            index=self.merged_data.index
        )
        
        p_df = pd.DataFrame(
            local_p_values,
            columns=var_names,
            index=self.merged_data.index
        )
        
        r_sq_series = pd.Series(
            local_r_squared,
            index=self.merged_data.index
        )
        
        collin_series = pd.Series(
            local_collinearity,
            index=self.merged_data.index
        )
        
        # Calculate global model for comparison
        global_r_squared = self._calculate_global_r_squared()
        
        # Calculate model fit statistics
        aic, aicc, bic, log_likelihood, edf = self._calculate_global_fit_statistics(
            local_coefficients, bandwidth, kernel_type
        )
        
        results = GWRResults(
            kernel_type=kernel_type,
            bandwidth_method=BandwidthSelection.CV,  # Will be updated
            optimal_bandwidth=bandwidth,
            local_coefficients=coeff_df,
            local_standard_errors=se_df,
            local_t_statistics=t_df,
            local_p_values=p_df,
            local_r_squared=r_sq_series,
            global_r_squared=global_r_squared,
            local_r_squared_improvement=np.mean(local_r_squared) - global_r_squared,
            aic=aic,
            aicc=aicc,
            bic=bic,
            log_likelihood=log_likelihood,
            effective_degrees_freedom=edf,
            moran_i_residuals=0.0,  # Will be calculated later
            moran_i_p_value=1.0,
            spatial_autocorr_local_coeffs={},
            local_collinearity=collin_series
        )
        
        return results
        
    def _calculate_weights(self, kernel_type: KernelType, bandwidth: float, location_idx: int) -> np.ndarray:
        """Calculate spatial weights for given location."""
        
        # Get distances from location_idx to all other locations
        distances = self.distance_matrix[location_idx, :]
        
        # Calculate weights based on kernel type
        if kernel_type == KernelType.GAUSSIAN:
            weights = np.exp(-(distances ** 2) / (2 * bandwidth ** 2))
            
        elif kernel_type == KernelType.EXPONENTIAL:
            weights = np.exp(-distances / bandwidth)
            
        elif kernel_type == KernelType.BISQUARE:
            weights = np.where(
                distances <= bandwidth,
                (1 - (distances / bandwidth) ** 2) ** 2,
                0
            )
            
        elif kernel_type == KernelType.TRICUBE:
            weights = np.where(
                distances <= bandwidth,
                (1 - (distances / bandwidth) ** 3) ** 3,
                0
            )
            
        else:
            raise ValueError(f"Unknown kernel type: {kernel_type}")
            
        # Normalize weights to sum to 1
        if np.sum(weights) > 0:
            weights = weights / np.sum(weights)
        else:
            weights = np.ones(len(weights)) / len(weights)
            
        return weights
        
    def _fit_local_model(self, location_idx: int, weights: np.ndarray) -> np.ndarray:
        """Fit local weighted least squares model."""
        
        # Prepare design matrix
        X = np.column_stack([
            np.ones(len(self.merged_data)),  # Intercept
            self.merged_data[self.exog_vars].values
        ])
        
        y = self.merged_data[self.outcome_var].values
        
        # Apply weights
        W = np.diag(weights)
        
        # Weighted least squares: β = (X'WX)⁻¹X'Wy
        XWX = X.T @ W @ X
        XWy = X.T @ W @ y
        
        # Check for singularity
        if np.linalg.cond(XWX) > 1e12:
            raise ValueError("Singular matrix in local regression")
            
        coefficients = solve(XWX, XWy)
        
        return coefficients
        
    def _fit_local_model_full(self, location_idx: int, weights: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, float, float]:
        """Fit local model with full diagnostics."""
        
        # Prepare design matrix
        X = np.column_stack([
            np.ones(len(self.merged_data)),
            self.merged_data[self.exog_vars].values
        ])
        
        y = self.merged_data[self.outcome_var].values
        
        # Apply weights
        W = np.diag(weights)
        
        # Weighted least squares
        XWX = X.T @ W @ X
        XWy = X.T @ W @ y
        
        # Check condition number
        condition_number = np.linalg.cond(XWX)
        
        if condition_number > 1e12:
            raise ValueError("Ill-conditioned local regression")
            
        coefficients = solve(XWX, XWy)
        
        # Calculate residuals and standard errors
        fitted_values = X @ coefficients
        residuals = y - fitted_values
        
        # Weighted residual sum of squares
        wrss = np.sum(weights * residuals ** 2)
        
        # Effective sample size
        effective_n = np.sum(weights > 1e-8)
        
        if effective_n <= len(coefficients):
            raise ValueError("Insufficient effective sample size")
            
        # Estimate variance
        sigma2 = wrss / (effective_n - len(coefficients))
        
        # Variance-covariance matrix
        var_covar = sigma2 * inv(XWX)
        standard_errors = np.sqrt(np.diag(var_covar))
        
        # t-statistics and p-values
        t_statistics = coefficients / standard_errors
        from scipy.stats import t as t_dist
        p_values = 2 * (1 - t_dist.cdf(np.abs(t_statistics), effective_n - len(coefficients)))
        
        # Local R-squared
        y_weighted_mean = np.sum(weights * y) / np.sum(weights)
        tss = np.sum(weights * (y - y_weighted_mean) ** 2)
        r_squared = 1 - wrss / tss if tss > 0 else 0
        
        return coefficients, standard_errors, t_statistics, p_values, r_squared, condition_number
        
    def _calculate_global_r_squared(self) -> float:
        """Calculate R-squared for global (non-spatial) model."""
        
        # Fit global OLS model
        X = np.column_stack([
            np.ones(len(self.merged_data)),
            self.merged_data[self.exog_vars].values
        ])
        
        y = self.merged_data[self.outcome_var].values
        
        try:
            global_coeffs = solve(X.T @ X, X.T @ y)
            fitted_values = X @ global_coeffs
            residuals = y - fitted_values
            
            tss = np.sum((y - np.mean(y)) ** 2)
            rss = np.sum(residuals ** 2)
            
            return 1 - rss / tss if tss > 0 else 0
            
        except Exception:
            return 0.0
            
    def _calculate_global_fit_statistics(self,
                                       local_coefficients: np.ndarray,
                                       bandwidth: float,
                                       kernel_type: KernelType) -> Tuple[float, float, float, float, float]:
        """Calculate global model fit statistics."""
        
        # Calculate fitted values using local coefficients
        fitted_values = np.zeros(len(self.merged_data))
        
        for i in range(len(self.merged_data)):
            X_i = np.concatenate([[1], self.merged_data.iloc[i][self.exog_vars].values])
            fitted_values[i] = X_i @ local_coefficients[i, :]
            
        y = self.merged_data[self.outcome_var].values
        residuals = y - fitted_values
        rss = np.sum(residuals ** 2)
        
        # Estimate effective degrees of freedom (trace of hat matrix)
        edf = self._calculate_effective_dof(bandwidth, kernel_type)
        
        # Log-likelihood (assuming normal errors)
        n = len(self.merged_data)
        sigma2 = rss / (n - edf)
        log_likelihood = -0.5 * n * (np.log(2 * np.pi) + np.log(sigma2)) - rss / (2 * sigma2)
        
        # Information criteria
        aic = -2 * log_likelihood + 2 * edf
        aicc = aic + (2 * edf * (edf + 1)) / (n - edf - 1) if n > edf + 1 else np.inf
        bic = -2 * log_likelihood + np.log(n) * edf
        
        return aic, aicc, bic, log_likelihood, edf
        
    def _calculate_effective_dof(self, bandwidth: float, kernel_type: KernelType) -> float:
        """Calculate effective degrees of freedom (trace of hat matrix)."""
        
        # Approximate calculation using weight matrices
        trace_hat = 0.0
        
        for i in range(len(self.merged_data)):
            weights = self._calculate_weights(kernel_type, bandwidth, i)
            
            # Local hat matrix diagonal element
            X = np.column_stack([
                np.ones(len(self.merged_data)),
                self.merged_data[self.exog_vars].values
            ])
            
            W = np.diag(weights)
            
            try:
                XWX_inv = inv(X.T @ W @ X)
                X_i = X[i, :].reshape(1, -1)
                
                # H_ii = X_i (X'WX)^-1 X_i' W_ii
                h_ii = X_i @ XWX_inv @ X_i.T * weights[i]
                trace_hat += h_ii[0, 0]
                
            except Exception:
                # If singular, approximate with weight
                trace_hat += weights[i]
                
        return min(trace_hat, len(self.merged_data) - 1)
        
    def _calculate_model_diagnostics(self, results: GWRResults) -> GWRResults:
        """Calculate additional model diagnostics."""
        
        # Local heteroskedasticity measure (coefficient of variation of local R²)
        r_sq_cv = np.std(results.local_r_squared) / np.mean(results.local_r_squared)
        results.local_heteroskedasticity = pd.Series([r_sq_cv] * len(results.local_r_squared))
        
        return results
        
    def _run_spatial_diagnostics(self, results: GWRResults) -> GWRResults:
        """Run spatial diagnostics on GWR results."""
        
        # Calculate residuals from fitted values
        fitted_values = np.zeros(len(self.merged_data))
        
        for i in range(len(self.merged_data)):
            X_i = np.concatenate([[1], self.merged_data.iloc[i][self.exog_vars].values])
            fitted_values[i] = X_i @ results.local_coefficients.iloc[i].values
            
        y = self.merged_data[self.outcome_var].values
        residuals = y - fitted_values
        
        # Moran's I test for spatial autocorrelation in residuals
        moran_i, moran_p = self._calculate_morans_i(residuals)
        results.moran_i_residuals = moran_i
        results.moran_i_p_value = moran_p
        
        # Spatial autocorrelation in local coefficients
        spatial_autocorr = {}
        for var in results.local_coefficients.columns:
            coeff_values = results.local_coefficients[var].values
            if not np.all(np.isnan(coeff_values)):
                moran_coeff, _ = self._calculate_morans_i(coeff_values)
                spatial_autocorr[var] = moran_coeff
                
        results.spatial_autocorr_local_coeffs = spatial_autocorr
        
        return results
        
    def _calculate_morans_i(self, values: np.ndarray) -> Tuple[float, float]:
        """Calculate Moran's I spatial autocorrelation statistic."""
        
        # Remove NaN values
        valid_idx = ~np.isnan(values)
        if np.sum(valid_idx) < 3:
            return 0.0, 1.0
            
        values_clean = values[valid_idx]
        coords_clean = self.coords[valid_idx]
        
        # Create spatial weights (inverse distance)
        n = len(values_clean)
        if n < 3:
            return 0.0, 1.0
            
        # Calculate distance matrix for valid observations
        coords_rad = np.radians(coords_clean)
        distances = haversine_distances(coords_rad) * 6371
        
        # Create spatial weights matrix (inverse distance)
        W = np.zeros((n, n))
        for i in range(n):
            for j in range(n):
                if i != j and distances[i, j] > 0:
                    W[i, j] = 1 / distances[i, j]
                    
        # Row standardize
        row_sums = W.sum(axis=1)
        row_sums[row_sums == 0] = 1
        W = W / row_sums[:, np.newaxis]
        
        # Calculate Moran's I
        values_centered = values_clean - np.mean(values_clean)
        
        numerator = 0.0
        denominator = np.sum(values_centered ** 2)
        
        for i in range(n):
            for j in range(n):
                numerator += W[i, j] * values_centered[i] * values_centered[j]
                
        if denominator == 0:
            return 0.0, 1.0
            
        moran_i = numerator / denominator
        
        # Calculate p-value (simplified)
        # Full implementation would use proper variance formula
        expected_i = -1 / (n - 1)
        p_value = 0.05 if abs(moran_i - expected_i) > 0.1 else 0.5
        
        return moran_i, p_value
        
    def predict(self, prediction_coords: pd.DataFrame) -> pd.DataFrame:
        """Generate predictions at new locations using GWR."""
        
        if self.results is None:
            raise ValueError("Model must be fitted before making predictions")
            
        predictions = []
        
        for _, new_location in prediction_coords.iterrows():
            new_coord = np.array([[new_location['latitude'], new_location['longitude']]])
            
            # Calculate distances to all training locations
            training_coords = self.coords
            distances = cdist(new_coord, training_coords).flatten()
            
            # Calculate weights
            weights = self._calculate_weights_from_distances(distances)
            
            # Interpolate coefficients using distance weights
            interpolated_coeffs = np.zeros(len(self.results.local_coefficients.columns))
            
            for i, coeff_name in enumerate(self.results.local_coefficients.columns):
                coeff_values = self.results.local_coefficients[coeff_name].values
                valid_mask = ~np.isnan(coeff_values)
                
                if np.any(valid_mask):
                    interpolated_coeffs[i] = np.average(
                        coeff_values[valid_mask],
                        weights=weights[valid_mask]
                    )
                    
            predictions.append({
                'latitude': new_location['latitude'],
                'longitude': new_location['longitude'],
                'interpolated_coefficients': interpolated_coeffs,
                'effective_sample_size': np.sum(weights > 0.01)
            })
            
        return pd.DataFrame(predictions)
        
    def _calculate_weights_from_distances(self, distances: np.ndarray) -> np.ndarray:
        """Calculate weights from distances using fitted kernel and bandwidth."""
        
        bandwidth = self.results.optimal_bandwidth
        kernel_type = self.results.kernel_type
        
        if kernel_type == KernelType.GAUSSIAN:
            weights = np.exp(-(distances ** 2) / (2 * bandwidth ** 2))
        elif kernel_type == KernelType.EXPONENTIAL:
            weights = np.exp(-distances / bandwidth)
        elif kernel_type == KernelType.BISQUARE:
            weights = np.where(
                distances <= bandwidth,
                (1 - (distances / bandwidth) ** 2) ** 2,
                0
            )
        elif kernel_type == KernelType.TRICUBE:
            weights = np.where(
                distances <= bandwidth,
                (1 - (distances / bandwidth) ** 3) ** 3,
                0
            )
            
        # Normalize
        if np.sum(weights) > 0:
            weights = weights / np.sum(weights)
        else:
            weights = np.ones(len(weights)) / len(weights)
            
        return weights
        
    def summary(self) -> Dict[str, any]:
        """Generate comprehensive GWR summary."""
        
        if self.results is None:
            raise ValueError("Model must be fitted before generating summary")
            
        # Calculate coefficient statistics
        coeff_stats = {}
        for var in self.results.local_coefficients.columns:
            coeff_values = self.results.local_coefficients[var].dropna()
            
            coeff_stats[var] = {
                'mean': np.mean(coeff_values),
                'std': np.std(coeff_values),
                'min': np.min(coeff_values),
                'max': np.max(coeff_values),
                'range': np.max(coeff_values) - np.min(coeff_values),
                'significant_locations': np.sum(self.results.local_p_values[var] < 0.05),
                'spatial_autocorrelation': self.results.spatial_autocorr_local_coeffs.get(var, 0.0)
            }
            
        summary = {
            'model_type': 'Geographically Weighted Regression',
            'kernel_type': self.results.kernel_type.value,
            'bandwidth_method': self.results.bandwidth_method.value,
            'optimal_bandwidth_km': self.results.optimal_bandwidth,
            'model_fit': {
                'aic': self.results.aic,
                'aicc': self.results.aicc,
                'bic': self.results.bic,
                'log_likelihood': self.results.log_likelihood,
                'effective_degrees_freedom': self.results.effective_degrees_freedom
            },
            'spatial_variation': {
                'global_r_squared': self.results.global_r_squared,
                'mean_local_r_squared': np.mean(self.results.local_r_squared),
                'improvement_over_global': self.results.local_r_squared_improvement,
                'r_squared_range': (np.min(self.results.local_r_squared), 
                                  np.max(self.results.local_r_squared))
            },
            'coefficient_variation': coeff_stats,
            'spatial_diagnostics': {
                'moran_i_residuals': self.results.moran_i_residuals,
                'moran_i_p_value': self.results.moran_i_p_value,
                'residuals_spatially_autocorrelated': self.results.moran_i_p_value < 0.05
            },
            'model_diagnostics': {
                'mean_condition_number': np.mean(self.results.local_collinearity),
                'problematic_locations': np.sum(self.results.local_collinearity > 30),
                'successful_estimations': np.sum(~np.isnan(self.results.local_r_squared))
            }
        }
        
        return summary