"""
Spatial Regime Models for Currency Zone Analysis.

Implements spatial models with regime-switching based on currency zones
(Houthi vs Government territorial control) in Yemen conflict setting.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import logging
from scipy import optimize
from scipy.spatial.distance import cdist

from .spatial_weights import SpatialWeightMatrix
from .spatial_lag_model import SpatialLagModel, SpatialLagResults

logger = logging.getLogger(__name__)


class RegimeType(Enum):
    """Currency zone regime types in Yemen."""
    HOUTHI = "houthi"
    GOVERNMENT = "government"
    CONTESTED = "contested"


@dataclass
class SpatialRegimeResults:
    """Results from spatial regime model estimation."""
    
    # Model specification
    regime_variable: str
    regimes: List[str]
    n_regimes: int
    
    # Regime-specific parameters
    regime_coefficients: Dict[str, np.ndarray]
    regime_standard_errors: Dict[str, np.ndarray]
    regime_spatial_parameters: Dict[str, float]
    regime_spatial_se: Dict[str, float]
    
    # Model fit statistics
    log_likelihood: float
    aic: float
    bic: float
    n_obs: int
    
    # Regime testing
    regime_test_statistic: float
    regime_test_pvalue: float
    
    # Spatial diagnostics by regime
    regime_diagnostics: Dict[str, Dict[str, float]]
    
    # Weight matrices by regime
    regime_weights: Dict[str, np.ndarray]


class SpatialRegimeModel:
    """
    Spatial Regime Model with Currency Zone Switching.
    
    Allows different spatial processes in different currency zones:
    - Houthi-controlled areas (Northern exchange rate)
    - Government-controlled areas (Southern exchange rate)  
    - Contested areas (mixed effects)
    """
    
    def __init__(self,
                 data: pd.DataFrame,
                 coordinates: pd.DataFrame,
                 outcome_var: str,
                 exog_vars: List[str],
                 regime_var: str = 'currency_zone',
                 market_id_var: str = 'market_id',
                 spatial_weight_options: Optional[Dict] = None):
        """
        Initialize spatial regime model.
        
        Args:
            data: Market data with regime indicators
            coordinates: Market coordinates with regime information
            outcome_var: Dependent variable (e.g., log price)
            exog_vars: Exogenous variables
            regime_var: Variable indicating currency zone regime
            market_id_var: Market identifier
            spatial_weight_options: Options for spatial weight construction
        """
        self.data = data.copy()
        self.coordinates = coordinates.copy()
        self.outcome_var = outcome_var
        self.exog_vars = exog_vars
        self.regime_var = regime_var
        self.market_id_var = market_id_var
        self.spatial_weight_options = spatial_weight_options or {}
        
        self.results = None
        
        # Validate and prepare data
        self._validate_inputs()
        self._prepare_regime_data()
        self._construct_regime_weights()
        
    def _validate_inputs(self):
        """Validate model inputs for regime structure."""
        required_data_cols = [self.outcome_var, self.market_id_var, self.regime_var] + self.exog_vars
        missing_data_cols = [col for col in required_data_cols if col not in self.data.columns]
        if missing_data_cols:
            raise ValueError(f"Missing columns in data: {missing_data_cols}")
            
        required_coord_cols = [self.market_id_var, 'latitude', 'longitude', self.regime_var]
        missing_coord_cols = [col for col in required_coord_cols if col not in self.coordinates.columns]
        if missing_coord_cols:
            raise ValueError(f"Missing columns in coordinates: {missing_coord_cols}")
            
        # Check regime coverage
        self.regimes = sorted(self.data[self.regime_var].unique())
        logger.info(f"Identified regimes: {self.regimes}")
        
        if len(self.regimes) < 2:
            raise ValueError("Need at least 2 regimes for regime model")
            
    def _prepare_regime_data(self):
        """Prepare data for regime-specific estimation."""
        # Merge coordinates with data to ensure consistent regime assignment
        self.data = self.data.merge(
            self.coordinates[[self.market_id_var, self.regime_var, 'latitude', 'longitude']], 
            on=self.market_id_var, 
            how='left',
            suffixes=('', '_coord')
        )
        
        # Use coordinate-based regime if different from data
        if f'{self.regime_var}_coord' in self.data.columns:
            regime_mismatches = self.data[self.regime_var] != self.data[f'{self.regime_var}_coord']
            if regime_mismatches.any():
                logger.warning(f"Regime mismatches found for {regime_mismatches.sum()} observations")
                self.data[self.regime_var] = self.data[f'{self.regime_var}_coord']
                
        # Create regime-specific datasets
        self.regime_data = {}
        self.regime_sample_sizes = {}
        
        for regime in self.regimes:
            regime_subset = self.data[self.data[self.regime_var] == regime].copy()
            self.regime_data[regime] = regime_subset
            self.regime_sample_sizes[regime] = len(regime_subset)
            
            logger.info(f"Regime '{regime}': {len(regime_subset)} observations")
            
        # Check minimum sample sizes
        min_obs = 30
        insufficient_regimes = [r for r, n in self.regime_sample_sizes.items() if n < min_obs]
        if insufficient_regimes:
            logger.warning(f"Regimes with insufficient data (<{min_obs} obs): {insufficient_regimes}")
            
    def _construct_regime_weights(self):
        """Construct spatial weight matrices for each regime."""
        self.regime_weights = {}
        self.regime_weight_matrices = {}
        
        for regime in self.regimes:
            logger.info(f"Constructing spatial weights for regime: {regime}")
            
            # Get markets in this regime
            regime_markets = self.regime_data[regime][self.market_id_var].unique()
            regime_coords = self.coordinates[
                self.coordinates[self.market_id_var].isin(regime_markets)
            ].copy()
            
            if len(regime_coords) < 2:
                logger.warning(f"Insufficient markets for spatial weights in regime {regime}")
                self.regime_weights[regime] = np.eye(len(regime_coords))
                continue
                
            try:
                # Construct regime-specific spatial weights
                weight_matrix = SpatialWeightMatrix.from_coordinates(
                    regime_coords,
                    weight_type='currency_aware',
                    options=None
                )
                
                self.regime_weight_matrices[regime] = weight_matrix
                self.regime_weights[regime] = weight_matrix.matrix
                
                # Log connectivity statistics
                stats = weight_matrix.get_connectivity_stats()
                logger.info(f"Regime {regime} spatial weights - "
                           f"Sparsity: {stats['sparsity']:.3f}, "
                           f"Avg connections: {stats['avg_connections_per_market']:.1f}")
                           
            except Exception as e:
                logger.error(f"Failed to construct weights for regime {regime}: {e}")
                # Fallback to identity matrix
                self.regime_weights[regime] = np.eye(len(regime_coords))
                
    def fit(self, method: str = 'ml') -> SpatialRegimeResults:
        """
        Estimate spatial regime model.
        
        Args:
            method: Estimation method ('ml' for maximum likelihood)
            
        Returns:
            SpatialRegimeResults object
        """
        logger.info(f"Estimating spatial regime model with {len(self.regimes)} regimes")
        
        if method == 'ml':
            return self._fit_maximum_likelihood()
        else:
            raise NotImplementedError(f"Method {method} not implemented")
            
    def _fit_maximum_likelihood(self) -> SpatialRegimeResults:
        """Estimate model using maximum likelihood."""
        
        # Estimate regime-specific models
        regime_results = {}
        regime_coefficients = {}
        regime_standard_errors = {}
        regime_spatial_parameters = {}
        regime_spatial_se = {}
        regime_diagnostics = {}
        
        total_log_likelihood = 0.0
        total_n_obs = 0
        
        for regime in self.regimes:
            logger.info(f"Estimating spatial model for regime: {regime}")
            
            regime_data = self.regime_data[regime]
            regime_weights = self.regime_weights[regime]
            
            if len(regime_data) < 10:  # Minimum for stable estimation
                logger.warning(f"Skipping regime {regime} due to insufficient data")
                continue
                
            try:
                # Estimate spatial lag model for this regime
                spatial_model = SpatialLagModel(
                    regime_data,
                    regime_weights,
                    self.outcome_var,
                    self.exog_vars,
                    self.market_id_var
                )
                
                regime_result = spatial_model.fit()
                regime_results[regime] = regime_result
                
                # Extract parameters
                regime_coefficients[regime] = regime_result.coefficients
                regime_standard_errors[regime] = regime_result.standard_errors
                regime_spatial_parameters[regime] = regime_result.rho
                regime_spatial_se[regime] = regime_result.rho_se
                
                # Accumulate log-likelihood
                total_log_likelihood += regime_result.log_likelihood
                total_n_obs += regime_result.n_obs
                
                # Run regime-specific diagnostics
                regime_diagnostics[regime] = self._run_regime_diagnostics(regime_result)
                
                logger.info(f"Regime {regime} - Spatial parameter: {regime_result.rho:.4f} "
                           f"(SE: {regime_result.rho_se:.4f})")
                           
            except Exception as e:
                logger.error(f"Failed to estimate model for regime {regime}: {e}")
                continue
                
        # Test for regime differences
        regime_test_stat, regime_test_pval = self._test_regime_differences(regime_results)
        
        # Calculate model selection criteria
        n_params = sum(len(coef) + 1 for coef in regime_coefficients.values())  # +1 for spatial param
        aic = -2 * total_log_likelihood + 2 * n_params
        bic = -2 * total_log_likelihood + np.log(total_n_obs) * n_params
        
        # Assemble results
        results = SpatialRegimeResults(
            regime_variable=self.regime_var,
            regimes=list(regime_coefficients.keys()),
            n_regimes=len(regime_coefficients),
            regime_coefficients=regime_coefficients,
            regime_standard_errors=regime_standard_errors,
            regime_spatial_parameters=regime_spatial_parameters,
            regime_spatial_se=regime_spatial_se,
            log_likelihood=total_log_likelihood,
            aic=aic,
            bic=bic,
            n_obs=total_n_obs,
            regime_test_statistic=regime_test_stat,
            regime_test_pvalue=regime_test_pval,
            regime_diagnostics=regime_diagnostics,
            regime_weights=self.regime_weights
        )
        
        self.results = results
        return results
        
    def _run_regime_diagnostics(self, regime_result) -> Dict[str, float]:
        """Run diagnostics for individual regime."""
        # Simplified diagnostics - full implementation would include:
        # - Moran's I test for spatial dependence
        # - LM tests for spatial specification
        # - Jarque-Bera normality test
        # - Spatial heteroskedasticity tests
        
        diagnostics = {
            'r_squared': getattr(regime_result, 'r_squared', 0.0),
            'spatial_significance': 1.0 if hasattr(regime_result, 'rho_pvalue') and regime_result.rho_pvalue < 0.05 else 0.0,
            'n_observations': regime_result.n_obs
        }
        
        return diagnostics
        
    def _test_regime_differences(self, regime_results: Dict) -> Tuple[float, float]:
        """Test whether regimes have significantly different parameters."""
        
        if len(regime_results) < 2:
            return 0.0, 1.0
            
        # Simplified Chow test for regime differences
        # Full implementation would use likelihood ratio test
        
        # Calculate pooled model likelihood (unrestricted)
        unrestricted_ll = sum(result.log_likelihood for result in regime_results.values())
        
        # Calculate restricted model (pooled estimation)
        try:
            pooled_data = pd.concat([self.regime_data[regime] for regime in regime_results.keys()])
            
            # Create pooled spatial weights (simplified)
            pooled_weights = np.eye(len(pooled_data))
            
            pooled_model = SpatialLagModel(
                pooled_data,
                pooled_weights,
                self.outcome_var,
                self.exog_vars,
                self.market_id_var
            )
            
            pooled_result = pooled_model.fit()
            restricted_ll = pooled_result.log_likelihood
            
            # Likelihood ratio test
            lr_statistic = 2 * (unrestricted_ll - restricted_ll)
            
            # Degrees of freedom = difference in number of parameters
            n_params_unrestricted = sum(len(result.coefficients) + 1 for result in regime_results.values())
            n_params_restricted = len(pooled_result.coefficients) + 1
            df = n_params_unrestricted - n_params_restricted
            
            # Chi-square p-value
            from scipy.stats import chi2
            p_value = 1 - chi2.cdf(lr_statistic, df) if df > 0 else 1.0
            
            return lr_statistic, p_value
            
        except Exception as e:
            logger.warning(f"Could not perform regime test: {e}")
            return 0.0, 1.0
            
    def predict_by_regime(self, prediction_data: pd.DataFrame) -> pd.DataFrame:
        """Generate regime-specific predictions."""
        if self.results is None:
            raise ValueError("Model must be fitted before making predictions")
            
        predictions = []
        
        for regime in self.results.regimes:
            regime_subset = prediction_data[
                prediction_data[self.regime_var] == regime
            ].copy()
            
            if len(regime_subset) == 0:
                continue
                
            # Simple prediction using regime coefficients
            # Full implementation would account for spatial dependencies
            
            X = regime_subset[self.exog_vars].values
            coefficients = self.results.regime_coefficients[regime]
            
            if len(coefficients) == len(self.exog_vars):
                regime_predictions = X @ coefficients
            else:
                # Handle case where intercept is included
                X_with_intercept = np.column_stack([np.ones(len(X)), X])
                regime_predictions = X_with_intercept @ coefficients
                
            regime_subset['predicted_value'] = regime_predictions
            regime_subset['regime'] = regime
            predictions.append(regime_subset)
            
        if predictions:
            return pd.concat(predictions, ignore_index=True)
        else:
            return pd.DataFrame()
            
    def summary(self) -> Dict[str, any]:
        """Generate comprehensive regime model summary."""
        if self.results is None:
            raise ValueError("Model must be fitted before generating summary")
            
        summary = {
            'model_type': 'Spatial Regime Model',
            'regime_variable': self.results.regime_variable,
            'n_regimes': self.results.n_regimes,
            'regimes': self.results.regimes,
            'model_fit': {
                'log_likelihood': self.results.log_likelihood,
                'aic': self.results.aic,
                'bic': self.results.bic,
                'n_observations': self.results.n_obs
            },
            'regime_test': {
                'statistic': self.results.regime_test_statistic,
                'p_value': self.results.regime_test_pvalue,
                'significant': self.results.regime_test_pvalue < 0.05
            },
            'regime_results': {}
        }
        
        # Add regime-specific results
        for regime in self.results.regimes:
            if regime in self.results.regime_coefficients:
                summary['regime_results'][regime] = {
                    'spatial_parameter': {
                        'value': self.results.regime_spatial_parameters[regime],
                        'standard_error': self.results.regime_spatial_se[regime],
                        'significant': (abs(self.results.regime_spatial_parameters[regime] / 
                                         self.results.regime_spatial_se[regime]) > 1.96)
                    },
                    'coefficients': self.results.regime_coefficients[regime].tolist(),
                    'standard_errors': self.results.regime_standard_errors[regime].tolist(),
                    'diagnostics': self.results.regime_diagnostics.get(regime, {}),
                    'sample_size': self.regime_sample_sizes[regime]
                }
                
        return summary
        
    def compare_regimes(self) -> pd.DataFrame:
        """Compare spatial parameters across regimes."""
        if self.results is None:
            raise ValueError("Model must be fitted before comparing regimes")
            
        comparison_data = []
        
        for regime in self.results.regimes:
            if regime in self.results.regime_spatial_parameters:
                spatial_param = self.results.regime_spatial_parameters[regime]
                spatial_se = self.results.regime_spatial_se[regime]
                
                comparison_data.append({
                    'regime': regime,
                    'spatial_parameter': spatial_param,
                    'standard_error': spatial_se,
                    't_statistic': spatial_param / spatial_se if spatial_se > 0 else np.nan,
                    'significant': abs(spatial_param / spatial_se) > 1.96 if spatial_se > 0 else False,
                    'sample_size': self.regime_sample_sizes[regime],
                    'spatial_connectivity': np.mean(self.regime_weights[regime] > 0) if regime in self.regime_weights else 0.0
                })
                
        return pd.DataFrame(comparison_data)


def test_spatial_regime_break(
    data: pd.DataFrame,
    coordinates: pd.DataFrame,
    outcome_var: str,
    exog_vars: List[str],
    regime_var: str,
    market_id_var: str = 'market_id'
) -> Dict[str, float]:
    """
    Test for structural breaks in spatial parameters across regimes.
    
    Args:
        data: Market panel data
        coordinates: Market coordinates with regime information
        outcome_var: Dependent variable
        exog_vars: Exogenous variables
        regime_var: Variable indicating regime
        market_id_var: Market identifier
        
    Returns:
        Dictionary with test statistics and p-values
    """
    
    # Estimate regime model
    regime_model = SpatialRegimeModel(
        data, coordinates, outcome_var, exog_vars, regime_var, market_id_var
    )
    
    regime_results = regime_model.fit()
    
    # Estimate pooled model for comparison
    pooled_data = data.copy()
    
    # Simple pooled weights (identity matrix for comparison)
    n_markets = len(coordinates)
    pooled_weights = np.eye(n_markets)
    
    pooled_model = SpatialLagModel(
        pooled_data, pooled_weights, outcome_var, exog_vars, market_id_var
    )
    
    pooled_result = pooled_model.fit()
    
    # Calculate test statistics
    test_results = {
        'regime_lr_statistic': regime_results.regime_test_statistic,
        'regime_lr_pvalue': regime_results.regime_test_pvalue,
        'pooled_spatial_param': pooled_result.rho,
        'pooled_spatial_se': pooled_result.rho_se,
        'regime_spatial_params': regime_results.regime_spatial_parameters,
        'significant_regime_differences': regime_results.regime_test_pvalue < 0.05
    }
    
    return test_results