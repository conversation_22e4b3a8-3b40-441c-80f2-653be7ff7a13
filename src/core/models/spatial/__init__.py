"""
Advanced Spatial Econometric Framework for Market Integration Analysis.

This module provides comprehensive spatial econometric capabilities
for analyzing market integration in conflict settings with advanced methods.

Core Components:
- SpatialWeightMatrix: Construction of spatial connectivity matrices
- SpatialLagModel: Spatial autoregressive (SAR) models  
- SpatialErrorModel: Spatial error (SEM) models
- SpatialPanelModel: Fixed effects spatial panel models
- SpatialDiagnostics: Testing for spatial dependence

Advanced Methods:
- SpatialRegimeModel: Currency zone-specific spatial processes
- DynamicSpatialPanelModel: Spatial panels with temporal dynamics
- SpatialThresholdModel: Conflict intensity threshold switching
- GeographicallyWeightedRegression: Local spatial relationship analysis

Usage:
    from src.core.models.spatial import (
        SpatialLagModel, SpatialWeightMatrix, SpatialRegimeModel,
        DynamicSpatialPanelModel, SpatialThresholdModel,
        GeographicallyWeightedRegression
    )
    
    # Basic spatial analysis
    W = SpatialWeightMatrix.from_coordinates(market_coords, weight_type='currency_aware')
    model = SpatialLagModel(data, W)
    results = model.fit()
    
    # Advanced currency zone regime analysis
    regime_model = SpatialRegimeModel(data, coords, 'log_price', exog_vars, 'currency_zone')
    regime_results = regime_model.fit()
    
    # Dynamic spatial panel with temporal lags
    dynamic_model = DynamicSpatialPanelModel(panel_data, W, 'log_price', exog_vars)
    dynamic_results = dynamic_model.fit(specification=DynamicSpecification.FULL_DYNAMIC)
    
    # Conflict threshold analysis
    threshold_model = SpatialThresholdModel(data, W, 'log_price', exog_vars, 'conflict_intensity')
    threshold_results = threshold_model.fit(threshold_type=ThresholdType.SINGLE_THRESHOLD)
    
    # Local spatial relationship analysis
    gwr_model = GeographicallyWeightedRegression(data, coords, 'log_price', exog_vars)
    gwr_results = gwr_model.fit(kernel_type=KernelType.GAUSSIAN)
"""

from .spatial_weights import SpatialWeightMatrix, SpatialWeightOptions
from .spatial_lag_model import SpatialLagModel, SpatialLagResults
from .spatial_error_model import SpatialErrorModel, SpatialErrorResults
from .spatial_panel_model import (
    SpatialPanelModel, SpatialPanelResults, SpatialPanelType, FixedEffectsType
)
from .spatial_diagnostics import SpatialDiagnostics

# Advanced spatial methods
from .spatial_regime_model import (
    SpatialRegimeModel, SpatialRegimeResults, RegimeType, test_spatial_regime_break
)
from .dynamic_spatial_panel import (
    DynamicSpatialPanelModel, DynamicSpatialResults, DynamicSpecification
)
from .spatial_threshold_model import (
    SpatialThresholdModel, SpatialThresholdResults, ThresholdType, ThresholdVariable
)
from .geographically_weighted_regression import (
    GeographicallyWeightedRegression, GWRResults, KernelType, BandwidthSelection
)

__all__ = [
    # Core spatial methods
    'SpatialWeightMatrix',
    'SpatialWeightOptions',
    'SpatialLagModel',
    'SpatialLagResults',
    'SpatialErrorModel',
    'SpatialErrorResults',
    'SpatialPanelModel',
    'SpatialPanelResults',
    'SpatialPanelType',
    'FixedEffectsType',
    'SpatialDiagnostics',
    
    # Advanced spatial methods
    'SpatialRegimeModel',
    'SpatialRegimeResults',
    'RegimeType',
    'test_spatial_regime_break',
    'DynamicSpatialPanelModel',
    'DynamicSpatialResults',
    'DynamicSpecification',
    'SpatialThresholdModel',
    'SpatialThresholdResults',
    'ThresholdType',
    'ThresholdVariable',
    'GeographicallyWeightedRegression',
    'GWRResults',
    'KernelType',
    'BandwidthSelection'
]