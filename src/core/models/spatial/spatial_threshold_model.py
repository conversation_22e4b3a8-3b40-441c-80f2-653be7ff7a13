"""
Spatial Threshold Models for Conflict Intensity Effects.

Implements spatial models where spatial dependence switches at conflict 
intensity thresholds, capturing non-linear effects of conflict on market integration.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import logging
from scipy import optimize
from scipy.stats import norm, chi2

from .spatial_lag_model import SpatialLagModel, SpatialLagResults
from .spatial_error_model import SpatialErrorModel, SpatialErrorResults

logger = logging.getLogger(__name__)


class ThresholdType(Enum):
    """Types of threshold specifications."""
    SINGLE_THRESHOLD = "single_threshold"
    DOUBLE_THRESHOLD = "double_threshold"
    SMOOTH_TRANSITION = "smooth_transition"


class ThresholdVariable(Enum):
    """Variables that can serve as threshold variables."""
    CONFLICT_INTENSITY = "conflict_intensity"
    FATALITY_RATE = "fatality_rate"
    EVENT_DENSITY = "event_density"
    DISTANCE_TO_CONFLICT = "distance_to_conflict"


@dataclass
class SpatialThresholdResults:
    """Results from spatial threshold model estimation."""
    
    # Model specification
    threshold_type: ThresholdType
    threshold_variable: str
    spatial_model_type: str
    
    # Threshold parameters
    threshold_values: np.ndarray
    threshold_confidence_intervals: List[Tuple[float, float]]
    
    # Regime-specific spatial parameters
    regime_spatial_parameters: Dict[str, float]
    regime_spatial_se: Dict[str, float]
    
    # Other model parameters
    coefficients: np.ndarray
    standard_errors: np.ndarray
    
    # Model fit and selection
    log_likelihood: float
    aic: float
    bic: float
    r_squared: float
    n_obs: int
    
    # Threshold tests
    threshold_test_statistic: float
    threshold_test_pvalue: float
    linearity_test_statistic: float
    linearity_test_pvalue: float
    
    # Regime classification
    regime_assignments: pd.Series
    regime_probabilities: Optional[pd.DataFrame] = None
    
    # Bootstrap results (if performed)
    bootstrap_thresholds: Optional[np.ndarray] = None
    
    # Spatial diagnostics by regime
    regime_spatial_diagnostics: Dict[str, Dict[str, float]]


class SpatialThresholdModel:
    """
    Spatial Threshold Model with Conflict Intensity Switching.
    
    Models spatial dependence that changes based on conflict intensity:
    
    Low conflict: Y = ρ₁WY + βX + ε  (high spatial integration)
    High conflict: Y = ρ₂WY + βX + ε  (low spatial integration)
    
    With threshold determined by conflict intensity variable.
    """
    
    def __init__(self,
                 data: pd.DataFrame,
                 weight_matrix: np.ndarray,
                 outcome_var: str,
                 exog_vars: List[str],
                 threshold_var: str,
                 market_id_var: str = 'market_id',
                 spatial_model_type: str = 'lag'):
        """
        Initialize spatial threshold model.
        
        Args:
            data: Market data with conflict indicators
            weight_matrix: Spatial weight matrix
            outcome_var: Dependent variable
            exog_vars: Exogenous variables
            threshold_var: Variable for threshold detection
            market_id_var: Market identifier
            spatial_model_type: Type of spatial model ('lag' or 'error')
        """
        self.data = data.copy()
        self.W = weight_matrix
        self.outcome_var = outcome_var
        self.exog_vars = exog_vars
        self.threshold_var = threshold_var
        self.market_id_var = market_id_var
        self.spatial_model_type = spatial_model_type
        
        self.results = None
        
        # Validate inputs
        self._validate_inputs()
        self._prepare_threshold_data()
        
    def _validate_inputs(self):
        """Validate model inputs for threshold analysis."""
        required_cols = [self.outcome_var, self.threshold_var, self.market_id_var] + self.exog_vars
        missing_cols = [col for col in required_cols if col not in self.data.columns]
        if missing_cols:
            raise ValueError(f"Missing columns in data: {missing_cols}")
            
        # Check threshold variable properties
        threshold_values = self.data[self.threshold_var].dropna()
        if len(threshold_values) < 50:
            raise ValueError(f"Insufficient observations for threshold analysis: {len(threshold_values)}")
            
        if threshold_values.nunique() < 10:
            logger.warning(f"Limited variation in threshold variable: {threshold_values.nunique()} unique values")
            
    def _prepare_threshold_data(self):
        """Prepare data for threshold estimation."""
        # Remove missing values
        self.data = self.data.dropna(subset=[self.threshold_var, self.outcome_var] + self.exog_vars)
        
        # Sort by threshold variable for search algorithm
        self.data = self.data.sort_values(self.threshold_var)
        
        # Get threshold variable range for search
        self.threshold_range = (
            self.data[self.threshold_var].quantile(0.15),  # Exclude extreme values
            self.data[self.threshold_var].quantile(0.85)
        )
        
        logger.info(f"Threshold search range: [{self.threshold_range[0]:.3f}, {self.threshold_range[1]:.3f}]")
        
    def fit(self,
            threshold_type: ThresholdType = ThresholdType.SINGLE_THRESHOLD,
            trim_percentage: float = 0.15,
            bootstrap_reps: int = 0) -> SpatialThresholdResults:
        """
        Estimate spatial threshold model.
        
        Args:
            threshold_type: Type of threshold model
            trim_percentage: Percentage to trim from extremes for threshold search
            bootstrap_reps: Number of bootstrap replications for inference
            
        Returns:
            SpatialThresholdResults object
        """
        logger.info(f"Estimating spatial threshold model: {threshold_type.value}")
        
        if threshold_type == ThresholdType.SINGLE_THRESHOLD:
            return self._fit_single_threshold(trim_percentage, bootstrap_reps)
        elif threshold_type == ThresholdType.DOUBLE_THRESHOLD:
            return self._fit_double_threshold(trim_percentage, bootstrap_reps)
        elif threshold_type == ThresholdType.SMOOTH_TRANSITION:
            return self._fit_smooth_transition(trim_percentage, bootstrap_reps)
        else:
            raise NotImplementedError(f"Threshold type {threshold_type} not implemented")
            
    def _fit_single_threshold(self,
                             trim_percentage: float,
                             bootstrap_reps: int) -> SpatialThresholdResults:
        """Estimate single threshold spatial model."""
        
        # Grid search for optimal threshold
        threshold_candidates = self._generate_threshold_grid(trim_percentage)
        
        best_threshold = None
        best_likelihood = -np.inf
        best_results = None
        
        logger.info(f"Searching over {len(threshold_candidates)} threshold candidates")
        
        for threshold in threshold_candidates:
            try:
                # Estimate model with this threshold
                regime_results = self._estimate_threshold_model(threshold)
                
                # Calculate total likelihood
                total_likelihood = sum(res.log_likelihood for res in regime_results.values())
                
                if total_likelihood > best_likelihood:
                    best_likelihood = total_likelihood
                    best_threshold = threshold
                    best_results = regime_results
                    
            except Exception as e:
                logger.debug(f"Failed to estimate model with threshold {threshold}: {e}")
                continue
                
        if best_threshold is None:
            raise ValueError("Could not find valid threshold")
            
        logger.info(f"Optimal threshold: {best_threshold:.4f}")
        
        # Extract results
        threshold_results = self._extract_threshold_results(
            best_threshold, 
            best_results, 
            ThresholdType.SINGLE_THRESHOLD
        )
        
        # Bootstrap inference if requested
        if bootstrap_reps > 0:
            threshold_results = self._bootstrap_threshold_inference(
                threshold_results, bootstrap_reps
            )
            
        # Run threshold tests
        threshold_results = self._run_threshold_tests(threshold_results, best_results)
        
        self.results = threshold_results
        return threshold_results
        
    def _fit_double_threshold(self,
                             trim_percentage: float,
                             bootstrap_reps: int) -> SpatialThresholdResults:
        """Estimate double threshold spatial model."""
        
        # Grid search for two thresholds
        threshold_candidates = self._generate_threshold_grid(trim_percentage)
        
        best_thresholds = None
        best_likelihood = -np.inf
        best_results = None
        
        logger.info(f"Searching over {len(threshold_candidates)}² threshold combinations")
        
        # Search over threshold pairs
        for i, thresh1 in enumerate(threshold_candidates[:-1]):
            for thresh2 in threshold_candidates[i+1:]:
                try:
                    # Estimate three-regime model
                    regime_results = self._estimate_double_threshold_model(thresh1, thresh2)
                    
                    # Calculate total likelihood
                    total_likelihood = sum(res.log_likelihood for res in regime_results.values())
                    
                    if total_likelihood > best_likelihood:
                        best_likelihood = total_likelihood
                        best_thresholds = (thresh1, thresh2)
                        best_results = regime_results
                        
                except Exception as e:
                    logger.debug(f"Failed to estimate model with thresholds {thresh1}, {thresh2}: {e}")
                    continue
                    
        if best_thresholds is None:
            raise ValueError("Could not find valid double threshold")
            
        logger.info(f"Optimal thresholds: {best_thresholds[0]:.4f}, {best_thresholds[1]:.4f}")
        
        # Extract results
        threshold_results = self._extract_double_threshold_results(
            best_thresholds,
            best_results,
            ThresholdType.DOUBLE_THRESHOLD
        )
        
        # Bootstrap inference if requested
        if bootstrap_reps > 0:
            threshold_results = self._bootstrap_threshold_inference(
                threshold_results, bootstrap_reps
            )
            
        self.results = threshold_results
        return threshold_results
        
    def _fit_smooth_transition(self,
                              trim_percentage: float,
                              bootstrap_reps: int) -> SpatialThresholdResults:
        """Estimate smooth transition spatial model."""
        
        # Implement smooth transition threshold model
        # For now, return single threshold as approximation
        logger.warning("Smooth transition not fully implemented, using single threshold")
        return self._fit_single_threshold(trim_percentage, bootstrap_reps)
        
    def _generate_threshold_grid(self, trim_percentage: float) -> np.ndarray:
        """Generate grid of threshold candidates."""
        
        # Trim extreme values
        lower_quantile = trim_percentage
        upper_quantile = 1 - trim_percentage
        
        threshold_min = self.data[self.threshold_var].quantile(lower_quantile)
        threshold_max = self.data[self.threshold_var].quantile(upper_quantile)
        
        # Create grid with sufficient density
        n_candidates = min(100, int(len(self.data) * 0.1))  # Reasonable number of candidates
        
        return np.linspace(threshold_min, threshold_max, n_candidates)
        
    def _estimate_threshold_model(self, threshold: float) -> Dict[str, Union[SpatialLagResults, SpatialErrorResults]]:
        """Estimate spatial model for given threshold value."""
        
        # Split data into regimes
        low_regime_data = self.data[self.data[self.threshold_var] <= threshold].copy()
        high_regime_data = self.data[self.data[self.threshold_var] > threshold].copy()
        
        results = {}
        
        # Estimate low regime model
        if len(low_regime_data) >= 10:  # Minimum observations
            try:
                if self.spatial_model_type == 'lag':
                    low_model = SpatialLagModel(
                        low_regime_data, self.W, self.outcome_var, 
                        self.exog_vars, self.market_id_var
                    )
                else:
                    low_model = SpatialErrorModel(
                        low_regime_data, self.W, self.outcome_var,
                        self.exog_vars, self.market_id_var
                    )
                    
                results['low'] = low_model.fit()
                
            except Exception as e:
                logger.debug(f"Failed to estimate low regime: {e}")
                
        # Estimate high regime model
        if len(high_regime_data) >= 10:
            try:
                if self.spatial_model_type == 'lag':
                    high_model = SpatialLagModel(
                        high_regime_data, self.W, self.outcome_var,
                        self.exog_vars, self.market_id_var
                    )
                else:
                    high_model = SpatialErrorModel(
                        high_regime_data, self.W, self.outcome_var,
                        self.exog_vars, self.market_id_var
                    )
                    
                results['high'] = high_model.fit()
                
            except Exception as e:
                logger.debug(f"Failed to estimate high regime: {e}")
                
        if len(results) < 2:
            raise ValueError(f"Insufficient regimes estimated for threshold {threshold}")
            
        return results
        
    def _estimate_double_threshold_model(self,
                                       threshold1: float,
                                       threshold2: float) -> Dict[str, Union[SpatialLagResults, SpatialErrorResults]]:
        """Estimate spatial model with two thresholds (three regimes)."""
        
        # Ensure proper ordering
        thresh_low, thresh_high = sorted([threshold1, threshold2])
        
        # Split data into three regimes
        low_regime_data = self.data[self.data[self.threshold_var] <= thresh_low].copy()
        middle_regime_data = self.data[
            (self.data[self.threshold_var] > thresh_low) & 
            (self.data[self.threshold_var] <= thresh_high)
        ].copy()
        high_regime_data = self.data[self.data[self.threshold_var] > thresh_high].copy()
        
        results = {}
        
        # Estimate each regime
        regime_data = {
            'low': low_regime_data,
            'middle': middle_regime_data,
            'high': high_regime_data
        }
        
        for regime_name, regime_data_subset in regime_data.items():
            if len(regime_data_subset) >= 10:
                try:
                    if self.spatial_model_type == 'lag':
                        model = SpatialLagModel(
                            regime_data_subset, self.W, self.outcome_var,
                            self.exog_vars, self.market_id_var
                        )
                    else:
                        model = SpatialErrorModel(
                            regime_data_subset, self.W, self.outcome_var,
                            self.exog_vars, self.market_id_var
                        )
                        
                    results[regime_name] = model.fit()
                    
                except Exception as e:
                    logger.debug(f"Failed to estimate {regime_name} regime: {e}")
                    
        if len(results) < 3:
            raise ValueError(f"Insufficient regimes estimated for double threshold")
            
        return results
        
    def _extract_threshold_results(self,
                                  threshold: float,
                                  regime_results: Dict,
                                  threshold_type: ThresholdType) -> SpatialThresholdResults:
        """Extract results from threshold estimation."""
        
        # Extract spatial parameters
        regime_spatial_parameters = {}
        regime_spatial_se = {}
        
        for regime, result in regime_results.items():
            if isinstance(result, SpatialLagResults):
                regime_spatial_parameters[regime] = result.rho
                regime_spatial_se[regime] = result.rho_se
            else:  # SpatialErrorResults
                regime_spatial_parameters[regime] = result.lam
                regime_spatial_se[regime] = result.lam_se
                
        # Calculate total likelihood and fit statistics
        total_likelihood = sum(res.log_likelihood for res in regime_results.values())
        total_n_obs = sum(res.n_obs for res in regime_results.values())
        
        # Count parameters
        n_params = sum(len(res.coefficients) + 1 for res in regime_results.values())  # +1 for spatial param
        
        aic = -2 * total_likelihood + 2 * n_params
        bic = -2 * total_likelihood + np.log(total_n_obs) * n_params
        
        # Assign regime memberships
        regime_assignments = pd.Series(index=self.data.index, dtype=str)
        regime_assignments[self.data[self.threshold_var] <= threshold] = 'low'
        regime_assignments[self.data[self.threshold_var] > threshold] = 'high'
        
        # Simplified spatial diagnostics
        regime_spatial_diagnostics = {}
        for regime, result in regime_results.items():
            regime_spatial_diagnostics[regime] = {
                'r_squared': getattr(result, 'r_squared', 0.0),
                'n_observations': result.n_obs
            }
            
        results = SpatialThresholdResults(
            threshold_type=threshold_type,
            threshold_variable=self.threshold_var,
            spatial_model_type=self.spatial_model_type,
            threshold_values=np.array([threshold]),
            threshold_confidence_intervals=[(threshold, threshold)],  # Will be updated with bootstrap
            regime_spatial_parameters=regime_spatial_parameters,
            regime_spatial_se=regime_spatial_se,
            coefficients=np.concatenate([res.coefficients for res in regime_results.values()]),
            standard_errors=np.concatenate([res.standard_errors for res in regime_results.values()]),
            log_likelihood=total_likelihood,
            aic=aic,
            bic=bic,
            r_squared=0.0,  # Would need to calculate properly
            n_obs=total_n_obs,
            threshold_test_statistic=0.0,  # Will be calculated in threshold tests
            threshold_test_pvalue=1.0,
            linearity_test_statistic=0.0,
            linearity_test_pvalue=1.0,
            regime_assignments=regime_assignments,
            regime_spatial_diagnostics=regime_spatial_diagnostics
        )
        
        return results
        
    def _extract_double_threshold_results(self,
                                        thresholds: Tuple[float, float],
                                        regime_results: Dict,
                                        threshold_type: ThresholdType) -> SpatialThresholdResults:
        """Extract results from double threshold estimation."""
        
        # Similar to single threshold but with three regimes
        thresh_low, thresh_high = sorted(thresholds)
        
        # Extract spatial parameters
        regime_spatial_parameters = {}
        regime_spatial_se = {}
        
        for regime, result in regime_results.items():
            if isinstance(result, SpatialLagResults):
                regime_spatial_parameters[regime] = result.rho
                regime_spatial_se[regime] = result.rho_se
            else:  # SpatialErrorResults
                regime_spatial_parameters[regime] = result.lam
                regime_spatial_se[regime] = result.lam_se
                
        # Calculate fit statistics
        total_likelihood = sum(res.log_likelihood for res in regime_results.values())
        total_n_obs = sum(res.n_obs for res in regime_results.values())
        n_params = sum(len(res.coefficients) + 1 for res in regime_results.values())
        
        aic = -2 * total_likelihood + 2 * n_params
        bic = -2 * total_likelihood + np.log(total_n_obs) * n_params
        
        # Assign regime memberships for three regimes
        regime_assignments = pd.Series(index=self.data.index, dtype=str)
        regime_assignments[self.data[self.threshold_var] <= thresh_low] = 'low'
        regime_assignments[
            (self.data[self.threshold_var] > thresh_low) & 
            (self.data[self.threshold_var] <= thresh_high)
        ] = 'middle'
        regime_assignments[self.data[self.threshold_var] > thresh_high] = 'high'
        
        # Spatial diagnostics
        regime_spatial_diagnostics = {}
        for regime, result in regime_results.items():
            regime_spatial_diagnostics[regime] = {
                'r_squared': getattr(result, 'r_squared', 0.0),
                'n_observations': result.n_obs
            }
            
        results = SpatialThresholdResults(
            threshold_type=threshold_type,
            threshold_variable=self.threshold_var,
            spatial_model_type=self.spatial_model_type,
            threshold_values=np.array([thresh_low, thresh_high]),
            threshold_confidence_intervals=[(thresh_low, thresh_low), (thresh_high, thresh_high)],
            regime_spatial_parameters=regime_spatial_parameters,
            regime_spatial_se=regime_spatial_se,
            coefficients=np.concatenate([res.coefficients for res in regime_results.values()]),
            standard_errors=np.concatenate([res.standard_errors for res in regime_results.values()]),
            log_likelihood=total_likelihood,
            aic=aic,
            bic=bic,
            r_squared=0.0,
            n_obs=total_n_obs,
            threshold_test_statistic=0.0,
            threshold_test_pvalue=1.0,
            linearity_test_statistic=0.0,
            linearity_test_pvalue=1.0,
            regime_assignments=regime_assignments,
            regime_spatial_diagnostics=regime_spatial_diagnostics
        )
        
        return results
        
    def _bootstrap_threshold_inference(self,
                                     results: SpatialThresholdResults,
                                     bootstrap_reps: int) -> SpatialThresholdResults:
        """Bootstrap inference for threshold values."""
        
        logger.info(f"Running bootstrap inference with {bootstrap_reps} replications")
        
        bootstrap_thresholds = []
        
        for rep in range(bootstrap_reps):
            try:
                # Bootstrap sample
                bootstrap_indices = np.random.choice(len(self.data), size=len(self.data), replace=True)
                bootstrap_data = self.data.iloc[bootstrap_indices].copy()
                
                # Re-estimate threshold on bootstrap sample
                bootstrap_model = SpatialThresholdModel(
                    bootstrap_data, self.W, self.outcome_var,
                    self.exog_vars, self.threshold_var, self.market_id_var,
                    self.spatial_model_type
                )
                
                bootstrap_results = bootstrap_model.fit(
                    threshold_type=results.threshold_type,
                    bootstrap_reps=0  # Don't bootstrap the bootstrap
                )
                
                bootstrap_thresholds.append(bootstrap_results.threshold_values)
                
            except Exception as e:
                logger.debug(f"Bootstrap replication {rep} failed: {e}")
                continue
                
        if bootstrap_thresholds:
            bootstrap_thresholds = np.array(bootstrap_thresholds)
            
            # Calculate confidence intervals
            confidence_intervals = []
            for i in range(len(results.threshold_values)):
                threshold_dist = bootstrap_thresholds[:, i]
                ci_lower = np.percentile(threshold_dist, 2.5)
                ci_upper = np.percentile(threshold_dist, 97.5)
                confidence_intervals.append((ci_lower, ci_upper))
                
            results.threshold_confidence_intervals = confidence_intervals
            results.bootstrap_thresholds = bootstrap_thresholds
            
            logger.info(f"Bootstrap completed: {len(bootstrap_thresholds)} successful replications")
            
        return results
        
    def _run_threshold_tests(self,
                           results: SpatialThresholdResults,
                           regime_results: Dict) -> SpatialThresholdResults:
        """Run tests for threshold significance."""
        
        # Test for threshold effect (vs linear model)
        # Estimate linear model for comparison
        try:
            if self.spatial_model_type == 'lag':
                linear_model = SpatialLagModel(
                    self.data, self.W, self.outcome_var,
                    self.exog_vars, self.market_id_var
                )
            else:
                linear_model = SpatialErrorModel(
                    self.data, self.W, self.outcome_var,
                    self.exog_vars, self.market_id_var
                )
                
            linear_result = linear_model.fit()
            
            # Likelihood ratio test
            lr_statistic = 2 * (results.log_likelihood - linear_result.log_likelihood)
            df = len(regime_results) - 1  # Additional regimes vs linear
            lr_pvalue = 1 - chi2.cdf(lr_statistic, df) if df > 0 else 1.0
            
            results.linearity_test_statistic = lr_statistic
            results.linearity_test_pvalue = lr_pvalue
            
            logger.info(f"Linearity test: LR = {lr_statistic:.3f}, p-value = {lr_pvalue:.3f}")
            
        except Exception as e:
            logger.warning(f"Could not perform linearity test: {e}")
            
        # Hansen threshold test (simplified)
        # Full implementation would use proper Hansen test statistic
        results.threshold_test_statistic = results.linearity_test_statistic
        results.threshold_test_pvalue = results.linearity_test_pvalue
        
        return results
        
    def predict_by_regime(self, prediction_data: pd.DataFrame) -> pd.DataFrame:
        """Generate predictions based on threshold regime assignment."""
        
        if self.results is None:
            raise ValueError("Model must be fitted before making predictions")
            
        predictions = prediction_data.copy()
        
        # Assign regimes based on threshold values
        if self.results.threshold_type == ThresholdType.SINGLE_THRESHOLD:
            threshold = self.results.threshold_values[0]
            predictions['regime'] = np.where(
                predictions[self.threshold_var] <= threshold, 'low', 'high'
            )
        elif self.results.threshold_type == ThresholdType.DOUBLE_THRESHOLD:
            thresh_low, thresh_high = self.results.threshold_values
            predictions['regime'] = np.select(
                [predictions[self.threshold_var] <= thresh_low,
                 predictions[self.threshold_var] <= thresh_high],
                ['low', 'middle'],
                default='high'
            )
            
        # Generate regime-specific predictions (simplified)
        predictions['predicted_value'] = 0.0
        predictions['spatial_effect'] = 0.0
        
        for regime in predictions['regime'].unique():
            if regime in self.results.regime_spatial_parameters:
                regime_mask = predictions['regime'] == regime
                
                # Simple prediction without full spatial lag calculation
                predictions.loc[regime_mask, 'spatial_effect'] = self.results.regime_spatial_parameters[regime]
                
        return predictions
        
    def summary(self) -> Dict[str, any]:
        """Generate comprehensive threshold model summary."""
        
        if self.results is None:
            raise ValueError("Model must be fitted before generating summary")
            
        summary = {
            'model_type': f'Spatial Threshold Model ({self.results.threshold_type.value})',
            'spatial_model': self.spatial_model_type,
            'threshold_variable': self.results.threshold_variable,
            'thresholds': {
                'values': self.results.threshold_values.tolist(),
                'confidence_intervals': self.results.threshold_confidence_intervals,
                'significant': self.results.threshold_test_pvalue < 0.05
            },
            'regime_effects': {},
            'model_fit': {
                'log_likelihood': self.results.log_likelihood,
                'aic': self.results.aic,
                'bic': self.results.bic,
                'n_observations': self.results.n_obs
            },
            'threshold_tests': {
                'threshold_test_statistic': self.results.threshold_test_statistic,
                'threshold_test_pvalue': self.results.threshold_test_pvalue,
                'linearity_test_statistic': self.results.linearity_test_statistic,
                'linearity_test_pvalue': self.results.linearity_test_pvalue
            },
            'regime_composition': dict(self.results.regime_assignments.value_counts())
        }
        
        # Add regime-specific effects
        for regime in self.results.regime_spatial_parameters:
            spatial_param = self.results.regime_spatial_parameters[regime]
            spatial_se = self.results.regime_spatial_se[regime]
            
            summary['regime_effects'][regime] = {
                'spatial_parameter': spatial_param,
                'standard_error': spatial_se,
                't_statistic': spatial_param / spatial_se if spatial_se > 0 else np.nan,
                'significant': abs(spatial_param / spatial_se) > 1.96 if spatial_se > 0 else False,
                'diagnostics': self.results.regime_spatial_diagnostics.get(regime, {})
            }
            
        return summary
        
    def plot_threshold_function(self) -> pd.DataFrame:
        """Generate data for plotting threshold function."""
        
        if self.results is None:
            raise ValueError("Model must be fitted before plotting")
            
        # Create grid of threshold variable values
        threshold_min = self.data[self.threshold_var].min()
        threshold_max = self.data[self.threshold_var].max()
        threshold_grid = np.linspace(threshold_min, threshold_max, 100)
        
        plot_data = []
        
        for threshold_val in threshold_grid:
            # Determine regime
            if self.results.threshold_type == ThresholdType.SINGLE_THRESHOLD:
                regime = 'low' if threshold_val <= self.results.threshold_values[0] else 'high'
            elif self.results.threshold_type == ThresholdType.DOUBLE_THRESHOLD:
                thresh_low, thresh_high = self.results.threshold_values
                if threshold_val <= thresh_low:
                    regime = 'low'
                elif threshold_val <= thresh_high:
                    regime = 'middle'
                else:
                    regime = 'high'
                    
            spatial_effect = self.results.regime_spatial_parameters.get(regime, 0.0)
            
            plot_data.append({
                'threshold_value': threshold_val,
                'spatial_parameter': spatial_effect,
                'regime': regime
            })
            
        return pd.DataFrame(plot_data)