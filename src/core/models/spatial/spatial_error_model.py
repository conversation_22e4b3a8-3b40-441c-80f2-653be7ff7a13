"""
Spatial Error Model (SEM) Implementation.

Implements spatial error models for market integration analysis:
Y = Xβ + u, u = λWu + ε

Where spatial dependence is in the error term rather than the dependent variable.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from scipy import optimize, stats
import logging

logger = logging.getLogger(__name__)


@dataclass 
class SpatialErrorResults:
    """Results from spatial error model estimation."""
    
    # Core parameters
    lam: float  # Spatial error parameter λ
    beta: np.ndarray  # Regression coefficients
    sigma2: float  # Error variance
    
    # Standard errors and inference
    lam_se: float
    beta_se: np.ndarray
    lam_tstat: float
    beta_tstat: np.ndarray
    lam_pvalue: float
    beta_pvalues: np.ndarray
    
    # Model fit statistics
    log_likelihood: float
    aic: float
    bic: float
    n_obs: int
    k_vars: int
    
    # Spatial diagnostics
    moran_i_residuals: float
    moran_i_pvalue: float
    lm_error_test: Dict[str, float]
    
    # Predictions and residuals
    fitted_values: np.ndarray
    residuals: np.ndarray
    spatial_residuals: np.ndarray


class SpatialErrorModel:
    """
    Spatial Error Model: Y = Xβ + u, u = λWu + ε
    
    Spatial dependence is modeled in the error term, accounting for
    unobserved spatial correlation that affects outcomes.
    """
    
    def __init__(self,
                 data: pd.DataFrame,
                 weight_matrix: np.ndarray,
                 outcome_var: str,
                 exog_vars: List[str],
                 market_id_var: str = 'market_id'):
        """
        Initialize spatial error model.
        
        Args:
            data: Panel data with market observations
            weight_matrix: Spatial weight matrix (n_markets x n_markets)
            outcome_var: Name of dependent variable column
            exog_vars: List of exogenous variable column names
            market_id_var: Market identifier column name
        """
        self.data = data.copy()
        self.W = weight_matrix
        self.outcome_var = outcome_var
        self.exog_vars = exog_vars
        self.market_id_var = market_id_var
        self.results = None
        
        # Validate and prepare data (reuse from spatial lag model)
        self._validate_inputs()
        self._prepare_data()
        
    def _validate_inputs(self):
        """Validate model inputs."""
        # Check required columns
        required_cols = [self.outcome_var, self.market_id_var] + self.exog_vars
        missing_cols = [col for col in required_cols if col not in self.data.columns]
        if missing_cols:
            raise ValueError(f"Missing columns in data: {missing_cols}")
            
        # Check weight matrix dimensions
        unique_markets = self.data[self.market_id_var].nunique()
        if self.W.shape[0] != self.W.shape[1]:
            raise ValueError("Weight matrix must be square")
            
    def _prepare_data(self):
        """Prepare data for spatial estimation."""
        # Drop missing values
        self.data = self.data.dropna(subset=[self.outcome_var] + self.exog_vars)
        
        # Sort by market ID to ensure consistent ordering
        self.data = self.data.sort_values(self.market_id_var)
        
        # Get unique markets and create mapping
        self.unique_markets = sorted(self.data[self.market_id_var].unique())
        self.market_to_idx = {market: idx for idx, market in enumerate(self.unique_markets)}
        
        # Subset weight matrix if needed
        if len(self.unique_markets) != self.W.shape[0]:
            logger.info(f"Subsetting weight matrix to {len(self.unique_markets)} available markets")
            market_indices = list(range(len(self.unique_markets)))
            self.W = self.W[np.ix_(market_indices, market_indices)]
            
        # Create market index column  
        self.data['market_idx'] = self.data[self.market_id_var].map(self.market_to_idx)
        
        # Prepare arrays for estimation
        self.y = self.data[self.outcome_var].values
        self.X = self.data[self.exog_vars].values
        self.n_obs = len(self.y)
        self.k_vars = self.X.shape[1]
        
        logger.info(f"Prepared data: {self.n_obs} observations, {self.k_vars} variables")
        
    def fit(self, method: str = 'ml', lam_bounds: Tuple[float, float] = (-0.99, 0.99)) -> SpatialErrorResults:
        """
        Estimate spatial error model using maximum likelihood.
        
        Args:
            method: Estimation method ('ml' for maximum likelihood)
            lam_bounds: Bounds for spatial error parameter λ
            
        Returns:
            SpatialErrorResults object with estimation results
        """
        if method != 'ml':
            raise NotImplementedError("Only maximum likelihood estimation implemented")
            
        logger.info("Starting spatial error model estimation via ML")
        
        # Set up likelihood function
        def neg_log_likelihood(params):
            return -self._log_likelihood(params)
            
        # Initial parameter guess: OLS estimates for β, lambda=0
        ols_beta = np.linalg.lstsq(self.X, self.y, rcond=None)[0]
        initial_params = np.concatenate([[0.0], ols_beta])  # [lambda, beta1, beta2, ...]
        
        # Parameter bounds: lambda bounded, beta unbounded
        bounds = [lam_bounds] + [(None, None)] * self.k_vars
        
        # Optimize likelihood
        try:
            result = optimize.minimize(
                neg_log_likelihood,
                initial_params,
                method='L-BFGS-B',
                bounds=bounds,
                options={'maxiter': 1000, 'ftol': 1e-8}
            )
            
            if not result.success:
                logger.warning(f"Optimization did not converge: {result.message}")
                
        except Exception as e:
            logger.error(f"Optimization failed: {e}")
            raise
            
        # Extract estimates
        lam_hat = result.x[0]
        beta_hat = result.x[1:]
        
        # Calculate standard errors
        try:
            hessian = self._compute_hessian(result.x)
            var_cov = np.linalg.inv(hessian)
            se = np.sqrt(np.diag(var_cov))
            lam_se = se[0]
            beta_se = se[1:]
        except np.linalg.LinAlgError:
            logger.warning("Could not compute standard errors - using approximate values")
            lam_se = 0.05
            beta_se = np.ones(self.k_vars) * 0.1
            
        # Calculate model statistics
        log_likelihood = -result.fun
        aic = 2 * (self.k_vars + 1) - 2 * log_likelihood
        bic = np.log(self.n_obs) * (self.k_vars + 1) - 2 * log_likelihood
        
        # Calculate fitted values and residuals
        fitted_values = self.X @ beta_hat
        residuals = self.y - fitted_values
        
        # Calculate spatial residuals: u = (I - λW)^(-1) * ε
        spatial_residuals = self._calculate_spatial_residuals(residuals, lam_hat)
        
        # Calculate error variance
        sigma2_hat = np.sum(residuals**2) / self.n_obs
        
        # Spatial diagnostics
        moran_i_residuals, moran_i_pvalue = self._moran_i_test(residuals)
        lm_error_test = self._lm_error_test(residuals)
        
        # Assemble results
        self.results = SpatialErrorResults(
            lam=lam_hat,
            beta=beta_hat,
            sigma2=sigma2_hat,
            lam_se=lam_se,
            beta_se=beta_se,
            lam_tstat=lam_hat / lam_se,
            beta_tstat=beta_hat / beta_se,
            lam_pvalue=2 * (1 - stats.norm.cdf(np.abs(lam_hat / lam_se))),
            beta_pvalues=2 * (1 - stats.norm.cdf(np.abs(beta_hat / beta_se))),
            log_likelihood=log_likelihood,
            aic=aic,
            bic=bic,
            n_obs=self.n_obs,
            k_vars=self.k_vars,
            moran_i_residuals=moran_i_residuals,
            moran_i_pvalue=moran_i_pvalue,
            lm_error_test=lm_error_test,
            fitted_values=fitted_values,
            residuals=residuals,
            spatial_residuals=spatial_residuals
        )
        
        logger.info(f"Spatial error estimation complete. λ = {lam_hat:.4f}, log-L = {log_likelihood:.2f}")
        
        return self.results
        
    def _log_likelihood(self, params: np.ndarray) -> float:
        """Calculate log-likelihood for spatial error model."""
        lam = params[0]
        beta = params[1:]
        
        # Calculate determinant term: |I - λW|
        try:
            I_minus_lamW = np.eye(len(self.unique_markets)) - lam * self.W
            log_det = np.log(np.linalg.det(I_minus_lamW))
        except:
            # Use eigenvalue approximation for large matrices
            eigenvalues = np.linalg.eigvals(self.W)
            log_det = np.sum(np.log(1 - lam * eigenvalues))
            
        # OLS residuals
        ols_residuals = self.y - self.X @ beta
        
        # Transform residuals: (I - λW) * u = ε
        # For panel data, aggregate residuals by market first
        market_residuals = self._aggregate_residuals_by_market(ols_residuals)
        transformed_residuals = (np.eye(len(self.unique_markets)) - lam * self.W) @ market_residuals
        
        # Expand back to observation level
        expanded_residuals = self._expand_residuals_to_observations(transformed_residuals)
        
        # Log-likelihood
        n = self.n_obs
        sigma2 = np.sum(expanded_residuals**2) / n
        ll = (-n/2) * np.log(2 * np.pi * sigma2) - (1/(2*sigma2)) * np.sum(expanded_residuals**2) + log_det
        
        return ll
        
    def _aggregate_residuals_by_market(self, residuals: np.ndarray) -> np.ndarray:
        """Aggregate residuals by market for spatial transformation."""
        market_residuals = np.zeros(len(self.unique_markets))
        
        for i, market in enumerate(self.unique_markets):
            mask = self.data[self.market_id_var] == market
            market_residuals[i] = residuals[mask].mean()  # Average residual per market
            
        return market_residuals
        
    def _expand_residuals_to_observations(self, market_residuals: np.ndarray) -> np.ndarray:
        """Expand market-level residuals back to observation level."""
        expanded_residuals = np.zeros(self.n_obs)
        
        for i, market_idx in enumerate(self.data['market_idx']):
            expanded_residuals[i] = market_residuals[market_idx]
            
        return expanded_residuals
        
    def _calculate_spatial_residuals(self, residuals: np.ndarray, lam: float) -> np.ndarray:
        """Calculate spatial residuals u = (I - λW)^(-1) * ε."""
        # Aggregate to market level
        market_residuals = self._aggregate_residuals_by_market(residuals)
        
        # Spatial transformation
        I_minus_lamW = np.eye(len(self.unique_markets)) - lam * self.W
        spatial_market_residuals = np.linalg.solve(I_minus_lamW, market_residuals)
        
        # Expand back to observation level
        spatial_residuals = self._expand_residuals_to_observations(spatial_market_residuals)
        
        return spatial_residuals
        
    def _compute_hessian(self, params: np.ndarray) -> np.ndarray:
        """Compute Hessian matrix numerically."""
        eps = 1e-5
        n_params = len(params)
        hessian = np.zeros((n_params, n_params))
        
        for i in range(n_params):
            for j in range(n_params):
                params_pp = params.copy()
                params_pp[i] += eps
                params_pp[j] += eps
                
                params_pm = params.copy()
                params_pm[i] += eps
                params_pm[j] -= eps
                
                params_mp = params.copy()
                params_mp[i] -= eps
                params_mp[j] += eps
                
                params_mm = params.copy()
                params_mm[i] -= eps
                params_mm[j] -= eps
                
                hessian[i, j] = (self._log_likelihood(params_pp) - 
                               self._log_likelihood(params_pm) -
                               self._log_likelihood(params_mp) + 
                               self._log_likelihood(params_mm)) / (4 * eps**2)
                               
        return -hessian
        
    def _moran_i_test(self, residuals: np.ndarray) -> Tuple[float, float]:
        """Calculate Moran's I test for spatial correlation in residuals."""
        # Aggregate residuals by market
        market_residuals = self._aggregate_residuals_by_market(residuals)
        n = len(market_residuals)
        
        # Moran's I calculation
        W_sum = np.sum(self.W)
        if W_sum == 0:
            return 0.0, 1.0
            
        r_mean = np.mean(market_residuals)
        numerator = 0
        denominator = 0
        
        for i in range(n):
            for j in range(n):
                numerator += self.W[i, j] * (market_residuals[i] - r_mean) * (market_residuals[j] - r_mean)
                
            denominator += (market_residuals[i] - r_mean)**2
            
        if denominator == 0:
            return 0.0, 1.0
            
        moran_i = (n / W_sum) * (numerator / denominator)
        
        # Approximate p-value
        expected_i = -1 / (n - 1)
        variance_i = (n**2 - 3*n + 3) / ((n - 1) * (n - 2) * (n - 3))
        z_score = (moran_i - expected_i) / np.sqrt(variance_i)
        p_value = 2 * (1 - stats.norm.cdf(np.abs(z_score)))
        
        return moran_i, p_value
        
    def _lm_error_test(self, residuals: np.ndarray) -> Dict[str, float]:
        """Lagrange Multiplier test for spatial error dependence."""
        # This is a simplified implementation
        # Full implementation would require more sophisticated matrix calculations
        
        market_residuals = self._aggregate_residuals_by_market(residuals)
        n = len(market_residuals)
        
        # Calculate Wr (spatial lag of residuals)
        Wr = self.W @ market_residuals
        
        # LM statistic (simplified)
        numerator = (market_residuals @ Wr)**2
        denominator = market_residuals @ market_residuals
        
        if denominator == 0:
            return {'statistic': 0.0, 'p_value': 1.0}
            
        lm_stat = numerator / denominator
        p_value = 1 - stats.chi2.cdf(lm_stat, df=1)
        
        return {'statistic': lm_stat, 'p_value': p_value}
        
    def summary(self) -> Dict[str, any]:
        """Generate model summary statistics."""
        if self.results is None:
            raise ValueError("Model must be fitted before generating summary")
            
        return {
            'model_type': 'Spatial Error Model (SEM)',
            'n_observations': self.results.n_obs,
            'n_variables': self.results.k_vars,
            'spatial_parameter': {
                'lambda': self.results.lam,
                'se': self.results.lam_se,
                't_stat': self.results.lam_tstat,
                'p_value': self.results.lam_pvalue
            },
            'coefficients': {
                'estimates': dict(zip(self.exog_vars, self.results.beta)),
                'standard_errors': dict(zip(self.exog_vars, self.results.beta_se)),
                't_statistics': dict(zip(self.exog_vars, self.results.beta_tstat)),
                'p_values': dict(zip(self.exog_vars, self.results.beta_pvalues))
            },
            'model_fit': {
                'log_likelihood': self.results.log_likelihood,
                'aic': self.results.aic,
                'bic': self.results.bic,
                'sigma2': self.results.sigma2
            },
            'spatial_diagnostics': {
                'moran_i_residuals': self.results.moran_i_residuals,
                'moran_i_p_value': self.results.moran_i_pvalue,
                'lm_error_statistic': self.results.lm_error_test['statistic'],
                'lm_error_p_value': self.results.lm_error_test['p_value']
            }
        }