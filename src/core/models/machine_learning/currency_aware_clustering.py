"""
Currency-Aware Market Clustering for Yemen Market Integration

This module implements a specialized clustering approach that respects currency zone
boundaries as hard constraints. This is critical because markets in different 
currency zones (North: ~535 YER/USD, South: ~2000+ YER/USD) operate in 
fundamentally different economic environments.

Key Features:
1. Hard constraint enforcement: Markets from different zones NEVER cluster together
2. Zone-specific feature normalization
3. Cross-zone integration analysis
4. Validation against methodology requirements
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass
from datetime import datetime
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
from sklearn.ensemble import RandomForestClassifier
import logging
import warnings

from src.core.domain.market.currency_zones import CurrencyZone
from src.core.validation.methodology_validator import MethodologyValidator, MethodologyViolation

logger = logging.getLogger(__name__)


@dataclass
class CurrencyAwareClusteringResults:
    """Results from currency-aware market clustering analysis."""
    # Basic clustering info
    algorithm: str
    total_clusters: int
    clusters_by_zone: Dict[str, int]  # Zone -> number of clusters
    
    # Cluster assignments
    cluster_labels: np.ndarray
    zone_cluster_mapping: Dict[str, List[int]]  # Zone -> list of cluster IDs
    
    # Quality metrics
    within_zone_silhouette: Dict[str, float]  # Silhouette score within each zone
    overall_silhouette: float
    calinski_harabasz_score: float
    davies_bouldin_score: float
    
    # Cluster characteristics
    cluster_profiles: Dict[int, Dict[str, Any]]
    zone_integration_matrix: pd.DataFrame  # Cross-zone integration strengths
    
    # Feature importance by zone
    feature_importance_by_zone: Dict[str, Dict[str, float]]
    
    # Validation
    methodology_compliance: bool
    validation_report: Optional[str] = None
    
    # Temporal stability
    temporal_stability: Optional[Dict[int, float]] = None
    bootstrap_stability: Optional[float] = None


class CurrencyAwareMarketClustering:
    """
    Implements market clustering that strictly respects currency zone boundaries.
    
    This is critical for Yemen analysis because:
    1. Northern markets (Houthi, ~535 YER/USD) face different economic realities 
       than Southern markets (Government, ~2000 YER/USD)
    2. Cross-zone comparisons require proper currency conversion
    3. Aid allocation must account for zone-specific conditions
    4. Market integration patterns differ fundamentally by zone
    
    The clustering ensures that markets are NEVER grouped across currency zones
    without proper economic justification and currency conversion.
    """
    
    def __init__(self,
                 n_clusters_per_zone: Optional[Dict[str, int]] = None,
                 min_cluster_size: int = 5,
                 clustering_algorithm: str = 'kmeans',
                 enforce_zone_constraints: bool = True,
                 validate_methodology: bool = True):
        """
        Initialize currency-aware clustering.
        
        Args:
            n_clusters_per_zone: Number of clusters for each zone 
                                 (None for automatic selection)
            min_cluster_size: Minimum markets per cluster
            clustering_algorithm: Algorithm to use within zones
            enforce_zone_constraints: If True, hard boundary between zones
            validate_methodology: If True, validate against methodology requirements
        """
        self.n_clusters_per_zone = n_clusters_per_zone or {}
        self.min_cluster_size = min_cluster_size
        self.clustering_algorithm = clustering_algorithm
        self.enforce_zone_constraints = enforce_zone_constraints
        self.validate_methodology = validate_methodology
        
        # Zone-specific scalers
        self.scalers = {}
        self.models = {}
        self.fitted = False
        
        # Methodology validator
        if self.validate_methodology:
            self.validator = MethodologyValidator()
    
    def fit(self, 
            panel_data: pd.DataFrame,
            conflict_data: Optional[pd.DataFrame] = None,
            geographic_data: Optional[pd.DataFrame] = None) -> 'CurrencyAwareMarketClustering':
        """
        Fit clustering model respecting currency zones.
        
        Args:
            panel_data: Must include price_usd, currency_zone, exchange_rate_used
            conflict_data: Optional conflict event data
            geographic_data: Optional geographic features
            
        Returns:
            Self for chaining
            
        Raises:
            MethodologyViolation: If data fails validation
        """
        logger.info("Fitting currency-aware clustering model")
        
        # Validate methodology compliance
        if self.validate_methodology:
            is_valid, report = self.validator.validate_analysis_inputs(
                observations=panel_data,
                analysis_type="clustering"
            )
            if not is_valid:
                raise MethodologyViolation(
                    f"Data fails methodology requirements: {report.critical_failures}"
                )
        
        # Extract features for each market
        features_df = self._create_market_features(
            panel_data, conflict_data, geographic_data
        )
        
        # Cluster within each currency zone
        all_labels = np.zeros(len(features_df), dtype=int)
        cluster_offset = 0
        
        for zone in features_df['currency_zone'].unique():
            logger.info(f"Clustering markets in {zone} zone")
            
            zone_mask = features_df['currency_zone'] == zone
            zone_features = features_df[zone_mask]
            
            if len(zone_features) < self.min_cluster_size:
                logger.warning(
                    f"Zone {zone} has only {len(zone_features)} markets, "
                    f"less than minimum {self.min_cluster_size}"
                )
                # Assign all to single cluster
                all_labels[zone_mask] = cluster_offset
                cluster_offset += 1
                continue
            
            # Fit zone-specific model
            zone_labels = self._fit_zone_clustering(
                zone, zone_features, cluster_offset
            )
            
            all_labels[zone_mask] = zone_labels
            cluster_offset = zone_labels.max() + 1
        
        self.cluster_labels_ = all_labels
        self.features_df_ = features_df
        self.fitted = True
        
        logger.info(
            f"Clustering complete: {len(np.unique(all_labels))} total clusters "
            f"across {len(features_df['currency_zone'].unique())} currency zones"
        )
        
        return self
    
    def _create_market_features(self,
                               panel_data: pd.DataFrame,
                               conflict_data: Optional[pd.DataFrame],
                               geographic_data: Optional[pd.DataFrame]) -> pd.DataFrame:
        """Create feature matrix for clustering with currency zone awareness."""
        logger.info("Creating currency-aware market features")
        
        features_list = []
        
        # Group by market
        for market_id, market_data in panel_data.groupby('market_id'):
            # Ensure we have USD prices and exchange rates
            if 'price_usd' not in market_data.columns:
                raise MethodologyViolation(
                    f"Market {market_id} missing USD prices - currency conversion required"
                )
            
            # Extract features
            features = {
                'market_id': market_id,
                'currency_zone': market_data['currency_zone'].mode().iloc[0],
                
                # Price features (all in USD for comparability)
                'avg_price_usd': market_data['price_usd'].mean(),
                'price_volatility_usd': market_data['price_usd'].std(),
                'price_trend_usd': self._calculate_trend(market_data['price_usd']),
                
                # Exchange rate features (zone-specific)
                'avg_exchange_rate': market_data['exchange_rate_used'].mean(),
                'exchange_rate_volatility': market_data['exchange_rate_used'].std(),
                'exchange_rate_trend': self._calculate_trend(market_data['exchange_rate_used']),
                
                # Price in local currency (for within-zone comparison)
                'avg_price_yer': market_data.get('price_yer', 
                                                 market_data['price_usd'] * market_data['exchange_rate_used']).mean(),
                
                # Data quality
                'observation_count': len(market_data),
                'data_completeness': 1.0 - market_data['price_usd'].isna().mean()
            }
            
            # Add conflict features if available
            if conflict_data is not None and market_id in conflict_data['market_id'].values:
                market_conflict = conflict_data[conflict_data['market_id'] == market_id]
                features.update({
                    'conflict_intensity': market_conflict['event_count'].mean(),
                    'conflict_volatility': market_conflict['event_count'].std(),
                    'control_stability': 1.0 - market_conflict['control_change'].mean()
                })
            else:
                features.update({
                    'conflict_intensity': 0.0,
                    'conflict_volatility': 0.0,
                    'control_stability': 1.0
                })
            
            # Add geographic features if available
            if geographic_data is not None and market_id in geographic_data.index:
                geo = geographic_data.loc[market_id]
                features.update({
                    'distance_to_capital': geo.get('distance_to_capital', 100.0),
                    'distance_to_port': geo.get('distance_to_port', 100.0),
                    'distance_to_border': geo.get('distance_to_border', 50.0),
                    'urban_rural': geo.get('urban_rural', 0.5),
                    'road_quality': geo.get('road_quality', 0.5)
                })
            else:
                features.update({
                    'distance_to_capital': 100.0,
                    'distance_to_port': 100.0,
                    'distance_to_border': 50.0,
                    'urban_rural': 0.5,
                    'road_quality': 0.5
                })
            
            features_list.append(features)
        
        features_df = pd.DataFrame(features_list)
        
        # Add zone-specific features
        features_df = self._add_zone_specific_features(features_df, panel_data)
        
        logger.info(
            f"Created features for {len(features_df)} markets "
            f"across {features_df['currency_zone'].nunique()} zones"
        )
        
        return features_df
    
    def _calculate_trend(self, series: pd.Series) -> float:
        """Calculate linear trend coefficient."""
        if len(series) < 3:
            return 0.0
        
        x = np.arange(len(series))
        y = series.values
        
        # Remove NaN values
        mask = ~np.isnan(y)
        if mask.sum() < 3:
            return 0.0
        
        x = x[mask]
        y = y[mask]
        
        # Linear regression
        coef = np.polyfit(x, y, 1)[0]
        return float(coef)
    
    def _add_zone_specific_features(self, 
                                   features_df: pd.DataFrame,
                                   panel_data: pd.DataFrame) -> pd.DataFrame:
        """Add features specific to currency zone dynamics."""
        # Calculate zone-level statistics
        zone_stats = panel_data.groupby('currency_zone').agg({
            'price_usd': ['mean', 'std'],
            'exchange_rate_used': ['mean', 'std']
        })
        
        # Add relative features
        for zone in features_df['currency_zone'].unique():
            zone_mask = features_df['currency_zone'] == zone
            
            # Price relative to zone average
            zone_avg_price = zone_stats.loc[zone, ('price_usd', 'mean')]
            features_df.loc[zone_mask, 'price_relative_to_zone'] = (
                features_df.loc[zone_mask, 'avg_price_usd'] / zone_avg_price
            )
            
            # Exchange rate stability relative to zone
            zone_er_std = zone_stats.loc[zone, ('exchange_rate_used', 'std')]
            features_df.loc[zone_mask, 'er_stability_relative'] = (
                1.0 - features_df.loc[zone_mask, 'exchange_rate_volatility'] / (zone_er_std + 1e-6)
            )
        
        return features_df
    
    def _fit_zone_clustering(self, 
                            zone: str,
                            zone_features: pd.DataFrame,
                            cluster_offset: int) -> np.ndarray:
        """Fit clustering model for a specific currency zone."""
        # Prepare features
        feature_cols = [
            'avg_price_usd', 'price_volatility_usd', 'price_trend_usd',
            'exchange_rate_volatility', 'exchange_rate_trend',
            'conflict_intensity', 'conflict_volatility', 'control_stability',
            'distance_to_capital', 'distance_to_port', 'distance_to_border',
            'price_relative_to_zone', 'er_stability_relative'
        ]
        
        X = zone_features[feature_cols].values
        
        # Zone-specific scaling
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        self.scalers[zone] = scaler
        
        # Determine number of clusters for this zone
        if zone in self.n_clusters_per_zone:
            n_clusters = self.n_clusters_per_zone[zone]
        else:
            n_clusters = self._find_optimal_clusters_for_zone(X_scaled, zone)
            self.n_clusters_per_zone[zone] = n_clusters
        
        logger.info(f"Using {n_clusters} clusters for {zone} zone")
        
        # Fit clustering model
        if self.clustering_algorithm == 'kmeans':
            model = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        elif self.clustering_algorithm == 'hierarchical':
            model = AgglomerativeClustering(n_clusters=n_clusters)
        elif self.clustering_algorithm == 'dbscan':
            # DBSCAN with adaptive eps
            eps = self._calculate_adaptive_eps(X_scaled)
            model = DBSCAN(eps=eps, min_samples=self.min_cluster_size)
        else:
            raise ValueError(f"Unknown algorithm: {self.clustering_algorithm}")
        
        # Fit and get labels
        zone_labels = model.fit_predict(X_scaled)
        
        # Offset labels to ensure global uniqueness
        zone_labels = zone_labels + cluster_offset
        
        self.models[zone] = model
        
        return zone_labels
    
    def _find_optimal_clusters_for_zone(self, X_scaled: np.ndarray, zone: str) -> int:
        """Find optimal number of clusters for a specific zone."""
        max_clusters = min(10, len(X_scaled) // self.min_cluster_size)
        
        if max_clusters < 2:
            return 1
        
        scores = []
        for k in range(2, max_clusters + 1):
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            labels = kmeans.fit_predict(X_scaled)
            
            if len(np.unique(labels)) > 1:
                score = silhouette_score(X_scaled, labels)
                scores.append((k, score))
        
        if not scores:
            return 2
        
        # Find best score
        best_k = max(scores, key=lambda x: x[1])[0]
        
        logger.info(f"Optimal clusters for {zone}: {best_k} (silhouette: {max(scores, key=lambda x: x[1])[1]:.3f})")
        
        return best_k
    
    def _calculate_adaptive_eps(self, X_scaled: np.ndarray) -> float:
        """Calculate adaptive eps for DBSCAN based on data density."""
        # Calculate k-nearest neighbor distances
        from sklearn.neighbors import NearestNeighbors
        
        k = min(self.min_cluster_size, len(X_scaled) - 1)
        nbrs = NearestNeighbors(n_neighbors=k).fit(X_scaled)
        distances, _ = nbrs.kneighbors(X_scaled)
        
        # Use knee point of sorted k-distances
        k_distances = np.sort(distances[:, -1])
        
        # Simple knee detection (can be improved)
        eps = np.percentile(k_distances, 90)
        
        return float(eps)
    
    def analyze_results(self) -> CurrencyAwareClusteringResults:
        """
        Analyze clustering results with currency zone awareness.
        
        Returns:
            Comprehensive analysis results
            
        Raises:
            ValueError: If model not fitted
        """
        if not self.fitted:
            raise ValueError("Model must be fitted before analysis")
        
        logger.info("Analyzing currency-aware clustering results")
        
        # Calculate metrics by zone
        within_zone_silhouette = {}
        zone_cluster_mapping = {}
        
        for zone in self.features_df_['currency_zone'].unique():
            zone_mask = self.features_df_['currency_zone'] == zone
            zone_features = self.features_df_[zone_mask]
            zone_labels = self.cluster_labels_[zone_mask]
            
            if len(np.unique(zone_labels)) > 1:
                # Prepare features
                feature_cols = [col for col in self.features_df_.columns 
                               if col not in ['market_id', 'currency_zone']]
                X_zone = zone_features[feature_cols].values
                X_scaled = self.scalers[zone].transform(X_zone)
                
                # Calculate silhouette score
                silhouette = silhouette_score(X_scaled, zone_labels)
                within_zone_silhouette[zone] = float(silhouette)
            else:
                within_zone_silhouette[zone] = 0.0
            
            # Map clusters to zones
            zone_cluster_mapping[zone] = list(np.unique(zone_labels))
        
        # Overall metrics
        feature_cols = [col for col in self.features_df_.columns 
                       if col not in ['market_id', 'currency_zone']]
        X_all = self.features_df_[feature_cols].values
        
        # Normalize across all data for overall metrics
        scaler_all = StandardScaler()
        X_all_scaled = scaler_all.fit_transform(X_all)
        
        overall_silhouette = silhouette_score(X_all_scaled, self.cluster_labels_)
        calinski = calinski_harabasz_score(X_all_scaled, self.cluster_labels_)
        davies_bouldin = davies_bouldin_score(X_all_scaled, self.cluster_labels_)
        
        # Create cluster profiles
        cluster_profiles = self._create_cluster_profiles()
        
        # Calculate zone integration matrix
        zone_integration_matrix = self._calculate_zone_integration()
        
        # Feature importance by zone
        feature_importance_by_zone = self._calculate_feature_importance_by_zone()
        
        # Count clusters by zone
        clusters_by_zone = {}
        for zone, clusters in zone_cluster_mapping.items():
            clusters_by_zone[zone] = len(clusters)
        
        # Temporal stability (if we have time series)
        temporal_stability = self._assess_temporal_stability()
        
        # Methodology compliance
        methodology_compliance = self._check_methodology_compliance()
        
        return CurrencyAwareClusteringResults(
            algorithm=self.clustering_algorithm,
            total_clusters=len(np.unique(self.cluster_labels_)),
            clusters_by_zone=clusters_by_zone,
            cluster_labels=self.cluster_labels_,
            zone_cluster_mapping=zone_cluster_mapping,
            within_zone_silhouette=within_zone_silhouette,
            overall_silhouette=float(overall_silhouette),
            calinski_harabasz_score=float(calinski),
            davies_bouldin_score=float(davies_bouldin),
            cluster_profiles=cluster_profiles,
            zone_integration_matrix=zone_integration_matrix,
            feature_importance_by_zone=feature_importance_by_zone,
            methodology_compliance=methodology_compliance,
            temporal_stability=temporal_stability
        )
    
    def _create_cluster_profiles(self) -> Dict[int, Dict[str, Any]]:
        """Create detailed profiles for each cluster."""
        profiles = {}
        
        for cluster_id in np.unique(self.cluster_labels_):
            cluster_mask = self.cluster_labels_ == cluster_id
            cluster_features = self.features_df_[cluster_mask]
            
            profile = {
                'size': int(cluster_mask.sum()),
                'currency_zone': cluster_features['currency_zone'].iloc[0],  # All same zone
                'markets': list(cluster_features['market_id']),
                
                # Price characteristics in USD
                'avg_price_usd': float(cluster_features['avg_price_usd'].mean()),
                'price_volatility_usd': float(cluster_features['price_volatility_usd'].mean()),
                'price_trend_usd': float(cluster_features['price_trend_usd'].mean()),
                
                # Exchange rate characteristics
                'avg_exchange_rate': float(cluster_features['avg_exchange_rate'].mean()),
                'er_volatility': float(cluster_features['exchange_rate_volatility'].mean()),
                
                # Conflict profile
                'conflict_intensity': float(cluster_features['conflict_intensity'].mean()),
                'control_stability': float(cluster_features['control_stability'].mean()),
                
                # Geographic spread
                'geographic_diversity': float(cluster_features[['distance_to_capital', 
                                                               'distance_to_port', 
                                                               'distance_to_border']].std().mean()),
                
                # Data quality
                'avg_data_completeness': float(cluster_features['data_completeness'].mean())
            }
            
            profiles[int(cluster_id)] = profile
        
        return profiles
    
    def _calculate_zone_integration(self) -> pd.DataFrame:
        """Calculate integration strength between currency zones."""
        zones = self.features_df_['currency_zone'].unique()
        
        # Create empty matrix
        integration_matrix = pd.DataFrame(
            index=zones,
            columns=zones,
            data=0.0
        )
        
        # Fill diagonal (within-zone integration)
        for zone in zones:
            zone_mask = self.features_df_['currency_zone'] == zone
            zone_clusters = len(np.unique(self.cluster_labels_[zone_mask]))
            
            # More clusters = less integration
            integration_score = 1.0 / zone_clusters if zone_clusters > 0 else 0.0
            integration_matrix.loc[zone, zone] = integration_score
        
        # Cross-zone integration (always 0 with hard constraints)
        if self.enforce_zone_constraints:
            # Off-diagonal remains 0
            pass
        else:
            # Would calculate based on price correlations if allowed
            logger.warning("Cross-zone integration calculation not implemented for soft constraints")
        
        return integration_matrix
    
    def _calculate_feature_importance_by_zone(self) -> Dict[str, Dict[str, float]]:
        """Calculate feature importance for clustering within each zone."""
        importance_by_zone = {}
        
        feature_cols = [
            'avg_price_usd', 'price_volatility_usd', 'price_trend_usd',
            'exchange_rate_volatility', 'exchange_rate_trend',
            'conflict_intensity', 'conflict_volatility', 'control_stability',
            'distance_to_capital', 'distance_to_port', 'distance_to_border',
            'price_relative_to_zone', 'er_stability_relative'
        ]
        
        for zone in self.features_df_['currency_zone'].unique():
            zone_mask = self.features_df_['currency_zone'] == zone
            zone_features = self.features_df_[zone_mask]
            zone_labels = self.cluster_labels_[zone_mask]
            
            if len(np.unique(zone_labels)) <= 1:
                # Single cluster, no feature importance
                importance_by_zone[zone] = {col: 0.0 for col in feature_cols}
                continue
            
            # Use Random Forest to assess feature importance
            X = zone_features[feature_cols].values
            X_scaled = self.scalers[zone].transform(X)
            
            rf = RandomForestClassifier(n_estimators=100, random_state=42)
            rf.fit(X_scaled, zone_labels)
            
            # Get importance
            importance = dict(zip(feature_cols, rf.feature_importances_))
            
            # Normalize
            total = sum(importance.values())
            if total > 0:
                importance = {k: v/total for k, v in importance.items()}
            
            importance_by_zone[zone] = importance
        
        return importance_by_zone
    
    def _assess_temporal_stability(self) -> Dict[int, float]:
        """Assess temporal stability of clusters."""
        # Simplified assessment based on volatility measures
        stability = {}
        
        for cluster_id in np.unique(self.cluster_labels_):
            cluster_mask = self.cluster_labels_ == cluster_id
            cluster_features = self.features_df_[cluster_mask]
            
            # Stability based on various volatility measures
            price_stability = 1.0 / (1.0 + cluster_features['price_volatility_usd'].mean() / 100)
            er_stability = 1.0 / (1.0 + cluster_features['exchange_rate_volatility'].mean() / 100)
            conflict_stability = cluster_features['control_stability'].mean()
            
            # Combined stability score
            overall_stability = (price_stability + er_stability + conflict_stability) / 3
            
            stability[int(cluster_id)] = float(overall_stability)
        
        return stability
    
    def _check_methodology_compliance(self) -> bool:
        """Check if clustering respects methodology requirements."""
        # Check 1: All markets have currency zones
        if self.features_df_['currency_zone'].isna().any():
            return False
        
        # Check 2: No cluster spans multiple zones (if enforcing constraints)
        if self.enforce_zone_constraints:
            for cluster_id in np.unique(self.cluster_labels_):
                cluster_mask = self.cluster_labels_ == cluster_id
                zones_in_cluster = self.features_df_[cluster_mask]['currency_zone'].unique()
                if len(zones_in_cluster) > 1:
                    return False
        
        # Check 3: All prices in USD
        if 'avg_price_usd' not in self.features_df_.columns:
            return False
        
        return True
    
    def predict(self, new_markets: pd.DataFrame) -> np.ndarray:
        """
        Predict cluster assignments for new markets.
        
        Args:
            new_markets: DataFrame with same features as training data
            
        Returns:
            Array of cluster assignments
        """
        if not self.fitted:
            raise ValueError("Model must be fitted before prediction")
        
        predictions = []
        
        for _, market in new_markets.iterrows():
            zone = market['currency_zone']
            
            if zone not in self.models:
                raise ValueError(f"Unknown currency zone: {zone}")
            
            # Prepare features
            feature_cols = [
                'avg_price_usd', 'price_volatility_usd', 'price_trend_usd',
                'exchange_rate_volatility', 'exchange_rate_trend',
                'conflict_intensity', 'conflict_volatility', 'control_stability',
                'distance_to_capital', 'distance_to_port', 'distance_to_border',
                'price_relative_to_zone', 'er_stability_relative'
            ]
            
            X = market[feature_cols].values.reshape(1, -1)
            X_scaled = self.scalers[zone].transform(X)
            
            # Predict using zone-specific model
            if hasattr(self.models[zone], 'predict'):
                cluster = self.models[zone].predict(X_scaled)[0]
            else:
                # For algorithms without predict
                raise NotImplementedError(
                    f"Prediction not available for {self.clustering_algorithm}"
                )
            
            predictions.append(cluster)
        
        return np.array(predictions)
    
    def visualize_results(self, save_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Create comprehensive visualizations of clustering results.
        
        Args:
            save_path: Optional path to save figures
            
        Returns:
            Dictionary of matplotlib figures
        """
        import matplotlib.pyplot as plt
        import seaborn as sns
        
        results = self.analyze_results()
        figures = {}
        
        # Figure 1: Clusters by Zone
        fig1, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Pie chart of clusters by zone
        zone_counts = results.clusters_by_zone
        axes[0].pie(zone_counts.values(), labels=zone_counts.keys(), autopct='%1.0f%%')
        axes[0].set_title('Distribution of Clusters by Currency Zone')
        
        # Bar chart of silhouette scores by zone
        zones = list(results.within_zone_silhouette.keys())
        scores = list(results.within_zone_silhouette.values())
        axes[1].bar(zones, scores)
        axes[1].set_ylabel('Silhouette Score')
        axes[1].set_title('Clustering Quality by Currency Zone')
        axes[1].axhline(y=results.overall_silhouette, color='r', linestyle='--', 
                       label=f'Overall: {results.overall_silhouette:.3f}')
        axes[1].legend()
        
        plt.tight_layout()
        figures['zone_analysis'] = fig1
        
        # Figure 2: Feature Importance by Zone
        fig2, axes = plt.subplots(1, len(zone_counts), figsize=(20, 6))
        if len(zone_counts) == 1:
            axes = [axes]
        
        for idx, (zone, importance) in enumerate(results.feature_importance_by_zone.items()):
            top_features = sorted(importance.items(), key=lambda x: x[1], reverse=True)[:8]
            features, scores = zip(*top_features)
            
            axes[idx].barh(range(len(features)), scores)
            axes[idx].set_yticks(range(len(features)))
            axes[idx].set_yticklabels(features)
            axes[idx].set_xlabel('Importance')
            axes[idx].set_title(f'{zone} Zone')
            axes[idx].grid(True, alpha=0.3)
        
        plt.suptitle('Top Features by Currency Zone', fontsize=16)
        plt.tight_layout()
        figures['feature_importance'] = fig2
        
        # Figure 3: Cluster Profiles
        fig3, ax = plt.subplots(figsize=(12, 8))
        
        # Create heatmap of cluster characteristics
        profile_data = []
        cluster_ids = []
        
        for cluster_id, profile in results.cluster_profiles.items():
            profile_data.append([
                profile['avg_price_usd'],
                profile['price_volatility_usd'],
                profile['avg_exchange_rate'],
                profile['conflict_intensity'],
                profile['control_stability']
            ])
            cluster_ids.append(f"C{cluster_id} ({profile['currency_zone']})")
        
        profile_df = pd.DataFrame(
            profile_data,
            index=cluster_ids,
            columns=['Avg Price (USD)', 'Price Volatility', 'Exchange Rate', 
                    'Conflict', 'Stability']
        )
        
        # Normalize for visualization
        profile_df_norm = (profile_df - profile_df.mean()) / profile_df.std()
        
        sns.heatmap(profile_df_norm, cmap='RdBu_r', center=0, 
                   annot=True, fmt='.2f', ax=ax)
        ax.set_title('Normalized Cluster Profiles')
        
        plt.tight_layout()
        figures['cluster_profiles'] = fig3
        
        # Save if requested
        if save_path:
            for name, fig in figures.items():
                fig.savefig(f"{save_path}_{name}.png", dpi=300, bbox_inches='tight')
        
        return figures
    
    def export_for_tier1_analysis(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """
        Export cluster assignments for integration with Tier 1 analysis.
        
        Args:
            panel_data: Original panel data
            
        Returns:
            Panel data with cluster assignments
        """
        if not self.fitted:
            raise ValueError("Model must be fitted before export")
        
        # Create assignment mapping
        assignments = pd.DataFrame({
            'market_id': self.features_df_['market_id'],
            'cluster': self.cluster_labels_,
            'currency_zone': self.features_df_['currency_zone']
        })
        
        # Add cluster characteristics
        results = self.analyze_results()
        for cluster_id, profile in results.cluster_profiles.items():
            mask = assignments['cluster'] == cluster_id
            assignments.loc[mask, 'cluster_size'] = profile['size']
            assignments.loc[mask, 'cluster_stability'] = results.temporal_stability.get(
                cluster_id, 0.5
            )
        
        # Merge with panel data
        panel_with_clusters = panel_data.merge(
            assignments[['market_id', 'cluster', 'cluster_size', 'cluster_stability']],
            on='market_id',
            how='left'
        )
        
        logger.info(
            f"Added cluster assignments to {len(panel_with_clusters)} observations"
        )
        
        return panel_with_clusters


# Helper function for easy integration
def apply_currency_aware_clustering(
    panel_data: pd.DataFrame,
    conflict_data: Optional[pd.DataFrame] = None,
    geographic_data: Optional[pd.DataFrame] = None,
    n_clusters_per_zone: Optional[Dict[str, int]] = None,
    validate: bool = True
) -> Tuple[pd.DataFrame, CurrencyAwareClusteringResults]:
    """
    Apply currency-aware clustering to panel data.
    
    This function provides an easy interface for integrating clustering
    into the Three-Tier analysis framework.
    
    Args:
        panel_data: Must include price_usd, currency_zone, exchange_rate_used
        conflict_data: Optional conflict event data
        geographic_data: Optional geographic features
        n_clusters_per_zone: Number of clusters per zone (None for automatic)
        validate: Whether to validate methodology compliance
        
    Returns:
        Tuple of (panel data with cluster assignments, clustering results)
        
    Example:
        >>> panel_with_clusters, results = apply_currency_aware_clustering(
        ...     panel_data=balanced_panel,
        ...     conflict_data=conflict_events,
        ...     validate=True
        ... )
        >>> print(f"Created {results.total_clusters} clusters across zones")
    """
    logger.info("Applying currency-aware clustering to market data")
    
    # Initialize clustering
    clustering = CurrencyAwareMarketClustering(
        n_clusters_per_zone=n_clusters_per_zone,
        min_cluster_size=5,
        clustering_algorithm='kmeans',
        enforce_zone_constraints=True,
        validate_methodology=validate
    )
    
    # Fit model
    clustering.fit(panel_data, conflict_data, geographic_data)
    
    # Get results
    results = clustering.analyze_results()
    
    # Export with cluster assignments
    panel_with_clusters = clustering.export_for_tier1_analysis(panel_data)
    
    # Log summary
    logger.info(f"Clustering complete:")
    logger.info(f"  - Total clusters: {results.total_clusters}")
    logger.info(f"  - By zone: {results.clusters_by_zone}")
    logger.info(f"  - Overall silhouette: {results.overall_silhouette:.3f}")
    logger.info(f"  - Methodology compliant: {results.methodology_compliance}")
    
    return panel_with_clusters, results