"""
Interactive Fixed Effects (IFE) Model Implementation

This module implements Interactive Fixed Effects for panel data analysis,
allowing for unobserved heterogeneity that varies over time - crucial
for conflict-affected markets like Yemen.

Key Features:
1. Factor model estimation for time-varying unobserved effects
2. Optimal factor selection using information criteria
3. Currency zone-specific factor structures
4. Robust to unbalanced panels and missing data
5. Integration with Three-Tier analysis framework

References:
- Bai (2009) "Panel Data Models with Interactive Fixed Effects"
- Gobillon & Magnac (2016) "Regional Policy Evaluation: IFE and Synthetic Controls"
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from scipy.linalg import eigh
from sklearn.preprocessing import StandardScaler
import logging

logger = logging.getLogger(__name__)


@dataclass
class IFEResults:
    """Results from Interactive Fixed Effects estimation."""
    # Core estimates
    coefficients: Dict[str, float]  # Parameter estimates
    standard_errors: Dict[str, float]  # Standard errors
    t_statistics: Dict[str, float]  # t-statistics
    p_values: Dict[str, float]  # p-values
    
    # Factor structure
    n_factors: int  # Number of factors used
    factor_loadings: np.ndarray  # λ_i (N x r matrix)
    common_factors: np.ndarray  # F_t (T x r matrix)
    factor_contributions: np.ndarray  # λ_i * F_t' (N x T matrix)
    
    # Model fit
    r_squared: float  # R-squared
    r_squared_within: float  # Within R-squared
    aic: float  # Akaike Information Criterion
    bic: float  # Bayesian Information Criterion
    
    # Residuals and diagnostics
    residuals: np.ndarray  # Model residuals
    fitted_values: np.ndarray  # Fitted values
    
    # Zone-specific results (if applicable)
    zone_specific_factors: Optional[Dict[str, np.ndarray]] = None
    zone_factor_importance: Optional[Dict[str, float]] = None
    
    # Robustness checks
    factor_stability: Optional[Dict[int, float]] = None  # Stability of each factor
    bootstrap_ci: Optional[Dict[str, Tuple[float, float]]] = None  # Bootstrap CIs


class InteractiveFixedEffects:
    """
    Interactive Fixed Effects estimator for panel data.
    
    This estimator is particularly suited for Yemen market analysis where:
    - Unobserved factors (governance, security) change over time
    - Different currency zones may have different factor structures
    - Conflict causes structural breaks in relationships
    """
    
    def __init__(self,
                 n_factors: Optional[int] = None,
                 max_factors: int = 10,
                 factor_selection: str = 'ic',  # 'ic' for info criteria, 'eigen' for eigenvalue ratio
                 zone_specific: bool = False,
                 robust_se: bool = True):
        """
        Initialize IFE estimator.
        
        Args:
            n_factors: Number of factors (None for automatic selection)
            max_factors: Maximum factors to consider
            factor_selection: Method for selecting number of factors
            zone_specific: Allow zone-specific factor structures
            robust_se: Use robust standard errors
        """
        self.n_factors = n_factors
        self.max_factors = max_factors
        self.factor_selection = factor_selection
        self.zone_specific = zone_specific
        self.robust_se = robust_se
        self.fitted = False
        
    def fit(self,
            y: np.ndarray,
            X: np.ndarray,
            entity_ids: np.ndarray,
            time_ids: np.ndarray,
            zones: Optional[np.ndarray] = None,
            weights: Optional[np.ndarray] = None) -> 'InteractiveFixedEffects':
        """
        Fit Interactive Fixed Effects model.
        
        Args:
            y: Dependent variable (n_obs,)
            X: Independent variables (n_obs, k)
            entity_ids: Entity (market) identifiers (n_obs,)
            time_ids: Time period identifiers (n_obs,)
            zones: Currency zone identifiers (n_obs,) - optional
            weights: Observation weights (n_obs,) - optional
            
        Returns:
            Self for chaining
        """
        logger.info("Fitting Interactive Fixed Effects model")
        
        # Convert to panel structure
        self._setup_panel_structure(y, X, entity_ids, time_ids, zones)
        
        # Determine optimal number of factors if not specified
        if self.n_factors is None:
            self.n_factors = self._select_n_factors()
            logger.info(f"Selected {self.n_factors} factors using {self.factor_selection} method")
        
        # Main estimation using iterative algorithm
        self._estimate_ife(weights)
        
        # Calculate standard errors
        if self.robust_se:
            self._calculate_robust_se()
        else:
            self._calculate_standard_se()
        
        # Model diagnostics
        self._calculate_diagnostics()
        
        # Zone-specific analysis if requested
        if self.zone_specific and zones is not None:
            self._analyze_zone_factors()
        
        self.fitted = True
        
        return self
    
    def _setup_panel_structure(self, y, X, entity_ids, time_ids, zones):
        """Convert data to balanced panel structure with missing values."""
        # Get unique entities and time periods
        self.entities = np.unique(entity_ids)
        self.time_periods = np.unique(time_ids)
        self.n_entities = len(self.entities)
        self.n_periods = len(self.time_periods)
        
        # Create entity and time maps
        entity_map = {e: i for i, e in enumerate(self.entities)}
        time_map = {t: i for i, t in enumerate(self.time_periods)}
        
        # Initialize panel matrices with NaN
        self.Y_panel = np.full((self.n_entities, self.n_periods), np.nan)
        self.X_panel = np.full((self.n_entities, self.n_periods, X.shape[1]), np.nan)
        
        if zones is not None:
            self.zones_panel = np.full((self.n_entities, self.n_periods), np.nan, dtype=object)
        
        # Fill panel matrices
        for i in range(len(y)):
            e_idx = entity_map[entity_ids[i]]
            t_idx = time_map[time_ids[i]]
            self.Y_panel[e_idx, t_idx] = y[i]
            self.X_panel[e_idx, t_idx, :] = X[i]
            
            if zones is not None:
                self.zones_panel[e_idx, t_idx] = zones[i]
        
        # Store dimensions
        self.k_vars = X.shape[1]
        
        # Create masks for missing data
        self.valid_mask = ~np.isnan(self.Y_panel)
        
    def _select_n_factors(self) -> int:
        """Select optimal number of factors using information criteria."""
        logger.info(f"Selecting optimal number of factors (max: {self.max_factors})")
        
        if self.factor_selection == 'ic':
            # Use information criteria (Bai & Ng 2002)
            ic_values = []
            
            for r in range(1, min(self.max_factors + 1, min(self.n_entities, self.n_periods) // 2)):
                # Estimate with r factors
                ssr = self._estimate_with_r_factors(r)
                
                # Calculate penalty
                penalty = r * self._calculate_ic_penalty()
                
                # Information criterion
                ic = np.log(ssr) + penalty
                ic_values.append(ic)
            
            # Select r that minimizes IC
            optimal_r = np.argmin(ic_values) + 1
            
        elif self.factor_selection == 'eigen':
            # Use eigenvalue ratio test
            eigenvalues = self._calculate_eigenvalues()
            ratios = eigenvalues[:-1] / eigenvalues[1:]
            
            # Find elbow point
            optimal_r = np.argmax(ratios) + 1
            
        else:
            raise ValueError(f"Unknown factor selection method: {self.factor_selection}")
        
        return min(optimal_r, self.max_factors)
    
    def _calculate_ic_penalty(self) -> float:
        """Calculate penalty term for information criteria."""
        N, T = self.n_entities, self.n_periods
        NT = N * T
        
        # Bai & Ng (2002) penalty
        if min(N, T) < 50:
            # Small sample adjustment
            penalty = np.log(min(N, T)) / min(N, T)
        else:
            # Standard penalty
            penalty = (N + T) / NT * np.log(NT / (N + T))
        
        return penalty
    
    def _estimate_with_r_factors(self, r: int) -> float:
        """Estimate model with r factors and return SSR."""
        # Simplified estimation for IC calculation
        # In practice, this would run the full iterative algorithm
        
        # Initial factor estimation using PCA on demeaned data
        Y_demean = self.Y_panel - np.nanmean(self.Y_panel)
        
        # Handle missing values
        Y_filled = np.where(self.valid_mask, Y_demean, 0)
        
        # PCA to get initial factors
        U, S, Vt = np.linalg.svd(Y_filled, full_matrices=False)
        
        # Extract r factors
        F = Vt[:r, :].T * np.sqrt(self.n_periods)
        Lambda = U[:, :r] * S[:r] / np.sqrt(self.n_periods)
        
        # Calculate residual sum of squares
        Y_hat = Lambda @ F.T
        residuals = Y_filled - Y_hat
        ssr = np.sum(residuals[self.valid_mask] ** 2)
        
        return ssr
    
    def _calculate_eigenvalues(self) -> np.ndarray:
        """Calculate eigenvalues for factor selection."""
        # Demean data
        Y_demean = self.Y_panel - np.nanmean(self.Y_panel)
        Y_filled = np.where(self.valid_mask, Y_demean, 0)
        
        # Calculate covariance matrix
        if self.n_entities < self.n_periods:
            # Use N x N matrix
            cov_matrix = Y_filled @ Y_filled.T / self.n_periods
        else:
            # Use T x T matrix
            cov_matrix = Y_filled.T @ Y_filled / self.n_entities
        
        # Get eigenvalues
        eigenvalues = eigh(cov_matrix, eigvals_only=True)
        eigenvalues = np.sort(eigenvalues)[::-1]
        
        return eigenvalues
    
    def _estimate_ife(self, weights: Optional[np.ndarray] = None):
        """
        Main IFE estimation using iterative algorithm.
        
        Algorithm:
        1. Initialize β using pooled OLS
        2. Given β, estimate factors using PCA
        3. Given factors, estimate β using panel regression
        4. Iterate until convergence
        """
        logger.info(f"Estimating IFE with {self.n_factors} factors")
        
        # Initialize coefficients with pooled OLS
        beta = self._pooled_ols_init()
        
        # Convergence parameters
        max_iter = 100
        tol = 1e-6
        
        for iteration in range(max_iter):
            beta_old = beta.copy()
            
            # Step 1: Given β, estimate factors
            Lambda, F = self._estimate_factors(beta)
            
            # Step 2: Given factors, estimate β
            beta = self._estimate_beta(Lambda, F, weights)
            
            # Check convergence
            beta_change = np.max(np.abs(beta - beta_old))
            if beta_change < tol:
                logger.info(f"IFE converged after {iteration + 1} iterations")
                break
        
        if iteration == max_iter - 1:
            logger.warning("IFE reached maximum iterations without convergence")
        
        # Store results
        self.beta = beta
        self.Lambda = Lambda
        self.F = F
        
        # Calculate fitted values and residuals
        self._calculate_fitted_residuals()
    
    def _pooled_ols_init(self) -> np.ndarray:
        """Initialize with pooled OLS estimates."""
        # Flatten panel data
        y_vec = self.Y_panel[self.valid_mask]
        X_vec = self.X_panel[self.valid_mask]
        
        # Pooled OLS
        XtX = X_vec.T @ X_vec
        Xty = X_vec.T @ y_vec
        beta_init = np.linalg.solve(XtX, Xty)
        
        return beta_init
    
    def _estimate_factors(self, beta: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Estimate factors given coefficients."""
        # Calculate residuals after removing X*beta
        residuals = np.full((self.n_entities, self.n_periods), np.nan)
        
        for i in range(self.n_entities):
            for t in range(self.n_periods):
                if self.valid_mask[i, t]:
                    residuals[i, t] = self.Y_panel[i, t] - self.X_panel[i, t] @ beta
        
        # Fill missing values for PCA
        residuals_filled = np.where(~np.isnan(residuals), residuals, 0)
        
        # Principal components extraction
        if self.n_entities < self.n_periods:
            # Use entity covariance matrix
            cov_matrix = residuals_filled @ residuals_filled.T / self.n_periods
            eigenvalues, eigenvectors = eigh(cov_matrix)
            
            # Sort by eigenvalue
            idx = np.argsort(eigenvalues)[::-1]
            eigenvectors = eigenvectors[:, idx[:self.n_factors]]
            
            # Normalize
            Lambda = eigenvectors * np.sqrt(self.n_entities)
            F = residuals_filled.T @ Lambda / self.n_entities
            
        else:
            # Use time covariance matrix
            cov_matrix = residuals_filled.T @ residuals_filled / self.n_entities
            eigenvalues, eigenvectors = eigh(cov_matrix)
            
            # Sort by eigenvalue
            idx = np.argsort(eigenvalues)[::-1]
            eigenvectors = eigenvectors[:, idx[:self.n_factors]]
            
            # Normalize
            F = eigenvectors * np.sqrt(self.n_periods)
            Lambda = residuals_filled @ F / self.n_periods
        
        return Lambda, F
    
    def _estimate_beta(self, Lambda: np.ndarray, F: np.ndarray, 
                      weights: Optional[np.ndarray] = None) -> np.ndarray:
        """Estimate coefficients given factors."""
        # Subtract factor contribution from Y
        factor_contribution = Lambda @ F.T
        
        # Prepare data for regression
        y_list = []
        X_list = []
        w_list = [] if weights is not None else None
        
        for i in range(self.n_entities):
            for t in range(self.n_periods):
                if self.valid_mask[i, t]:
                    y_adjusted = self.Y_panel[i, t] - factor_contribution[i, t]
                    y_list.append(y_adjusted)
                    X_list.append(self.X_panel[i, t])
                    
                    if weights is not None:
                        w_list.append(weights[i, t])
        
        y_vec = np.array(y_list)
        X_mat = np.array(X_list)
        
        # Weighted or unweighted regression
        if weights is not None:
            W = np.diag(w_list)
            XtWX = X_mat.T @ W @ X_mat
            XtWy = X_mat.T @ W @ y_vec
            beta = np.linalg.solve(XtWX, XtWy)
        else:
            XtX = X_mat.T @ X_mat
            Xty = X_mat.T @ y_vec
            beta = np.linalg.solve(XtX, Xty)
        
        return beta
    
    def _calculate_fitted_residuals(self):
        """Calculate fitted values and residuals."""
        self.fitted_values = np.full((self.n_entities, self.n_periods), np.nan)
        self.residuals = np.full((self.n_entities, self.n_periods), np.nan)
        
        # Factor contribution
        factor_contrib = self.Lambda @ self.F.T
        
        for i in range(self.n_entities):
            for t in range(self.n_periods):
                if self.valid_mask[i, t]:
                    X_contrib = self.X_panel[i, t] @ self.beta
                    self.fitted_values[i, t] = X_contrib + factor_contrib[i, t]
                    self.residuals[i, t] = self.Y_panel[i, t] - self.fitted_values[i, t]
    
    def _calculate_robust_se(self):
        """Calculate cluster-robust standard errors."""
        # Residuals and regressors
        e_list = []
        X_list = []
        entity_list = []
        
        for i in range(self.n_entities):
            for t in range(self.n_periods):
                if self.valid_mask[i, t]:
                    e_list.append(self.residuals[i, t])
                    X_list.append(self.X_panel[i, t])
                    entity_list.append(i)
        
        e_vec = np.array(e_list)
        X_mat = np.array(X_list)
        entities = np.array(entity_list)
        
        # Cluster-robust covariance matrix
        n_obs = len(e_vec)
        k = self.k_vars
        
        # Meat matrix
        meat = np.zeros((k, k))
        for i in range(self.n_entities):
            entity_mask = entities == i
            if np.any(entity_mask):
                X_i = X_mat[entity_mask]
                e_i = e_vec[entity_mask]
                meat += X_i.T @ np.outer(e_i, e_i) @ X_i
        
        # Bread matrix
        bread = np.linalg.inv(X_mat.T @ X_mat)
        
        # Robust covariance
        robust_cov = n_obs / (n_obs - k) * bread @ meat @ bread
        
        # Standard errors
        self.se = np.sqrt(np.diag(robust_cov))
        
    def _calculate_standard_se(self):
        """Calculate standard (non-robust) standard errors."""
        # Residual variance
        n_obs = np.sum(self.valid_mask)
        k = self.k_vars
        sigma2 = np.sum(self.residuals[self.valid_mask] ** 2) / (n_obs - k - self.n_factors)
        
        # Regressor matrix
        X_list = []
        for i in range(self.n_entities):
            for t in range(self.n_periods):
                if self.valid_mask[i, t]:
                    X_list.append(self.X_panel[i, t])
        
        X_mat = np.array(X_list)
        
        # Covariance matrix
        cov_matrix = sigma2 * np.linalg.inv(X_mat.T @ X_mat)
        
        # Standard errors
        self.se = np.sqrt(np.diag(cov_matrix))
    
    def _calculate_diagnostics(self):
        """Calculate model diagnostics."""
        # R-squared
        y_vec = self.Y_panel[self.valid_mask]
        fitted_vec = self.fitted_values[self.valid_mask]
        residuals_vec = self.residuals[self.valid_mask]
        
        tss = np.sum((y_vec - np.mean(y_vec)) ** 2)
        rss = np.sum(residuals_vec ** 2)
        self.r_squared = 1 - rss / tss
        
        # Within R-squared (after removing entity means)
        y_demeaned = self.Y_panel - np.nanmean(self.Y_panel, axis=1, keepdims=True)
        fitted_demeaned = self.fitted_values - np.nanmean(self.fitted_values, axis=1, keepdims=True)
        
        y_within = y_demeaned[self.valid_mask]
        fitted_within = fitted_demeaned[self.valid_mask]
        
        tss_within = np.sum(y_within ** 2)
        rss_within = np.sum((y_within - fitted_within) ** 2)
        self.r_squared_within = 1 - rss_within / tss_within
        
        # Information criteria
        n_obs = np.sum(self.valid_mask)
        k_total = self.k_vars + self.n_factors * (self.n_entities + self.n_periods)
        
        log_likelihood = -n_obs / 2 * (np.log(2 * np.pi) + np.log(rss / n_obs) + 1)
        
        self.aic = -2 * log_likelihood + 2 * k_total
        self.bic = -2 * log_likelihood + np.log(n_obs) * k_total
        
    def _analyze_zone_factors(self):
        """Analyze factor structure by currency zone."""
        if not hasattr(self, 'zones_panel'):
            return
        
        self.zone_factors = {}
        self.zone_importance = {}
        
        # Get unique zones
        unique_zones = []
        for i in range(self.n_entities):
            for t in range(self.n_periods):
                if self.valid_mask[i, t] and not np.isnan(self.zones_panel[i, t]):
                    zone = self.zones_panel[i, t]
                    if zone not in unique_zones:
                        unique_zones.append(zone)
        
        # Analyze each zone
        for zone in unique_zones:
            # Get entities in this zone
            zone_entities = []
            for i in range(self.n_entities):
                # Check if entity belongs to zone (majority rule)
                zone_counts = pd.Series([self.zones_panel[i, t] for t in range(self.n_periods) 
                                       if self.valid_mask[i, t]]).value_counts()
                if len(zone_counts) > 0 and zone_counts.index[0] == zone:
                    zone_entities.append(i)
            
            if len(zone_entities) > 0:
                # Average factor loadings for zone
                zone_loadings = self.Lambda[zone_entities, :].mean(axis=0)
                self.zone_factors[zone] = zone_loadings
                
                # Importance of factors for this zone (variance explained)
                zone_variance = np.var(self.Lambda[zone_entities, :], axis=0)
                self.zone_importance[zone] = zone_variance / zone_variance.sum()
    
    def predict(self, X_new: np.ndarray, 
                entity_ids: np.ndarray,
                time_ids: np.ndarray) -> np.ndarray:
        """
        Predict using fitted IFE model.
        
        Args:
            X_new: New predictor values
            entity_ids: Entity identifiers for predictions
            time_ids: Time identifiers for predictions
            
        Returns:
            Predicted values
        """
        if not self.fitted:
            raise ValueError("Model must be fitted before prediction")
        
        predictions = np.zeros(len(X_new))
        
        for i in range(len(X_new)):
            # Get entity and time indices
            if entity_ids[i] in self.entities:
                e_idx = np.where(self.entities == entity_ids[i])[0][0]
            else:
                # New entity - use average factor loading
                e_idx = None
            
            if time_ids[i] in self.time_periods:
                t_idx = np.where(self.time_periods == time_ids[i])[0][0]
            else:
                # New time period - extrapolate factors
                t_idx = None
            
            # Linear prediction
            linear_pred = X_new[i] @ self.beta
            
            # Factor contribution
            if e_idx is not None and t_idx is not None:
                factor_pred = self.Lambda[e_idx] @ self.F[t_idx]
            elif e_idx is not None and t_idx is None:
                # Extrapolate time factor
                factor_pred = self.Lambda[e_idx] @ self.F[-1]  # Use last period
            elif e_idx is None and t_idx is not None:
                # Use average loading
                avg_loading = self.Lambda.mean(axis=0)
                factor_pred = avg_loading @ self.F[t_idx]
            else:
                # Both new - use averages
                avg_loading = self.Lambda.mean(axis=0)
                factor_pred = avg_loading @ self.F[-1]
            
            predictions[i] = linear_pred + factor_pred
        
        return predictions
    
    def get_results(self) -> IFEResults:
        """
        Get comprehensive results object.
        
        Returns:
            IFEResults with all estimation outputs
        """
        if not self.fitted:
            raise ValueError("Model must be fitted before getting results")
        
        # Create coefficient dictionary
        coef_names = [f'X{i}' for i in range(self.k_vars)]
        coefficients = dict(zip(coef_names, self.beta))
        standard_errors = dict(zip(coef_names, self.se))
        
        # Calculate t-statistics and p-values
        t_stats = self.beta / self.se
        p_values = 2 * (1 - stats.norm.cdf(np.abs(t_stats)))
        
        t_statistics = dict(zip(coef_names, t_stats))
        p_values_dict = dict(zip(coef_names, p_values))
        
        # Factor contributions
        factor_contributions = self.Lambda @ self.F.T
        
        # Factor stability (simplified - correlation over time)
        factor_stability = {}
        for r in range(self.n_factors):
            factor_series = self.F[:, r]
            if len(factor_series) > 1:
                stability = np.corrcoef(factor_series[:-1], factor_series[1:])[0, 1]
            else:
                stability = 1.0
            factor_stability[r] = stability
        
        return IFEResults(
            coefficients=coefficients,
            standard_errors=standard_errors,
            t_statistics=t_statistics,
            p_values=p_values_dict,
            n_factors=self.n_factors,
            factor_loadings=self.Lambda,
            common_factors=self.F,
            factor_contributions=factor_contributions,
            r_squared=self.r_squared,
            r_squared_within=self.r_squared_within,
            aic=self.aic,
            bic=self.bic,
            residuals=self.residuals,
            fitted_values=self.fitted_values,
            zone_specific_factors=getattr(self, 'zone_factors', None),
            zone_factor_importance=getattr(self, 'zone_importance', None),
            factor_stability=factor_stability
        )


# Integration with Three-Tier framework
def apply_ife_to_panel(panel_data: pd.DataFrame,
                      dependent_var: str,
                      independent_vars: List[str],
                      entity_var: str = 'market_id',
                      time_var: str = 'date',
                      zone_var: Optional[str] = 'currency_zone',
                      n_factors: Optional[int] = None) -> IFEResults:
    """
    Apply IFE to panel data in Three-Tier framework.
    
    Args:
        panel_data: Panel DataFrame
        dependent_var: Name of dependent variable
        independent_vars: List of independent variable names
        entity_var: Entity identifier column
        time_var: Time identifier column
        zone_var: Currency zone column (optional)
        n_factors: Number of factors (None for automatic)
        
    Returns:
        IFEResults object
    """
    logger.info(f"Applying IFE to panel data with {len(independent_vars)} variables")
    
    # Prepare data
    y = panel_data[dependent_var].values
    X = panel_data[independent_vars].values
    entities = panel_data[entity_var].values
    time_ids = panel_data[time_var].values
    
    zones = None
    if zone_var and zone_var in panel_data.columns:
        zones = panel_data[zone_var].values
        zone_specific = True
    else:
        zone_specific = False
    
    # Initialize and fit IFE
    ife = InteractiveFixedEffects(
        n_factors=n_factors,
        zone_specific=zone_specific,
        robust_se=True
    )
    
    ife.fit(y, X, entities, time_ids, zones)
    
    # Get results
    results = ife.get_results()
    
    # Log summary
    logger.info(f"IFE estimation complete:")
    logger.info(f"  - Factors: {results.n_factors}")
    logger.info(f"  - R-squared: {results.r_squared:.4f}")
    logger.info(f"  - Within R-squared: {results.r_squared_within:.4f}")
    
    return results