"""
Specification Validation Framework

This module provides comprehensive validation of analysis specifications
against the locked pre-analysis plan to ensure research transparency
and prevent p-hacking.

Version: 1.0
Status: LOCKED - NO MODIFICATIONS PERMITTED
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
import warnings
import logging
from scipy import stats
from sklearn.model_selection import cross_val_score
from sklearn.metrics import mean_squared_error

from .locked_specifications import LOCKED_SPECS, HypothesisConfig, SpecificationConfig


@dataclass
class ValidationResult:
    """Result of specification validation"""
    is_valid: bool
    deviations: List[str]
    warnings: List[str]
    compliance_score: float
    recommendations: List[str]


@dataclass
class SampleValidation:
    """Result of sample validation"""
    meets_criteria: bool
    sample_size: int
    coverage_rate: float
    missing_patterns: Dict[str, float]
    representativeness_score: float
    exclusions_applied: List[str]


@dataclass
class PowerValidation:
    """Result of power validation"""
    estimated_power: float
    minimum_detectable_effect: float
    sample_adequacy: bool
    confidence_interval_width: float
    recommendations: List[str]


class SpecificationValidator:
    """
    Validates analysis specifications against the pre-analysis plan.
    
    This class ensures that:
    - All specifications match the locked plan exactly
    - Sample criteria are properly applied
    - Statistical power is adequate for meaningful inference
    - Multiple testing corrections are properly implemented
    """
    
    def __init__(self):
        """Initialize the specification validator"""
        self.specs = LOCKED_SPECS
        self.logger = logging.getLogger(__name__)
        
        # Validation thresholds
        self.compliance_threshold = 0.95
        self.power_threshold = 0.80
        self.coverage_threshold = 0.80
        
    def validate_full_specification(
        self, 
        hypothesis_id: str,
        specification: Dict[str, Any],
        data: pd.DataFrame,
        analysis_type: str = "primary"
    ) -> ValidationResult:
        """Comprehensive validation of specification against plan"""
        
        # Get locked configuration
        hyp_config = self.specs.get_hypothesis_config(hypothesis_id)
        if hyp_config is None:
            return ValidationResult(
                is_valid=False,
                deviations=[f"Unknown hypothesis: {hypothesis_id}"],
                warnings=[],
                compliance_score=0.0,
                recommendations=["Use only pre-specified hypotheses"]
            )
        
        deviations = []
        warnings_list = []
        
        # Validate specification components
        spec_deviations = self._validate_specification_components(
            hyp_config, specification, analysis_type
        )
        deviations.extend(spec_deviations)
        
        # Validate sample
        sample_validation = self.validate_sample(data, hypothesis_id)
        if not sample_validation.meets_criteria:
            deviations.append("Sample does not meet inclusion criteria")
        
        # Check statistical power
        power_validation = self._validate_statistical_power(
            hyp_config, data, specification
        )
        if not power_validation.sample_adequacy:
            warnings_list.append(f"Statistical power below threshold: {power_validation.estimated_power:.2f}")
        
        # Calculate compliance score
        compliance_score = self._calculate_compliance_score(deviations, warnings_list)
        
        # Generate recommendations
        recommendations = self._generate_validation_recommendations(
            deviations, warnings_list, sample_validation, power_validation
        )
        
        return ValidationResult(
            is_valid=len(deviations) == 0,
            deviations=deviations,
            warnings=warnings_list,
            compliance_score=compliance_score,
            recommendations=recommendations
        )
    
    def _validate_specification_components(
        self, 
        hyp_config: HypothesisConfig,
        specification: Dict[str, Any],
        analysis_type: str
    ) -> List[str]:
        """Validate individual specification components"""
        deviations = []
        
        # Select appropriate specification config
        if analysis_type == "primary":
            target_spec = hyp_config.primary_specification
        elif analysis_type == "robustness" and hyp_config.robustness_specifications:
            # For robustness, allow any of the pre-specified alternatives
            target_specs = hyp_config.robustness_specifications
            if not any(self._specs_match(spec, target_spec) for target_spec in target_specs):
                deviations.append("Robustness specification not in pre-approved list")
            return deviations
        else:
            deviations.append(f"Invalid analysis type: {analysis_type}")
            return deviations
        
        # Validate model formula
        if specification.get("model_formula") != target_spec.model_formula:
            deviations.append(
                f"Model formula mismatch. Expected: {target_spec.model_formula}, "
                f"Got: {specification.get('model_formula')}"
            )
        
        # Validate estimation method
        if specification.get("estimation_method") != target_spec.estimation_method:
            deviations.append(
                f"Estimation method mismatch. Expected: {target_spec.estimation_method}, "
                f"Got: {specification.get('estimation_method')}"
            )
        
        # Validate fixed effects
        spec_fe = set(specification.get("fixed_effects", []))
        target_fe = set(target_spec.fixed_effects)
        if spec_fe != target_fe:
            deviations.append(
                f"Fixed effects mismatch. Expected: {target_fe}, Got: {spec_fe}"
            )
        
        # Validate clustering
        if specification.get("clustering_variable") != target_spec.clustering_variable:
            deviations.append(
                f"Clustering variable mismatch. Expected: {target_spec.clustering_variable}, "
                f"Got: {specification.get('clustering_variable')}"
            )
        
        # Validate sample restrictions
        spec_restrictions = specification.get("sample_restrictions", {})
        target_restrictions = target_spec.sample_restrictions
        
        for restriction, expected_value in target_restrictions.items():
            if restriction not in spec_restrictions:
                deviations.append(f"Missing required sample restriction: {restriction}")
            elif spec_restrictions[restriction] != expected_value:
                deviations.append(
                    f"Sample restriction mismatch for {restriction}. "
                    f"Expected: {expected_value}, Got: {spec_restrictions[restriction]}"
                )
        
        return deviations
    
    def _specs_match(self, spec: Dict[str, Any], target_spec: SpecificationConfig) -> bool:
        """Check if specification matches target specification config"""
        return (
            spec.get("model_formula") == target_spec.model_formula and
            spec.get("estimation_method") == target_spec.estimation_method and
            set(spec.get("fixed_effects", [])) == set(target_spec.fixed_effects) and
            spec.get("clustering_variable") == target_spec.clustering_variable
        )
    
    def validate_sample(self, data: pd.DataFrame, hypothesis_id: str) -> SampleValidation:
        """Validate sample against inclusion/exclusion criteria"""
        criteria = self.specs.sample_criteria
        
        # Calculate basic sample statistics
        sample_size = len(data)
        
        # Check market coverage
        if 'market' in data.columns:
            n_markets = data['market'].nunique()
        else:
            n_markets = sample_size  # Assume each row is a market
        
        # Check time coverage
        if 'date' in data.columns:
            date_range = pd.to_datetime(data['date'])
            time_span_months = (date_range.max() - date_range.min()).days / 30.44
        else:
            time_span_months = criteria["inclusion_criteria"]["min_months_per_market"]
        
        # Calculate coverage rate
        if 'price' in data.columns:
            coverage_rate = 1 - data['price'].isna().mean()
        else:
            coverage_rate = 1.0  # Assume complete if no price column
        
        # Check missing patterns
        missing_patterns = {}
        for col in data.columns:
            if data[col].dtype in [np.float64, np.int64]:
                missing_patterns[col] = data[col].isna().mean()
        
        # Evaluate criteria compliance
        meets_criteria = (
            n_markets >= criteria["inclusion_criteria"]["min_months_per_market"] and
            coverage_rate >= criteria["inclusion_criteria"]["min_data_availability"] and
            time_span_months >= criteria["inclusion_criteria"]["min_months_per_market"]
        )
        
        # Calculate representativeness score
        representativeness_score = self._calculate_representativeness(data, criteria)
        
        # Identify applied exclusions
        exclusions_applied = self._identify_applied_exclusions(data, criteria)
        
        return SampleValidation(
            meets_criteria=meets_criteria,
            sample_size=sample_size,
            coverage_rate=coverage_rate,
            missing_patterns=missing_patterns,
            representativeness_score=representativeness_score,
            exclusions_applied=exclusions_applied
        )
    
    def _calculate_representativeness(self, data: pd.DataFrame, criteria: Dict[str, Any]) -> float:
        """Calculate how representative the sample is"""
        # Simplified representativeness calculation
        # In practice, this would compare against population parameters
        
        score = 1.0
        
        # Penalize for high missing data rates
        if 'price' in data.columns:
            missing_rate = data['price'].isna().mean()
            score *= (1 - missing_rate)
        
        # Penalize for temporal gaps
        if 'date' in data.columns:
            dates = pd.to_datetime(data['date']).sort_values()
            if len(dates) > 1:
                expected_dates = pd.date_range(dates.min(), dates.max(), freq='MS')
                actual_coverage = len(dates.unique()) / len(expected_dates)
                score *= actual_coverage
        
        return max(0.0, min(1.0, score))
    
    def _identify_applied_exclusions(self, data: pd.DataFrame, criteria: Dict[str, Any]) -> List[str]:
        """Identify which exclusions have been applied to the data"""
        exclusions_applied = []
        
        # Check for standard exclusions
        if 'estimated_flag' in data.columns and not data['estimated_flag'].any():
            exclusions_applied.append("wfp_estimated_prices")
        
        if 'ramadan_flag' in data.columns and not data['ramadan_flag'].any():
            exclusions_applied.append("ramadan_months")
        
        if 'outlier_flag' in data.columns and not data['outlier_flag'].any():
            exclusions_applied.append("extreme_outliers")
        
        return exclusions_applied
    
    def _validate_statistical_power(
        self, 
        hyp_config: HypothesisConfig,
        data: pd.DataFrame,
        specification: Dict[str, Any]
    ) -> PowerValidation:
        """Validate statistical power for the analysis"""
        
        # Extract effect size threshold
        effect_threshold = hyp_config.economic_significance_threshold
        alpha = hyp_config.significance_level
        
        # Estimate power using sample size and expected effect
        sample_size = len(data)
        
        # Simplified power calculation (in practice, would use more sophisticated methods)
        if hyp_config.test_type.value == "one_tailed":
            critical_t = stats.t.ppf(1 - alpha, sample_size - 10)  # Rough df estimate
        else:
            critical_t = stats.t.ppf(1 - alpha/2, sample_size - 10)
        
        # Estimate standard error (simplified)
        if 'price' in data.columns:
            outcome_var = data['price'].dropna()
            if len(outcome_var) > 1:
                estimated_se = outcome_var.std() / np.sqrt(len(outcome_var))
            else:
                estimated_se = 0.1  # Default
        else:
            estimated_se = 0.1  # Default
        
        # Calculate power
        effect_t = effect_threshold / estimated_se
        if hyp_config.test_type.value == "one_tailed":
            power = 1 - stats.t.cdf(critical_t - effect_t, sample_size - 10)
        else:
            power = (1 - stats.t.cdf(critical_t - effect_t, sample_size - 10) +
                    stats.t.cdf(-critical_t - effect_t, sample_size - 10))
        
        # Calculate minimum detectable effect
        mde = critical_t * estimated_se
        
        # Calculate confidence interval width
        ci_width = 2 * critical_t * estimated_se
        
        # Assess sample adequacy
        sample_adequacy = power >= self.power_threshold
        
        # Generate recommendations
        recommendations = []
        if not sample_adequacy:
            recommendations.append(f"Increase sample size to achieve {self.power_threshold:.0%} power")
        if ci_width > effect_threshold:
            recommendations.append("Confidence intervals may be too wide for meaningful inference")
        
        return PowerValidation(
            estimated_power=power,
            minimum_detectable_effect=mde,
            sample_adequacy=sample_adequacy,
            confidence_interval_width=ci_width,
            recommendations=recommendations
        )
    
    def validate_multiple_testing_procedure(
        self, 
        hypothesis_results: Dict[str, Dict[str, float]]
    ) -> Tuple[bool, List[str]]:
        """Validate that multiple testing corrections are properly applied"""
        
        issues = []
        
        # Separate primary and secondary hypotheses
        primary_hyps = []
        secondary_hyps = []
        
        for hyp_id in hypothesis_results.keys():
            hyp_config = self.specs.get_hypothesis_config(hyp_id)
            if hyp_config is None:
                issues.append(f"Unknown hypothesis in results: {hyp_id}")
                continue
            
            if hyp_config.hypothesis_type.value == "primary":
                primary_hyps.append(hyp_id)
            elif hyp_config.hypothesis_type.value == "secondary":
                secondary_hyps.append(hyp_id)
        
        # Check primary hypothesis corrections (should use FDR)
        if primary_hyps:
            primary_alpha = self.specs.multiple_testing_config["primary_alpha"]
            expected_method = self.specs.multiple_testing_config["primary_method"]
            
            for hyp_id in primary_hyps:
                results = hypothesis_results[hyp_id]
                if "correction_method" not in results:
                    issues.append(f"Missing correction method for primary hypothesis {hyp_id}")
                elif results["correction_method"] != expected_method:
                    issues.append(
                        f"Wrong correction method for {hyp_id}. "
                        f"Expected: {expected_method}, Got: {results['correction_method']}"
                    )
        
        # Check secondary hypothesis corrections (should use Bonferroni)
        if secondary_hyps:
            secondary_alpha = self.specs.multiple_testing_config["secondary_alpha"]
            expected_method = self.specs.multiple_testing_config["secondary_method"]
            
            for hyp_id in secondary_hyps:
                results = hypothesis_results[hyp_id]
                if "correction_method" not in results:
                    issues.append(f"Missing correction method for secondary hypothesis {hyp_id}")
                elif results["correction_method"] != expected_method:
                    issues.append(
                        f"Wrong correction method for {hyp_id}. "
                        f"Expected: {expected_method}, Got: {results['correction_method']}"
                    )
        
        return len(issues) == 0, issues
    
    def _calculate_compliance_score(self, deviations: List[str], warnings: List[str]) -> float:
        """Calculate overall compliance score"""
        # Weight deviations more heavily than warnings
        deviation_penalty = len(deviations) * 0.2
        warning_penalty = len(warnings) * 0.05
        
        total_penalty = deviation_penalty + warning_penalty
        compliance_score = max(0.0, 1.0 - total_penalty)
        
        return compliance_score
    
    def _generate_validation_recommendations(
        self,
        deviations: List[str],
        warnings: List[str],
        sample_validation: SampleValidation,
        power_validation: PowerValidation
    ) -> List[str]:
        """Generate recommendations for improving compliance"""
        
        recommendations = []
        
        # Address deviations
        if deviations:
            recommendations.append("Correct specification deviations to match pre-analysis plan")
        
        # Address warnings
        if warnings:
            recommendations.append("Review warnings and consider their impact on results")
        
        # Address sample issues
        if not sample_validation.meets_criteria:
            recommendations.append("Ensure sample meets all inclusion criteria")
        
        if sample_validation.coverage_rate < self.coverage_threshold:
            recommendations.append(f"Improve data coverage (current: {sample_validation.coverage_rate:.1%})")
        
        # Address power issues
        if not power_validation.sample_adequacy:
            recommendations.append("Consider increasing sample size for adequate power")
        
        # Add power-specific recommendations
        recommendations.extend(power_validation.recommendations)
        
        return recommendations
    
    def generate_validation_report(
        self,
        hypothesis_id: str,
        validation_result: ValidationResult,
        sample_validation: SampleValidation,
        power_validation: PowerValidation
    ) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        
        report = {
            "hypothesis_id": hypothesis_id,
            "validation_timestamp": pd.Timestamp.now().isoformat(),
            "overall_compliance": {
                "is_valid": validation_result.is_valid,
                "compliance_score": validation_result.compliance_score,
                "meets_threshold": validation_result.compliance_score >= self.compliance_threshold
            },
            "specification_validation": {
                "deviations": validation_result.deviations,
                "warnings": validation_result.warnings
            },
            "sample_validation": {
                "meets_criteria": sample_validation.meets_criteria,
                "sample_size": sample_validation.sample_size,
                "coverage_rate": sample_validation.coverage_rate,
                "representativeness_score": sample_validation.representativeness_score,
                "missing_patterns": sample_validation.missing_patterns,
                "exclusions_applied": sample_validation.exclusions_applied
            },
            "power_validation": {
                "estimated_power": power_validation.estimated_power,
                "meets_threshold": power_validation.sample_adequacy,
                "minimum_detectable_effect": power_validation.minimum_detectable_effect,
                "confidence_interval_width": power_validation.confidence_interval_width
            },
            "recommendations": validation_result.recommendations,
            "approval_status": self._determine_approval_status(validation_result, sample_validation, power_validation)
        }
        
        return report
    
    def _determine_approval_status(
        self,
        validation_result: ValidationResult,
        sample_validation: SampleValidation,
        power_validation: PowerValidation
    ) -> str:
        """Determine overall approval status"""
        
        if not validation_result.is_valid:
            return "REJECTED - Specification deviations"
        
        if not sample_validation.meets_criteria:
            return "REJECTED - Sample criteria not met"
        
        if validation_result.compliance_score < self.compliance_threshold:
            return "CONDITIONAL - Low compliance score"
        
        if not power_validation.sample_adequacy:
            return "CONDITIONAL - Inadequate statistical power"
        
        return "APPROVED"