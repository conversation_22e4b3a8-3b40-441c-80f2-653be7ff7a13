"""
Locked Specifications for Yemen Market Integration Pre-Analysis Plan

This module contains immutable specifications that enforce the pre-analysis plan.
Any attempt to modify specifications after the lock date will raise an error.

Version: 1.0
Lock Date: January 6, 2025
Status: LOCKED - NO MODIFICATIONS PERMITTED
"""

import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
import warnings


class HypothesisType(Enum):
    """Classification of hypotheses for multiple testing correction"""
    PRIMARY = "primary"
    SECONDARY = "secondary" 
    METHODOLOGICAL = "methodological"


class TestType(Enum):
    """Type of statistical test"""
    ONE_TAILED = "one_tailed"
    TWO_TAILED = "two_tailed"
    JOINT_TEST = "joint_test"


@dataclass(frozen=True)
class SpecificationConfig:
    """Immutable specification configuration"""
    model_formula: str
    estimation_method: str
    fixed_effects: List[str]
    clustering_variable: str
    sample_restrictions: Dict[str, Any]
    robustness_checks: List[str]
    

@dataclass(frozen=True)
class HypothesisConfig:
    """Immutable hypothesis configuration"""
    hypothesis_id: str
    hypothesis_type: HypothesisType
    test_type: TestType
    significance_level: float
    expected_sign: Optional[str]
    economic_significance_threshold: float
    primary_specification: SpecificationConfig
    robustness_specifications: List[SpecificationConfig]


class LockedSpecifications:
    """
    Immutable specifications for pre-analysis plan.
    Any modification throws an error after lock_date.
    """
    
    def __init__(self, lock_date: datetime = None):
        """Initialize locked specifications"""
        self._lock_date = lock_date or datetime(2025, 1, 6, 23, 59, 59)
        self._is_locked = datetime.now() >= self._lock_date
        self._plan_version = "1.0"
        self._plan_hash = None
        
        # Lock all specifications immediately
        self._primary_hypotheses = self._define_primary_hypotheses()
        self._secondary_hypotheses = self._define_secondary_hypotheses()
        self._multiple_testing_config = self._define_multiple_testing()
        self._sample_criteria = self._define_sample_criteria()
        self._variable_definitions = self._define_variables()
        self._robustness_framework = self._define_robustness_framework()
        
        # Generate immutable hash
        self._plan_hash = self._generate_plan_hash()
        
    def _check_lock_status(self):
        """Check if modifications are permitted"""
        if self._is_locked:
            raise RuntimeError(
                f"Specifications are LOCKED as of {self._lock_date}. "
                f"No modifications permitted. Current plan hash: {self._plan_hash}"
            )
    
    def _generate_plan_hash(self) -> str:
        """Generate cryptographic hash of complete specification"""
        content = {
            'version': self._plan_version,
            'lock_date': self._lock_date.isoformat(),
            'primary_hypotheses': [h.hypothesis_id for h in self._primary_hypotheses],
            'secondary_hypotheses': [h.hypothesis_id for h in self._secondary_hypotheses],
            'multiple_testing': self._multiple_testing_config,
            'sample_criteria': self._sample_criteria
        }
        
        content_str = str(sorted(content.items()))
        return hashlib.md5(content_str.encode()).hexdigest()
    
    def _define_primary_hypotheses(self) -> List[HypothesisConfig]:
        """Define primary hypotheses with exact specifications"""
        return [
            HypothesisConfig(
                hypothesis_id="H1",
                hypothesis_type=HypothesisType.PRIMARY,
                test_type=TestType.TWO_TAILED,
                significance_level=0.0167,  # Bonferroni-corrected
                expected_sign=None,  # No directional prediction
                economic_significance_threshold=0.14,  # 15% price differential
                primary_specification=SpecificationConfig(
                    model_formula="price_usd ~ zone + controls",
                    estimation_method="ols",
                    fixed_effects=[],
                    clustering_variable="governorate", 
                    sample_restrictions={
                        "min_observations_per_market": 24,
                        "exclude_ramadan": True,
                        "exclude_estimated_prices": True,
                        "time_period": "2015-01-01 to 2024-12-31"
                    },
                    robustness_checks=["fixed_effects", "logged_prices"]
                ),
                robustness_specifications=[
                    SpecificationConfig(
                        model_formula="log_price_usd ~ zone_houthi + conflict_intensity + log_population + distance_to_port",
                        estimation_method="reghdfe",
                        fixed_effects=["market", "commodity_month"],
                        clustering_variable="governorate",
                        sample_restrictions={
                            "min_observations_per_market": 24,
                            "exclude_ramadan": True,
                            "exclude_estimated_prices": True,
                            "time_period": "2015-01-01 to 2024-12-31"
                        },
                        robustness_checks=[]
                    ),
                    SpecificationConfig(
                        model_formula="d_log_price_usd ~ d_zone_houthi + d_conflict_intensity + d_log_population",
                        estimation_method="reghdfe",
                        fixed_effects=["market"],
                        clustering_variable="governorate",
                        sample_restrictions={
                            "control_changes_only": True,
                            "exclude_ramadan": True,
                            "time_period": "2015-01-01 to 2024-12-31"
                        },
                        robustness_checks=[]
                    )
                ]
            ),
            
            HypothesisConfig(
                hypothesis_id="H3",
                hypothesis_type=HypothesisType.PRIMARY,
                test_type=TestType.TWO_TAILED,
                significance_level=0.0167,  # Bonferroni-corrected
                expected_sign=None,  # No directional prediction
                economic_significance_threshold=0.10,  # 10% effect
                primary_specification=SpecificationConfig(
                    model_formula="log_price ~ conflict + controls",
                    estimation_method="reghdfe",
                    fixed_effects=["market", "month"],
                    clustering_variable="governorate",
                    sample_restrictions={
                        "min_observations_per_market": 24,
                        "exclude_ramadan": True,
                        "time_period": "2015-01-01 to 2024-12-31"
                    },
                    robustness_checks=["alternative_conflict_radius"]
                ),
                robustness_specifications=[
                    SpecificationConfig(
                        model_formula="log_price_yer ~ log_conflict_10km + log_conflict_10km:essential + essential + log_population + distance_to_port",
                        estimation_method="reghdfe",
                        fixed_effects=["market", "month"],
                        clustering_variable="governorate",
                        sample_restrictions={
                            "min_observations_per_market": 24,
                            "exclude_ramadan": True,
                            "time_period": "2015-01-01 to 2024-12-31"
                        },
                        robustness_checks=[]
                    )
                ]
            ),
            
            HypothesisConfig(
                hypothesis_id="H4",
                hypothesis_type=HypothesisType.PRIMARY,
                test_type=TestType.JOINT_TEST,
                significance_level=0.0167,  # Bonferroni-corrected
                expected_sign=None,  # Joint test
                economic_significance_threshold=0.25,  # 25% deviation from theory
                primary_specification=SpecificationConfig(
                    model_formula="price_diff ~ transport_cost + exchange_diff",
                    estimation_method="reg",
                    fixed_effects=[],
                    clustering_variable="market_pair",
                    sample_restrictions={
                        "tradeable_goods_only": True,
                        "max_distance_km": 500,
                        "min_trade_volume": 0,
                        "time_period": "2015-01-01 to 2024-12-31"
                    },
                    robustness_checks=["alternative_transport_costs", "alternative_distance_cutoffs"]
                ),
                robustness_specifications=[
                    SpecificationConfig(
                        model_formula="price_diff ~ transport_cost_fuel_adjusted + exchange_diff",
                        estimation_method="reg",
                        fixed_effects=[],
                        clustering_variable="market_pair",
                        sample_restrictions={
                            "tradeable_goods_only": True,
                            "max_distance_km": 300,
                            "time_period": "2015-01-01 to 2024-12-31"
                        },
                        robustness_checks=[]
                    )
                ]
            )
        ]
    
    def _define_secondary_hypotheses(self) -> List[HypothesisConfig]:
        """Define secondary hypotheses"""
        return [
            HypothesisConfig(
                hypothesis_id="H2",
                hypothesis_type=HypothesisType.SECONDARY,
                test_type=TestType.TWO_TAILED,
                significance_level=0.00125,  # Bonferroni-corrected (0.01/8)
                expected_sign=None,
                economic_significance_threshold=0.05,
                primary_specification=SpecificationConfig(
                    model_formula="log_price_yer ~ aid_intensity + log_population + distance_to_port",
                    estimation_method="ivreghdfe",
                    fixed_effects=["market", "month"],
                    clustering_variable="governorate",
                    sample_restrictions={
                        "aid_data_available": True,
                        "time_period": "2015-01-01 to 2024-12-31"
                    },
                    robustness_checks=["alternative_instruments"]
                ),
                robustness_specifications=[]
            ),
            # Additional secondary hypotheses would be defined here...
        ]
    
    def _define_multiple_testing(self) -> Dict[str, Any]:
        """Define multiple testing correction parameters"""
        return {
            "primary_method": "bonferroni",
            "primary_alpha": 0.05,
            "primary_adjusted_alpha": 0.0167,  # 0.05/3
            "secondary_method": "bonferroni", 
            "secondary_alpha": 0.01,
            "secondary_individual_alpha": 0.001,  # 0.01/10
            "robustness_alpha": 0.10,
            "robustness_correction": None
        }
    
    def _define_sample_criteria(self) -> Dict[str, Any]:
        """Define sample inclusion/exclusion criteria"""
        return {
            "inclusion_criteria": {
                "geographic_scope": "all_yemen_wfp_markets",
                "min_months_per_market": 24,
                "min_commodities_per_market": 5,
                "temporal_scope": "2015-01-01 to 2024-12-31",
                "min_data_availability": 0.6  # 60% completion rate
            },
            "exclusion_criteria": {
                "markets_with_insufficient_data": "<12_months_continuous",
                "extreme_outliers": ">5_std_from_governorate_mean", 
                "disputed_areas": "unclear_currency_zone",
                "ramadan_months": True,
                "major_conflict_escalation_periods": ">200_fatalities_per_month_governorate",
                "wfp_estimated_prices": True
            },
            "projected_final_sample": {
                "markets": 150,
                "time_periods": 108,
                "observations": 45000,
                "balanced_subset": 25000
            }
        }
    
    def _define_variables(self) -> Dict[str, Dict[str, Any]]:
        """Define exact variable construction rules"""
        return {
            "dependent_variables": {
                "price_usd": {
                    "formula": "price_yer / exchange_rate",
                    "exchange_rate_rule": "territorial_control_based",
                    "government_areas": "cby_aden_rate",
                    "houthi_areas": "cby_sanaa_rate",
                    "contested_areas": "population_weighted_average"
                },
                "price_yer": {
                    "formula": "wfp_reported_price_yer",
                    "currency_conversion": "if_usd_multiply_by_rate"
                }
            },
            "key_independent_variables": {
                "zone_houthi": {
                    "definition": "1_if_houthi_control_jan_2015",
                    "source": "acaps_territorial_control_maps",
                    "time_varying": False
                },
                "conflict_intensity": {
                    "formula": "log(1 + acled_fatalities_20km)",
                    "radius": "20km",
                    "source": "acled_battle_deaths",
                    "aggregation": "monthly_sum"
                },
                "essential": {
                    "definition": "1_if_wheat_rice_oil_diesel_petrol",
                    "source": "wfp_classification",
                    "time_varying": False
                }
            },
            "control_variables": {
                "log_population": {
                    "source": "worldpop_governorate",
                    "interpolation": "linear_between_years"
                },
                "distance_to_port": {
                    "formula": "min(distance_hodeidah, distance_aden)",
                    "units": "kilometers",
                    "method": "great_circle_distance"
                }
            }
        }
    
    def _define_robustness_framework(self) -> Dict[str, List[str]]:
        """Define complete robustness check framework"""
        return {
            "specification_robustness": [
                "commodity_time_fixed_effects",
                "governorate_time_fixed_effects", 
                "market_specific_trends",
                "alternative_clustering_market",
                "two_way_clustering_market_time",
                "bootstrapped_standard_errors"
            ],
            "sample_robustness": [
                "balanced_panel_only",
                "exclude_border_markets",
                "exclude_covid_period_2020_2021",
                "alternative_outlier_treatment_99th_percentile"
            ],
            "variable_robustness": [
                "parallel_market_exchange_rates",
                "imf_official_rates_where_available",
                "10km_conflict_radius_instead_20km",
                "conflict_weighted_by_distance_decay",
                "wfp_food_basket_essential_definition",
                "caloric_content_essential_threshold"
            ],
            "methodological_robustness": [
                "quantile_regression_median",
                "instrumental_variables_endogenous_regressors",
                "system_gmm_dynamic_panels",
                "spatial_regression_models",
                "machine_learning_controls",
                "placebo_tests_randomized_treatment"
            ]
        }
    
    # Read-only properties
    @property
    def lock_date(self) -> datetime:
        """Return the lock date"""
        return self._lock_date
    
    @property
    def is_locked(self) -> bool:
        """Check if specifications are locked"""
        return self._is_locked
    
    @property
    def plan_hash(self) -> str:
        """Return plan hash for verification"""
        return self._plan_hash
    
    @property
    def primary_hypotheses(self) -> List[HypothesisConfig]:
        """Return primary hypotheses (immutable)"""
        return self._primary_hypotheses.copy()
    
    @property
    def secondary_hypotheses(self) -> List[HypothesisConfig]:
        """Return secondary hypotheses (immutable)"""
        return self._secondary_hypotheses.copy()
    
    @property
    def multiple_testing_config(self) -> Dict[str, Any]:
        """Return multiple testing configuration (immutable)"""
        return self._multiple_testing_config.copy()
    
    @property
    def sample_criteria(self) -> Dict[str, Any]:
        """Return sample criteria (immutable)"""
        return self._sample_criteria.copy()
    
    @property
    def variable_definitions(self) -> Dict[str, Dict[str, Any]]:
        """Return variable definitions (immutable)"""
        return self._variable_definitions.copy()
    
    @property 
    def robustness_framework(self) -> Dict[str, List[str]]:
        """Return robustness framework (immutable)"""
        return self._robustness_framework.copy()
    
    def get_hypothesis_config(self, hypothesis_id: str) -> Optional[HypothesisConfig]:
        """Get configuration for specific hypothesis"""
        all_hypotheses = self._primary_hypotheses + self._secondary_hypotheses
        for hyp in all_hypotheses:
            if hyp.hypothesis_id == hypothesis_id:
                return hyp
        return None
    
    def get_corrected_alpha(self, hypothesis_id: str) -> float:
        """Get multiple testing corrected alpha for hypothesis"""
        hyp_config = self.get_hypothesis_config(hypothesis_id)
        if hyp_config is None:
            raise ValueError(f"Unknown hypothesis: {hypothesis_id}")
        return hyp_config.significance_level
    
    def validate_specification_integrity(self) -> bool:
        """Validate that specifications remain unchanged"""
        current_hash = self._generate_plan_hash()
        if current_hash != self._plan_hash:
            warnings.warn(
                f"Specification integrity compromised! "
                f"Original hash: {self._plan_hash}, Current hash: {current_hash}",
                RuntimeWarning
            )
            return False
        return True
    
    def __setattr__(self, name: str, value: Any) -> None:
        """Prevent modification of locked specifications"""
        if hasattr(self, '_is_locked') and self._is_locked and not name.startswith('_'):
            raise RuntimeError(f"Cannot modify {name}: specifications are LOCKED")
        super().__setattr__(name, value)
    
    def __delattr__(self, name: str) -> None:
        """Prevent deletion of locked specifications"""
        if hasattr(self, '_is_locked') and self._is_locked:
            raise RuntimeError(f"Cannot delete {name}: specifications are LOCKED")
        super().__delattr__(name)


# Create global locked instance
LOCKED_SPECS = LockedSpecifications()