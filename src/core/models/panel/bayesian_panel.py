"""
Bayesian Panel Models for Yemen Market Integration.

Implements hierarchical Bayesian models that account for:
- Parameter uncertainty across markets and time
- Heterogeneous treatment effects by currency zone
- Missing data patterns driven by conflict
- Structural breaks in exchange rate regimes
"""

import numpy as np
import pandas as pd
import pymc as pm
import arviz as az
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
import logging
from scipy import stats

from ...utils.logging import get_logger
from ..interfaces import Model
from ...domain.market.currency_zones import CurrencyZone

logger = get_logger(__name__)


@dataclass
class BayesianPanelResults:
    """Results from Bayesian panel estimation."""

    # Posterior summaries
    posterior_means: Dict[str, float]
    posterior_sds: Dict[str, float]
    posterior_intervals: Dict[str, Tuple[float, float]]  # 95% credible intervals

    # Model fit
    loo: float  # Leave-one-out cross-validation
    waic: float  # Widely Applicable Information Criterion
    r_hat: Dict[str, float]  # Convergence diagnostics

    # Zone-specific effects
    zone_effects: Dict[str, Dict[str, float]]
    heterogeneity_measures: Dict[str, float]

    # Full posterior samples (for advanced analysis)
    trace: az.InferenceData

    # Model metadata
    n_chains: int
    n_samples: int
    n_observations: int
    converged: bool


class BayesianPanelModel(Model):
    """
    Hierarchical Bayesian panel model for market integration analysis.

    Key features:
    - Hierarchical priors for market-specific effects
    - Time-varying parameters with structural breaks
    - Zone-specific heterogeneity
    - Robust to outliers via Student-t likelihood
    - Handles missing data via imputation
    """

    def __init__(
        self,
        model_type: str = "hierarchical",
        robust: bool = True,
        structural_breaks: bool = True,
        zone_heterogeneity: bool = True,
        n_chains: int = 4,
        n_samples: int = 2000,
        n_tune: int = 1000,
        target_accept: float = 0.9,
    ):
        """
        Initialize Bayesian panel model.

        Args:
            model_type: 'hierarchical', 'pooled', or 'varying_intercept'
            robust: Use Student-t likelihood for outlier robustness
            structural_breaks: Model structural breaks in parameters
            zone_heterogeneity: Allow parameters to vary by currency zone
            n_chains: Number of MCMC chains
            n_samples: Number of posterior samples per chain
            n_tune: Number of tuning samples
            target_accept: Target acceptance rate for NUTS sampler
        """
        self.model_type = model_type
        self.robust = robust
        self.structural_breaks = structural_breaks
        self.zone_heterogeneity = zone_heterogeneity
        self.n_chains = n_chains
        self.n_samples = n_samples
        self.n_tune = n_tune
        self.target_accept = target_accept

        self._model = None
        self._trace = None
        self._is_fitted = False

    @property
    def name(self) -> str:
        return f"Bayesian {self.model_type.title()} Panel Model"

    @property
    def required_data_structure(self) -> str:
        return "panel"

    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate input data."""
        errors = []

        # Check for required columns
        required_cols = [
            "market_id",
            "date",
            "price_usd",
            "exchange_rate_used",
            "currency_zone",
        ]
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            errors.append(f"Missing required columns: {missing_cols}")

        # Check for minimum observations
        if len(data) < 100:
            errors.append(f"Insufficient data: {len(data)} observations (minimum 100)")

        # Check for minimum markets
        if "market_id" in data.columns:
            n_markets = data["market_id"].nunique()
            if n_markets < 5:
                errors.append(f"Insufficient markets: {n_markets} (minimum 5)")

        # Check for minimum time periods
        if "date" in data.columns:
            n_periods = data["date"].nunique()
            if n_periods < 12:
                errors.append(f"Insufficient time periods: {n_periods} (minimum 12)")

        return errors

    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for Bayesian estimation."""
        df = data.copy()

        # Create numeric indices
        df["market_idx"], market_map = pd.factorize(df["market_id"])
        df["time_idx"], time_map = pd.factorize(df["date"])

        # Create zone indicators
        df["zone_north"] = (df["currency_zone"] == CurrencyZone.HOUTHI).astype(int)
        df["zone_south"] = (df["currency_zone"] == CurrencyZone.GOVERNMENT).astype(int)

        # Log transform for elasticity interpretation
        df["log_price_usd"] = np.log(df["price_usd"])
        df["log_exchange_rate"] = np.log(df["exchange_rate_used"])

        # Store mappings
        self._market_map = market_map
        self._time_map = time_map

        return df

    def fit(self, data: pd.DataFrame) -> BayesianPanelResults:
        """
        Fit Bayesian panel model using MCMC.

        Uses NUTS (No-U-Turn Sampler) for efficient exploration of posterior.
        """
        logger.info(f"Fitting {self.name} with {self.n_chains} chains")

        # Prepare data
        df = self.prepare_data(data)

        # Build model based on type
        if self.model_type == "hierarchical":
            model = self._build_hierarchical_model(df)
        elif self.model_type == "pooled":
            model = self._build_pooled_model(df)
        elif self.model_type == "varying_intercept":
            model = self._build_varying_intercept_model(df)
        else:
            raise ValueError(f"Unknown model type: {self.model_type}")

        self._model = model

        # Sample from posterior
        with model:
            trace = pm.sample(
                draws=self.n_samples,
                tune=self.n_tune,
                chains=self.n_chains,
                target_accept=self.target_accept,
                return_inferencedata=True,
                progressbar=True,
            )

        self._trace = trace
        self._is_fitted = True

        # Extract results
        results = self._extract_results(trace, df)

        return results

    def _build_hierarchical_model(self, df: pd.DataFrame) -> pm.Model:
        """
        Build hierarchical Bayesian model.

        Model structure:
        - Global parameters with zone-specific deviations
        - Market-specific random effects
        - Time-varying parameters with potential breaks
        """
        n_markets = df["market_idx"].nunique()
        n_times = df["time_idx"].nunique()

        with pm.Model() as model:
            # Data containers
            market_idx = pm.MutableData("market_idx", df["market_idx"].values)
            time_idx = pm.MutableData("time_idx", df["time_idx"].values)

            # Predictors
            log_exchange = pm.MutableData(
                "log_exchange", df["log_exchange_rate"].values
            )
            conflict = pm.MutableData(
                "conflict", df.get("conflict_intensity", np.zeros(len(df))).values
            )
            zone_north = pm.MutableData("zone_north", df["zone_north"].values)
            zone_south = pm.MutableData("zone_south", df["zone_south"].values)

            # Outcome
            log_price = pm.MutableData("log_price", df["log_price_usd"].values)

            # === Hierarchical Priors ===

            # Global intercept
            mu_alpha = pm.Normal("mu_alpha", mu=0, sigma=1)
            sigma_alpha = pm.Exponential("sigma_alpha", 1)

            # Market-specific intercepts
            alpha_market = pm.Normal(
                "alpha_market", mu=mu_alpha, sigma=sigma_alpha, shape=n_markets
            )

            # Exchange rate effect with zone heterogeneity
            if self.zone_heterogeneity:
                # Base effect
                beta_exchange_base = pm.Normal("beta_exchange_base", mu=0.7, sigma=0.2)

                # Zone-specific deviations
                beta_exchange_north_dev = pm.Normal(
                    "beta_exchange_north_dev", mu=0, sigma=0.1
                )
                beta_exchange_south_dev = pm.Normal(
                    "beta_exchange_south_dev", mu=0, sigma=0.1
                )

                # Combined effect
                beta_exchange = (
                    beta_exchange_base
                    + beta_exchange_north_dev * zone_north
                    + beta_exchange_south_dev * zone_south
                )
            else:
                beta_exchange = pm.Normal("beta_exchange", mu=0.7, sigma=0.2)

            # Conflict effect (also zone-specific if enabled)
            if self.zone_heterogeneity:
                beta_conflict_base = pm.Normal("beta_conflict_base", mu=-0.1, sigma=0.1)
                beta_conflict_north_dev = pm.Normal(
                    "beta_conflict_north_dev", mu=0, sigma=0.05
                )
                beta_conflict_south_dev = pm.Normal(
                    "beta_conflict_south_dev", mu=0, sigma=0.05
                )

                beta_conflict = (
                    beta_conflict_base
                    + beta_conflict_north_dev * zone_north
                    + beta_conflict_south_dev * zone_south
                )
            else:
                beta_conflict = pm.Normal("beta_conflict", mu=-0.1, sigma=0.1)

            # === Structural Breaks ===
            if self.structural_breaks:
                # Detect potential break point (e.g., major policy change)
                break_point = pm.DiscreteUniform(
                    "break_point", lower=10, upper=n_times - 10
                )

                # Pre/post break indicators
                pre_break = pm.math.switch(time_idx < break_point, 1, 0)
                post_break = 1 - pre_break

                # Parameter shift after break
                exchange_shift = pm.Normal("exchange_shift", mu=0, sigma=0.1)
                beta_exchange_final = beta_exchange + exchange_shift * post_break
            else:
                beta_exchange_final = beta_exchange

            # === Model Mean ===
            mu = (
                alpha_market[market_idx]
                + beta_exchange_final * log_exchange
                + beta_conflict * conflict
            )

            # === Likelihood ===
            if self.robust:
                # Student-t for outlier robustness
                nu = pm.Exponential("nu", 1 / 30)  # Degrees of freedom
                sigma = pm.Exponential("sigma", 1)

                pm.StudentT("likelihood", nu=nu, mu=mu, sigma=sigma, observed=log_price)
            else:
                # Normal likelihood
                sigma = pm.Exponential("sigma", 1)

                pm.Normal("likelihood", mu=mu, sigma=sigma, observed=log_price)

            # === Derived Quantities ===
            # Zone differences
            if self.zone_heterogeneity:
                pm.Deterministic(
                    "exchange_effect_diff",
                    beta_exchange_north_dev - beta_exchange_south_dev,
                )
                pm.Deterministic(
                    "conflict_effect_diff",
                    beta_conflict_north_dev - beta_conflict_south_dev,
                )

        return model

    def _build_pooled_model(self, df: pd.DataFrame) -> pm.Model:
        """Build simple pooled Bayesian model."""
        with pm.Model() as model:
            # Data
            log_exchange = pm.MutableData(
                "log_exchange", df["log_exchange_rate"].values
            )
            conflict = pm.MutableData(
                "conflict", df.get("conflict_intensity", np.zeros(len(df))).values
            )
            log_price = pm.MutableData("log_price", df["log_price_usd"].values)

            # Priors
            alpha = pm.Normal("alpha", mu=0, sigma=1)
            beta_exchange = pm.Normal("beta_exchange", mu=0.7, sigma=0.2)
            beta_conflict = pm.Normal("beta_conflict", mu=-0.1, sigma=0.1)

            # Model
            mu = alpha + beta_exchange * log_exchange + beta_conflict * conflict

            # Likelihood
            sigma = pm.Exponential("sigma", 1)
            pm.Normal("likelihood", mu=mu, sigma=sigma, observed=log_price)

        return model

    def _build_varying_intercept_model(self, df: pd.DataFrame) -> pm.Model:
        """Build varying intercept model (random effects)."""
        n_markets = df["market_idx"].nunique()

        with pm.Model() as model:
            # Data
            market_idx = pm.MutableData("market_idx", df["market_idx"].values)
            log_exchange = pm.MutableData(
                "log_exchange", df["log_exchange_rate"].values
            )
            conflict = pm.MutableData(
                "conflict", df.get("conflict_intensity", np.zeros(len(df))).values
            )
            log_price = pm.MutableData("log_price", df["log_price_usd"].values)

            # Hierarchical intercepts
            mu_alpha = pm.Normal("mu_alpha", mu=0, sigma=1)
            sigma_alpha = pm.Exponential("sigma_alpha", 1)
            alpha_market = pm.Normal(
                "alpha_market", mu=mu_alpha, sigma=sigma_alpha, shape=n_markets
            )

            # Fixed slopes
            beta_exchange = pm.Normal("beta_exchange", mu=0.7, sigma=0.2)
            beta_conflict = pm.Normal("beta_conflict", mu=-0.1, sigma=0.1)

            # Model
            mu = (
                alpha_market[market_idx]
                + beta_exchange * log_exchange
                + beta_conflict * conflict
            )

            # Likelihood
            sigma = pm.Exponential("sigma", 1)
            pm.Normal("likelihood", mu=mu, sigma=sigma, observed=log_price)

        return model

    def _extract_results(
        self, trace: az.InferenceData, df: pd.DataFrame
    ) -> BayesianPanelResults:
        """Extract and summarize results from posterior samples."""

        # Basic posterior summaries
        summary = az.summary(trace)

        posterior_means = {}
        posterior_sds = {}
        posterior_intervals = {}
        r_hat = {}

        for param in summary.index:
            posterior_means[param] = summary.loc[param, "mean"]
            posterior_sds[param] = summary.loc[param, "sd"]
            posterior_intervals[param] = (
                summary.loc[param, "hdi_3%"],
                summary.loc[param, "hdi_97%"],
            )
            r_hat[param] = summary.loc[param, "r_hat"]

        # Model comparison metrics
        loo = az.loo(trace)
        waic = az.waic(trace)

        # Check convergence
        converged = all(r <= 1.01 for r in r_hat.values())

        # Zone-specific effects if applicable
        zone_effects = {}
        if self.zone_heterogeneity and self.model_type == "hierarchical":
            zone_effects = {
                "north": {
                    "exchange_rate_effect": posterior_means.get("beta_exchange_base", 0)
                    + posterior_means.get("beta_exchange_north_dev", 0),
                    "conflict_effect": posterior_means.get("beta_conflict_base", 0)
                    + posterior_means.get("beta_conflict_north_dev", 0),
                },
                "south": {
                    "exchange_rate_effect": posterior_means.get("beta_exchange_base", 0)
                    + posterior_means.get("beta_exchange_south_dev", 0),
                    "conflict_effect": posterior_means.get("beta_conflict_base", 0)
                    + posterior_means.get("beta_conflict_south_dev", 0),
                },
            }

        # Heterogeneity measures
        heterogeneity_measures = {}
        if "sigma_alpha" in posterior_means:
            # Intraclass correlation
            total_var = (
                posterior_means["sigma_alpha"] ** 2
                + posterior_means.get("sigma", 1) ** 2
            )
            heterogeneity_measures["icc"] = (
                posterior_means["sigma_alpha"] ** 2 / total_var
            )

        if self.zone_heterogeneity:
            heterogeneity_measures["exchange_rate_heterogeneity"] = posterior_means.get(
                "exchange_effect_diff", 0
            )
            heterogeneity_measures["conflict_heterogeneity"] = posterior_means.get(
                "conflict_effect_diff", 0
            )

        return BayesianPanelResults(
            posterior_means=posterior_means,
            posterior_sds=posterior_sds,
            posterior_intervals=posterior_intervals,
            loo=loo.elpd_loo,
            waic=waic.elpd_waic,
            r_hat=r_hat,
            zone_effects=zone_effects,
            heterogeneity_measures=heterogeneity_measures,
            trace=trace,
            n_chains=self.n_chains,
            n_samples=self.n_samples,
            n_observations=len(df),
            converged=converged,
        )

    def predict(self, new_data: pd.DataFrame) -> np.ndarray:
        """Generate predictions for new data."""
        if not self._is_fitted:
            raise ValueError("Model must be fitted before prediction")

        # Prepare new data
        df_new = self.prepare_data(new_data)

        # Update data in model
        with self._model:
            pm.set_data(
                {
                    "log_exchange": df_new["log_exchange_rate"].values,
                    "conflict": df_new.get(
                        "conflict_intensity", np.zeros(len(df_new))
                    ).values,
                    "zone_north": df_new["zone_north"].values,
                    "zone_south": df_new["zone_south"].values,
                    "market_idx": df_new["market_idx"].values,
                    "time_idx": df_new["time_idx"].values,
                }
            )

            # Generate posterior predictive samples
            posterior_predictive = pm.sample_posterior_predictive(
                self._trace, predictions=True, progressbar=False
            )

        # Return mean predictions
        predictions = posterior_predictive.predictions["likelihood"].mean(
            dim=["chain", "draw"]
        )

        # Transform back from log scale
        return np.exp(predictions.values)

    def plot_diagnostics(self) -> None:
        """Plot diagnostic plots for model checking."""
        if not self._is_fitted:
            raise ValueError("Model must be fitted before plotting diagnostics")

        # Trace plots
        az.plot_trace(self._trace)

        # Posterior plots
        az.plot_posterior(self._trace)

        # Energy plot
        az.plot_energy(self._trace)

        # Autocorrelation
        az.plot_autocorr(self._trace)

    def get_diagnostics(self) -> List[str]:
        """
        Get list of diagnostic tests applicable to this model.
        
        Returns list of diagnostic test names.
        """
        diagnostics = [
            "trace_plot",
            "posterior_predictive_check",
            "rhat_convergence",
            "effective_sample_size",
            "energy_plot",
            "divergences",
            "loo_cross_validation",
            "waic"
        ]
        
        if self.structural_breaks:
            diagnostics.append("structural_break_detection")
            
        if self.zone_heterogeneity:
            diagnostics.extend([
                "zone_heterogeneity_test",
                "zone_parameter_comparison"
            ])
            
        return diagnostics
    
    @property
    def is_fitted(self) -> bool:
        return self._is_fitted

    @property
    def results(self) -> Optional[BayesianPanelResults]:
        if self._is_fitted and hasattr(self, "_results"):
            return self._results
        return None
