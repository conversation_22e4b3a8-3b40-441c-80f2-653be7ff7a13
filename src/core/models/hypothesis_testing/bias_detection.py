"""
Bias Detection Tools for Hypothesis Testing

Tools to detect and prevent confirmation bias, p-hacking, and other
forms of analytical bias in hypothesis testing.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import json
import hashlib

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class AnalysisRecord:
    """Record of a single analysis attempt."""
    timestamp: datetime
    hypothesis_id: str
    specification: Dict[str, Any]
    p_value: float
    effect_size: float
    reported: bool = False
    notes: Optional[str] = None


@dataclass 
class PreAnalysisPlan:
    """Pre-registered analysis plan."""
    registered_date: datetime
    hypotheses: List[str]
    primary_specifications: List[Dict[str, Any]]
    decision_rules: Dict[str, Any]
    power_analysis: Dict[str, float]
    hash: str  # Hash of the plan for verification


class BiasDetector:
    """Tools to detect and prevent confirmation bias in analysis."""
    
    def __init__(self, analysis_log_path: Optional[str] = None):
        """
        Initialize bias detector.
        
        Args:
            analysis_log_path: Path to store analysis history
        """
        self.analysis_history: List[AnalysisRecord] = []
        self.log_path = analysis_log_path
        self.pre_analysis_plan: Optional[PreAnalysisPlan] = None
        
        if self.log_path:
            self._load_history()
            
    def register_pre_analysis_plan(self, plan: Dict[str, Any]) -> PreAnalysisPlan:
        """Register a pre-analysis plan before looking at data."""
        plan_str = json.dumps(plan, sort_keys=True)
        plan_hash = hashlib.sha256(plan_str.encode()).hexdigest()
        
        self.pre_analysis_plan = PreAnalysisPlan(
            registered_date=datetime.now(),
            hypotheses=plan.get('hypotheses', []),
            primary_specifications=plan.get('primary_specifications', []),
            decision_rules=plan.get('decision_rules', {}),
            power_analysis=plan.get('power_analysis', {}),
            hash=plan_hash
        )
        
        logger.info(f"Pre-analysis plan registered with hash: {plan_hash}")
        return self.pre_analysis_plan
        
    def log_analysis(self, hypothesis_id: str, specification: Dict[str, Any],
                    p_value: float, effect_size: float, 
                    reported: bool = False, notes: Optional[str] = None) -> None:
        """Log an analysis attempt."""
        record = AnalysisRecord(
            timestamp=datetime.now(),
            hypothesis_id=hypothesis_id,
            specification=specification,
            p_value=p_value,
            effect_size=effect_size,
            reported=reported,
            notes=notes
        )
        
        self.analysis_history.append(record)
        
        if self.log_path:
            self._save_history()
            
    def check_p_hacking(self, hypothesis_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Detect potential p-hacking behavior.
        
        Returns dict with:
        - p_hacking_risk: bool
        - total_tests: int
        - reported_tests: int
        - selective_reporting_ratio: float
        - suspicious_patterns: List[str]
        """
        if hypothesis_id:
            records = [r for r in self.analysis_history if r.hypothesis_id == hypothesis_id]
        else:
            records = self.analysis_history
            
        if not records:
            return {'p_hacking_risk': False, 'total_tests': 0}
            
        total_tests = len(records)
        reported_tests = sum(1 for r in records if r.reported)
        
        suspicious_patterns = []
        
        # Check 1: Selective reporting
        if total_tests > 5 and reported_tests < total_tests * 0.5:
            selective_ratio = reported_tests / total_tests
            suspicious_patterns.append(
                f"Selective reporting detected: {reported_tests}/{total_tests} "
                f"({selective_ratio:.1%}) tests reported"
            )
            
        # Check 2: Multiple testing without correction
        p_values = [r.p_value for r in records]
        significant_tests = sum(1 for p in p_values if p < 0.05)
        expected_false_positives = total_tests * 0.05
        
        if significant_tests > expected_false_positives * 2 and total_tests > 10:
            suspicious_patterns.append(
                f"Excess significant results: {significant_tests} significant "
                f"(expected ~{expected_false_positives:.1f} by chance)"
            )
            
        # Check 3: P-value clustering near 0.05
        near_threshold = sum(1 for p in p_values if 0.04 < p < 0.06)
        if near_threshold / total_tests > 0.2:
            suspicious_patterns.append(
                f"P-values clustering near 0.05: {near_threshold}/{total_tests}"
            )
            
        # Check 4: Specification searching
        unique_specs = len(set(
            json.dumps(r.specification, sort_keys=True) 
            for r in records
        ))
        if unique_specs > total_tests * 0.8:
            suspicious_patterns.append(
                f"Specification searching: {unique_specs} unique specifications tried"
            )
            
        return {
            'p_hacking_risk': len(suspicious_patterns) > 0,
            'total_tests': total_tests,
            'reported_tests': reported_tests,
            'selective_reporting_ratio': reported_tests / total_tests if total_tests > 0 else 0,
            'suspicious_patterns': suspicious_patterns
        }
        
    def check_specification_searching(self, hypothesis_id: str) -> Dict[str, Any]:
        """Detect if analyst is searching for favorable specifications."""
        records = [r for r in self.analysis_history if r.hypothesis_id == hypothesis_id]
        
        if len(records) < 2:
            return {'specification_searching': False}
            
        # Group by time windows (e.g., within same session)
        time_threshold = pd.Timedelta(hours=2)
        sessions = []
        current_session = [records[0]]
        
        for i in range(1, len(records)):
            if records[i].timestamp - records[i-1].timestamp < time_threshold:
                current_session.append(records[i])
            else:
                sessions.append(current_session)
                current_session = [records[i]]
        sessions.append(current_session)
        
        # Check each session for searching behavior
        searching_indicators = []
        
        for session in sessions:
            if len(session) > 3:
                # Many attempts in one session
                p_values = [r.p_value for r in session]
                
                # Check if p-values are improving (getting smaller)
                improving = all(p_values[i] <= p_values[i-1] * 1.1 
                              for i in range(1, len(p_values)))
                
                if improving:
                    searching_indicators.append(
                        f"P-values improving over {len(session)} attempts in session"
                    )
                    
                # Check if stopped after finding significance
                if p_values[-1] < 0.05 and session[-1].reported:
                    searching_indicators.append(
                        "Stopped testing after finding significant result"
                    )
                    
        return {
            'specification_searching': len(searching_indicators) > 0,
            'n_sessions': len(sessions),
            'attempts_per_session': [len(s) for s in sessions],
            'indicators': searching_indicators
        }
        
    def enforce_pre_analysis_plan(self, hypothesis_id: str, 
                                specification: Dict[str, Any]) -> Dict[str, Any]:
        """Check if current analysis follows pre-analysis plan."""
        if not self.pre_analysis_plan:
            return {
                'follows_plan': False,
                'reason': 'No pre-analysis plan registered'
            }
            
        # Check if hypothesis was pre-registered
        if hypothesis_id not in self.pre_analysis_plan.hypotheses:
            return {
                'follows_plan': False,
                'reason': f'Hypothesis {hypothesis_id} not in pre-analysis plan',
                'registered_hypotheses': self.pre_analysis_plan.hypotheses
            }
            
        # Check if specification matches pre-registered specs
        spec_str = json.dumps(specification, sort_keys=True)
        registered_specs = [
            json.dumps(s, sort_keys=True) 
            for s in self.pre_analysis_plan.primary_specifications
        ]
        
        if spec_str not in registered_specs:
            return {
                'follows_plan': False,
                'reason': 'Specification not in pre-analysis plan',
                'is_exploratory': True
            }
            
        return {
            'follows_plan': True,
            'plan_date': self.pre_analysis_plan.registered_date,
            'plan_hash': self.pre_analysis_plan.hash
        }
        
    def calculate_false_discovery_rate(self, p_values: List[float], 
                                     alpha: float = 0.05) -> Tuple[List[bool], List[float]]:
        """
        Calculate false discovery rate adjusted p-values.
        
        Returns:
            Tuple of (rejected, adjusted_p_values)
        """
        from statsmodels.stats.multitest import multipletests
        
        if not p_values:
            return [], []
            
        rejected, adjusted_p, _, _ = multipletests(
            p_values,
            method='fdr_bh',  # Benjamini-Hochberg
            alpha=alpha
        )
        
        return list(rejected), list(adjusted_p)
        
    def generate_bias_report(self) -> str:
        """Generate comprehensive bias detection report."""
        report = ["Bias Detection Report", "=" * 50, ""]
        
        # Overall statistics
        report.append(f"Total analyses logged: {len(self.analysis_history)}")
        
        if self.pre_analysis_plan:
            report.append(f"Pre-analysis plan registered: {self.pre_analysis_plan.registered_date}")
            report.append(f"Plan hash: {self.pre_analysis_plan.hash}")
        else:
            report.append("WARNING: No pre-analysis plan registered")
            
        report.append("")
        
        # P-hacking assessment
        p_hack_check = self.check_p_hacking()
        report.append("P-Hacking Assessment:")
        report.append(f"  Risk detected: {p_hack_check['p_hacking_risk']}")
        report.append(f"  Total tests: {p_hack_check['total_tests']}")
        report.append(f"  Reported tests: {p_hack_check['reported_tests']}")
        
        if p_hack_check['suspicious_patterns']:
            report.append("  Suspicious patterns:")
            for pattern in p_hack_check['suspicious_patterns']:
                report.append(f"    - {pattern}")
                
        report.append("")
        
        # By hypothesis
        hypotheses = set(r.hypothesis_id for r in self.analysis_history)
        
        for hyp_id in sorted(hypotheses):
            report.append(f"Hypothesis {hyp_id}:")
            
            # Specification searching
            spec_check = self.check_specification_searching(hyp_id)
            report.append(f"  Specification searching: {spec_check['specification_searching']}")
            
            if spec_check['indicators']:
                for indicator in spec_check['indicators']:
                    report.append(f"    - {indicator}")
                    
            # Calculate FDR for this hypothesis
            records = [r for r in self.analysis_history if r.hypothesis_id == hyp_id]
            p_values = [r.p_value for r in records]
            
            if p_values:
                rejected, adjusted_p = self.calculate_false_discovery_rate(p_values)
                report.append(f"  Multiple testing correction:")
                report.append(f"    - Unadjusted significant: {sum(p < 0.05 for p in p_values)}")
                report.append(f"    - FDR adjusted significant: {sum(rejected)}")
                
            report.append("")
            
        return "\n".join(report)
        
    def _save_history(self) -> None:
        """Save analysis history to file."""
        if not self.log_path:
            return
            
        data = []
        for record in self.analysis_history:
            data.append({
                'timestamp': record.timestamp.isoformat(),
                'hypothesis_id': record.hypothesis_id,
                'specification': record.specification,
                'p_value': record.p_value,
                'effect_size': record.effect_size,
                'reported': record.reported,
                'notes': record.notes
            })
            
        with open(self.log_path, 'w') as f:
            json.dump(data, f, indent=2)
            
    def _load_history(self) -> None:
        """Load analysis history from file."""
        if not self.log_path:
            return
            
        try:
            with open(self.log_path, 'r') as f:
                data = json.load(f)
                
            for item in data:
                record = AnalysisRecord(
                    timestamp=datetime.fromisoformat(item['timestamp']),
                    hypothesis_id=item['hypothesis_id'],
                    specification=item['specification'],
                    p_value=item['p_value'],
                    effect_size=item['effect_size'],
                    reported=item.get('reported', False),
                    notes=item.get('notes')
                )
                self.analysis_history.append(record)
                
        except FileNotFoundError:
            logger.info(f"No existing history file at {self.log_path}")
        except Exception as e:
            logger.error(f"Error loading history: {e}")


def create_pre_analysis_plan(hypotheses: List[str],
                           specifications: List[Dict[str, Any]],
                           alpha: float = 0.05,
                           power: float = 0.80) -> Dict[str, Any]:
    """Helper to create a pre-analysis plan."""
    return {
        'hypotheses': hypotheses,
        'primary_specifications': specifications,
        'decision_rules': {
            'alpha': alpha,
            'power': power,
            'multiple_testing_correction': 'fdr_bh',
            'minimum_effect_size': 0.10  # Define what's meaningful
        },
        'power_analysis': {
            'target_power': power,
            'alpha': alpha,
            'assumed_effect_size': 0.30
        },
        'registered_date': datetime.now().isoformat()
    }