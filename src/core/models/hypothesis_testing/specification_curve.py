"""
Specification Curve Analysis for Hypothesis Testing

Implements specification curve analysis to prevent cherry-picking and ensure
robustness of results across reasonable model specifications.

Based on <PERSON><PERSON><PERSON>, <PERSON> (2020) "Specification curve analysis"
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Callable, Optional, Tuple
from dataclasses import dataclass
from itertools import product
import matplotlib.pyplot as plt
from scipy import stats

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class SpecificationChoice:
    """Represents a single choice in specification space."""
    name: str
    options: List[Any]
    description: str


@dataclass
class SpecificationResult:
    """Results from running a single specification."""
    specification: Dict[str, Any]
    coefficient: float
    std_error: float
    p_value: float
    confidence_interval: Tuple[float, float]
    n_observations: int
    r_squared: Optional[float] = None
    additional_stats: Optional[Dict[str, Any]] = None


class SpecificationCurve:
    """
    Run all reasonable model specifications to avoid cherry-picking.
    
    This class helps researchers run multiple reasonable specifications
    of their analysis and visualize the results to show robustness
    (or lack thereof) to analytical choices.
    """
    
    def __init__(self, base_analysis_function: Callable):
        """
        Initialize specification curve analysis.
        
        Args:
            base_analysis_function: Function that takes (data, **spec_kwargs)
                                   and returns SpecificationResult
        """
        self.base_analysis_function = base_analysis_function
        self.specification_choices: List[SpecificationChoice] = []
        self.results: List[SpecificationResult] = []
        
    def add_specification_choice(self, name: str, options: List[Any], 
                               description: str) -> None:
        """Add a specification choice dimension."""
        self.specification_choices.append(
            SpecificationChoice(name=name, options=options, description=description)
        )
        
    def generate_all_specifications(self) -> List[Dict[str, Any]]:
        """Generate all combinations of specification choices."""
        if not self.specification_choices:
            return [{}]
            
        # Get all option lists
        option_lists = [choice.options for choice in self.specification_choices]
        choice_names = [choice.name for choice in self.specification_choices]
        
        # Generate all combinations
        specifications = []
        for combination in product(*option_lists):
            spec = dict(zip(choice_names, combination))
            specifications.append(spec)
            
        return specifications
    
    def run_all_specifications(self, data: pd.DataFrame, 
                             parallel: bool = False,
                             max_workers: Optional[int] = None) -> None:
        """
        Run all specifications.
        
        Args:
            data: Input data for analysis
            parallel: Whether to run specifications in parallel
            max_workers: Maximum number of parallel workers
        """
        specifications = self.generate_all_specifications()
        logger.info(f"Running {len(specifications)} specifications")
        
        self.results = []
        
        if parallel and len(specifications) > 10:
            # Parallel execution for large specification sets
            from concurrent.futures import ProcessPoolExecutor
            
            with ProcessPoolExecutor(max_workers=max_workers) as executor:
                futures = []
                for spec in specifications:
                    future = executor.submit(
                        self._run_single_specification, data, spec
                    )
                    futures.append((future, spec))
                
                for future, spec in futures:
                    try:
                        result = future.result()
                        if result:
                            self.results.append(result)
                    except Exception as e:
                        logger.warning(f"Specification failed: {spec}, error: {e}")
        else:
            # Sequential execution
            for i, spec in enumerate(specifications):
                if i % 10 == 0:
                    logger.info(f"Progress: {i}/{len(specifications)} specifications")
                    
                result = self._run_single_specification(data, spec)
                if result:
                    self.results.append(result)
                    
        logger.info(f"Completed {len(self.results)} specifications successfully")
        
    def _run_single_specification(self, data: pd.DataFrame, 
                                specification: Dict[str, Any]) -> Optional[SpecificationResult]:
        """Run a single specification."""
        try:
            result = self.base_analysis_function(data, **specification)
            return result
        except Exception as e:
            logger.debug(f"Specification {specification} failed: {e}")
            return None
            
    def get_summary_statistics(self) -> Dict[str, Any]:
        """Get summary statistics across all specifications."""
        if not self.results:
            return {}
            
        coefficients = [r.coefficient for r in self.results]
        p_values = [r.p_value for r in self.results]
        significant = [p < 0.05 for p in p_values]
        
        # Check sign consistency
        positive = [c > 0 for c in coefficients]
        sign_consistency = max(sum(positive), sum(not p for p in positive)) / len(positive)
        
        return {
            'n_specifications': len(self.results),
            'median_coefficient': np.median(coefficients),
            'mean_coefficient': np.mean(coefficients),
            'std_coefficient': np.std(coefficients),
            'coefficient_range': (min(coefficients), max(coefficients)),
            'prop_significant': sum(significant) / len(significant),
            'sign_consistency': sign_consistency,
            'median_p_value': np.median(p_values),
            'robust': sum(significant) / len(significant) > 0.95  # 95% significant
        }
        
    def plot_specification_curve(self, save_path: Optional[str] = None) -> None:
        """Create specification curve plot."""
        if not self.results:
            logger.warning("No results to plot")
            return
            
        # Sort results by coefficient value
        sorted_results = sorted(self.results, key=lambda r: r.coefficient)
        
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10), 
                                           gridspec_kw={'height_ratios': [3, 1, 2]})
        
        # Panel 1: Specification curve
        coefficients = [r.coefficient for r in sorted_results]
        ci_lower = [r.confidence_interval[0] for r in sorted_results]
        ci_upper = [r.confidence_interval[1] for r in sorted_results]
        significant = [r.p_value < 0.05 for r in sorted_results]
        
        x = range(len(sorted_results))
        colors = ['blue' if sig else 'gray' for sig in significant]
        
        ax1.scatter(x, coefficients, c=colors, alpha=0.6, s=20)
        ax1.fill_between(x, ci_lower, ci_upper, alpha=0.2, color='gray')
        ax1.axhline(y=0, color='red', linestyle='--', alpha=0.5)
        ax1.axhline(y=np.median(coefficients), color='green', linestyle='-', alpha=0.7)
        ax1.set_ylabel('Effect Size')
        ax1.set_title('Specification Curve: All Reasonable Specifications')
        ax1.grid(True, alpha=0.3)
        
        # Panel 2: P-values
        p_values = [r.p_value for r in sorted_results]
        ax2.scatter(x, p_values, c=colors, alpha=0.6, s=20)
        ax2.axhline(y=0.05, color='red', linestyle='--', alpha=0.5)
        ax2.set_ylabel('P-value')
        ax2.set_ylim(0, 1)
        ax2.grid(True, alpha=0.3)
        
        # Panel 3: Specification choices
        if self.specification_choices:
            choice_matrix = []
            for result in sorted_results:
                row = []
                for choice in self.specification_choices:
                    value = result.specification.get(choice.name)
                    # Convert to numeric for visualization
                    if isinstance(value, bool):
                        row.append(1 if value else 0)
                    elif isinstance(value, str):
                        # Create numeric mapping
                        options = choice.options
                        row.append(options.index(value) if value in options else -1)
                    else:
                        row.append(value if value is not None else -1)
                choice_matrix.append(row)
                
            choice_matrix = np.array(choice_matrix).T
            
            im = ax3.imshow(choice_matrix, aspect='auto', cmap='viridis')
            ax3.set_yticks(range(len(self.specification_choices)))
            ax3.set_yticklabels([c.name for c in self.specification_choices])
            ax3.set_xlabel('Specification Number')
            ax3.set_title('Specification Choices')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        else:
            plt.show()
            
    def get_robust_estimate(self) -> Dict[str, Any]:
        """Get robust estimate based on all specifications."""
        if not self.results:
            return {}
            
        # Weight by inverse of standard error (precision weighting)
        weights = [1 / r.std_error for r in self.results]
        total_weight = sum(weights)
        weights = [w / total_weight for w in weights]
        
        # Weighted average
        weighted_coef = sum(r.coefficient * w for r, w in zip(self.results, weights))
        
        # Bootstrap confidence interval
        n_bootstrap = 1000
        bootstrap_coefs = []
        
        for _ in range(n_bootstrap):
            # Resample specifications with replacement
            sampled_results = np.random.choice(self.results, size=len(self.results), replace=True)
            sampled_coefs = [r.coefficient for r in sampled_results]
            bootstrap_coefs.append(np.median(sampled_coefs))
            
        ci_lower = np.percentile(bootstrap_coefs, 2.5)
        ci_upper = np.percentile(bootstrap_coefs, 97.5)
        
        return {
            'robust_coefficient': weighted_coef,
            'median_coefficient': np.median([r.coefficient for r in self.results]),
            'bootstrap_ci': (ci_lower, ci_upper),
            'n_specifications': len(self.results)
        }
        
    def analyze_specification_importance(self) -> Dict[str, float]:
        """Analyze which specification choices matter most."""
        if not self.results or not self.specification_choices:
            return {}
            
        importance = {}
        
        for choice in self.specification_choices:
            # Group results by this choice
            groups = {}
            for result in self.results:
                option = result.specification.get(choice.name)
                if option not in groups:
                    groups[option] = []
                groups[option].append(result.coefficient)
                
            # ANOVA to test if choice matters
            if len(groups) > 1:
                f_stat, p_value = stats.f_oneway(*groups.values())
                importance[choice.name] = 1 - p_value  # Higher = more important
            else:
                importance[choice.name] = 0
                
        # Normalize to sum to 1
        total_importance = sum(importance.values())
        if total_importance > 0:
            importance = {k: v/total_importance for k, v in importance.items()}
            
        return importance


def create_standard_specifications() -> SpecificationCurve:
    """Create standard specification choices for econometric analysis."""
    curve = SpecificationCurve(None)  # Will set function later
    
    # Fixed effects
    curve.add_specification_choice(
        name='fixed_effects',
        options=['none', 'entity', 'time', 'both'],
        description='Fixed effects specification'
    )
    
    # Clustering
    curve.add_specification_choice(
        name='clustering',
        options=['none', 'entity', 'time', 'twoway'],
        description='Standard error clustering'
    )
    
    # Outlier handling
    curve.add_specification_choice(
        name='outlier_handling',
        options=['none', 'winsorize_1pct', 'winsorize_5pct', 'trim_1pct'],
        description='Outlier treatment method'
    )
    
    # Sample restrictions
    curve.add_specification_choice(
        name='balanced_panel',
        options=[True, False],
        description='Restrict to balanced panel'
    )
    
    # Time trends
    curve.add_specification_choice(
        name='time_trends',
        options=['none', 'linear', 'quadratic', 'entity_specific'],
        description='Time trend specification'
    )
    
    return curve