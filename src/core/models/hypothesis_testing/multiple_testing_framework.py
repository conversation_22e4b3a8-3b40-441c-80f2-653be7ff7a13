"""
Multiple Testing and Power Analysis Framework.

Implements proper False Discovery Rate (FDR) control and power analysis
protocols to address the critical statistical inference issues identified
in the Yemen Market Integration methodology.

CRITICAL FIX: Addresses inadequate multiple testing procedures that could
lead to inflated Type I error rates across the H1-H10 hypothesis framework.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass
from enum import Enum
from scipy import stats
from statsmodels.stats.multitest import multipletests
from statsmodels.stats.power import ttest_power, ftest_power
import logging

logger = logging.getLogger(__name__)


class MultipleTestingMethod(Enum):
    """Methods for multiple testing correction."""
    BONFERRONI = "bonferroni"
    HOLM = "holm"
    SIDAK = "sidak"
    BENJAMINI_HOCHBERG = "fdr_bh"
    BENJAMINI_YEKUTIELI = "fdr_by"
    ROMANO_WOLF = "romano_wolf"


class PowerAnalysisType(Enum):
    """Types of power analysis."""
    EX_ANTE = "ex_ante"  # Before data collection
    EX_POST = "ex_post"  # After data analysis
    SIMULATION = "simulation"  # Bootstrap-based
    MINIMUM_DETECTABLE_EFFECT = "mde"


@dataclass
class HypothesisTest:
    """Individual hypothesis test result."""
    hypothesis_id: str
    test_statistic: float
    p_value: float
    effect_size: float
    standard_error: float
    n_observations: int
    test_type: str  # t-test, F-test, etc.
    

@dataclass
class MultipleTestingResults:
    """Results from multiple testing correction."""
    
    # Original test results
    original_tests: List[HypothesisTest]
    
    # Corrected results
    corrected_p_values: Dict[str, float]
    significant_hypotheses: List[str]
    rejected_hypotheses: List[str]
    
    # Method details
    correction_method: MultipleTestingMethod
    family_wise_error_rate: float
    false_discovery_rate: float
    
    # Diagnostic information
    discovery_proportion: float
    expected_false_discoveries: float
    power_estimates: Dict[str, float]
    
    # Hierarchical testing (if applicable)
    primary_hypotheses: List[str]
    secondary_hypotheses: List[str]
    exploratory_hypotheses: List[str]


@dataclass
class PowerAnalysisResults:
    """Results from power analysis."""
    
    # Power calculations
    statistical_power: Dict[str, float]
    minimum_detectable_effects: Dict[str, float]
    required_sample_sizes: Dict[str, int]
    
    # Analysis type and parameters
    analysis_type: PowerAnalysisType
    alpha_level: float
    target_power: float
    
    # Effect size assumptions
    assumed_effect_sizes: Dict[str, float]
    effect_size_justification: Dict[str, str]
    
    # Recommendations
    adequately_powered_tests: List[str]
    underpowered_tests: List[str]
    sample_size_recommendations: Dict[str, str]


class MultipleTestingFramework:
    """
    Comprehensive framework for multiple testing correction and power analysis.
    
    Implements state-of-the-art methods for controlling Type I and Type II
    error rates in the context of testing multiple economic hypotheses.
    """
    
    def __init__(self, 
                 primary_alpha: float = 0.05,
                 secondary_alpha: float = 0.10,
                 target_power: float = 0.80):
        """
        Initialize multiple testing framework.
        
        Args:
            primary_alpha: Significance level for primary hypotheses
            secondary_alpha: Significance level for secondary hypotheses
            target_power: Target statistical power for power analysis
        """
        self.primary_alpha = primary_alpha
        self.secondary_alpha = secondary_alpha
        self.target_power = target_power
        
        # Track hypothesis hierarchy
        self.primary_hypotheses = []
        self.secondary_hypotheses = []
        self.exploratory_hypotheses = []
        
    def register_hypothesis_hierarchy(self,
                                    primary: List[str],
                                    secondary: List[str] = None,
                                    exploratory: List[str] = None):
        """
        Register hypothesis testing hierarchy.
        
        Args:
            primary: Primary hypotheses (pre-registered, confirmatory)
            secondary: Secondary hypotheses (pre-specified, important)
            exploratory: Exploratory hypotheses (post-hoc, descriptive)
        """
        self.primary_hypotheses = primary or []
        self.secondary_hypotheses = secondary or []
        self.exploratory_hypotheses = exploratory or []
        
        logger.info(f"Registered hypothesis hierarchy: "
                   f"{len(self.primary_hypotheses)} primary, "
                   f"{len(self.secondary_hypotheses)} secondary, "
                   f"{len(self.exploratory_hypotheses)} exploratory")
        
    def apply_multiple_testing_correction(self,
                                        test_results: List[HypothesisTest],
                                        method: MultipleTestingMethod = MultipleTestingMethod.BENJAMINI_HOCHBERG,
                                        hierarchical: bool = True) -> MultipleTestingResults:
        """
        Apply multiple testing correction to hypothesis test results.
        
        Args:
            test_results: List of individual hypothesis test results
            method: Multiple testing correction method
            hierarchical: Whether to apply hierarchical testing procedure
            
        Returns:
            MultipleTestingResults with corrected p-values and decisions
        """
        logger.info(f"Applying {method.value} correction to {len(test_results)} tests")
        
        if hierarchical and (self.primary_hypotheses or self.secondary_hypotheses):
            return self._apply_hierarchical_correction(test_results, method)
        else:
            return self._apply_standard_correction(test_results, method)
            
    def _apply_standard_correction(self,
                                 test_results: List[HypothesisTest],
                                 method: MultipleTestingMethod) -> MultipleTestingResults:
        """Apply standard multiple testing correction."""
        
        # Extract p-values and hypothesis IDs
        p_values = [test.p_value for test in test_results]
        hypothesis_ids = [test.hypothesis_id for test in test_results]
        
        # Apply correction
        if method == MultipleTestingMethod.ROMANO_WOLF:
            # Romano-Wolf requires bootstrap - simplified implementation
            corrected_p_values = self._romano_wolf_correction(test_results)
        else:
            # Use statsmodels implementation
            rejected, corrected_p_vals, _, _ = multipletests(
                p_values, alpha=self.primary_alpha, method=method.value
            )
            corrected_p_values = dict(zip(hypothesis_ids, corrected_p_vals))
            
        # Determine significant and rejected hypotheses
        significant_hypotheses = [
            hyp_id for hyp_id, p_val in corrected_p_values.items()
            if p_val < self.primary_alpha
        ]
        
        rejected_hypotheses = [
            hyp_id for hyp_id in hypothesis_ids
            if hyp_id not in significant_hypotheses
        ]
        
        # Calculate diagnostic metrics
        discovery_proportion = len(significant_hypotheses) / len(test_results) if test_results else 0
        
        if method in [MultipleTestingMethod.BENJAMINI_HOCHBERG, MultipleTestingMethod.BENJAMINI_YEKUTIELI]:
            false_discovery_rate = self.primary_alpha
            expected_false_discoveries = discovery_proportion * false_discovery_rate * len(test_results)
        else:
            false_discovery_rate = 0.0  # FWER methods don't control FDR directly
            expected_false_discoveries = 0.0
            
        # Estimate power for each test
        power_estimates = self._estimate_test_power(test_results, corrected_p_values)
        
        return MultipleTestingResults(
            original_tests=test_results,
            corrected_p_values=corrected_p_values,
            significant_hypotheses=significant_hypotheses,
            rejected_hypotheses=rejected_hypotheses,
            correction_method=method,
            family_wise_error_rate=self.primary_alpha if method != MultipleTestingMethod.BENJAMINI_HOCHBERG else 0.0,
            false_discovery_rate=false_discovery_rate,
            discovery_proportion=discovery_proportion,
            expected_false_discoveries=expected_false_discoveries,
            power_estimates=power_estimates,
            primary_hypotheses=self.primary_hypotheses,
            secondary_hypotheses=self.secondary_hypotheses,
            exploratory_hypotheses=self.exploratory_hypotheses
        )
        
    def _apply_hierarchical_correction(self,
                                     test_results: List[HypothesisTest],
                                     method: MultipleTestingMethod) -> MultipleTestingResults:
        """Apply hierarchical multiple testing procedure."""
        
        # Separate tests by hierarchy level
        primary_tests = [t for t in test_results if t.hypothesis_id in self.primary_hypotheses]
        secondary_tests = [t for t in test_results if t.hypothesis_id in self.secondary_hypotheses]
        exploratory_tests = [t for t in test_results if t.hypothesis_id in self.exploratory_hypotheses]
        
        corrected_p_values = {}
        significant_hypotheses = []
        
        # Step 1: Test primary hypotheses
        if primary_tests:
            logger.info(f"Testing {len(primary_tests)} primary hypotheses at α = {self.primary_alpha}")
            
            primary_p_values = [t.p_value for t in primary_tests]
            primary_ids = [t.hypothesis_id for t in primary_tests]
            
            _, primary_corrected, _, _ = multipletests(
                primary_p_values, alpha=self.primary_alpha, method=method.value
            )
            
            for i, hyp_id in enumerate(primary_ids):
                corrected_p_values[hyp_id] = primary_corrected[i]
                if primary_corrected[i] < self.primary_alpha:
                    significant_hypotheses.append(hyp_id)
                    
        # Step 2: Test secondary hypotheses (only if at least one primary is significant)
        if secondary_tests and significant_hypotheses:
            logger.info(f"Testing {len(secondary_tests)} secondary hypotheses at α = {self.secondary_alpha}")
            
            secondary_p_values = [t.p_value for t in secondary_tests]
            secondary_ids = [t.hypothesis_id for t in secondary_tests]
            
            _, secondary_corrected, _, _ = multipletests(
                secondary_p_values, alpha=self.secondary_alpha, method=method.value
            )
            
            for i, hyp_id in enumerate(secondary_ids):
                corrected_p_values[hyp_id] = secondary_corrected[i]
                if secondary_corrected[i] < self.secondary_alpha:
                    significant_hypotheses.append(hyp_id)
                    
        elif secondary_tests and not significant_hypotheses:
            logger.info("No primary hypotheses significant - skipping secondary tests")
            for test in secondary_tests:
                corrected_p_values[test.hypothesis_id] = 1.0  # Force non-significance
                
        # Step 3: Test exploratory hypotheses (descriptive only)
        if exploratory_tests:
            logger.info(f"Reporting {len(exploratory_tests)} exploratory tests (descriptive only)")
            
            for test in exploratory_tests:
                corrected_p_values[test.hypothesis_id] = test.p_value  # No correction
                # Don't add to significant_hypotheses - these are descriptive only
                
        # Determine rejected hypotheses
        rejected_hypotheses = [
            test.hypothesis_id for test in test_results
            if test.hypothesis_id not in significant_hypotheses
        ]
        
        # Calculate diagnostic metrics
        discovery_proportion = len(significant_hypotheses) / len(test_results) if test_results else 0
        
        # Power estimates
        power_estimates = self._estimate_test_power(test_results, corrected_p_values)
        
        return MultipleTestingResults(
            original_tests=test_results,
            corrected_p_values=corrected_p_values,
            significant_hypotheses=significant_hypotheses,
            rejected_hypotheses=rejected_hypotheses,
            correction_method=method,
            family_wise_error_rate=self.primary_alpha,
            false_discovery_rate=self.primary_alpha if method == MultipleTestingMethod.BENJAMINI_HOCHBERG else 0.0,
            discovery_proportion=discovery_proportion,
            expected_false_discoveries=discovery_proportion * self.primary_alpha * len(primary_tests),
            power_estimates=power_estimates,
            primary_hypotheses=self.primary_hypotheses,
            secondary_hypotheses=self.secondary_hypotheses,
            exploratory_hypotheses=self.exploratory_hypotheses
        )
        
    def _romano_wolf_correction(self, test_results: List[HypothesisTest]) -> Dict[str, float]:
        """Romano-Wolf stepdown procedure (simplified implementation)."""
        
        # This is a placeholder for Romano-Wolf correction
        # Full implementation requires bootstrap resampling
        
        p_values = [test.p_value for test in test_results]
        hypothesis_ids = [test.hypothesis_id for test in test_results]
        
        # For now, use Holm-Bonferroni as approximation
        _, corrected_p_vals, _, _ = multipletests(
            p_values, alpha=self.primary_alpha, method='holm'
        )
        
        logger.warning("Romano-Wolf correction not fully implemented - using Holm-Bonferroni")
        
        return dict(zip(hypothesis_ids, corrected_p_vals))
        
    def _estimate_test_power(self, 
                           test_results: List[HypothesisTest],
                           corrected_p_values: Dict[str, float]) -> Dict[str, float]:
        """Estimate statistical power for each test."""
        
        power_estimates = {}
        
        for test in test_results:
            try:
                if test.test_type == "t_test":
                    # For t-tests, use effect size and sample size
                    effect_size = abs(test.effect_size / test.standard_error)  # Cohen's d approximation
                    power = ttest_power(effect_size, test.n_observations, self.primary_alpha)
                    
                elif test.test_type == "f_test":
                    # For F-tests, use effect size and sample size
                    effect_size = test.effect_size
                    # Simplified power calculation
                    power = min(0.99, max(0.05, effect_size * np.sqrt(test.n_observations) / 10))
                    
                else:
                    # Default approximation based on observed test statistic
                    # Higher test statistics generally indicate higher power
                    power = min(0.99, max(0.05, abs(test.test_statistic) / 5))
                    
                power_estimates[test.hypothesis_id] = power
                
            except Exception as e:
                logger.warning(f"Could not estimate power for {test.hypothesis_id}: {e}")
                power_estimates[test.hypothesis_id] = 0.5  # Default moderate power
                
        return power_estimates
        
    def conduct_power_analysis(self,
                             effect_sizes: Dict[str, float],
                             sample_sizes: Dict[str, int],
                             analysis_type: PowerAnalysisType = PowerAnalysisType.EX_POST,
                             test_types: Dict[str, str] = None) -> PowerAnalysisResults:
        """
        Conduct comprehensive power analysis for hypothesis tests.
        
        Args:
            effect_sizes: Expected or observed effect sizes for each hypothesis
            sample_sizes: Sample sizes for each hypothesis test
            analysis_type: Type of power analysis to conduct
            test_types: Test types for each hypothesis (t_test, f_test, etc.)
            
        Returns:
            PowerAnalysisResults with power calculations and recommendations
        """
        logger.info(f"Conducting {analysis_type.value} power analysis for {len(effect_sizes)} tests")
        
        test_types = test_types or {}
        
        # Calculate statistical power
        statistical_power = {}
        minimum_detectable_effects = {}
        required_sample_sizes = {}
        
        for hypothesis_id, effect_size in effect_sizes.items():
            sample_size = sample_sizes.get(hypothesis_id, 100)  # Default
            test_type = test_types.get(hypothesis_id, "t_test")  # Default
            
            try:
                if test_type == "t_test":
                    # Power for t-test
                    power = ttest_power(effect_size, sample_size, self.primary_alpha)
                    
                    # MDE for given sample size and power
                    if analysis_type == PowerAnalysisType.MINIMUM_DETECTABLE_EFFECT:
                        # Solve for effect size given power and sample size
                        mde = self._solve_for_mde_ttest(sample_size, self.target_power, self.primary_alpha)
                    else:
                        mde = effect_size
                        
                    # Required sample size for target power
                    required_n = self._solve_for_sample_size_ttest(effect_size, self.target_power, self.primary_alpha)
                    
                elif test_type == "f_test":
                    # Simplified F-test power calculation
                    power = self._calculate_f_test_power(effect_size, sample_size, self.primary_alpha)
                    mde = effect_size  # Simplified
                    required_n = sample_size * 2  # Simplified rule of thumb
                    
                else:
                    # Generic power approximation
                    power = min(0.99, max(0.05, effect_size * np.sqrt(sample_size) / 3))
                    mde = effect_size
                    required_n = int(sample_size * self.target_power / power) if power > 0 else sample_size * 2
                    
                statistical_power[hypothesis_id] = power
                minimum_detectable_effects[hypothesis_id] = mde
                required_sample_sizes[hypothesis_id] = required_n
                
            except Exception as e:
                logger.warning(f"Power analysis failed for {hypothesis_id}: {e}")
                statistical_power[hypothesis_id] = 0.5
                minimum_detectable_effects[hypothesis_id] = effect_size
                required_sample_sizes[hypothesis_id] = sample_size
                
        # Categorize tests by power adequacy
        adequately_powered = [hyp_id for hyp_id, power in statistical_power.items() 
                             if power >= self.target_power]
        underpowered = [hyp_id for hyp_id, power in statistical_power.items() 
                       if power < self.target_power]
        
        # Generate sample size recommendations
        sample_size_recommendations = {}
        for hyp_id in underpowered:
            current_n = sample_sizes.get(hyp_id, 100)
            required_n = required_sample_sizes[hyp_id]
            increase_pct = (required_n / current_n - 1) * 100
            
            if increase_pct > 100:
                sample_size_recommendations[hyp_id] = f"Increase sample size by >{increase_pct:.0f}% (to {required_n:,})"
            elif increase_pct > 50:
                sample_size_recommendations[hyp_id] = f"Increase sample size by {increase_pct:.0f}% (to {required_n:,})"
            else:
                sample_size_recommendations[hyp_id] = f"Minor increase needed to {required_n:,} observations"
                
        # Effect size justifications
        effect_size_justification = {
            hyp_id: self._justify_effect_size(hyp_id, effect_size)
            for hyp_id, effect_size in effect_sizes.items()
        }
        
        return PowerAnalysisResults(
            statistical_power=statistical_power,
            minimum_detectable_effects=minimum_detectable_effects,
            required_sample_sizes=required_sample_sizes,
            analysis_type=analysis_type,
            alpha_level=self.primary_alpha,
            target_power=self.target_power,
            assumed_effect_sizes=effect_sizes,
            effect_size_justification=effect_size_justification,
            adequately_powered_tests=adequately_powered,
            underpowered_tests=underpowered,
            sample_size_recommendations=sample_size_recommendations
        )
        
    def _solve_for_mde_ttest(self, n: int, power: float, alpha: float) -> float:
        """Solve for minimum detectable effect in t-test."""
        
        # Approximate solution using inverse of power function
        # This is a simplified implementation
        
        from scipy.optimize import fsolve
        
        def power_equation(effect_size):
            return ttest_power(effect_size, n, alpha) - power
            
        try:
            mde = fsolve(power_equation, 0.5)[0]
            return abs(mde)
        except:
            # Fallback approximation
            return 2.8 / np.sqrt(n)  # Cohen's rule of thumb
            
    def _solve_for_sample_size_ttest(self, effect_size: float, power: float, alpha: float) -> int:
        """Solve for required sample size in t-test."""
        
        from scipy.optimize import fsolve
        
        def power_equation(n):
            return ttest_power(effect_size, n, alpha) - power
            
        try:
            required_n = fsolve(power_equation, 100)[0]
            return max(10, int(np.ceil(required_n)))
        except:
            # Fallback approximation
            return max(10, int(np.ceil(16 / (effect_size**2))))  # Rough approximation
            
    def _calculate_f_test_power(self, effect_size: float, n: int, alpha: float) -> float:
        """Calculate power for F-test (simplified)."""
        
        try:
            # Use statsmodels if available, otherwise approximate
            power = ftest_power(effect_size, n - 2, 1, alpha)
            return min(0.99, max(0.05, power))
        except:
            # Fallback approximation
            return min(0.99, max(0.05, effect_size * np.sqrt(n) / 5))
            
    def _justify_effect_size(self, hypothesis_id: str, effect_size: float) -> str:
        """Provide justification for assumed effect size."""
        
        if abs(effect_size) < 0.2:
            return "Small effect size based on conflict economics literature"
        elif abs(effect_size) < 0.5:
            return "Medium effect size - economically meaningful"
        elif abs(effect_size) < 0.8:
            return "Large effect size - substantial economic impact"
        else:
            return "Very large effect size - may warrant verification"
            
    def generate_testing_report(self,
                              multiple_testing_results: MultipleTestingResults,
                              power_analysis_results: PowerAnalysisResults) -> str:
        """
        Generate comprehensive testing report.
        
        Args:
            multiple_testing_results: Results from multiple testing correction
            power_analysis_results: Results from power analysis
            
        Returns:
            Formatted report string
        """
        
        report = []
        report.append("# COMPREHENSIVE HYPOTHESIS TESTING REPORT")
        report.append("\n## Multiple Testing Correction")
        
        # Multiple testing summary
        report.append(f"Method: {multiple_testing_results.correction_method.value}")
        report.append(f"Total tests: {len(multiple_testing_results.original_tests)}")
        report.append(f"Significant results: {len(multiple_testing_results.significant_hypotheses)}")
        report.append(f"Discovery rate: {multiple_testing_results.discovery_proportion:.1%}")
        
        if multiple_testing_results.false_discovery_rate > 0:
            report.append(f"False discovery rate: {multiple_testing_results.false_discovery_rate:.3f}")
            report.append(f"Expected false discoveries: {multiple_testing_results.expected_false_discoveries:.1f}")
            
        # Significant hypotheses
        if multiple_testing_results.significant_hypotheses:
            report.append("\n### Significant Hypotheses")
            for hyp_id in multiple_testing_results.significant_hypotheses:
                original_p = next(t.p_value for t in multiple_testing_results.original_tests if t.hypothesis_id == hyp_id)
                corrected_p = multiple_testing_results.corrected_p_values[hyp_id]
                report.append(f"- {hyp_id}: p = {original_p:.4f} → {corrected_p:.4f}")
                
        # Power analysis summary
        report.append("\n## Power Analysis")
        report.append(f"Target power: {power_analysis_results.target_power:.0%}")
        report.append(f"Adequately powered tests: {len(power_analysis_results.adequately_powered_tests)}")
        report.append(f"Underpowered tests: {len(power_analysis_results.underpowered_tests)}")
        
        # Power details
        if power_analysis_results.underpowered_tests:
            report.append("\n### Underpowered Tests")
            for hyp_id in power_analysis_results.underpowered_tests:
                power = power_analysis_results.statistical_power[hyp_id]
                recommendation = power_analysis_results.sample_size_recommendations.get(hyp_id, "Review design")
                report.append(f"- {hyp_id}: Power = {power:.0%}, {recommendation}")
                
        # Recommendations
        report.append("\n## Recommendations")
        
        if multiple_testing_results.significant_hypotheses:
            report.append("✓ Found significant results after multiple testing correction")
        else:
            report.append("⚠ No significant results after multiple testing correction")
            
        if len(power_analysis_results.adequately_powered_tests) >= len(power_analysis_results.underpowered_tests):
            report.append("✓ Most tests are adequately powered")
        else:
            report.append("⚠ Many tests are underpowered - consider increasing sample size")
            
        return "\n".join(report)
        
    def create_summary_table(self,
                           multiple_testing_results: MultipleTestingResults,
                           power_analysis_results: PowerAnalysisResults) -> pd.DataFrame:
        """
        Create summary table of all hypothesis test results.
        
        Returns:
            DataFrame with comprehensive test results
        """
        
        summary_data = []
        
        for test in multiple_testing_results.original_tests:
            hyp_id = test.hypothesis_id
            
            row = {
                'Hypothesis': hyp_id,
                'Test_Statistic': test.test_statistic,
                'Original_P_Value': test.p_value,
                'Corrected_P_Value': multiple_testing_results.corrected_p_values.get(hyp_id, test.p_value),
                'Significant': hyp_id in multiple_testing_results.significant_hypotheses,
                'Effect_Size': test.effect_size,
                'Statistical_Power': power_analysis_results.statistical_power.get(hyp_id, np.nan),
                'Adequately_Powered': hyp_id in power_analysis_results.adequately_powered_tests,
                'Required_Sample_Size': power_analysis_results.required_sample_sizes.get(hyp_id, np.nan),
                'Hierarchy': ('Primary' if hyp_id in multiple_testing_results.primary_hypotheses 
                            else 'Secondary' if hyp_id in multiple_testing_results.secondary_hypotheses
                            else 'Exploratory')
            }
            
            summary_data.append(row)
            
        return pd.DataFrame(summary_data)