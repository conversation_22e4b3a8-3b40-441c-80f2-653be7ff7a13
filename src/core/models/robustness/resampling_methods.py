"""
Resampling Methods for Robustness Testing

Implements bootstrap and permutation tests for robust inference
in the Yemen market integration analysis.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Callable, Any
from dataclasses import dataclass
import warnings
from scipy import stats
from joblib import Parallel, delayed
from tqdm import tqdm

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class BootstrapResult:
    """Results from bootstrap procedure."""
    point_estimate: float
    bootstrap_se: float
    percentile_ci: Tuple[float, float]
    bca_ci: Optional[Tuple[float, float]] = None
    bootstrap_distribution: Optional[np.ndarray] = None
    n_bootstrap: int = 0
    convergence_rate: float = 1.0


@dataclass
class PermutationResult:
    """Results from permutation test."""
    observed_statistic: float
    p_value: float
    permutation_distribution: np.ndarray
    n_permutations: int
    one_sided_p_lower: float
    one_sided_p_upper: float


class ResamplingRobustness:
    """
    Implement bootstrap and permutation tests for robust inference.
    
    This class provides various resampling methods suitable for:
    - Panel data with clustering
    - Time series dependence
    - Spatial correlation
    - Small sample inference
    """
    
    def __init__(self, random_seed: int = 42):
        """Initialize resampling methods with random seed for reproducibility."""
        self.random_seed = random_seed
        np.random.seed(random_seed)
        
    def cluster_bootstrap(self, 
                         data: pd.DataFrame,
                         model: Callable,
                         cluster_var: str = 'market_id',
                         n_bootstrap: int = 1000,
                         confidence_level: float = 0.95,
                         return_distribution: bool = False,
                         parallel: bool = True,
                         n_jobs: int = -1) -> BootstrapResult:
        """
        Cluster bootstrap for panel data inference.
        
        Args:
            data: Panel data
            model: Function that takes data and returns coefficient estimate
            cluster_var: Variable defining clusters (e.g., market_id)
            n_bootstrap: Number of bootstrap replications
            confidence_level: Confidence level for intervals
            return_distribution: Whether to return full bootstrap distribution
            parallel: Use parallel processing
            n_jobs: Number of parallel jobs (-1 for all cores)
            
        Returns:
            BootstrapResult with robust standard errors and confidence intervals
        """
        # Get unique clusters
        clusters = data[cluster_var].unique()
        n_clusters = len(clusters)
        
        # Run original model
        try:
            original_estimate = model(data)
            if isinstance(original_estimate, dict):
                original_estimate = original_estimate.get('coefficient', original_estimate.get('estimate'))
        except Exception as e:
            logger.error(f"Original model failed: {e}")
            return BootstrapResult(
                point_estimate=np.nan,
                bootstrap_se=np.nan,
                percentile_ci=(np.nan, np.nan),
                n_bootstrap=0,
                convergence_rate=0.0
            )
            
        # Bootstrap function
        def single_bootstrap(seed):
            np.random.seed(seed)
            # Resample clusters with replacement
            sampled_clusters = np.random.choice(clusters, size=n_clusters, replace=True)
            
            # Build bootstrap sample
            bootstrap_data = []
            for cluster in sampled_clusters:
                cluster_data = data[data[cluster_var] == cluster].copy()
                bootstrap_data.append(cluster_data)
                
            bootstrap_data = pd.concat(bootstrap_data, ignore_index=True)
            
            # Run model on bootstrap sample
            try:
                result = model(bootstrap_data)
                if isinstance(result, dict):
                    return result.get('coefficient', result.get('estimate'))
                return result
            except:
                return np.nan
                
        # Run bootstrap
        if parallel:
            seeds = [self.random_seed + i for i in range(n_bootstrap)]
            bootstrap_estimates = Parallel(n_jobs=n_jobs)(
                delayed(single_bootstrap)(seed) 
                for seed in tqdm(seeds, desc="Bootstrap iterations")
            )
        else:
            bootstrap_estimates = []
            for i in tqdm(range(n_bootstrap), desc="Bootstrap iterations"):
                estimate = single_bootstrap(self.random_seed + i)
                bootstrap_estimates.append(estimate)
                
        # Remove failed iterations
        bootstrap_estimates = [e for e in bootstrap_estimates if not np.isnan(e)]
        convergence_rate = len(bootstrap_estimates) / n_bootstrap
        
        if len(bootstrap_estimates) < 100:
            logger.warning(f"Only {len(bootstrap_estimates)} bootstrap iterations converged")
            
        bootstrap_estimates = np.array(bootstrap_estimates)
        
        # Calculate statistics
        bootstrap_se = np.std(bootstrap_estimates, ddof=1)
        
        # Percentile confidence interval
        alpha = 1 - confidence_level
        percentile_ci = (
            np.percentile(bootstrap_estimates, 100 * alpha / 2),
            np.percentile(bootstrap_estimates, 100 * (1 - alpha / 2))
        )
        
        # BCa confidence interval (if requested)
        bca_ci = None
        if len(bootstrap_estimates) > 500:  # Need sufficient samples for BCa
            try:
                bca_ci = self._calculate_bca_ci(
                    original_estimate, 
                    bootstrap_estimates,
                    data, 
                    model, 
                    cluster_var,
                    confidence_level
                )
            except Exception as e:
                logger.debug(f"BCa CI calculation failed: {e}")
                
        return BootstrapResult(
            point_estimate=original_estimate,
            bootstrap_se=bootstrap_se,
            percentile_ci=percentile_ci,
            bca_ci=bca_ci,
            bootstrap_distribution=bootstrap_estimates if return_distribution else None,
            n_bootstrap=len(bootstrap_estimates),
            convergence_rate=convergence_rate
        )
        
    def _calculate_bca_ci(self, 
                         theta_hat: float,
                         bootstrap_distribution: np.ndarray,
                         data: pd.DataFrame,
                         model: Callable,
                         cluster_var: str,
                         confidence_level: float) -> Tuple[float, float]:
        """Calculate BCa (bias-corrected and accelerated) confidence interval."""
        # Calculate bias correction factor
        z0 = stats.norm.ppf((bootstrap_distribution < theta_hat).mean())
        
        # Calculate acceleration factor using jackknife
        clusters = data[cluster_var].unique()
        jackknife_estimates = []
        
        for cluster in clusters:
            # Leave one cluster out
            jack_data = data[data[cluster_var] != cluster]
            try:
                estimate = model(jack_data)
                if isinstance(estimate, dict):
                    estimate = estimate.get('coefficient', estimate.get('estimate'))
                jackknife_estimates.append(estimate)
            except:
                pass
                
        if len(jackknife_estimates) < len(clusters) * 0.9:
            # Fall back to percentile method if jackknife fails
            raise ValueError("Insufficient jackknife estimates for BCa")
            
        jackknife_estimates = np.array(jackknife_estimates)
        jack_mean = np.mean(jackknife_estimates)
        
        # Acceleration factor
        numerator = np.sum((jack_mean - jackknife_estimates) ** 3)
        denominator = 6 * (np.sum((jack_mean - jackknife_estimates) ** 2) ** 1.5)
        a = numerator / denominator if denominator != 0 else 0
        
        # Adjusted percentiles
        alpha = 1 - confidence_level
        z_alpha = stats.norm.ppf(alpha / 2)
        z_1alpha = stats.norm.ppf(1 - alpha / 2)
        
        p_lower = stats.norm.cdf(z0 + (z0 + z_alpha) / (1 - a * (z0 + z_alpha)))
        p_upper = stats.norm.cdf(z0 + (z0 + z_1alpha) / (1 - a * (z0 + z_1alpha)))
        
        # BCa confidence interval
        ci_lower = np.percentile(bootstrap_distribution, 100 * p_lower)
        ci_upper = np.percentile(bootstrap_distribution, 100 * p_upper)
        
        return (ci_lower, ci_upper)
        
    def block_bootstrap(self,
                       data: pd.DataFrame,
                       model: Callable,
                       time_var: str = 'date',
                       entity_var: str = 'market_id',
                       block_length: Optional[int] = None,
                       n_bootstrap: int = 1000,
                       method: str = 'moving_block') -> BootstrapResult:
        """
        Block bootstrap for time series panel data.
        
        Args:
            data: Panel data with time dimension
            model: Estimation function
            time_var: Time variable name
            entity_var: Entity variable name
            block_length: Length of blocks (auto-determined if None)
            n_bootstrap: Number of bootstrap samples
            method: 'moving_block' or 'stationary_block'
            
        Returns:
            BootstrapResult
        """
        # Sort data
        data = data.sort_values([entity_var, time_var])
        
        # Determine optimal block length if not provided
        if block_length is None:
            block_length = self._optimal_block_length(data, entity_var, time_var)
            logger.info(f"Using block length: {block_length}")
            
        # Get time periods
        time_periods = sorted(data[time_var].unique())
        n_periods = len(time_periods)
        
        # Run original model
        original_estimate = model(data)
        if isinstance(original_estimate, dict):
            original_estimate = original_estimate.get('coefficient', original_estimate.get('estimate'))
            
        bootstrap_estimates = []
        
        for b in range(n_bootstrap):
            if method == 'moving_block':
                # Moving block bootstrap
                bootstrap_periods = []
                while len(bootstrap_periods) < n_periods:
                    # Random starting point
                    start_idx = np.random.randint(0, n_periods - block_length + 1)
                    block = time_periods[start_idx:start_idx + block_length]
                    bootstrap_periods.extend(block)
                    
                # Trim to exact length
                bootstrap_periods = bootstrap_periods[:n_periods]
                
            else:  # stationary_block
                # Stationary bootstrap with random block lengths
                bootstrap_periods = []
                i = 0
                while len(bootstrap_periods) < n_periods:
                    # Geometric distribution for block length
                    current_block_length = min(
                        np.random.geometric(1/block_length),
                        n_periods - i
                    )
                    start_idx = np.random.randint(0, n_periods)
                    for j in range(current_block_length):
                        bootstrap_periods.append(time_periods[(start_idx + j) % n_periods])
                        
                bootstrap_periods = bootstrap_periods[:n_periods]
                
            # Create bootstrap sample
            bootstrap_data = []
            for i, period in enumerate(bootstrap_periods):
                period_data = data[data[time_var] == period].copy()
                # Assign new time to maintain panel structure
                period_data[time_var] = time_periods[i]
                bootstrap_data.append(period_data)
                
            bootstrap_data = pd.concat(bootstrap_data, ignore_index=True)
            
            # Run model
            try:
                result = model(bootstrap_data)
                if isinstance(result, dict):
                    result = result.get('coefficient', result.get('estimate'))
                bootstrap_estimates.append(result)
            except:
                pass
                
        bootstrap_estimates = np.array([e for e in bootstrap_estimates if not np.isnan(e)])
        
        # Calculate statistics
        bootstrap_se = np.std(bootstrap_estimates, ddof=1)
        percentile_ci = (
            np.percentile(bootstrap_estimates, 2.5),
            np.percentile(bootstrap_estimates, 97.5)
        )
        
        return BootstrapResult(
            point_estimate=original_estimate,
            bootstrap_se=bootstrap_se,
            percentile_ci=percentile_ci,
            n_bootstrap=len(bootstrap_estimates),
            convergence_rate=len(bootstrap_estimates) / n_bootstrap
        )
        
    def _optimal_block_length(self, data: pd.DataFrame, 
                            entity_var: str, time_var: str) -> int:
        """Determine optimal block length using Politis & White (2004) method."""
        # Simplified version - could be enhanced
        n_periods = data[time_var].nunique()
        
        # Rule of thumb: n^(1/3) for moving block bootstrap
        block_length = int(np.ceil(n_periods ** (1/3)))
        
        # Ensure reasonable bounds
        block_length = max(2, min(block_length, n_periods // 4))
        
        return block_length
        
    def wild_cluster_bootstrap(self,
                             data: pd.DataFrame,
                             model: Callable,
                             cluster_var: str = 'market_id',
                             n_bootstrap: int = 999,
                             impose_null: bool = True,
                             weights: str = 'rademacher') -> BootstrapResult:
        """
        Wild cluster bootstrap for few clusters.
        
        Particularly useful when number of clusters < 30.
        Based on Cameron, Gelbach & Miller (2008).
        
        Args:
            data: Panel data
            model: Model estimation function
            cluster_var: Clustering variable
            n_bootstrap: Number of bootstrap iterations (999 recommended)
            impose_null: Whether to impose null hypothesis
            weights: 'rademacher' or 'webb' weights
            
        Returns:
            BootstrapResult
        """
        clusters = data[cluster_var].unique()
        n_clusters = len(clusters)
        
        if n_clusters >= 30:
            logger.warning(f"Wild bootstrap less necessary with {n_clusters} clusters")
            
        # Run original model
        original_result = model(data)
        if isinstance(original_result, dict):
            original_estimate = original_result.get('coefficient')
            original_residuals = original_result.get('residuals')
        else:
            raise ValueError("Model must return dict with coefficient and residuals")
            
        # Generate weight matrix
        if weights == 'rademacher':
            # Rademacher weights: +1 or -1 with equal probability
            weight_matrix = np.random.choice([-1, 1], size=(n_bootstrap, n_clusters))
        elif weights == 'webb':
            # Webb 6-point weights
            webb_weights = [-np.sqrt(3), -np.sqrt(2), -1, 1, np.sqrt(2), np.sqrt(3)]
            weight_matrix = np.random.choice(webb_weights, size=(n_bootstrap, n_clusters))
        else:
            raise ValueError(f"Unknown weight type: {weights}")
            
        bootstrap_estimates = []
        
        for b in range(n_bootstrap):
            # Apply weights to residuals by cluster
            bootstrap_data = data.copy()
            
            if impose_null:
                # Impose null hypothesis by using residuals from restricted model
                for i, cluster in enumerate(clusters):
                    mask = bootstrap_data[cluster_var] == cluster
                    bootstrap_data.loc[mask, 'bootstrap_weight'] = weight_matrix[b, i]
            else:
                # Unrestricted wild bootstrap
                for i, cluster in enumerate(clusters):
                    mask = bootstrap_data[cluster_var] == cluster
                    bootstrap_data.loc[mask, 'y'] *= weight_matrix[b, i]
                    
            # Run model on bootstrap sample
            try:
                result = model(bootstrap_data)
                if isinstance(result, dict):
                    result = result.get('coefficient')
                bootstrap_estimates.append(result)
            except:
                pass
                
        bootstrap_estimates = np.array(bootstrap_estimates)
        
        # Calculate p-value for hypothesis test
        if impose_null:
            p_value = np.mean(np.abs(bootstrap_estimates) >= np.abs(original_estimate))
        else:
            p_value = None
            
        # Standard error and CI
        bootstrap_se = np.std(bootstrap_estimates, ddof=1)
        percentile_ci = (
            np.percentile(bootstrap_estimates, 2.5),
            np.percentile(bootstrap_estimates, 97.5)
        )
        
        return BootstrapResult(
            point_estimate=original_estimate,
            bootstrap_se=bootstrap_se,
            percentile_ci=percentile_ci,
            n_bootstrap=len(bootstrap_estimates),
            convergence_rate=len(bootstrap_estimates) / n_bootstrap
        )
        
    def randomization_inference(self,
                              data: pd.DataFrame,
                              treatment_var: str,
                              outcome_var: str,
                              cluster_var: Optional[str] = None,
                              n_permutations: int = 1000,
                              test_statistic: str = 'mean_diff') -> PermutationResult:
        """
        Fisher randomization test for sharp null hypothesis.
        
        Tests the sharp null of no effect for any unit.
        
        Args:
            data: Dataset
            treatment_var: Treatment variable name
            outcome_var: Outcome variable name 
            cluster_var: Optional clustering variable
            n_permutations: Number of permutations
            test_statistic: 'mean_diff' or 'regression_coef'
            
        Returns:
            PermutationResult
        """
        # Calculate observed test statistic
        if test_statistic == 'mean_diff':
            treated = data[data[treatment_var] == 1][outcome_var]
            control = data[data[treatment_var] == 0][outcome_var]
            observed_stat = treated.mean() - control.mean()
        elif test_statistic == 'regression_coef':
            # Simple regression
            import statsmodels.api as sm
            X = sm.add_constant(data[treatment_var])
            model = sm.OLS(data[outcome_var], X).fit()
            observed_stat = model.params[treatment_var]
        else:
            raise ValueError(f"Unknown test statistic: {test_statistic}")
            
        # Permutation test
        permuted_stats = []
        
        for p in range(n_permutations):
            # Permute treatment assignment
            if cluster_var:
                # Cluster-level permutation
                clusters = data[cluster_var].unique()
                cluster_treatment = data.groupby(cluster_var)[treatment_var].first()
                permuted_cluster_treatment = np.random.permutation(cluster_treatment)
                
                permuted_data = data.copy()
                for i, cluster in enumerate(clusters):
                    mask = permuted_data[cluster_var] == cluster
                    permuted_data.loc[mask, treatment_var] = permuted_cluster_treatment[i]
            else:
                # Unit-level permutation
                permuted_data = data.copy()
                permuted_data[treatment_var] = np.random.permutation(data[treatment_var])
                
            # Calculate test statistic under permutation
            if test_statistic == 'mean_diff':
                treated = permuted_data[permuted_data[treatment_var] == 1][outcome_var]
                control = permuted_data[permuted_data[treatment_var] == 0][outcome_var]
                perm_stat = treated.mean() - control.mean()
            else:
                X = sm.add_constant(permuted_data[treatment_var])
                try:
                    model = sm.OLS(permuted_data[outcome_var], X).fit()
                    perm_stat = model.params[treatment_var]
                except:
                    perm_stat = np.nan
                    
            permuted_stats.append(perm_stat)
            
        permuted_stats = np.array([s for s in permuted_stats if not np.isnan(s)])
        
        # Calculate p-values
        p_value_two_sided = np.mean(np.abs(permuted_stats) >= np.abs(observed_stat))
        p_value_upper = np.mean(permuted_stats >= observed_stat)
        p_value_lower = np.mean(permuted_stats <= observed_stat)
        
        return PermutationResult(
            observed_statistic=observed_stat,
            p_value=p_value_two_sided,
            permutation_distribution=permuted_stats,
            n_permutations=len(permuted_stats),
            one_sided_p_lower=p_value_lower,
            one_sided_p_upper=p_value_upper
        )
        
    def spatial_bootstrap(self,
                         data: pd.DataFrame,
                         model: Callable,
                         x_coord: str = 'longitude',
                         y_coord: str = 'latitude', 
                         n_bootstrap: int = 1000,
                         block_size: Optional[float] = None) -> BootstrapResult:
        """
        Spatial block bootstrap for geographically correlated data.
        
        Args:
            data: Data with spatial coordinates
            model: Estimation function
            x_coord: X coordinate column name
            y_coord: Y coordinate column name
            n_bootstrap: Number of bootstrap samples
            block_size: Spatial block size (auto-determined if None)
            
        Returns:
            BootstrapResult
        """
        if block_size is None:
            # Determine block size based on spatial distribution
            x_range = data[x_coord].max() - data[x_coord].min()
            y_range = data[y_coord].max() - data[y_coord].min()
            block_size = min(x_range, y_range) / 10  # Default to 10 blocks per dimension
            
        # Run original model
        original_estimate = model(data)
        if isinstance(original_estimate, dict):
            original_estimate = original_estimate.get('coefficient')
            
        bootstrap_estimates = []
        
        for b in range(n_bootstrap):
            # Generate random block centers
            n_blocks = int(np.ceil(len(data) / 50))  # Average 50 obs per block
            block_centers_x = np.random.uniform(
                data[x_coord].min(), 
                data[x_coord].max(), 
                n_blocks
            )
            block_centers_y = np.random.uniform(
                data[y_coord].min(),
                data[y_coord].max(),
                n_blocks
            )
            
            # Assign observations to nearest block
            bootstrap_indices = []
            for i in range(n_blocks):
                # Find observations within block
                distances = np.sqrt(
                    (data[x_coord] - block_centers_x[i])**2 + 
                    (data[y_coord] - block_centers_y[i])**2
                )
                block_obs = data.index[distances <= block_size].tolist()
                
                if block_obs:
                    # Resample block with replacement
                    resampled = np.random.choice(block_obs, size=len(block_obs), replace=True)
                    bootstrap_indices.extend(resampled)
                    
            # Create bootstrap sample
            bootstrap_data = data.iloc[bootstrap_indices]
            
            # Run model
            try:
                result = model(bootstrap_data)
                if isinstance(result, dict):
                    result = result.get('coefficient')
                bootstrap_estimates.append(result)
            except:
                pass
                
        bootstrap_estimates = np.array([e for e in bootstrap_estimates if not np.isnan(e)])
        
        # Calculate statistics
        bootstrap_se = np.std(bootstrap_estimates, ddof=1)
        percentile_ci = (
            np.percentile(bootstrap_estimates, 2.5),
            np.percentile(bootstrap_estimates, 97.5)
        )
        
        return BootstrapResult(
            point_estimate=original_estimate,
            bootstrap_se=bootstrap_se,
            percentile_ci=percentile_ci,
            n_bootstrap=len(bootstrap_estimates),
            convergence_rate=len(bootstrap_estimates) / n_bootstrap
        )
        
    def subsampling(self,
                   data: pd.DataFrame,
                   model: Callable,
                   subsample_size: Optional[int] = None,
                   n_subsamples: int = 1000) -> Dict[str, Any]:
        """
        Subsampling for inference when bootstrap fails.
        
        Useful for non-standard asymptotics or extreme value statistics.
        
        Args:
            data: Dataset
            model: Estimation function
            subsample_size: Size of subsamples (auto if None)
            n_subsamples: Number of subsamples to draw
            
        Returns:
            Dictionary with subsampling results
        """
        n = len(data)
        
        if subsample_size is None:
            # Politis, Romano & Wolf (1999) suggestion
            subsample_size = int(n ** (2/3))
            
        # Run full sample model
        full_estimate = model(data)
        if isinstance(full_estimate, dict):
            full_estimate = full_estimate.get('coefficient')
            
        # Subsampling
        subsample_estimates = []
        
        for s in range(n_subsamples):
            # Draw subsample without replacement
            indices = np.random.choice(n, size=subsample_size, replace=False)
            subsample_data = data.iloc[indices]
            
            try:
                result = model(subsample_data)
                if isinstance(result, dict):
                    result = result.get('coefficient')
                subsample_estimates.append(result)
            except:
                pass
                
        subsample_estimates = np.array([e for e in subsample_estimates if not np.isnan(e)])
        
        # Subsampling distribution centered at full sample estimate
        centered_distribution = np.sqrt(subsample_size) * (subsample_estimates - full_estimate)
        
        # Confidence interval
        alpha = 0.05
        critical_values = (
            np.percentile(centered_distribution, 100 * alpha / 2),
            np.percentile(centered_distribution, 100 * (1 - alpha / 2))
        )
        
        ci_lower = full_estimate - critical_values[1] / np.sqrt(n)
        ci_upper = full_estimate - critical_values[0] / np.sqrt(n)
        
        return {
            'estimate': full_estimate,
            'ci_lower': ci_lower,
            'ci_upper': ci_upper,
            'subsample_size': subsample_size,
            'n_subsamples': len(subsample_estimates),
            'subsample_se': np.std(subsample_estimates)
        }