"""
Enhanced Specification Curve Analysis for Yemen Market Integration

Implements <PERSON>, Simmons & Nelson (2020) specification curve analysis
with extensions for panel data and conflict settings.
"""

import numpy as np
import pandas as pd
from itertools import product
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, field
import seaborn as sns
from scipy import stats
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class AnalyticalChoice:
    """Represents a single analytical choice dimension."""
    name: str
    options: List[Any]
    description: str
    default: Any = None
    
    
@dataclass 
class SpecificationResult:
    """Results from a single specification."""
    spec_id: int
    specification: Dict[str, Any]
    coefficient: float
    se: float
    p_value: float
    ci_lower: float
    ci_upper: float
    n_obs: int
    r_squared: Optional[float] = None
    converged: bool = True
    error: Optional[str] = None
    additional_stats: Dict[str, Any] = field(default_factory=dict)


class SpecificationCurveAnalysis:
    """
    Comprehensive specification curve analysis for robustness testing.
    
    This implementation extends the basic specification curve with:
    - Panel data specific choices
    - Conflict-related specifications  
    - Currency zone handling
    - Parallel processing for large specification spaces
    - Advanced visualization options
    """
    
    def __init__(self, base_model: Callable, model_name: str = "Main Model"):
        """
        Initialize specification curve analysis.
        
        Args:
            base_model: Function that takes (data, **kwargs) and returns results
            model_name: Name for identification in outputs
        """
        self.base_model = base_model
        self.model_name = model_name
        self.specifications: List[Dict[str, Any]] = []
        self.results: List[SpecificationResult] = []
        self.analytical_choices: List[AnalyticalChoice] = []
        
    def define_analytical_choices(self) -> None:
        """Define all reasonable analytical choices for Yemen analysis."""
        
        # Sample composition
        self.add_choice(
            name='sample',
            options=[
                'full_sample',
                'balanced_panel', 
                'exclude_capitals',
                'exclude_contested',
                'post_2020_only',
                'pre_fragmentation',
                'stable_markets_only'
            ],
            description='Sample selection criteria',
            default='full_sample'
        )
        
        # Missing data handling
        self.add_choice(
            name='missing_data',
            options=[
                'complete_case',
                'forward_fill',
                'interpolation',
                'multiple_imputation',
                'selection_model'
            ],
            description='Missing data treatment',
            default='complete_case'
        )
        
        # Outlier treatment
        self.add_choice(
            name='outliers',
            options=[
                'no_treatment',
                'winsorize_1pct',
                'winsorize_2.5pct',
                'winsorize_5pct',
                'trim_1pct',
                'robust_regression'
            ],
            description='Outlier handling method',
            default='winsorize_1pct'
        )
        
        # Fixed effects structure
        self.add_choice(
            name='fixed_effects',
            options=[
                'market',
                'market_commodity',
                'market_time',
                'market_commodity_time',
                'governorate',
                'governorate_time'
            ],
            description='Fixed effects specification',
            default='market_commodity'
        )
        
        # Time trends
        self.add_choice(
            name='time_trends', 
            options=[
                'none',
                'linear',
                'quadratic',
                'market_specific_linear',
                'commodity_specific_linear'
            ],
            description='Time trend specification',
            default='linear'
        )
        
        # Clustering
        self.add_choice(
            name='clustering',
            options=[
                'market',
                'governorate',
                'market_time',
                'two_way_market_time',
                'commodity'
            ],
            description='Standard error clustering',
            default='market'
        )
        
        # Control variables
        self.add_choice(
            name='controls',
            options=[
                'minimal',
                'standard',
                'extended',
                'kitchen_sink'
            ],
            description='Control variable set',
            default='standard'
        )
        
        # Exchange rate specification
        self.add_choice(
            name='exchange_rate',
            options=[
                'official_cbya',
                'official_cbys', 
                'parallel_market',
                'weighted_average',
                'zone_specific'
            ],
            description='Exchange rate source',
            default='zone_specific'
        )
        
        # Price transformation
        self.add_choice(
            name='price_transformation',
            options=[
                'levels',
                'logs',
                'first_differences',
                'growth_rates',
                'real_prices'
            ],
            description='Price variable transformation',
            default='logs'
        )
        
        # Conflict measurement
        self.add_choice(
            name='conflict_measure',
            options=[
                'binary_any_event',
                'event_count',
                'fatalities',
                'spatial_lag',
                'cumulative_intensity'
            ],
            description='Conflict variable construction',
            default='event_count'
        )
        
    def add_choice(self, name: str, options: List[Any], 
                   description: str, default: Any = None) -> None:
        """Add an analytical choice dimension."""
        self.analytical_choices.append(
            AnalyticalChoice(
                name=name,
                options=options,
                description=description,
                default=default
            )
        )
        
    def generate_all_specifications(self) -> None:
        """Generate all combinations of analytical choices."""
        if not self.analytical_choices:
            self.define_analytical_choices()
            
        # Get all option lists
        option_lists = [choice.options for choice in self.analytical_choices]
        choice_names = [choice.name for choice in self.analytical_choices]
        
        # Generate all combinations
        self.specifications = []
        for i, combination in enumerate(product(*option_lists)):
            spec = {'spec_id': i}
            spec.update(dict(zip(choice_names, combination)))
            self.specifications.append(spec)
            
        logger.info(f"Generated {len(self.specifications)} unique specifications")
        
    def run_all_specifications(self, data: pd.DataFrame, 
                             parallel: bool = True,
                             max_workers: Optional[int] = None,
                             progress_bar: bool = True) -> None:
        """
        Run all specifications with parallel processing support.
        
        Args:
            data: Input data
            parallel: Whether to use parallel processing
            max_workers: Number of parallel workers
            progress_bar: Show progress bar
        """
        if not self.specifications:
            self.generate_all_specifications()
            
        self.results = []
        failed_specs = []
        
        if parallel and len(self.specifications) > 10:
            # Parallel execution
            with ProcessPoolExecutor(max_workers=max_workers) as executor:
                # Submit all jobs
                future_to_spec = {
                    executor.submit(self._run_single_specification, data, spec): spec
                    for spec in self.specifications
                }
                
                # Process completed jobs
                iterator = as_completed(future_to_spec)
                if progress_bar:
                    iterator = tqdm(iterator, total=len(self.specifications))
                    
                for future in iterator:
                    spec = future_to_spec[future]
                    try:
                        result = future.result()
                        if result and result.converged:
                            self.results.append(result)
                        else:
                            failed_specs.append(spec)
                    except Exception as e:
                        logger.debug(f"Specification {spec['spec_id']} failed: {e}")
                        failed_specs.append(spec)
        else:
            # Sequential execution
            iterator = self.specifications
            if progress_bar:
                iterator = tqdm(iterator)
                
            for spec in iterator:
                result = self._run_single_specification(data, spec)
                if result and result.converged:
                    self.results.append(result)
                else:
                    failed_specs.append(spec)
                    
        logger.info(f"Successfully completed {len(self.results)} specifications")
        if failed_specs:
            logger.warning(f"{len(failed_specs)} specifications failed to converge")
            
    def _run_single_specification(self, data: pd.DataFrame,
                                specification: Dict[str, Any]) -> Optional[SpecificationResult]:
        """Run a single specification and return results."""
        try:
            # Prepare data according to specification
            prepared_data = self._prepare_data(data.copy(), specification)
            
            # Run model
            model_kwargs = self._get_model_kwargs(specification)
            result = self.base_model(prepared_data, **model_kwargs)
            
            # Extract results
            return SpecificationResult(
                spec_id=specification['spec_id'],
                specification=specification,
                coefficient=result.get('coefficient', np.nan),
                se=result.get('se', np.nan),
                p_value=result.get('p_value', np.nan),
                ci_lower=result.get('ci_lower', result.get('coefficient', 0) - 1.96 * result.get('se', 0)),
                ci_upper=result.get('ci_upper', result.get('coefficient', 0) + 1.96 * result.get('se', 0)),
                n_obs=result.get('n_obs', len(prepared_data)),
                r_squared=result.get('r_squared'),
                converged=result.get('converged', True),
                additional_stats=result.get('additional_stats', {})
            )
        except Exception as e:
            return SpecificationResult(
                spec_id=specification['spec_id'],
                specification=specification,
                coefficient=np.nan,
                se=np.nan,
                p_value=np.nan,
                ci_lower=np.nan,
                ci_upper=np.nan,
                n_obs=0,
                converged=False,
                error=str(e)
            )
            
    def _prepare_data(self, data: pd.DataFrame, 
                     specification: Dict[str, Any]) -> pd.DataFrame:
        """Prepare data according to specification choices."""
        
        # Sample selection
        if specification.get('sample') == 'balanced_panel':
            # Keep only entities with complete time series
            entity_counts = data.groupby('market_id').size()
            complete_entities = entity_counts[entity_counts == entity_counts.max()].index
            data = data[data['market_id'].isin(complete_entities)]
        elif specification.get('sample') == 'exclude_capitals':
            data = data[~data['market_name'].isin(['Sana\'a', 'Aden'])]
        elif specification.get('sample') == 'exclude_contested':
            data = data[data['control_stability'] == 'stable']
        elif specification.get('sample') == 'post_2020_only':
            data = data[data['date'] >= '2021-01-01']
        elif specification.get('sample') == 'pre_fragmentation':
            data = data[data['date'] < '2021-01-01']
            
        # Outlier treatment
        if 'winsorize' in specification.get('outliers', ''):
            pct = float(specification['outliers'].split('_')[1].replace('pct', ''))
            numeric_cols = data.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if col not in ['market_id', 'commodity_id', 'year', 'month']:
                    lower = data[col].quantile(pct/100)
                    upper = data[col].quantile(1 - pct/100)
                    data[col] = data[col].clip(lower=lower, upper=upper)
                    
        # Price transformation
        if specification.get('price_transformation') == 'logs':
            data['price'] = np.log(data['price'] + 1)
        elif specification.get('price_transformation') == 'first_differences':
            data = data.sort_values(['market_id', 'commodity_id', 'date'])
            data['price'] = data.groupby(['market_id', 'commodity_id'])['price'].diff()
            data = data.dropna(subset=['price'])
            
        return data
        
    def _get_model_kwargs(self, specification: Dict[str, Any]) -> Dict[str, Any]:
        """Convert specification choices to model kwargs."""
        kwargs = {}
        
        # Fixed effects
        kwargs['fixed_effects'] = specification.get('fixed_effects', 'market')
        
        # Clustering  
        kwargs['cluster'] = specification.get('clustering', 'market')
        
        # Time trends
        kwargs['time_trends'] = specification.get('time_trends', 'none')
        
        # Controls
        control_sets = {
            'minimal': ['conflict_intensity'],
            'standard': ['conflict_intensity', 'population', 'distance_to_border'],
            'extended': ['conflict_intensity', 'population', 'distance_to_border', 
                        'aid_presence', 'rainfall', 'temperature'],
            'kitchen_sink': ['conflict_intensity', 'population', 'distance_to_border',
                           'aid_presence', 'rainfall', 'temperature', 'road_density',
                           'market_size', 'storage_capacity', 'trader_count']
        }
        kwargs['controls'] = control_sets.get(specification.get('controls', 'standard'))
        
        return kwargs
        
    def create_specification_curve_plot(self, 
                                      highlight_main: bool = True,
                                      save_path: Optional[str] = None) -> plt.Figure:
        """
        Create enhanced specification curve visualization.
        
        Args:
            highlight_main: Highlight the main specification
            save_path: Path to save figure
            
        Returns:
            matplotlib figure
        """
        if not self.results:
            logger.warning("No results to plot")
            return None
            
        # Sort results by coefficient value
        sorted_results = sorted(self.results, key=lambda r: r.coefficient)
        
        # Create figure with GridSpec for better layout control
        fig = plt.figure(figsize=(14, 10))
        gs = fig.add_gridspec(4, 2, height_ratios=[3, 1, 1, 1], 
                             width_ratios=[5, 1], hspace=0.1)
        
        # Main specification curve
        ax1 = fig.add_subplot(gs[0, :])
        self._plot_specification_curve(ax1, sorted_results, highlight_main)
        
        # P-values
        ax2 = fig.add_subplot(gs[1, 0])
        self._plot_p_values(ax2, sorted_results)
        
        # Sample sizes
        ax3 = fig.add_subplot(gs[2, 0])
        self._plot_sample_sizes(ax3, sorted_results)
        
        # Specification matrix
        ax4 = fig.add_subplot(gs[3, 0])
        self._plot_specification_matrix(ax4, sorted_results)
        
        # Summary statistics
        ax5 = fig.add_subplot(gs[1:, 1])
        self._plot_summary_statistics(ax5)
        
        plt.suptitle(f'Specification Curve Analysis: {self.model_name}', 
                    fontsize=16, y=0.98)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
        
    def _plot_specification_curve(self, ax: plt.Axes, 
                                 sorted_results: List[SpecificationResult],
                                 highlight_main: bool) -> None:
        """Plot the main specification curve panel."""
        x = range(len(sorted_results))
        coefficients = [r.coefficient for r in sorted_results]
        ci_lower = [r.ci_lower for r in sorted_results]
        ci_upper = [r.ci_upper for r in sorted_results]
        significant = [r.p_value < 0.05 for r in sorted_results]
        
        # Color by significance
        colors = ['#2E86AB' if sig else '#A8DADC' for sig in significant]
        
        # Plot coefficients
        ax.scatter(x, coefficients, c=colors, alpha=0.7, s=20, zorder=3)
        
        # Confidence intervals
        for i, (lower, upper, sig) in enumerate(zip(ci_lower, ci_upper, significant)):
            color = '#2E86AB' if sig else '#A8DADC'
            ax.plot([i, i], [lower, upper], color=color, alpha=0.3, linewidth=1)
        
        # Reference lines
        ax.axhline(y=0, color='#E63946', linestyle='--', alpha=0.7, linewidth=1.5)
        median_coef = np.median(coefficients)
        ax.axhline(y=median_coef, color='#F77F00', linestyle='-', alpha=0.7, 
                  linewidth=2, label=f'Median: {median_coef:.3f}')
        
        # Highlight main specification if requested
        if highlight_main:
            main_spec_idx = self._find_main_specification(sorted_results)
            if main_spec_idx is not None:
                ax.scatter(main_spec_idx, coefficients[main_spec_idx], 
                         color='#D62828', s=100, marker='*', zorder=4,
                         label='Main specification')
        
        ax.set_ylabel('Effect Size', fontsize=12)
        ax.set_xlim(-5, len(sorted_results) + 5)
        ax.grid(True, alpha=0.3, linestyle=':')
        ax.legend(loc='upper left')
        ax.set_xticklabels([])
        
    def _plot_p_values(self, ax: plt.Axes, sorted_results: List[SpecificationResult]) -> None:
        """Plot p-values panel."""
        x = range(len(sorted_results))
        p_values = [r.p_value for r in sorted_results]
        significant = [p < 0.05 for p in p_values]
        
        colors = ['#2E86AB' if sig else '#A8DADC' for sig in significant]
        ax.scatter(x, p_values, c=colors, alpha=0.7, s=15)
        ax.axhline(y=0.05, color='#E63946', linestyle='--', alpha=0.7)
        ax.set_ylabel('P-value', fontsize=10)
        ax.set_ylim(0, 1)
        ax.set_xlim(-5, len(sorted_results) + 5)
        ax.grid(True, alpha=0.3, linestyle=':')
        ax.set_xticklabels([])
        
    def _plot_sample_sizes(self, ax: plt.Axes, sorted_results: List[SpecificationResult]) -> None:
        """Plot sample sizes panel."""
        x = range(len(sorted_results))
        n_obs = [r.n_obs for r in sorted_results]
        
        ax.bar(x, n_obs, color='#457B9D', alpha=0.5, width=1)
        ax.set_ylabel('N Obs', fontsize=10)
        ax.set_xlim(-5, len(sorted_results) + 5)
        ax.set_xticklabels([])
        
    def _plot_specification_matrix(self, ax: plt.Axes, 
                                  sorted_results: List[SpecificationResult]) -> None:
        """Plot specification choices matrix."""
        if not self.analytical_choices:
            return
            
        # Create matrix of choices
        n_specs = len(sorted_results)
        n_choices = len(self.analytical_choices)
        
        choice_matrix = np.zeros((n_choices, n_specs))
        
        for i, result in enumerate(sorted_results):
            for j, choice in enumerate(self.analytical_choices):
                value = result.specification.get(choice.name)
                if value in choice.options:
                    choice_idx = choice.options.index(value)
                    choice_matrix[j, i] = choice_idx / (len(choice.options) - 1)
                    
        # Plot heatmap
        im = ax.imshow(choice_matrix, aspect='auto', cmap='viridis', 
                      interpolation='nearest')
        
        # Labels
        ax.set_yticks(range(n_choices))
        ax.set_yticklabels([c.name.replace('_', ' ').title() 
                           for c in self.analytical_choices], fontsize=9)
        ax.set_xlabel('Specification Number', fontsize=10)
        ax.set_xlim(-0.5, n_specs - 0.5)
        
    def _plot_summary_statistics(self, ax: plt.Axes) -> None:
        """Plot summary statistics panel."""
        ax.axis('off')
        
        summary = self.summarize_robustness()
        
        # Format summary text
        text_lines = [
            f"Total Specifications: {summary['total_specifications']}",
            f"Median Effect: {summary['median_effect']:.4f}",
            f"Mean Effect: {summary['mean_effect']:.4f}",
            f"Std Effect: {summary['std_effect']:.4f}",
            f"% Significant: {summary['pct_significant']:.1f}%",
            f"% Same Sign: {summary['pct_same_sign']:.1f}%",
            f"Min Effect: {summary['min_effect']:.4f}",
            f"Max Effect: {summary['max_effect']:.4f}",
            "",
            f"Fragility: {summary['fragility_assessment']}"
        ]
        
        # Color code fragility assessment
        colors = {
            'ROBUST': '#2E86AB',
            'MODERATELY FRAGILE': '#F77F00',
            'FRAGILE': '#E63946'
        }
        
        for i, line in enumerate(text_lines):
            if i == len(text_lines) - 1:  # Fragility line
                for key, color in colors.items():
                    if key in line:
                        ax.text(0.05, 0.9 - i * 0.08, line, fontsize=11,
                               transform=ax.transAxes, weight='bold', color=color)
                        break
            else:
                ax.text(0.05, 0.9 - i * 0.08, line, fontsize=10,
                       transform=ax.transAxes)
                       
    def _find_main_specification(self, sorted_results: List[SpecificationResult]) -> Optional[int]:
        """Find index of main specification in sorted results."""
        # Main specification uses default values
        for i, result in enumerate(sorted_results):
            is_main = True
            for choice in self.analytical_choices:
                if choice.default is not None:
                    if result.specification.get(choice.name) != choice.default:
                        is_main = False
                        break
            if is_main:
                return i
        return None
        
    def summarize_robustness(self) -> Dict[str, Any]:
        """Generate comprehensive robustness summary."""
        if not self.results:
            return {}
            
        coefficients = [r.coefficient for r in self.results if not np.isnan(r.coefficient)]
        p_values = [r.p_value for r in self.results if not np.isnan(r.p_value)]
        
        if not coefficients:
            return {'error': 'No valid results'}
            
        # Basic statistics
        summary = {
            'total_specifications': len(self.specifications),
            'successful_specifications': len(self.results),
            'convergence_rate': len(self.results) / len(self.specifications),
            'median_effect': np.median(coefficients),
            'mean_effect': np.mean(coefficients),
            'std_effect': np.std(coefficients),
            'min_effect': np.min(coefficients),
            'max_effect': np.max(coefficients),
            'pct_significant': (np.array(p_values) < 0.05).mean() * 100 if p_values else 0,
            'pct_same_sign': (np.sign(coefficients) == np.sign(np.median(coefficients))).mean() * 100
        }
        
        # Assess fragility
        summary['fragility_assessment'] = self._assess_fragility(coefficients, p_values)
        
        # Specification importance
        summary['specification_importance'] = self.analyze_specification_importance()
        
        # Robust bounds
        summary['robust_bounds'] = self._calculate_robust_bounds(coefficients)
        
        return summary
        
    def _assess_fragility(self, coefficients: List[float], 
                         p_values: List[float]) -> str:
        """Assess overall fragility of results."""
        # Check if sign changes
        if np.min(coefficients) * np.max(coefficients) < 0:
            sign_change_pct = (np.array(coefficients) < 0).mean()
            if 0.1 < sign_change_pct < 0.9:
                return "FRAGILE: Effect sign changes across specifications"
                
        # Check significance variation
        pct_sig = (np.array(p_values) < 0.05).mean()
        if 0.2 < pct_sig < 0.8:
            return "MODERATELY FRAGILE: Significance varies with specification"
            
        # Check magnitude variation
        cv = np.std(coefficients) / abs(np.mean(coefficients))
        if cv > 1:
            return "FRAGILE: Large variation in effect magnitude"
            
        # Check if mostly robust
        if pct_sig > 0.9 and cv < 0.3:
            return "ROBUST: Consistent across specifications"
            
        return "MODERATELY ROBUST: Some variation but generally consistent"
        
    def _calculate_robust_bounds(self, coefficients: List[float]) -> Dict[str, float]:
        """Calculate various robust bounds on the effect."""
        return {
            'percentile_5': np.percentile(coefficients, 5),
            'percentile_95': np.percentile(coefficients, 95),
            'trimmed_mean_5pct': stats.trim_mean(coefficients, 0.05),
            'winsorized_mean_5pct': stats.mstats.winsorize(coefficients, limits=(0.05, 0.05)).mean(),
            'iqr_lower': np.percentile(coefficients, 25),
            'iqr_upper': np.percentile(coefficients, 75)
        }
        
    def analyze_specification_importance(self) -> Dict[str, float]:
        """Determine which analytical choices matter most."""
        if not self.results or not self.analytical_choices:
            return {}
            
        importance = {}
        coefficients = [r.coefficient for r in self.results if not np.isnan(r.coefficient)]
        
        for choice in self.analytical_choices:
            # Group results by this choice
            groups = {}
            for result in self.results:
                if not np.isnan(result.coefficient):
                    option = result.specification.get(choice.name)
                    if option not in groups:
                        groups[option] = []
                    groups[option].append(result.coefficient)
                    
            # Calculate variance explained by this choice
            if len(groups) > 1 and all(len(g) > 0 for g in groups.values()):
                # Between-group variance
                group_means = [np.mean(g) for g in groups.values()]
                group_sizes = [len(g) for g in groups.values()]
                overall_mean = np.mean(coefficients)
                
                between_var = sum(n * (m - overall_mean)**2 
                                for n, m in zip(group_sizes, group_means))
                total_var = sum((c - overall_mean)**2 for c in coefficients)
                
                importance[choice.name] = between_var / total_var if total_var > 0 else 0
            else:
                importance[choice.name] = 0
                
        # Normalize
        total_importance = sum(importance.values())
        if total_importance > 0:
            importance = {k: v/total_importance for k, v in importance.items()}
            
        return importance
        
    def export_results(self, filepath: str) -> None:
        """Export detailed results to file."""
        if not self.results:
            logger.warning("No results to export")
            return
            
        # Create results dataframe
        results_data = []
        for result in self.results:
            row = {
                'spec_id': result.spec_id,
                'coefficient': result.coefficient,
                'se': result.se,
                'p_value': result.p_value,
                'ci_lower': result.ci_lower,
                'ci_upper': result.ci_upper,
                'n_obs': result.n_obs,
                'r_squared': result.r_squared,
                'converged': result.converged
            }
            # Add specification details
            row.update(result.specification)
            results_data.append(row)
            
        df = pd.DataFrame(results_data)
        
        # Export based on file extension
        if filepath.endswith('.csv'):
            df.to_csv(filepath, index=False)
        elif filepath.endswith('.xlsx'):
            with pd.ExcelWriter(filepath) as writer:
                df.to_excel(writer, sheet_name='Results', index=False)
                
                # Add summary sheet
                summary_df = pd.DataFrame([self.summarize_robustness()])
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
        else:
            df.to_pickle(filepath)
            
        logger.info(f"Results exported to {filepath}")
        
    def create_specification_importance_plot(self, 
                                           save_path: Optional[str] = None) -> plt.Figure:
        """Create visualization of specification choice importance."""
        importance = self.analyze_specification_importance()
        
        if not importance:
            return None
            
        # Sort by importance
        sorted_items = sorted(importance.items(), key=lambda x: x[1], reverse=True)
        choices = [item[0].replace('_', ' ').title() for item in sorted_items]
        values = [item[1] * 100 for item in sorted_items]
        
        # Create plot
        fig, ax = plt.subplots(figsize=(10, 6))
        
        bars = ax.barh(choices, values, color='#2E86AB', alpha=0.8)
        
        # Add value labels
        for bar, value in zip(bars, values):
            ax.text(bar.get_width() + 0.5, bar.get_y() + bar.get_height()/2,
                   f'{value:.1f}%', va='center', fontsize=10)
                   
        ax.set_xlabel('Relative Importance (%)', fontsize=12)
        ax.set_title('Specification Choice Importance Analysis', fontsize=14)
        ax.grid(True, alpha=0.3, axis='x')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
        return fig