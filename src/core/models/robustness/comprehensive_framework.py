"""
Comprehensive Robustness Framework

Integrates all robustness testing components into a unified framework
that can be applied to any analysis in the Yemen market integration project.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field
import json
import datetime
from pathlib import Path

from .specification_curve_analysis import SpecificationCurveAnalysis
from .resampling_methods import ResamplingRobustness
from .sensitivity_analysis import SensitivityAnalysis
from .robustness_dashboard import RobustnessDashboard

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class RobustTestResults:
    """Complete results from robustness testing."""
    main_results: Dict[str, Any]
    specification_curve: Dict[str, Any]
    bootstrap_results: Dict[str, Any]
    sensitivity_results: List[Dict[str, Any]]
    subsample_results: Dict[str, Any]
    overall_assessment: str
    robustness_score: float
    report_path: Optional[str] = None
    visualizations: Dict[str, Any] = field(default_factory=dict)


class ComprehensiveRobustnessFramework:
    """
    Unified framework for conducting all robustness tests.
    
    This class orchestrates the complete robustness testing pipeline:
    1. Specification curve analysis
    2. Bootstrap and resampling tests
    3. Sensitivity analysis
    4. Subsample stability
    5. Visualization and reporting
    """
    
    def __init__(self, 
                 project_name: str = "Yemen Market Integration",
                 output_dir: Optional[str] = None):
        """
        Initialize the framework.
        
        Args:
            project_name: Name for identification in outputs
            output_dir: Directory for saving results
        """
        self.project_name = project_name
        self.output_dir = Path(output_dir) if output_dir else Path("results/robustness")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.spec_curve = SpecificationCurveAnalysis(None)
        self.resampling = ResamplingRobustness()
        self.sensitivity = SensitivityAnalysis()
        self.dashboard = RobustnessDashboard()
        
    def run_comprehensive_test(self,
                             data: pd.DataFrame,
                             main_model: Callable,
                             hypothesis_name: str,
                             **kwargs) -> RobustTestResults:
        """
        Run complete robustness testing suite.
        
        Args:
            data: Input data
            main_model: Primary model function
            hypothesis_name: Name of hypothesis being tested
            **kwargs: Additional arguments for specific tests
            
        Returns:
            RobustTestResults with complete robustness analysis
        """
        logger.info(f"Starting comprehensive robustness test for {hypothesis_name}")
        
        # 1. Run main specification
        main_results = self._run_main_specification(data, main_model)
        
        # 2. Specification curve analysis
        spec_results = self._run_specification_curve(data, main_model, **kwargs)
        
        # 3. Bootstrap tests
        bootstrap_results = self._run_bootstrap_tests(data, main_model, **kwargs)
        
        # 4. Sensitivity analysis
        sensitivity_results = self._run_sensitivity_tests(
            data, main_model, main_results, **kwargs
        )
        
        # 5. Subsample stability
        subsample_results = self._run_subsample_analysis(data, main_model, **kwargs)
        
        # 6. Calculate overall robustness
        robustness_score = self._calculate_overall_robustness(
            spec_results, bootstrap_results, sensitivity_results, subsample_results
        )
        
        overall_assessment = self._generate_overall_assessment(robustness_score)
        
        # 7. Create visualizations
        visualizations = self._create_visualizations(
            main_results, spec_results, bootstrap_results, 
            sensitivity_results, subsample_results, hypothesis_name
        )
        
        # 8. Generate report
        report_path = self._generate_report(
            hypothesis_name, main_results, spec_results,
            bootstrap_results, sensitivity_results, subsample_results,
            robustness_score, overall_assessment
        )
        
        return RobustTestResults(
            main_results=main_results,
            specification_curve=spec_results,
            bootstrap_results=bootstrap_results,
            sensitivity_results=sensitivity_results,
            subsample_results=subsample_results,
            overall_assessment=overall_assessment,
            robustness_score=robustness_score,
            report_path=str(report_path),
            visualizations=visualizations
        )
        
    def _run_main_specification(self, data: pd.DataFrame, 
                              model: Callable) -> Dict[str, Any]:
        """Run the main model specification."""
        logger.info("Running main specification")
        
        try:
            result = model(data)
            
            # Ensure standard format
            if not isinstance(result, dict):
                result = {'coefficient': result}
                
            # Add basic statistics if missing
            if 'n_obs' not in result:
                result['n_obs'] = len(data)
                
            return result
            
        except Exception as e:
            logger.error(f"Main specification failed: {e}")
            raise
            
    def _run_specification_curve(self, data: pd.DataFrame,
                               model: Callable,
                               **kwargs) -> Dict[str, Any]:
        """Run enhanced specification curve analysis with 1000+ specifications."""
        logger.info("Running enhanced specification curve analysis")
        
        # Set up specification curve
        self.spec_curve.base_model = model
        self.spec_curve.define_analytical_choices()
        
        # Add Yemen-specific choices
        yemen_choices = [
            {
                'name': 'currency_zone_buffer',
                'options': ['no_buffer', '10km_buffer', '25km_buffer', '50km_buffer'],
                'description': 'Currency zone boundary treatment',
                'default': 'no_buffer'
            },
            {
                'name': 'conflict_measurement',
                'options': [
                    'acled_events', 'acled_fatalities', 'territorial_control',
                    'aid_disruption', 'spatial_spillover'
                ],
                'description': 'Conflict intensity measurement',
                'default': 'acled_events'
            },
            {
                'name': 'seasonality_controls',
                'options': [
                    'month_fe', 'ramadan_controls', 'harvest_season',
                    'flood_season', 'no_seasonal'
                ],
                'description': 'Seasonal control variables',
                'default': 'month_fe'
            },
            {
                'name': 'aid_controls',
                'options': [
                    'no_aid', 'aid_binary', 'aid_intensity', 
                    'aid_lagged', 'aid_instrumented'
                ],
                'description': 'Humanitarian aid controls',
                'default': 'aid_binary'
            },
            {
                'name': 'territorial_stability',
                'options': [
                    'all_markets', 'stable_only', 'exclude_contested',
                    'stability_weighted', 'frontline_only'
                ],
                'description': 'Territorial control stability',
                'default': 'all_markets'
            }
        ]
        
        for choice in yemen_choices:
            self.spec_curve.add_choice(**choice)
                
        # Add custom choices if provided
        if 'custom_choices' in kwargs:
            for choice in kwargs['custom_choices']:
                self.spec_curve.add_choice(**choice)
                
        # Generate all specifications (targeting 1000+)
        self.spec_curve.generate_all_specifications()
        logger.info(f"Generated {len(self.spec_curve.specifications)} specifications")
        
        # Run all specifications with parallel processing
        self.spec_curve.run_all_specifications(
            data, 
            parallel=kwargs.get('parallel', True),
            max_workers=kwargs.get('max_workers', -1),
            progress_bar=kwargs.get('progress_bar', True)
        )
        
        # Enhanced summary with specification importance
        summary = self.spec_curve.summarize_robustness()
        summary['coefficients'] = [r.coefficient for r in self.spec_curve.results]
        summary['significant'] = [r.p_value < 0.05 for r in self.spec_curve.results]
        summary['n_specs'] = len(self.spec_curve.specifications)
        summary['assessment'] = summary['fragility_assessment']
        summary['specification_importance'] = self.spec_curve.analyze_specification_importance()
        
        # Yemen-specific importance analysis
        summary['yemen_specific_importance'] = self._analyze_yemen_importance()
        
        return summary
        
    def _analyze_yemen_importance(self) -> Dict[str, float]:
        """Analyze importance of Yemen-specific choices."""
        if not hasattr(self.spec_curve, 'results') or not self.spec_curve.results:
            return {}
            
        yemen_choices = ['currency_zone_buffer', 'conflict_measurement', 
                        'seasonality_controls', 'aid_controls', 'territorial_stability']
        
        importance = {}
        coefficients = [r.coefficient for r in self.spec_curve.results 
                       if not np.isnan(r.coefficient)]
        
        if not coefficients:
            return {}
            
        overall_mean = np.mean(coefficients)
        total_var = np.var(coefficients)
        
        for choice_name in yemen_choices:
            # Group results by this choice
            groups = {}
            for result in self.spec_curve.results:
                if not np.isnan(result.coefficient):
                    choice_value = result.specification.get(choice_name)
                    if choice_value not in groups:
                        groups[choice_value] = []
                    groups[choice_value].append(result.coefficient)
                    
            # Calculate variance explained
            if len(groups) > 1 and all(len(g) > 0 for g in groups.values()):
                between_var = sum(
                    len(g) * (np.mean(g) - overall_mean)**2 
                    for g in groups.values()
                )
                importance[choice_name] = between_var / (total_var * len(coefficients))
            else:
                importance[choice_name] = 0.0
                
        return importance
        
    def _run_bootstrap_tests(self, data: pd.DataFrame,
                           model: Callable,
                           **kwargs) -> Dict[str, Any]:
        """Run enhanced bootstrap tests with Yemen-specific clustering."""
        logger.info("Running enhanced bootstrap tests")
        
        results = {}
        
        # Cluster bootstrap (standard)
        cluster_var = kwargs.get('cluster_var', 'market_id')
        if cluster_var in data.columns:
            logger.info(f"Running cluster bootstrap on {cluster_var}")
            cluster_result = self.resampling.cluster_bootstrap(
                data, model, cluster_var,
                n_bootstrap=kwargs.get('n_bootstrap', 1000),
                return_distribution=True,
                parallel=True
            )
            results['cluster'] = self._format_bootstrap_result(cluster_result)
            
        # Wild cluster bootstrap for few clusters (Yemen has few governorates)
        if cluster_var in data.columns:
            n_clusters = data[cluster_var].nunique()
            if n_clusters < 30:
                logger.info(f"Running wild cluster bootstrap ({n_clusters} clusters)")
                wild_result = self.resampling.wild_cluster_bootstrap(
                    data, model, cluster_var,
                    n_bootstrap=999,  # Odd number for exact p-values
                    weights='webb'    # Webb 6-point for better properties
                )
                results['wild_cluster'] = self._format_bootstrap_result(wild_result)
                
        # Spatial bootstrap for geographic correlation
        if 'latitude' in data.columns and 'longitude' in data.columns:
            logger.info("Running spatial bootstrap")
            spatial_result = self.resampling.spatial_bootstrap(
                data, model,
                x_coord='longitude',
                y_coord='latitude',
                n_bootstrap=kwargs.get('n_bootstrap', 1000)
            )
            results['spatial'] = self._format_bootstrap_result(spatial_result)
            
        # Block bootstrap for time series
        if 'date' in data.columns:
            logger.info("Running block bootstrap for time series")
            block_result = self.resampling.block_bootstrap(
                data, model,
                time_var='date',
                entity_var=cluster_var if cluster_var in data.columns else None,
                n_bootstrap=kwargs.get('n_bootstrap', 1000),
                method='moving_block'
            )
            results['block'] = self._format_bootstrap_result(block_result)
            
        # Subsampling for robust inference
        logger.info("Running subsampling")
        subsample_result = self.resampling.subsampling(
            data, model,
            n_subsamples=1000
        )
        results['subsampling'] = subsample_result
        
        # Choose primary result based on data structure
        if 'wild_cluster' in results:
            results.update(results['wild_cluster'])  # Use wild bootstrap for few clusters
        elif 'cluster' in results:
            results.update(results['cluster'])       # Use cluster bootstrap
        elif 'spatial' in results:
            results.update(results['spatial'])       # Use spatial bootstrap
        elif 'block' in results:
            results.update(results['block'])         # Use block bootstrap
            
        return results
        
    def _format_bootstrap_result(self, result) -> Dict[str, Any]:
        """Format bootstrap result for consistency."""
        return {
            'point_estimate': result.point_estimate,
            'bootstrap_se': result.bootstrap_se,
            'ci_lower': result.percentile_ci[0],
            'ci_upper': result.percentile_ci[1],
            'convergence_rate': result.convergence_rate,
            'n_bootstrap': result.n_bootstrap,
            'converged': result.convergence_rate > 0.9
        }
        
    def _run_sensitivity_tests(self, data: pd.DataFrame,
                             model: Callable,
                             main_results: Dict[str, Any],
                             **kwargs) -> List[Dict[str, Any]]:
        """Run sensitivity analyses."""
        logger.info("Running sensitivity analyses")
        
        sensitivity_results = []
        
        # Omitted variable bias (Oster bounds)
        if 'r_squared' in main_results:
            logger.info("Testing omitted variable bias (Oster bounds)")
            ovb_result = self.sensitivity.omitted_variable_bias(
                main_results,
                r_squared_current=main_results['r_squared'],
                r_squared_max=kwargs.get('r_squared_max', min(1.0, main_results['r_squared'] * 1.3))
            )
            sensitivity_results.append({
                'test': 'Omitted Variable Bias (Oster)',
                'assessment': ovb_result.robustness_assessment,
                'critical_value': ovb_result.critical_value,
                'bounds': ovb_result.bounds
            })
            
        # Measurement error sensitivity
        logger.info("Testing measurement error sensitivity")
        me_result = self.sensitivity.measurement_error_sensitivity(
            main_results,
            reliability_range=kwargs.get('reliability_range', (0.7, 1.0))
        )
        sensitivity_results.append({
            'test': 'Measurement Error',
            'assessment': me_result.robustness_assessment,
            'critical_value': me_result.critical_value
        })
        
        # Bounds analysis if treatment variable specified
        if 'treatment_var' in kwargs and 'outcome_var' in kwargs:
            logger.info("Running bounds analysis")
            bounds_result = self.sensitivity.bounds_analysis(
                data,
                outcome_var=kwargs['outcome_var'],
                treatment_var=kwargs['treatment_var'],
                assumptions=kwargs.get('bounds_assumptions', ['monotone_treatment'])
            )
            sensitivity_results.append({
                'test': 'Bounds Analysis',
                'assessment': bounds_result.robustness_assessment,
                'bounds': bounds_result.bounds
            })
            
        # Placebo tests if specified
        if 'placebo_specs' in kwargs:
            logger.info("Running placebo tests")
            placebo_result = self.sensitivity.placebo_test_suite(
                data, model, kwargs['placebo_specs']
            )
            sensitivity_results.append({
                'test': 'Placebo Tests',
                'assessment': placebo_result.robustness_assessment,
                'details': placebo_result.adjusted_estimates
            })
            
        return sensitivity_results
        
    def _run_subsample_analysis(self, data: pd.DataFrame,
                              model: Callable,
                              **kwargs) -> Dict[str, Any]:
        """Run analysis on various subsamples."""
        logger.info("Running subsample analysis")
        
        subsample_specs = kwargs.get('subsample_specs', self._get_default_subsamples())
        results = {
            'subsamples': [],
            'estimates': [],
            'ci_lower': [],
            'ci_upper': [],
            'main_estimate': None
        }
        
        # Run main estimate first
        main_result = model(data)
        if isinstance(main_result, dict):
            results['main_estimate'] = main_result.get('coefficient')
        else:
            results['main_estimate'] = main_result
            
        # Run subsamples
        for spec in subsample_specs:
            subsample_data = self._create_subsample(data, spec)
            
            if len(subsample_data) < 100:  # Skip if too small
                continue
                
            try:
                result = model(subsample_data)
                if isinstance(result, dict):
                    estimate = result.get('coefficient')
                    se = result.get('se', 0.1)
                else:
                    estimate = result
                    se = 0.1  # Default SE
                    
                results['subsamples'].append(spec['name'])
                results['estimates'].append(estimate)
                results['ci_lower'].append(estimate - 1.96 * se)
                results['ci_upper'].append(estimate + 1.96 * se)
                
            except Exception as e:
                logger.debug(f"Subsample {spec['name']} failed: {e}")
                
        # Calculate stability metrics
        if results['estimates']:
            estimates = np.array(results['estimates'])
            results['stability_score'] = 1 - np.std(estimates) / (np.abs(np.mean(estimates)) + 1e-6)
            results['all_same_sign'] = all(np.sign(estimates) == np.sign(estimates[0]))
        else:
            results['stability_score'] = 0
            results['all_same_sign'] = False
            
        return results
        
    def _run_yemen_specific_tests(self, data: pd.DataFrame,
                                model: Callable,
                                **kwargs) -> Dict[str, Any]:
        """Run Yemen-specific robustness tests."""
        logger.info("Running Yemen-specific robustness tests")
        
        from .yemen_specific_robustness import YemenSpecificRobustness
        yemen_robustness = YemenSpecificRobustness()
        
        results = {}
        
        # Currency zone robustness
        if 'baseline_zones' in kwargs:
            zone_results = yemen_robustness.test_currency_zone_robustness(
                data, model, kwargs['baseline_zones']
            )
            results['currency_zones'] = {
                'effect_stability': zone_results.effect_stability,
                'critical_markets': zone_results.critical_markets,
                'n_alternatives': len(zone_results.alternative_definitions)
            }
            
        # Conflict endogeneity
        conflict_results = yemen_robustness.test_conflict_endogeneity_robustness(
            data, model
        )
        results['conflict_endogeneity'] = conflict_results
        
        # Exchange rate measurement
        exchange_results = yemen_robustness.test_exchange_rate_measurement(
            data, model
        )
        results['exchange_rate'] = exchange_results
        
        return results
        
    def _run_conflict_placebo_tests(self, data: pd.DataFrame,
                                  model: Callable,
                                  **kwargs) -> Dict[str, Any]:
        """Run conflict-specific placebo tests."""
        logger.info("Running conflict economics placebo tests")
        
        # Define Yemen-specific placebo tests
        placebo_specs = [
            {
                'name': 'Fake Treatment Time (Pre-Fragmentation)',
                'type': 'fake_treatment_time',
                'fake_date': '2018-01-01'  # Before currency fragmentation
            },
            {
                'name': 'Fake Treatment Time (COVID Period)',
                'type': 'fake_treatment_time', 
                'fake_date': '2020-03-01'  # COVID impact period
            },
            {
                'name': 'Random Market Assignment',
                'type': 'fake_treatment_location',
                'treatment_proportion': 0.5
            },
            {
                'name': 'Lead Treatment (Future Shock)',
                'type': 'fake_treatment_time',
                'fake_date': '2025-01-01'  # Future date
            }
        ]
        
        # Add unaffected outcome tests if available
        if 'placebo_outcomes' in kwargs:
            for outcome in kwargs['placebo_outcomes']:
                placebo_specs.append({
                    'name': f'Unaffected Outcome: {outcome}',
                    'type': 'unaffected_outcome',
                    'placebo_outcome': outcome
                })
                
        # Run placebo test suite
        placebo_result = self.sensitivity.placebo_test_suite(
            data, model, placebo_specs
        )
        
        return {
            'test_results': placebo_result.adjusted_estimates,
            'pass_rate': sum(1 for r in placebo_result.adjusted_estimates.values() 
                           if r.get('passed', False)) / len(placebo_specs),
            'assessment': placebo_result.robustness_assessment
        }
        
    def _get_default_subsamples(self) -> List[Dict[str, Any]]:
        """Get default subsample specifications."""
        return [
            {'name': 'North only', 'filter': 'region == "north"'},
            {'name': 'South only', 'filter': 'region == "south"'},
            {'name': 'Pre-2021', 'filter': 'date < "2021-01-01"'},
            {'name': 'Post-2021', 'filter': 'date >= "2021-01-01"'},
            {'name': 'Urban markets', 'filter': 'urban == 1'},
            {'name': 'Rural markets', 'filter': 'urban == 0'},
            {'name': 'High conflict', 'filter': 'conflict_intensity > conflict_intensity.median()'},
            {'name': 'Low conflict', 'filter': 'conflict_intensity <= conflict_intensity.median()'}
        ]
        
    def _create_subsample(self, data: pd.DataFrame, 
                         spec: Dict[str, Any]) -> pd.DataFrame:
        """Create subsample based on specification."""
        if 'filter' in spec:
            try:
                return data.query(spec['filter'])
            except:
                return data
        elif 'indices' in spec:
            return data.iloc[spec['indices']]
        else:
            return data
            
    def _calculate_overall_robustness(self,
                                    spec_results: Dict[str, Any],
                                    bootstrap_results: Dict[str, Any],
                                    sensitivity_results: List[Dict[str, Any]],
                                    subsample_results: Dict[str, Any],
                                    yemen_results: Optional[Dict[str, Any]] = None,
                                    placebo_results: Optional[Dict[str, Any]] = None) -> float:
        """Calculate overall robustness score from 0 to 1."""
        scores = []
        
        # Specification curve score
        if spec_results:
            # Based on percentage of significant results
            spec_score = spec_results.get('prop_significant', 0)
            scores.append(spec_score)
            
        # Bootstrap convergence
        if bootstrap_results:
            conv_rate = bootstrap_results.get('convergence_rate', 0)
            scores.append(conv_rate)
            
        # Sensitivity tests
        if sensitivity_results:
            robust_tests = sum(1 for r in sensitivity_results 
                             if 'ROBUST' in r.get('assessment', ''))
            sens_score = robust_tests / len(sensitivity_results)
            scores.append(sens_score)
            
        # Subsample stability
        if subsample_results:
            stability = subsample_results.get('stability_score', 0)
            scores.append(stability)
            
        # Yemen-specific robustness
        if yemen_results:
            yemen_score = 0
            n_yemen_tests = 0
            
            # Currency zone stability
            if 'currency_zones' in yemen_results:
                yemen_score += yemen_results['currency_zones'].get('effect_stability', 0)
                n_yemen_tests += 1
                
            # Exchange rate robustness
            if 'exchange_rate' in yemen_results:
                yemen_score += yemen_results['exchange_rate'].get('stability_score', 0)
                n_yemen_tests += 1
                
            if n_yemen_tests > 0:
                scores.append(yemen_score / n_yemen_tests)
                
        # Placebo test results
        if placebo_results:
            placebo_score = placebo_results.get('pass_rate', 0)
            scores.append(placebo_score)
            
        return np.mean(scores) if scores else 0.5
        
    def _generate_overall_assessment(self, robustness_score: float) -> str:
        """Generate text assessment of overall robustness."""
        if robustness_score >= 0.8:
            return ("HIGHLY ROBUST: Results are stable across specifications, "
                   "resampling methods, and sensitivity tests. Suitable for "
                   "policy recommendations.")
        elif robustness_score >= 0.6:
            return ("MODERATELY ROBUST: Results show reasonable stability with "
                   "some variation. Interpret with appropriate caveats.")
        elif robustness_score >= 0.4:
            return ("MIXED ROBUSTNESS: Substantial variation across tests. "
                   "Results should be interpreted cautiously.")
        else:
            return ("FRAGILE: Results are sensitive to analytical choices. "
                   "Additional investigation strongly recommended.")
                   
    def _create_visualizations(self,
                             main_results: Dict[str, Any],
                             spec_results: Dict[str, Any],
                             bootstrap_results: Dict[str, Any],
                             sensitivity_results: List[Dict[str, Any]],
                             subsample_results: Dict[str, Any],
                             hypothesis_name: str) -> Dict[str, str]:
        """Create and save all visualizations."""
        logger.info("Creating visualizations")
        
        viz_paths = {}
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Main dashboard
        dashboard_path = self.output_dir / f"{hypothesis_name}_dashboard_{timestamp}.png"
        
        all_results = {
            'spec_curve': spec_results,
            'bootstrap': bootstrap_results,
            'subsamples': subsample_results,
            'sensitivity': sensitivity_results
        }
        
        fig = self.dashboard.create_dashboard(
            main_results, all_results,
            save_path=str(dashboard_path),
            title=f"Robustness Analysis: {hypothesis_name}"
        )
        viz_paths['dashboard'] = str(dashboard_path)
        
        # Specification curve detail
        if hasattr(self.spec_curve, 'results') and self.spec_curve.results:
            spec_path = self.output_dir / f"{hypothesis_name}_spec_curve_{timestamp}.png"
            fig = self.spec_curve.create_specification_curve_plot(
                save_path=str(spec_path)
            )
            viz_paths['specification_curve'] = str(spec_path)
            
        # Publication figure
        pub_path = self.output_dir / f"{hypothesis_name}_publication_{timestamp}.png"
        fig = self.dashboard.create_publication_figure(
            main_results, spec_results, bootstrap_results,
            save_path=str(pub_path)
        )
        viz_paths['publication'] = str(pub_path)
        
        return viz_paths
        
    def _generate_report(self,
                        hypothesis_name: str,
                        main_results: Dict[str, Any],
                        spec_results: Dict[str, Any],
                        bootstrap_results: Dict[str, Any],
                        sensitivity_results: List[Dict[str, Any]],
                        subsample_results: Dict[str, Any],
                        robustness_score: float,
                        overall_assessment: str) -> Path:
        """Generate comprehensive robustness report."""
        logger.info("Generating robustness report")
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = self.output_dir / f"{hypothesis_name}_robustness_report_{timestamp}.md"
        
        with open(report_path, 'w') as f:
            # Header
            f.write(f"# Robustness Report: {hypothesis_name}\n\n")
            f.write(f"Generated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Project: {self.project_name}\n\n")
            
            # Overall Assessment
            f.write("## Overall Assessment\n\n")
            f.write(f"**Robustness Score: {robustness_score:.1%}**\n\n")
            f.write(f"{overall_assessment}\n\n")
            
            # Main Results
            f.write("## Main Specification Results\n\n")
            f.write(f"- Coefficient: {main_results.get('coefficient', 'N/A'):.4f}\n")
            f.write(f"- Standard Error: {main_results.get('se', 'N/A'):.4f}\n")
            f.write(f"- P-value: {main_results.get('p_value', 'N/A'):.4f}\n")
            f.write(f"- N: {main_results.get('n_obs', 'N/A'):,}\n\n")
            
            # Specification Curve
            f.write("## Specification Curve Analysis\n\n")
            if spec_results:
                f.write(f"- Total specifications: {spec_results.get('n_specifications', 0)}\n")
                f.write(f"- Median coefficient: {spec_results.get('median_coefficient', 0):.4f}\n")
                f.write(f"- % Significant: {spec_results.get('prop_significant', 0)*100:.1f}%\n")
                f.write(f"- Assessment: {spec_results.get('fragility_assessment', 'N/A')}\n\n")
                
            # Bootstrap Results
            f.write("## Bootstrap Inference\n\n")
            if bootstrap_results:
                f.write(f"- Bootstrap SE: {bootstrap_results.get('bootstrap_se', 'N/A'):.4f}\n")
                f.write(f"- 95% CI: [{bootstrap_results.get('ci_lower', 0):.4f}, "
                       f"{bootstrap_results.get('ci_upper', 0):.4f}]\n")
                f.write(f"- Convergence rate: {bootstrap_results.get('convergence_rate', 0):.1%}\n\n")
                
            # Sensitivity Analysis
            f.write("## Sensitivity Analysis\n\n")
            for sens in sensitivity_results:
                f.write(f"### {sens['test']}\n")
                f.write(f"- Assessment: {sens.get('assessment', 'N/A')}\n")
                if 'critical_value' in sens:
                    f.write(f"- Critical value: {sens['critical_value']:.3f}\n")
                f.write("\n")
                
            # Subsample Stability
            f.write("## Subsample Stability\n\n")
            if subsample_results and subsample_results.get('estimates'):
                f.write(f"- Stability score: {subsample_results.get('stability_score', 0):.1%}\n")
                f.write(f"- All same sign: {subsample_results.get('all_same_sign', False)}\n")
                f.write("\n### Subsample Estimates:\n")
                for name, est in zip(subsample_results['subsamples'], 
                                   subsample_results['estimates']):
                    f.write(f"- {name}: {est:.4f}\n")
                    
            # Technical Details
            f.write("\n## Technical Details\n\n")
            f.write("This report was generated using the Comprehensive Robustness Framework:\n")
            f.write("- Specification curve analysis (Simonsohn et al. 2020)\n")
            f.write("- Cluster bootstrap inference (Cameron et al. 2008)\n")
            f.write("- Sensitivity to unobservables (Oster 2019)\n")
            f.write("- Multiple testing corrections applied\n")
            
        logger.info(f"Report saved to {report_path}")
        return report_path
        
    def export_results(self, results: RobustTestResults, 
                      format: str = 'json') -> str:
        """
        Export results in various formats.
        
        Args:
            results: RobustTestResults to export
            format: 'json', 'csv', or 'latex'
            
        Returns:
            Path to exported file
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format == 'json':
            export_path = self.output_dir / f"robustness_results_{timestamp}.json"
            
            # Convert to serializable format
            export_data = {
                'main_results': results.main_results,
                'specification_curve': results.specification_curve,
                'bootstrap_results': results.bootstrap_results,
                'sensitivity_results': results.sensitivity_results,
                'subsample_results': results.subsample_results,
                'overall_assessment': results.overall_assessment,
                'robustness_score': results.robustness_score
            }
            
            with open(export_path, 'w') as f:
                json.dump(export_data, f, indent=2)
                
        elif format == 'latex':
            export_path = self.output_dir / f"robustness_table_{timestamp}.tex"
            self._create_latex_table(results, export_path)
            
        else:
            raise ValueError(f"Unsupported format: {format}")
            
        return str(export_path)
        
    def _create_latex_table(self, results: RobustTestResults, path: Path):
        """Create LaTeX table of robustness results."""
        with open(path, 'w') as f:
            f.write("\\begin{table}[htbp]\n")
            f.write("\\centering\n")
            f.write("\\caption{Robustness Analysis Results}\n")
            f.write("\\begin{tabular}{lc}\n")
            f.write("\\hline\\hline\n")
            f.write("Test & Result \\\\\n")
            f.write("\\hline\n")
            
            # Main result
            f.write(f"Main specification & {results.main_results.get('coefficient', 0):.3f}"
                   f" ({results.main_results.get('se', 0):.3f}) \\\\\n")
                   
            # Specification curve
            if results.specification_curve:
                f.write(f"Specification curve & {results.specification_curve.get('n_specifications', 0)} specs, "
                       f"{results.specification_curve.get('prop_significant', 0)*100:.0f}\\% sig. \\\\\n")
                       
            # Bootstrap
            if results.bootstrap_results:
                f.write(f"Bootstrap 95\\% CI & [{results.bootstrap_results.get('ci_lower', 0):.3f}, "
                       f"{results.bootstrap_results.get('ci_upper', 0):.3f}] \\\\\n")
                       
            # Overall
            f.write(f"Robustness score & {results.robustness_score:.0%} \\\\\n")
            
            f.write("\\hline\\hline\n")
            f.write("\\end{tabular}\n")
            f.write("\\end{table}\n")