"""
Pre-Analysis Plan Module

Implements pre-registration of analytical choices to prevent
p-hacking and ensure research integrity.
"""

import json
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field, asdict
import pandas as pd

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class AnalyticalChoice:
    """A single analytical choice that must be pre-specified."""
    name: str
    description: str
    options: List[Any]
    default: Any
    justification: str


@dataclass
class PreAnalysisPlan:
    """
    Pre-analysis plan for a hypothesis test.
    
    Once locked, prevents changes to analytical choices,
    ensuring research integrity.
    """
    hypothesis_id: str
    created_at: datetime = field(default_factory=datetime.now)
    locked_at: Optional[datetime] = None
    is_locked: bool = False
    
    # Main specification
    main_specification: Optional[Dict[str, Any]] = None
    
    # Analytical choices
    analytical_choices: List[AnalyticalChoice] = field(default_factory=list)
    
    # Robustness tests to run
    robustness_tests: List[str] = field(default_factory=list)
    
    # Expected results (optional, for stronger pre-registration)
    expected_direction: Optional[str] = None  # 'positive', 'negative', 'null'
    expected_magnitude: Optional[float] = None
    uncertainty_range: Optional[Tuple[float, float]] = None
    
    # Decision rules
    decision_rules: Dict[str, str] = field(default_factory=dict)
    
    # Hash for integrity
    content_hash: Optional[str] = None
    
    def add_analytical_choice(self, name: str, description: str,
                            options: List[Any], default: Any,
                            justification: str):
        """Add an analytical choice to the plan."""
        if self.is_locked:
            raise ValueError("Cannot modify locked pre-analysis plan")
            
        choice = AnalyticalChoice(
            name=name,
            description=description,
            options=options,
            default=default,
            justification=justification
        )
        self.analytical_choices.append(choice)
        
    def set_main_specification(self, specification: Dict[str, Any]):
        """Set the main model specification."""
        if self.is_locked:
            raise ValueError("Cannot modify locked pre-analysis plan")
            
        self.main_specification = specification
        
    def add_robustness_test(self, test_name: str):
        """Add a robustness test to run."""
        if self.is_locked:
            raise ValueError("Cannot modify locked pre-analysis plan")
            
        self.robustness_tests.append(test_name)
        
    def add_decision_rule(self, condition: str, action: str):
        """Add a decision rule for interpreting results."""
        if self.is_locked:
            raise ValueError("Cannot modify locked pre-analysis plan")
            
        self.decision_rules[condition] = action
        
    def lock(self, save_path: Optional[str] = None):
        """
        Lock the pre-analysis plan, preventing further changes.
        
        Args:
            save_path: Path to save the locked plan
        """
        if self.is_locked:
            logger.warning("Plan already locked")
            return
            
        # Calculate content hash
        content = {
            'hypothesis_id': self.hypothesis_id,
            'main_specification': self.main_specification,
            'analytical_choices': [asdict(c) for c in self.analytical_choices],
            'robustness_tests': self.robustness_tests,
            'expected_direction': self.expected_direction,
            'expected_magnitude': self.expected_magnitude,
            'decision_rules': self.decision_rules
        }
        
        content_str = json.dumps(content, sort_keys=True)
        self.content_hash = hashlib.sha256(content_str.encode()).hexdigest()
        
        # Lock the plan
        self.locked_at = datetime.now()
        self.is_locked = True
        
        # Save if requested
        if save_path:
            self.save(save_path)
            
        logger.info(f"Pre-analysis plan locked for {self.hypothesis_id}")
        logger.info(f"Content hash: {self.content_hash[:16]}...")
        
    def save(self, path: str):
        """Save the plan to file."""
        plan_dict = {
            'hypothesis_id': self.hypothesis_id,
            'created_at': self.created_at.isoformat(),
            'locked_at': self.locked_at.isoformat() if self.locked_at else None,
            'is_locked': self.is_locked,
            'content_hash': self.content_hash,
            'main_specification': self.main_specification,
            'analytical_choices': [asdict(c) for c in self.analytical_choices],
            'robustness_tests': self.robustness_tests,
            'expected_direction': self.expected_direction,
            'expected_magnitude': self.expected_magnitude,
            'uncertainty_range': self.uncertainty_range,
            'decision_rules': self.decision_rules
        }
        
        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(path, 'w') as f:
            json.dump(plan_dict, f, indent=2)
            
        logger.info(f"Pre-analysis plan saved to {path}")
        
    @classmethod
    def load(cls, path: str) -> 'PreAnalysisPlan':
        """Load a pre-analysis plan from file."""
        with open(path, 'r') as f:
            plan_dict = json.load(f)
            
        plan = cls(hypothesis_id=plan_dict['hypothesis_id'])
        plan.created_at = datetime.fromisoformat(plan_dict['created_at'])
        plan.locked_at = datetime.fromisoformat(plan_dict['locked_at']) if plan_dict['locked_at'] else None
        plan.is_locked = plan_dict['is_locked']
        plan.content_hash = plan_dict['content_hash']
        plan.main_specification = plan_dict['main_specification']
        plan.analytical_choices = [
            AnalyticalChoice(**choice) for choice in plan_dict['analytical_choices']
        ]
        plan.robustness_tests = plan_dict['robustness_tests']
        plan.expected_direction = plan_dict.get('expected_direction')
        plan.expected_magnitude = plan_dict.get('expected_magnitude')
        plan.uncertainty_range = plan_dict.get('uncertainty_range')
        plan.decision_rules = plan_dict['decision_rules']
        
        return plan
        
    def verify_integrity(self) -> bool:
        """Verify the plan hasn't been tampered with."""
        if not self.is_locked:
            logger.warning("Plan not locked, cannot verify integrity")
            return True
            
        # Recalculate hash
        content = {
            'hypothesis_id': self.hypothesis_id,
            'main_specification': self.main_specification,
            'analytical_choices': [asdict(c) for c in self.analytical_choices],
            'robustness_tests': self.robustness_tests,
            'expected_direction': self.expected_direction,
            'expected_magnitude': self.expected_magnitude,
            'decision_rules': self.decision_rules
        }
        
        content_str = json.dumps(content, sort_keys=True)
        current_hash = hashlib.sha256(content_str.encode()).hexdigest()
        
        return current_hash == self.content_hash
        
    def generate_report(self) -> str:
        """Generate a formatted report of the plan."""
        report = f"""
PRE-ANALYSIS PLAN: {self.hypothesis_id}
{'='*60}

Created: {self.created_at.strftime('%Y-%m-%d %H:%M:%S')}
Locked: {self.locked_at.strftime('%Y-%m-%d %H:%M:%S') if self.locked_at else 'Not locked'}
Status: {'LOCKED' if self.is_locked else 'DRAFT'}
Hash: {self.content_hash[:16] if self.content_hash else 'N/A'}...

MAIN SPECIFICATION
-----------------
{json.dumps(self.main_specification, indent=2) if self.main_specification else 'Not specified'}

ANALYTICAL CHOICES
-----------------
"""
        for i, choice in enumerate(self.analytical_choices, 1):
            report += f"\n{i}. {choice.name}: {choice.description}\n"
            report += f"   Options: {choice.options}\n"
            report += f"   Default: {choice.default}\n"
            report += f"   Justification: {choice.justification}\n"
            
        report += f"\nROBUSTNESS TESTS\n"
        report += "-----------------\n"
        for test in self.robustness_tests:
            report += f"- {test}\n"
            
        if self.expected_direction:
            report += f"\nEXPECTED RESULTS\n"
            report += "-----------------\n"
            report += f"Direction: {self.expected_direction}\n"
            if self.expected_magnitude:
                report += f"Magnitude: {self.expected_magnitude}\n"
            if self.uncertainty_range:
                report += f"Range: {self.uncertainty_range}\n"
                
        if self.decision_rules:
            report += f"\nDECISION RULES\n"
            report += "-----------------\n"
            for condition, action in self.decision_rules.items():
                report += f"IF {condition}: {action}\n"
                
        return report


class PreAnalysisRegistry:
    """Registry for managing multiple pre-analysis plans."""
    
    _plans: Dict[str, PreAnalysisPlan] = {}
    _save_dir: Path = Path("pre_analysis_plans")
    
    @classmethod
    def register(cls, plan: PreAnalysisPlan, auto_lock: bool = False):
        """Register a pre-analysis plan."""
        if plan.hypothesis_id in cls._plans:
            raise ValueError(f"Plan for {plan.hypothesis_id} already registered")
            
        cls._plans[plan.hypothesis_id] = plan
        
        if auto_lock:
            save_path = cls._save_dir / f"{plan.hypothesis_id}_plan.json"
            plan.lock(save_path=str(save_path))
            
    @classmethod
    def get(cls, hypothesis_id: str) -> Optional[PreAnalysisPlan]:
        """Get a registered plan."""
        return cls._plans.get(hypothesis_id)
        
    @classmethod
    def load_all(cls, directory: str = None):
        """Load all plans from directory."""
        load_dir = Path(directory) if directory else cls._save_dir
        
        if not load_dir.exists():
            logger.warning(f"Directory {load_dir} does not exist")
            return
            
        for plan_file in load_dir.glob("*_plan.json"):
            try:
                plan = PreAnalysisPlan.load(str(plan_file))
                cls._plans[plan.hypothesis_id] = plan
                logger.info(f"Loaded plan for {plan.hypothesis_id}")
            except Exception as e:
                logger.error(f"Failed to load {plan_file}: {e}")
                
    @classmethod
    def verify_all(cls) -> Dict[str, bool]:
        """Verify integrity of all plans."""
        results = {}
        for hypothesis_id, plan in cls._plans.items():
            results[hypothesis_id] = plan.verify_integrity()
        return results
        
    @classmethod
    def summary(cls) -> pd.DataFrame:
        """Generate summary of all registered plans."""
        data = []
        for hypothesis_id, plan in cls._plans.items():
            data.append({
                'hypothesis_id': hypothesis_id,
                'created_at': plan.created_at,
                'locked': plan.is_locked,
                'locked_at': plan.locked_at,
                'n_choices': len(plan.analytical_choices),
                'n_robustness': len(plan.robustness_tests),
                'has_expectations': plan.expected_direction is not None
            })
        return pd.DataFrame(data)


def create_yemen_h1_preanalysis_plan() -> PreAnalysisPlan:
    """
    Example: Create pre-analysis plan for H1 (Exchange Rate Mechanism).
    
    This should be done BEFORE looking at the data.
    """
    plan = PreAnalysisPlan(hypothesis_id="H1")
    
    # Main specification
    plan.set_main_specification({
        'model_type': 'panel_fixed_effects',
        'outcome': 'log_price_usd',
        'treatment': 'log_exchange_rate',
        'controls': ['conflict_intensity', 'market_connectivity', 'seasonal_dummies'],
        'fixed_effects': ['market_id', 'commodity_id', 'year_month'],
        'clustering': 'market_id',
        'sample': 'full_balanced_panel'
    })
    
    # Analytical choices
    plan.add_analytical_choice(
        name='exchange_rate_source',
        description='Source of exchange rate data',
        options=['official_cbya', 'official_cbys', 'parallel_market', 'weighted_average'],
        default='parallel_market',
        justification='Parallel market rates better reflect actual transaction prices'
    )
    
    plan.add_analytical_choice(
        name='currency_zone_definition',
        description='Method for defining currency zones',
        options=['static_2024', 'time_varying', 'distance_based'],
        default='static_2024',
        justification='Current territorial control most relevant for policy'
    )
    
    plan.add_analytical_choice(
        name='outlier_treatment',
        description='How to handle price outliers',
        options=['none', 'winsorize_1pct', 'winsorize_5pct', 'robust_regression'],
        default='winsorize_1pct',
        justification='Preserve information while limiting influence of errors'
    )
    
    plan.add_analytical_choice(
        name='missing_data',
        description='How to handle missing price observations',
        options=['complete_case', 'forward_fill_max3', 'multiple_imputation'],
        default='complete_case',
        justification='Missing data often indicates market closure, not random'
    )
    
    # Robustness tests
    plan.add_robustness_test('specification_curve')
    plan.add_robustness_test('bootstrap_clustered')
    plan.add_robustness_test('currency_zone_sensitivity')
    plan.add_robustness_test('pre_trend_test')
    plan.add_robustness_test('placebo_nontradables')
    
    # Expected results
    plan.expected_direction = 'positive'
    plan.expected_magnitude = 0.7  # 70% pass-through
    plan.uncertainty_range = (0.5, 0.9)
    
    # Decision rules
    plan.add_decision_rule(
        condition='p_value < 0.05 AND bootstrap_ci excludes 0',
        action='Reject null of no exchange rate effect'
    )
    
    plan.add_decision_rule(
        condition='zone_stability < 0.6',
        action='Results too sensitive to zone definitions for policy use'
    )
    
    plan.add_decision_rule(
        condition='specification_curve_pct_significant < 0.5',
        action='Evidence too fragile for strong conclusions'
    )
    
    return plan


# Enforcement functions
def enforce_preanalysis_plan(plan: PreAnalysisPlan, 
                            actual_specification: Dict[str, Any]) -> Dict[str, Any]:
    """
    Enforce that analysis follows pre-analysis plan.
    
    Returns dict with any deviations noted.
    """
    if not plan.is_locked:
        raise ValueError("Pre-analysis plan must be locked before enforcement")
        
    deviations = {}
    
    # Check main specification
    if plan.main_specification:
        for key, expected_value in plan.main_specification.items():
            actual_value = actual_specification.get(key)
            if actual_value != expected_value:
                deviations[f'main_spec_{key}'] = {
                    'expected': expected_value,
                    'actual': actual_value
                }
                
    # Check analytical choices
    for choice in plan.analytical_choices:
        actual_value = actual_specification.get(choice.name)
        if actual_value != choice.default:
            deviations[f'choice_{choice.name}'] = {
                'expected': choice.default,
                'actual': actual_value,
                'justification_required': True
            }
            
    return deviations