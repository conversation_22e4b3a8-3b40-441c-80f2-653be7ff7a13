"""
Automated Robustness Testing Pipeline for Yemen Market Integration

This module provides a production-ready automated pipeline for comprehensive
robustness testing that meets World Bank publication standards.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Callable, Any
from pathlib import Path
import json
import datetime
from dataclasses import dataclass, asdict
import logging

from .comprehensive_framework import ComprehensiveRobustnessFramework, RobustTestResults
from .specification_curve_analysis import SpecificationCurveAnalysis
from .resampling_methods import ResamplingRobustness
from .sensitivity_analysis import SensitivityAnalysis
from .yemen_specific_robustness import YemenSpecificRobustness

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class RobustnessPipelineConfig:
    """Configuration for robustness testing pipeline."""
    # Basic settings
    n_bootstrap: int = 1000
    n_specifications: int = 2000  # Target 2000+ specifications
    parallel: bool = True
    max_workers: Optional[int] = None
    
    # Yemen-specific settings
    baseline_zones: Optional[Dict[str, str]] = None
    currency_zone_buffers: List[int] = None
    placebo_outcomes: List[str] = None
    
    # Robustness thresholds
    min_robustness_score: float = 0.7
    min_spec_significance: float = 0.6
    min_bootstrap_convergence: float = 0.9
    
    # Output settings
    output_dir: str = "results/robustness"
    save_distributions: bool = True
    create_visualizations: bool = True
    
    def __post_init__(self):
        if self.currency_zone_buffers is None:
            self.currency_zone_buffers = [10, 25, 50]
        if self.placebo_outcomes is None:
            self.placebo_outcomes = ['temperature', 'rainfall', 'population']


class AutomatedRobustnessPipeline:
    """
    Automated robustness testing pipeline for Yemen market integration analysis.
    
    This pipeline provides:
    1. Comprehensive specification curve analysis (1000+ specifications)
    2. Bootstrap validation with conflict-aware clustering
    3. Yemen-specific robustness tests
    4. Sensitivity analysis (Oster bounds, placebo tests)
    5. Automated reporting and visualization
    """
    
    def __init__(self, config: Optional[RobustnessPipelineConfig] = None):
        """Initialize pipeline with configuration."""
        self.config = config or RobustnessPipelineConfig()
        self.output_dir = Path(self.config.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize framework components
        self.framework = ComprehensiveRobustnessFramework(
            project_name="Yemen Market Integration",
            output_dir=str(self.output_dir)
        )
        
        self.results_cache = {}
        
    def run_hypothesis_robustness(self, 
                                data: pd.DataFrame,
                                hypothesis_model: Callable,
                                hypothesis_name: str,
                                **kwargs) -> RobustTestResults:
        """
        Run complete robustness testing for a single hypothesis.
        
        Args:
            data: Market panel data
            hypothesis_model: Model estimation function
            hypothesis_name: Name of hypothesis (e.g., "H1_Exchange_Rate")
            **kwargs: Additional model-specific parameters
            
        Returns:
            RobustTestResults with comprehensive analysis
        """
        logger.info(f"Starting automated robustness testing for {hypothesis_name}")
        start_time = datetime.datetime.now()
        
        # Prepare configuration
        test_kwargs = self._prepare_test_kwargs(data, **kwargs)
        
        # Run comprehensive robustness test
        results = self.framework.run_comprehensive_test(
            data=data,
            main_model=hypothesis_model,
            hypothesis_name=hypothesis_name,
            **test_kwargs
        )
        
        # Add timing information
        end_time = datetime.datetime.now()
        results.execution_time = (end_time - start_time).total_seconds()
        
        # Validate results against standards
        validation_report = self._validate_results(results, hypothesis_name)
        results.validation_report = validation_report
        
        # Cache results
        self.results_cache[hypothesis_name] = results
        
        # Generate automated report
        self._generate_automated_report(results, hypothesis_name)
        
        logger.info(f"Completed robustness testing for {hypothesis_name} "
                   f"in {results.execution_time:.1f} seconds")
        
        return results
        
    def run_all_hypotheses_robustness(self,
                                    data: pd.DataFrame,
                                    hypothesis_models: Dict[str, Callable],
                                    **kwargs) -> Dict[str, RobustTestResults]:
        """
        Run robustness testing for all hypotheses H1-H10.
        
        Args:
            data: Market panel data
            hypothesis_models: Dictionary mapping hypothesis names to models
            **kwargs: Additional parameters
            
        Returns:
            Dictionary of results for each hypothesis
        """
        logger.info(f"Starting automated robustness testing for "
                   f"{len(hypothesis_models)} hypotheses")
        
        all_results = {}
        
        for hypothesis_name, model in hypothesis_models.items():
            try:
                results = self.run_hypothesis_robustness(
                    data, model, hypothesis_name, **kwargs
                )
                all_results[hypothesis_name] = results
                
            except Exception as e:
                logger.error(f"Failed robustness testing for {hypothesis_name}: {e}")
                # Create failure result
                all_results[hypothesis_name] = self._create_failure_result(
                    hypothesis_name, str(e)
                )
                
        # Generate cross-hypothesis comparison
        self._generate_cross_hypothesis_report(all_results)
        
        return all_results
        
    def _prepare_test_kwargs(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """Prepare keyword arguments for robustness testing."""
        test_kwargs = {
            'n_bootstrap': self.config.n_bootstrap,
            'parallel': self.config.parallel,
            'max_workers': self.config.max_workers,
            'progress_bar': True,
            'cluster_var': kwargs.get('cluster_var', 'market_id'),
            'baseline_zones': self.config.baseline_zones or self._detect_currency_zones(data),
            'placebo_outcomes': self.config.placebo_outcomes,
        }
        
        # Add custom analytical choices
        test_kwargs['custom_choices'] = self._get_yemen_analytical_choices()
        
        # Merge with user-provided kwargs
        test_kwargs.update(kwargs)
        
        return test_kwargs
        
    def _detect_currency_zones(self, data: pd.DataFrame) -> Dict[str, str]:
        """Auto-detect currency zones if not provided."""
        if 'currency_zone' in data.columns:
            # Extract zone mapping from data
            zone_mapping = data.groupby('market_id')['currency_zone'].first().to_dict()
            return zone_mapping
        else:
            # Default simple north/south split
            markets = data['market_id'].unique() if 'market_id' in data.columns else []
            return {market: 'north' if i < len(markets)//2 else 'south' 
                   for i, market in enumerate(markets)}
                   
    def _get_yemen_analytical_choices(self) -> List[Dict[str, Any]]:
        """Get Yemen-specific analytical choices for specification curve."""
        return [
            {
                'name': 'exchange_rate_volatility',
                'options': ['daily', 'weekly', 'monthly', 'quarterly'],
                'description': 'Exchange rate volatility measurement',
                'default': 'monthly'
            },
            {
                'name': 'conflict_lag_structure',
                'options': ['contemporaneous', 'lag1', 'lag2', 'lag3', 'distributed'],
                'description': 'Conflict variable lag structure',
                'default': 'lag1'
            },
            {
                'name': 'food_security_controls',
                'options': ['none', 'ipc_phase', 'food_gap', 'malnutrition', 'all'],
                'description': 'Food security control variables',
                'default': 'ipc_phase'
            },
            {
                'name': 'trade_route_controls',
                'options': ['none', 'port_access', 'road_quality', 'checkpoints', 'all'],
                'description': 'Trade route control variables', 
                'default': 'port_access'
            }
        ]
        
    def _validate_results(self, results: RobustTestResults, 
                         hypothesis_name: str) -> Dict[str, Any]:
        """Validate results against World Bank publication standards."""
        validation = {
            'overall_score': results.robustness_score,
            'meets_standards': True,
            'warnings': [],
            'recommendations': []
        }
        
        # Check robustness score threshold
        if results.robustness_score < self.config.min_robustness_score:
            validation['meets_standards'] = False
            validation['warnings'].append(
                f"Robustness score {results.robustness_score:.2f} below "
                f"threshold {self.config.min_robustness_score}"
            )
            validation['recommendations'].append(
                "Consider additional sensitivity tests or model refinements"
            )
            
        # Check specification curve results
        if results.specification_curve:
            prop_sig = results.specification_curve.get('prop_significant', 0)
            if prop_sig < self.config.min_spec_significance:
                validation['warnings'].append(
                    f"Only {prop_sig*100:.1f}% of specifications significant"
                )
                validation['recommendations'].append(
                    "Investigate specification sensitivity - effect may be fragile"
                )
                
        # Check bootstrap convergence
        if results.bootstrap_results:
            conv_rate = results.bootstrap_results.get('convergence_rate', 0)
            if conv_rate < self.config.min_bootstrap_convergence:
                validation['warnings'].append(
                    f"Bootstrap convergence rate {conv_rate:.2f} below threshold"
                )
                validation['recommendations'].append(
                    "Increase bootstrap iterations or check model stability"
                )
                
        # Yemen-specific validation
        validation.update(self._validate_yemen_specific(results))
        
        return validation
        
    def _validate_yemen_specific(self, results: RobustTestResults) -> Dict[str, Any]:
        """Validate Yemen-specific robustness requirements."""
        yemen_validation = {
            'currency_zone_robust': True,
            'conflict_robust': True,
            'missing_data_robust': True
        }
        
        # Check if Yemen-specific tests were run
        if not hasattr(results, 'yemen_results'):
            yemen_validation['currency_zone_robust'] = False
            yemen_validation['conflict_robust'] = False
            
        return yemen_validation
        
    def _generate_automated_report(self, results: RobustTestResults,
                                 hypothesis_name: str):
        """Generate automated robustness report."""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = self.output_dir / f"{hypothesis_name}_automated_report_{timestamp}.json"
        
        # Create comprehensive report
        report = {
            'metadata': {
                'hypothesis': hypothesis_name,
                'timestamp': timestamp,
                'pipeline_version': '2.0',
                'config': asdict(self.config)
            },
            'summary': {
                'robustness_score': results.robustness_score,
                'overall_assessment': results.overall_assessment,
                'meets_standards': getattr(results, 'validation_report', {}).get('meets_standards', False),
                'execution_time': getattr(results, 'execution_time', 0)
            },
            'detailed_results': {
                'main_results': results.main_results,
                'specification_curve': results.specification_curve,
                'bootstrap_results': results.bootstrap_results,
                'sensitivity_results': results.sensitivity_results,
                'subsample_results': results.subsample_results
            },
            'validation_report': getattr(results, 'validation_report', {}),
            'visualizations': results.visualizations
        }
        
        # Save report
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
            
        logger.info(f"Automated report saved to {report_path}")
        
    def _generate_cross_hypothesis_report(self, all_results: Dict[str, RobustTestResults]):
        """Generate cross-hypothesis comparison report."""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = self.output_dir / f"cross_hypothesis_report_{timestamp}.json"
        
        # Summarize across hypotheses
        summary = {
            'metadata': {
                'timestamp': timestamp,
                'n_hypotheses': len(all_results),
                'pipeline_version': '2.0'
            },
            'aggregate_statistics': {
                'mean_robustness_score': np.mean([r.robustness_score for r in all_results.values()]),
                'robust_hypotheses': sum(1 for r in all_results.values() 
                                       if r.robustness_score >= self.config.min_robustness_score),
                'fragile_hypotheses': [name for name, r in all_results.items()
                                     if r.robustness_score < self.config.min_robustness_score]
            },
            'individual_results': {
                name: {
                    'robustness_score': result.robustness_score,
                    'assessment': result.overall_assessment,
                    'meets_standards': getattr(result, 'validation_report', {}).get('meets_standards', False)
                }
                for name, result in all_results.items()
            }
        }
        
        # Save cross-hypothesis report
        with open(report_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
            
        logger.info(f"Cross-hypothesis report saved to {report_path}")
        
    def _create_failure_result(self, hypothesis_name: str, error_msg: str) -> RobustTestResults:
        """Create failure result for failed hypothesis."""
        return RobustTestResults(
            main_results={'error': error_msg},
            specification_curve={},
            bootstrap_results={},
            sensitivity_results=[],
            subsample_results={},
            overall_assessment=f"FAILED: {error_msg}",
            robustness_score=0.0,
            visualizations={}
        )
        
    def export_publication_tables(self, results: Dict[str, RobustTestResults],
                                output_format: str = 'latex') -> str:
        """
        Export publication-ready tables of robustness results.
        
        Args:
            results: Dictionary of robustness results
            output_format: 'latex', 'excel', or 'csv'
            
        Returns:
            Path to exported file
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if output_format == 'latex':
            output_path = self.output_dir / f"robustness_tables_{timestamp}.tex"
            self._create_latex_tables(results, output_path)
        elif output_format == 'excel':
            output_path = self.output_dir / f"robustness_tables_{timestamp}.xlsx"
            self._create_excel_tables(results, output_path)
        else:
            output_path = self.output_dir / f"robustness_tables_{timestamp}.csv"
            self._create_csv_tables(results, output_path)
            
        return str(output_path)
        
    def _create_latex_tables(self, results: Dict[str, RobustTestResults], 
                           output_path: Path):
        """Create LaTeX tables for publication."""
        with open(output_path, 'w') as f:
            f.write("% Robustness Analysis Tables - Yemen Market Integration\n")
            f.write("% Generated by Automated Robustness Pipeline\n\n")
            
            # Main results table
            f.write("\\begin{table}[htbp]\n")
            f.write("\\centering\n")
            f.write("\\caption{Robustness Analysis Summary}\n")
            f.write("\\label{tab:robustness_summary}\n")
            f.write("\\begin{tabular}{lcccc}\n")
            f.write("\\hline\\hline\n")
            f.write("Hypothesis & Robustness Score & Main Effect & Bootstrap CI & Assessment \\\\\n")
            f.write("\\hline\n")
            
            for name, result in results.items():
                main_coef = result.main_results.get('coefficient', 0)
                boot_ci = result.bootstrap_results
                ci_str = f"[{boot_ci.get('ci_lower', 0):.3f}, {boot_ci.get('ci_upper', 0):.3f}]" if boot_ci else "N/A"
                assessment = "Robust" if result.robustness_score >= 0.7 else "Fragile"
                
                f.write(f"{name.replace('_', '\\_')} & {result.robustness_score:.2f} & "
                       f"{main_coef:.3f} & {ci_str} & {assessment} \\\\\n")
                
            f.write("\\hline\\hline\n")
            f.write("\\end{tabular}\n")
            f.write("\\end{table}\n\n")
            
        logger.info(f"LaTeX tables saved to {output_path}")
        
    def _create_excel_tables(self, results: Dict[str, RobustTestResults], 
                           output_path: Path):
        """Create Excel tables for publication."""
        import pandas as pd
        
        # Create summary dataframe
        summary_data = []
        for name, result in results.items():
            summary_data.append({
                'Hypothesis': name,
                'Robustness_Score': result.robustness_score,
                'Main_Effect': result.main_results.get('coefficient', 0),
                'Bootstrap_SE': result.bootstrap_results.get('bootstrap_se', 0),
                'CI_Lower': result.bootstrap_results.get('ci_lower', 0),
                'CI_Upper': result.bootstrap_results.get('ci_upper', 0),
                'Assessment': result.overall_assessment,
                'N_Specifications': result.specification_curve.get('n_specifications', 0),
                'Prop_Significant': result.specification_curve.get('prop_significant', 0)
            })
            
        summary_df = pd.DataFrame(summary_data)
        
        # Save to Excel with multiple sheets
        with pd.ExcelWriter(output_path) as writer:
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
            
            # Add detailed results for each hypothesis
            for name, result in results.items():
                if result.specification_curve.get('coefficients'):
                    spec_df = pd.DataFrame({
                        'Specification': range(len(result.specification_curve['coefficients'])),
                        'Coefficient': result.specification_curve['coefficients'],
                        'Significant': result.specification_curve.get('significant', [])
                    })
                    sheet_name = name[:31]  # Excel sheet name limit
                    spec_df.to_excel(writer, sheet_name=sheet_name, index=False)
                    
        logger.info(f"Excel tables saved to {output_path}")
        
    def _create_csv_tables(self, results: Dict[str, RobustTestResults], 
                         output_path: Path):
        """Create CSV tables for publication."""
        import pandas as pd
        
        # Create summary dataframe
        summary_data = []
        for name, result in results.items():
            summary_data.append({
                'hypothesis': name,
                'robustness_score': result.robustness_score,
                'main_effect': result.main_results.get('coefficient', 0),
                'bootstrap_se': result.bootstrap_results.get('bootstrap_se', 0),
                'ci_lower': result.bootstrap_results.get('ci_lower', 0),
                'ci_upper': result.bootstrap_results.get('ci_upper', 0),
                'assessment': result.overall_assessment,
                'n_specifications': result.specification_curve.get('n_specifications', 0),
                'prop_significant': result.specification_curve.get('prop_significant', 0)
            })
            
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv(output_path, index=False)
        
        logger.info(f"CSV tables saved to {output_path}")


def create_default_pipeline() -> AutomatedRobustnessPipeline:
    """Create pipeline with default Yemen-specific configuration."""
    config = RobustnessPipelineConfig(
        n_bootstrap=1000,
        n_specifications=2000,
        parallel=True,
        currency_zone_buffers=[10, 25, 50],
        placebo_outcomes=['temperature', 'rainfall', 'population'],
        min_robustness_score=0.7,
        output_dir="results/robustness"
    )
    
    return AutomatedRobustnessPipeline(config)