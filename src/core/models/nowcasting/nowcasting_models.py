"""
Nowcasting models for real-time market predictions in conflict settings.

This module implements advanced nowcasting techniques specifically designed for
Yemen's multi-currency environment, providing early warning capabilities for
humanitarian interventions.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
import warnings
from datetime import datetime, timedelta

# Core statistical libraries
from statsmodels.tsa.statespace.dynamic_factor import DynamicFactor
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller, acf, pacf

# Machine learning libraries
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_percentage_error

# For uncertainty quantification
from scipy import stats
from scipy.stats import norm

# Optional advanced ML libraries (graceful fallback if not available)
try:
    import xgboost as xgb
    HAS_XGBOOST = True
except ImportError:
    HAS_XGBOOST = False
    
try:
    import lightgbm as lgb
    HAS_LIGHTGBM = True
except ImportError:
    HAS_LIGHTGBM = False


@dataclass
class NowcastResult:
    """Container for nowcast results with uncertainty quantification."""
    point_forecast: pd.Series
    prediction_intervals: Dict[float, pd.DataFrame]  # confidence level -> (lower, upper)
    forecast_horizon: int
    method: str
    currency_zone: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    

@dataclass
class EarlyWarningSignal:
    """Early warning signal for humanitarian intervention."""
    market_id: str
    commodity: str
    signal_type: str  # 'price_spike', 'shortage_risk', 'integration_breakdown'
    severity: str  # 'low', 'medium', 'high', 'critical'
    probability: float
    predicted_date: datetime
    confidence_interval: Tuple[float, float]
    recommended_action: str
    supporting_evidence: Dict[str, Any]


class NowcastingModel(ABC):
    """Abstract base class for nowcasting models."""
    
    def __init__(self, 
                 forecast_horizon: int = 1,
                 confidence_levels: List[float] = None):
        """
        Initialize nowcasting model.
        
        Parameters
        ----------
        forecast_horizon : int
            Number of periods ahead to forecast
        confidence_levels : List[float]
            Confidence levels for prediction intervals (default: [0.8, 0.95])
        """
        self.forecast_horizon = forecast_horizon
        self.confidence_levels = confidence_levels or [0.8, 0.95]
        self.is_fitted = False
        self.training_data = None
        
    @abstractmethod
    def fit(self, data: pd.DataFrame, **kwargs) -> 'NowcastingModel':
        """Fit the nowcasting model."""
        pass
        
    @abstractmethod
    def predict(self, 
                exog_future: Optional[pd.DataFrame] = None,
                **kwargs) -> NowcastResult:
        """Generate nowcast predictions."""
        pass
        
    def validate_currency_conversion(self, data: pd.DataFrame) -> None:
        """Ensure all prices are in USD before nowcasting."""
        if 'usd_price' not in data.columns:
            raise ValueError(
                "CRITICAL: All prices must be converted to USD before nowcasting. "
                "Column 'usd_price' not found. Use MethodologyValidator first."
            )
        
        # Check for any remaining non-USD prices
        if 'currency' in data.columns:
            non_usd = data[data['currency'] != 'USD']
            if len(non_usd) > 0:
                raise ValueError(
                    f"Found {len(non_usd)} observations not in USD. "
                    "All prices must be converted before nowcasting."
                )


class DynamicFactorNowcast(NowcastingModel):
    """
    Dynamic Factor Model for nowcasting with panel data.
    
    Particularly effective for:
    - High-dimensional panel data with many markets/commodities
    - Extracting common trends from noisy individual series
    - Handling missing data patterns common in conflict settings
    """
    
    def __init__(self,
                 n_factors: int = 3,
                 factor_order: int = 1,
                 error_order: int = 1,
                 forecast_horizon: int = 1,
                 confidence_levels: List[float] = None):
        """
        Initialize Dynamic Factor nowcasting model.
        
        Parameters
        ----------
        n_factors : int
            Number of common factors to extract
        factor_order : int
            AR order for factor dynamics
        error_order : int
            AR order for idiosyncratic errors
        """
        super().__init__(forecast_horizon, confidence_levels)
        self.n_factors = n_factors
        self.factor_order = factor_order
        self.error_order = error_order
        self.model = None
        self.results = None
        self.scaler = StandardScaler()
        
    def fit(self, data: pd.DataFrame, **kwargs) -> 'DynamicFactorNowcast':
        """
        Fit Dynamic Factor Model.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data with MultiIndex (market_id, date) and price columns
        """
        self.validate_currency_conversion(data)
        
        # Prepare data for DFM
        pivot_data = self._prepare_panel_for_dfm(data)
        
        # Standardize data
        data_scaled = self.scaler.fit_transform(pivot_data.fillna(method='ffill'))
        pivot_scaled = pd.DataFrame(
            data_scaled,
            index=pivot_data.index,
            columns=pivot_data.columns
        )
        
        # Initialize and fit DFM
        self.model = DynamicFactor(
            pivot_scaled,
            k_factors=self.n_factors,
            factor_order=self.factor_order,
            error_order=self.error_order
        )
        
        self.results = self.model.fit(disp=False)
        self.training_data = pivot_data
        self.is_fitted = True
        
        return self
        
    def predict(self,
                exog_future: Optional[pd.DataFrame] = None,
                **kwargs) -> NowcastResult:
        """Generate nowcast using fitted DFM."""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
            
        # Get in-sample predictions for last period
        in_sample_pred = self.results.fittedvalues.iloc[-1]
        
        # Generate out-of-sample forecasts
        forecast = self.results.forecast(steps=self.forecast_horizon)
        
        # Transform back to original scale
        forecast_original = self.scaler.inverse_transform(forecast)
        
        # Calculate prediction intervals
        forecast_errors = self.results.resid.std(axis=0)
        prediction_intervals = {}
        
        for alpha in self.confidence_levels:
            z_score = norm.ppf((1 + alpha) / 2)
            lower = forecast_original - z_score * forecast_errors.values
            upper = forecast_original + z_score * forecast_errors.values
            
            prediction_intervals[alpha] = pd.DataFrame({
                'lower': lower.mean(axis=1),
                'upper': upper.mean(axis=1)
            })
            
        # Create point forecast
        point_forecast = pd.Series(
            forecast_original.mean(axis=1),
            index=pd.date_range(
                start=self.training_data.index[-1] + pd.Timedelta(days=30),
                periods=self.forecast_horizon,
                freq='M'
            )
        )
        
        return NowcastResult(
            point_forecast=point_forecast,
            prediction_intervals=prediction_intervals,
            forecast_horizon=self.forecast_horizon,
            method='DynamicFactorModel',
            metadata={
                'n_factors': self.n_factors,
                'factor_loadings': self.results.coefficients_of_determination,
                'model_diagnostics': {
                    'log_likelihood': self.results.llf,
                    'aic': self.results.aic,
                    'bic': self.results.bic
                }
            }
        )
        
    def _prepare_panel_for_dfm(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare panel data for Dynamic Factor Model."""
        # Pivot to wide format: dates as rows, markets as columns
        if isinstance(data.index, pd.MultiIndex):
            pivot = data.reset_index().pivot(
                index='date',
                columns='market_id',
                values='usd_price'
            )
        else:
            pivot = data.pivot(
                index='date',
                columns='market_id',
                values='usd_price'
            )
            
        # Handle missing data pattern
        # DFM can handle missing data, but we'll forward fill short gaps
        pivot = pivot.fillna(method='ffill', limit=2)
        
        return pivot


class SARIMAXNowcast(NowcastingModel):
    """
    SARIMAX model for univariate time series nowcasting with seasonality.
    
    Effective for:
    - Individual market-commodity pairs
    - Incorporating exogenous variables (conflict events, exchange rates)
    - Capturing seasonal patterns in agricultural commodities
    """
    
    def __init__(self,
                 order: Tuple[int, int, int] = (1, 1, 1),
                 seasonal_order: Tuple[int, int, int, int] = (1, 1, 1, 12),
                 forecast_horizon: int = 1,
                 confidence_levels: List[float] = None,
                 auto_order: bool = True):
        """
        Initialize SARIMAX nowcasting model.
        
        Parameters
        ----------
        order : tuple
            (p, d, q) order for ARIMA component
        seasonal_order : tuple
            (P, D, Q, s) order for seasonal component
        auto_order : bool
            Whether to automatically select orders using information criteria
        """
        super().__init__(forecast_horizon, confidence_levels)
        self.order = order
        self.seasonal_order = seasonal_order
        self.auto_order = auto_order
        self.model = None
        self.results = None
        
    def fit(self, 
            data: pd.DataFrame,
            exog: Optional[pd.DataFrame] = None,
            **kwargs) -> 'SARIMAXNowcast':
        """
        Fit SARIMAX model.
        
        Parameters
        ----------
        data : pd.DataFrame
            Time series data with 'usd_price' column
        exog : pd.DataFrame, optional
            Exogenous variables (e.g., conflict intensity, exchange rates)
        """
        self.validate_currency_conversion(data)
        
        # Extract time series
        if 'usd_price' in data.columns:
            y = data['usd_price']
        else:
            raise ValueError("Data must contain 'usd_price' column")
            
        # Auto-select orders if requested
        if self.auto_order:
            self.order, self.seasonal_order = self._auto_select_orders(y)
            
        # Fit SARIMAX model
        self.model = SARIMAX(
            y,
            order=self.order,
            seasonal_order=self.seasonal_order,
            exog=exog,
            enforce_stationarity=False,
            enforce_invertibility=False
        )
        
        self.results = self.model.fit(disp=False)
        self.training_data = data
        self.training_exog = exog
        self.is_fitted = True
        
        return self
        
    def predict(self,
                exog_future: Optional[pd.DataFrame] = None,
                **kwargs) -> NowcastResult:
        """Generate SARIMAX nowcast."""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
            
        # Generate forecast
        forecast_result = self.results.forecast(
            steps=self.forecast_horizon,
            exog=exog_future
        )
        
        # Get prediction intervals
        forecast_df = self.results.get_forecast(
            steps=self.forecast_horizon,
            exog=exog_future
        ).summary_frame()
        
        # Extract prediction intervals
        prediction_intervals = {}
        for alpha in self.confidence_levels:
            alpha_str = f'{alpha:.0%}'
            if f'mean_ci_lower' in forecast_df.columns:
                # Standard statsmodels output
                prediction_intervals[alpha] = pd.DataFrame({
                    'lower': forecast_df['mean_ci_lower'],
                    'upper': forecast_df['mean_ci_upper']
                })
            else:
                # Calculate manually if needed
                se = forecast_df.get('mean_se', np.sqrt(self.results.scale))
                z_score = norm.ppf((1 + alpha) / 2)
                prediction_intervals[alpha] = pd.DataFrame({
                    'lower': forecast_df['mean'] - z_score * se,
                    'upper': forecast_df['mean'] + z_score * se
                })
                
        # Create point forecast
        point_forecast = forecast_df['mean']
        
        # Calculate performance metrics on training data
        in_sample_pred = self.results.fittedvalues
        residuals = self.training_data['usd_price'] - in_sample_pred
        
        return NowcastResult(
            point_forecast=point_forecast,
            prediction_intervals=prediction_intervals,
            forecast_horizon=self.forecast_horizon,
            method='SARIMAX',
            metadata={
                'order': self.order,
                'seasonal_order': self.seasonal_order,
                'model_diagnostics': {
                    'aic': self.results.aic,
                    'bic': self.results.bic,
                    'ljung_box': self.results.test_serial_correlation('ljungbox')[0][-1]
                }
            },
            performance_metrics={
                'rmse': np.sqrt(mean_squared_error(self.training_data['usd_price'], in_sample_pred)),
                'mape': mean_absolute_percentage_error(self.training_data['usd_price'], in_sample_pred)
            }
        )
        
    def _auto_select_orders(self, y: pd.Series) -> Tuple[Tuple[int, int, int], Tuple[int, int, int, int]]:
        """Automatically select ARIMA orders using information criteria."""
        # Test for stationarity
        adf_result = adfuller(y.dropna())
        d = 0 if adf_result[1] < 0.05 else 1
        
        # Simple order selection (could be enhanced with grid search)
        # For now, use reasonable defaults based on ACF/PACF
        acf_vals = acf(y.dropna(), nlags=20)
        pacf_vals = pacf(y.dropna(), nlags=20)
        
        # Simplified selection
        p = 2  # AR order
        q = 1  # MA order
        
        # Seasonal detection
        if len(y) > 24:  # Need at least 2 years for seasonal
            decomposition = seasonal_decompose(y.dropna(), model='additive', period=12)
            seasonal_strength = decomposition.seasonal.std() / y.std()
            if seasonal_strength > 0.1:
                seasonal_order = (1, 1, 1, 12)
            else:
                seasonal_order = (0, 0, 0, 0)
        else:
            seasonal_order = (0, 0, 0, 0)
            
        return (p, d, q), seasonal_order


class MachineLearningNowcast(NowcastingModel):
    """
    Machine learning-based nowcasting using ensemble methods.
    
    Effective for:
    - Non-linear relationships
    - High-dimensional feature spaces
    - Capturing complex interaction effects
    """
    
    def __init__(self,
                 model_type: str = 'random_forest',
                 forecast_horizon: int = 1,
                 confidence_levels: List[float] = None,
                 **model_params):
        """
        Initialize ML nowcasting model.
        
        Parameters
        ----------
        model_type : str
            Type of ML model: 'random_forest', 'gradient_boosting', 'xgboost', 'lightgbm'
        model_params : dict
            Additional parameters for the ML model
        """
        super().__init__(forecast_horizon, confidence_levels)
        self.model_type = model_type
        self.model_params = model_params
        self.model = None
        self.feature_names = None
        self.scaler = StandardScaler()
        
    def fit(self, 
            data: pd.DataFrame,
            feature_cols: Optional[List[str]] = None,
            **kwargs) -> 'MachineLearningNowcast':
        """
        Fit ML model with engineered features.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data with price and feature columns
        feature_cols : List[str]
            Additional feature columns beyond engineered ones
        """
        self.validate_currency_conversion(data)
        
        # Engineer features
        features_df = self._engineer_features(data, feature_cols)
        
        # Prepare training data
        X, y = self._prepare_ml_data(features_df, data['usd_price'])
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Initialize model
        if self.model_type == 'random_forest':
            self.model = RandomForestRegressor(
                n_estimators=self.model_params.get('n_estimators', 100),
                max_depth=self.model_params.get('max_depth', 10),
                random_state=42,
                n_jobs=-1
            )
        elif self.model_type == 'gradient_boosting':
            self.model = GradientBoostingRegressor(
                n_estimators=self.model_params.get('n_estimators', 100),
                learning_rate=self.model_params.get('learning_rate', 0.1),
                max_depth=self.model_params.get('max_depth', 5),
                random_state=42
            )
        elif self.model_type == 'xgboost' and HAS_XGBOOST:
            self.model = xgb.XGBRegressor(
                n_estimators=self.model_params.get('n_estimators', 100),
                learning_rate=self.model_params.get('learning_rate', 0.1),
                max_depth=self.model_params.get('max_depth', 5),
                random_state=42
            )
        elif self.model_type == 'lightgbm' and HAS_LIGHTGBM:
            self.model = lgb.LGBMRegressor(
                n_estimators=self.model_params.get('n_estimators', 100),
                learning_rate=self.model_params.get('learning_rate', 0.1),
                num_leaves=self.model_params.get('num_leaves', 31),
                random_state=42
            )
        else:
            raise ValueError(f"Unsupported model type: {self.model_type}")
            
        # Fit model
        self.model.fit(X_scaled, y)
        self.feature_names = X.columns.tolist()
        self.training_data = data
        self.training_features = features_df
        self.is_fitted = True
        
        return self
        
    def predict(self,
                future_features: Optional[pd.DataFrame] = None,
                **kwargs) -> NowcastResult:
        """Generate ML-based nowcast."""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
            
        # Prepare future features
        if future_features is None:
            # Use last known features and project forward
            future_features = self._project_features_forward()
            
        # Scale features
        X_future = future_features[self.feature_names]
        X_future_scaled = self.scaler.transform(X_future)
        
        # Generate predictions
        predictions = self.model.predict(X_future_scaled)
        
        # Estimate prediction intervals using out-of-bag predictions (for RF)
        # or through bootstrap
        prediction_intervals = self._estimate_prediction_intervals(
            X_future_scaled, predictions
        )
        
        # Create point forecast
        point_forecast = pd.Series(
            predictions,
            index=pd.date_range(
                start=self.training_data.index[-1] + pd.Timedelta(days=30),
                periods=self.forecast_horizon,
                freq='M'
            )
        )
        
        # Feature importance
        if hasattr(self.model, 'feature_importances_'):
            feature_importance = pd.Series(
                self.model.feature_importances_,
                index=self.feature_names
            ).sort_values(ascending=False)
        else:
            feature_importance = None
            
        return NowcastResult(
            point_forecast=point_forecast,
            prediction_intervals=prediction_intervals,
            forecast_horizon=self.forecast_horizon,
            method=f'ML_{self.model_type}',
            metadata={
                'feature_importance': feature_importance.to_dict() if feature_importance is not None else None,
                'n_features': len(self.feature_names),
                'model_params': self.model_params
            }
        )
        
    def _engineer_features(self, 
                          data: pd.DataFrame,
                          additional_features: Optional[List[str]] = None) -> pd.DataFrame:
        """Engineer features for ML model."""
        features = pd.DataFrame(index=data.index)
        
        # Lag features
        for lag in [1, 2, 3, 6, 12]:
            features[f'price_lag_{lag}'] = data['usd_price'].shift(lag)
            
        # Rolling statistics
        for window in [3, 6, 12]:
            features[f'price_ma_{window}'] = data['usd_price'].rolling(window).mean()
            features[f'price_std_{window}'] = data['usd_price'].rolling(window).std()
            
        # Price changes
        features['price_change_1m'] = data['usd_price'].pct_change(1)
        features['price_change_3m'] = data['usd_price'].pct_change(3)
        features['price_change_12m'] = data['usd_price'].pct_change(12)
        
        # Seasonal features (if date available)
        if 'date' in data.index.names or hasattr(data.index, 'month'):
            if 'date' in data.index.names:
                dates = data.index.get_level_values('date')
            else:
                dates = data.index
            features['month'] = dates.month
            features['quarter'] = dates.quarter
            features['is_ramadan'] = self._is_ramadan_period(dates)
            
        # Add currency zone features if available
        if 'currency_zone' in data.columns:
            zone_dummies = pd.get_dummies(data['currency_zone'], prefix='zone')
            features = pd.concat([features, zone_dummies], axis=1)
            
        # Add conflict features if available
        if 'conflict_intensity' in data.columns:
            features['conflict_intensity'] = data['conflict_intensity']
            features['conflict_ma_3m'] = data['conflict_intensity'].rolling(3).mean()
            
        # Add additional features if provided
        if additional_features:
            for feat in additional_features:
                if feat in data.columns:
                    features[feat] = data[feat]
                    
        # Drop rows with NaN (from lagging)
        features = features.dropna()
        
        return features
        
    def _prepare_ml_data(self, 
                        features: pd.DataFrame,
                        target: pd.Series) -> Tuple[pd.DataFrame, pd.Series]:
        """Prepare data for ML training."""
        # Align features and target
        common_index = features.index.intersection(target.index)
        
        X = features.loc[common_index]
        y = target.loc[common_index]
        
        return X, y
        
    def _estimate_prediction_intervals(self,
                                     X: np.ndarray,
                                     predictions: np.ndarray) -> Dict[float, pd.DataFrame]:
        """Estimate prediction intervals for ML models."""
        prediction_intervals = {}
        
        if self.model_type == 'random_forest' and hasattr(self.model, 'estimators_'):
            # Use individual tree predictions
            tree_predictions = np.array([
                tree.predict(X) for tree in self.model.estimators_
            ])
            
            for alpha in self.confidence_levels:
                lower_q = (1 - alpha) / 2
                upper_q = (1 + alpha) / 2
                
                prediction_intervals[alpha] = pd.DataFrame({
                    'lower': np.quantile(tree_predictions, lower_q, axis=0),
                    'upper': np.quantile(tree_predictions, upper_q, axis=0)
                })
        else:
            # Use residual-based intervals as fallback
            # This is simplified - in practice would use proper bootstrap
            if hasattr(self, 'training_residuals_'):
                residual_std = self.training_residuals_.std()
            else:
                # Rough estimate
                residual_std = 0.1 * np.mean(predictions)
                
            for alpha in self.confidence_levels:
                z_score = norm.ppf((1 + alpha) / 2)
                prediction_intervals[alpha] = pd.DataFrame({
                    'lower': predictions - z_score * residual_std,
                    'upper': predictions + z_score * residual_std
                })
                
        return prediction_intervals
        
    def _project_features_forward(self) -> pd.DataFrame:
        """Project features forward for future predictions."""
        # Simple projection - in practice would be more sophisticated
        last_features = self.training_features.iloc[-1:].copy()
        
        future_features = []
        for h in range(self.forecast_horizon):
            # Update time-based features
            if 'month' in last_features.columns:
                last_features['month'] = (last_features['month'] + 1) % 12 or 12
            if 'quarter' in last_features.columns:
                last_features['quarter'] = ((last_features['month'] - 1) // 3) + 1
                
            future_features.append(last_features.copy())
            
        return pd.concat(future_features, ignore_index=True)
        
    def _is_ramadan_period(self, dates: pd.DatetimeIndex) -> np.ndarray:
        """Identify Ramadan periods (simplified - would use hijri calendar)."""
        # Simplified approximation - in practice would use proper Islamic calendar
        ramadan_months = {
            2019: (5, 6),   # May-June 2019
            2020: (4, 5),   # April-May 2020
            2021: (4, 5),   # April-May 2021
            2022: (4, 5),   # April 2022
            2023: (3, 4),   # March-April 2023
            2024: (3, 4),   # March-April 2024
        }
        
        is_ramadan = np.zeros(len(dates), dtype=bool)
        for i, date in enumerate(dates):
            if date.year in ramadan_months:
                start_month, end_month = ramadan_months[date.year]
                if start_month <= date.month <= end_month:
                    is_ramadan[i] = True
                    
        return is_ramadan


class EnsembleNowcast(NowcastingModel):
    """
    Ensemble nowcasting combining multiple methods for robustness.
    
    Particularly important in conflict settings where no single model
    may capture all dynamics.
    """
    
    def __init__(self,
                 models: Optional[List[NowcastingModel]] = None,
                 weights: Optional[Dict[str, float]] = None,
                 weight_method: str = 'equal',
                 forecast_horizon: int = 1,
                 confidence_levels: List[float] = None):
        """
        Initialize ensemble nowcasting model.
        
        Parameters
        ----------
        models : List[NowcastingModel]
            List of models to ensemble
        weights : Dict[str, float]
            Model weights (if weight_method='manual')
        weight_method : str
            Method for weighting: 'equal', 'performance', 'manual'
        """
        super().__init__(forecast_horizon, confidence_levels)
        self.models = models or self._get_default_models()
        self.weights = weights or {}
        self.weight_method = weight_method
        self.model_results = {}
        
    def _get_default_models(self) -> List[NowcastingModel]:
        """Get default ensemble of models."""
        return [
            DynamicFactorNowcast(n_factors=3, forecast_horizon=self.forecast_horizon),
            SARIMAXNowcast(forecast_horizon=self.forecast_horizon),
            MachineLearningNowcast(model_type='random_forest', forecast_horizon=self.forecast_horizon),
            MachineLearningNowcast(model_type='gradient_boosting', forecast_horizon=self.forecast_horizon)
        ]
        
    def fit(self, data: pd.DataFrame, **kwargs) -> 'EnsembleNowcast':
        """Fit all models in the ensemble."""
        self.validate_currency_conversion(data)
        
        # Fit each model
        for model in self.models:
            try:
                model.fit(data, **kwargs)
                self.model_results[model.__class__.__name__] = {
                    'fitted': True,
                    'error': None
                }
            except Exception as e:
                warnings.warn(f"Failed to fit {model.__class__.__name__}: {str(e)}")
                self.model_results[model.__class__.__name__] = {
                    'fitted': False,
                    'error': str(e)
                }
                
        # Calculate weights if using performance-based weighting
        if self.weight_method == 'performance':
            self._calculate_performance_weights(data)
        elif self.weight_method == 'equal':
            n_fitted = sum(1 for m in self.models if self.model_results[m.__class__.__name__]['fitted'])
            for model in self.models:
                if self.model_results[model.__class__.__name__]['fitted']:
                    self.weights[model.__class__.__name__] = 1.0 / n_fitted
                    
        self.training_data = data
        self.is_fitted = True
        
        return self
        
    def predict(self,
                exog_future: Optional[pd.DataFrame] = None,
                **kwargs) -> NowcastResult:
        """Generate ensemble nowcast."""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
            
        # Collect predictions from each model
        predictions = {}
        prediction_intervals_all = {}
        
        for model in self.models:
            model_name = model.__class__.__name__
            if self.model_results[model_name]['fitted']:
                try:
                    result = model.predict(exog_future=exog_future, **kwargs)
                    predictions[model_name] = result
                except Exception as e:
                    warnings.warn(f"Prediction failed for {model_name}: {str(e)}")
                    
        # Combine predictions
        if not predictions:
            raise ValueError("No models produced valid predictions")
            
        # Calculate weighted average for point forecast
        point_forecasts = []
        weights_used = []
        
        for model_name, result in predictions.items():
            weight = self.weights.get(model_name, 0)
            if weight > 0:
                point_forecasts.append(result.point_forecast * weight)
                weights_used.append(weight)
                
        # Normalize weights
        total_weight = sum(weights_used)
        ensemble_point_forecast = sum(point_forecasts) / total_weight
        
        # Combine prediction intervals (simplified - could use more sophisticated methods)
        ensemble_intervals = {}
        for alpha in self.confidence_levels:
            lower_bounds = []
            upper_bounds = []
            
            for model_name, result in predictions.items():
                weight = self.weights.get(model_name, 0)
                if weight > 0 and alpha in result.prediction_intervals:
                    lower_bounds.append(result.prediction_intervals[alpha]['lower'] * weight)
                    upper_bounds.append(result.prediction_intervals[alpha]['upper'] * weight)
                    
            ensemble_intervals[alpha] = pd.DataFrame({
                'lower': sum(lower_bounds) / total_weight,
                'upper': sum(upper_bounds) / total_weight
            })
            
        # Collect metadata
        metadata = {
            'models_used': list(predictions.keys()),
            'weights': self.weights,
            'weight_method': self.weight_method,
            'individual_results': {
                name: {
                    'point_forecast': result.point_forecast.tolist(),
                    'method': result.method
                }
                for name, result in predictions.items()
            }
        }
        
        return NowcastResult(
            point_forecast=ensemble_point_forecast,
            prediction_intervals=ensemble_intervals,
            forecast_horizon=self.forecast_horizon,
            method='Ensemble',
            metadata=metadata
        )
        
    def _calculate_performance_weights(self, data: pd.DataFrame) -> None:
        """Calculate weights based on out-of-sample performance."""
        # Use time series cross-validation
        tscv = TimeSeriesSplit(n_splits=3)
        
        model_scores = {}
        
        for train_idx, test_idx in tscv.split(data):
            train_data = data.iloc[train_idx]
            test_data = data.iloc[test_idx]
            
            for model in self.models:
                model_name = model.__class__.__name__
                if self.model_results[model_name]['fitted']:
                    try:
                        # Refit on training subset
                        model_cv = model.__class__(**model.__dict__)
                        model_cv.fit(train_data)
                        
                        # Predict on test set
                        pred = model_cv.predict()
                        
                        # Calculate error (simplified - would align properly)
                        error = mean_squared_error(
                            test_data['usd_price'].iloc[:len(pred.point_forecast)],
                            pred.point_forecast
                        )
                        
                        if model_name not in model_scores:
                            model_scores[model_name] = []
                        model_scores[model_name].append(error)
                        
                    except Exception:
                        pass
                        
        # Convert scores to weights (inverse of error)
        for model_name, scores in model_scores.items():
            avg_score = np.mean(scores)
            self.weights[model_name] = 1.0 / (avg_score + 1e-6)
            
        # Normalize weights
        total_weight = sum(self.weights.values())
        self.weights = {k: v/total_weight for k, v in self.weights.items()}


class EarlyWarningSystem:
    """
    Early warning system for humanitarian intervention based on nowcasts.
    
    Generates actionable alerts for:
    - Price spikes beyond sustainable thresholds
    - Market integration breakdowns
    - Supply shortage risks
    """
    
    def __init__(self,
                 nowcast_model: NowcastingModel,
                 thresholds: Optional[Dict[str, Dict[str, float]]] = None):
        """
        Initialize early warning system.
        
        Parameters
        ----------
        nowcast_model : NowcastingModel
            Fitted nowcasting model
        thresholds : Dict[str, Dict[str, float]]
            Warning thresholds by commodity and severity level
        """
        self.nowcast_model = nowcast_model
        self.thresholds = thresholds or self._get_default_thresholds()
        self.historical_volatility = {}
        
    def _get_default_thresholds(self) -> Dict[str, Dict[str, float]]:
        """Get default warning thresholds based on humanitarian standards."""
        return {
            'wheat_flour': {
                'low': 1.2,      # 20% increase
                'medium': 1.5,   # 50% increase
                'high': 2.0,     # 100% increase
                'critical': 3.0  # 200% increase
            },
            'rice': {
                'low': 1.2,
                'medium': 1.5,
                'high': 2.0,
                'critical': 3.0
            },
            'fuel': {
                'low': 1.15,     # Fuel more sensitive
                'medium': 1.3,
                'high': 1.5,
                'critical': 2.0
            },
            'default': {         # For other commodities
                'low': 1.25,
                'medium': 1.5,
                'high': 2.0,
                'critical': 2.5
            }
        }
        
    def generate_warnings(self,
                         nowcast_results: Dict[str, NowcastResult],
                         baseline_prices: pd.DataFrame,
                         market_metadata: Optional[pd.DataFrame] = None) -> List[EarlyWarningSignal]:
        """
        Generate early warning signals based on nowcast results.
        
        Parameters
        ----------
        nowcast_results : Dict[str, NowcastResult]
            Nowcast results by market-commodity pair
        baseline_prices : pd.DataFrame
            Baseline prices for comparison
        market_metadata : pd.DataFrame
            Additional market information (population, vulnerability, etc.)
        """
        warnings = []
        
        for key, nowcast in nowcast_results.items():
            market_id, commodity = key.split('_', 1)
            
            # Get baseline price
            baseline = baseline_prices.get(key, baseline_prices.get(commodity, None))
            if baseline is None:
                continue
                
            # Calculate price ratios
            price_ratios = nowcast.point_forecast / baseline
            
            # Get appropriate thresholds
            commodity_thresholds = self.thresholds.get(commodity, self.thresholds['default'])
            
            # Check each forecast period
            for i, (date, ratio) in enumerate(zip(nowcast.point_forecast.index, price_ratios)):
                # Determine severity
                severity = None
                for level in ['critical', 'high', 'medium', 'low']:
                    if ratio >= commodity_thresholds[level]:
                        severity = level
                        break
                        
                if severity:
                    # Calculate probability based on prediction intervals
                    prob = self._calculate_exceedance_probability(
                        nowcast, i, baseline * commodity_thresholds[severity]
                    )
                    
                    # Generate warning
                    warning = EarlyWarningSignal(
                        market_id=market_id,
                        commodity=commodity,
                        signal_type='price_spike',
                        severity=severity,
                        probability=prob,
                        predicted_date=date,
                        confidence_interval=(
                            nowcast.prediction_intervals[0.95]['lower'].iloc[i] / baseline,
                            nowcast.prediction_intervals[0.95]['upper'].iloc[i] / baseline
                        ),
                        recommended_action=self._get_recommended_action(severity, commodity),
                        supporting_evidence={
                            'baseline_price': baseline,
                            'predicted_price': nowcast.point_forecast.iloc[i],
                            'price_ratio': ratio,
                            'threshold': commodity_thresholds[severity],
                            'nowcast_method': nowcast.method
                        }
                    )
                    warnings.append(warning)
                    
        # Check for integration breakdown signals
        integration_warnings = self._check_integration_breakdown(nowcast_results)
        warnings.extend(integration_warnings)
        
        return warnings
        
    def _calculate_exceedance_probability(self,
                                        nowcast: NowcastResult,
                                        period_idx: int,
                                        threshold: float) -> float:
        """Calculate probability of exceeding threshold."""
        # Use prediction intervals to estimate probability
        # Simplified - assumes normal distribution
        
        point_forecast = nowcast.point_forecast.iloc[period_idx]
        
        # Get 95% prediction interval
        if 0.95 in nowcast.prediction_intervals:
            lower = nowcast.prediction_intervals[0.95]['lower'].iloc[period_idx]
            upper = nowcast.prediction_intervals[0.95]['upper'].iloc[period_idx]
            
            # Estimate standard deviation
            # For 95% CI: upper - lower = 2 * 1.96 * sigma
            sigma = (upper - lower) / (2 * 1.96)
            
            # Calculate exceedance probability
            z_score = (threshold - point_forecast) / sigma
            prob = 1 - norm.cdf(z_score)
            
            return min(max(prob, 0), 1)  # Ensure in [0, 1]
        else:
            # Fallback: use deterministic threshold
            return 1.0 if point_forecast >= threshold else 0.0
            
    def _get_recommended_action(self, severity: str, commodity: str) -> str:
        """Get recommended humanitarian action based on warning severity."""
        actions = {
            'low': {
                'default': "Monitor situation closely. Consider preventive market support.",
                'fuel': "Monitor fuel supply chains. Prepare contingency transport plans."
            },
            'medium': {
                'default': "Activate market monitoring. Prepare cash transfer adjustments.",
                'fuel': "Implement fuel voucher programs. Support alternative transport."
            },
            'high': {
                'default': "Increase cash transfer values. Consider direct food distribution.",
                'fuel': "Emergency fuel distribution. Subsidize public transport."
            },
            'critical': {
                'default': "Emergency food distribution required. Full humanitarian response.",
                'fuel': "Critical fuel shortage. Immediate intervention required."
            }
        }
        
        commodity_type = 'fuel' if 'fuel' in commodity.lower() else 'default'
        return actions.get(severity, {}).get(commodity_type, "Assess situation and respond accordingly.")
        
    def _check_integration_breakdown(self,
                                   nowcast_results: Dict[str, NowcastResult]) -> List[EarlyWarningSignal]:
        """Check for signs of market integration breakdown."""
        warnings = []
        
        # Group by commodity
        commodity_groups = {}
        for key, nowcast in nowcast_results.items():
            market_id, commodity = key.split('_', 1)
            if commodity not in commodity_groups:
                commodity_groups[commodity] = {}
            commodity_groups[commodity][market_id] = nowcast
            
        # Check each commodity group
        for commodity, market_nowcasts in commodity_groups.items():
            if len(market_nowcasts) < 2:
                continue
                
            # Calculate coefficient of variation across markets
            forecasts_df = pd.DataFrame({
                market: nowcast.point_forecast
                for market, nowcast in market_nowcasts.items()
            })
            
            cv_series = forecasts_df.std(axis=1) / forecasts_df.mean(axis=1)
            
            # Check for increasing CV (sign of disintegration)
            for i, (date, cv) in enumerate(cv_series.items()):
                if cv > 0.5:  # High dispersion threshold
                    warning = EarlyWarningSignal(
                        market_id='SYSTEM',
                        commodity=commodity,
                        signal_type='integration_breakdown',
                        severity='high' if cv > 0.7 else 'medium',
                        probability=0.8,  # Simplified
                        predicted_date=date,
                        confidence_interval=(cv * 0.8, cv * 1.2),
                        recommended_action="Investigate supply chain disruptions. Consider regional interventions.",
                        supporting_evidence={
                            'coefficient_of_variation': cv,
                            'markets_affected': list(market_nowcasts.keys()),
                            'price_dispersion': forecasts_df.iloc[i].std()
                        }
                    )
                    warnings.append(warning)
                    
        return warnings
        
    def generate_dashboard_data(self,
                              warnings: List[EarlyWarningSignal]) -> pd.DataFrame:
        """Generate data for humanitarian dashboard."""
        dashboard_data = []
        
        for warning in warnings:
            dashboard_data.append({
                'market_id': warning.market_id,
                'commodity': warning.commodity,
                'signal_type': warning.signal_type,
                'severity': warning.severity,
                'severity_score': {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}[warning.severity],
                'probability': warning.probability,
                'predicted_date': warning.predicted_date,
                'days_until': (warning.predicted_date - datetime.now()).days,
                'recommended_action': warning.recommended_action,
                'confidence_lower': warning.confidence_interval[0],
                'confidence_upper': warning.confidence_interval[1]
            })
            
        df = pd.DataFrame(dashboard_data)
        
        # Sort by severity and date
        df = df.sort_values(['severity_score', 'days_until'], ascending=[False, True])
        
        return df