"""Unified data pipeline orchestration service.

This service manages the complete data pipeline from collection through
processing to final integrated panel creation, following World Bank standards.
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from uuid import UUID, uuid4

from ...core.domain.shared.exceptions import DataPipelineException
from ...core.utils.logging import get_logger
from ..commands.run_three_tier_analysis import RunThreeTierAnalysisCommand
from .data_ingestion_service import DataIngestionService, IngestionResult
from .ingestion_orchestrator import IngestionOrchestrator, IngestionPriority, IngestionStatus
from .panel_builder_service import PanelBuilderService


logger = get_logger(__name__)


class PipelineStage(Enum):
    """Data pipeline stages."""
    
    COLLECTION = "collection"
    PROCESSING = "processing"
    INTEGRATION = "integration"
    VALIDATION = "validation"
    PANEL_BUILDING = "panel_building"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class PipelineConfig:
    """Configuration for data pipeline execution."""
    
    # Data collection
    start_date: datetime
    end_date: datetime
    force_refresh: bool = False
    
    # Data sources to include
    include_wfp: bool = True
    include_acled: bool = True
    include_acaps: bool = True
    include_hdx: bool = True
    
    # Processing options
    validate_currency: bool = True
    enforce_coverage: bool = True
    target_coverage: float = 0.884  # 88.4% target
    
    # Panel building
    create_balanced_panel: bool = True
    commodities: List[str] = None
    min_markets_per_commodity: int = 10
    
    # Output options
    output_dir: Path = Path("data/processed")
    save_intermediate: bool = True
    
    def __post_init__(self):
        if self.commodities is None:
            self.commodities = ["wheat", "sugar", "fuel", "rice"]


@dataclass
class PipelineStatus:
    """Status of pipeline execution."""
    
    pipeline_id: UUID = None
    stage: PipelineStage = PipelineStage.COLLECTION
    started_at: datetime = None
    completed_at: Optional[datetime] = None
    
    # Stage progress
    stages_completed: Dict[PipelineStage, bool] = None
    current_stage_progress: float = 0.0
    
    # Data metrics
    records_collected: Dict[str, int] = None
    records_processed: Dict[str, int] = None
    validation_passed: bool = False
    coverage_achieved: float = 0.0
    
    # Results
    panel_created: bool = False
    panel_path: Optional[Path] = None
    analysis_ready: bool = False
    
    # Errors
    errors: List[str] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.pipeline_id is None:
            self.pipeline_id = uuid4()
        if self.stages_completed is None:
            self.stages_completed = {stage: False for stage in PipelineStage}
        if self.records_collected is None:
            self.records_collected = {}
        if self.records_processed is None:
            self.records_processed = {}
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []


class DataPipelineOrchestrator:
    """Orchestrates the complete data pipeline workflow."""
    
    def __init__(
        self,
        ingestion_service: DataIngestionService,
        ingestion_orchestrator: IngestionOrchestrator,
        panel_builder_service: PanelBuilderService
    ):
        self.ingestion_service = ingestion_service
        self.ingestion_orchestrator = ingestion_orchestrator
        self.panel_builder_service = panel_builder_service
        self.logger = logger
        
        # Track active pipelines
        self._active_pipelines: Dict[UUID, PipelineStatus] = {}
    
    async def run_full_pipeline(
        self,
        config: PipelineConfig,
        progress_callback: Optional[callable] = None
    ) -> PipelineStatus:
        """Run the complete data pipeline from collection to panel building.
        
        Args:
            config: Pipeline configuration
            progress_callback: Optional callback for progress updates
            
        Returns:
            PipelineStatus with results
        """
        status = PipelineStatus(started_at=datetime.utcnow())
        self._active_pipelines[status.pipeline_id] = status
        
        try:
            # Stage 1: Data Collection
            await self._run_collection_stage(config, status, progress_callback)
            
            # Stage 2: Data Processing
            await self._run_processing_stage(config, status, progress_callback)
            
            # Stage 3: Data Integration
            await self._run_integration_stage(config, status, progress_callback)
            
            # Stage 4: Data Validation
            await self._run_validation_stage(config, status, progress_callback)
            
            # Stage 5: Panel Building
            await self._run_panel_building_stage(config, status, progress_callback)
            
            # Mark as completed
            status.stage = PipelineStage.COMPLETED
            status.completed_at = datetime.utcnow()
            status.analysis_ready = True
            
            self.logger.info(f"Pipeline {status.pipeline_id} completed successfully")
            
        except Exception as e:
            status.stage = PipelineStage.FAILED
            status.errors.append(str(e))
            self.logger.error(f"Pipeline {status.pipeline_id} failed: {e}", exc_info=True)
            raise DataPipelineException(f"Pipeline failed: {e}")
        
        finally:
            # Clean up
            if status.pipeline_id in self._active_pipelines:
                del self._active_pipelines[status.pipeline_id]
        
        return status
    
    async def _run_collection_stage(
        self,
        config: PipelineConfig,
        status: PipelineStatus,
        progress_callback: Optional[callable]
    ):
        """Run data collection stage."""
        status.stage = PipelineStage.COLLECTION
        self.logger.info("Starting data collection stage")
        
        # Prepare collection jobs
        sources = []
        if config.include_wfp:
            sources.append("wfp")
        if config.include_acled:
            sources.append("acled")
        if config.include_acaps:
            sources.append("acaps")
        if config.include_hdx:
            sources.append("hdx")
        
        # Submit batch job
        job_ids = await self.ingestion_orchestrator.submit_batch_job(
            sources=sources,
            priority=IngestionPriority.HIGH,
            start_date=config.start_date,
            end_date=config.end_date,
            force_refresh=config.force_refresh
        )
        
        # Wait for completion
        total_jobs = len(job_ids)
        completed_jobs = 0
        
        while completed_jobs < total_jobs:
            await asyncio.sleep(5)  # Check every 5 seconds
            
            for job_id in job_ids:
                job_status = await self.ingestion_orchestrator.get_job_status(job_id)
                if job_status and job_status.is_complete:
                    completed_jobs += 1
                    
                    if job_status.status == IngestionStatus.COMPLETED:
                        status.records_collected[job_status.source] = job_status.result.records_processed
                    else:
                        status.warnings.append(f"Job {job_status.source} failed")
            
            # Update progress
            status.current_stage_progress = completed_jobs / total_jobs
            if progress_callback:
                await progress_callback(status)
        
        status.stages_completed[PipelineStage.COLLECTION] = True
        self.logger.info(f"Collection stage completed: {status.records_collected}")
    
    async def _run_processing_stage(
        self,
        config: PipelineConfig,
        status: PipelineStatus,
        progress_callback: Optional[callable]
    ):
        """Run data processing stage."""
        status.stage = PipelineStage.PROCESSING
        self.logger.info("Starting data processing stage")
        
        # Process WFP data (prices and exchange rates)
        if config.include_wfp:
            from ...infrastructure.processors.currency_aware_wfp_processor import CurrencyAwareWFPProcessor
            processor = CurrencyAwareWFPProcessor()
            
            # Process prices
            wfp_prices_path = config.output_dir / "wfp_commodity_prices.parquet"
            if wfp_prices_path.exists():
                await processor.process_file(str(wfp_prices_path))
                status.records_processed["wfp_prices"] = True
        
        # Process ACLED conflict data
        if config.include_acled:
            from ...infrastructure.processors.acled_processor import ACLEDProcessor
            processor = ACLEDProcessor()
            
            acled_path = config.output_dir.parent / "raw/acled"
            if acled_path.exists():
                # Process latest ACLED file
                acled_files = sorted(acled_path.glob("*.csv"))
                if acled_files:
                    await processor.process(str(acled_files[-1]))
                    status.records_processed["acled"] = True
        
        # Process ACAPS control zones
        if config.include_acaps:
            # ACAPS processing already handled by updated processor
            control_zones_path = config.output_dir / "control_zones/control_zones_master.parquet"
            if control_zones_path.exists():
                status.records_processed["acaps"] = True
            else:
                # Trigger ACAPS processing
                from scripts.data_processing.process_acaps_data import main as process_acaps
                await asyncio.create_subprocess_exec("python", "scripts/data_processing/process_acaps_data.py")
        
        status.stages_completed[PipelineStage.PROCESSING] = True
        self.logger.info(f"Processing stage completed: {status.records_processed}")
    
    async def _run_integration_stage(
        self,
        config: PipelineConfig,
        status: PipelineStatus,
        progress_callback: Optional[callable]
    ):
        """Run data integration stage."""
        status.stage = PipelineStage.INTEGRATION
        self.logger.info("Starting data integration stage")
        
        # Run spatial joins to link markets with control zones
        if all(k in status.records_processed for k in ["wfp_prices", "acaps"]):
            from scripts.data_processing.run_spatial_joins import main as run_spatial_joins
            await asyncio.create_subprocess_exec("python", "scripts/data_processing/run_spatial_joins.py")
            status.records_processed["spatial_joins"] = True
        
        # Create integrated panel
        from scripts.analysis.create_integrated_balanced_panel import main as create_panel
        await asyncio.create_subprocess_exec("python", "scripts/analysis/create_integrated_balanced_panel.py")
        
        status.stages_completed[PipelineStage.INTEGRATION] = True
        self.logger.info("Integration stage completed")
    
    async def _run_validation_stage(
        self,
        config: PipelineConfig,
        status: PipelineStatus,
        progress_callback: Optional[callable]
    ):
        """Run data validation stage."""
        status.stage = PipelineStage.VALIDATION
        self.logger.info("Starting validation stage")
        
        # Check integrated panel exists
        panel_path = config.output_dir / "integrated_panel/yemen_integrated_balanced_panel.parquet"
        if not panel_path.exists():
            raise DataPipelineException("Integrated panel not found")
        
        # Validate using methodology validator
        from ...core.validation.methodology_validator import MethodologyValidator
        validator = MethodologyValidator()
        
        import pandas as pd
        panel_df = pd.read_parquet(panel_path)
        
        # Run validation
        is_valid, report = validator.validate_analysis_inputs(
            observations=panel_df,
            analysis_type="panel_analysis"
        )
        
        status.validation_passed = is_valid
        status.coverage_achieved = len(panel_df) / (300 * 36 * 12)  # markets * months * commodities
        
        if not is_valid:
            status.warnings.extend(report.critical_failures)
        
        status.stages_completed[PipelineStage.VALIDATION] = True
        self.logger.info(f"Validation completed: {is_valid}, coverage: {status.coverage_achieved:.2%}")
    
    async def _run_panel_building_stage(
        self,
        config: PipelineConfig,
        status: PipelineStatus,
        progress_callback: Optional[callable]
    ):
        """Run panel building stage."""
        status.stage = PipelineStage.PANEL_BUILDING
        self.logger.info("Starting panel building stage")
        
        # Prepare data for modeling
        from scripts.analysis.prepare_data_for_modeling import main as prepare_modeling
        await asyncio.create_subprocess_exec("python", "scripts/analysis/prepare_data_for_modeling.py")
        
        # Create final modeling-ready dataset
        modeling_ready_path = config.output_dir / "modeling_ready/yemen_panel_ready.parquet"
        if modeling_ready_path.exists():
            status.panel_created = True
            status.panel_path = modeling_ready_path
        
        status.stages_completed[PipelineStage.PANEL_BUILDING] = True
        self.logger.info(f"Panel building completed: {status.panel_path}")
    
    async def run_data_update(
        self,
        sources: List[str],
        force_refresh: bool = False
    ) -> Dict[str, IngestionResult]:
        """Run targeted data update for specific sources.
        
        Args:
            sources: List of data sources to update
            force_refresh: Force re-download even if data exists
            
        Returns:
            Dict of source to ingestion results
        """
        results = {}
        
        for source in sources:
            try:
                job_id = await self.ingestion_orchestrator.submit_job(
                    source=source,
                    priority=IngestionPriority.NORMAL,
                    force_refresh=force_refresh
                )
                
                # Wait for completion
                while True:
                    job_status = await self.ingestion_orchestrator.get_job_status(job_id)
                    if job_status and job_status.is_complete:
                        if job_status.result:
                            results[source] = job_status.result
                        break
                    await asyncio.sleep(2)
                    
            except Exception as e:
                self.logger.error(f"Failed to update {source}: {e}")
                results[source] = IngestionResult(
                    source=source,
                    success=False,
                    records_processed=0,
                    records_saved=0,
                    errors=[str(e)],
                    processing_time_seconds=0,
                    data_quality_score=0
                )
        
        return results
    
    def get_pipeline_status(self, pipeline_id: UUID) -> Optional[PipelineStatus]:
        """Get status of a running pipeline."""
        return self._active_pipelines.get(pipeline_id)