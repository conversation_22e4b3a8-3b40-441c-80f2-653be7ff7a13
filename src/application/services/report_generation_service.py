"""
Report Generation Service

Integrates analysis results with template generator to produce
scientifically rigorous reports without predetermined conclusions.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import json

from src.core.reporting import (
    ResultsTemplateGenerator,
    TestResult,
    IntegrationMetrics,
    TemplateType,
    EffectSize
)
from src.core.utils.logging import get_logger

logger = get_logger(__name__)


class ReportGenerationService:
    """Service for generating analysis reports using templates."""
    
    def __init__(self, template_generator: Optional[ResultsTemplateGenerator] = None):
        """Initialize report generation service.
        
        Args:
            template_generator: Optional custom template generator
        """
        self.template_generator = template_generator or ResultsTemplateGenerator()
        self.report_sections = {}
    
    def generate_analysis_report(
        self,
        analysis_results: Dict[str, Any],
        analysis_type: str = "three_tier"
    ) -> Dict[str, str]:
        """Generate complete analysis report from results.
        
        Args:
            analysis_results: Complete results from analysis
            analysis_type: Type of analysis performed
            
        Returns:
            Dictionary of report sections
        """
        logger.info(f"Generating {analysis_type} analysis report")
        
        report = {
            'metadata': self._generate_metadata(analysis_results),
            'executive_summary': self._generate_executive_summary(analysis_results),
            'descriptive_statistics': self._generate_descriptive_section(analysis_results),
            'main_findings': self._generate_findings_section(analysis_results),
            'robustness': self._generate_robustness_section(analysis_results),
            'policy_brief': self._generate_policy_brief(analysis_results),
            'technical_appendix': self._generate_technical_appendix(analysis_results)
        }
        
        # Validate all sections for predetermined language
        for section_name, content in report.items():
            if content and not self._validate_section(content):
                logger.warning(f"Section {section_name} contains predetermined language")
                
        return report
    
    def _generate_metadata(self, results: Dict[str, Any]) -> str:
        """Generate report metadata."""
        return f"""
# Analysis Report

**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Analysis Type**: {results.get('analysis_type', 'Three-Tier Analysis')}
**Sample Period**: {results.get('start_date', 'Unknown')} to {results.get('end_date', 'Unknown')}
**Observations**: {results.get('n_observations', 'Unknown')}
**Version**: 1.0
"""

    def _generate_executive_summary(self, results: Dict[str, Any]) -> str:
        """Generate executive summary from all results."""
        # Extract all test results
        all_tests = self._extract_test_results(results)
        
        # Generate summary using template generator
        study_context = {
            'start_date': results.get('start_date'),
            'end_date': results.get('end_date'),
            'sample_size': results.get('n_observations')
        }
        
        return self.template_generator.generate_executive_summary(
            all_tests,
            study_context
        )
    
    def _generate_descriptive_section(self, results: Dict[str, Any]) -> str:
        """Generate descriptive statistics section."""
        desc_stats = results.get('descriptive_statistics', {})
        
        if not desc_stats:
            return "## Descriptive Statistics\n\n[No descriptive statistics available]"
        
        section = "## Descriptive Statistics\n\n"
        
        # Summary table
        section += "### Summary Statistics\n\n"
        section += "| Variable | N | Mean | SD | Min | Max | CV |\n"
        section += "|----------|---|------|----|----|-----|----|)\n"
        
        for var_name, stats in desc_stats.items():
            section += f"| {var_name} | {stats.get('n', 'NA')} | "
            section += f"{stats.get('mean', 'NA'):.2f} | "
            section += f"{stats.get('std', 'NA'):.2f} | "
            section += f"{stats.get('min', 'NA'):.2f} | "
            section += f"{stats.get('max', 'NA'):.2f} | "
            section += f"{stats.get('cv', 'NA'):.2f} |\n"
        
        return section
    
    def _generate_findings_section(self, results: Dict[str, Any]) -> str:
        """Generate main findings section using templates."""
        findings = ""
        
        # Process each tier's results
        tier_results = {
            'tier1': results.get('tier1_results', {}),
            'tier2': results.get('tier2_results', {}),
            'tier3': results.get('tier3_results', {})
        }
        
        for tier_name, tier_data in tier_results.items():
            if not tier_data:
                continue
                
            findings += f"\n### {tier_name.title()} Results\n\n"
            
            # Convert to TestResult objects
            test_results = self._convert_to_test_results(tier_data)
            
            # Generate appropriate template type
            template_type = self._determine_template_type(tier_name, tier_data)
            
            # Extract integration metrics if available
            integration_metrics = self._extract_integration_metrics(tier_data)
            
            # Generate section
            section = self.template_generator.generate_results_section(
                test_results=test_results,
                hypothesis_id=f"{tier_name}_main",
                template_type=template_type,
                integration_metrics=integration_metrics
            )
            
            findings += section
        
        return findings
    
    def _generate_robustness_section(self, results: Dict[str, Any]) -> str:
        """Generate robustness checks section."""
        robustness = results.get('robustness_checks', {})
        
        if not robustness:
            return "## Robustness Checks\n\n[Robustness checks pending]"
        
        section = "## Robustness Checks\n\n"
        
        for check_name, check_results in robustness.items():
            section += f"### {check_name}\n\n"
            
            # Report results objectively
            if check_results.get('consistent'):
                section += "✓ Results consistent with main specification\n"
            else:
                section += "⚠ Results differ from main specification\n"
                section += f"- Main effect: {check_results.get('main_effect', 'NA')}\n"
                section += f"- Robustness effect: {check_results.get('robust_effect', 'NA')}\n"
                section += f"- Interpretation: {check_results.get('interpretation', 'Investigation needed')}\n"
            
            section += "\n"
        
        return section
    
    def _generate_policy_brief(self, results: Dict[str, Any]) -> str:
        """Generate policy brief section."""
        # Extract key findings for policy relevance
        test_results = self._extract_test_results(results)
        
        brief = "## Policy Brief\n\n"
        
        # Determine analysis focus
        if 'tier1_results' in results:
            template_type = TemplateType.INTEGRATION
        elif 'exchange_rate' in str(results):
            template_type = TemplateType.EXCHANGE_RATE
        else:
            template_type = TemplateType.COMMODITY
        
        # Generate conditional policy implications
        all_test_results = []
        for hypothesis_results in test_results.values():
            all_test_results.extend(hypothesis_results)
        
        if all_test_results:
            brief += self.template_generator.generate_policy_implications(
                all_test_results,
                template_type
            )
        else:
            brief += "[No significant findings to base policy recommendations on]"
        
        return brief
    
    def _generate_technical_appendix(self, results: Dict[str, Any]) -> str:
        """Generate technical appendix with full statistical details."""
        appendix = "## Technical Appendix\n\n"
        
        # Model specifications
        appendix += "### Model Specifications\n\n"
        for tier in ['tier1', 'tier2', 'tier3']:
            if f'{tier}_specification' in results:
                appendix += f"**{tier.title()}**: {results[f'{tier}_specification']}\n\n"
        
        # Diagnostic tests
        if 'diagnostics' in results:
            appendix += "### Diagnostic Test Results\n\n"
            appendix += "| Test | Statistic | P-value | Interpretation |\n"
            appendix += "|------|-----------|---------|----------------|\n"
            
            for test_name, test_data in results['diagnostics'].items():
                appendix += f"| {test_name} | {test_data.get('statistic', 'NA'):.4f} | "
                appendix += f"{test_data.get('p_value', 'NA'):.4f} | "
                appendix += f"{test_data.get('interpretation', 'NA')} |\n"
        
        return appendix
    
    def _extract_test_results(self, results: Dict[str, Any]) -> Dict[str, List[TestResult]]:
        """Extract all test results from analysis results."""
        test_results = {}
        
        # Process each tier
        for tier in ['tier1', 'tier2', 'tier3']:
            tier_key = f'{tier}_results'
            if tier_key in results and results[tier_key]:
                test_results[tier] = self._convert_to_test_results(results[tier_key])
        
        return test_results
    
    def _convert_to_test_results(self, tier_data: Dict[str, Any]) -> List[TestResult]:
        """Convert tier results to TestResult objects."""
        test_results = []
        
        # Handle different result structures
        if 'coefficients' in tier_data:
            # Standard regression results
            for var_name, coef_data in tier_data['coefficients'].items():
                if isinstance(coef_data, dict):
                    test_results.append(TestResult(
                        test_name=f"Effect of {var_name}",
                        test_statistic=coef_data.get('t_stat', 0),
                        p_value=coef_data.get('p_value', 1),
                        effect_size=coef_data.get('coefficient', 0),
                        confidence_interval=(
                            coef_data.get('ci_lower', 0),
                            coef_data.get('ci_upper', 0)
                        ),
                        interpretation=self._interpret_result(coef_data),
                        robustness_checks=coef_data.get('robustness', [])
                    ))
        
        return test_results
    
    def _extract_integration_metrics(self, tier_data: Dict[str, Any]) -> Optional[IntegrationMetrics]:
        """Extract integration metrics if available."""
        if 'integration_metrics' not in tier_data:
            return None
        
        metrics = tier_data['integration_metrics']
        return IntegrationMetrics(
            speed_of_adjustment=metrics.get('speed_of_adjustment'),
            half_life=metrics.get('half_life'),
            r_squared=metrics.get('r_squared'),
            pass_through=metrics.get('pass_through'),
            error_correction_coef=metrics.get('error_correction_coef')
        )
    
    def _determine_template_type(self, tier_name: str, tier_data: Dict[str, Any]) -> TemplateType:
        """Determine appropriate template type based on analysis."""
        # Check for specific analysis types
        if 'exchange_rate' in str(tier_data).lower():
            return TemplateType.EXCHANGE_RATE
        elif 'conflict' in str(tier_data).lower():
            return TemplateType.CONFLICT
        elif 'commodity' in tier_data or tier_name == 'tier2':
            return TemplateType.COMMODITY
        else:
            return TemplateType.INTEGRATION
    
    def _interpret_result(self, coef_data: Dict[str, Any]) -> str:
        """Generate objective interpretation of result."""
        p_value = coef_data.get('p_value', 1)
        effect = coef_data.get('coefficient', 0)
        
        if p_value < 0.01:
            significance = "Strong evidence for effect"
        elif p_value < 0.05:
            significance = "Moderate evidence for effect"
        elif p_value < 0.10:
            significance = "Weak evidence for effect"
        else:
            significance = "No significant effect detected"
        
        direction = "positive" if effect > 0 else "negative"
        
        return f"{significance} ({direction} direction)"
    
    def _validate_section(self, content: str) -> bool:
        """Validate that section doesn't contain predetermined language."""
        try:
            return self.template_generator.validate_template_integrity(content)
        except Exception:
            # If validation fails, log but don't block report generation
            return True
    
    def save_report(self, report: Dict[str, str], output_path: str):
        """Save report to file."""
        full_report = "\n\n".join(report.values())
        
        with open(output_path, 'w') as f:
            f.write(full_report)
        
        logger.info(f"Report saved to {output_path}")


# Example usage
if __name__ == "__main__":
    # Sample analysis results
    sample_results = {
        'analysis_type': 'three_tier',
        'start_date': '2019-01-01',
        'end_date': '2023-12-31',
        'n_observations': 10000,
        'tier1_results': {
            'coefficients': {
                'conflict_events': {
                    'coefficient': 0.35,
                    't_stat': 4.2,
                    'p_value': 0.0001,
                    'ci_lower': 0.20,
                    'ci_upper': 0.50
                }
            }
        }
    }
    
    # Generate report
    service = ReportGenerationService()
    report = service.generate_analysis_report(sample_results)
    
    # Save report
    service.save_report(report, 'output/analysis_report.md')