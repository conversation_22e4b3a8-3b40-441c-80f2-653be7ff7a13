# Currency Methodology Checker

## Overview

The documentation consistency checker has been enhanced with sophisticated currency validation specifically designed to catch the Yemen exchange rate issue. This ensures that any price comparison between regions explicitly mentions currency conversion and the correct exchange rates.

## Key Features

### 1. Yemen-Specific Exchange Rate Validation

The checker enforces proper documentation of exchange rates:
- **Northern areas (Houthi-controlled)**: 535 YER/USD
- **Southern areas (Government-controlled)**: 2,000+ YER/USD

### 2. Currency Methodology Checks

The `_check_currency_methodology` method performs the following validations:

#### Cross-Regional Price Comparisons
- Detects when documentation discusses prices across different regions
- Ensures currency conversion is explicitly mentioned
- Verifies both exchange rates are documented

#### Currency Specification
- Flags numerical values without currency specification (YER or USD)
- Detects mixed currencies in the same context
- Identifies regional price mentions without currency

#### Exchange Rate Accuracy
- Catches incorrect exchange rate values
- Flags statements suggesting uniform exchange rates across Yemen
- Identifies exchange rates that are too low for Yemen context

## Usage

### Command Line

```bash
# Check all documentation
python3 src/tools/documentation_consistency_checker.py

# Check specific directory with verbose output
python3 src/tools/documentation_consistency_checker.py docs/research-methodology-package/ -v

# Generate markdown report
python3 src/tools/documentation_consistency_checker.py -m consistency_report.md
```

### Programmatic Usage

```python
from src.tools.documentation_consistency_checker import DocumentationConsistencyChecker

# Create checker instance
checker = DocumentationConsistencyChecker()

# Run scan
report = checker.scan_documentation(
    paths=["docs/"],
    exclude_patterns=["__pycache__", ".git"]
)

# Print report
checker.print_report(report, verbose=True)
```

## Issue Types

### Currency-Related Issues

1. **`missing_currency_conversion`** (ERROR)
   - Cross-regional price comparison without mentioning currency conversion
   - Severity: Error
   - Fix: Explicitly state currency conversion methodology

2. **`missing_exchange_rates`** (ERROR)
   - Missing exchange rate specifications for cross-regional analysis
   - Severity: Error
   - Fix: Include both northern (535 YER/USD) and southern (2,000+ YER/USD) rates

3. **`regional_price_without_currency`** (ERROR)
   - Regional price mentioned without currency specification
   - Severity: Error
   - Fix: Always specify YER or USD

4. **`price_comparison_without_currency`** (WARNING)
   - Price comparison without currency specification
   - Severity: Warning
   - Fix: Specify whether prices are in YER or USD

5. **`incorrect_exchange_rate`** (ERROR)
   - Exchange rate values that don't match Yemen reality
   - Severity: Error
   - Fix: Use correct rates (North: ~535, South: ~2,000+ YER/USD)

## Example Output

```
Currency Methodology Issues

**Critical for Yemen Analysis:** Price comparisons must account for exchange rate differences between regions.

### Missing Currency Conversion

- **docs/analysis/market-integration.md** (Line 0): Cross-regional price comparison without mentioning currency conversion
  Regions mentioned: north, south

### Missing Exchange Rates

- **docs/results/price-analysis.md** (Line 0): Missing exchange rate specifications: Northern rate (535 YER/USD), Southern rate (2,000+ YER/USD)

### Regional Price Without Currency

- **docs/findings/wheat-prices.md** (Line 45): Regional price mentioned without currency specification
  Context: Wheat prices in Sana'a averaged 500 in 2023
```

## Best Practices

### Correct Documentation Example

```markdown
# Price Analysis

When comparing prices between regions, we must account for different exchange rates:
- Northern areas (Houthi-controlled): 535 YER/USD
- Southern areas (Government-controlled): 2,000+ YER/USD

After converting to USD:
- Wheat in Sana'a: 500 YER = 0.93 USD
- Wheat in Aden: 1,800 YER = 0.90 USD

This reveals that northern prices are actually higher when properly converted.
```

### Common Mistakes to Avoid

1. **Comparing raw YER prices across regions**
   ```markdown
   ❌ "Prices in the north (500 YER) are lower than the south (1,800 YER)"
   ✓ "Prices in the north (500 YER = 0.93 USD) are higher than the south (1,800 YER = 0.90 USD)"
   ```

2. **Using a single exchange rate**
   ```markdown
   ❌ "Using the national exchange rate of 800 YER/USD..."
   ✓ "Using region-specific rates (North: 535, South: 2,000+ YER/USD)..."
   ```

3. **Omitting currency in regional analysis**
   ```markdown
   ❌ "Average price in Aden: 1,500"
   ✓ "Average price in Aden: 1,500 YER (0.75 USD at 2,000 YER/USD)"
   ```

## Integration with CI/CD

Add to your GitHub Actions workflow:

```yaml
- name: Check Documentation Consistency
  run: |
    python3 src/tools/documentation_consistency_checker.py docs/ -o consistency_report.json
    if [ $? -ne 0 ]; then
      echo "Documentation consistency check failed"
      exit 1
    fi
```

## Troubleshooting

### False Positives

If the checker flags legitimate content:
1. Ensure currency is always specified for monetary values
2. Include exchange rate context when discussing regional differences
3. Use precise language to avoid ambiguity

### Performance

For large documentation sets:
- Use specific paths instead of scanning everything
- Exclude non-documentation directories
- Run in parallel for different sections

## Future Enhancements

Planned improvements:
1. Context-aware validation (understanding when currency matters)
2. Automatic suggestion of correct exchange rates
3. Historical exchange rate validation
4. Integration with data validation tools