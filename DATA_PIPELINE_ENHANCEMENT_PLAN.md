# Yemen Market Integration: Data Pipeline Enhancement Implementation Plan

**Date**: January 2025  
**Priority**: CRITICAL - Exchange rate data invalidates all current results  
**Duration**: 4 weeks  
**Impact**: Fundamental correction of price analysis methodology  

## Executive Summary

Critical data quality issues have been identified that invalidate current analysis results:
- Exchange rates are incorrectly set to ~250 YER/USD (should be ~535 North, ~2000+ South)
- Zone classifications use wrong nomenclature (DFA/IRG vs HOUTHI/GOVERNMENT)
- Rich WFP data (wages, fuel, livestock) remains unused
- No validation layer exists to catch these issues

This plan provides a systematic approach to fix these issues and enhance the data pipeline.

## Week 1: Critical Exchange Rate Fix (MUST DO FIRST)

### Day 1-2: Extract Real Exchange Rates from WFP Data

**Objective**: Fix the fundamental exchange rate extraction error

**Files to Modify**:
- `src/infrastructure/processors/wfp_processor.py`
- `src/infrastructure/processors/currency_aware_wfp_processor.py`

**Implementation**:

```python
# In wfp_processor.py, add to _extract_price_observations():

async def _extract_exchange_rates_from_commodity(self, df: pd.DataFrame) -> Dict[Tuple[str, datetime], Decimal]:
    """Extract exchange rates from WFP commodity data."""
    # Filter for exchange rate commodity
    er_df = df[df['commodity'].str.contains('Exchange rate', case=False, na=False)]
    
    exchange_rates = {}
    for _, row in er_df.iterrows():
        market = row['market_name']
        date = row['date']
        rate = Decimal(str(row['price']))
        
        # Validate rate is in expected range
        if rate < 100 or rate > 3000:
            logger.warning(f"Suspicious exchange rate {rate} for {market} on {date}")
            continue
            
        exchange_rates[(market, date)] = rate
    
    logger.info(f"Extracted {len(exchange_rates)} exchange rates from WFP data")
    return exchange_rates
```

**Validation Script**:

```python
# scripts/validate_exchange_rates.py
import pandas as pd
from pathlib import Path

def validate_exchange_rates():
    # Load raw WFP data
    wfp_raw = pd.read_csv('data/raw/wfp/wfp_food_prices_wfp-food-prices-for-yemen.csv')
    
    # Extract exchange rate data
    er_data = wfp_raw[wfp_raw['commodity'].str.contains('Exchange rate', case=False, na=False)]
    
    # Group by region and show rate ranges
    print("Exchange Rate Ranges by Region:")
    print(er_data.groupby('admin1')['price'].agg(['min', 'mean', 'max', 'count']))
    
    # Flag anomalies
    anomalies = er_data[(er_data['price'] < 300) | (er_data['price'] > 2500)]
    print(f"\nFound {len(anomalies)} anomalous rates out of {len(er_data)}")
    
    return er_data

if __name__ == "__main__":
    validate_exchange_rates()
```

**Success Criteria**:
- [ ] Exchange rates extracted show ~535 for Northern markets
- [ ] Exchange rates extracted show ~2000+ for Southern markets  
- [ ] No rates below 300 or above 2500 (except for documented events)

### Day 3: Implement Zone Classification Mapping

**Objective**: Map DFA/IRG to HOUTHI/GOVERNMENT classifications

**Files to Modify**:
- `src/infrastructure/processors/currency_zone_classifier.py`
- `src/core/domain/market/currency_zones.py`

**Implementation**:

```python
# Add to currency_zone_classifier.py

ZONE_NAME_MAPPING = {
    'DFA': CurrencyZone.HOUTHI,
    'De Facto Authority': CurrencyZone.HOUTHI,
    'IRG': CurrencyZone.GOVERNMENT,
    'Internationally Recognized Government': CurrencyZone.GOVERNMENT,
    'Contested': CurrencyZone.CONTESTED,
    'Unknown': CurrencyZone.UNKNOWN
}

def standardize_zone_name(self, zone_name: str) -> CurrencyZone:
    """Standardize zone names to enum values."""
    if pd.isna(zone_name):
        return CurrencyZone.UNKNOWN
        
    zone_name = str(zone_name).strip()
    
    # Direct mapping
    if zone_name in ZONE_NAME_MAPPING:
        return ZONE_NAME_MAPPING[zone_name]
    
    # Fuzzy matching for variations
    zone_lower = zone_name.lower()
    if 'dfa' in zone_lower or 'de facto' in zone_lower:
        return CurrencyZone.HOUTHI
    elif 'irg' in zone_lower or 'recognized' in zone_lower:
        return CurrencyZone.GOVERNMENT
    
    logger.warning(f"Unknown zone name: {zone_name}")
    return CurrencyZone.UNKNOWN
```

**Test Script**:

```python
# tests/test_zone_classification.py
def test_zone_mapping():
    classifier = CurrencyZoneClassifier()
    
    test_cases = [
        ('DFA', CurrencyZone.HOUTHI),
        ('IRG', CurrencyZone.GOVERNMENT),
        ('De Facto Authority', CurrencyZone.HOUTHI),
        ('Contested', CurrencyZone.CONTESTED),
        (None, CurrencyZone.UNKNOWN),
        ('Unknown String', CurrencyZone.UNKNOWN)
    ]
    
    for input_name, expected in test_cases:
        result = classifier.standardize_zone_name(input_name)
        assert result == expected, f"Failed for {input_name}: got {result}, expected {expected}"
```

### Day 4-5: Validate USD Conversions

**Objective**: Compare calculated USD prices with WFP's pre-calculated values

**Implementation**:

```python
# scripts/validate_usd_conversions.py
import pandas as pd
import numpy as np

def validate_usd_conversions():
    # Load raw WFP data with both YER and USD prices
    wfp_raw = pd.read_csv('data/raw/wfp/wfp_food_prices_wfp-food-prices-for-yemen.csv')
    
    # Load processed data
    processed = pd.read_parquet('data/processed/integrated_panel/yemen_integrated_balanced_panel.parquet')
    
    # Merge on market, commodity, and date
    comparison = pd.merge(
        wfp_raw[['date', 'market', 'commodity', 'price', 'usdprice']],
        processed[['date', 'market_name', 'commodity', 'price_usd_actual']],
        left_on=['date', 'market', 'commodity'],
        right_on=['date', 'market_name', 'commodity']
    )
    
    # Calculate discrepancy
    comparison['discrepancy_pct'] = abs(
        (comparison['usdprice'] - comparison['price_usd_actual']) / comparison['usdprice'] * 100
    )
    
    # Report findings
    print(f"Total comparisons: {len(comparison)}")
    print(f"Average discrepancy: {comparison['discrepancy_pct'].mean():.2f}%")
    print(f"Comparisons with >5% discrepancy: {(comparison['discrepancy_pct'] > 5).sum()}")
    
    # Show worst discrepancies
    worst = comparison.nlargest(10, 'discrepancy_pct')
    print("\nWorst discrepancies:")
    print(worst[['market', 'commodity', 'date', 'usdprice', 'price_usd_actual', 'discrepancy_pct']])
    
    return comparison
```

**Create Validation Report**:

```python
# scripts/generate_exchange_rate_impact_report.py
def generate_impact_report():
    """Show how fixing exchange rates changes the analysis results."""
    
    # Load data with wrong exchange rates
    old_data = pd.read_parquet('data/processed/integrated_panel/yemen_integrated_balanced_panel.parquet')
    
    # Load data with correct exchange rates (after fix)
    new_data = pd.read_parquet('data/processed/integrated_panel/yemen_integrated_balanced_panel_fixed.parquet')
    
    # Compare price differentials
    old_diff = calculate_price_differential(old_data)
    new_diff = calculate_price_differential(new_data)
    
    print("Impact of Exchange Rate Fix:")
    print(f"Old North-South differential: {old_diff:.2%}")
    print(f"New North-South differential: {new_diff:.2%}")
    print(f"Change in findings: {abs(new_diff - old_diff):.2%}")
```

## Week 2: Data Pipeline Enhancement

### Day 6-7: Configure CurrencyAwareWFPProcessor

**Objective**: Enable zone-specific exchange rate application

**Files to Modify**:
- `src/infrastructure/processors/currency_aware_wfp_processor.py`
- `src/application/services/data_pipeline_orchestrator.py`

**Implementation**:

```python
# Update data_pipeline_orchestrator.py to use currency-aware processor

async def _process_wfp_data(self):
    """Process WFP data with currency awareness."""
    # Initialize with proper configuration
    processor = CurrencyAwareWFPProcessor(
        zone_classifier=CurrencyZoneClassifier(),
        enable_zone_conversion=True,
        enable_advanced_quality_control=True
    )
    
    # Process with validation
    markets, observations, rates, metrics = await processor.process(
        data_source='data/raw/wfp/wfp_food_prices_wfp-food-prices-for-yemen.csv',
        zone_aware=True
    )
    
    # Log quality metrics
    logger.info(f"Data quality score: {metrics.get('quality_score', 0):.2f}")
    logger.info(f"Conversion success rate: {metrics.get('conversion_success_rate', 0):.2%}")
    
    return markets, observations, rates
```

### Day 8-9: Add Validation Layer

**Objective**: Implement automated data quality checks

**Create New File**:
- `src/infrastructure/validation/exchange_rate_validator.py`

```python
from dataclasses import dataclass
from typing import List, Tuple, Optional
import pandas as pd
import numpy as np

@dataclass
class ValidationIssue:
    severity: str  # 'critical', 'warning', 'info'
    category: str  # 'exchange_rate', 'zone', 'price', 'coverage'
    message: str
    affected_records: int
    details: Optional[dict] = None

class ExchangeRateValidator:
    """Validate exchange rates and currency conversions."""
    
    # Expected ranges by zone
    ZONE_RATE_RANGES = {
        'HOUTHI': (400, 700),      # Historical range for North
        'GOVERNMENT': (1500, 2500), # Historical range for South
        'CONTESTED': (600, 1800)    # Somewhere between
    }
    
    def validate_dataset(self, df: pd.DataFrame) -> List[ValidationIssue]:
        """Run all validation checks on dataset."""
        issues = []
        
        # Check 1: Exchange rate ranges
        issues.extend(self._validate_exchange_rate_ranges(df))
        
        # Check 2: Zone classifications
        issues.extend(self._validate_zone_classifications(df))
        
        # Check 3: USD conversion accuracy
        issues.extend(self._validate_usd_conversions(df))
        
        # Check 4: Temporal consistency
        issues.extend(self._validate_temporal_consistency(df))
        
        # Check 5: Spatial consistency
        issues.extend(self._validate_spatial_consistency(df))
        
        return issues
    
    def _validate_exchange_rate_ranges(self, df: pd.DataFrame) -> List[ValidationIssue]:
        """Check if exchange rates are within expected ranges."""
        issues = []
        
        if 'exchange_rate_used' not in df.columns or 'currency_zone' not in df.columns:
            issues.append(ValidationIssue(
                severity='critical',
                category='exchange_rate',
                message='Missing exchange rate or currency zone columns',
                affected_records=len(df)
            ))
            return issues
        
        for zone, (min_rate, max_rate) in self.ZONE_RATE_RANGES.items():
            zone_data = df[df['currency_zone'] == zone]
            if len(zone_data) == 0:
                continue
                
            out_of_range = zone_data[
                (zone_data['exchange_rate_used'] < min_rate) | 
                (zone_data['exchange_rate_used'] > max_rate)
            ]
            
            if len(out_of_range) > 0:
                issues.append(ValidationIssue(
                    severity='critical',
                    category='exchange_rate',
                    message=f'Exchange rates out of range for {zone}',
                    affected_records=len(out_of_range),
                    details={
                        'expected_range': (min_rate, max_rate),
                        'actual_min': out_of_range['exchange_rate_used'].min(),
                        'actual_max': out_of_range['exchange_rate_used'].max(),
                        'sample_dates': out_of_range['date'].value_counts().head().to_dict()
                    }
                ))
        
        return issues
```

### Day 10: Expand Commodity Coverage

**Objective**: Include wages, fuel, livestock in analysis

**Files to Modify**:
- `config/commodities_config.yaml` (create new)
- `src/infrastructure/processors/wfp_processor.py`

```yaml
# config/commodities_config.yaml
commodity_groups:
  food_staples:
    - Wheat flour
    - Rice (imported)
    - Sugar (white)
    - Cooking oil
  
  proteins:
    - Beans (kidney red)
    - Chicken (meat)
    - Mutton
    - Fish
  
  energy:
    - Fuel (diesel)
    - Fuel (petrol)
    - Gas (LPG)
  
  labor:
    - Wage (qualified labour)
    - Wage (non-qualified labour)
  
  livestock:
    - Sheep (one year old)
    - Sheep (two year old)
  
  processing:
    - Wheat (grain, milling cost)

analysis_sets:
  core: [food_staples]
  extended: [food_staples, proteins, energy]
  comprehensive: [food_staples, proteins, energy, labor, livestock, processing]
```

**Update WFP Processor**:

```python
def load_commodity_config(self) -> dict:
    """Load commodity configuration."""
    import yaml
    with open('config/commodities_config.yaml', 'r') as f:
        return yaml.safe_load(f)

def get_analysis_commodities(self, analysis_type: str = 'extended') -> List[str]:
    """Get list of commodities for analysis type."""
    config = self.load_commodity_config()
    commodity_groups = config['analysis_sets'].get(analysis_type, ['food_staples'])
    
    commodities = []
    for group in commodity_groups:
        commodities.extend(config['commodity_groups'].get(group, []))
    
    return commodities
```

## Week 3: Multi-Source Validation & Monitoring

### Day 11-12: Implement Cross-Source Validation

**Objective**: Validate exchange rates across multiple sources

**Create New Module**:
- `src/infrastructure/validation/multi_source_validator.py`

```python
class MultiSourceValidator:
    """Validate data across multiple sources."""
    
    def __init__(self):
        self.sources = {
            'wfp': WFPDataSource(),
            'cby_aden': CBYAdenScraper(),
            'cby_sanaa': CBYSanaaScraper(),
            'xe': XEAPIClient(),
            'oanda': OANDAAPIClient()
        }
    
    async def validate_exchange_rates(
        self, 
        date: datetime, 
        zone: CurrencyZone
    ) -> ValidationResult:
        """Cross-validate exchange rates for a given date and zone."""
        
        rates = {}
        for source_name, source in self.sources.items():
            try:
                rate = await source.get_rate(date, zone)
                if rate:
                    rates[source_name] = rate
            except Exception as e:
                logger.warning(f"Failed to get rate from {source_name}: {e}")
        
        if len(rates) < 2:
            return ValidationResult(
                valid=False,
                message="Insufficient sources for validation",
                rates=rates
            )
        
        # Calculate statistics
        rate_values = list(rates.values())
        mean_rate = np.mean(rate_values)
        std_rate = np.std(rate_values)
        cv = std_rate / mean_rate if mean_rate > 0 else float('inf')
        
        # Flag if coefficient of variation > 10%
        if cv > 0.10:
            return ValidationResult(
                valid=False,
                message=f"High variation in rates (CV={cv:.2%})",
                rates=rates,
                stats={'mean': mean_rate, 'std': std_rate, 'cv': cv}
            )
        
        return ValidationResult(
            valid=True,
            message="Rates validated across sources",
            rates=rates,
            stats={'mean': mean_rate, 'std': std_rate, 'cv': cv}
        )
```

### Day 13-14: Create Monitoring Dashboard

**Objective**: Real-time data quality monitoring

**Create New File**:
- `scripts/monitoring_dashboard.py`

```python
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta

st.set_page_config(
    page_title="Yemen Data Pipeline Monitor",
    layout="wide",
    initial_sidebar_state="expanded"
)

st.title("Yemen Market Integration - Data Quality Dashboard")

# Sidebar filters
with st.sidebar:
    st.header("Filters")
    date_range = st.date_input(
        "Date Range",
        value=(datetime.now() - timedelta(days=30), datetime.now()),
        max_value=datetime.now()
    )
    
    zones = st.multiselect(
        "Currency Zones",
        options=['HOUTHI', 'GOVERNMENT', 'CONTESTED'],
        default=['HOUTHI', 'GOVERNMENT']
    )

# Main dashboard
col1, col2, col3, col4 = st.columns(4)

with col1:
    st.metric("Data Coverage", "95.2%", "+2.1%")
with col2:
    st.metric("Exchange Rate Quality", "87.3%", "-1.2%")
with col3:
    st.metric("Zone Classification", "91.5%", "+0.5%")
with col4:
    st.metric("USD Conversion Accuracy", "98.7%", "+0.3%")

# Exchange rate trends
st.header("Exchange Rate Trends by Zone")

@st.cache_data
def load_exchange_rate_data():
    # Load actual data
    return pd.read_parquet('data/processed/exchange_rates/zone_rates_daily.parquet')

er_data = load_exchange_rate_data()

fig = px.line(
    er_data,
    x='date',
    y='rate',
    color='zone',
    title='YER/USD Exchange Rates by Currency Zone',
    labels={'rate': 'Exchange Rate (YER/USD)', 'date': 'Date'}
)

fig.add_hline(y=535, line_dash="dash", line_color="blue", 
              annotation_text="Historical North Baseline")
fig.add_hline(y=2000, line_dash="dash", line_color="red",
              annotation_text="Historical South Baseline")

st.plotly_chart(fig, use_container_width=True)

# Data quality issues
st.header("Recent Data Quality Issues")

issues_df = pd.DataFrame({
    'Timestamp': pd.date_range(end=datetime.now(), periods=10, freq='H'),
    'Severity': ['Warning', 'Critical', 'Info'] * 3 + ['Warning'],
    'Category': ['Exchange Rate', 'Zone', 'Coverage'] * 3 + ['Price'],
    'Message': [
        'Rate spike detected in Aden market',
        'Missing zone classification for 15 observations',
        'Coverage below target for Wheat flour',
    ] * 3 + ['Negative price detected']
})

st.dataframe(
    issues_df,
    use_container_width=True,
    hide_index=True
)

# Add refresh button
if st.button("Refresh Data"):
    st.cache_data.clear()
    st.experimental_rerun()
```

### Day 15: Implement Anomaly Detection

**Objective**: Automated detection of data quality issues

**Create New Module**:
- `src/infrastructure/monitoring/anomaly_detector.py`

```python
from sklearn.ensemble import IsolationForest
from statsmodels.tsa.seasonal import seasonal_decompose
import numpy as np

class ExchangeRateAnomalyDetector:
    """Detect anomalies in exchange rate data."""
    
    def __init__(self, contamination: float = 0.05):
        self.contamination = contamination
        self.models = {}
        
    def train(self, df: pd.DataFrame):
        """Train anomaly detection models by zone."""
        for zone in df['currency_zone'].unique():
            zone_data = df[df['currency_zone'] == zone].copy()
            
            # Feature engineering
            zone_data['rate_log'] = np.log(zone_data['exchange_rate_used'])
            zone_data['rate_change'] = zone_data['rate_log'].diff()
            zone_data['rate_volatility'] = zone_data['rate_change'].rolling(7).std()
            
            # Train isolation forest
            features = ['rate_log', 'rate_change', 'rate_volatility']
            X = zone_data[features].dropna()
            
            model = IsolationForest(
                contamination=self.contamination,
                random_state=42
            )
            model.fit(X)
            
            self.models[zone] = {
                'model': model,
                'features': features,
                'scaler': StandardScaler().fit(X)
            }
    
    def detect_anomalies(self, df: pd.DataFrame) -> pd.DataFrame:
        """Detect anomalies in new data."""
        results = []
        
        for zone, zone_data in df.groupby('currency_zone'):
            if zone not in self.models:
                continue
                
            # Prepare features
            zone_data = zone_data.copy()
            zone_data['rate_log'] = np.log(zone_data['exchange_rate_used'])
            zone_data['rate_change'] = zone_data['rate_log'].diff()
            zone_data['rate_volatility'] = zone_data['rate_change'].rolling(7).std()
            
            # Predict anomalies
            features = self.models[zone]['features']
            X = zone_data[features].dropna()
            
            if len(X) > 0:
                X_scaled = self.models[zone]['scaler'].transform(X)
                predictions = self.models[zone]['model'].predict(X_scaled)
                
                zone_data.loc[X.index, 'is_anomaly'] = predictions == -1
                zone_data.loc[X.index, 'anomaly_score'] = (
                    self.models[zone]['model'].score_samples(X_scaled)
                )
            
            results.append(zone_data)
        
        return pd.concat(results, ignore_index=True)
```

## Week 4: Advanced Analytics Extensions

### Day 16-17: Labor Market Integration Analysis

**Objective**: Analyze wage convergence across zones

**Create New Analysis Module**:
- `src/application/analysis/labor_market_integration.py`

```python
class LaborMarketIntegrationAnalysis:
    """Analyze labor market integration using wage data."""
    
    def __init__(self, panel_data: pd.DataFrame):
        self.panel_data = panel_data
        self.wage_commodities = [
            'Wage (qualified labour)',
            'Wage (non-qualified labour)'
        ]
    
    def calculate_real_wage_differential(self) -> pd.DataFrame:
        """Calculate real wage differentials between zones."""
        
        # Filter wage data
        wage_data = self.panel_data[
            self.panel_data['commodity'].isin(self.wage_commodities)
        ].copy()
        
        # Calculate real wages (wages / food price index)
        food_price_index = self._calculate_food_price_index()
        wage_data = wage_data.merge(food_price_index, on=['market_id', 'date'])
        wage_data['real_wage'] = wage_data['price_usd'] / wage_data['food_price_index']
        
        # Calculate zone averages
        zone_wages = wage_data.groupby(['date', 'currency_zone', 'commodity'])[
            'real_wage'
        ].mean().reset_index()
        
        # Pivot to calculate differentials
        wage_pivot = zone_wages.pivot_table(
            index=['date', 'commodity'],
            columns='currency_zone',
            values='real_wage'
        )
        
        # Calculate differential
        wage_pivot['north_south_diff_pct'] = (
            (wage_pivot['HOUTHI'] - wage_pivot['GOVERNMENT']) / 
            wage_pivot['GOVERNMENT'] * 100
        )
        
        return wage_pivot
    
    def test_wage_convergence(self) -> Dict[str, Any]:
        """Test if wages are converging or diverging between zones."""
        from statsmodels.tsa.stattools import coint
        
        results = {}
        
        for commodity in self.wage_commodities:
            # Get zone wages
            comm_data = self.panel_data[
                self.panel_data['commodity'] == commodity
            ]
            
            north_wages = comm_data[
                comm_data['currency_zone'] == 'HOUTHI'
            ].set_index('date')['price_usd'].sort_index()
            
            south_wages = comm_data[
                comm_data['currency_zone'] == 'GOVERNMENT'  
            ].set_index('date')['price_usd'].sort_index()
            
            # Align series
            north_wages, south_wages = north_wages.align(south_wages, join='inner')
            
            if len(north_wages) > 30:  # Need sufficient observations
                # Cointegration test
                coint_result = coint(north_wages, south_wages)
                
                results[commodity] = {
                    'cointegrated': coint_result[1] < 0.05,
                    'p_value': coint_result[1],
                    'convergence_speed': self._estimate_convergence_speed(
                        north_wages, south_wages
                    )
                }
        
        return results
```

### Day 18-19: Energy Price Transmission Analysis

**Objective**: Study fuel price pass-through to transportation costs

```python
class EnergyPriceTransmission:
    """Analyze energy price transmission mechanisms."""
    
    def estimate_fuel_passthrough(self) -> pd.DataFrame:
        """Estimate pass-through from fuel to food prices."""
        
        # Get fuel prices
        fuel_prices = self.panel_data[
            self.panel_data['commodity'].str.contains('Fuel', case=False)
        ].groupby(['market_id', 'date'])['price_usd'].mean()
        
        # Get food prices  
        food_prices = self.panel_data[
            self.panel_data['commodity'].isin(['Wheat flour', 'Rice', 'Sugar'])
        ].groupby(['market_id', 'date'])['price_usd'].mean()
        
        # Calculate pass-through elasticity
        from linearmodels import PanelOLS
        
        # Merge and prepare panel
        analysis_df = pd.DataFrame({
            'fuel_price_log': np.log(fuel_prices),
            'food_price_log': np.log(food_prices)
        }).reset_index()
        
        # Set multi-index for panel
        analysis_df = analysis_df.set_index(['market_id', 'date'])
        
        # Estimate pass-through
        model = PanelOLS(
            dependent=analysis_df['food_price_log'],
            exog=analysis_df[['fuel_price_log']],
            entity_effects=True,
            time_effects=True
        )
        
        results = model.fit(cov_type='clustered', cluster_entity=True)
        
        return results
```

### Day 20: Create Comprehensive Validation Report

**Objective**: Document all changes and their impact

**Create Report Generator**:
- `scripts/generate_data_quality_report.py`

```python
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, Image
from reportlab.lib.styles import getSampleStyleSheet

class DataQualityReport:
    """Generate comprehensive data quality report."""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.story = []
        
    def generate_report(self, output_path: str = 'reports/data_quality_report.pdf'):
        """Generate full PDF report."""
        
        doc = SimpleDocTemplate(output_path, pagesize=letter)
        
        # Title
        self.story.append(Paragraph(
            "Yemen Market Integration: Data Quality Report",
            self.styles['Title']
        ))
        self.story.append(Spacer(1, 12))
        
        # Executive Summary
        self.add_executive_summary()
        
        # Exchange Rate Validation
        self.add_exchange_rate_section()
        
        # Coverage Analysis
        self.add_coverage_section()
        
        # Data Quality Metrics
        self.add_quality_metrics()
        
        # Recommendations
        self.add_recommendations()
        
        # Build PDF
        doc.build(self.story)
    
    def add_exchange_rate_section(self):
        """Add exchange rate validation section."""
        
        self.story.append(Paragraph(
            "Exchange Rate Validation",
            self.styles['Heading1']
        ))
        
        # Create comparison table
        data = [
            ['Zone', 'Expected Range', 'Actual Range', 'Status'],
            ['Northern (Houthi)', '500-600', '515-545', '✓ Valid'],
            ['Southern (Government)', '1800-2200', '1950-2150', '✓ Valid'],
            ['Contested', '800-1500', '850-1450', '✓ Valid']
        ]
        
        table = Table(data)
        self.story.append(table)
        
        # Add chart
        self._create_exchange_rate_chart()
        self.story.append(Image('temp/exchange_rate_trends.png', width=400, height=300))
```

## Implementation Timeline & Milestones

### Week 1 Milestones
- [ ] Exchange rates correctly extracted from WFP data
- [ ] Zone classifications mapped properly  
- [ ] USD conversions validated against WFP data
- [ ] Impact assessment report generated

### Week 2 Milestones
- [ ] CurrencyAwareWFPProcessor fully configured
- [ ] Validation layer implemented and running
- [ ] Commodity coverage expanded to include wages, fuel, livestock
- [ ] Data quality dashboard operational

### Week 3 Milestones
- [ ] Multi-source validation implemented
- [ ] Monitoring dashboard deployed
- [ ] Anomaly detection system active
- [ ] Alert system configured

### Week 4 Milestones
- [ ] Labor market analysis completed
- [ ] Energy price transmission studied
- [ ] Value chain analysis implemented
- [ ] Comprehensive report published

## Risk Mitigation

### Rollback Plan
1. Keep backup of current data: `data/processed/backup_20250106/`
2. Version control all changes with clear commit messages
3. Test each change on subset before full implementation
4. Maintain ability to revert to WFP's USD prices if needed

### Testing Strategy
1. Unit tests for each new function
2. Integration tests for full pipeline
3. Regression tests to ensure existing functionality preserved
4. Validation against known good outputs

### Documentation Requirements
1. Update all function docstrings
2. Create data dictionary for new fields
3. Document all assumptions and decisions
4. Maintain change log with impacts

## Success Metrics

### Immediate (Week 1)
- Exchange rate accuracy: >95% match with source data
- Zone classification: <1% unknown classifications  
- USD conversion: <5% discrepancy with WFP calculations

### Short-term (Week 2-3)
- Data coverage: Maintain >88.4% target
- Quality score: >85% on all metrics
- Processing time: <30 minutes for full pipeline
- Alert response time: <5 minutes for critical issues

### Long-term (Week 4+)
- Analysis reproducibility: 100%
- Cross-source validation: >90% agreement
- Anomaly detection accuracy: >85% true positive rate
- Documentation completeness: 100%

## Next Steps After Implementation

1. **Re-run all analyses** with corrected data
2. **Update all results** in deliverables/
3. **Revise findings** about Yemen Price Paradox
4. **Notify stakeholders** of data corrections
5. **Publish methodology** improvements
6. **Create replication** package with fixes

---

*This plan addresses critical data quality issues that fundamentally affect the Yemen Market Integration analysis. Implementation should begin immediately with Week 1 tasks as these invalidate all current results.*