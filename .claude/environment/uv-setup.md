# UV Environment Configuration for Claude Code

## Quick Start Commands

```bash
# Initial setup (run once)
uv sync --extra dev

# Daily usage
uv run python script.py
uv run pytest tests/
uv run jupyter notebook
```

## Environment Status Check

```bash
# Verify UV installation
uv --version

# Verify project environment
uv run python -c "import sys; print(f'Python: {sys.version}'); print(f'Location: {sys.executable}')"

# Check key packages
uv run python -c "import pandas, numpy, statsmodels; print('✅ Core packages available')"
```

## Package Management

```bash
# Add packages
uv add package_name
uv add --dev dev_package_name

# Remove packages
uv remove package_name

# Update dependencies
uv sync
```

## Troubleshooting

### Common Issues

**"ModuleNotFoundError"**
```bash
uv sync --extra dev
```

**"Virtual environment not found"**
```bash
# UV creates .venv automatically
uv sync
```

**"requirements.txt not found"**
```bash
# This is expected - use UV instead
uv sync
```

### Environment Reset

If you need to reset the environment:
```bash
rm -rf .venv
uv sync --extra dev
```

## Directory Structure

- `.venv/` - Virtual environment (auto-created by UV)
- `pyproject.toml` - Main dependency configuration
- `uv.lock` - Locked dependency versions
- `archive/deprecated-2025-06-05/requirements.txt` - Archived pip file

## Best Practices

1. **Always use `uv run` prefix** for Python commands
2. **Never activate .venv manually** - UV handles this
3. **Use `uv add/remove`** for package management
4. **Run `uv sync`** after pulling changes
5. **Check `uv.lock`** into version control