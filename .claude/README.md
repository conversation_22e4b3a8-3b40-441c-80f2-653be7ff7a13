# Claude AI Project Configuration
## Yemen Market Integration Research Methodology

### Directory Structure

```
.claude/
├── prompts/                    # Specialized prompts for different contexts
│   ├── methodological-transformation.md
│   └── perplexity-ai-optimization.md
├── context/                    # Project context and background information
│   └── project-overview.md
├── workflows/                  # Process documentation and procedures
│   └── quality-assurance.md
├── templates/                  # Reusable templates and frameworks
│   └── master-document-template.md
└── README.md                   # This file
```

### Purpose and Usage

This `.claude/` directory contains specialized configuration files designed to optimize Claude AI interactions for the Yemen Market Integration research methodology project. The structure follows best practices for AI-assisted academic research projects while maintaining World Bank publication standards.

### File Descriptions

#### Prompts Directory
- **`methodological-transformation.md`**: Comprehensive prompt for enhancing the three-tier econometric framework with advanced methods (ML clustering, regime-switching, nowcasting)
- **`perplexity-ai-optimization.md`**: Specialized prompt for optimizing content deployment to Perplexity AI Spaces with strategic consolidation and search optimization

#### Context Directory
- **`project-overview.md`**: Complete project context including currency conversion requirements, methodology framework, and implementation priorities

#### Workflows Directory
- **`quality-assurance.md`**: Comprehensive quality assurance workflow ensuring World Bank publication standards, academic rigor, and policy relevance

#### Templates Directory
- **`master-document-template.md`**: Standardized template for creating consolidated master documents optimized for AI processing and user accessibility

### Usage Guidelines

#### For Claude Interactions
1. **Reference Appropriate Prompt**: Use specific prompts based on the task context
2. **Maintain Context**: Always consider the project overview when working on any component
3. **Follow Workflows**: Adhere to quality assurance procedures for all content creation
4. **Use Templates**: Apply standardized templates for consistent document structure

#### For Project Development
1. **Academic Standards**: All work must meet World Bank research quality standards
2. **Methodological Focus**: Emphasize proper currency conversion in multi-exchange rate environments
3. **Policy Relevance**: Ensure all technical components connect to humanitarian programming
4. **Quality Assurance**: Implement continuous validation and compliance monitoring

### Key Project Principles

#### Academic Excellence
- **World Bank Publication Standards**: All content must meet flagship research quality
- **Peer Review Readiness**: Documentation suitable for top-tier economics journals
- **Methodological Rigor**: Comprehensive validation and robustness testing
- **External Validation**: Cross-country confirmation across Syria, Lebanon, Somalia

#### Technical Innovation
- **Three-Tier Enhancement**: Advance existing framework with ML, regime-switching, nowcasting
- **Real-Time Capabilities**: Implement operational early warning and monitoring systems
- **Performance Optimization**: Achieve <5 second response time for complex operations
- **AI Integration**: Optimize for both Claude Code and Perplexity AI deployment

#### Policy Impact
- **Humanitarian Programming**: 40-60% improvement in aid effectiveness
- **Evidence-Based Programming**: Clear linkage from research to operational recommendations
- **Institutional Adoption**: Integration potential with World Bank, WFP, OCHA
- **Measurable Outcomes**: Quantifiable benefits and success metrics

### Integration with Project Structure

#### Core Methodology
- **Primary Focus**: `docs/research-methodology-package/` (213 files, 2.6MB)
- **Enhancement Target**: Transform into world-class econometric masterpiece
- **Quality Standard**: Maintain World Bank publication readiness throughout

#### Deployment Optimization
- **Perplexity AI Preparation**: `perplexity-ai-spaces-preparation/` (11 files, 100KB)
- **Strategic Enhancement**: `docs/05-methodology/strategic-documents/yemen-methodological-transformation-strategy.md`
- **Implementation Guide**: `CLAUDE_CODE_IMPLEMENTATION_PROMPT.md`

#### Project Coordination
- **Main Documentation**: `CLAUDE.md` (project overview and guidelines)
- **Quality Assurance**: Automated validation and compliance monitoring
- **Cross-Reference System**: Comprehensive internal linking and navigation

### Best Practices

#### Content Creation
1. **Use Appropriate Prompts**: Select context-specific prompts for optimal results
2. **Follow Templates**: Apply standardized structures for consistency
3. **Maintain Quality**: Adhere to academic standards and validation procedures
4. **Methodological Rigor**: Ensure proper currency conversion methodology is applied

#### Quality Assurance
1. **Continuous Validation**: Regular checking of academic standards and technical accuracy
2. **Cross-Reference Integrity**: Maintain navigation system functionality
3. **Performance Monitoring**: Track AI processing efficiency and user experience
4. **Impact Assessment**: Measure humanitarian programming effectiveness improvements

#### Collaboration
1. **Context Sharing**: Use project overview for consistent understanding
2. **Workflow Adherence**: Follow established procedures for quality assurance
3. **Template Usage**: Apply standardized formats for document creation
4. **Feedback Integration**: Incorporate improvements based on usage experience

### Success Metrics

#### Academic Achievement
- Economics journal publication readiness
- World Bank research standard compliance
- Comprehensive external validation across multiple countries
- Advancement in conflict economics methodology

#### Technical Excellence
- <5 second response time for complex econometric operations
- Seamless AI integration and processing optimization
- Comprehensive quality assurance and validation protocols
- Real-time monitoring and early warning capabilities

#### Policy Transformation
- 40-60% improvement in humanitarian programming effectiveness
- Evidence-based programming with measurable outcomes
- Institutional adoption by major development organizations
- Operational early warning and decision support systems

This configuration ensures optimal Claude AI assistance while maintaining the academic rigor and methodological soundness of the Yemen Market Integration research project.
