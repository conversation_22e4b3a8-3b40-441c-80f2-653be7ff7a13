# Data Pipeline V2 Implementation Handover Prompt

## 🚨 CRITICAL CONTEXT

You are taking over a **MAJOR DATA INFRASTRUCTURE OVERHAUL** that is currently on Day 1 of a 20-day implementation plan. The project is transforming from a collection of scripts to a production-ready, CLI-driven data pipeline integrating 10+ missing data sources.

## 📍 Current Status: Day 1 of 20 (June 7, 2025)

### What Has Been Completed Today
1. **Planning & Architecture** ✅
   - Comprehensive 455-line implementation plan
   - Technical blueprint with detailed design
   - 20-day roadmap with daily milestones
   - Progress tracking system established

2. **Core Infrastructure Started** ✅
   - BaseProcessor framework (async, retry, validation)
   - ValidationFramework with multi-level validators
   - CacheManager with TTL and size management
   - HDXEnhancedClient for all data sources

3. **First Domain Implementation** ✅
   - Conflict domain entities and value objects
   - ConflictProcessor with spatial/temporal calculations
   - Ready for integration testing

### Your Immediate Tasks (Day 1 Completion)

```bash
# 1. Complete domain setup (30 minutes)
mkdir -p src/core/domain/{aid,climate,infrastructure,panel}/{entities,value_objects}
touch src/core/domain/{aid,climate,infrastructure,panel}/__init__.py

# 2. Check what's been implemented
find src/core/domain -name "*.py" | head -20
find src/infrastructure -name "*.py" | head -20
```

### Day 1 Remaining Tasks (Your Focus)

1. **Implement ACLED Client** (1 hour)
   ```python
   # Create src/infrastructure/external_services/acled_client.py
   # - API authentication with retry
   # - Date range queries  
   # - Response parsing to ConflictEvent entities
   # - Rate limiting and backoff
   ```

2. **Create Spatial Integration Service** (2 hours)
   ```python
   # Create src/infrastructure/spatial/spatial_integration_service.py
   # - Point-in-polygon for market-zone mapping
   # - Buffer calculations (10km, 25km, 50km)
   # - CRS transformations (WGS84 ↔ local)
   # - Spatial indices for performance
   ```

3. **Build Temporal Alignment Service** (1 hour)
   ```python
   # Create src/infrastructure/temporal/temporal_alignment_service.py
   # - Monthly aggregation (anchored on 15th)
   # - Flow vs stock data handling
   # - Missing period detection
   # - Interpolation strategies
   ```

4. **Write Unit Tests** (1 hour)
   ```bash
   # Create test files
   tests/infrastructure/test_base_processor.py
   tests/infrastructure/test_validation_framework.py
   tests/infrastructure/test_cache_manager.py
   tests/infrastructure/test_hdx_enhanced_client.py
   ```

## 📋 Key Documents to Read

1. **Start Here**: `docs/DATA_PIPELINE_V2_ROADMAP.md` - Your daily guide
2. **Architecture**: `docs/DATA_PIPELINE_V2_IMPLEMENTATION_BLUEPRINT.md`
3. **Full Plan**: `docs/DATA_PIPELINE_V2_COMPREHENSIVE_PLAN.md`
4. **Progress**: `docs/DATA_PIPELINE_V2_PROGRESS.md`

## 🎯 Critical Requirements

### Architecture Principles
- **Async-First**: All I/O operations use asyncio
- **Domain Boundaries**: Each data source has its own bounded context
- **Validation Layers**: Schema → Constraint → Statistical → Business
- **Zero Error Tolerance**: Fail fast with clear messages
- **Progress Tracking**: Real-time updates for long operations

### Data Sources to Integrate (Priority Order)
1. ✅ ACLED Conflict Data (processor done, need client)
2. ⏳ OCHA Aid Distribution (3W, FTS, Cash)
3. ⏳ Climate Data (Rainfall, NDVI, Temperature)
4. ⏳ Population Data (WorldPop, DTM)
5. ⏳ Infrastructure (OSM, Roads)
6. ⏳ Global Prices (FAO, World Bank)
7. ⏳ Market Characteristics
8. ⏳ IPC Food Security
9. ⏳ Administrative Boundaries
10. ⏳ Control Zones (ACAPS)

### Success Metrics
- **Today**: Complete Day 1 tasks with 90% test coverage
- **Week 1**: All infrastructure components working
- **Week 2**: All data processors implemented
- **Week 3**: Full integration and CLI complete
- **Week 4**: Production deployment with <30min processing

## 🚀 Your First Commands

```bash
# 1. Check current implementation
cat docs/DATA_PIPELINE_V2_PROGRESS.md
cat .claude/implementation/current-status.md

# 2. See what's already built
ls -la src/infrastructure/processors/
ls -la src/infrastructure/data_quality/
ls -la src/core/domain/

# 3. Run existing tests
uv run pytest tests/ -v --tb=short

# 4. Start implementing
code src/infrastructure/external_services/acled_client.py
```

## ⚠️ Critical Warnings

1. **NO SHORTCUTS**: Each component depends on previous ones
2. **TEST EVERYTHING**: 90% coverage minimum
3. **VALIDATE CONSTANTLY**: Use ValidationFramework
4. **DOCUMENT DECISIONS**: Update decision-log.md
5. **TRACK PROGRESS**: Update status files daily

## 🔧 Technical Setup

```bash
# Ensure environment ready
uv sync --extra dev

# Required environment variables
export ACLED_API_KEY="your_key_here"
export ACLED_EMAIL="<EMAIL>"
export HDX_API_KEY="your_hdx_key"
```

## 📊 Day 2 Preview

After completing Day 1:
- Complete BaseProcessor documentation
- Implement DataFrameProcessor tests
- Create GeoDataProcessor for spatial data
- Build processor factory pattern
- Enhance error handling

## 🎯 Remember

This is a **4-week effort** to transform the project's data capabilities. You're on Day 1 of 20. Each day builds on the previous - maintain quality and momentum!

The goal: **Zero manual scripts, 100% automation, production-ready quality**

Good luck! The foundation is solid - now execute with precision. 🚀