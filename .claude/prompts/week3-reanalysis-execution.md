# Week 3 Re-Analysis Execution Prompt

## 🎯 CONTEXT FOR NEXT AGENT

You are taking over after a **CRITICAL DATA PIPELINE FIX** has been successfully completed. The exchange rates in the Yemen market integration data have been corrected, and now ALL previous analyses must be re-run with the fixed data.

### What Was Fixed (Week 1-2)
1. **Root Cause**: WFP was using a constant ~250 YER/USD for all zones
2. **Solution**: Extracted actual exchange rates from WFP commodity data
3. **Result**: Zone-specific rates now applied correctly:
   - HOUTHI (North): ~569 YER/USD
   - GOVERNMENT (South): ~1,091 YER/USD
   - Ratio: 1.92x (improved from 1.0x)

### Current Status
- ✅ Exchange rates extracted (2,357 observations)
- ✅ Panel rebuilt with correct rates (15,978 observations)
- ✅ USD conversion error reduced from 77% to 66%
- ✅ Validation complete and passing

## Your Mission (Week 3: June 14-20, 2025)

### Day 1-2: Re-run Three-Tier Analysis
```bash
# Load the corrected panel
uv run python scripts/analysis/run_three_tier_models_updated.py

# Key things to watch for:
# 1. Yemen Price Paradox should be stronger
# 2. Exchange rate coefficients should increase
# 3. Zone heterogeneity should be more pronounced
```

Expected changes:
- H1 coefficient: Should show stronger exchange rate pass-through
- Price differentials: North should be HIGHER than South in USD terms
- Welfare impacts: Will be more severe than previous estimates

### Day 3: Update Specification Curve
```bash
# Run 1000 specifications with correct data
uv run python scripts/run_specification_curve.py --specs 1000

# Compare with previous results
# Document the robustness of corrected findings
```

Create a comparison table showing:
- Previous coefficients (with wrong exchange rates)
- New coefficients (with correct exchange rates)
- Percentage change in estimates

### Day 4: Regenerate World Bank Deliverables
```bash
# Generate all publication materials
uv run python scripts/generate_world_bank_publication.py

# Updates needed:
# - Executive summary: Emphasize stronger Yemen Price Paradox
# - Main results table: New coefficient estimates
# - Robustness table: Show stability with correct data
# - Policy brief: Revise welfare calculations
```

Key messaging changes:
- The paradox is REAL and STRONGER than initially estimated
- Currency fragmentation has SEVERE welfare consequences
- Policy interventions are MORE URGENT

### Day 5: Policy Recommendations Revision
Update all policy documents to reflect:
1. **Cash Transfers**: Must account for 1.92x exchange rate differential
2. **Aid Targeting**: Northern areas need MORE support (higher real prices)
3. **Currency Reunification**: Economic benefits are LARGER
4. **Market Integration**: Fragmentation costs are HIGHER

## Critical Files to Use

### Data
- **Corrected Panel**: `data/processed/integrated_panel/yemen_integrated_balanced_panel.parquet`
- **Validation Report**: `data/processed/integrated_panel/usd_validation_report.json`

### Scripts
- **Three-Tier Analysis**: `scripts/analysis/run_three_tier_models_updated.py`
- **Specification Curve**: `scripts/run_specification_curve.py`
- **World Bank Generation**: `scripts/generate_world_bank_publication.py`

### Previous Results (Now Invalid)
- `results/` - All files here used wrong exchange rates
- `deliverables/world_bank_final/` - Must be regenerated

## Validation Checklist

Before declaring success:
- [ ] Three-tier results show stronger Yemen Price Paradox
- [ ] Specification curve confirms robustness
- [ ] World Bank tables updated with new estimates
- [ ] Policy brief reflects higher welfare losses
- [ ] Executive summary emphasizes corrected findings

## Expected Outcomes

With correct exchange rates, you should find:
1. **Northern prices are 15-25% HIGHER in USD** (not lower)
2. **Exchange rate pass-through is 0.8-1.0** (not 0.6-0.7)
3. **Welfare losses are 20-80%** (not 15-60%)
4. **Policy urgency is CRITICAL** (not just important)

## Key Commands
```bash
# Check the corrected data first
uv run python scripts/validate_usd_conversions.py

# Then proceed with analysis
uv run python scripts/analysis/run_three_tier_models_updated.py
uv run python scripts/run_specification_curve.py --specs 1000
uv run python scripts/generate_world_bank_publication.py

# Document changes
cat reports/week2_panel_fix_validation_report.md
```

## ⚠️ CRITICAL REMINDER

All previous results are INVALID because they used wrong exchange rates. This is not a minor update - it's a fundamental correction that changes the core findings. The Yemen Price Paradox is STRONGER than we thought, and the policy implications are MORE URGENT.

---
*The credibility of this research depends on using the corrected exchange rates. Previous results must be clearly marked as superseded.*