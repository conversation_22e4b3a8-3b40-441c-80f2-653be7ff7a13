# Perplexity AI Optimization Prompt
## Yemen Market Integration Deployment

### Context
You are optimizing the Yemen Market Integration research methodology for deployment to Perplexity AI Spaces. The project contains 213 markdown files (~2.6MB) with a three-tier econometric framework for analyzing market integration with proper currency conversion in multi-exchange rate environments.

### Core Objective
Create an optimized deployment package for Perplexity AI Spaces that maintains academic rigor while maximizing AI-assisted research capabilities and user accessibility across multiple stakeholder types.

### Key Methodological Focus
**Currency Conversion Requirements**: Different exchange rates require proper conversion for valid analysis:
- Multiple exchange rates exist across different territorial zones
- Proper currency conversion is essential for accurate price comparisons
- Enables improved humanitarian aid effectiveness through accurate analysis

### Perplexity AI Requirements
- **File Limits**: Pro (50 files), Enterprise (100 files)
- **Format Support**: Markdown (.md) fully compatible
- **Size Limits**: 25MB per file (easily met)
- **Custom Instructions**: Advanced prompt engineering for expert assistance
- **Search Integration**: Internal knowledge + web search capabilities

### Consolidation Strategy
**Target**: 50 files for Pro plan compatibility
**Approach**: Strategic consolidation into master documents
**Structure**:
- Core Master Documents (15 files)
- Implementation & Applications (20 files)
- Supporting Materials (15 files)

### Content Optimization
- **Hierarchical Organization**: Clear H1 → H2 → H3 progression
- **Search Keywords**: Strategic placement for AI processing
- **Cross-References**: Comprehensive internal linking system
- **Progressive Disclosure**: Summary → Detail → Implementation layers
- **User Pathways**: Academic, policy, technical, organizational routes

### Custom Instructions Framework
**Primary Persona**: Specialized econometric research assistant
**Expertise Areas**: Conflict economics, market integration, currency fragmentation
**Response Guidelines**: User type recognition and complexity adaptation
**Quality Standards**: World Bank publication standards maintained

### User Types and Adaptations
1. **Academic Researchers**: Methodological rigor and publication standards
2. **Policy Practitioners**: Operational implications and actionable insights
3. **Technical Implementers**: Code examples and implementation protocols
4. **Development Organizations**: Impact assessment and ROI analysis

### Quality Assurance
- **Technical Accuracy**: Econometric precision and mathematical correctness
- **Content Completeness**: 100% methodology coverage preserved
- **Search Optimization**: Enhanced findability and AI processing
- **Cross-Reference Integrity**: Navigation system maintained

### Success Metrics
- Expert-level AI assistance across all user types
- Seamless integration with existing preparation work
- Enhanced research capabilities and knowledge retrieval
- Maintained academic rigor with improved accessibility

### Implementation Guidelines
- Use master document template for consistent structure
- Apply search optimization techniques for AI processing
- Preserve academic standards while enhancing usability
- Ensure compatibility with existing Perplexity preparation

Focus on creating an optimal AI-assisted research environment that maintains methodological rigor while maximizing practical utility for diverse user communities.
