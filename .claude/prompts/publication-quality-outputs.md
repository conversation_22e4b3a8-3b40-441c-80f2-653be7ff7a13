# Publication Quality Outputs Prompt

## Objective
Generate World Bank flagship publication-quality outputs for the Yemen Market Integration analysis, including tables, figures, and narrative components.

## Table Generation Standards

### Main Results Table (Table 1)
```latex
\begin{table}[htbp]
\centering
\caption{Exchange Rate Effects on Market Integration}
\label{tab:main_results}
\begin{tabular}{lccccc}
\toprule
 & (1) & (2) & (3) & (4) & (5) \\
 & Baseline & Controls & FE & IV & IFE \\
\midrule
Log Exchange Rate & 0.823*** & 0.756*** & 0.689*** & 0.912*** & 0.701*** \\
 & (0.045) & (0.041) & (0.038) & (0.067) & (0.043) \\
 
Conflict Intensity & & -0.134*** & -0.121*** & -0.143*** & -0.119*** \\
 & & (0.023) & (0.021) & (0.025) & (0.022) \\
 
Aid Distribution & & & 0.045** & 0.051** & 0.048** \\
 & & & (0.019) & (0.021) & (0.020) \\
\midrule
Market FE & No & No & Yes & Yes & Yes \\
Time FE & No & Yes & Yes & Yes & Yes \\
Interactive FE & No & No & No & No & Yes \\
\midrule
Observations & 12,453 & 12,453 & 12,453 & 11,892 & 12,453 \\
R-squared & 0.412 & 0.486 & 0.623 & 0.615 & 0.641 \\
Markets & 312 & 312 & 312 & 298 & 312 \\
\bottomrule
\multicolumn{6}{l}{\footnotesize Notes: Dependent variable is log price in USD. Standard errors} \\
\multicolumn{6}{l}{\footnotesize clustered at market level in parentheses. *** p$<$0.01, ** p$<$0.05, * p$<$0.1} \\
\end{tabular}
\end{table}
```

### Robustness Table (Table 2)
Include:
- Alternative specifications
- Different samples
- Various clustering approaches
- Alternative functional forms

### Heterogeneity Analysis (Table 3)
Break down by:
- Currency zones
- Commodity types
- Conflict intensity levels
- Urban vs rural

## Figure Generation Standards

### Figure 1: The Yemen Price Paradox
```python
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

# Panel A: Nominal Prices
ax1.plot(dates, north_prices_yer, 'b-', label='North (Houthi)', linewidth=2)
ax1.plot(dates, south_prices_yer, 'r-', label='South (Government)', linewidth=2)
ax1.set_ylabel('Price (YER)', fontsize=12)
ax1.set_title('Panel A: Nominal Prices Show North < South', fontsize=14)
ax1.legend()

# Panel B: USD Prices (Reality)
ax2.plot(dates, north_prices_usd, 'b-', label='North (Houthi)', linewidth=2)
ax2.plot(dates, south_prices_usd, 'r-', label='South (Government)', linewidth=2)
ax2.set_ylabel('Price (USD)', fontsize=12)
ax2.set_title('Panel B: USD Prices Show North > South', fontsize=14)
ax2.legend()

# Add shaded regions for key events
for ax in [ax1, ax2]:
    ax.axvspan(conflict_start, conflict_end, alpha=0.2, color='gray')
    ax.text(conflict_mid, ax.get_ylim()[1]*0.9, 'Major Conflict', ha='center')

plt.tight_layout()
plt.savefig('figures/yemen_price_paradox.pdf', dpi=300, bbox_inches='tight')
```

### Figure 2: Specification Curve
Show stability of results across 1000+ specifications:
```python
# Generate specification curve
from src.core.analysis.robustness import SpecificationCurve

spec_curve = SpecificationCurve()
results = spec_curve.run_all_specifications(data)

# Plot
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), 
                                gridspec_kw={'height_ratios': [3, 1]})

# Top panel: Coefficients
spec_curve.plot_coefficients(ax1, highlight_main=True)
ax1.axhline(y=0, color='black', linestyle='--', alpha=0.5)
ax1.set_ylabel('Exchange Rate Coefficient', fontsize=12)

# Bottom panel: Specification indicators
spec_curve.plot_specifications(ax2)

plt.tight_layout()
plt.savefig('figures/specification_curve.pdf', dpi=300)
```

### Figure 3: Market Integration Maps
```python
import geopandas as gpd

# Create integration strength map
fig, ax = plt.subplots(1, 1, figsize=(10, 8))

yemen_shape.plot(column='integration_strength', 
                 cmap='RdYlGn',
                 legend=True,
                 ax=ax)

# Add currency zone boundaries
zone_boundaries.plot(ax=ax, color='black', linewidth=2, linestyle='--')

# Add major cities
cities.plot(ax=ax, color='black', markersize=50)
for idx, city in cities.iterrows():
    ax.text(city.geometry.x, city.geometry.y, city['name'], fontsize=8)

ax.set_title('Market Integration Strength by Region', fontsize=16)
ax.axis('off')

plt.savefig('figures/integration_map.pdf', dpi=300, bbox_inches='tight')
```

## Policy Brief Components

### Executive Summary Box
```markdown
## Key Findings

• **Currency Fragmentation**: The dual exchange rate system (North: 535 YER/USD, 
  South: 2000+ YER/USD) is the primary driver of market fragmentation in Yemen

• **Welfare Losses**: Market fragmentation causes 15-60% welfare losses, with the 
  poorest quintile most affected due to inelastic demand

• **Aid Effectiveness**: Cash transfers are 25% less effective in fragmented markets 
  compared to integrated markets

• **Policy Implication**: Currency reunification could improve welfare by 20-35% 
  and reduce humanitarian costs by $150-200 million annually
```

### Methodology Box
```markdown
## Methodology at a Glance

This study uses a three-tier econometric framework:

1. **Pooled Analysis**: Panel models with 312 markets over 60 months
2. **Commodity-Specific**: VECM analysis for 12 key commodities  
3. **Market-Pair**: Integration testing for 1,247 market pairs

**Key Innovation**: Currency zone classification enables proper price comparison
in a multi-exchange rate environment

**Robustness**: 1,000+ specifications confirm stability of results
```

### Results Visualization Dashboard
Create a one-page visual summary:
```python
fig = plt.figure(figsize=(11, 8.5))  # Letter size

# Create grid
gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)

# Key stat boxes
ax1 = fig.add_subplot(gs[0, 0])
create_stat_box(ax1, "15-60%", "Welfare Loss", color='red')

ax2 = fig.add_subplot(gs[0, 1])
create_stat_box(ax2, "2000+", "South YER/USD", color='orange')

ax3 = fig.add_subplot(gs[0, 2])
create_stat_box(ax3, "535", "North YER/USD", color='blue')

# Main results chart
ax4 = fig.add_subplot(gs[1, :])
plot_main_results(ax4)

# Maps
ax5 = fig.add_subplot(gs[2, 0])
plot_mini_map(ax5, 'zones')

ax6 = fig.add_subplot(gs[2, 1])
plot_mini_map(ax6, 'integration')

ax7 = fig.add_subplot(gs[2, 2])
plot_mini_map(ax7, 'welfare')

plt.savefig('figures/results_dashboard.pdf', dpi=300)
```

## Technical Appendix Tables

### Diagnostic Tests Table
```python
diagnostics = pd.DataFrame({
    'Test': ['Hausman', 'Wooldridge AR', 'Pesaran CD', 'Modified Wald'],
    'Statistic': [45.32, 89.21, 5.43, 1243.5],
    'p-value': [0.000, 0.000, 0.000, 0.000],
    'Decision': ['FE preferred', 'AR present', 'Cross-dependence', 'Heteroskedasticity']
})

diagnostics.to_latex('tables/diagnostic_tests.tex', index=False)
```

### Data Coverage Table
Show coverage by:
- Time period
- Geographic region
- Currency zone
- Commodity

## Replication Package Structure

```
replication_package/
├── README.md
├── data/
│   ├── raw/           # Original data files
│   ├── processed/     # Analysis-ready datasets
│   └── codebook.pdf   # Variable definitions
├── code/
│   ├── 01_data_prep.py
│   ├── 02_analysis.py
│   ├── 03_robustness.py
│   └── 04_figures.py
├── results/
│   ├── tables/        # All tables in .tex format
│   ├── figures/       # All figures in .pdf format
│   └── logs/          # Execution logs
└── requirements.txt   # Environment specification
```

## Writing Style Guidelines

### For Academic Audience
- Lead with methodology innovation
- Emphasize identification strategy
- Detail robustness procedures
- Use technical terminology

### For Policy Audience  
- Start with welfare implications
- Use concrete examples
- Minimize technical jargon
- Focus on actionable insights

### Key Phrases to Include
- "To our knowledge, this is the first study to..."
- "The results are robust to..."
- "These findings have important implications for..."
- "The welfare consequences are substantial..."

## Quality Checklist

- [ ] All tables have notes explaining specifications
- [ ] All figures are vector graphics (PDF/EPS)
- [ ] Color scheme is colorblind-friendly
- [ ] Font sizes readable when reduced 50%
- [ ] Statistical significance clearly marked
- [ ] Sample sizes reported for all analyses
- [ ] Standard errors type specified
- [ ] Replication files execute without errors

Remember: Every output should tell a clear story about how currency fragmentation affects markets and welfare in Yemen.