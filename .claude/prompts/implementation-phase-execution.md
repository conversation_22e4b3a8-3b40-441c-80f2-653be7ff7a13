# Implementation Phase Execution Prompt

## Context

You are working on the Yemen Market Integration project, implementing advanced econometric methods for World Bank-standard analysis. The project is currently in Phase 1/2 transition of a 12-week implementation plan.

## Critical Requirements

1. **Currency Conversion**: ALL prices must be converted to USD before ANY analysis
2. **Zone Classification**: Every market must be mapped to a currency zone (HOUTHI ~535 YER/USD, GOVERNMENT ~2000 YER/USD)
3. **Statistical Rigor**: Bonferroni correction for H1-H5 (α=0.01), <PERSON><PERSON><PERSON><PERSON><PERSON> for H6-H10
4. **Data Coverage**: Target 88.4% completeness with conflict-aware imputation
5. **Validation First**: Every analysis must pass MethodologyValidator before proceeding

## Your Current Task

Based on the current phase indicated in `.claude/implementation/current-status.md`, you should:

### If in Phase 1 (Weeks 1-2):
1. **Fix Tier Runner Validation**
   - Add MethodologyValidator calls to all tier runners
   - Ensure MethodologyViolation exceptions propagate correctly
   - Test that invalid data cannot proceed to analysis

2. **Resolve Data Access**
   - Fix HDX authentication issues
   - Validate WFP exchange rate coverage
   - Create fallback manual download scripts

### If in Phase 2 (Weeks 3-6):
1. **Implement ML Clustering**
   - Create CurrencyAwareMarketClustering class
   - Respect currency zone boundaries
   - Integrate with Tier 1 analysis

2. **Add Advanced Methods**
   - Interactive Fixed Effects (IFE) using linearmodels
   - Bayesian panel models with PyMC
   - Nowcasting framework for real-time predictions

### If in Phase 3 (Weeks 7-10):
1. **Robustness Testing**
   - Run specification curve analysis (1000+ models)
   - Implement cross-country validation
   - Generate publication-quality outputs

2. **World Bank Deliverables**
   - LaTeX tables with proper formatting
   - Policy brief with welfare calculations
   - Replication package preparation

### If in Phase 4 (Weeks 11-12):
1. **Operational Deployment**
   - Set up monitoring dashboards
   - Create early warning systems
   - Complete documentation

## Code Standards

```python
# ALWAYS validate before analysis
validator = MethodologyValidator()
is_valid, report = validator.validate_analysis_inputs(
    observations=panel_data,
    analysis_type=AnalysisType.PANEL_ANALYSIS,
    hypothesis_tests=command.hypotheses
)

if not is_valid:
    raise MethodologyViolation(f"Validation failed: {report.critical_failures}")

# ALWAYS use type hints
def calculate_differential(
    price_north: Decimal,
    price_south: Decimal,
    rate_north: Decimal,
    rate_south: Decimal
) -> Dict[str, Decimal]:
    """Calculate with proper currency conversion."""
    pass

# ALWAYS include comprehensive docstrings
"""
Args:
    panel_data: Must include price_usd, currency_zone
Returns:
    Results with validation report
Raises:
    MethodologyViolation: If validation fails
"""
```

## Testing Requirements

- Write tests BEFORE implementation
- Maintain >95% coverage
- Test both success and failure cases
- Include methodology compliance tests

## Progress Tracking

After completing tasks:
1. Update `.claude/implementation/current-status.md`
2. Log decisions in `.claude/memory/decision-log.md`
3. Update next actions in `.claude/memory/next-actions.md`
4. Run validation suite to ensure compliance

## Key Files to Reference

- Methodology: `docs/research-methodology-package/10-context-for-implementation/METHODOLOGY_TO_CODE_MAPPING.md`
- Validation: `.claude/validation/econometric-checklist.md`
- Standards: `.claude/standards/production-quality.md`
- Progress: `.claude/implementation/phase*.md`

## Remember

1. Currency conversion is NOT optional
2. Exchange rates are zone-specific
3. Missing data patterns are conflict-driven
4. World Bank standards throughout
5. Document all assumptions

When you start, first check the current status and identify the most critical task to work on. Focus on one task at a time, complete it thoroughly with tests, then move to the next.