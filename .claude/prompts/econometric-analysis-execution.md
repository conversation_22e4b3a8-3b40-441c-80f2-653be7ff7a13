# Econometric Analysis Execution Prompt

## You are conducting World Bank-standard econometric analysis on Yemen market data

### Core Objective
Analyze market integration patterns in Yemen, focusing on how currency fragmentation (Northern ~535 YER/USD vs Southern ~2000 YER/USD) affects price dynamics and market efficiency.

## Pre-Analysis Checklist

Before ANY analysis:
```python
# 1. Validate currency conversion
assert all(df['price_usd'].notna()), "Missing USD prices"
assert all(df['currency_zone'].isin(['HOUTHI', 'GOVERNMENT', 'CONTESTED'])), "Invalid zones"
assert all(df['exchange_rate_used'] > 0), "Invalid exchange rates"

# 2. Run methodology validator
from src.core.validation.methodology_validator import MethodologyValidator
validator = MethodologyValidator()
is_valid, report = validator.validate_analysis_inputs(df)
if not is_valid:
    raise MethodologyViolation(f"Cannot proceed: {report.critical_failures}")

# 3. Check statistical power
n_markets = df['market_id'].nunique()
n_periods = df['date'].nunique()
n_obs = len(df)
assert n_markets >= 300 and n_periods >= 36 and n_obs >= 10000
```

## Hypothesis Testing Framework

### Primary Hypotheses (H1-H5) - Bonferroni α=0.01

#### H1: Exchange Rate Mechanism
```python
# Test: Higher exchange rates → higher USD prices
model_h1 = PanelOLS(
    dependent=df['log_price_usd'],
    exog=df[['log_exchange_rate', 'conflict_intensity', 'controls']],
    entity_effects=True,
    time_effects=True
)
results_h1 = model_h1.fit(cov_type='clustered', cluster_entity=True)

# Apply Bonferroni correction
from src.core.models.hypothesis_testing.multiple_testing_framework import MultipleTestingFramework
mtf = MultipleTestingFramework()
adjusted_results = mtf.adjust_pvalues(
    p_values={'H1': results_h1.pvalues['log_exchange_rate']},
    method='bonferroni',
    alpha=0.01
)
```

#### H2: Aid Distribution Effects
- Test whether aid to Government zones increases price gaps
- Control for conflict intensity and seasonal patterns

#### H3: Conflict and Demand
- Negative relationship between conflict and price differentials
- Use spatial lags for conflict spillovers

#### H4: Administrative Boundaries
- Regression discontinuity at zone boundaries
- Test price jumps at checkpoints

#### H5: Cross-Border Trade
- Saudi border proximity effects
- Distance-based analysis

### Secondary Hypotheses (H6-H10) - Benjamini-Hochberg FDR

Use FDR control for exploratory hypotheses:
```python
secondary_pvalues = {
    'H6': results_h6.pvalues['usd_adoption'],
    'H7': results_h7.pvalues['cash_effectiveness'],
    'H8': results_h8.pvalues['mobile_coverage'],
    'H9': results_h9.pvalues['threshold_effect'],
    'H10': results_h10.pvalues['convergence']
}
adjusted_secondary = mtf.adjust_pvalues(
    p_values=secondary_pvalues,
    method='fdr_bh'
)
```

## Three-Tier Analysis Structure

### Tier 1: Pooled Analysis
```python
# Basic pooled model
pooled_model = PanelOLS(
    dependent=df['log_price_usd'],
    exog=df[pooled_vars],
    entity_effects=True,
    time_effects=True
)

# With ML clustering (Phase 2)
clustering = CurrencyAwareMarketClustering()
df['market_cluster'] = clustering.fit_predict(df)
clustered_model = PanelOLS(..., other_effects=df['market_cluster'])
```

### Tier 2: Commodity-Specific
```python
# VECM for each commodity
for commodity in ['wheat', 'sugar', 'fuel']:
    commodity_data = df[df['commodity'] == commodity]
    
    # Test for cointegration
    coint_test = engle_granger_test(
        commodity_data['price_north_usd'],
        commodity_data['price_south_usd']
    )
    
    if coint_test.pvalue < 0.05:
        # Estimate VECM
        model = VECM(commodity_data[['price_north_usd', 'price_south_usd']], k_ar_diff=2)
        results = model.fit()
```

### Tier 3: Market-Pair Integration
```python
# For each market pair
for market1, market2 in market_pairs:
    if same_currency_zone(market1, market2):
        # Within-zone analysis
        threshold = estimate_threshold(pair_data)
    else:
        # Cross-zone analysis with exchange rate adjustment
        threshold = estimate_threshold_with_currency(pair_data)
```

## Robustness Testing Protocol

### Specification Curve Analysis
```python
# Generate all reasonable specifications
specifications = generate_specifications(
    dependent=['price_usd', 'log_price_usd'],
    fixed_effects=['entity', 'time', 'entity+time'],
    clustering=['market', 'governorate', 'commodity'],
    controls=generate_control_combinations(),
    samples=generate_subsamples()
)

# Run all specifications
results = run_specification_curve(specifications)

# Visualize
plot_specification_curve(results, highlight_main=True)
```

### Alternative Explanations Testing
Test these alternatives systematically:
1. Transportation costs (distance-based)
2. Quality differences (price dispersion)
3. Market power (HHI calculations)
4. Measurement error (validation checks)
5. Risk premiums (conflict insurance)

## Results Reporting Standards

### Tables Format
```python
# Generate publication-quality tables
from src.infrastructure.outputs.tables import RegressionTable

table = RegressionTable()
table.add_model("Baseline", results_baseline)
table.add_model("With Controls", results_controls)
table.add_model("IV", results_iv)

# Export to LaTeX
table.to_latex(
    "results/tables/main_results.tex",
    stars={0.01: '***', 0.05: '**', 0.1: '*'},
    notes="Standard errors clustered at market level in parentheses."
)
```

### Visualization Requirements
```python
# Price differential evolution
fig, axes = plt.subplots(2, 2, figsize=(12, 10))

# Panel A: Nominal vs Real Differentials
plot_price_differentials(axes[0, 0], nominal=True)
plot_price_differentials(axes[0, 1], nominal=False)

# Panel B: By Currency Zone
plot_by_zone(axes[1, 0], 'HOUTHI')
plot_by_zone(axes[1, 1], 'GOVERNMENT')

plt.tight_layout()
plt.savefig("results/figures/price_differentials.pdf", dpi=300)
```

## Welfare Analysis

Calculate consumer surplus loss from fragmentation:
```python
# Harberger triangles by income quintile
welfare_loss = calculate_welfare_loss(
    price_gap=real_price_differential,
    elasticity=demand_elasticity,
    quantities=consumption_by_quintile
)

# Policy simulation
unified_welfare = simulate_currency_reunification(
    current_rates=zone_exchange_rates,
    target_rate=unified_rate,
    transition_period=24  # months
)
```

## Key Warnings

1. **NEVER compare YER prices across zones without conversion**
2. **ALWAYS check exchange rate validity (100-3000 range)**
3. **NEVER ignore missing data patterns (conflict-driven)**
4. **ALWAYS report both significant and null results**
5. **NEVER claim causation without proper identification**

## Output Checklist

- [ ] All hypotheses tested with proper corrections
- [ ] Robustness checks completed (1000+ specifications)
- [ ] Tables formatted for publication
- [ ] Figures meet World Bank standards
- [ ] Welfare implications calculated
- [ ] Policy recommendations evidence-based
- [ ] Replication package prepared
- [ ] Results tracker updated

Remember: The integrity of this research depends on proper currency conversion and methodological rigor throughout the analysis.