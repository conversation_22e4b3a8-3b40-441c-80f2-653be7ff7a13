# Week 2 Panel Building Fix - Execution Prompt

## 🚨 CRITICAL CONTEXT FOR NEXT AGENT

You are taking over a **CRITICAL DATA QUALITY FIX** that invalidates all current analysis results. We have discovered the root cause: WFP uses a constant exchange rate of ~250 YER/USD for all USD price calculations, ignoring the actual zone-specific rates.

### What We Discovered (Week 2, Days 6-7)
- **WFP's Method**: Uses constant ~250 YER/USD for all markets and all time periods
- **Actual Rates**: North Yemen ~565 YER/USD, South Yemen ~1,144 YER/USD
- **Error Factor**: WFP USD prices are inflated by 2.3x (North) and 4.6x (South)
- **Impact**: The Yemen Price Paradox is severely understated, all results invalid

### What Was Fixed (Week 1)
1. ✅ WFP processor now extracts exchange rates from commodity data (2,357 observations)
2. ✅ Zone mapping implemented (DFA→HOUTHI, IRG→GOVERNMENT)
3. ✅ Validation script shows 77% error in current USD conversions
4. ✅ Impact assessment report created

### Your Mission (Week 2: Days 8-10)

Days 6-7 are COMPLETE. We discovered WFP uses constant 250 YER/USD. Now you must complete the fix by updating the panel builder to use our calculated USD prices instead of WFP's incorrect ones.

## ✅ Day 6-7: COMPLETED
- Fixed CurrencyAwareWFPProcessor to extract and use actual rates
- Created integration script that applies zone-specific rates
- Discovered root cause: WFP uses constant 250 YER/USD
- Validated that our rates are correct (North ~565, South ~1,144)

### Required Fix
```python
def _calculate_dynamic_zone_multipliers(self, dates: List[Any]) -> Dict[CurrencyZone, Decimal]:
    """Use ACTUAL exchange rates from WFP data, not estimates."""
    
    # Step 1: Get exchange rates from processed data
    exchange_rates = self.get_extracted_exchange_rates()  # Need to implement
    
    # Step 2: Group by zone and date
    zone_rates = {}
    for zone in [CurrencyZone.HOUTHI, CurrencyZone.GOVERNMENT]:
        zone_data = exchange_rates[exchange_rates['zone'] == zone]
        if not zone_data.empty:
            zone_rates[zone] = zone_data.groupby('date')['rate'].mean()
    
    # Step 3: NO FALLBACKS - if no data, raise error
    if not zone_rates:
        raise ValueError("No exchange rate data available - cannot proceed")
    
    # Step 4: Return actual rates, not multipliers
    return zone_rates
```

### Test Your Fix
```bash
# This should now show exchange rates being used correctly
uv run python scripts/test_wfp_exchange_rate_extraction.py

# Check rates are in correct ranges
# North: 500-600, South: 1500-2500
```

## Day 8-9: Fix Panel USD Price Calculations

### Current Problem
The panel uses WFP's incorrect USD prices (calculated with constant 250 rate). We need to replace these with our correctly calculated USD prices.

### Required Changes

1. **Update panel processing** to use our calculated USD prices:
```python
# In any panel processing script:
# Replace WFP's usdprice with our calculation
panel_df['usdprice_wfp_original'] = panel_df['usdprice']  # Keep for reference
panel_df['usdprice'] = panel_df['price'] / panel_df['exchange_rate_actual']

# Validate the correction
discrepancy = abs(panel_df['usdprice'] - panel_df['usdprice_wfp_original']) / panel_df['usdprice_wfp_original']
print(f"Average change from WFP prices: {discrepancy.mean()*100:.1f}%")
```

2. **Update `data_pipeline_orchestrator.py`** to pass exchange rates:
```python
# In _run_processing_stage
markets, observations, exchange_rates = await wfp_processor.process(...)

# Pass exchange rates to panel builder
panel = await panel_builder.create_integrated_panel(
    price_observations=observations,
    exchange_rates=exchange_rates,  # ADD THIS
    markets=markets,
    start_date=start_date,
    end_date=end_date
)
```

## Day 10: Regenerate and Validate All Data

### Step 1: Clear Old Data
```bash
# Backup current data first
mkdir -p data/backup/pre_fix_20250607
cp -r data/processed/* data/backup/pre_fix_20250607/

# Clear processed data
rm -rf data/processed/integrated_panel/*
rm -rf data/interim/exchange_rates*
```

### Step 2: Regenerate with Fixed Pipeline
```bash
# Process WFP data with correct exchange rates
uv run python src/cli.py data update-data wfp --force

# Create integrated panel
uv run python scripts/analysis/create_integrated_balanced_panel.py
```

### Step 3: Validate the Fix
```bash
# Run validation - MUST show <5% discrepancy
uv run python scripts/validate_usd_conversions.py

# Check exchange rate ranges
uv run python -c "
import pandas as pd
df = pd.read_parquet('data/processed/integrated_panel/yemen_integrated_balanced_panel.parquet')
print('Exchange rate summary:')
print(df.groupby('currency_zone')['exchange_rate_used'].agg(['mean', 'min', 'max']))
"
```

### Success Criteria
- [ ] Northern zones: mean ~525 YER/USD (range 500-600)
- [ ] Southern zones: mean ~1935 YER/USD (range 1500-2500)  
- [ ] USD conversion discrepancy <5% vs WFP calculations
- [ ] No exchange rates below 300 or above 2500

## Critical Files to Modify

1. **src/infrastructure/processors/currency_aware_wfp_processor.py**
   - Remove `_calculate_dynamic_zone_multipliers` hard-coded values
   - Use actual extracted exchange rates

2. **src/infrastructure/processors/panel_builder.py**
   - Add exchange rate parameter to main methods
   - Implement exchange rate merging logic

3. **src/application/services/data_pipeline_orchestrator.py**
   - Pass exchange rates between processors
   - Add validation steps

## Validation Scripts to Use

- `scripts/validate_exchange_rates.py` - Shows correct extraction (already working)
- `scripts/validate_usd_conversions.py` - Shows 77% error (must get to <5%)
- `scripts/test_wfp_exchange_rate_extraction.py` - Tests extraction logic

## ⚠️ CRITICAL WARNINGS

1. **DO NOT** proceed with any analysis until USD discrepancy is <5%
2. **DO NOT** use estimated or hard-coded exchange rates
3. **DO NOT** skip validation steps
4. **BACKUP** all data before regenerating

## Expected Outcomes After Fix

1. **Exchange Rates**: Correctly show ~525 North, ~1935 South
2. **USD Prices**: Match WFP calculations within 5%
3. **Yemen Paradox**: Will show stronger effect (Northern prices higher in USD)
4. **Next Steps**: Can proceed with Week 3 analysis re-run

## Reference Materials

- **Implementation Plan**: `DATA_PIPELINE_ENHANCEMENT_PLAN.md` (Week 2 section)
- **Impact Assessment**: `reports/exchange_rate_impact_assessment.md`
- **What Was Fixed**: Check git commit `bb2fe2f`

## Your First Commands

```bash
# 1. Understand the problem
cat reports/exchange_rate_impact_assessment.md

# 2. See what's already fixed  
cat scripts/validate_exchange_rates.py
uv run python scripts/validate_exchange_rates.py

# 3. See the error
uv run python scripts/validate_usd_conversions.py

# 4. Start fixing
code src/infrastructure/processors/currency_aware_wfp_processor.py
```

---

**Remember**: This is not a minor fix - it's a fundamental correction that affects every single analysis result. The credibility of the entire Yemen Market Integration research depends on getting this right.

*Good luck! The previous agent has set you up well with Week 1 complete. Now execute Week 2 with precision.*