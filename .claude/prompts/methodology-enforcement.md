# Methodology Enforcement Implementation Prompt

## Objective

Implement strict methodology enforcement in the Yemen Market Integration codebase to prevent analysis errors, particularly those related to currency conversion in multi-exchange rate environments.

## Context

This econometric research project initially produced incorrect results because it failed to convert prices from YER to USD before analysis. With exchange rates varying from ~535 (Houthi areas) to ~2,000+ (Government areas) YER/USD, this oversight led to spurious findings.

## Implementation Requirements

### 1. Methodology Validator (PRIORITY 1)

Create a comprehensive data validation system that enforces ALL methodology requirements before any analysis can proceed.

```python
# src/core/validation/methodology_validator.py

class MethodologyValidator:
    """
    Validates that all data processing follows research methodology.
    
    Critical validations:
    1. Currency conversion (MUST be in USD)
    2. Exchange rate validation
    3. Zone classification completeness
    4. Multiple testing corrections
    5. Statistical power requirements
    6. Data coverage tracking
    """
    
    def validate_dataset(self, observations, analysis_type, hypothesis_tests=None):
        # Implement validation logic
        # Raise CriticalMethodologyViolation if requirements not met
```

Key validation rules:
- **Currency Check**: All prices must be in USD or have valid exchange rates
- **Zone Classification**: Every market must have a currency zone
- **Exchange Rates**: Must be validated against bounds (Houthi: 500-600, Gov: 1500-2500)
- **Statistical Power**: n≥300 markets, t≥36 months, obs≥10,000
- **Coverage Target**: Track and report against 88.4% target

### 2. Multiple Testing Framework

Implement corrections to prevent false discoveries:

```python
# src/core/models/hypothesis_testing/multiple_testing_framework.py

def apply_hypothesis_corrections(test_results):
    """
    Apply appropriate multiple testing corrections.
    
    - H1-H5: Bonferroni (α = 0.05/5 = 0.01)
    - H6-H10: Benjamini-Hochberg (FDR = 0.10)
    """
```

### 3. Pre-Analysis Enforcement

Add mandatory validation to all analysis entry points:

```python
# src/application/services/analysis_orchestrator.py

def run_analysis(self, data, analysis_type, hypotheses):
    # MUST validate first
    is_valid, report = validate_analysis_inputs(data, analysis_type, hypotheses)
    
    if not is_valid:
        raise MethodologyViolation(
            f"Cannot proceed with analysis. Critical failures:\n"
            f"{report.critical_failures}"
        )
    
    # Only proceed if validation passes
```

### 4. Exchange Rate Implementation

Replace mock data with real implementations:

```python
# src/infrastructure/external_services/exchange_rate_collector.py

class CBYAdenScraper(ExchangeRateSource):
    async def fetch_rates(self, date):
        # Implement actual web scraping or API calls
        # Not mock data
        
class CBYSanaaScraper(ExchangeRateSource):
    async def fetch_rates(self, date):
        # Implement actual web scraping or API calls
        # Not mock data
```

## Testing Strategy

### Unit Tests Required

1. **Validation Tests**
   - Test catches mixed YER/USD data
   - Test rejects missing exchange rates
   - Test validates zone classifications
   - Test enforces statistical power

2. **Correction Tests**
   - Test Bonferroni calculations
   - Test Benjamini-Hochberg calculations
   - Test correction application to results

3. **Integration Tests**
   - Full pipeline with validation
   - Rejection of invalid data
   - Proper error messages

### Test Coverage Requirements

- Maintain 100% coverage
- Test both success and failure paths
- Include edge cases (e.g., exactly 300 markets)

## Implementation Checklist

- [ ] Read methodology mapping document
- [ ] Implement MethodologyValidator class
- [ ] Add currency conversion validation
- [ ] Add exchange rate validation
- [ ] Add zone classification checks
- [ ] Implement multiple testing corrections
- [ ] Add pre-analysis enforcement
- [ ] Replace mock exchange rate data
- [ ] Write comprehensive tests
- [ ] Update documentation

## Key References

1. **Methodology Mapping**: `docs/research-methodology-package/10-context-for-implementation/METHODOLOGY_TO_CODE_MAPPING.md`
2. **Hypothesis Framework**: `docs/research-methodology-package/01-theoretical-foundation/hypotheses/testable-hypotheses.md`
3. **Current Validator**: `src/core/validation/methodology_validator.py` (needs enhancement)

## Common Pitfalls to Avoid

1. **Don't assume currency**: Always validate, never trust
2. **Don't skip zones**: Every market needs classification
3. **Don't ignore power**: Small samples invalidate results
4. **Don't mix currencies**: YER and USD must never combine
5. **Don't trust mock data**: Implement real sources

## Success Metrics

1. Zero analyses proceed without validation
2. All currency mixing caught and rejected
3. Multiple testing corrections automatically applied
4. Real exchange rate data collected and validated
5. 100% test coverage maintained

## Recommended Approach

1. Start with the validator implementation
2. Add one validation rule at a time
3. Write tests for each rule
4. Integrate into analysis pipeline
5. Document each validation and why it exists

Remember: Every validation exists because of a real error that occurred. The currency conversion mistake cost significant research time and credibility. These validations prevent recurrence.

## Use Extended Thinking

When implementing complex validation logic or econometric corrections, use "ultrathink" or "think step by step" to ensure correctness. The methodology must be implemented precisely as specified in the research package.