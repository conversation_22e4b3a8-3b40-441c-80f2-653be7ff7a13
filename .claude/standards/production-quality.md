# Production Quality Standards

## World Bank Research Standards

### Code Quality Requirements

#### Architecture Principles

1. **Separation of Concerns**: Clear boundaries between layers
2. **Dependency Injection**: Testable, modular components
3. **Immutability**: Data transformations don't modify source
4. **Fail-Fast**: Immediate validation and error reporting
5. **Audit Trail**: Complete logging of all operations

#### Implementation Standards

```python
# REQUIRED: Type hints for all functions
def calculate_price_differential(
    price_north: Decimal,
    price_south: Decimal,
    exchange_rate_north: Decimal,
    exchange_rate_south: Decimal
) -> Dict[str, Decimal]:
    """
    Calculate price differential with currency conversion.

    Args:
        price_north: Price in northern zone (YER)
        price_south: Price in southern zone (YER)
        exchange_rate_north: YER/USD rate for north (~535)
        exchange_rate_south: YER/USD rate for south (~2000)

    Returns:
        Dictionary with nominal and real differentials

    Raises:
        ValueError: If any input is negative or zero
        MethodologyViolation: If exchange rates outside valid range
    """
    # REQUIRED: Input validation
    if any(x <= 0 for x in [price_north, price_south, exchange_rate_north, exchange_rate_south]):
        raise ValueError("All inputs must be positive")

    # REQUIRED: Methodology compliance
    validator = MethodologyValidator()
    if not validator.validate_exchange_rate_range(exchange_rate_north, CurrencyZone.HOUTHI):
        raise MethodologyViolation(f"Invalid exchange rate for northern zone: {exchange_rate_north}")
```

### Testing Standards

#### Coverage Requirements

- **Unit Tests**: ≥95% line coverage
- **Integration Tests**: All critical paths
- **Validation Tests**: Methodology compliance
- **Performance Tests**: <5 second for analysis

#### Test Structure

```python
# tests/unit/test_price_differential.py
import pytest
from decimal import Decimal
from src.core.domain.shared.exceptions import MethodologyViolation

class TestPriceDifferential:
    """Test price differential calculations with currency conversion."""

    def test_valid_calculation(self):
        """Test normal case with typical values."""
        result = calculate_price_differential(
            price_north=Decimal("1000"),
            price_south=Decimal("3000"),
            exchange_rate_north=Decimal("535"),
            exchange_rate_south=Decimal("2000")
        )
        assert result["real_differential"] > 0

    def test_methodology_violation(self):
        """Test that invalid exchange rates are rejected."""
        with pytest.raises(MethodologyViolation):
            calculate_price_differential(
                price_north=Decimal("1000"),
                price_south=Decimal("3000"),
                exchange_rate_north=Decimal("50"),  # Too low
                exchange_rate_south=Decimal("2000")
            )
```

### Documentation Standards

#### Docstring Requirements

```python
def run_three_tier_analysis(
    panel_data: pd.DataFrame,
    commodities: List[str],
    hypothesis_tests: List[str],
    config: AnalysisConfig
) -> ThreeTierResults:
    """
    Execute three-tier econometric analysis with methodology validation.

    This function implements the hierarchical analysis framework described
    in docs/research-methodology-package/03-econometric-methodology/. It
    ensures all data passes currency conversion validation before analysis.

    Args:
        panel_data: Balanced panel with required fields (price_usd,
                   exchange_rate_used, currency_zone)
        commodities: List of commodities to analyze
        hypothesis_tests: List of hypotheses (H1-H10) to test
        config: Analysis configuration including robustness settings

    Returns:
        ThreeTierResults containing:
        - tier1_results: Pooled panel estimates
        - tier2_results: Commodity-specific VECM results
        - tier3_results: Market-pair integration tests
        - robustness_checks: Specification curve results
        - validation_report: Methodology compliance report

    Raises:
        MethodologyViolation: If data fails validation
        ValueError: If insufficient data for analysis

    Example:
        >>> config = AnalysisConfig(bootstrap_iterations=1000)
        >>> results = run_three_tier_analysis(
        ...     panel_data=df,
        ...     commodities=['wheat', 'sugar'],
        ...     hypothesis_tests=['H1', 'H2'],
        ...     config=config
        ... )
        >>> print(f"H1 p-value: {results.tier1_results.hypothesis_tests['H1'].p_value}")

    Note:
        This analysis requires approximately 16GB RAM for full dataset.
        Consider using commodity subsets for development testing.
    """
```

### Performance Standards

#### Response Time Requirements

- **Data Loading**: <10 seconds for full panel
- **Validation**: <2 seconds per check
- **Analysis**: <5 seconds per hypothesis
- **Visualization**: <1 second render time

#### Optimization Techniques

```python
# Use vectorized operations
# BAD
for i in range(len(df)):
    df.loc[i, 'price_usd'] = df.loc[i, 'price_yer'] / df.loc[i, 'exchange_rate']

# GOOD
df['price_usd'] = df['price_yer'] / df['exchange_rate']

# Use caching for expensive operations
from functools import lru_cache

@lru_cache(maxsize=128)
def calculate_distance_matrix(markets: Tuple[str, ...]) -> np.ndarray:
    """Calculate distance matrix with caching."""
    # Expensive calculation cached
    return distance_matrix
```

### Error Handling Standards

#### Exception Hierarchy

```python
# src/core/domain/shared/exceptions.py
class YemenMarketException(Exception):
    """Base exception for all project exceptions."""
    pass

class MethodologyViolation(YemenMarketException):
    """Raised when methodology requirements violated."""
    def __init__(self, message: str, report: ValidationReport = None):
        super().__init__(message)
        self.report = report

class DataQualityException(YemenMarketException):
    """Raised when data quality standards not met."""
    pass

class InsufficientDataException(DataQualityException):
    """Raised when insufficient data for analysis."""
    pass
```

#### Logging Standards

```python
import structlog

logger = structlog.get_logger()

# REQUIRED: Structured logging with context
logger.info(
    "analysis_started",
    analysis_id=analysis_id,
    hypothesis_tests=hypothesis_tests,
    n_observations=len(panel_data),
    commodities=commodities
)

# REQUIRED: Error logging with full context
try:
    results = run_analysis()
except Exception as e:
    logger.error(
        "analysis_failed",
        analysis_id=analysis_id,
        error=str(e),
        error_type=type(e).__name__,
        traceback=traceback.format_exc()
    )
    raise
```

### Security Standards

#### API Security

```python
# Rate limiting
from src.infrastructure.security.rate_limiter import rate_limit

@router.post("/analysis/run")
@rate_limit(calls=10, period=timedelta(minutes=1))
async def run_analysis(request: AnalysisRequest):
    pass

# Input validation
from pydantic import BaseModel, validator

class AnalysisRequest(BaseModel):
    commodities: List[str]
    hypothesis_tests: List[str]

    @validator('hypothesis_tests')
    def validate_hypotheses(cls, v):
        valid = {'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'H7', 'H8', 'H9', 'H10'}
        if not all(h in valid for h in v):
            raise ValueError("Invalid hypothesis test")
        return v
```

### Deployment Standards

#### Environment Configuration

```python
# config/settings.py
from pydantic import BaseSettings

class Settings(BaseSettings):
    # REQUIRED: All configuration from environment
    hdx_api_key: str = ""
    exchange_rate_api_key: str = ""
    database_url: str = "postgresql://..."
    redis_url: str = "redis://..."

    # REQUIRED: Validation of critical settings
    @validator('hdx_api_key')
    def hdx_key_required_in_production(cls, v, values):
        if values.get('environment') == 'production' and not v:
            raise ValueError("HDX API key required in production")
        return v

    class Config:
        env_file = ".env"
        case_sensitive = False
```

#### Health Checks

```python
# src/interfaces/api/rest/routes/health.py
@router.get("/health")
async def health_check():
    """Comprehensive health check for monitoring."""
    checks = {
        "database": await check_database(),
        "redis": await check_redis(),
        "hdx_api": await check_hdx_api(),
        "exchange_rates": await check_exchange_rates(),
        "disk_space": check_disk_space(),
        "memory": check_memory_usage()
    }

    status = "healthy" if all(checks.values()) else "unhealthy"
    return {
        "status": status,
        "checks": checks,
        "version": __version__,
        "timestamp": datetime.utcnow().isoformat()
    }
```

### Version Control Standards

#### Commit Message Format

```
type(scope): description

- feat(analysis): Add ML clustering to tier 1
- fix(validation): Correct exchange rate range check
- docs(methodology): Update currency zone documentation
- test(hypothesis): Add H1 integration tests
- refactor(pipeline): Simplify panel building logic
```

#### Branch Strategy

- `main`: Production-ready code
- `develop`: Integration branch
- `feature/*`: New features
- `fix/*`: Bug fixes
- `release/*`: Release preparation

### Review Checklist

Before merging any PR:

- [ ] All tests pass
- [ ] Coverage ≥95%
- [ ] Documentation updated
- [ ] Type hints complete
- [ ] Logging added
- [ ] Error handling proper
- [ ] Performance acceptable
- [ ] Security reviewed
- [ ] Methodology validated

---
*These standards ensure World Bank publication quality throughout implementation*
