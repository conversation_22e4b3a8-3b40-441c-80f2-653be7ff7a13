# Methodology-Code Alignment Task Context

## Current State Analysis

### Critical Gaps Identified

1. **Currency Conversion Enforcement**
   - Current: H1 hypothesis test does conversion but not systematically enforced
   - Required: ALL price analyses must validate USD conversion first
   - Gap: No pre-analysis validation that prevents YER-USD mixing

2. **Three-Tier Framework Implementation**
   - Current: Basic structure exists in `src/application/analysis_tiers/`
   - Missing:
     - ML clustering (required for Tier 1)
     - Interactive Fixed Effects (IFE) models
     - Bayesian panel implementations
     - Nowcasting capabilities (Tier 3)

3. **Statistical Testing Corrections**
   - Current: No multiple testing corrections implemented
   - Required: Bonferroni for H1-H5 (α=0.01), <PERSON><PERSON>-<PERSON><PERSON><PERSON> for H6-H10
   - File: `src/core/models/hypothesis_testing/multiple_testing_framework.py`

4. **Data Quality Tracking**
   - Current: Basic imputation exists, no coverage tracking
   - Required: 88.4% coverage target with reporting
   - Gap: No systematic measurement of coverage percentages

5. **Exchange Rate Collection**
   - Current: Mock data in `exchange_rate_collector.py`
   - Required: Real data from CBY-Aden, CBY-Sana'a, parallel markets
   - Gap: No actual API implementations

## Key Files to Modify

### Priority 1: Methodology Validator (CREATE/ENHANCE)
```
src/core/validation/methodology_validator.py
```
- Currently exists but focuses on documentation validation
- Needs complete rewrite for data validation
- Must enforce currency conversion BEFORE any analysis

### Priority 2: Multiple Testing Framework
```
src/core/models/hypothesis_testing/multiple_testing_framework.py
```
- Add Bonferroni correction implementation
- Add Benjamini-Hochberg implementation
- Integrate with all hypothesis tests

### Priority 3: Three-Tier Enhancements
```
src/application/analysis_tiers/tier1_runner.py
src/application/analysis_tiers/tier2_runner.py
src/application/analysis_tiers/tier3_runner.py
```
- Add ML clustering to Tier 1
- Implement IFE and Bayesian models
- Add nowcasting to Tier 3

### Priority 4: Exchange Rate Implementation
```
src/infrastructure/external_services/exchange_rate_collector.py
```
- Replace mock data with real API calls
- Implement actual scrapers for CBY websites
- Add validation and imputation

## Implementation Strategy

### Phase 1: Critical Validation (MUST DO FIRST)
1. Create comprehensive `methodology_validator.py`
2. Add currency conversion checks to ALL entry points
3. Block any analysis without proper validation

### Phase 2: Statistical Corrections
1. Implement multiple testing corrections
2. Update all hypothesis tests to use corrections
3. Add reporting of adjusted p-values

### Phase 3: Advanced Methods
1. Add ML clustering to Tier 1
2. Implement IFE models
3. Add Bayesian panel capabilities
4. Implement nowcasting

### Phase 4: Data Collection
1. Implement real exchange rate APIs
2. Add multi-source validation
3. Implement imputation strategies

## Code Examples

### Methodology Validator Pattern
```python
class MethodologyValidator:
    def validate_dataset(self, observations, analysis_type, hypothesis_tests=None):
        # 1. Currency conversion check (CRITICAL)
        currency_result = self._validate_currency_conversion(observations)
        if not currency_result.passed:
            raise CriticalMethodologyViolation(currency_result.message)
        
        # 2. Exchange rate completeness
        exchange_result = self._validate_exchange_rates(observations)
        
        # 3. Zone classification
        zone_result = self._validate_zone_classification(observations)
        
        # 4. Statistical power
        power_result = self._validate_statistical_power(observations)
        
        # 5. Data coverage
        coverage_result = self._validate_data_coverage(observations)
        
        return MethodologyCompliance(results)
```

### Multiple Testing Pattern
```python
class MultipleTestingCorrection:
    @staticmethod
    def bonferroni_correction(p_values, alpha=0.05):
        """Apply Bonferroni correction for primary hypotheses."""
        n_tests = len(p_values)
        adjusted_alpha = alpha / n_tests
        adjusted_p_values = [min(p * n_tests, 1.0) for p in p_values]
        return adjusted_p_values, adjusted_alpha
    
    @staticmethod
    def benjamini_hochberg(p_values, alpha=0.10):
        """Apply Benjamini-Hochberg for secondary hypotheses."""
        # Implementation here
```

## Testing Requirements

### Unit Tests Needed
1. Test currency validation catches mixed currencies
2. Test multiple testing corrections calculations
3. Test coverage metric calculations
4. Test exchange rate validation logic

### Integration Tests Needed
1. Full pipeline with methodology validation
2. Hypothesis testing with corrections
3. Three-tier analysis with all methods
4. Exchange rate collection and validation

## Success Criteria

1. **No analysis can run without currency validation**
2. **All H1-H5 tests apply Bonferroni correction**
3. **Coverage metrics reported for every analysis**
4. **Exchange rates validated from multiple sources**
5. **100% test coverage maintained**

## References

- Methodology Requirements: `docs/research-methodology-package/10-context-for-implementation/METHODOLOGY_TO_CODE_MAPPING.md`
- Hypothesis Definitions: `docs/research-methodology-package/01-theoretical-foundation/hypotheses/testable-hypotheses.md`
- Three-Tier Framework: `docs/research-methodology-package/03-econometric-methodology/`