# Phase 1: Critical Infrastructure (Weeks 1-2)

## Week 1 Objectives

### Day 1-2: Validation Integration 🚨
**Critical**: Fix tier runners to enforce methodology validation

#### Files to Modify
1. `src/application/analysis_tiers/tier1_runner.py`
2. `src/application/analysis_tiers/tier2_runner.py`
3. `src/application/analysis_tiers/tier3_runner.py`

#### Implementation Pattern
```python
from src.core.validation.methodology_validator import MethodologyValidator
from src.core.domain.shared.exceptions import MethodologyViolation

async def run(self, command: AnalysisCommand, analysis_id: str):
    """Run analysis with mandatory methodology validation."""
    try:
        # Step 1: Initialize validator
        validator = MethodologyValidator()
        
        # Step 2: Load data
        panel_data = await self._load_panel_data(command)
        
        # Step 3: Validate BEFORE any analysis
        is_valid, report = validator.validate_analysis_inputs(
            observations=panel_data,
            analysis_type=AnalysisType.PANEL_ANALYSIS,
            hypothesis_tests=command.hypotheses or []
        )
        
        # Step 4: Block invalid analysis
        if not is_valid:
            self._logger.error(f"Methodology validation failed: {report}")
            raise MethodologyViolation(
                f"Critical validation failures: {report.critical_failures}"
            )
        
        # Step 5: Proceed with analysis only if valid
        self._logger.info("Methodology validation passed, proceeding with analysis")
        # ... rest of the analysis code
        
    except MethodologyViolation:
        raise  # Re-raise methodology violations
    except Exception as e:
        self._logger.error(f"Analysis failed: {str(e)}")
        raise
```

#### Testing Commands
```bash
# Test individual tier runners
pytest tests/integration/test_tier1_integration.py -v
pytest tests/integration/test_tier2_integration.py -v
pytest tests/integration/test_tier3_integration.py -v

# Test validation enforcement
python -c "
from src.application.analysis_tiers.tier1_runner import Tier1Runner
# Create test data without USD prices
# Should raise MethodologyViolation
"
```

### Day 3-4: Data Access Resolution 📊

#### HDX Authentication Fix
1. **Check Current Implementation**
   ```bash
   grep -n "HDX_API_KEY" src/infrastructure/external_services/hdx_client.py
   ```

2. **Implement Authentication**
   ```python
   # In hdx_client.py
   class HDXClient:
       def __init__(self):
           self.api_key = os.environ.get('HDX_API_KEY')
           if not self.api_key:
               logger.warning("HDX_API_KEY not set, using anonymous access")
           
       def _get_headers(self):
           headers = {'User-Agent': 'Yemen-Market-Integration/1.0'}
           if self.api_key:
               headers['X-HDX-API-KEY'] = self.api_key
           return headers
   ```

3. **Create Fallback Script**
   ```bash
   # scripts/download_hdx_manual.py
   # Browser-based download with progress tracking
   ```

#### WFP Data Validation
```python
# Verify exchange rate coverage
from src.infrastructure.external_services.exchange_rate_collector_v2 import ExchangeRateCollectorV2

collector = ExchangeRateCollectorV2()
coverage = collector.check_wfp_coverage(
    start_date='2019-01-01',
    end_date='2024-12-31',
    currency_pair='YER/USD'
)
print(f"WFP Exchange Rate Coverage: {coverage:.1%}")
```

### Day 5: Integration Testing ✅

#### End-to-End Pipeline Test
```bash
# 1. Download data
python scripts/download_data.py --source all --validate

# 2. Create panel
python scripts/create_integrated_balanced_panel.py \
    --require-currency-zones \
    --min-coverage 0.884

# 3. Run validation
python -m src.core.validation.methodology_validator validate \
    --panel data/processed/balanced_panel.csv

# 4. Test analysis
python src/cli.py analysis run \
    --tier 1 \
    --validate-methodology \
    --hypothesis H1
```

## Week 2 Objectives

### Day 6-7: Repository Integration 🔧

#### Enforce Currency at Data Source
1. **Modify Price Repository**
   ```python
   # src/infrastructure/persistence/repositories/postgres/price_repository.py
   def get_prices(self, filters: Dict) -> pd.DataFrame:
       # Always include currency fields
       required_fields = ['price_usd', 'price_yer', 'exchange_rate_used', 'currency_zone']
       # Validate all required fields present
   ```

2. **Update Panel Builder**
   ```python
   # src/infrastructure/processors/panel_builder.py
   def build_panel(self):
       # Enforce validation at panel creation
       if not self._validate_currency_fields(df):
           raise ValueError("Panel missing required currency fields")
   ```

### Day 8-9: Error Handling 🚦

#### Propagate MethodologyViolation
1. **API Layer**
   ```python
   # src/interfaces/api/rest/middleware/error_handler.py
   @app.exception_handler(MethodologyViolation)
   async def methodology_violation_handler(request, exc):
       return JSONResponse(
           status_code=422,
           content={
               "error": "Methodology Violation",
               "details": str(exc),
               "validation_report": exc.report
           }
       )
   ```

2. **CLI Layer**
   ```python
   # src/cli.py
   except MethodologyViolation as e:
       click.echo(click.style(f"❌ Methodology Error: {e}", fg='red'))
       if hasattr(e, 'report'):
           click.echo("Validation Report:")
           click.echo(e.report)
       sys.exit(1)
   ```

### Day 10: Phase 1 Validation 🎯

#### Checklist Completion
- [ ] All tier runners call validator
- [ ] HDX data downloads work
- [ ] Exchange rates validated
- [ ] Integration tests pass
- [ ] Error handling complete
- [ ] Documentation updated

#### Final Validation Commands
```bash
# Run full validation suite
make validate-phase1

# Generate coverage report
python scripts/generate_coverage_report.py \
    --phase 1 \
    --output reports/phase1_completion.md
```

## Success Metrics

### Technical Metrics
- ✅ 100% of analyses validate currency conversion
- ✅ 0 analyses can run without validation
- ✅ All tests passing (100% coverage maintained)
- ✅ <2 second validation overhead

### Data Metrics  
- ✅ HDX data pipeline functional
- ✅ Exchange rate coverage >95%
- ✅ Panel creation successful
- ✅ 88.4% data coverage achieved

### Process Metrics
- ✅ Daily progress documented
- ✅ Decisions logged with rationale
- ✅ Issues tracked and resolved
- ✅ Next phase planned

## Troubleshooting

### Common Issues

1. **HDX Rate Limiting**
   - Solution: Implement exponential backoff
   - Fallback: Manual download scripts

2. **Memory Issues with Panel**
   - Solution: Chunk processing
   - Fallback: Reduce scope temporarily

3. **Test Failures**
   - Solution: Update test expectations
   - Note: Some legacy tests may need currency fields

4. **Exchange Rate Gaps**
   - Solution: Implement interpolation
   - Validate: Against known events

## Daily Standup Template

```markdown
## Date: [DATE]

### Completed
- [x] Task 1
- [x] Task 2

### In Progress
- [ ] Task 3 (70% complete)

### Blockers
- Issue 1: [Description] - [Proposed solution]

### Next
- Task 4
- Task 5

### Notes
- Key decision: [What and why]
- Learning: [What we discovered]
```

---
*Phase 1 establishes the critical foundation for methodologically sound analysis*