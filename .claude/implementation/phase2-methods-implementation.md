# Phase 2: Advanced Methods Implementation (Weeks 3-6)

## Overview

Phase 2 implements the advanced econometric methods required for world-class analysis, completing the three-tier framework with ML clustering, Interactive Fixed Effects (IFE), Bayesian panels, and nowcasting capabilities.

## Week 3-4: Machine Learning Integration

### Objective
Implement ML clustering for market grouping in Tier 1, accounting for currency zones and conflict dynamics.

### Implementation Tasks

#### 1. Market Clustering Implementation
**File**: `src/core/models/machine_learning/market_clustering.py`

```python
class EnhancedMarketClustering:
    """
    Cluster markets based on:
    - Price correlation patterns
    - Geographic proximity  
    - Currency zone boundaries
    - Conflict exposure levels
    """
    
    def __init__(self, n_clusters: Optional[int] = None):
        self.n_clusters = n_clusters
        self.clustering_methods = {
            'kmeans': KMeans,
            'dbscan': DBSCAN,
            'hierarchical': AgglomerativeClustering,
            'gaussian_mixture': GaussianMixture
        }
        
    def prepare_features(self, panel_data: pd.DataFrame) -> np.ndarray:
        """
        Create feature matrix for clustering:
        - Price correlations
        - Distance matrix
        - Zone indicators
        - Conflict intensity
        """
        
    def find_optimal_clusters(self, X: np.ndarray) -> int:
        """
        Use elbow method and silhouette analysis
        to determine optimal number of clusters
        """
        
    def cluster_markets(self, panel_data: pd.DataFrame) -> Dict[str, int]:
        """
        Main clustering method with validation
        """
```

#### 2. Integration with Tier 1
**Update**: `src/application/analysis_tiers/tier1_runner.py`

- Add clustering step before pooled analysis
- Use cluster indicators as additional controls
- Report cluster-specific results

### Testing Requirements
```bash
# Unit tests for clustering
pytest tests/unit/models/test_market_clustering.py -v

# Integration test with tier 1
pytest tests/integration/test_tier1_with_clustering.py -v

# Validation against manual classification
python scripts/validate_clustering.py --compare-with-zones
```

## Week 5-6: Interactive Fixed Effects & Bayesian Panels

### Interactive Fixed Effects (IFE)

#### Implementation
**File**: `src/core/models/econometric/interactive_fixed_effects.py`

```python
from linearmodels.panel import PanelOLS
import numpy as np

class InteractiveFixedEffects:
    """
    Implement Bai (2009) interactive fixed effects estimator.
    Captures unobserved heterogeneity through factor structure.
    """
    
    def __init__(self, n_factors: int = 2):
        self.n_factors = n_factors
        
    def estimate(self, 
                 y: np.ndarray, 
                 X: np.ndarray, 
                 entity_ids: np.ndarray,
                 time_ids: np.ndarray) -> IFEResults:
        """
        Estimate model with interactive fixed effects
        using iterative least squares
        """
```

#### Integration Points
- Add to panel model options in Tier 1
- Compare with standard fixed effects
- Use for robustness checks

### Bayesian Panel Models

#### Implementation  
**File**: `src/core/models/bayesian/bayesian_panel.py`

```python
import pymc as pm
import arviz as az

class BayesianPanelModel:
    """
    Bayesian hierarchical panel model with:
    - Market-level random effects
    - Time-varying parameters
    - Proper uncertainty quantification
    """
    
    def __init__(self, 
                 prior_config: Dict[str, Any] = None):
        self.prior_config = prior_config or self.default_priors()
        
    def build_model(self, panel_data: pd.DataFrame) -> pm.Model:
        """
        Construct PyMC model with hierarchical structure
        """
        
    def sample(self, 
               draws: int = 2000, 
               chains: int = 4) -> az.InferenceData:
        """
        MCMC sampling with diagnostics
        """
```

### Nowcasting Implementation

#### Core Architecture
**File**: `src/core/models/nowcasting/nowcasting_models.py`

```python
class NowcastingFramework:
    """
    Real-time price prediction using:
    - Mixed frequency data
    - Dynamic factor models
    - Machine learning ensemble
    """
    
    def __init__(self):
        self.models = {
            'midas': MIDASRegressor(),
            'dynamic_factor': DynamicFactorModel(),
            'lstm': LSTMNowcaster(),
            'ensemble': EnsembleNowcaster()
        }
        
    def nowcast_prices(self, 
                       historical_data: pd.DataFrame,
                       high_freq_indicators: pd.DataFrame) -> NowcastResults:
        """
        Generate real-time price predictions
        """
```

## Testing Strategy

### Unit Testing Requirements
```python
# tests/unit/models/test_ife.py
def test_ife_convergence():
    """Test IFE algorithm converges"""
    
def test_ife_vs_fe():
    """Compare IFE with standard FE"""

# tests/unit/models/test_bayesian_panel.py  
def test_prior_sensitivity():
    """Test sensitivity to prior specification"""
    
def test_mcmc_diagnostics():
    """Verify MCMC convergence"""
```

### Integration Testing
```bash
# Test all advanced methods
make test-advanced-methods

# Performance benchmarks
python scripts/benchmark_methods.py --methods all
```

## Week-by-Week Schedule

### Week 3: ML Clustering
- Monday-Tuesday: Implement base clustering
- Wednesday: Currency zone integration
- Thursday: Tier 1 integration
- Friday: Testing and validation

### Week 4: Clustering Refinement  
- Monday-Tuesday: Optimize cluster selection
- Wednesday: Visualization tools
- Thursday: Documentation
- Friday: Integration testing

### Week 5: IFE Implementation
- Monday-Tuesday: Core IFE algorithm
- Wednesday: Panel data integration
- Thursday: Comparison framework
- Friday: Testing

### Week 6: Bayesian & Nowcasting
- Monday-Tuesday: Bayesian panel setup
- Wednesday: MCMC implementation
- Thursday: Nowcasting framework
- Friday: Full integration test

## Success Metrics

### Technical Metrics
- ✅ All methods implemented and tested
- ✅ >95% test coverage maintained
- ✅ Performance <5 seconds per model
- ✅ Documentation complete

### Analytical Metrics
- ✅ Clustering improves model fit
- ✅ IFE captures unobserved factors
- ✅ Bayesian uncertainty quantified
- ✅ Nowcasting accuracy >80%

## Deliverables

### Code Deliverables
1. `market_clustering.py` - ML clustering implementation
2. `interactive_fixed_effects.py` - IFE estimator
3. `bayesian_panel.py` - Bayesian hierarchical model
4. `nowcasting_models.py` - Real-time prediction

### Documentation Deliverables
1. Method comparison report
2. Clustering validation results
3. Bayesian diagnostics report
4. Nowcasting performance metrics

### Integration Deliverables
1. Updated tier runners with new methods
2. Unified model interface
3. Performance benchmarks
4. Test suite extensions

## Dependencies

### Python Packages
```txt
# Add to requirements.txt
scikit-learn>=1.3.0  # For clustering
linearmodels>=5.0    # For IFE
pymc>=5.0           # For Bayesian models
arviz>=0.16.0       # For MCMC diagnostics
tensorflow>=2.13.0   # For LSTM nowcasting
```

### Data Requirements
- Full balanced panel for clustering
- High-frequency indicators for nowcasting
- Prior information for Bayesian models

## Risk Mitigation

### Technical Risks
1. **IFE Convergence Issues**
   - Mitigation: Implement multiple algorithms
   - Fallback: Standard FE with time trends

2. **MCMC Sampling Speed**
   - Mitigation: Use NUTS sampler
   - Fallback: Variational inference

3. **Clustering Instability**
   - Mitigation: Ensemble methods
   - Fallback: Theory-based grouping

### Implementation Risks
1. **Package Conflicts**
   - Mitigation: Virtual environment
   - Testing: CI/CD pipeline

2. **Memory Requirements**
   - Mitigation: Chunked processing
   - Monitoring: Memory profiling

---
*Phase 2 completes the advanced econometric toolkit required for world-class analysis*