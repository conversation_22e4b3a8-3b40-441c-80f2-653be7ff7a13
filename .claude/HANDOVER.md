# Claude Code Handover Guide - Yemen Market Integration

## Project Overview

This is a World Bank-standard econometric research project analyzing market integration in Yemen, with a focus on how currency fragmentation (North: ~535 YER/USD, South: ~2000+ YER/USD) affects price dynamics and welfare.

## Critical Context for New Sessions

### 1. Start Here

```bash
# Check current status
cat .claude/implementation/current-status.md

# Review immediate tasks
cat .claude/memory/next-actions.md

# See what was done previously
cat .claude/memory/session-log.md
```

### 2. Core Methodology Rule

**EVERY analysis must validate currency conversion first:**

```python
from src.core.validation.methodology_validator import MethodologyValidator

validator = MethodologyValidator()
is_valid, report = validator.validate_analysis_inputs(data)
if not is_valid:
    raise MethodologyViolation(f"Cannot proceed: {report.critical_failures}")
```

### 3. Current Implementation Status

- **Phase**: Week 1 of 12-week plan
- **Completed**: Currency conversion enforcement, exchange rate collection, statistical framework
- **In Progress**: Tier runner validation integration
- **Next**: ML clustering and advanced methods

## Key Files Reference

### For Understanding Requirements

1. `CLAUDE.md` - Project overview and critical requirements
2. `.claude/validation/econometric-checklist.md` - Comprehensive validation checklist
3. `docs/research-methodology-package/10-context-for-implementation/METHODOLOGY_TO_CODE_MAPPING.md` - How methodology maps to code

### For Implementation Work

1. `.claude/implementation/phase1-critical-tasks.md` - Current phase tasks
2. `.claude/prompts/implementation-phase-execution.md` - How to execute implementation
3. `.claude/standards/production-quality.md` - Code quality standards

### For Analysis

1. `.claude/prompts/econometric-analysis-execution.md` - How to run analyses
2. `.claude/results/hypothesis-tracking.md` - Track hypothesis test results
3. `.claude/prompts/publication-quality-outputs.md` - Generate final outputs

## Common Tasks

### Running Analysis with Validation

```bash
# Always use --validate-methodology flag
uv run python src/cli.py analysis run --tier 1 --validate-methodology --hypothesis H1

# Check data coverage
uv run python scripts/check_data_coverage.py --target 0.884

# Run full validation suite
uv run make validate-methodology
```

### Updating Progress

After completing work:

1. Update `.claude/implementation/current-status.md`
2. Log decisions in `.claude/memory/decision-log.md`
3. Plan next tasks in `.claude/memory/next-actions.md`
4. Add to `.claude/memory/session-log.md`

### Testing Changes

```bash
# Run specific test suites
uv run pytest tests/unit/core/validation/ -v
uv run pytest tests/integration/test_tier*_integration.py -v

# Check coverage
uv run pytest --cov=src --cov-report=html
```

## Critical Reminders

### What Makes This Project Special

1. **Currency Fragmentation Discovery**: The "Yemen Paradox" where nominal prices appear lower in the north but are actually higher when properly converted to USD
2. **Methodology First**: No analysis can proceed without proper currency conversion validation
3. **World Bank Standards**: Everything must be publication-ready with full robustness testing

### Common Pitfalls to Avoid

1. **Never** compare prices in YER across zones without conversion
2. **Never** use a single exchange rate for all of Yemen
3. **Never** ignore missing data patterns (they're conflict-driven)
4. **Always** apply multiple testing corrections
5. **Always** validate before analysis

### Key Success Metrics

- ✅ 100% of analyses use USD prices
- ✅ 88.4% data coverage target
- ✅ All hypotheses tested with corrections
- ✅ 1000+ robustness specifications
- ✅ World Bank publication standards met

## Technical Environment

### Required Setup

```bash
# UV required (modern Python package manager)
uv --version || curl -LsSf https://astral.sh/uv/install.sh | sh

# Install dependencies (creates .venv automatically)
uv sync --extra dev

# Set environment variables
export HDX_API_KEY="your_key_here"
export EXCHANGE_RATE_API_KEY="your_key_here"
```

### Key Dependencies

- `statsmodels` - Core econometric models
- `linearmodels` - Panel data models and IFE
- `pymc` - Bayesian models (Phase 2)
- `scikit-learn` - ML clustering (Phase 2)

## Getting Help

### If Stuck

1. Check `.claude/memory/decision-log.md` for similar past decisions
2. Review the prompts in `.claude/prompts/` for guidance
3. Validate assumptions with the methodology validator
4. Refer to `docs/research-methodology-package/` for theoretical basis

### Key Principles

1. **Methodology Enforcement**: The validator is always right
2. **Currency First**: Every price must be in USD
3. **Zone Awareness**: Markets behave differently by currency zone
4. **Robustness Obsession**: Test everything multiple ways
5. **Documentation**: Explain why, not just what

## Session Ending Checklist

Before ending your session:

- [ ] Updated current status
- [ ] Logged key decisions
- [ ] Documented next actions
- [ ] Committed code changes
- [ ] Ran tests for changes
- [ ] Updated session log

---

*This project aims to transform how we understand markets in conflict settings. The currency fragmentation insight is groundbreaking - handle with appropriate rigor.*

**Remember**: When in doubt, check the methodology validator!
