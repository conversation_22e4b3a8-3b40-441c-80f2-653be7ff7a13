# Methodology Validation Workflow

## Overview

This workflow ensures that all data analysis in the Yemen Market Integration project adheres to the strict methodological requirements documented in the research package. It prevents the recurrence of currency conversion errors and other methodological violations.

## Workflow Stages

### Stage 1: Pre-Analysis Validation

**Trigger**: Any analysis request (API call, CLI command, notebook execution)

**Actions**:
1. Intercept analysis request
2. Extract dataset and analysis parameters
3. Run MethodologyValidator.validate_dataset()
4. Check validation results
5. Block or proceed based on compliance

**Decision Points**:
- If CRITICAL failures → Block analysis, return detailed error report
- If WARNING only → Proceed but log warnings prominently
- If PASS → Continue to analysis

### Stage 2: Currency Conversion Validation

**Specific Checks**:
1. All prices in same currency (preferably USD)
2. If mixed currencies, valid exchange rates exist
3. Exchange rates match currency zones
4. Temporal alignment of prices and rates

**Implementation**:
```python
def validate_currency_conversion(observations):
    currencies = {obs.price.currency for obs in observations}
    
    if len(currencies) > 1:
        # Mixed currencies - check for exchange rates
        for obs in observations:
            if obs.price.currency == Currency.YER:
                if not hasattr(obs, 'exchange_rate_applied'):
                    return ValidationResult(
                        passed=False,
                        severity='critical',
                        message='YER prices without exchange rates'
                    )
    
    # Additional validation logic...
```

### Stage 3: Statistical Requirements Check

**Power Analysis**:
- Minimum 300 unique markets
- Minimum 36 time periods
- Minimum 10,000 observations
- Balanced panel preferred (88.4% coverage target)

**Multiple Testing**:
- Identify hypothesis tests requested
- Apply Bonferroni for H1-H5
- Apply Benjamini-Hochberg for H6-H10
- Document corrections applied

### Stage 4: Zone Classification Validation

**Requirements**:
- Every market mapped to currency zone
- Zone assignments consistent over time
- Confidence scores tracked
- Unknown zones < 10% of data

### Stage 5: Data Quality Reporting

**Metrics Tracked**:
- Data coverage percentage
- Missing data patterns
- Currency conversion success rate
- Zone classification confidence
- Statistical power achieved

**Report Format**:
```
=== Methodology Validation Report ===
Dataset: [ID]
Date: [Timestamp]

CRITICAL CHECKS:
✓ Currency Conversion: PASS (100% USD)
✓ Exchange Rates: PASS (validated from 3 sources)
✓ Zone Classification: PASS (95% confidence)
✗ Statistical Power: FAIL (n=250 markets < 300 required)

WARNINGS:
- Data coverage at 82.3% (below 88.4% target)
- 15 markets with imputed exchange rates

RECOMMENDATION: Cannot proceed with full analysis.
Suggested: Limit to descriptive statistics only.
```

## Integration Points

### API Integration

```python
@app.post("/api/v2/analysis/run")
async def run_analysis(request: AnalysisRequest):
    # Validation is mandatory
    validator = MethodologyValidator()
    compliance = validator.validate_dataset(
        observations=request.data,
        analysis_type=request.analysis_type,
        hypothesis_tests=request.hypotheses
    )
    
    if not compliance.overall_compliant:
        raise HTTPException(
            status_code=422,
            detail={
                "error": "Methodology validation failed",
                "violations": compliance.critical_failures,
                "recommendations": compliance.recommendations
            }
        )
    
    # Proceed with analysis only if compliant
```

### CLI Integration

```bash
# CLI automatically validates before analysis
$ python src/cli.py analysis run --type panel --hypothesis H1 H2

[INFO] Running methodology validation...
[ERROR] Validation failed: Mixed currencies detected (YER and USD)
[ERROR] Critical: Currency conversion required before analysis

Analysis blocked. Please run:
$ python src/cli.py data convert-currency --target USD
```

### Notebook Integration

```python
# In Jupyter notebooks
from src.core.validation import validate_before_analysis

@validate_before_analysis  # Decorator enforces validation
def run_market_integration_analysis(data):
    # Analysis code here
    pass

# Attempting to run without proper data will raise exception
```

## Automation Rules

### Automatic Corrections

Some issues can be automatically corrected:
1. **Missing exchange rates**: Attempt imputation if < 5% missing
2. **Zone classification**: Use governorate-based defaults if needed
3. **Multiple testing**: Always apply appropriate corrections

### Manual Intervention Required

Some issues require user action:
1. **Mixed currencies**: User must specify conversion approach
2. **Low statistical power**: User must acknowledge limitations
3. **High missing data**: User must choose imputation strategy

## Quality Assurance

### Continuous Monitoring

- Log all validation attempts and results
- Track validation failure reasons
- Monitor which rules trigger most often
- Regular review of validation thresholds

### Validation Updates

- Quarterly review of validation rules
- Update based on new methodological insights
- Version control for validation logic
- Backward compatibility maintained

## Error Handling

### User-Friendly Messages

Instead of:
```
ValidationError: Rule 3.2.1 failed
```

Provide:
```
Currency Conversion Error: Your dataset contains prices in both YER and USD.

What happened: 
- 2,453 prices are in YER (Yemeni Rial)
- 1,891 prices are in USD
- These cannot be compared directly due to exchange rate differences

How to fix:
1. Convert all prices to USD using:
   python src/cli.py data convert-currency --target USD
2. Or provide exchange rate data:
   python src/cli.py data add-exchange-rates --source CBY

Why this matters:
Northern Yemen uses ~535 YER/USD while Southern Yemen uses ~2,000 YER/USD.
Comparing YER prices directly would show false 4x price differences.
```

### Validation Bypass (Dev Only)

For development/testing only:
```python
# WARNING: Never use in production
validator.validate_dataset(
    data, 
    strict=False,  # Allows warnings to pass
    bypass_critical=['currency']  # Dangerous - logs bypassed rules
)
```

## Success Metrics

1. **Zero unvalidated analyses**: 100% of analyses pass through validation
2. **Catch rate**: > 95% of methodology errors caught before analysis
3. **User compliance**: < 5% validation bypass attempts
4. **Performance**: Validation adds < 2 seconds to analysis time

## Continuous Improvement

### Feedback Loop

1. Collect validation failure patterns
2. Identify common user errors
3. Improve error messages
4. Add automated corrections where safe
5. Update documentation

### Validation Effectiveness

Track and report:
- Most common validation failures
- False positive rate
- User satisfaction with error messages
- Time to resolution for failures

This workflow ensures that the hard-learned lessons about currency conversion and other methodological requirements are systematically enforced, preventing costly analysis errors.