# Yemen Market Integration Analysis - Replication Package

## Overview
This package replicates all results from "Currency Fragmentation and Market Integration in Yemen: Evidence from a Multi-Exchange Rate Environment"

## Requirements
- Python 3.10+
- 16GB RAM recommended
- See requirements.txt for packages

## Quick Start
```bash
# Install dependencies
pip install -r requirements.txt

# Run main analysis
python code/scripts/run_analysis.py

# Generate robustness tests
python code/scripts/run_specification_curve.py --n-specs 1000

# Create publication materials
python code/scripts/generate_world_bank_publication.py
```

## Package Structure
```
replication_package/
├── README.md              # This file
├── requirements.txt       # Python dependencies
├── data/
│   ├── raw/              # Original data files (download instructions)
│   └── processed/        # Analysis-ready panel data
├── code/
│   ├── src/              # Core analysis modules
│   └── scripts/          # Execution scripts
└── results/
    ├── robustness/       # Specification curve outputs
    └── world_bank_publication/  # Tables and documents
```

## Data
### Raw Data Sources
1. **WFP Price Data**: Available from HDX (hdx.humdata.org)
2. **Exchange Rates**: WFP Yemen dataset includes parallel market rates
3. **Conflict Data**: ACLED Yemen events (acleddata.com)
4. **Control Areas**: ACAPS Yemen Analysis Hub

### Processed Data
- `balanced_panel.parquet`: Main analysis dataset (22,022 observations)
- Contains: market_id, date, commodity, price_usd, price_yer, exchange_rate_used, currency_zone

## Replication Steps

### Step 1: Data Preparation
```bash
# Download raw data (see data/raw/download_instructions.md)
# Process into balanced panel
python code/scripts/create_integrated_balanced_panel.py
```

### Step 2: Main Analysis
```bash
# Run three-tier econometric analysis
python code/scripts/run_analysis.py

# Results saved to results/three_tier_analysis/
```

### Step 3: Robustness Testing
```bash
# Run specification curve (1000+ models)
python code/scripts/run_specification_curve.py \
    --n-specs 1000 \
    --data-path data/processed/balanced_panel.parquet \
    --output-dir results/robustness/

# Expected runtime: 2-4 hours
```

### Step 4: Generate Tables
```bash
# Create World Bank publication materials
python code/scripts/generate_world_bank_publication.py \
    --output-dir results/world_bank_publication/

# Outputs:
# - main_results_table.tex
# - robustness_table.tex
# - heterogeneity_table.tex
# - executive_summary.md
# - policy_brief.md
```

## Key Results to Verify

1. **Main Finding**: Exchange rate coefficient ~0.75-0.85
2. **Robustness**: 95%+ specifications show positive effect
3. **Significance**: 97%+ specifications with p < 0.05
4. **Zone Effects**: North ~0.80, South ~0.60 pass-through

## Technical Notes

### Currency Zone Mapping
- 'IRG' → 'GOVERNMENT' (South, ~2000 YER/USD)
- 'DFA' → 'HOUTHI' (North, ~535 YER/USD)
- Missing → 'CONTESTED'

### Methodology Enforcement
All analyses enforce:
- 100% currency conversion to USD
- Multi-source exchange rate validation
- Zone classification for every market
- Bonferroni correction for H1-H5 (α = 0.01)

### Computing Requirements
- Full replication: ~3-5 hours on 8-core CPU
- Memory peak: ~8GB during robustness testing
- Disk space: ~2GB for all outputs

## Support

**Contact**: <EMAIL>
**Issues**: github.com/world-bank/yemen-market-integration/issues
**Documentation**: See docs/ folder in main repository

## Citation

```bibtex
@techreport{yemen2025,
  title={Currency Fragmentation and Market Integration in Yemen},
  author={World Bank Yemen Team},
  year={2025},
  institution={World Bank},
  type={Policy Research Working Paper}
}
```

## License

This replication package is provided under the MIT License.
The methodology and findings are freely available for research use.

---
*Last Updated: June 2025*