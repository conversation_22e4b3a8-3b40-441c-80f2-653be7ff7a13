#!/usr/bin/env python3
"""
Documentation Consistency Checker

This tool scans all documentation files to ensure consistency, proper formatting,
and adherence to academic standards. It checks for:
- Forbidden phrases and language
- Proper hypothesis formatting
- Valid cross-references
- Placeholder usage in results templates
- Consistent terminology
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Tuple, Set, Optional
from dataclasses import dataclass, field
from datetime import datetime
import argparse
from collections import defaultdict


@dataclass
class Issue:
    """Represents a consistency issue found in documentation."""
    file_path: str
    line_number: int
    issue_type: str
    severity: str  # 'error', 'warning', 'info'
    message: str
    context: Optional[str] = None
    suggestion: Optional[str] = None


@dataclass
class ConsistencyReport:
    """Complete consistency check report."""
    timestamp: str
    total_files_scanned: int
    total_issues: int
    issues_by_type: Dict[str, int] = field(default_factory=dict)
    issues_by_severity: Dict[str, int] = field(default_factory=dict)
    issues_by_file: Dict[str, List[Issue]] = field(default_factory=lambda: defaultdict(list))
    cross_reference_map: Dict[str, Set[str]] = field(default_factory=lambda: defaultdict(set))
    hypothesis_inventory: Dict[str, Dict] = field(default_factory=dict)


class DocumentationConsistencyChecker:
    """Main checker class for documentation consistency."""
    
    def __init__(self, root_path: str = "."):
        self.root_path = Path(root_path)
        self.issues: List[Issue] = []
        self.cross_references: Dict[str, Set[str]] = defaultdict(set)
        self.hypothesis_inventory: Dict[str, Dict] = {}
        
        # Define forbidden phrases that indicate poor academic writing
        self.forbidden_phrases = {
            # Overly certain language
            "proves that": "demonstrates that",
            "clearly shows": "indicates",
            "obviously": "evidently",
            "definitely": "likely",
            "always leads to": "is associated with",
            "causes": "is associated with",
            "will result in": "may result in",
            
            # Causal language without proper identification
            "impact of": "association between",
            "effect of": "relationship between",
            "due to": "associated with",
            "because of": "in relation to",
            
            # Predetermined outcomes
            "we expect": "we hypothesize",
            "we will find": "we may find",
            "confirms that": "is consistent with",
            "validates": "supports",
            
            # Non-academic language
            "amazing": "notable",
            "incredible": "significant",
            "huge": "substantial",
            "tiny": "minimal",
        }
        
        # Define required elements for different documentation types
        self.required_elements = {
            "hypothesis": [
                r"H\d+:",  # Hypothesis number
                r"(Null|Alternative):",  # Hypothesis type
                r"Variables:",  # Variables involved
                r"Expected (relationship|effect|association):",  # Expected outcome
                r"Identification strategy:",  # How it will be tested
            ],
            "results": [
                r"\[PLACEHOLDER:",  # Placeholder markers
                r"Confidence interval:",  # Statistical uncertainty
                r"Robustness:",  # Robustness checks
                r"Limitations:",  # Acknowledged limitations
            ],
            "methodology": [
                r"Assumptions:",  # Model assumptions
                r"Data requirements:",  # Data needs
                r"Validation:",  # Validation approach
            ]
        }
        
        # Currency-related patterns to check
        self.currency_patterns = {
            "missing_specification": r"\b\d+\.?\d*\s*(?!YER|USD|%)",  # Numbers without currency
            "mixed_currencies": r"(?:YER.*USD|USD.*YER)",  # Mixed currencies in same context
            "exchange_rate_mention": r"exchange rate|currency|YER|USD",
            "price_comparison": r"(compar|analyz|assess|evaluat).*price",
            "regional_comparison": r"(north|south|Sana'?a|Aden|Houthi|government).*(?:price|cost|value)",
            "exchange_rate_values": r"\b(535|2000|2,000)\b.*(?:YER|exchange)",
        }
        
        # Yemen-specific exchange rate requirements
        self.yemen_exchange_rates = {
            "north": 535,  # YER/USD
            "south": 2000,  # YER/USD
            "regions": ["north", "south", "Sana'a", "Aden", "Houthi", "government-controlled"],
            "required_mentions": [
                "currency conversion",
                "exchange rate",
                "535 YER/USD",
                "2000 YER/USD",
                "2,000 YER/USD"
            ]
        }
        
    def scan_documentation(self, 
                          paths: Optional[List[str]] = None,
                          exclude_patterns: Optional[List[str]] = None) -> ConsistencyReport:
        """
        Scan documentation files for consistency issues.
        
        Args:
            paths: Specific paths to scan (default: entire docs/ directory)
            exclude_patterns: Patterns to exclude from scanning
        
        Returns:
            ConsistencyReport with all findings
        """
        if paths is None:
            paths = ["docs/"]
        
        if exclude_patterns is None:
            exclude_patterns = ["__pycache__", ".git", "node_modules", "venv"]
        
        # Reset state
        self.issues = []
        self.cross_references = defaultdict(set)
        self.hypothesis_inventory = {}
        
        # Scan all markdown files
        all_files = []
        for path in paths:
            full_path = self.root_path / path
            if full_path.exists():
                if full_path.is_file():
                    all_files.append(full_path)
                else:
                    all_files.extend(self._find_markdown_files(full_path, exclude_patterns))
        
        # Process each file
        for file_path in all_files:
            self._check_file(file_path)
        
        # Check cross-references validity
        self._validate_cross_references()
        
        # Generate report
        return self._generate_report(len(all_files))
    
    def _find_markdown_files(self, directory: Path, exclude_patterns: List[str]) -> List[Path]:
        """Find all markdown files in directory, excluding certain patterns."""
        markdown_files = []
        
        for root, dirs, files in os.walk(directory):
            # Remove excluded directories
            dirs[:] = [d for d in dirs if not any(pattern in d for pattern in exclude_patterns)]
            
            # Add markdown files
            for file in files:
                if file.endswith(('.md', '.MD')):
                    markdown_files.append(Path(root) / file)
        
        return markdown_files
    
    def _check_file(self, file_path: Path):
        """Check a single documentation file for consistency issues."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.splitlines()
            
            # Try to get relative path, fallback to absolute if not possible
            try:
                relative_path = file_path.relative_to(self.root_path)
            except ValueError:
                relative_path = file_path
            
            # Check based on file type
            if 'hypothesis' in str(file_path).lower() or 'hypotheses' in str(file_path).lower():
                self._check_hypothesis_file(relative_path, lines)
            
            if 'results' in str(file_path).lower() or 'findings' in str(file_path).lower():
                self._check_results_file(relative_path, lines)
            
            # General checks for all files
            self._check_forbidden_phrases(relative_path, lines)
            self._check_cross_references(relative_path, content)
            self._check_currency_consistency(relative_path, lines)
            self._check_currency_methodology(relative_path, lines, content)
            self._check_formatting_consistency(relative_path, lines)
            
        except Exception as e:
            # Use file_path if relative_path not available
            self.issues.append(Issue(
                file_path=str(file_path),
                line_number=0,
                issue_type="file_error",
                severity="error",
                message=f"Error reading file: {str(e)}"
            ))
    
    def _check_forbidden_phrases(self, file_path: Path, lines: List[str]):
        """Check for forbidden phrases that indicate poor academic writing."""
        for line_num, line in enumerate(lines, 1):
            line_lower = line.lower()
            
            for forbidden, suggestion in self.forbidden_phrases.items():
                if forbidden in line_lower:
                    self.issues.append(Issue(
                        file_path=str(file_path),
                        line_number=line_num,
                        issue_type="forbidden_phrase",
                        severity="warning",
                        message=f"Found forbidden phrase: '{forbidden}'",
                        context=line.strip(),
                        suggestion=f"Consider using: '{suggestion}'"
                    ))
    
    def _check_hypothesis_file(self, file_path: Path, lines: List[str]):
        """Check hypothesis files for proper formatting."""
        content = '\n'.join(lines)
        
        # Extract hypotheses
        hypothesis_pattern = r'(H\d+):\s*(.*?)(?=H\d+:|$)'
        hypotheses = re.findall(hypothesis_pattern, content, re.DOTALL)
        
        for h_num, h_content in hypotheses:
            # Check for required elements
            for element_pattern in self.required_elements["hypothesis"]:
                if not re.search(element_pattern, h_content, re.IGNORECASE):
                    self.issues.append(Issue(
                        file_path=str(file_path),
                        line_number=0,
                        issue_type="missing_hypothesis_element",
                        severity="error",
                        message=f"{h_num} missing required element: {element_pattern}",
                        suggestion="Ensure all hypotheses include null/alternative statements, variables, expected relationships, and identification strategy"
                    ))
            
            # Store hypothesis inventory
            self.hypothesis_inventory[h_num] = {
                "file": str(file_path),
                "content": h_content.strip()
            }
    
    def _check_results_file(self, file_path: Path, lines: List[str]):
        """Check results files for proper placeholder usage."""
        content = '\n'.join(lines)
        
        # Check for hardcoded values that should be placeholders
        number_pattern = r'\b\d+\.?\d*\s*(?:percent|%|percentage points|pp)\b'
        p_value_pattern = r'p\s*[<=]\s*0\.\d+'
        
        for line_num, line in enumerate(lines, 1):
            # Check for specific numerical results
            if re.search(number_pattern, line) and '[PLACEHOLDER' not in line:
                self.issues.append(Issue(
                    file_path=str(file_path),
                    line_number=line_num,
                    issue_type="hardcoded_result",
                    severity="error",
                    message="Found hardcoded numerical result instead of placeholder",
                    context=line.strip(),
                    suggestion="Use [PLACEHOLDER: description] for results that will be filled in"
                ))
            
            # Check for p-values
            if re.search(p_value_pattern, line) and '[PLACEHOLDER' not in line:
                self.issues.append(Issue(
                    file_path=str(file_path),
                    line_number=line_num,
                    issue_type="hardcoded_pvalue",
                    severity="error",
                    message="Found hardcoded p-value instead of placeholder",
                    context=line.strip(),
                    suggestion="Use [PLACEHOLDER: p-value] for statistical significance"
                ))
        
        # Check for required elements in results
        for element_pattern in self.required_elements["results"]:
            if not re.search(element_pattern, content, re.IGNORECASE):
                self.issues.append(Issue(
                    file_path=str(file_path),
                    line_number=0,
                    issue_type="missing_results_element",
                    severity="warning",
                    message=f"Results file missing: {element_pattern}",
                    suggestion="Include confidence intervals, robustness checks, and limitations"
                ))
    
    def _check_cross_references(self, file_path: Path, content: str):
        """Extract and validate cross-references."""
        # Find all markdown links
        link_pattern = r'\[([^\]]+)\]\(([^\)]+)\)'
        links = re.findall(link_pattern, content)
        
        for link_text, link_target in links:
            if link_target.startswith(('http://', 'https://', '#')):
                continue  # Skip external links and anchors
            
            # Store cross-reference for validation
            self.cross_references[str(file_path)].add(link_target)
    
    def _check_currency_consistency(self, file_path: Path, lines: List[str]):
        """Check for currency-related consistency issues."""
        for line_num, line in enumerate(lines, 1):
            # Check for numbers without currency specification
            if re.search(self.currency_patterns["missing_specification"], line):
                # Only flag if line contains price/cost/value context
                if any(word in line.lower() for word in ['price', 'cost', 'value', 'rate']):
                    self.issues.append(Issue(
                        file_path=str(file_path),
                        line_number=line_num,
                        issue_type="missing_currency",
                        severity="warning",
                        message="Numerical value without currency specification",
                        context=line.strip(),
                        suggestion="Specify currency (YER or USD) for all monetary values"
                    ))
            
            # Check for mixed currencies
            if re.search(self.currency_patterns["mixed_currencies"], line):
                self.issues.append(Issue(
                    file_path=str(file_path),
                    line_number=line_num,
                    issue_type="mixed_currencies",
                    severity="error",
                    message="Mixed currencies (YER and USD) in same context",
                    context=line.strip(),
                    suggestion="Convert to common currency before comparison"
                ))
    
    def _check_currency_methodology(self, file_path: Path, lines: List[str], content: str):
        """
        Check for Yemen-specific currency methodology requirements.
        
        This method ensures that any price comparison between regions explicitly
        mentions currency conversion and the correct exchange rates:
        - Northern areas (Houthi-controlled): 535 YER/USD
        - Southern areas (Government-controlled): 2,000+ YER/USD
        """
        # Check if file contains price comparisons or regional analysis
        has_price_comparison = bool(re.search(self.currency_patterns["price_comparison"], content, re.IGNORECASE))
        has_regional_comparison = bool(re.search(self.currency_patterns["regional_comparison"], content, re.IGNORECASE))
        
        if has_price_comparison or has_regional_comparison:
            # Check if proper exchange rate methodology is mentioned
            mentions_conversion = any(
                mention in content.lower() 
                for mention in ["currency conversion", "exchange rate", "convert to usd", "common currency"]
            )
            
            mentions_north_rate = bool(re.search(r"\b535\b.*(?:YER|exchange)", content, re.IGNORECASE))
            mentions_south_rate = bool(re.search(r"\b(?:2000|2,000)\b.*(?:YER|exchange)", content, re.IGNORECASE))
            
            # Check if discussing cross-regional comparison
            regions_mentioned = []
            for region in self.yemen_exchange_rates["regions"]:
                if region.lower() in content.lower():
                    regions_mentioned.append(region)
            
            cross_regional = len(regions_mentioned) >= 2 or any(
                pair in content.lower() 
                for pair in ["north.*south", "south.*north", "sana'a.*aden", "aden.*sana'a", 
                           "houthi.*government", "government.*houthi"]
            )
            
            # Issue warnings/errors based on findings
            if cross_regional and not mentions_conversion:
                self.issues.append(Issue(
                    file_path=str(file_path),
                    line_number=0,
                    issue_type="missing_currency_conversion",
                    severity="error",
                    message="Cross-regional price comparison without mentioning currency conversion",
                    context=f"Regions mentioned: {', '.join(regions_mentioned)}",
                    suggestion="Must explicitly state currency conversion when comparing prices between north (535 YER/USD) and south (2,000+ YER/USD)"
                ))
            
            if cross_regional and not (mentions_north_rate and mentions_south_rate):
                missing_rates = []
                if not mentions_north_rate:
                    missing_rates.append("Northern rate (535 YER/USD)")
                if not mentions_south_rate:
                    missing_rates.append("Southern rate (2,000+ YER/USD)")
                
                self.issues.append(Issue(
                    file_path=str(file_path),
                    line_number=0,
                    issue_type="missing_exchange_rates",
                    severity="error",
                    message=f"Missing exchange rate specifications: {', '.join(missing_rates)}",
                    suggestion="Include both exchange rates when discussing cross-regional price comparisons"
                ))
            
            # Check for specific problematic patterns
            for line_num, line in enumerate(lines, 1):
                # Check for price comparisons without currency specification
                if re.search(r"price.*(?:higher|lower|increase|decrease|differ)", line, re.IGNORECASE):
                    if not re.search(r"(YER|USD|currency|exchange)", line, re.IGNORECASE):
                        self.issues.append(Issue(
                            file_path=str(file_path),
                            line_number=line_num,
                            issue_type="price_comparison_without_currency",
                            severity="warning",
                            message="Price comparison without currency specification",
                            context=line.strip(),
                            suggestion="Specify whether prices are in YER or USD, and if converted"
                        ))
                
                # Check for regional mentions with prices
                if any(region.lower() in line.lower() for region in self.yemen_exchange_rates["regions"]):
                    if re.search(r"\b\d+\.?\d*\b", line) and "price" in line.lower():
                        if not re.search(r"(YER|USD)", line):
                            self.issues.append(Issue(
                                file_path=str(file_path),
                                line_number=line_num,
                                issue_type="regional_price_without_currency",
                                severity="error",
                                message="Regional price mentioned without currency specification",
                                context=line.strip(),
                                suggestion="Always specify currency (YER or USD) when mentioning regional prices"
                            ))
        
        # Check for incorrect exchange rate values
        incorrect_rate_patterns = [
            (r"(?<!535\s)(?<!2000\s)(?<!2,000\s)\b\d{3}\b\s*YER\s*(?:per|/)\s*USD", "Possible incorrect exchange rate"),
            (r"1\s*USD\s*=\s*\d{2,3}\s*YER", "Exchange rate seems too low for Yemen"),
            (r"exchange rate.*same.*across.*Yemen", "Exchange rates differ significantly between regions"),
            (r"approximately\s*\d{2,3}\s*YER\s*per\s*USD", "Exchange rate seems too low for Yemen context")
        ]
        
        for line_num, line in enumerate(lines, 1):
            for pattern, message in incorrect_rate_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    self.issues.append(Issue(
                        file_path=str(file_path),
                        line_number=line_num,
                        issue_type="incorrect_exchange_rate",
                        severity="error",
                        message=message,
                        context=line.strip(),
                        suggestion="North: ~535 YER/USD, South: ~2,000+ YER/USD"
                    ))
    
    def _check_formatting_consistency(self, file_path: Path, lines: List[str]):
        """Check for consistent formatting."""
        # Check heading hierarchy
        heading_levels = []
        for line_num, line in enumerate(lines, 1):
            if line.startswith('#'):
                level = len(line.split()[0])
                heading_levels.append((line_num, level))
        
        # Check for skipped heading levels
        for i in range(1, len(heading_levels)):
            prev_level = heading_levels[i-1][1]
            curr_level = heading_levels[i][1]
            
            if curr_level > prev_level + 1:
                self.issues.append(Issue(
                    file_path=str(file_path),
                    line_number=heading_levels[i][0],
                    issue_type="heading_hierarchy",
                    severity="warning",
                    message=f"Skipped heading level (from H{prev_level} to H{curr_level})",
                    suggestion="Use sequential heading levels"
                ))
    
    def _validate_cross_references(self):
        """Validate all cross-references point to existing files."""
        all_files = set()
        for root, _, files in os.walk(self.root_path):
            for file in files:
                if file.endswith(('.md', '.MD')):
                    rel_path = Path(root).relative_to(self.root_path) / file
                    all_files.add(str(rel_path))
        
        # Check each cross-reference
        for source_file, references in self.cross_references.items():
            for ref in references:
                # Resolve relative reference
                source_dir = Path(source_file).parent
                target_path = (source_dir / ref).resolve().relative_to(self.root_path.resolve())
                
                if str(target_path) not in all_files:
                    self.issues.append(Issue(
                        file_path=source_file,
                        line_number=0,
                        issue_type="broken_reference",
                        severity="error",
                        message=f"Broken cross-reference: {ref}",
                        suggestion=f"Check if target file exists: {target_path}"
                    ))
    
    def _generate_report(self, total_files: int) -> ConsistencyReport:
        """Generate comprehensive consistency report."""
        report = ConsistencyReport(
            timestamp=datetime.now().isoformat(),
            total_files_scanned=total_files,
            total_issues=len(self.issues)
        )
        
        # Organize issues
        for issue in self.issues:
            report.issues_by_type[issue.issue_type] = report.issues_by_type.get(issue.issue_type, 0) + 1
            report.issues_by_severity[issue.severity] = report.issues_by_severity.get(issue.severity, 0) + 1
            report.issues_by_file[issue.file_path].append(issue)
        
        report.cross_reference_map = dict(self.cross_references)
        report.hypothesis_inventory = self.hypothesis_inventory
        
        return report
    
    def print_report(self, report: ConsistencyReport, verbose: bool = False):
        """Print consistency report to console."""
        print("\n" + "="*80)
        print("DOCUMENTATION CONSISTENCY REPORT")
        print("="*80)
        print(f"Timestamp: {report.timestamp}")
        print(f"Files scanned: {report.total_files_scanned}")
        print(f"Total issues: {report.total_issues}")
        
        # Summary by severity
        print("\nIssues by Severity:")
        for severity in ['error', 'warning', 'info']:
            count = report.issues_by_severity.get(severity, 0)
            print(f"  {severity.upper()}: {count}")
        
        # Summary by type
        print("\nIssues by Type:")
        for issue_type, count in sorted(report.issues_by_type.items()):
            print(f"  {issue_type}: {count}")
        
        # Hypothesis inventory
        if report.hypothesis_inventory:
            print(f"\nHypotheses Found: {len(report.hypothesis_inventory)}")
            for h_num in sorted(report.hypothesis_inventory.keys()):
                print(f"  {h_num} in {report.hypothesis_inventory[h_num]['file']}")
        
        # Detailed issues by file
        if verbose:
            print("\nDetailed Issues by File:")
            for file_path, issues in sorted(report.issues_by_file.items()):
                print(f"\n{file_path} ({len(issues)} issues):")
                for issue in issues:
                    print(f"  Line {issue.line_number}: [{issue.severity}] {issue.message}")
                    if issue.context:
                        print(f"    Context: {issue.context}")
                    if issue.suggestion:
                        print(f"    Suggestion: {issue.suggestion}")
        
        print("\n" + "="*80)
    
    def save_report(self, report: ConsistencyReport, output_path: str):
        """Save report to JSON file."""
        # Convert report to JSON-serializable format
        report_dict = {
            "timestamp": report.timestamp,
            "total_files_scanned": report.total_files_scanned,
            "total_issues": report.total_issues,
            "issues_by_type": report.issues_by_type,
            "issues_by_severity": report.issues_by_severity,
            "issues": []
        }
        
        # Add detailed issues
        for file_path, issues in report.issues_by_file.items():
            for issue in issues:
                report_dict["issues"].append({
                    "file_path": issue.file_path,
                    "line_number": issue.line_number,
                    "issue_type": issue.issue_type,
                    "severity": issue.severity,
                    "message": issue.message,
                    "context": issue.context,
                    "suggestion": issue.suggestion
                })
        
        # Add hypothesis inventory
        report_dict["hypothesis_inventory"] = report.hypothesis_inventory
        
        # Add cross-reference map
        report_dict["cross_reference_map"] = {
            k: list(v) for k, v in report.cross_reference_map.items()
        }
        
        # Save to file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_dict, f, indent=2)
        
        print(f"\nReport saved to: {output_path}")
    
    def generate_markdown_report(self, report: ConsistencyReport) -> str:
        """Generate a markdown-formatted report."""
        md_lines = [
            "# Documentation Consistency Report",
            f"\n**Generated:** {report.timestamp}",
            f"\n**Files Scanned:** {report.total_files_scanned}",
            f"**Total Issues:** {report.total_issues}",
            "\n## Summary",
            "\n### Issues by Severity\n"
        ]
        
        # Severity table
        md_lines.append("| Severity | Count |")
        md_lines.append("|----------|-------|")
        for severity in ['error', 'warning', 'info']:
            count = report.issues_by_severity.get(severity, 0)
            md_lines.append(f"| {severity.upper()} | {count} |")
        
        # Issue type table
        md_lines.extend([
            "\n### Issues by Type\n",
            "| Issue Type | Count |",
            "|------------|-------|"
        ])
        
        for issue_type, count in sorted(report.issues_by_type.items()):
            md_lines.append(f"| {issue_type} | {count} |")
        
        # Critical issues
        md_lines.append("\n## Critical Issues (Errors)\n")
        
        error_count = 0
        for file_path, issues in sorted(report.issues_by_file.items()):
            errors = [i for i in issues if i.severity == 'error']
            if errors:
                md_lines.append(f"\n### {file_path}\n")
                for issue in errors:
                    md_lines.append(f"- **Line {issue.line_number}:** {issue.message}")
                    if issue.suggestion:
                        md_lines.append(f"  - *Suggestion:* {issue.suggestion}")
                error_count += len(errors)
        
        if error_count == 0:
            md_lines.append("No critical errors found! ✓")
        
        # Currency methodology issues section
        currency_issues = []
        for file_path, issues in report.issues_by_file.items():
            currency_related = [i for i in issues if any(
                keyword in i.issue_type 
                for keyword in ['currency', 'exchange_rate', 'price_comparison']
            )]
            if currency_related:
                currency_issues.extend(currency_related)
        
        if currency_issues:
            md_lines.extend([
                "\n## Currency Methodology Issues\n",
                "**Critical for Yemen Analysis:** Price comparisons must account for exchange rate differences between regions.\n"
            ])
            
            # Group by issue type
            currency_by_type = defaultdict(list)
            for issue in currency_issues:
                currency_by_type[issue.issue_type].append(issue)
            
            for issue_type, issues in sorted(currency_by_type.items()):
                md_lines.append(f"\n### {issue_type.replace('_', ' ').title()}\n")
                for issue in issues[:5]:  # Show first 5 of each type
                    md_lines.append(f"- **{issue.file_path}** (Line {issue.line_number}): {issue.message}")
                if len(issues) > 5:
                    md_lines.append(f"- *... and {len(issues) - 5} more*")
        
        # Hypothesis inventory
        if report.hypothesis_inventory:
            md_lines.extend([
                "\n## Hypothesis Inventory\n",
                "| Hypothesis | File |",
                "|------------|------|"
            ])
            
            for h_num in sorted(report.hypothesis_inventory.keys()):
                file_path = report.hypothesis_inventory[h_num]['file']
                md_lines.append(f"| {h_num} | {file_path} |")
        
        return '\n'.join(md_lines)


def main():
    """Main entry point for the consistency checker."""
    parser = argparse.ArgumentParser(
        description="Check documentation consistency and academic standards"
    )
    parser.add_argument(
        "paths",
        nargs="*",
        default=["docs/"],
        help="Paths to scan (default: docs/)"
    )
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Show detailed issue information"
    )
    parser.add_argument(
        "-o", "--output",
        help="Save report to JSON file"
    )
    parser.add_argument(
        "-m", "--markdown",
        help="Save report as markdown file"
    )
    parser.add_argument(
        "--exclude",
        nargs="+",
        help="Patterns to exclude from scanning"
    )
    
    args = parser.parse_args()
    
    # Create checker instance
    checker = DocumentationConsistencyChecker()
    
    # Run scan
    print("Scanning documentation for consistency issues...")
    report = checker.scan_documentation(
        paths=args.paths,
        exclude_patterns=args.exclude
    )
    
    # Print report
    checker.print_report(report, verbose=args.verbose)
    
    # Save reports if requested
    if args.output:
        checker.save_report(report, args.output)
    
    if args.markdown:
        md_report = checker.generate_markdown_report(report)
        with open(args.markdown, 'w', encoding='utf-8') as f:
            f.write(md_report)
        print(f"\nMarkdown report saved to: {args.markdown}")
    
    # Exit with error code if critical issues found
    if report.issues_by_severity.get('error', 0) > 0:
        return 1
    return 0


if __name__ == "__main__":
    exit(main())