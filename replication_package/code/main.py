"""Main entry point for Yemen Market Integration v2 API."""

import os
from pathlib import Path

# Import with fallbacks
try:
    import uvicorn
    UVICORN_AVAILABLE = True
except ImportError:
    UVICORN_AVAILABLE = False
    uvicorn = None

try:
    from dotenv import load_dotenv
except ImportError:
    load_dotenv = lambda *args, **kwargs: None

from src.interfaces.api.rest import create_app
from src.interfaces.api.rest.dependencies import set_container
from src.shared.container import Container


def create_container() -> Container:
    """Create and configure the dependency injection container."""
    container = Container()
    
    # Configure from environment - handle both full and simple containers
    try:
        container.config.database.url.from_env("DATABASE_URL", "postgresql://localhost/yemen_market")
        container.config.cache.type.from_env("CACHE_TYPE", "memory")
        container.config.cache.default_ttl.from_env("CACHE_TTL", 3600)
        container.config.cache.redis.url.from_env("REDIS_URL", "redis://localhost:6379")
        container.config.cache.memory.max_size.from_env("CACHE_MAX_SIZE", 1000)
        container.config.events.type.from_env("EVENT_BUS_TYPE", "inmemory")
        container.config.events.queue_size.from_env("EVENT_QUEUE_SIZE", 10000)
        container.config.external.hdx.timeout.from_env("HDX_TIMEOUT", 30)
        container.config.external.wfp.api_key.from_env("WFP_API_KEY", "")
        container.config.external.acled.api_key.from_env("ACLED_API_KEY", "")
        container.config.external.acled.email.from_env("ACLED_EMAIL", "")
        container.config.storage.policy_results_path.from_env("POLICY_RESULTS_PATH", "data/policy_results")
    except AttributeError:
        # Simple container - just set basic values
        container.config.database.url.from_value(os.getenv("DATABASE_URL", "postgresql://localhost/yemen_market"))
        container.config.cache.type.from_value(os.getenv("CACHE_TYPE", "memory"))
        container.config.external.hdx.timeout.from_value(int(os.getenv("HDX_TIMEOUT", "30")))
        container.config.external.wfp.api_key.from_value(os.getenv("WFP_API_KEY", ""))
        container.config.external.acled.api_key.from_value(os.getenv("ACLED_API_KEY", ""))
        container.config.external.acled.email.from_value(os.getenv("ACLED_EMAIL", ""))
    
    return container


def main():
    """Run the application."""
    # Load environment variables
    env_file = Path(__file__).parent / ".env"
    if env_file.exists():
        load_dotenv(env_file)
    
    # Create container
    container = create_container()
    
    # Set global container for dependencies
    set_container(container)
    
    # Create app
    app = create_app(container)
    
    # Run with uvicorn if available
    if UVICORN_AVAILABLE and uvicorn:
        uvicorn.run(
            app,
            host=os.getenv("API_HOST", "0.0.0.0"),
            port=int(os.getenv("API_PORT", "8000")),
            reload=os.getenv("API_RELOAD", "false").lower() == "true",
            log_level=os.getenv("API_LOG_LEVEL", "info").lower()
        )
    else:
        print("⚠️  uvicorn not available. Install with: pip install uvicorn")
        print("   API not started. Container and app created successfully.")
        return app


if __name__ == "__main__":
    main()