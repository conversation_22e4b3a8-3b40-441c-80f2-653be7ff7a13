#!/usr/bin/env python3
"""Run specification curve analysis for Yemen market integration.

This script runs 1000+ model specifications to test the robustness of
the main finding that exchange rates drive price differentials in Yemen.
"""

import argparse
import json
import logging
import sys
from datetime import datetime
from pathlib import Path

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.core.analysis.robustness.specification_curve import (
    SpecificationCurve,
    SpecificationGenerator,
    SpecificationCurveVisualizer,
)
# from src.infrastructure.data_loader import DataLoader
# from src.infrastructure.logging import setup_logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_panel_data() -> pd.DataFrame:
    """Load balanced panel data for analysis.
    
    Returns:
        DataFrame with price observations including USD prices and exchange rates
    """
    logger.info("Loading panel data...")
    
    # Try to load from processed data first
    processed_path = Path("data/processed/balanced_panel.parquet")
    if processed_path.exists():
        logger.info(f"Loading from {processed_path}")
        df = pd.read_parquet(processed_path)
        
        # Ensure date column is datetime
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
        
        # Rename columns to match expected names
        column_mapping = {
            'usdprice': 'price_usd',
            'price': 'price_yer',
            'control_zone': 'currency_zone'
        }
        
        for old_col, new_col in column_mapping.items():
            if old_col in df.columns and new_col not in df.columns:
                df = df.rename(columns={old_col: new_col})
        
        # Add exchange rate if missing (derive from prices)
        if 'exchange_rate_used' not in df.columns and 'price_yer' in df.columns and 'price_usd' in df.columns:
            df['exchange_rate_used'] = df['price_yer'] / df['price_usd'].replace(0, np.nan)
            df['exchange_rate_used'] = df['exchange_rate_used'].fillna(df['exchange_rate_used'].median())
        
        # Validate required columns
        required_cols = ['market_id', 'date', 'commodity', 'price_usd', 
                        'price_yer', 'exchange_rate_used', 'currency_zone']
        missing_cols = set(required_cols) - set(df.columns)
        if missing_cols:
            logger.warning(f"Missing columns after renaming: {missing_cols}")
        
        logger.info(f"Loaded {len(df):,} observations from {df['market_id'].nunique()} markets")
        return df
    
    # Otherwise, raise error
    raise FileNotFoundError(
        "Processed data not found. Please run the data pipeline first or "
        "specify a data path using --data-path argument"
    )


def create_specifications(n_specs: int = 1000) -> SpecificationGenerator:
    """Create specification generator with Yemen-specific choices.
    
    Args:
        n_specs: Target number of specifications
        
    Returns:
        Configured SpecificationGenerator
    """
    base_specification = {
        "hypothesis_tests": ["H1"],  # Exchange rate mechanism
        "target_variable": "exchange_rate_used",
        "country": "Yemen"
    }
    
    # Define specification choices
    generator = SpecificationGenerator(
        base_specification=base_specification,
        dependent_vars=["price_usd", "log_price_usd"],
        fixed_effects=["entity", "time", "twoway"],
        clustering=["market", "governorate", "commodity"],
        control_sets=[
            [],  # No controls
            ["conflict_intensity"],  # Basic
            ["conflict_intensity", "aid_distribution"],  # Extended
            ["conflict_intensity", "aid_distribution", "population_density"],  # Full
            ["conflict_intensity", "aid_distribution", "population_density", "distance_to_border"]  # Complete
        ],
        sample_periods=[
            ("2019-01-01", "2024-12-31"),  # Full sample
            ("2019-01-01", "2020-02-29"),  # Pre-COVID
            ("2020-03-01", "2024-12-31"),  # COVID period
            ("2019-01-01", "2021-12-31"),  # Pre-escalation
            ("2022-01-01", "2024-12-31"),  # Post-escalation
            ("2019-01-01", "2022-12-31"),  # Exclude recent
            ("2020-01-01", "2023-12-31"),  # Mid-period
        ],
        functional_forms=["linear", "log", "polynomial"],
        estimation_methods=["ols", "fe", "re", "ife", "bayesian"]
    )
    
    return generator


def run_robustness_analysis(data: pd.DataFrame, 
                           n_specs: int = 1000,
                           output_dir: Path = None) -> dict:
    """Run full robustness analysis.
    
    Args:
        data: Panel data for analysis
        n_specs: Number of specifications to run
        output_dir: Directory for outputs
        
    Returns:
        Dictionary with results and paths to outputs
    """
    if output_dir is None:
        output_dir = Path("results/robustness") / datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Running robustness analysis with {n_specs} specifications")
    logger.info(f"Output directory: {output_dir}")
    
    # Create specifications
    generator = create_specifications(n_specs)
    
    # Initialize specification curve
    curve = SpecificationCurve(
        base_specification=generator.base_specification,
        n_jobs=-1  # Use all CPU cores
    )
    
    # Generate and add specifications
    specifications = generator.generate_specifications(max_specs=n_specs)
    curve.add_specifications(specifications)
    
    logger.info(f"Generated {len(specifications)} unique specifications")
    
    # Save specifications for reproducibility
    specs_path = output_dir / "specifications.json"
    with open(specs_path, 'w') as f:
        json.dump(specifications, f, indent=2, default=str)
    
    # Run all specifications
    logger.info("Running specifications (this may take several minutes)...")
    results = curve.run_all_specifications(data, show_progress=True)
    
    # Analyze stability
    stability_metrics = curve.analyze_stability()
    logger.info("Stability Analysis:")
    
    if 'warning' in stability_metrics:
        logger.warning(stability_metrics['warning'])
        logger.info(f"  Valid specifications: 0/{len(specifications)}")
    else:
        logger.info(f"  Valid specifications: {stability_metrics['n_valid']}/{stability_metrics['n_total']}")
        logger.info(f"  Coefficient mean: {stability_metrics['coefficient_mean']:.3f}")
        logger.info(f"  Coefficient CV: {stability_metrics['coefficient_cv']:.3f}")
        logger.info(f"  % Significant: {stability_metrics['pct_significant']:.1f}%")
        logger.info(f"  % Positive: {stability_metrics['pct_positive']:.1f}%")
    
    # Save stability metrics
    metrics_path = output_dir / "stability_metrics.json"
    with open(metrics_path, 'w') as f:
        json.dump(stability_metrics, f, indent=2)
    
    # Create visualizations
    logger.info("Creating visualizations...")
    viz = SpecificationCurveVisualizer(results)
    
    # Main specification curve
    curve_path = output_dir / "specification_curve.png"
    fig_curve = viz.plot_specification_curve(
        highlight_main="spec_0000",  # Highlight first spec as "main"
        save_path=curve_path
    )
    plt.close(fig_curve)
    
    # Coefficient distribution
    dist_path = output_dir / "coefficient_distribution.png"
    fig_dist = viz.plot_coefficient_distribution(save_path=dist_path)
    plt.close(fig_dist)
    
    # Robustness table
    robustness_table = viz.create_robustness_table()
    table_path = output_dir / "robustness_summary.csv"
    robustness_table.to_csv(table_path, index=False)
    
    # Save detailed results
    results_df = pd.DataFrame([
        {
            'specification_id': r.specification_id,
            'coefficient': r.coefficient,
            'std_error': r.std_error,
            'p_value': r.p_value,
            'ci_lower': r.ci_lower,
            'ci_upper': r.ci_upper,
            'n_observations': r.n_observations,
            'r_squared': r.r_squared,
            'methodology_valid': r.methodology_valid,
            'error': r.error,
            **r.specification
        }
        for r in results
    ])
    
    results_path = output_dir / "all_results.csv"
    results_df.to_csv(results_path, index=False)
    
    # Create summary report
    report_path = output_dir / "robustness_report.txt"
    with open(report_path, 'w') as f:
        f.write("SPECIFICATION CURVE ANALYSIS REPORT\n")
        f.write("="*50 + "\n\n")
        f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total specifications: {len(specifications)}\n")
        
        if 'warning' in stability_metrics:
            f.write(f"Valid specifications: 0\n\n")
            f.write("KEY FINDINGS:\n")
            f.write("-"*30 + "\n")
            f.write(f"WARNING: {stability_metrics['warning']}\n")
            f.write("No valid specifications passed methodology validation.\n")
            f.write("Please check data contains required columns:\n")
            f.write("  - price_usd, price_yer, exchange_rate_used, currency_zone\n")
        else:
            f.write(f"Valid specifications: {stability_metrics['n_valid']}\n\n")
            
            f.write("KEY FINDINGS:\n")
            f.write("-"*30 + "\n")
            f.write(f"1. Exchange rate coefficient is {'STABLE' if stability_metrics['coefficient_cv'] < 0.2 else 'UNSTABLE'}\n")
            f.write(f"   - Mean: {stability_metrics['coefficient_mean']:.3f}\n")
            f.write(f"   - CV: {stability_metrics['coefficient_cv']:.3f}\n")
            f.write(f"   - Range: [{stability_metrics['coefficient_min']:.3f}, {stability_metrics['coefficient_max']:.3f}]\n\n")
            
            f.write(f"2. Direction is {'CONSISTENT' if stability_metrics['pct_positive'] > 95 else 'INCONSISTENT'}\n")
            f.write(f"   - {stability_metrics['pct_positive']:.1f}% of specifications show positive effect\n\n")
            
            f.write(f"3. Statistical significance is {'ROBUST' if stability_metrics['pct_significant'] > 90 else 'WEAK'}\n")
            f.write(f"   - {stability_metrics['pct_significant']:.1f}% of specifications are significant at 5% level\n\n")
        
        f.write("ROBUSTNESS BY SPECIFICATION CHOICE:\n")
        f.write("-"*30 + "\n")
        f.write(robustness_table.to_string(index=False))
        
        f.write("\n\nCONCLUSION:\n")
        f.write("-"*30 + "\n")
        if 'warning' in stability_metrics:
            f.write("Cannot assess robustness without valid specifications.\n")
            f.write("Data preprocessing may be required to ensure methodology compliance.\n")
        elif stability_metrics['coefficient_cv'] < 0.2 and stability_metrics['pct_positive'] > 95:
            f.write("The main finding that exchange rates drive price differentials in Yemen\n")
            f.write("is HIGHLY ROBUST across alternative specifications.\n")
        else:
            f.write("The results show some sensitivity to specification choices.\n")
            f.write("Further investigation of specific specifications is warranted.\n")
    
    logger.info(f"Analysis complete. Results saved to {output_dir}")
    
    return {
        'stability_metrics': stability_metrics,
        'output_dir': str(output_dir),
        'n_specifications': len(specifications),
        'n_valid': stability_metrics.get('n_valid', 0),
        'files': {
            'specifications': str(specs_path),
            'results': str(results_path),
            'metrics': str(metrics_path),
            'curve_plot': str(curve_path),
            'dist_plot': str(dist_path),
            'table': str(table_path),
            'report': str(report_path)
        }
    }


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Run specification curve analysis for Yemen market integration"
    )
    parser.add_argument(
        "--n-specs",
        type=int,
        default=1000,
        help="Number of specifications to run (default: 1000)"
    )
    parser.add_argument(
        "--output-dir",
        type=Path,
        help="Output directory (default: auto-generated)"
    )
    parser.add_argument(
        "--data-path",
        type=Path,
        help="Path to panel data file"
    )
    
    args = parser.parse_args()
    
    try:
        # Load data
        if args.data_path and args.data_path.exists():
            logger.info(f"Loading data from {args.data_path}")
            data = pd.read_parquet(args.data_path) if args.data_path.suffix == '.parquet' else pd.read_csv(args.data_path)
            
            # Apply same column mapping as in load_panel_data
            column_mapping = {
                'usdprice': 'price_usd',
                'price': 'price_yer',
                'control_zone': 'currency_zone'
            }
            
            for old_col, new_col in column_mapping.items():
                if old_col in data.columns and new_col not in data.columns:
                    data = data.rename(columns={old_col: new_col})
            
            # Add exchange rate if missing
            if 'exchange_rate_used' not in data.columns and 'price_yer' in data.columns and 'price_usd' in data.columns:
                data['exchange_rate_used'] = data['price_yer'] / data['price_usd'].replace(0, np.nan)
                data['exchange_rate_used'] = data['exchange_rate_used'].fillna(data['exchange_rate_used'].median())
                
            # Ensure date column is datetime
            if 'date' in data.columns:
                data['date'] = pd.to_datetime(data['date'])
        else:
            data = load_panel_data()
        
        # Clean data - drop rows with missing critical values
        logger.info(f"Data shape before cleaning: {data.shape}")
        data = data.dropna(subset=['price_usd', 'price_yer', 'currency_zone'])
        logger.info(f"Data shape after dropping missing values: {data.shape}")
        
        # Map currency zones to expected values
        zone_mapping = {
            'IRG': 'GOVERNMENT',
            'DFA': 'HOUTHI',
            None: 'CONTESTED'
        }
        if 'currency_zone' in data.columns:
            data['currency_zone'] = data['currency_zone'].map(lambda x: zone_mapping.get(x, x))
        
        # Add derived columns if missing
        if 'log_price_usd' not in data.columns and 'price_usd' in data.columns:
            data['log_price_usd'] = np.log(data['price_usd'].clip(lower=1e-6))
            
        # Add mock control variables if missing (for demonstration)
        if 'conflict_intensity' not in data.columns:
            logger.warning("Adding mock control variables for demonstration")
            np.random.seed(42)
            data['conflict_intensity'] = np.random.uniform(0, 10, len(data))
            data['aid_distribution'] = np.random.uniform(0, 5, len(data))
            data['population_density'] = np.random.uniform(10, 100, len(data))
            data['distance_to_border'] = np.random.uniform(0, 200, len(data))
        
        # Run analysis
        results = run_robustness_analysis(
            data=data,
            n_specs=args.n_specs,
            output_dir=args.output_dir
        )
        
        # Print summary
        print("\n" + "="*60)
        print("SPECIFICATION CURVE ANALYSIS COMPLETE")
        print("="*60)
        print(f"Total specifications run: {results['n_specifications']}")
        print(f"Valid specifications: {results['n_valid']}")
        if 'coefficient_cv' in results['stability_metrics']:
            print(f"Coefficient CV: {results['stability_metrics']['coefficient_cv']:.3f}")
        print(f"Results saved to: {results['output_dir']}")
        print("="*60)
        
    except Exception as e:
        logger.error(f"Analysis failed: {str(e)}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()