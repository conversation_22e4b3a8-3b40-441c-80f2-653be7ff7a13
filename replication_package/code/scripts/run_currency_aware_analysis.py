#!/usr/bin/env python3
"""Run three-tier analysis with currency zone awareness."""

import argparse
import asyncio
from datetime import datetime
from pathlib import Path
import sys

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.infrastructure.processors.currency_aware_wfp_processor import CurrencyAwareWFPProcessor
from src.infrastructure.processors.currency_zone_classifier import CurrencyZoneClassifier
from src.infrastructure.processors.acled_processor import ACLEDProcessor
from src.infrastructure.processors.acaps_processor import ACAPSProcessor
from src.application.services.three_tier_analysis_service import ThreeTierAnalysisService
from src.shared.container import Container


async def run_currency_aware_analysis(
    wfp_path: Path,
    acled_path: Path,
    acaps_path: Path,
    output_dir: Path,
    currency_aware: bool = True
):
    """Run three-tier analysis with optional currency zone awareness."""
    
    # Initialize container
    container = Container()
    
    # Initialize processors
    zone_classifier = CurrencyZoneClassifier(acaps_path)
    
    if currency_aware:
        print("=" * 80)
        print("RUNNING CURRENCY ZONE-AWARE ANALYSIS")
        print("This implements the revolutionary Yemen Paradox solution")
        print("=" * 80)
        wfp_processor = CurrencyAwareWFPProcessor(
            zone_classifier=zone_classifier,
            enable_zone_conversion=True
        )
    else:
        print("Running traditional analysis (no currency zone awareness)")
        from src.infrastructure.processors.wfp_processor import WFPProcessor
        wfp_processor = WFPProcessor()
    
    acled_processor = ACLEDProcessor()
    acaps_processor = ACAPSProcessor()
    
    # Process data
    print("\n1. Processing WFP price data...")
    if currency_aware:
        markets, prices, exchange_rates, zone_metrics = await wfp_processor.process(
            str(wfp_path),
            zone_aware=True
        )
        
        # Display zone metrics
        if zone_metrics:
            print(f"\nCurrency Zone Metrics:")
            print(f"- Markets analyzed: {zone_metrics.get('total_markets', 0)}")
            print(f"- Zone distribution: {zone_metrics.get('zone_distribution', {})}")
            print(f"- Average fragmentation ratio: {zone_metrics.get('avg_fragmentation_ratio', 0):.2f}x")
            print(f"- Estimated aid effectiveness gain: {zone_metrics.get('estimated_aid_effectiveness_gain_pct', 0):.1f}%")
            
            if zone_metrics.get('price_paradox_examples'):
                print(f"\nYemen Paradox Examples (conflict zones MORE expensive in USD):")
                for ex in zone_metrics['price_paradox_examples'][:3]:
                    print(f"  - {ex['commodity']} on {ex['date']}: "
                          f"Houthi ${ex['houthi_usd_price']:.2f} vs "
                          f"Government ${ex['government_usd_price']:.2f} "
                          f"({ex['premium_pct']:.1f}% premium)")
    else:
        markets, prices, exchange_rates = await wfp_processor.process(str(wfp_path))
        zone_metrics = {}
    
    print(f"Processed {len(markets)} markets with {len(prices)} price observations")
    
    print("\n2. Processing ACLED conflict data...")
    conflicts = await acled_processor.process(str(acled_path))
    print(f"Processed {len(conflicts)} conflict events")
    
    print("\n3. Processing ACAPS control area data...")
    control_changes = await acaps_processor.process(str(acaps_path))
    print(f"Processed {len(control_changes)} control status changes")
    
    # Run three-tier analysis
    print("\n4. Running three-tier econometric analysis...")
    analysis_service = ThreeTierAnalysisService(container)
    
    # Create analysis request
    request = {
        'markets': markets,
        'prices': prices,
        'conflicts': conflicts,
        'control_changes': control_changes,
        'exchange_rates': exchange_rates,
        'currency_aware': currency_aware,
        'zone_metrics': zone_metrics
    }
    
    # Run analysis
    results = await analysis_service.run_analysis(request)
    
    # Save results
    output_dir.mkdir(exist_ok=True, parents=True)
    
    # Save main results
    results_file = output_dir / f"analysis_results_{'currency_aware' if currency_aware else 'traditional'}.json"
    with open(results_file, 'w') as f:
        import json
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n5. Results saved to {results_file}")
    
    # Create comparison if currency aware
    if currency_aware and zone_metrics:
        comparison_file = output_dir / "yemen_paradox_validation.txt"
        with open(comparison_file, 'w') as f:
            f.write("YEMEN PARADOX VALIDATION RESULTS\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"Currency Fragmentation Ratio: {zone_metrics.get('avg_fragmentation_ratio', 0):.2f}x\n")
            f.write(f"Potential Aid Effectiveness Gain: {zone_metrics.get('estimated_aid_effectiveness_gain_pct', 0):.1f}%\n\n")
            
            f.write("Price Paradox Examples (Houthi areas MORE expensive in USD):\n")
            for ex in zone_metrics.get('price_paradox_examples', []):
                f.write(f"- {ex['commodity']}: {ex['premium_pct']:.1f}% premium in conflict zones\n")
        
        print(f"Yemen Paradox validation saved to {comparison_file}")
    
    return results, zone_metrics


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Run Yemen market integration analysis with currency zone awareness"
    )
    
    parser.add_argument(
        "--wfp-data",
        type=Path,
        required=True,
        help="Path to WFP price data CSV"
    )
    
    parser.add_argument(
        "--acled-data",
        type=Path,
        required=True,
        help="Path to ACLED conflict data CSV"
    )
    
    parser.add_argument(
        "--acaps-data",
        type=Path,
        required=True,
        help="Path to ACAPS control area data"
    )
    
    parser.add_argument(
        "--output-dir",
        type=Path,
        default=Path("results/currency_aware_analysis"),
        help="Output directory for results"
    )
    
    parser.add_argument(
        "--currency-aware",
        action="store_true",
        help="Enable currency zone-aware analysis (Yemen Paradox solution)"
    )
    
    parser.add_argument(
        "--compare",
        action="store_true",
        help="Run both traditional and currency-aware analysis for comparison"
    )
    
    args = parser.parse_args()
    
    if args.compare:
        print("Running comparative analysis...")
        
        # Run traditional
        print("\nTRADITIONAL ANALYSIS:")
        results1, _ = asyncio.run(run_currency_aware_analysis(
            args.wfp_data,
            args.acled_data,
            args.acaps_data,
            args.output_dir / "traditional",
            currency_aware=False
        ))
        
        # Run currency-aware
        print("\n\nCURRENCY-AWARE ANALYSIS:")
        results2, zone_metrics = asyncio.run(run_currency_aware_analysis(
            args.wfp_data,
            args.acled_data,
            args.acaps_data,
            args.output_dir / "currency_aware",
            currency_aware=True
        ))
        
        print("\n" + "=" * 80)
        print("COMPARISON COMPLETE")
        print("Traditional analysis shows the paradox: conflict zones appear cheaper")
        print("Currency-aware analysis reveals the truth: conflict zones are more expensive")
        print("=" * 80)
    else:
        # Run single analysis
        asyncio.run(run_currency_aware_analysis(
            args.wfp_data,
            args.acled_data,
            args.acaps_data,
            args.output_dir,
            currency_aware=args.currency_aware
        ))


if __name__ == "__main__":
    main()