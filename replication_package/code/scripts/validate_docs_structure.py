#!/usr/bin/env python3
"""Validate documentation structure and SRC alignment."""

import os
import sys
from pathlib import Path
from typing import Dict, List, Set


def check_src_alignment(root_dir: Path) -> List[str]:
    """Check that documentation structure aligns with SRC architecture."""
    errors = []
    
    # Expected SRC-aligned documentation sections
    expected_sections = {
        "11-application-guides": "Application layer documentation",
        "12-core-reference": "Core domain documentation", 
        "13-infrastructure-ops": "Infrastructure operations",
        "14-interfaces-integration": "Interface layer integration",
        "15-shared-utilities": "Shared components"
    }
    
    docs_dir = root_dir / "docs"
    
    print("🏗️ Checking SRC architecture alignment...")
    
    for section, description in expected_sections.items():
        section_path = docs_dir / section
        
        if not section_path.exists():
            errors.append(f"❌ Missing SRC section: {section} ({description})")
            continue
        
        # Check for README.md
        readme_path = section_path / "README.md"
        if not readme_path.exists():
            errors.append(f"❌ Missing README.md in {section}")
        
        # Check that section has content beyond just README
        md_files = list(section_path.glob("*.md"))
        if len(md_files) <= 1:  # Only README or no files
            errors.append(f"⚠️ Section {section} appears empty (only README found)")
    
    return errors


def check_readme_coverage(root_dir: Path) -> List[str]:
    """Check that all major directories have README.md files."""
    errors = []
    
    docs_dir = root_dir / "docs"
    
    print("📚 Checking README coverage...")
    
    # Find all directories that should have READMEs
    for item in docs_dir.iterdir():
        if item.is_dir() and not item.name.startswith('.'):
            readme_path = item / "README.md"
            
            if not readme_path.exists():
                errors.append(f"❌ Missing README.md in {item.relative_to(root_dir)}")
            else:
                # Check that README has content
                try:
                    with open(readme_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if len(content) < 100:  # Very minimal README
                            errors.append(f"⚠️ README appears minimal in {item.relative_to(root_dir)}")
                except Exception as e:
                    errors.append(f"❌ Cannot read README in {item.relative_to(root_dir)}: {e}")
    
    return errors


def validate_research_methodology_integrity(root_dir: Path) -> List[str]:
    """Validate that research methodology package is preserved."""
    errors = []
    
    methodology_dir = root_dir / "docs" / "research-methodology-package"
    
    print("🔬 Checking research methodology package integrity...")
    
    if not methodology_dir.exists():
        errors.append("❌ CRITICAL: Research methodology package directory missing!")
        return errors
    
    # Count markdown files
    md_files = list(methodology_dir.rglob("*.md"))
    file_count = len(md_files)
    
    print(f"   Found {file_count} markdown files in research methodology package")
    
    # Should have around 264 files (as mentioned in the reorganization)
    if file_count < 250:
        errors.append(f"❌ Research methodology package appears incomplete: {file_count} files (expected ~264)")
    elif file_count < 260:
        errors.append(f"⚠️ Research methodology package may be incomplete: {file_count} files (expected ~264)")
    
    # Check for key directories
    expected_dirs = [
        "00-overview",
        "01-theoretical-foundation", 
        "02-data-infrastructure",
        "03-econometric-methodology",
        "04-external-validation",
        "05-welfare-analysis",
        "06-implementation-guides",
        "07-results-templates",
        "08-publication-materials",
        "09-policy-applications",
        "10-context-for-implementation"
    ]
    
    for expected_dir in expected_dirs:
        dir_path = methodology_dir / expected_dir
        if not dir_path.exists():
            errors.append(f"❌ Missing methodology directory: {expected_dir}")
    
    return errors


def check_file_organization(root_dir: Path) -> List[str]:
    """Check that files are properly organized after reorganization."""
    errors = []
    
    docs_dir = root_dir / "docs"
    
    print("📁 Checking file organization...")
    
    # Check that moved files are in correct locations
    expected_locations = {
        "04-development/implementation-reports": [
            "ALTERNATIVE_EXPLANATIONS_FIX_REPORT.md",
            "DATACLASS_FIX_SUMMARY.md", 
            "HYPOTHESIS_REFACTORING_SUMMARY.md",
            "ROBUSTNESS_IMPLEMENTATION_SUMMARY.md"
        ],
        "09-troubleshooting/technical-issues": [
            "ALTERNATIVE_EXPLANATIONS_TECHNICAL_ISSUES.md",
            "ECONOMETRIC_TECHNICAL_ISSUES_REPORT.md"
        ],
        "08-results/validation-reports": [
            "VERIFICATION_REPORT.md"
        ],
        "05-methodology/strategic-documents": [
            "COMPREHENSIVE_IMPROVEMENT_PLAN.md",
            "yemen-methodological-transformation-strategy.md"
        ]
    }
    
    for location, files in expected_locations.items():
        location_path = docs_dir / location
        
        if not location_path.exists():
            errors.append(f"❌ Missing directory: docs/{location}")
            continue
        
        for file_name in files:
            file_path = location_path / file_name
            if not file_path.exists():
                errors.append(f"❌ Missing file: docs/{location}/{file_name}")
    
    # Check that these files are NOT in root directory anymore
    for location, files in expected_locations.items():
        for file_name in files:
            root_file_path = root_dir / file_name
            if root_file_path.exists():
                errors.append(f"❌ File still in root directory: {file_name} (should be in docs/{location})")
    
    return errors


def check_documentation_navigation(root_dir: Path) -> List[str]:
    """Check that documentation navigation is properly structured."""
    errors = []
    
    docs_dir = root_dir / "docs"
    main_readme = docs_dir / "README.md"
    
    print("🧭 Checking documentation navigation...")
    
    if not main_readme.exists():
        errors.append("❌ Missing main documentation README.md")
        return errors
    
    try:
        with open(main_readme, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        errors.append(f"❌ Cannot read main documentation README: {e}")
        return errors
    
    # Check for references to new SRC sections
    src_sections = [
        "11-application-guides",
        "12-core-reference", 
        "13-infrastructure-ops",
        "14-interfaces-integration",
        "15-shared-utilities"
    ]
    
    for section in src_sections:
        if section not in content:
            errors.append(f"⚠️ Main README doesn't reference SRC section: {section}")
    
    # Check for research methodology package reference
    if "research-methodology-package" not in content:
        errors.append("⚠️ Main README doesn't reference research methodology package")
    
    return errors


def main():
    """Main validation function."""
    root_dir = Path(__file__).parent.parent
    
    print("🚀 Starting documentation structure validation...")
    print(f"Root directory: {root_dir}")
    
    all_errors = []
    
    # Run all validation checks
    all_errors.extend(check_src_alignment(root_dir))
    all_errors.extend(check_readme_coverage(root_dir))
    all_errors.extend(validate_research_methodology_integrity(root_dir))
    all_errors.extend(check_file_organization(root_dir))
    all_errors.extend(check_documentation_navigation(root_dir))
    
    # Report results
    print(f"\n📊 Structure Validation Results:")
    print(f"   Total issues found: {len(all_errors)}")
    
    if all_errors:
        print(f"\n❌ Found {len(all_errors)} documentation structure issues:")
        for error in all_errors:
            print(f"\n{error}")
        sys.exit(1)
    else:
        print(f"\n✅ Documentation structure is valid!")
        sys.exit(0)


if __name__ == "__main__":
    main()