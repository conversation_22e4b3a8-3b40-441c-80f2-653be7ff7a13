#!/usr/bin/env python3
"""
Master Integration Verification Script

This script runs all integration tools in sequence to verify the complete
integration of the research methodology documentation.

Usage:
    python scripts/verify_documentation_integration.py [--verbose] [--fix] [--currency-check]

Currency Check Mode:
    --currency-check mode specifically validates all price comparisons in the documentation
    to ensure proper currency specification and conversion statements. This addresses the
    root cause of the original methodological error where YER and USD prices were mixed
    without proper conversion.

    The currency checker validates:
    - All price mentions include clear currency specification (YER vs USD)
    - Price comparisons include conversion statements
    - Exchange rates are specified when relevant
    - No ambiguous currency mixing without conversion context
"""

import argparse
import json
import re
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Any, Set
import os

# Add project root to path
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))


class IntegrationVerifier:
    """Master integration verification coordinator"""
    
    def __init__(self, verbose: bool = False, fix: bool = False, currency_check: bool = False):
        self.verbose = verbose
        self.fix = fix
        self.currency_check = currency_check
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'PENDING',
            'checks': {},
            'summary': {}
        }
        self.scripts_dir = PROJECT_ROOT / 'scripts'
        self.docs_dir = PROJECT_ROOT / 'docs' / 'research-methodology-package'
        
    def run_all_checks(self) -> bool:
        """Run all integration checks in sequence"""
        print("=" * 80)
        if self.currency_check:
            print("CURRENCY VALIDATION MODE")
        else:
            print("MASTER INTEGRATION VERIFICATION")
        print("=" * 80)
        print(f"Started at: {self.results['timestamp']}")
        print(f"Documentation directory: {self.docs_dir}")
        print(f"Verbose mode: {self.verbose}")
        print(f"Fix mode: {self.fix}")
        print(f"Currency check mode: {self.currency_check}")
        print("=" * 80)
        
        # Define checks to run in order
        if self.currency_check:
            checks = [
                ('currency_validation', self.run_currency_validation)
            ]
        else:
            checks = [
                ('consistency', self.run_consistency_check),
                ('links', self.run_link_verification),
                ('tests', self.run_integration_tests),
                ('report', self.generate_integration_report)
            ]
        
        all_passed = True
        
        for check_name, check_func in checks:
            print(f"\n{'=' * 40}")
            print(f"Running: {check_name.upper()}")
            print('=' * 40)
            
            try:
                success, details = check_func()
                self.results['checks'][check_name] = {
                    'status': 'PASSED' if success else 'FAILED',
                    'details': details
                }
                
                if not success:
                    all_passed = False
                    
                print(f"\n✓ {check_name}: {'PASSED' if success else 'FAILED'}")
                
            except Exception as e:
                self.results['checks'][check_name] = {
                    'status': 'ERROR',
                    'error': str(e)
                }
                all_passed = False
                print(f"\n✗ {check_name}: ERROR - {e}")
        
        # Set overall status
        self.results['overall_status'] = 'PASSED' if all_passed else 'FAILED'
        
        # Generate summary
        self._generate_summary()
        
        # Save results
        self._save_results()
        
        # Print final summary
        self._print_final_summary()
        
        return all_passed
    
    def run_consistency_check(self) -> Tuple[bool, Dict]:
        """Run documentation consistency checker"""
        script_path = self.scripts_dir / 'check_documentation_consistency.py'
        
        if not script_path.exists():
            return False, {'error': f'Script not found: {script_path}'}
        
        cmd = ['python', str(script_path)]
        if self.verbose:
            cmd.append('--verbose')
        if self.fix:
            cmd.append('--fix')
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=str(PROJECT_ROOT)
            )
            
            # Parse output for issues
            output_lines = result.stdout.split('\n')
            issues = []
            for line in output_lines:
                if 'ERROR:' in line or 'WARNING:' in line:
                    issues.append(line.strip())
            
            success = result.returncode == 0
            details = {
                'return_code': result.returncode,
                'issues_found': len(issues),
                'issues': issues[:10] if self.verbose else []  # Limit issues shown
            }
            
            if self.verbose:
                print(result.stdout)
                if result.stderr:
                    print(f"STDERR: {result.stderr}")
            
            return success, details
            
        except Exception as e:
            return False, {'error': str(e)}
    
    def run_link_verification(self) -> Tuple[bool, Dict]:
        """Run link verification script"""
        script_path = self.scripts_dir / 'verify_documentation_links.py'
        
        if not script_path.exists():
            return False, {'error': f'Script not found: {script_path}'}
        
        cmd = ['python', str(script_path)]
        if self.verbose:
            cmd.append('--verbose')
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=str(PROJECT_ROOT)
            )
            
            # Look for link statistics in output
            output_lines = result.stdout.split('\n')
            total_links = 0
            broken_links = 0
            
            for line in output_lines:
                if 'Total links checked:' in line:
                    try:
                        total_links = int(line.split(':')[1].strip())
                    except:
                        pass
                elif 'Broken links found:' in line:
                    try:
                        broken_links = int(line.split(':')[1].strip())
                    except:
                        pass
            
            success = result.returncode == 0
            details = {
                'return_code': result.returncode,
                'total_links': total_links,
                'broken_links': broken_links,
                'success_rate': f"{((total_links - broken_links) / total_links * 100):.1f}%" if total_links > 0 else "N/A"
            }
            
            if self.verbose:
                print(result.stdout)
                if result.stderr:
                    print(f"STDERR: {result.stderr}")
            
            return success, details
            
        except Exception as e:
            return False, {'error': str(e)}
    
    def run_integration_tests(self) -> Tuple[bool, Dict]:
        """Run documentation integration tests"""
        script_path = self.scripts_dir / 'test_documentation_integration.py'
        
        if not script_path.exists():
            return False, {'error': f'Script not found: {script_path}'}
        
        cmd = ['python', str(script_path)]
        if self.verbose:
            cmd.append('--verbose')
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=str(PROJECT_ROOT)
            )
            
            # Parse test results
            output_lines = result.stdout.split('\n')
            tests_run = 0
            tests_passed = 0
            tests_failed = 0
            
            for line in output_lines:
                if 'tests run' in line.lower():
                    try:
                        tests_run = int(line.split()[0])
                    except:
                        pass
                elif 'passed' in line.lower() and 'test' in line.lower():
                    try:
                        tests_passed = int(line.split()[0])
                    except:
                        pass
                elif 'failed' in line.lower() and 'test' in line.lower():
                    try:
                        tests_failed = int(line.split()[0])
                    except:
                        pass
            
            success = result.returncode == 0
            details = {
                'return_code': result.returncode,
                'tests_run': tests_run,
                'tests_passed': tests_passed,
                'tests_failed': tests_failed,
                'success_rate': f"{(tests_passed / tests_run * 100):.1f}%" if tests_run > 0 else "N/A"
            }
            
            if self.verbose:
                print(result.stdout)
                if result.stderr:
                    print(f"STDERR: {result.stderr}")
            
            return success, details
            
        except Exception as e:
            return False, {'error': str(e)}
    
    def generate_integration_report(self) -> Tuple[bool, Dict]:
        """Generate comprehensive integration report"""
        script_path = self.scripts_dir / 'generate_integration_report.py'
        
        if not script_path.exists():
            return False, {'error': f'Script not found: {script_path}'}
        
        cmd = ['python', str(script_path)]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=str(PROJECT_ROOT)
            )
            
            # Check if report was generated
            report_path = PROJECT_ROOT / 'reports' / 'documentation_integration_report.md'
            report_exists = report_path.exists()
            
            success = result.returncode == 0 and report_exists
            details = {
                'return_code': result.returncode,
                'report_generated': report_exists,
                'report_path': str(report_path) if report_exists else None
            }
            
            if self.verbose:
                print(result.stdout)
                if result.stderr:
                    print(f"STDERR: {result.stderr}")
            
            return success, details
            
        except Exception as e:
            return False, {'error': str(e)}
    
    def run_currency_validation(self) -> Tuple[bool, Dict]:
        """Run comprehensive currency validation check on all documentation"""
        print("Running currency validation check...")
        
        # Currency validation patterns - more specific to avoid false positives
        price_patterns = [
            # Direct price mentions with numbers
            r'\b\d+(?:,\d{3})*(?:\.\d+)?\s*(?:YER|USD|yer|usd|riyals?|dollars?)\b',
            r'\b(?:YER|USD|yer|usd)\s*\d+(?:,\d{3})*(?:\.\d+)?\b',
            r'(?:price|cost|value)\s+(?:of\s+)?\d+',
            r'\$\d+(?:,\d{3})*(?:\.\d+)?',
            r'\d+(?:,\d{3})*(?:\.\d+)?\s*(?:riyals?|dollars?)',
            
            # Exchange rate mentions with numbers
            r'\d+(?:,\d{3})*(?:\.\d+)?\s*YER/USD',
            r'~\d+(?:,\d{3})*(?:\.\d+)?\s*(?:YER|USD)',
            r'exchange\s+rate\s+(?:of\s+)?\d+',
            
            # Price comparison terms with numbers or specific contexts
            r'price\s+(?:comparison|analysis|transmission)\s+(?:of\s+)?\d+',
            r'wheat\s+price\s+(?:of\s+)?\d+',
            r'commodity\s+price\s+(?:of\s+)?\d+',
            r'(?:cheaper|expensive|higher|lower)\s+(?:by\s+)?\d+',
            
            # Cost/value analysis with numbers
            r'cost\s+(?:of\s+)?\d+',
            r'value\s+(?:of\s+)?\d+',
        ]
        
        currency_keywords = {
            'currency_declaration': [
                'YER', 'USD', 'yer', 'usd', 'riyal', 'dollar', 'currency',
                'exchange rate', 'conversion', 'converted'
            ],
            'currency_conversion': [
                'converted to', 'conversion', 'exchange rate applied',
                'USD equivalent', 'common currency', 'normalized'
            ],
            'price_analysis': [
                'price comparison', 'price analysis', 'price transmission',
                'econometric analysis', 'panel data analysis', 'commodity analysis',
                'wheat price', 'exchange rate analysis', 'currency analysis'
            ]
        }
        
        validation_results = {
            'files_scanned': 0,
            'files_with_price_analysis': 0,
            'files_with_currency_issues': 0,
            'currency_issues': [],
            'good_examples': [],
            'statistics': {
                'total_price_mentions': 0,
                'price_mentions_with_currency': 0,
                'conversion_statements': 0,
                'analysis_sections_checked': 0
            }
        }
        
        # Scan all markdown files in documentation
        for md_file in self.docs_dir.rglob('*.md'):
            validation_results['files_scanned'] += 1
            
            try:
                with open(md_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                file_issues = self._validate_currency_in_file(
                    md_file, content, price_patterns, currency_keywords
                )
                
                if file_issues['has_price_analysis']:
                    validation_results['files_with_price_analysis'] += 1
                
                if file_issues['currency_issues']:
                    validation_results['files_with_currency_issues'] += 1
                    validation_results['currency_issues'].extend(file_issues['currency_issues'])
                
                if file_issues['good_examples']:
                    validation_results['good_examples'].extend(file_issues['good_examples'])
                
                # Update statistics
                for key in validation_results['statistics']:
                    if key in file_issues['statistics']:
                        validation_results['statistics'][key] += file_issues['statistics'][key]
                        
            except Exception as e:
                validation_results['currency_issues'].append({
                    'file': str(md_file),
                    'type': 'FILE_ERROR',
                    'message': f"Error reading file: {str(e)}",
                    'line': 0
                })
        
        # Determine success criteria
        # Critical: No high-priority currency issues
        # Warning: Low ratio of currency declarations to price analysis
        critical_issues = [
            issue for issue in validation_results['currency_issues']
            if issue['type'] in ['PRICE_WITHOUT_CURRENCY', 'AMBIGUOUS_CURRENCY_MIX', 'MISSING_CONVERSION_STATEMENT']
        ]
        
        success = len(critical_issues) == 0
        
        # Calculate coverage statistics
        price_mentions = validation_results['statistics']['total_price_mentions']
        currency_mentions = validation_results['statistics']['price_mentions_with_currency']
        coverage_rate = (currency_mentions / price_mentions * 100) if price_mentions > 0 else 100
        
        details = {
            'files_scanned': validation_results['files_scanned'],
            'files_with_price_analysis': validation_results['files_with_price_analysis'],
            'files_with_issues': validation_results['files_with_currency_issues'],
            'critical_issues': len(critical_issues),
            'total_issues': len(validation_results['currency_issues']),
            'currency_coverage_rate': f"{coverage_rate:.1f}%",
            'statistics': validation_results['statistics'],
            'issues': validation_results['currency_issues'][:20] if self.verbose else critical_issues[:10],
            'good_examples': validation_results['good_examples'][:5] if self.verbose else []
        }
        
        # Print summary
        if self.verbose:
            self._print_currency_validation_summary(validation_results, critical_issues)
        
        return success, details
    
    def _validate_currency_in_file(self, file_path: Path, content: str, 
                                 price_patterns: List[str], currency_keywords: Dict) -> Dict:
        """Validate currency usage in a single file"""
        
        issues = []
        good_examples = []
        statistics = {
            'total_price_mentions': 0,
            'price_mentions_with_currency': 0,
            'conversion_statements': 0,
            'analysis_sections_checked': 0
        }
        
        lines = content.split('\n')
        has_price_analysis = False
        
        # Check each line for currency issues
        for line_num, line in enumerate(lines, 1):
            line_lower = line.lower()
            
            # Skip code blocks and comments
            if line.strip().startswith('```') or line.strip().startswith('#'):
                continue
            
            # Check for price analysis indicators
            for keyword in currency_keywords['price_analysis']:
                if keyword.lower() in line_lower:
                    has_price_analysis = True
                    statistics['analysis_sections_checked'] += 1
                    break
            
            # Check for price mentions
            for pattern in price_patterns:
                matches = re.finditer(pattern, line, re.IGNORECASE)
                for match in matches:
                    statistics['total_price_mentions'] += 1
                    
                    # Check if this price mention has currency context
                    context_start = max(0, match.start() - 50)
                    context_end = min(len(line), match.end() + 50)
                    context = line[context_start:context_end].lower()
                    
                    has_currency = False
                    for currency_word in currency_keywords['currency_declaration']:
                        if currency_word.lower() in context:
                            has_currency = True
                            statistics['price_mentions_with_currency'] += 1
                            break
                    
                    if not has_currency:
                        # Check surrounding lines for currency context
                        surrounding_lines = []
                        for i in range(max(0, line_num-3), min(len(lines), line_num+3)):
                            surrounding_lines.append(lines[i].lower())
                        
                        surrounding_text = ' '.join(surrounding_lines)
                        
                        for currency_word in currency_keywords['currency_declaration']:
                            if currency_word.lower() in surrounding_text:
                                has_currency = True
                                statistics['price_mentions_with_currency'] += 1
                                break
                    
                    if not has_currency:
                        issues.append({
                            'file': str(file_path.relative_to(self.docs_dir)),
                            'line': line_num,
                            'type': 'PRICE_WITHOUT_CURRENCY',
                            'message': f"Price mention without clear currency specification: '{match.group()}'",
                            'context': line.strip()
                        })
            
            # Check for conversion statements
            for keyword in currency_keywords['currency_conversion']:
                if keyword.lower() in line_lower:
                    statistics['conversion_statements'] += 1
                    good_examples.append({
                        'file': str(file_path.relative_to(self.docs_dir)),
                        'line': line_num,
                        'type': 'GOOD_CONVERSION_PRACTICE',
                        'message': f"Proper conversion statement: '{keyword}'",
                        'context': line.strip()
                    })
            
            # Check for potential currency mixing issues
            if 'yer' in line_lower and 'usd' in line_lower and 'comparison' in line_lower:
                if 'convert' not in line_lower and 'exchange' not in line_lower:
                    issues.append({
                        'file': str(file_path.relative_to(self.docs_dir)),
                        'line': line_num,
                        'type': 'AMBIGUOUS_CURRENCY_MIX',
                        'message': "YER and USD mentioned in comparison without explicit conversion reference",
                        'context': line.strip()
                    })
        
        # Check for missing conversion statements in files with price analysis
        if has_price_analysis and statistics['conversion_statements'] == 0:
            if statistics['total_price_mentions'] > 2:  # Only flag if significant price discussion
                issues.append({
                    'file': str(file_path.relative_to(self.docs_dir)),
                    'line': 0,
                    'type': 'MISSING_CONVERSION_STATEMENT',
                    'message': "File contains price analysis but lacks currency conversion statements",
                    'context': f"Found {statistics['total_price_mentions']} price mentions without conversion context"
                })
        
        return {
            'has_price_analysis': has_price_analysis,
            'currency_issues': issues,
            'good_examples': good_examples,
            'statistics': statistics
        }
    
    def _print_currency_validation_summary(self, results: Dict, critical_issues: List):
        """Print detailed currency validation summary"""
        print("\n" + "=" * 60)
        print("CURRENCY VALIDATION DETAILED RESULTS")
        print("=" * 60)
        
        print(f"Files scanned: {results['files_scanned']}")
        print(f"Files with price analysis: {results['files_with_price_analysis']}")
        print(f"Files with currency issues: {results['files_with_currency_issues']}")
        
        print(f"\nPrice Analysis Statistics:")
        stats = results['statistics']
        print(f"  Total price mentions: {stats['total_price_mentions']}")
        print(f"  Price mentions with currency: {stats['price_mentions_with_currency']}")
        print(f"  Conversion statements found: {stats['conversion_statements']}")
        print(f"  Analysis sections checked: {stats['analysis_sections_checked']}")
        
        if stats['total_price_mentions'] > 0:
            coverage = stats['price_mentions_with_currency'] / stats['total_price_mentions'] * 100
            print(f"  Currency coverage rate: {coverage:.1f}%")
        
        print(f"\nCritical Issues Found: {len(critical_issues)}")
        if critical_issues:
            print("\nCRITICAL CURRENCY ISSUES:")
            for issue in critical_issues[:10]:
                print(f"  ❌ {issue['file']}:{issue['line']} - {issue['message']}")
        
        if results['good_examples']:
            print(f"\nGood Currency Practices Found: {len(results['good_examples'])}")
            print("\nEXAMPLES OF GOOD PRACTICE:")
            for example in results['good_examples'][:5]:
                print(f"  ✅ {example['file']}:{example['line']} - {example['message']}")
        
        print("\n" + "=" * 60)
    
    def _generate_summary(self):
        """Generate summary statistics"""
        total_checks = len(self.results['checks'])
        passed_checks = sum(1 for check in self.results['checks'].values() 
                          if check['status'] == 'PASSED')
        failed_checks = sum(1 for check in self.results['checks'].values() 
                          if check['status'] == 'FAILED')
        error_checks = sum(1 for check in self.results['checks'].values() 
                          if check['status'] == 'ERROR')
        
        self.results['summary'] = {
            'total_checks': total_checks,
            'passed': passed_checks,
            'failed': failed_checks,
            'errors': error_checks,
            'success_rate': f"{(passed_checks / total_checks * 100):.1f}%" if total_checks > 0 else "0%"
        }
    
    def _save_results(self):
        """Save verification results to JSON file"""
        results_path = PROJECT_ROOT / 'reports' / 'master_integration_verification.json'
        results_path.parent.mkdir(exist_ok=True)
        
        with open(results_path, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\nResults saved to: {results_path}")
    
    def _print_final_summary(self):
        """Print final summary"""
        print("\n" + "=" * 80)
        print("FINAL INTEGRATION STATUS SUMMARY")
        print("=" * 80)
        print(f"Overall Status: {self.results['overall_status']}")
        print(f"Timestamp: {self.results['timestamp']}")
        print("\nCheck Results:")
        print("-" * 40)
        
        for check_name, check_result in self.results['checks'].items():
            status = check_result['status']
            symbol = '✓' if status == 'PASSED' else '✗'
            print(f"{symbol} {check_name.upper()}: {status}")
            
            if 'details' in check_result and self.verbose:
                details = check_result['details']
                if 'issues_found' in details:
                    print(f"  - Issues found: {details['issues_found']}")
                if 'broken_links' in details:
                    print(f"  - Broken links: {details['broken_links']}/{details.get('total_links', 'N/A')}")
                if 'tests_failed' in details:
                    print(f"  - Tests failed: {details['tests_failed']}/{details.get('tests_run', 'N/A')}")
                if 'critical_issues' in details:
                    print(f"  - Critical currency issues: {details['critical_issues']}")
                    print(f"  - Currency coverage rate: {details.get('currency_coverage_rate', 'N/A')}")
        
        print("\nSummary Statistics:")
        print("-" * 40)
        summary = self.results['summary']
        print(f"Total Checks: {summary['total_checks']}")
        print(f"Passed: {summary['passed']}")
        print(f"Failed: {summary['failed']}")
        print(f"Errors: {summary['errors']}")
        print(f"Success Rate: {summary['success_rate']}")
        
        print("\nRecommendations:")
        print("-" * 40)
        
        if self.results['overall_status'] == 'PASSED':
            if self.currency_check:
                print("✓ Currency validation passed successfully!")
                print("✓ All price comparisons properly specify currency and conversion.")
            else:
                print("✓ All integration checks passed successfully!")
                print("✓ Documentation is ready for deployment.")
        else:
            if self.currency_check:
                print("✗ Currency validation failed.")
                print("\nCRITICAL: Price analysis found without proper currency specification.")
                print("This addresses the root cause of the original methodological error.")
            else:
                print("✗ Integration verification failed.")
            
            print("\nRequired Actions:")
            
            for check_name, check_result in self.results['checks'].items():
                if check_result['status'] != 'PASSED':
                    print(f"\n- Fix {check_name} issues:")
                    
                    if check_name == 'currency_validation':
                        print("  PRIORITY: Review price analysis sections that lack currency specification")
                        print("  Add explicit currency declarations (YER vs USD)")
                        print("  Include conversion statements where prices are compared")
                        print("  Specify exchange rates used in analysis")
                        details = check_result.get('details', {})
                        if 'critical_issues' in details and details['critical_issues'] > 0:
                            print(f"  Address {details['critical_issues']} critical currency issues")
                    elif check_name == 'consistency' and not self.fix:
                        print("  Run with --fix flag to auto-fix consistency issues")
                    elif check_name == 'links':
                        print("  Review and update broken links")
                    elif check_name == 'tests':
                        print("  Fix failing integration tests")
                    elif check_name == 'report':
                        print("  Ensure report generation completes successfully")
        
        print("\n" + "=" * 80)


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description='Master integration verification for research methodology documentation'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Show detailed output from each check'
    )
    parser.add_argument(
        '--fix',
        action='store_true',
        help='Attempt to fix issues automatically where possible'
    )
    parser.add_argument(
        '--currency-check',
        action='store_true',
        help='Run currency validation mode: check all price comparisons for proper currency specification'
    )
    
    args = parser.parse_args()
    
    verifier = IntegrationVerifier(
        verbose=args.verbose, 
        fix=args.fix,
        currency_check=args.currency_check
    )
    success = verifier.run_all_checks()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()