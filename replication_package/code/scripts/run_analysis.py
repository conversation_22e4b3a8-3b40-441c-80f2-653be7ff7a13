#!/usr/bin/env python3
"""
Unified entry point for Yemen Market Integration econometric analysis.

This script provides a single command-line interface for running the complete
three-tier econometric analysis with configurable options and output formats.

Usage:
    python scripts/run_analysis.py [options]

Options:
    --config PATH       Path to configuration file (JSON/YAML)
    --output-dir PATH   Output directory for results
    --format FORMAT     Output format: json, csv, excel, all (default: all)
    --tier TIER         Run specific tier only: 1, 2, 3, or all (default: all)
    --parallel          Enable parallel processing for Tier 2
    --verbose           Enable verbose logging
    --dry-run           Validate configuration without running analysis
"""

import sys
import argparse
import json
import yaml
import pandas as pd
from pathlib import Path
from typing import Dict, Any, Optional

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Use V1 compatibility layer for backward compatibility
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from v1_compat import (
    setup_logging, info, error, warning, timer, bind,
    PanelBuilder, ThreeTierAnalysis, ModelMigrationHelper
)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Yemen Market Integration Econometric Analysis",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )

    parser.add_argument(
        '--config', '-c',
        type=str,
        help='Path to configuration file (JSON or YAML)'
    )

    parser.add_argument(
        '--output-dir', '-o',
        type=str,
        default='results/analysis',
        help='Output directory for results (default: results/analysis)'
    )

    parser.add_argument(
        '--format', '-f',
        choices=['json', 'csv', 'excel', 'all'],
        default='all',
        help='Output format (default: all)'
    )

    parser.add_argument(
        '--tier', '-t',
        choices=['1', '2', '3', 'all'],
        default='all',
        help='Run specific tier only (default: all)'
    )

    parser.add_argument(
        '--parallel', '-p',
        action='store_true',
        help='Enable parallel processing for Tier 2'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Validate configuration without running analysis'
    )

    return parser.parse_args()


def load_configuration(config_path: Optional[str]) -> Dict[str, Any]:
    """Load configuration from file or use defaults."""
    bind(module="configuration")

    if config_path:
        config_file = Path(config_path)
        if not config_file.exists():
            error(f"Configuration file not found: {config_path}")
            raise FileNotFoundError(f"Configuration file not found: {config_path}")

        info(f"Loading configuration from {config_path}")

        if config_file.suffix.lower() in ['.yaml', '.yml']:
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)
        elif config_file.suffix.lower() == '.json':
            with open(config_file, 'r') as f:
                config = json.load(f)
        else:
            error("Configuration file must be JSON or YAML")
            raise ValueError("Configuration file must be JSON or YAML")
    else:
        # Default configuration
        info("Using default configuration")
        config = {
            'tier1_config': {
                'fixed_effects': ['entity', 'time'],
                'cluster_var': 'entity',
                'driscoll_kraay': True,
                'dependent_var': 'usd_price',
                'independent_vars': None  # Auto-detect
            },
            'tier2_config': {
                'min_observations': 50,
                'min_markets': 3,
                'min_periods': 20,
                'test_thresholds': True,
                'max_lags': 4,
                'threshold_var': 'conflict_intensity',
                'bootstrap_reps': 1000
            },
            'tier3_config': {
                'n_factors': 3,
                'standardize': True,
                'conflict_validation': True,
                'conflict_threshold': 10,
                'min_variance_explained': 0.8
            }
        }

    return config


def load_data():
    """Load panel and conflict data."""
    bind(module="data_loading")

    with timer("load_data"):
        # Load panel data
        panel_path = Path("data/processed/panels/integrated_panel.parquet")

        if not panel_path.exists():
            error(f"Panel data not found at {panel_path}")
            error("Please run: python scripts/analysis/build_panel_datasets.py")
            raise FileNotFoundError("Panel data not found. Run build_panel_datasets.py first.")

        panel_df = pd.read_parquet(panel_path)
        info(f"Loaded panel data: {panel_df.shape}")

        # Standardize column names
        column_mapping = {
            'market': 'governorate',
            'price_usd': 'usd_price'
        }

        for old_col, new_col in column_mapping.items():
            if old_col in panel_df.columns and new_col not in panel_df.columns:
                panel_df[new_col] = panel_df[old_col]
                info(f"Renamed column: {old_col} → {new_col}")

        # Load conflict data if available
        conflict_path = Path("data/processed/conflict/acled_processed.parquet")
        conflict_df = None

        if conflict_path.exists():
            try:
                conflict_df = pd.read_parquet(conflict_path)
                info(f"Loaded conflict data: {conflict_df.shape}")
            except Exception as e:
                warning(f"Failed to load conflict data: {str(e)}")
        else:
            warning("Conflict data not found. Tier 3 conflict validation will be limited.")

        return panel_df, conflict_df


def validate_configuration(config: Dict[str, Any], args) -> bool:
    """Validate configuration and arguments."""
    bind(module="validation")

    info("Validating configuration...")

    # Check required sections
    required_sections = ['tier1_config', 'tier2_config', 'tier3_config']
    for section in required_sections:
        if section not in config:
            error(f"Missing required configuration section: {section}")
            return False

    # Validate output directory
    output_dir = Path(args.output_dir)
    try:
        output_dir.mkdir(parents=True, exist_ok=True)
        info(f"Output directory: {output_dir}")
    except Exception as e:
        error(f"Cannot create output directory {output_dir}: {str(e)}")
        return False

    # Add runtime configuration
    config['output_dir'] = str(output_dir)
    config['run_parallel'] = args.parallel
    config['output_format'] = args.format
    config['selected_tier'] = args.tier

    info("✅ Configuration validation passed")
    return True


def run_analysis_pipeline(panel_df, conflict_df, config: Dict[str, Any], args) -> Dict[str, Any]:
    """Run the complete analysis pipeline."""
    bind(module="analysis_pipeline")

    info("="*60)
    info("Yemen Market Integration - Three-Tier Analysis")
    info("="*60)

    with timer("complete_analysis"):
        # Initialize analysis
        analysis = ThreeTierAnalysis(config)

        # Run selected tiers
        if args.tier == 'all':
            info("Running complete three-tier analysis")
            results = analysis.run_full_analysis(panel_df, conflict_data=conflict_df)
        else:
            info(f"Running Tier {args.tier} only")
            results = analysis.run_single_tier(
                int(args.tier), panel_df, conflict_data=conflict_df
            )

        # Log summary statistics
        log_analysis_summary(results, args.tier)

        return results


def log_analysis_summary(results: Dict[str, Any], tier_selection: str):
    """Log summary of analysis results."""
    bind(module="summary")

    info("\n" + "="*40)
    info("ANALYSIS SUMMARY")
    info("="*40)

    if tier_selection == 'all' or tier_selection == '1':
        if 'tier1' in results and results['tier1']:
            tier1 = results['tier1']
            if hasattr(tier1, 'comparison_metrics'):
                info(f"Tier 1 - R²: {tier1.comparison_metrics.r_squared:.4f}")
            else:
                info("Tier 1 - Completed successfully")

    if tier_selection == 'all' or tier_selection == '2':
        if 'tier2' in results and results['tier2']:
            n_commodities = len(results['tier2'])
            info(f"Tier 2 - Analyzed {n_commodities} commodities")

    if tier_selection == 'all' or tier_selection == '3':
        if 'tier3' in results and results['tier3']:
            info("Tier 3 - Factor analysis and validation completed")

    info("="*40)


def save_results(results: Dict[str, Any], config: Dict[str, Any]):
    """Save results in requested formats."""
    bind(module="output")

    output_dir = Path(config['output_dir'])
    output_format = config['output_format']

    info(f"Saving results to {output_dir}")

    # Always save JSON for programmatic access
    json_path = output_dir / "analysis_results.json"
    with open(json_path, 'w') as f:
        # Convert results to JSON-serializable format
        json_results = serialize_results(results)
        json.dump(json_results, f, indent=2, default=str)
    info(f"Results saved to {json_path}")

    # Save additional formats if requested
    if output_format in ['csv', 'all']:
        save_csv_results(results, output_dir)

    if output_format in ['excel', 'all']:
        save_excel_results(results, output_dir)


def serialize_results(results: Dict[str, Any]) -> Dict[str, Any]:
    """Convert results to JSON-serializable format."""
    # This is a simplified serialization - in production you'd want more sophisticated handling
    serialized = {}

    for tier, tier_results in results.items():
        if hasattr(tier_results, '__dict__'):
            # Convert objects to dictionaries
            serialized[tier] = {
                'type': type(tier_results).__name__,
                'summary': str(tier_results) if hasattr(tier_results, '__str__') else 'Results object'
            }
        else:
            serialized[tier] = tier_results

    return serialized


def save_csv_results(results: Dict[str, Any], output_dir: Path):
    """Save results in CSV format."""
    # Implementation would depend on specific result structure
    info("CSV export functionality would be implemented here")


def save_excel_results(results: Dict[str, Any], output_dir: Path):
    """Save results in Excel format."""
    # Implementation would depend on specific result structure
    info("Excel export functionality would be implemented here")


def main():
    """Main entry point."""
    args = parse_arguments()

    # Setup logging
    log_level = "DEBUG" if args.verbose else "INFO"
    setup_logging(log_level)
    bind(module="main")

    try:
        # Load configuration
        config = load_configuration(args.config)

        # Validate configuration
        if not validate_configuration(config, args):
            error("Configuration validation failed")
            sys.exit(1)

        if args.dry_run:
            info("✅ Dry run completed successfully")
            return

        # Load data
        panel_df, conflict_df = load_data()

        # Run analysis
        results = run_analysis_pipeline(panel_df, conflict_df, config, args)

        # Save results
        save_results(results, config)

        info("\n✅ Analysis completed successfully!")
        info(f"Results saved to: {config['output_dir']}")

    except Exception as e:
        error(f"Analysis failed: {str(e)}")
        if args.verbose:
            import traceback
            error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
