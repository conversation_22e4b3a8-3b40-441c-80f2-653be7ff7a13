#!/usr/bin/env python3
"""Validate internal documentation links."""

import os
import re
import sys
from pathlib import Path
from typing import List, Tuple, Set


def find_markdown_files(root_dir: Path) -> List[Path]:
    """Find all markdown files in the repository."""
    markdown_files = []
    
    for md_file in root_dir.rglob("*.md"):
        # Skip node_modules and .git directories
        if "node_modules" in str(md_file) or ".git" in str(md_file):
            continue
        markdown_files.append(md_file)
    
    return markdown_files


def extract_internal_links(file_path: Path, root_dir: Path) -> List[Tuple[str, int]]:
    """Extract internal links from a markdown file."""
    internal_links = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return []
    
    # Find all markdown links [text](url)
    link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
    
    for line_num, line in enumerate(content.split('\n'), 1):
        matches = re.finditer(link_pattern, line)
        
        for match in matches:
            link_text, link_url = match.groups()
            
            # Skip external links (http/https)
            if link_url.startswith(('http://', 'https://')):
                continue
            
            # Skip mailto links
            if link_url.startswith('mailto:'):
                continue
            
            # Skip anchors without files
            if link_url.startswith('#'):
                continue
            
            internal_links.append((link_url, line_num))
    
    return internal_links


def resolve_link_path(link_url: str, source_file: Path, root_dir: Path) -> Path:
    """Resolve a relative link to an absolute path."""
    
    # Remove anchor fragments
    if '#' in link_url:
        link_url = link_url.split('#')[0]
    
    # If empty after removing anchor, it's a same-file anchor
    if not link_url:
        return source_file
    
    # Resolve relative to the source file's directory
    source_dir = source_file.parent
    
    if link_url.startswith('./'):
        # Relative to current directory
        resolved_path = source_dir / link_url[2:]
    elif link_url.startswith('../'):
        # Relative to parent directory
        resolved_path = source_dir / link_url
    elif link_url.startswith('/'):
        # Absolute path from root
        resolved_path = root_dir / link_url[1:]
    else:
        # Relative to current directory (no ./ prefix)
        resolved_path = source_dir / link_url
    
    return resolved_path.resolve()


def validate_documentation_links(root_dir: Path) -> Tuple[List[str], int]:
    """Validate all internal documentation links."""
    errors = []
    total_links = 0
    
    print("🔍 Finding markdown files...")
    markdown_files = find_markdown_files(root_dir)
    print(f"Found {len(markdown_files)} markdown files")
    
    # Get set of all existing files for quick lookup
    existing_files = set()
    for md_file in markdown_files:
        existing_files.add(md_file.resolve())
    
    print("\n🔗 Validating internal links...")
    
    for md_file in markdown_files:
        print(f"Checking {md_file.relative_to(root_dir)}")
        
        internal_links = extract_internal_links(md_file, root_dir)
        total_links += len(internal_links)
        
        for link_url, line_num in internal_links:
            target_path = resolve_link_path(link_url, md_file, root_dir)
            
            if not target_path.exists():
                error_msg = f"❌ Broken link in {md_file.relative_to(root_dir)}:{line_num}"
                error_msg += f"\n   Link: {link_url}"
                error_msg += f"\n   Resolved to: {target_path.relative_to(root_dir)}"
                error_msg += f"\n   File does not exist"
                errors.append(error_msg)
    
    return errors, total_links


def check_moved_file_references(root_dir: Path) -> List[str]:
    """Check for references to files that were moved during reorganization."""
    errors = []
    
    moved_files = [
        "ALTERNATIVE_EXPLANATIONS_FIX_REPORT.md",
        "DATACLASS_FIX_SUMMARY.md", 
        "HYPOTHESIS_REFACTORING_SUMMARY.md",
        "ROBUSTNESS_IMPLEMENTATION_SUMMARY.md",
        "ALTERNATIVE_EXPLANATIONS_TECHNICAL_ISSUES.md",
        "ECONOMETRIC_TECHNICAL_ISSUES_REPORT.md",
        "VERIFICATION_REPORT.md",
        "COMPREHENSIVE_IMPROVEMENT_PLAN.md",
        "yemen-methodological-transformation-strategy.md"
    ]
    
    print("\n📁 Checking for references to moved files...")
    
    for md_file in find_markdown_files(root_dir):
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception:
            continue
        
        for moved_file in moved_files:
            if moved_file in content:
                # Check if it's a proper reference to new location
                if f"docs/04-development/implementation-reports/{moved_file}" in content:
                    continue
                if f"docs/09-troubleshooting/technical-issues/{moved_file}" in content:
                    continue
                if f"docs/08-results/validation-reports/{moved_file}" in content:
                    continue
                if f"docs/05-methodology/strategic-documents/{moved_file}" in content:
                    continue
                
                error_msg = f"❌ Stale reference to moved file in {md_file.relative_to(root_dir)}"
                error_msg += f"\n   References: {moved_file}"
                error_msg += f"\n   Should reference new location in appropriate docs subdirectory"
                errors.append(error_msg)
    
    return errors


def main():
    """Main validation function."""
    root_dir = Path(__file__).parent.parent
    
    print("🚀 Starting documentation link validation...")
    print(f"Root directory: {root_dir}")
    
    # Validate internal links
    link_errors, total_links = validate_documentation_links(root_dir)
    
    # Check for moved file references
    moved_file_errors = check_moved_file_references(root_dir)
    
    # Report results
    all_errors = link_errors + moved_file_errors
    
    print(f"\n📊 Validation Results:")
    print(f"   Total links checked: {total_links}")
    print(f"   Broken links found: {len(link_errors)}")
    print(f"   Moved file reference issues: {len(moved_file_errors)}")
    print(f"   Total errors: {len(all_errors)}")
    
    if all_errors:
        print(f"\n❌ Found {len(all_errors)} documentation issues:")
        for error in all_errors:
            print(f"\n{error}")
        sys.exit(1)
    else:
        print(f"\n✅ All documentation links are valid!")
        sys.exit(0)


if __name__ == "__main__":
    main()