#!/usr/bin/env python3
"""Generate comprehensive reports for Yemen Market Integration analysis.

This is a thin wrapper script that uses the ReportGenerator class to create
publication-ready reports in multiple formats.

Usage:
    python scripts/generate_report.py [options]
    
Options:
    --input PATH        Path to analysis results (JSON file)
    --output-dir PATH   Output directory for reports
    --format FORMAT     Report format: pdf, html, word, latex, all (default: all)
    --template PATH     Custom template directory
    --config PATH       Report configuration file
    --verbose           Enable verbose logging
"""

import sys
import argparse
import json
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from yemen_market.utils.logging import setup_logging, info, error, bind
from yemen_market.reporting import ReportGenerator


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Yemen Market Integration Report Generator",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        '--input', '-i',
        type=str,
        default='results/analysis/analysis_results.json',
        help='Path to analysis results JSON file'
    )
    
    parser.add_argument(
        '--output-dir', '-o',
        type=str,
        default='reports/generated',
        help='Output directory for reports'
    )
    
    parser.add_argument(
        '--format', '-f',
        choices=['pdf', 'html', 'word', 'latex', 'all'],
        default='all',
        help='Report format (default: all)'
    )
    
    parser.add_argument(
        '--template', '-t',
        type=str,
        help='Custom template directory'
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='Report configuration file'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    return parser.parse_args()


def load_analysis_results(results_path: str) -> dict:
    """Load analysis results from JSON file."""
    results_file = Path(results_path)
    if not results_file.exists():
        error(f"Results file not found: {results_path}")
        raise FileNotFoundError(f"Results file not found: {results_path}")
    
    info(f"Loading analysis results from {results_path}")
    
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    info(f"Loaded results with {len(results)} sections")
    return results


def load_report_config(config_path: str = None) -> dict:
    """Load report configuration."""
    if config_path:
        config_file = Path(config_path)
        if not config_file.exists():
            error(f"Configuration file not found: {config_path}")
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_file, 'r') as f:
            config = json.load(f)
        info(f"Loaded report configuration from {config_path}")
    else:
        # Default configuration
        config = {
            'title': 'Yemen Market Integration Analysis',
            'subtitle': 'Three-Tier Econometric Methodology with World Bank Enhancements',
            'author': 'World Bank Development Research Group',
            'date': datetime.now().strftime('%B %Y'),
            'logo_path': None,
            'include_technical_appendix': True,
            'include_methodology': True,
            'include_data_description': True,
            'executive_summary_pages': 2,
            'figure_quality': 'high'
        }
        info("Using default report configuration")
    
    return config


def main():
    """Main entry point."""
    args = parse_arguments()
    
    # Setup logging
    log_level = "DEBUG" if args.verbose else "INFO"
    setup_logging(log_level)
    bind(module="generate_report")
    
    try:
        # Load analysis results
        results = load_analysis_results(args.input)
        
        # Load report configuration
        config = load_report_config(args.config)
        
        # Generate reports
        generator = ReportGenerator(results, config, args.output_dir)
        
        formats = [args.format] if args.format != 'all' else ['html', 'pdf', 'word', 'latex']
        generator.generate_all_reports(formats)
        
        info("✅ Report generation completed successfully!")
        info(f"Reports saved to: {args.output_dir}")
        
    except Exception as e:
        error(f"Report generation failed: {str(e)}")
        if args.verbose:
            import traceback
            error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()