"""
Migration script to transition from mock exchange rate data to real data sources.

This script demonstrates how to:
1. Configure real data sources
2. Validate API connections
3. Compare mock vs real data
4. Migrate existing analyses to use real data
"""

import asyncio
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
import json
import os
from typing import Dict, List

from src.infrastructure.external_services.exchange_rate_collector import ExchangeRateCollector
from src.infrastructure.external_services.exchange_rate_collector_v2 import (
    ExchangeRateCollectorV2,
    EXAMPLE_CONFIG
)


def load_api_credentials() -> Dict[str, str]:
    """Load API credentials from environment or config file."""
    credentials = {}
    
    # Try environment variables first
    credentials['xe_api_key'] = os.environ.get('XE_API_KEY', '')
    credentials['oanda_api_key'] = os.environ.get('OANDA_API_KEY', '')
    credentials['oanda_account_id'] = os.environ.get('OANDA_ACCOUNT_ID', '')
    credentials['telegram_api_id'] = os.environ.get('TELEGRAM_API_ID', '')
    credentials['telegram_api_hash'] = os.environ.get('TELEGRAM_API_HASH', '')
    
    # Try loading from config file
    config_file = Path('.exchange_rate_config.json')
    if config_file.exists():
        with open(config_file, 'r') as f:
            file_config = json.load(f)
            credentials.update(file_config)
    
    return credentials


async def test_data_sources(config: Dict[str, any]) -> Dict[str, bool]:
    """Test connectivity to each data source."""
    print("Testing data source connectivity...")
    print("-" * 50)
    
    results = {}
    collector = ExchangeRateCollectorV2(config)
    test_date = datetime.now() - timedelta(days=1)  # Yesterday
    
    for source in collector.sources:
        source_name = source.get_source_name()
        try:
            async with source:
                rates = await source.fetch_rates(test_date)
                success = len(rates) > 0
                results[source_name] = success
                
                if success:
                    print(f"✅ {source_name}: Connected successfully ({len(rates)} rates)")
                else:
                    print(f"⚠️  {source_name}: Connected but no data returned")
                    
        except Exception as e:
            results[source_name] = False
            print(f"❌ {source_name}: Connection failed - {str(e)[:50]}...")
    
    return results


async def compare_mock_vs_real(date_range: tuple) -> pd.DataFrame:
    """Compare mock data with real data for validation."""
    print("\nComparing mock vs real exchange rates...")
    print("-" * 50)
    
    # Initialize collectors
    mock_collector = ExchangeRateCollector()  # V1 with mock data
    
    # Load real credentials
    credentials = load_api_credentials()
    real_config = EXAMPLE_CONFIG.copy()
    real_config.update(credentials)
    
    real_collector = ExchangeRateCollectorV2(real_config)
    
    # Collect data for date range
    comparison_data = []
    current_date = date_range[0]
    
    while current_date <= date_range[1]:
        print(f"Collecting data for {current_date.strftime('%Y-%m-%d')}...")
        
        # Collect mock data
        mock_rates = await mock_collector.collect_daily_rates(current_date)
        
        # Collect real data
        real_rates = await real_collector.collect_daily_rates(current_date)
        
        # Compare by zone
        for zone in ['HOUTHI', 'GOVERNMENT', 'CONTESTED']:
            mock_rate = next((r for r in mock_rates if r.zone.value == zone), None)
            real_rate = next((r for r in real_rates if r.zone.value == zone), None)
            
            comparison_data.append({
                'date': current_date,
                'zone': zone,
                'mock_rate': float(mock_rate.rate) if mock_rate else None,
                'real_rate': float(real_rate.rate) if real_rate else None,
                'difference': abs(float(mock_rate.rate) - float(real_rate.rate)) if mock_rate and real_rate else None,
                'real_confidence': real_rate.confidence if real_rate else None,
                'real_source': real_rate.source if real_rate else None
            })
        
        current_date += timedelta(days=1)
    
    df = pd.DataFrame(comparison_data)
    
    # Summary statistics
    print("\nComparison Summary:")
    for zone in ['HOUTHI', 'GOVERNMENT', 'CONTESTED']:
        zone_data = df[df['zone'] == zone]
        valid_comparisons = zone_data.dropna(subset=['difference'])
        
        if len(valid_comparisons) > 0:
            avg_diff = valid_comparisons['difference'].mean()
            max_diff = valid_comparisons['difference'].max()
            coverage = len(valid_comparisons) / len(zone_data) * 100
            
            print(f"\n{zone} Zone:")
            print(f"  - Coverage: {coverage:.1f}%")
            print(f"  - Average difference: {avg_diff:.2f} YER/USD")
            print(f"  - Maximum difference: {max_diff:.2f} YER/USD")
        else:
            print(f"\n{zone} Zone: No valid comparisons available")
    
    return df


async def migrate_existing_data(config: Dict[str, any]):
    """Migrate existing analyses to use real exchange rate data."""
    print("\nMigrating existing data to use real exchange rates...")
    print("-" * 50)
    
    # Find existing processed data files
    processed_dir = Path('./data/processed')
    existing_files = list(processed_dir.glob('**/price_data_*.csv'))
    
    if not existing_files:
        print("No existing processed data files found.")
        return
    
    print(f"Found {len(existing_files)} files to migrate:")
    
    collector = ExchangeRateCollectorV2(config)
    
    for file_path in existing_files:
        print(f"\nMigrating {file_path.name}...")
        
        try:
            # Load existing data
            df = pd.read_csv(file_path)
            
            if 'price_usd' not in df.columns:
                print(f"  ⚠️  Skipping - no USD prices found")
                continue
            
            # Identify unique dates and zones
            if 'date' in df.columns and 'currency_zone' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
                unique_combinations = df[['date', 'currency_zone']].drop_duplicates()
                
                # Collect real exchange rates
                print(f"  Collecting real exchange rates for {len(unique_combinations)} date-zone combinations...")
                
                updated_count = 0
                for _, row in unique_combinations.iterrows():
                    date = row['date']
                    zone = row['currency_zone']
                    
                    # Get real rate
                    rates = await collector.collect_daily_rates(date)
                    zone_rate = next((r for r in rates if r.zone.value == zone), None)
                    
                    if zone_rate:
                        # Update all rows with this date-zone combination
                        mask = (df['date'] == date) & (df['currency_zone'] == zone)
                        df.loc[mask, 'exchange_rate_used_real'] = float(zone_rate.rate)
                        df.loc[mask, 'price_usd_real'] = df.loc[mask, 'price_yer'] / float(zone_rate.rate)
                        df.loc[mask, 'exchange_rate_source_real'] = zone_rate.source
                        updated_count += mask.sum()
                
                print(f"  ✅ Updated {updated_count} rows with real exchange rates")
                
                # Save migrated file
                migrated_path = file_path.parent / f"migrated_{file_path.name}"
                df.to_csv(migrated_path, index=False)
                print(f"  💾 Saved to {migrated_path}")
                
        except Exception as e:
            print(f"  ❌ Error migrating file: {e}")


def generate_migration_report(comparison_df: pd.DataFrame, test_results: Dict[str, bool]):
    """Generate a migration readiness report."""
    print("\n" + "=" * 60)
    print("EXCHANGE RATE DATA MIGRATION REPORT")
    print("=" * 60)
    
    # Data source readiness
    print("\n1. Data Source Readiness:")
    total_sources = len(test_results)
    working_sources = sum(test_results.values())
    
    print(f"   - Total sources configured: {total_sources}")
    print(f"   - Working sources: {working_sources}")
    print(f"   - Success rate: {working_sources/total_sources*100:.1f}%")
    
    # Critical sources check
    critical_sources = ['WFP_MarketMonitoring', 'XE_International']
    critical_available = sum(1 for s in critical_sources if test_results.get(s, False))
    
    if critical_available >= 1:
        print(f"   ✅ At least one critical source is available")
    else:
        print(f"   ⚠️  WARNING: No critical sources available")
    
    # Data quality assessment
    print("\n2. Data Quality Assessment:")
    
    if not comparison_df.empty:
        coverage_by_zone = comparison_df.groupby('zone')['real_rate'].apply(
            lambda x: (x.notna().sum() / len(x) * 100)
        )
        
        for zone, coverage in coverage_by_zone.items():
            print(f"   - {zone} zone coverage: {coverage:.1f}%")
        
        # Check for data gaps
        data_gaps = comparison_df[comparison_df['real_rate'].isna()]
        if len(data_gaps) > 0:
            gap_dates = data_gaps['date'].unique()
            print(f"\n   ⚠️  Data gaps found on {len(gap_dates)} dates")
    
    # Migration recommendations
    print("\n3. Migration Recommendations:")
    
    if working_sources >= 2:
        print("   ✅ READY for production use with real data")
        print("   - Configure API credentials in production environment")
        print("   - Set up automated daily collection")
        print("   - Monitor data quality metrics")
    else:
        print("   ⚠️  NOT READY for full migration")
        print("   - Need at least 2 working data sources")
        print("   - Consider adding WFP data files as backup")
        print("   - May need to implement web scraping for CBY sites")
    
    # Next steps
    print("\n4. Next Steps:")
    print("   1. Obtain API credentials for missing sources")
    print("   2. Set up data quality monitoring")
    print("   3. Implement fallback mechanisms")
    print("   4. Schedule regular data collection")
    print("   5. Create alerts for data gaps")
    
    print("\n" + "=" * 60)


async def main():
    """Run the migration process."""
    print("Yemen Market Integration - Exchange Rate Data Migration")
    print("=" * 60)
    
    # Load configuration
    credentials = load_api_credentials()
    config = EXAMPLE_CONFIG.copy()
    config.update(credentials)
    
    # Step 1: Test data sources
    test_results = await test_data_sources(config)
    
    # Step 2: Compare mock vs real (if we have working sources)
    comparison_df = pd.DataFrame()
    if any(test_results.values()):
        date_range = (
            datetime.now() - timedelta(days=7),
            datetime.now() - timedelta(days=1)
        )
        comparison_df = await compare_mock_vs_real(date_range)
        
        # Save comparison for analysis
        comparison_df.to_csv('./exchange_rate_comparison.csv', index=False)
    
    # Step 3: Migrate existing data (optional)
    migrate_choice = input("\nMigrate existing data files? (y/n): ")
    if migrate_choice.lower() == 'y':
        await migrate_existing_data(config)
    
    # Step 4: Generate report
    generate_migration_report(comparison_df, test_results)


if __name__ == "__main__":
    asyncio.run(main())