#!/usr/bin/env python3
"""
Comprehensive test runner for Yemen Market Integration econometric research.

This script provides a unified interface for running econometric model tests,
validation tests, and system integration tests with rigorous standards.
"""

import argparse
import json
import os
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

try:
    import psutil
except ImportError:
    psutil = None


class EconometricTestRunner:
    """Comprehensive test runner for econometric research system."""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.test_dir = project_root / "tests"
        self.src_dir = project_root / "src"
        self.results = {}
        self.start_time = None
        self.use_poetry = False
        
        # Econometric test categories
        self.test_categories = {
            'unit': 'Core econometric model unit tests',
            'integration': 'Three-tier analysis integration tests',
            'validation': 'Research finding validation tests',
            'econometric': 'Econometric assumption tests',
            'data_quality': 'Data quality and consistency tests',
            'robustness': 'Robustness and sensitivity tests'
        }
        
    def run_command(self, command: List[str], timeout: int = 300) -> Tuple[int, str, str]:
        """Run a command and capture output."""
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.project_root
            )
            return result.returncode, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return 1, "", f"Command timed out after {timeout} seconds"
        except Exception as e:
            return 1, "", str(e)
    
    def check_dependencies(self) -> bool:
        """Check if all required dependencies are available."""
        print("🔍 Checking econometric research dependencies...")
        
        # Check Python version
        if sys.version_info < (3, 9):
            print("❌ Python 3.9+ required for econometric libraries")
            return False
        
        # Check if using Poetry or pip
        returncode, _, _ = self.run_command(["poetry", "--version"])
        self.use_poetry = returncode == 0
        
        if not self.use_poetry:
            # Check for pip/venv setup
            venv_path = self.project_root / "venv"
            if not venv_path.exists():
                print("❌ No Poetry or virtual environment found.")
                print("   Please run: python -m venv venv && source venv/bin/activate && pip install -r requirements.txt")
                return False
        
        # Install dependencies if needed
        if self.use_poetry:
            returncode, _, _ = self.run_command(["poetry", "env", "info"])
            if returncode != 0:
                print("⚠️  Poetry environment not found. Installing dependencies...")
                returncode, _, stderr = self.run_command(["poetry", "install", "--with", "dev"])
                if returncode != 0:
                    print(f"❌ Failed to install dependencies: {stderr}")
                    return False
        else:
            # Check pytest is available
            returncode, _, _ = self.run_command(["python", "-m", "pytest", "--version"])
            if returncode != 0:
                print("❌ pytest not found. Please install test dependencies.")
                return False
        
        # Check econometric packages
        print("📊 Checking econometric packages...")
        required_packages = ['numpy', 'pandas', 'scipy', 'statsmodels', 'scikit-learn']
        
        for package in required_packages:
            try:
                __import__(package)
                print(f"  ✅ {package}")
            except ImportError:
                print(f"  ❌ {package} not found")
                return False
        
        print("✅ All dependencies available")
        return True
    
    def _get_command_prefix(self) -> List[str]:
        """Get command prefix based on package manager."""
        if self.use_poetry:
            return ["poetry", "run"]
        else:
            return ["python", "-m"]
    
    def run_code_quality_checks(self) -> Dict[str, bool]:
        """Run code quality checks focused on econometric code."""
        print("\n🎯 Running econometric code quality checks...")
        
        checks = {}
        prefix = self._get_command_prefix()
        
        # Linting with focus on econometric code
        print("  Running Ruff linting...")
        returncode, stdout, stderr = self.run_command(
            prefix + ["ruff", "check", "src/", "tests/"]
        )
        checks["ruff"] = returncode == 0
        if returncode != 0:
            print(f"    ❌ Ruff found issues")
            if "--fix" in stderr or "fixable" in stdout:
                print("    💡 Run 'ruff check --fix' to auto-fix some issues")
        else:
            print("    ✅ Ruff linting passed")
        
        # Type checking with MyPy (important for numerical code)
        print("  Running MyPy type checking...")
        returncode, stdout, stderr = self.run_command(
            prefix + ["mypy", "src/", "--ignore-missing-imports", "--show-error-codes"]
        )
        checks["mypy"] = returncode == 0
        if returncode != 0:
            print(f"    ❌ MyPy found type issues")
            # Show first few errors for context
            error_lines = stderr.split('\n')[:5]
            for line in error_lines:
                if line.strip():
                    print(f"       {line}")
        else:
            print("    ✅ MyPy type checking passed")
        
        # Code formatting with Black
        print("  Checking code formatting...")
        returncode, stdout, stderr = self.run_command(
            prefix + ["black", "--check", "src/", "tests/"]
        )
        checks["black"] = returncode == 0
        if returncode != 0:
            print("    ❌ Code formatting issues found")
            print("    💡 Run 'black src/ tests/' to auto-format")
        else:
            print("    ✅ Code formatting is correct")
        
        # Import sorting with isort
        print("  Checking import sorting...")
        returncode, stdout, stderr = self.run_command(
            prefix + ["isort", "--check-only", "src/", "tests/"]
        )
        checks["isort"] = returncode == 0
        if returncode != 0:
            print("    ❌ Import sorting issues found")
            print("    💡 Run 'isort src/ tests/' to auto-sort imports")
        else:
            print("    ✅ Import sorting is correct")
        
        # Check for common numerical issues
        print("  Checking for numerical computation issues...")
        numerical_check = self._check_numerical_issues()
        checks["numerical"] = numerical_check
        if not numerical_check:
            print("    ❌ Potential numerical issues found")
        else:
            print("    ✅ No numerical issues detected")
        
        return checks
    
    def _check_numerical_issues(self) -> bool:
        """Check for common numerical computation issues in econometric code."""
        # Look for potential issues in Python files
        issues_found = False
        
        for py_file in self.src_dir.rglob("*.py"):
            try:
                content = py_file.read_text()
                
                # Check for division without zero protection
                if "/ " in content and "if" not in content:
                    # This is a simplified check - real check would be more sophisticated
                    pass
                
                # Check for hardcoded tolerances
                if "1e-" in content or "0.001" in content:
                    # Should use configurable tolerances
                    pass
                    
            except Exception:
                continue
        
        return not issues_found
    
    def run_econometric_unit_tests(self, verbose: bool = False, coverage: bool = True, 
                                  markers: List[str] = None) -> Dict[str, any]:
        """Run econometric model unit tests."""
        print("\n🧪 Running econometric unit tests...")
        
        prefix = self._get_command_prefix()
        command = prefix + ["pytest", "tests/unit/"]
        
        if coverage:
            command.extend([
                "--cov=src",
                "--cov-report=term-missing",
                "--cov-report=xml:coverage.xml",
                "--cov-report=html:htmlcov",
                "--cov-fail-under=70",  # Minimum 70% coverage for econometric code
                "--cov-branch"  # Branch coverage for complex econometric logic
            ])
        
        if verbose:
            command.append("-v")
        
        command.extend([
            "--junit-xml=junit-unit.xml",
            "--tb=short",
            "-x",  # Stop on first failure for faster feedback
            "--strict-markers",  # Ensure all markers are defined
            "--durations=10"  # Show 10 slowest tests
        ])
        
        if markers:
            command.extend(["-m", " and ".join(markers)])
        
        start_time = time.time()
        returncode, stdout, stderr = self.run_command(command, timeout=900)  # Longer timeout for complex models
        duration = time.time() - start_time
        
        # Parse test results
        success = returncode == 0
        
        # Extract detailed test information
        test_count = self._extract_test_count(stdout)
        coverage_pct = self._extract_coverage_percentage(stdout) if coverage else None
        
        result = {
            "success": success,
            "duration": duration,
            "test_count": test_count,
            "coverage_percentage": coverage_pct,
            "output": stdout,
            "errors": stderr,
            "category": "econometric_unit"
        }
        
        if success:
            cov_str = f", {coverage_pct:.1f}% coverage" if coverage_pct else ""
            print(f"    ✅ Econometric unit tests passed ({test_count} tests in {duration:.2f}s{cov_str})")
        else:
            print(f"    ❌ Econometric unit tests failed")
            self._print_test_failures(stderr)
        
        return result
    
    def run_validation_tests(self, verbose: bool = False) -> Dict[str, any]:
        """Run research finding validation tests."""
        print("\n🎯 Running research validation tests...")
        
        prefix = self._get_command_prefix()
        command = prefix + [
            "pytest", "tests/validation/",
            "--junit-xml=junit-validation.xml",
            "--tb=short",
            "-m", "validation"
        ]
        
        if verbose:
            command.append("-v")
        
        start_time = time.time()
        returncode, stdout, stderr = self.run_command(command, timeout=600)
        duration = time.time() - start_time
        
        success = returncode == 0
        test_count = self._extract_test_count(stdout)
        
        result = {
            "success": success,
            "duration": duration,
            "test_count": test_count,
            "output": stdout,
            "errors": stderr,
            "category": "validation"
        }
        
        if success:
            print(f"    ✅ Validation tests passed ({test_count} tests in {duration:.2f}s)")
        else:
            print(f"    ❌ Validation tests failed")
            self._print_test_failures(stderr)
        
        return result
    
    def run_three_tier_integration_tests(self, verbose: bool = False, 
                                        skip_service_check: bool = False) -> Dict[str, any]:
        """Run three-tier analysis integration tests."""
        print("\n🔗 Running three-tier integration tests...")
        
        # Check if required services are running (unless skipped)
        if not skip_service_check and not self._check_services():
            print("    ⚠️  Required services not available, skipping integration tests")
            print("    💡 Use --skip-service-check to run anyway")
            return {"success": False, "skipped": True, "reason": "Services not available"}
        
        prefix = self._get_command_prefix()
        command = prefix + [
            "pytest", "tests/integration/",
            "--junit-xml=junit-integration.xml",
            "--tb=short",
            "-m", "integration",  # Only run marked integration tests
            "--timeout=300"  # 5 minute timeout per test
        ]
        
        if verbose:
            command.append("-v")
        
        start_time = time.time()
        returncode, stdout, stderr = self.run_command(command, timeout=1800)  # 30 min for integration
        duration = time.time() - start_time
        
        success = returncode == 0
        test_count = self._extract_test_count(stdout)
        
        result = {
            "success": success,
            "duration": duration,
            "test_count": test_count,
            "output": stdout,
            "errors": stderr,
            "category": "three_tier_integration"
        }
        
        if success:
            print(f"    ✅ Three-tier integration tests passed ({test_count} tests in {duration:.2f}s)")
        else:
            print(f"    ❌ Three-tier integration tests failed")
            self._print_test_failures(stderr)
        
        return result
    
    def run_econometric_diagnostics(self, verbose: bool = False) -> Dict[str, any]:
        """Run econometric diagnostic tests."""
        print("\n📊 Running econometric diagnostic tests...")
        
        prefix = self._get_command_prefix()
        command = prefix + [
            "pytest", 
            "tests/unit/",
            "tests/integration/",
            "-k", "diagnostic",
            "--junit-xml=junit-diagnostics.xml",
            "--tb=short"
        ]
        
        if verbose:
            command.append("-v")
        
        start_time = time.time()
        returncode, stdout, stderr = self.run_command(command, timeout=900)
        duration = time.time() - start_time
        
        success = returncode == 0
        test_count = self._extract_test_count(stdout)
        
        result = {
            "success": success,
            "duration": duration,
            "test_count": test_count,
            "output": stdout,
            "errors": stderr,
            "category": "econometric_diagnostics"
        }
        
        if success:
            print(f"    ✅ Econometric diagnostics passed ({test_count} tests in {duration:.2f}s)")
        else:
            print(f"    ❌ Econometric diagnostics failed")
            self._print_test_failures(stderr)
        
        return result
    
    def run_specific_tests(self, test_path: str, verbose: bool = False) -> Dict[str, any]:
        """Run specific test file or directory."""
        print(f"\n🎯 Running specific tests: {test_path}")
        
        prefix = self._get_command_prefix()
        command = prefix + [
            "pytest", test_path,
            "--tb=short"
        ]
        
        if verbose:
            command.append("-v")
        
        start_time = time.time()
        returncode, stdout, stderr = self.run_command(command)
        duration = time.time() - start_time
        
        success = returncode == 0
        test_count = self._extract_test_count(stdout)
        
        result = {
            "success": success,
            "duration": duration,
            "test_count": test_count,
            "output": stdout,
            "errors": stderr
        }
        
        if success:
            print(f"    ✅ Tests passed ({test_count} tests in {duration:.2f}s)")
        else:
            print(f"    ❌ Tests failed")
        
        return result
    
    def generate_coverage_report(self) -> Dict[str, any]:
        """Generate comprehensive coverage report."""
        print("\n📊 Generating coverage report...")
        
        # Check if coverage.xml exists
        coverage_file = self.project_root / "coverage.xml"
        if not coverage_file.exists():
            print("    ⚠️  No coverage data found")
            return {"success": False, "reason": "No coverage data"}
        
        prefix = self._get_command_prefix()
        # Generate HTML report
        command = prefix + ["coverage", "html", "-d", "htmlcov"]
        returncode, stdout, stderr = self.run_command(command)
        
        if returncode == 0:
            print("    ✅ Coverage report generated in htmlcov/")
        
        # Get coverage percentage
        command = prefix + ["coverage", "report", "--show-missing"]
        returncode, stdout, stderr = self.run_command(command)
        
        coverage_percentage = self._extract_coverage_percentage(stdout)
        
        return {
            "success": returncode == 0,
            "coverage_percentage": coverage_percentage,
            "html_report": "htmlcov/index.html"
        }
    
    def _check_services(self) -> bool:
        """Check if required services are available."""
        services_ok = True
        
        # Check PostgreSQL (simplified check)
        try:
            import psycopg2
            print("    ✅ PostgreSQL driver available")
        except ImportError:
            print("    ⚠️  PostgreSQL driver not available")
            services_ok = False
        
        # Check Redis (simplified check)
        try:
            import redis
            print("    ✅ Redis driver available")
        except ImportError:
            print("    ⚠️  Redis driver not available")
            services_ok = False
        
        return services_ok
    
    def _extract_test_count(self, output: str) -> int:
        """Extract test count from pytest output."""
        lines = output.split('\n')
        for line in reversed(lines):
            if 'passed' in line or 'failed' in line:
                # Try to extract numbers from summary line
                import re
                numbers = re.findall(r'(\d+) passed', line)
                if numbers:
                    passed = int(numbers[0])
                    failed_match = re.findall(r'(\d+) failed', line)
                    failed = int(failed_match[0]) if failed_match else 0
                    return passed + failed
        return 0
    
    def _extract_coverage_percentage(self, output: str) -> float:
        """Extract coverage percentage from output."""
        lines = output.split('\n')
        for line in reversed(lines):
            if 'TOTAL' in line:
                import re
                percentages = re.findall(r'(\d+)%', line)
                if percentages:
                    return float(percentages[-1])
        return 0.0
    
    def _print_test_failures(self, stderr: str):
        """Print test failure summary."""
        if stderr:
            lines = stderr.split('\n')[:10]  # Show first 10 lines
            for line in lines:
                if line.strip():
                    print(f"      {line}")
    
    def save_results(self, filename: str = "test_results.json"):
        """Save test results to JSON file."""
        results_file = self.project_root / filename
        
        summary = {
            "timestamp": datetime.now().isoformat(),
            "total_duration": time.time() - self.start_time if self.start_time else 0,
            "results": self.results,
            "system_info": {
                "python_version": sys.version,
                "platform": sys.platform,
                "cpu_count": psutil.cpu_count() if psutil else "unknown",
                "memory_gb": psutil.virtual_memory().total / (1024**3) if psutil else "unknown"
            }
        }
        
        with open(results_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\n📄 Test results saved to {filename}")
        return summary
    
    def print_summary(self):
        """Print test execution summary."""
        print("\n" + "="*60)
        print("📋 ECONOMETRIC TEST EXECUTION SUMMARY")
        print("="*60)
        
        total_tests = 0
        total_duration = 0
        all_passed = True
        
        for test_type, result in self.results.items():
            if isinstance(result, dict) and 'success' in result:
                status = "✅ PASSED" if result['success'] else "❌ FAILED"
                duration = result.get('duration', 0)
                test_count = result.get('test_count', 0)
                coverage = result.get('coverage_percentage', None)
                
                cov_str = f" ({coverage:.1f}% cov)" if coverage else ""
                print(f"{test_type.replace('_', ' ').title():<25} {status:<10} "
                      f"{test_count:>3} tests  {duration:>6.2f}s{cov_str}")
                
                total_tests += test_count
                total_duration += duration
                
                if not result['success']:
                    all_passed = False
        
        print("-" * 60)
        print(f"{'TOTAL':<25} {'✅ ALL PASSED' if all_passed else '❌ SOME FAILED':<10} "
              f"{total_tests:>3} tests  {total_duration:>6.2f}s")
        
        # Coverage summary
        if 'econometric_unit' in self.results:
            coverage = self.results['econometric_unit'].get('coverage_percentage', 0)
            coverage_status = "✅" if coverage >= 80 else "⚠️" if coverage >= 70 else "❌"
            print(f"\nCoverage: {coverage_status} {coverage:.1f}% (Target: 80%+)")
        
        print("="*60)


def main():
    """Main entry point for econometric test runner."""
    parser = argparse.ArgumentParser(
        description="Comprehensive test runner for Yemen Market Integration econometric research"
    )
    
    parser.add_argument(
        "test_types",
        nargs="*",
        choices=["quality", "unit", "validation", "integration", "diagnostics", "all"],
        default=["all"],
        help="Types of tests to run"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    
    parser.add_argument(
        "--no-coverage",
        action="store_true",
        help="Skip coverage reporting"
    )
    
    parser.add_argument(
        "--fail-fast",
        action="store_true",
        help="Stop on first failure"
    )
    
    parser.add_argument(
        "--output", "-o",
        default="test_results.json",
        help="Output file for test results"
    )
    
    parser.add_argument(
        "--skip-service-check",
        action="store_true",
        help="Skip checking for required services"
    )
    
    parser.add_argument(
        "--markers", "-m",
        nargs="+",
        help="Run tests with specific markers"
    )
    
    parser.add_argument(
        "--test-path",
        help="Run specific test file or directory"
    )
    
    args = parser.parse_args()
    
    # Determine which tests to run
    if "all" in args.test_types:
        test_types = ["quality", "unit", "validation", "integration", "diagnostics"]
    else:
        test_types = args.test_types
    
    # Initialize test runner
    project_root = Path(__file__).parent.parent
    runner = EconometricTestRunner(project_root)
    runner.start_time = time.time()
    
    print("🚀 Starting comprehensive econometric test execution...")
    print(f"📁 Project root: {project_root}")
    print(f"🎯 Test types: {', '.join(test_types)}")
    
    # Check dependencies
    if not runner.check_dependencies():
        sys.exit(1)
    
    # Run selected tests
    try:
        # Run specific test if requested
        if args.test_path:
            runner.results["specific_tests"] = runner.run_specific_tests(
                args.test_path, verbose=args.verbose
            )
            if args.fail_fast and not runner.results["specific_tests"]["success"]:
                print("💥 Tests failed, stopping execution")
                sys.exit(1)
        
        if "quality" in test_types:
            runner.results["code_quality"] = runner.run_code_quality_checks()
            if args.fail_fast and not all(runner.results["code_quality"].values()):
                print("💥 Code quality checks failed, stopping execution")
                sys.exit(1)
        
        if "unit" in test_types:
            runner.results["econometric_unit"] = runner.run_econometric_unit_tests(
                verbose=args.verbose,
                coverage=not args.no_coverage,
                markers=args.markers
            )
            if args.fail_fast and not runner.results["econometric_unit"]["success"]:
                print("💥 Unit tests failed, stopping execution")
                sys.exit(1)
        
        if "validation" in test_types:
            runner.results["validation_tests"] = runner.run_validation_tests(
                verbose=args.verbose
            )
            if args.fail_fast and not runner.results["validation_tests"]["success"]:
                print("💥 Validation tests failed, stopping execution")
                sys.exit(1)
        
        if "integration" in test_types:
            runner.results["three_tier_integration"] = runner.run_three_tier_integration_tests(
                verbose=args.verbose,
                skip_service_check=args.skip_service_check
            )
            if args.fail_fast and not runner.results["three_tier_integration"]["success"]:
                print("💥 Integration tests failed, stopping execution")
                sys.exit(1)
        
        if "diagnostics" in test_types:
            runner.results["econometric_diagnostics"] = runner.run_econometric_diagnostics(
                verbose=args.verbose
            )
        
        # Generate coverage report if coverage was collected
        if not args.no_coverage and any(t in test_types for t in ["unit", "integration"]):
            runner.results["coverage"] = runner.generate_coverage_report()
        
    except KeyboardInterrupt:
        print("\n⏹️  Test execution interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
    
    # Generate summary and save results
    runner.print_summary()
    summary = runner.save_results(args.output)
    
    # Exit with appropriate code
    all_tests_passed = all(
        result.get("success", True) if isinstance(result, dict) else True
        for result in runner.results.values()
    )
    
    if all_tests_passed:
        print("\n🎉 ALL ECONOMETRIC TESTS PASSED!")
        print("✅ System ready for rigorous econometric analysis")
    else:
        print("\n❌ Some tests failed")
        print("🔧 Please address failures before proceeding")
    
    sys.exit(0 if all_tests_passed else 1)


if __name__ == "__main__":
    main()