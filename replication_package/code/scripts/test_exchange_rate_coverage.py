#!/usr/bin/env python3
"""Test exchange rate data coverage for Yemen."""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.external_services.exchange_rate_collector_v2 import ExchangeRateCollectorV2
from src.core.domain.market.currency_zones import CurrencyZone


async def check_exchange_rate_coverage():
    """Check exchange rate data coverage for Yemen."""
    print("=" * 60)
    print("Exchange Rate Coverage Check")
    print("=" * 60)
    
    # Initialize collector with minimal config
    config = {
        'cache_ttl': 3600,
        'timeout': 30,
        'wfp_data_directory': './data/raw/wfp',
        'cache_dir': './data/processed/exchange_rates'
    }
    
    collector = ExchangeRateCollectorV2(config)
    
    # Test dates
    test_dates = [
        datetime(2024, 1, 1),
        datetime(2024, 6, 1),
        datetime(2024, 12, 1),
    ]
    
    print(f"\nChecking {len(collector.sources)} configured data sources:")
    for source in collector.sources:
        print(f"  - {source.get_source_name()} (reliability: {source.get_reliability_score():.2f})")
    
    if not collector.sources:
        print("\n⚠️  No data sources configured!")
        print("To enable data sources, set environment variables:")
        print("  - XE_API_KEY: For XE.com integration")
        print("  - OANDA_API_KEY: For OANDA integration")
        print("  - WFP data directory: Place WFP CSV files in ./data/raw/wfp/")
        print("\nUsing mock data for testing...")
    
    print("\nChecking exchange rate availability for test dates:")
    
    coverage_summary = {
        'dates_checked': 0,
        'dates_with_data': 0,
        'zones_covered': set(),
        'missing_dates': []
    }
    
    for test_date in test_dates:
        print(f"\n{test_date.strftime('%Y-%m-%d')}:")
        
        try:
            # Try to collect rates for this date
            rates = await collector.collect_daily_rates(test_date)
            
            if rates:
                print(f"  ✅ Found {len(rates)} exchange rates")
                for rate in rates:
                    print(f"     - {rate.zone.value}: {rate.rate:.2f} YER/USD ({rate.source})")
                    coverage_summary['zones_covered'].add(rate.zone.value)
                coverage_summary['dates_with_data'] += 1
            else:
                print(f"  ❌ No exchange rates found")
                coverage_summary['missing_dates'].append(test_date)
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
            coverage_summary['missing_dates'].append(test_date)
        
        coverage_summary['dates_checked'] += 1
    
    # Check full year coverage
    print("\n" + "=" * 60)
    print("Full Year Coverage Check (2024)")
    print("=" * 60)
    
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 12, 31)
    current_date = start_date
    
    monthly_coverage = {}
    
    while current_date <= end_date:
        month_key = current_date.strftime('%Y-%m')
        if month_key not in monthly_coverage:
            monthly_coverage[month_key] = {
                'days_checked': 0,
                'days_with_data': 0,
                'zones': set()
            }
        
        # Check if cache exists for this date
        cache_file = Path(config['cache_dir']) / f"rates_{current_date.strftime('%Y%m%d')}.json"
        if cache_file.exists():
            monthly_coverage[month_key]['days_with_data'] += 1
            # Could load and check zones if needed
        
        monthly_coverage[month_key]['days_checked'] += 1
        current_date += timedelta(days=1)
    
    # Print monthly summary
    print("\nMonthly Coverage Summary:")
    for month, stats in sorted(monthly_coverage.items()):
        coverage_pct = (stats['days_with_data'] / stats['days_checked']) * 100
        print(f"  {month}: {stats['days_with_data']}/{stats['days_checked']} days ({coverage_pct:.1f}%)")
    
    # Overall summary
    print("\n" + "=" * 60)
    print("Overall Coverage Summary")
    print("=" * 60)
    
    overall_coverage = (coverage_summary['dates_with_data'] / coverage_summary['dates_checked']) * 100
    print(f"Test dates coverage: {coverage_summary['dates_with_data']}/{coverage_summary['dates_checked']} ({overall_coverage:.1f}%)")
    print(f"Currency zones found: {', '.join(sorted(coverage_summary['zones_covered']))}")
    
    if coverage_summary['missing_dates']:
        print(f"\nMissing data for dates:")
        for date in coverage_summary['missing_dates']:
            print(f"  - {date.strftime('%Y-%m-%d')}")
    
    # Recommendations
    print("\n" + "=" * 60)
    print("Recommendations")
    print("=" * 60)
    
    if not collector.sources:
        print("1. Configure at least one data source (XE, OANDA, or WFP)")
        print("2. Download WFP data using: python scripts/data_collection/download_data.py")
        print("3. Set API keys in environment variables or .env file")
    elif overall_coverage < 80:
        print("1. Coverage is below 80% - consider adding more data sources")
        print("2. Check if WFP data files are up to date")
        print("3. Implement data imputation for missing dates")
    else:
        print("✅ Exchange rate coverage appears adequate for analysis")
    
    return coverage_summary


def main():
    """Main execution."""
    try:
        # Run async function
        coverage = asyncio.run(check_exchange_rate_coverage())
        
        # Exit code based on coverage
        if coverage['dates_with_data'] == 0:
            print("\n❌ CRITICAL: No exchange rate data available!")
            return 1
        elif coverage['dates_with_data'] < coverage['dates_checked'] * 0.5:
            print("\n⚠️  WARNING: Exchange rate coverage below 50%")
            return 1
        else:
            print("\n✅ Exchange rate check completed")
            return 0
            
    except KeyboardInterrupt:
        print("\n\nInterrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())