#!/usr/bin/env python3
"""
API Server Startup Script for Yemen Market Integration v2
"""

import os
import sys
import subprocess
from pathlib import Path

def setup_environment():
    """Set up environment variables for API server."""
    env_vars = {
        "DATABASE_URL": "postgresql://localhost/yemen_market",
        "CACHE_TYPE": "memory",
        "CACHE_TTL": "3600",
        "CACHE_MAX_SIZE": "1000",
        "EVENT_BUS_TYPE": "inmemory",
        "EVENT_QUEUE_SIZE": "10000",
        "HDX_TIMEOUT": "30",
        "WFP_API_KEY": "",
        "ACLED_API_KEY": "",
        "ACLED_EMAIL": "",
        "POLICY_RESULTS_PATH": "data/policy_results",
        "API_HOST": "0.0.0.0",
        "API_PORT": "8000",
        "API_RELOAD": "true",
        "API_LOG_LEVEL": "info"
    }
    
    for key, default_value in env_vars.items():
        if key not in os.environ:
            os.environ[key] = default_value
    
    print("Environment variables configured:")
    for key, value in env_vars.items():
        print(f"  {key}={os.environ.get(key)}")

def main():
    """Start the API server."""
    print("🚀 Starting Yemen Market Integration API v2...")
    
    # Setup environment
    setup_environment()
    
    # Change to project directory
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    # Start server
    try:
        # Method 1: Try using the main.py from the main src directory
        if (project_root / "src" / "interfaces" / "api" / "rest" / "app.py").exists():
            print("Starting server using main src API...")
            subprocess.run([
                sys.executable, "-m", "uvicorn", 
                "src.interfaces.api.rest.app:app",
                "--host", os.environ.get("API_HOST", "0.0.0.0"),
                "--port", os.environ.get("API_PORT", "8000"),
                "--reload" if os.environ.get("API_RELOAD", "false").lower() == "true" else "--no-reload",
                "--log-level", os.environ.get("API_LOG_LEVEL", "info")
            ], check=True)
        
        # Method 2: Try using v2/main.py
        elif (project_root / "v2" / "main.py").exists():
            print("Starting server using v2 main.py...")
            subprocess.run([
                sys.executable, str(project_root / "v2" / "main.py")
            ], check=True)
        
        else:
            print("❌ Could not find API application to start")
            print("Please ensure one of these exists:")
            print("  - src/interfaces/api/rest/app.py")
            print("  - v2/main.py")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")

if __name__ == "__main__":
    main()