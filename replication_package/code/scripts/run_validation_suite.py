#!/usr/bin/env python3
"""
V1/V2 Validation Suite CLI

Command-line interface for running comprehensive V1/V2 parallel validation.
"""

import asyncio
import argparse
import sys
import json
from pathlib import Path
from datetime import datetime
from typing import Optional

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.infrastructure.validation.validation_orchestrator import (
    ValidationOrchestrator, ValidationSuite, create_default_validation_suite, run_validation_suite
)
from src.infrastructure.validation.parallel_validation import ValidationConfiguration
from src.infrastructure.validation.performance_benchmarker import BenchmarkConfiguration
from src.infrastructure.validation.conflict_effect_validator import ConflictEffectConfiguration
from src.infrastructure.validation.validation_dashboard import DashboardConfiguration, create_validation_dashboard


def setup_argument_parser() -> argparse.ArgumentParser:
    """Set up command-line argument parser."""
    
    parser = argparse.ArgumentParser(
        description="V1/V2 Parallel Validation Suite for Yemen Market Integration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run full validation suite with defaults
  python run_validation_suite.py --v1-data-path /path/to/v1/data --v2-database-url ********************************/db

  # Run validation with custom thresholds
  python run_validation_suite.py --v1-data-path /path/to/v1/data --v2-database-url ********************************/db --min-performance-improvement 15 --min-accuracy 98

  # Run only performance benchmark
  python run_validation_suite.py --v1-data-path /path/to/v1/data --v2-database-url ********************************/db --benchmark-only

  # Launch interactive dashboard
  python run_validation_suite.py --dashboard-only

  # Generate validation report from existing results
  python run_validation_suite.py --generate-report /path/to/validation/results
        """
    )
    
    # Required arguments
    parser.add_argument(
        "--v1-data-path",
        type=Path,
        help="Path to V1 system data directory"
    )
    
    parser.add_argument(
        "--v2-database-url", 
        type=str,
        help="PostgreSQL connection URL for V2 system"
    )
    
    # Component selection
    component_group = parser.add_argument_group("Component Selection")
    component_group.add_argument(
        "--parallel-validation",
        action="store_true",
        default=True,
        help="Run parallel validation (default: True)"
    )
    
    component_group.add_argument(
        "--performance-benchmark",
        action="store_true", 
        default=True,
        help="Run performance benchmark (default: True)"
    )
    
    component_group.add_argument(
        "--conflict-validation",
        action="store_true",
        default=True,
        help="Run conflict effect validation (default: True)"
    )
    
    component_group.add_argument(
        "--benchmark-only",
        action="store_true",
        help="Run only performance benchmark"
    )
    
    component_group.add_argument(
        "--dashboard-only",
        action="store_true",
        help="Launch only the monitoring dashboard"
    )
    
    # Thresholds and criteria
    threshold_group = parser.add_argument_group("Validation Thresholds")
    threshold_group.add_argument(
        "--min-overall-score",
        type=float,
        default=90.0,
        help="Minimum overall score for go/no-go (default: 90.0)"
    )
    
    threshold_group.add_argument(
        "--min-performance-improvement",
        type=float,
        default=10.0,
        help="Minimum performance improvement factor (default: 10.0x)"
    )
    
    threshold_group.add_argument(
        "--min-accuracy",
        type=float,
        default=95.0,
        help="Minimum numerical accuracy percentage (default: 95.0%)"
    )
    
    threshold_group.add_argument(
        "--numerical-tolerance",
        type=float,
        default=0.001,
        help="Numerical comparison tolerance (default: 0.001)"
    )
    
    # Performance benchmark settings
    perf_group = parser.add_argument_group("Performance Benchmark Settings")
    perf_group.add_argument(
        "--test-iterations",
        type=int,
        default=3,
        help="Number of benchmark iterations (default: 3)"
    )
    
    perf_group.add_argument(
        "--test-data-sizes",
        type=int,
        nargs="+",
        default=[1000, 5000, 10000, 25000],
        help="Data sizes for testing (default: 1000 5000 10000 25000)"
    )
    
    perf_group.add_argument(
        "--enable-profiling",
        action="store_true",
        default=True,
        help="Enable detailed profiling (default: True)"
    )
    
    # Output settings
    output_group = parser.add_argument_group("Output Settings")
    output_group.add_argument(
        "--output-directory",
        type=Path,
        default=Path("./validation_results"),
        help="Output directory for results (default: ./validation_results)"
    )
    
    output_group.add_argument(
        "--generate-executive-summary",
        action="store_true",
        default=True,
        help="Generate executive summary (default: True)"
    )
    
    output_group.add_argument(
        "--save-raw-data",
        action="store_true",
        default=True,
        help="Save raw validation data (default: True)"
    )
    
    # Special modes
    special_group = parser.add_argument_group("Special Modes")
    special_group.add_argument(
        "--generate-report",
        type=Path,
        help="Generate report from existing validation results directory"
    )
    
    special_group.add_argument(
        "--dry-run",
        action="store_true",
        help="Validate configuration without running validation"
    )
    
    special_group.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )
    
    special_group.add_argument(
        "--quiet",
        action="store_true",
        help="Minimize output"
    )
    
    return parser


def create_validation_suite_from_args(args) -> ValidationSuite:
    """Create validation suite configuration from command-line arguments."""
    
    # Parallel validation config
    parallel_config = ValidationConfiguration(
        v1_data_path=args.v1_data_path,
        v2_database_url=args.v2_database_url,
        v1_analysis_path=args.v1_data_path.parent / "results" if args.v1_data_path else Path("./results"),
        output_path=args.output_directory / "parallel_validation",
        numerical_tolerance=args.numerical_tolerance,
        performance_improvement_target=args.min_performance_improvement
    )
    
    # Performance benchmark config
    benchmark_config = BenchmarkConfiguration(
        test_iterations=args.test_iterations,
        test_data_sizes=args.test_data_sizes,
        enable_profiling=args.enable_profiling
    )
    
    # Conflict effect config
    conflict_config = ConflictEffectConfiguration(
        coefficient_tolerance=0.05,
        effect_size_tolerance=0.02,
        expected_effect_magnitude=-0.35
    )
    
    # Dashboard config
    dashboard_config = DashboardConfiguration(
        refresh_interval_seconds=5,
        enable_alerts=True
    )
    
    # Determine which components to run
    if args.benchmark_only:
        run_parallel = False
        run_performance = True
        run_conflict = False
    else:
        run_parallel = args.parallel_validation
        run_performance = args.performance_benchmark
        run_conflict = args.conflict_validation
    
    return ValidationSuite(
        parallel_validation_config=parallel_config,
        performance_benchmark_config=benchmark_config,
        conflict_effect_config=conflict_config,
        dashboard_config=dashboard_config,
        run_parallel_validation=run_parallel,
        run_performance_benchmark=run_performance,
        run_conflict_validation=run_conflict,
        output_directory=args.output_directory,
        generate_executive_summary=args.generate_executive_summary,
        save_raw_data=args.save_raw_data,
        minimum_overall_score=args.min_overall_score,
        minimum_performance_improvement=args.min_performance_improvement,
        minimum_accuracy_score=args.min_accuracy,
        require_conflict_validation=run_conflict
    )


def print_banner():
    """Print application banner."""
    banner = """
    ╔══════════════════════════════════════════════════════════════════╗
    ║                 V1/V2 Validation Suite                          ║
    ║             Yemen Market Integration Project                     ║
    ║                                                                  ║
    ║  Comprehensive parallel validation for V1 and V2 systems        ║
    ║  ensuring feature parity, performance, and research accuracy    ║
    ╚══════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def print_configuration_summary(suite: ValidationSuite):
    """Print validation configuration summary."""
    print("\n📋 Validation Configuration Summary")
    print("=" * 50)
    print(f"Components to run:")
    print(f"  • Parallel Validation: {'✅' if suite.run_parallel_validation else '❌'}")
    print(f"  • Performance Benchmark: {'✅' if suite.run_performance_benchmark else '❌'}")
    print(f"  • Conflict Validation: {'✅' if suite.run_conflict_validation else '❌'}")
    
    print(f"\nGo/No-Go Criteria:")
    print(f"  • Minimum Overall Score: {suite.minimum_overall_score}%")
    print(f"  • Minimum Performance Improvement: {suite.minimum_performance_improvement}x")
    print(f"  • Minimum Accuracy Score: {suite.minimum_accuracy_score}%")
    print(f"  • Require Conflict Validation: {'Yes' if suite.require_conflict_validation else 'No'}")
    
    print(f"\nOutput Directory: {suite.output_directory}")
    print()


async def run_dashboard_mode():
    """Run in dashboard-only mode."""
    print("\n🖥️  Launching Validation Dashboard...")
    print("📊 Dashboard will be available at: http://localhost:8501")
    print("⏹️  Press Ctrl+C to stop the dashboard")
    
    try:
        # This would launch the Streamlit dashboard
        # For now, simulate dashboard mode
        print("\n✨ Dashboard is running...")
        print("   (This is a simulation - actual implementation would launch Streamlit)")
        
        # Simulate running dashboard
        await asyncio.sleep(5)
        print("\n⏹️  Dashboard simulation complete")
        
    except KeyboardInterrupt:
        print("\n⏹️  Dashboard stopped by user")


def generate_report_from_existing(results_path: Path):
    """Generate report from existing validation results."""
    print(f"\n📄 Generating report from: {results_path}")
    
    if not results_path.exists():
        print(f"❌ Error: Results directory not found: {results_path}")
        return
    
    # Look for validation summary files
    summary_files = list(results_path.glob("validation_summary_*.json"))
    
    if not summary_files:
        print(f"❌ Error: No validation summary files found in {results_path}")
        return
    
    # Use the most recent summary file
    latest_summary = max(summary_files, key=lambda f: f.stat().st_mtime)
    
    print(f"📊 Using summary file: {latest_summary.name}")
    
    try:
        with open(latest_summary, 'r') as f:
            summary_data = json.load(f)
        
        # Print summary
        print("\n" + "=" * 60)
        print("VALIDATION SUMMARY REPORT")
        print("=" * 60)
        print(f"Validation ID: {summary_data.get('validation_id', 'Unknown')}")
        print(f"Timestamp: {summary_data.get('timestamp', 'Unknown')}")
        print(f"Overall Score: {summary_data.get('overall_score', 0):.1f}%")
        print(f"Performance Improvement: {summary_data.get('performance_improvement_factor', 0):.1f}x")
        print(f"Numerical Accuracy: {summary_data.get('numerical_accuracy_percentage', 0):.1f}%")
        print(f"Conflict Finding Validated: {'Yes' if summary_data.get('conflict_finding_validated', False) else 'No'}")
        print(f"Go/No-Go Decision: {'PASS' if summary_data.get('passes_go_no_go', False) else 'FAIL'}")
        
        if summary_data.get('critical_blockers'):
            print("\n❌ Critical Blockers:")
            for blocker in summary_data['critical_blockers']:
                print(f"  • {blocker}")
        
        if summary_data.get('recommendations'):
            print("\n💡 Recommendations:")
            for rec in summary_data['recommendations']:
                print(f"  • {rec}")
        
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Error reading summary file: {e}")


async def main():
    """Main CLI entry point."""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # Handle special modes
    if args.dashboard_only:
        await run_dashboard_mode()
        return
    
    if args.generate_report:
        generate_report_from_existing(args.generate_report)
        return
    
    # Print banner
    if not args.quiet:
        print_banner()
    
    # Validate required arguments
    if not args.v1_data_path or not args.v2_database_url:
        print("❌ Error: --v1-data-path and --v2-database-url are required")
        parser.print_help()
        sys.exit(1)
    
    # Validate paths
    if not args.v1_data_path.exists():
        print(f"❌ Error: V1 data path does not exist: {args.v1_data_path}")
        sys.exit(1)
    
    # Create validation suite configuration
    try:
        suite = create_validation_suite_from_args(args)
    except Exception as e:
        print(f"❌ Error creating validation configuration: {e}")
        sys.exit(1)
    
    # Print configuration summary
    if not args.quiet:
        print_configuration_summary(suite)
    
    # Dry run mode
    if args.dry_run:
        print("✅ Configuration validated successfully")
        print("🔧 Dry run mode - validation not executed")
        return
    
    # Run validation suite
    try:
        print("🚀 Starting validation suite execution...")
        start_time = datetime.now()
        
        summary = await run_validation_suite(suite)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Print results
        print("\n" + "=" * 60)
        print("VALIDATION SUITE RESULTS")
        print("=" * 60)
        print(f"Validation ID: {summary.validation_id}")
        print(f"Duration: {duration:.1f} seconds")
        print(f"Overall Score: {summary.overall_score:.1f}%")
        print(f"Performance Improvement: {summary.performance_improvement_factor:.1f}x")
        print(f"Numerical Accuracy: {summary.numerical_accuracy_percentage:.1f}%")
        print(f"Conflict Finding Validated: {'Yes' if summary.conflict_finding_validated else 'No'}")
        print(f"Go/No-Go Decision: {'✅ PASS' if summary.passes_go_no_go else '❌ FAIL'}")
        
        if summary.critical_blockers:
            print("\n❌ Critical Blockers:")
            for blocker in summary.critical_blockers:
                print(f"  • {blocker}")
        
        if summary.warnings:
            print("\n⚠️  Warnings:")
            for warning in summary.warnings:
                print(f"  • {warning}")
        
        if summary.recommendations:
            print("\n💡 Recommendations:")
            for rec in summary.recommendations:
                print(f"  • {rec}")
        
        print(f"\n📄 Detailed reports saved to: {suite.output_directory}")
        print("=" * 60)
        
        # Exit with appropriate code
        sys.exit(0 if summary.passes_go_no_go else 1)
        
    except KeyboardInterrupt:
        print("\n⏹️  Validation interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ Validation failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())