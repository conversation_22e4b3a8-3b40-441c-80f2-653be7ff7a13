"""
Yemen Market Integration V1 Compatibility Module

This module provides backward compatibility for V1 imports while 
redirecting to the new V2 Clean Architecture structure.
"""

# Import V1 compatibility components
try:
    from .v1_compat import (
        setup_logging, info, error, warning, debug, timer, bind,
        PanelBuilder, ThreeTierAnalysis, ModelMigrationHelper,
        prepare_data_for_modeling
    )
except ImportError:
    # Fallback for direct module execution
    from v1_compat import (
        setup_logging, info, error, warning, debug, timer, bind,
        PanelBuilder, ThreeTierAnalysis, ModelMigrationHelper,
        prepare_data_for_modeling
    )

# Create submodules for backward compatibility
class _CompatModule:
    """Helper class to create module-like objects for backward compatibility."""
    
    def __init__(self, name, components):
        self.__name__ = name
        for key, value in components.items():
            setattr(self, key, value)

# Analysis submodule
analysis = _CompatModule('yemen_market.analysis', {
    'ThreeTierAnalysis': ThreeTierAnalysis,
    'prepare_data_for_modeling': prepare_data_for_modeling,
})

# Data submodule  
data = _CompatModule('yemen_market.data', {
    'PanelBuilder': PanelBuilder,
})

# Utils submodule
utils = _CompatModule('yemen_market.utils', {
    'setup_logging': setup_logging,
    'info': info,
    'error': error, 
    'warning': warning,
    'debug': debug,
    'timer': timer,
    'bind': bind,
})

# Models submodule (placeholder)
models = _CompatModule('yemen_market.models', {
    'ModelMigrationHelper': ModelMigrationHelper,
})

# Features submodule (placeholder)
features = _CompatModule('yemen_market.features', {
    'prepare_data_for_modeling': prepare_data_for_modeling,
})

# Export main components at module level for direct imports
__all__ = [
    'analysis', 'data', 'utils', 'models', 'features',
    'setup_logging', 'info', 'error', 'warning', 'debug', 
    'timer', 'bind', 'PanelBuilder', 'ThreeTierAnalysis',
    'ModelMigrationHelper', 'prepare_data_for_modeling'
]