"""Plugin interfaces and base classes."""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Type

from ...core.models.interfaces import Model, Estimator


class DataProcessor(ABC):
    """Base interface for data processors."""
    
    @abstractmethod
    async def process(self, *args, **kwargs):
        """Process data and return results."""
        pass


@dataclass
class PluginMetadata:
    """Plugin metadata."""
    
    name: str
    version: str
    author: str
    description: str
    dependencies: List[str] = None
    config_schema: Dict[str, Any] = None
    
    def __post_init__(self):
        """Initialize with defaults."""
        if self.dependencies is None:
            self.dependencies = []
        if self.config_schema is None:
            self.config_schema = {}


class Plugin(ABC):
    """Base plugin interface."""
    
    @property
    @abstractmethod
    def metadata(self) -> PluginMetadata:
        """Get plugin metadata."""
        pass
    
    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize plugin with configuration."""
        pass
    
    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate plugin configuration."""
        pass
    
    def on_load(self) -> None:
        """Called when plugin is loaded."""
        pass
    
    def on_unload(self) -> None:
        """Called when plugin is unloaded."""
        pass


class ModelPlugin(Plugin):
    """Plugin interface for econometric models."""
    
    @abstractmethod
    def get_model_class(self) -> Type[Model]:
        """Get the model class."""
        pass
    
    @abstractmethod
    def get_estimator_class(self) -> Type[Estimator]:
        """Get the estimator class."""
        pass
    
    @abstractmethod
    def get_supported_features(self) -> List[str]:
        """Get list of supported features/capabilities."""
        pass
    
    def preprocess_data(self, data: Any) -> Any:
        """Preprocess data before model estimation."""
        return data
    
    def postprocess_results(self, results: Any) -> Any:
        """Postprocess results after estimation."""
        return results


class DataSourcePlugin(Plugin):
    """Plugin interface for data sources."""
    
    @abstractmethod
    async def fetch_data(
        self,
        query: Dict[str, Any],
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Fetch data from the source."""
        pass
    
    @abstractmethod
    def get_supported_data_types(self) -> List[str]:
        """Get list of supported data types."""
        pass
    
    @abstractmethod
    def validate_query(self, query: Dict[str, Any]) -> bool:
        """Validate query parameters."""
        pass
    
    def transform_data(self, raw_data: List[Dict[str, Any]]) -> Any:
        """Transform raw data to standard format."""
        return raw_data


class OutputPlugin(Plugin):
    """Plugin interface for output formats."""
    
    @abstractmethod
    def export(
        self,
        data: Any,
        output_path: str,
        options: Dict[str, Any] = None
    ) -> None:
        """Export data to the specified format."""
        pass
    
    @abstractmethod
    def get_file_extension(self) -> str:
        """Get the file extension for this format."""
        pass
    
    @abstractmethod
    def get_mime_type(self) -> str:
        """Get the MIME type for this format."""
        pass
    
    def validate_data(self, data: Any) -> bool:
        """Validate data before export."""
        return True