"""Plugin manager for discovery and loading."""

import importlib
import inspect
import sys
from pathlib import Path
from typing import Dict, List, Optional, Type

from .interfaces import Plugin, ModelPlugin, DataSourcePlugin, OutputPlugin
from .registry import PluginRegistry


class PluginManager:
    """Manages plugin discovery, loading, and lifecycle."""
    
    def __init__(self, plugin_dirs: List[Path] = None):
        """Initialize plugin manager."""
        self.plugin_dirs = plugin_dirs or [Path("plugins")]
        self.registry = PluginRegistry()
        self._loaded_modules = {}
    
    def discover_plugins(self) -> Dict[str, List[str]]:
        """Discover all available plugins."""
        discovered = {
            "models": [],
            "data_sources": [],
            "outputs": []
        }
        
        for plugin_dir in self.plugin_dirs:
            if not plugin_dir.exists():
                continue
            
            # Check each plugin type directory
            for plugin_type in ["models", "data_sources", "outputs"]:
                type_dir = plugin_dir / plugin_type
                if not type_dir.exists():
                    continue
                
                # Find plugin modules
                for item in type_dir.iterdir():
                    if item.is_dir() and (item / "__init__.py").exists():
                        plugin_name = item.name
                        if self._is_valid_plugin(item):
                            discovered[plugin_type].append(plugin_name)
        
        return discovered
    
    def load_plugin(self, plugin_type: str, plugin_name: str) -> Plugin:
        """Load a specific plugin."""
        # Check if already loaded
        plugin_key = f"{plugin_type}.{plugin_name}"
        if self.registry.is_registered(plugin_type, plugin_name):
            return self.registry.get_plugin(plugin_type, plugin_name)
        
        # Find plugin directory
        plugin_path = None
        for plugin_dir in self.plugin_dirs:
            candidate = plugin_dir / plugin_type / plugin_name
            if candidate.exists() and (candidate / "__init__.py").exists():
                plugin_path = candidate
                break
        
        if not plugin_path:
            raise ValueError(f"Plugin not found: {plugin_type}/{plugin_name}")
        
        # Load plugin module
        module = self._load_module(plugin_path, plugin_key)
        
        # Find plugin class
        plugin_class = self._find_plugin_class(module, plugin_type)
        if not plugin_class:
            raise ValueError(f"No valid plugin class found in {plugin_key}")
        
        # Instantiate plugin
        plugin_instance = plugin_class()
        
        # Register plugin
        self.registry.register(plugin_type, plugin_name, plugin_instance)
        
        # Call lifecycle method
        plugin_instance.on_load()
        
        return plugin_instance
    
    def load_all_plugins(self) -> Dict[str, int]:
        """Load all discovered plugins."""
        discovered = self.discover_plugins()
        loaded_count = {
            "models": 0,
            "data_sources": 0,
            "outputs": 0
        }
        
        for plugin_type, plugin_names in discovered.items():
            for plugin_name in plugin_names:
                try:
                    self.load_plugin(plugin_type, plugin_name)
                    loaded_count[plugin_type] += 1
                except Exception as e:
                    print(f"Failed to load plugin {plugin_type}/{plugin_name}: {e}")
        
        return loaded_count
    
    def unload_plugin(self, plugin_type: str, plugin_name: str) -> None:
        """Unload a plugin."""
        plugin = self.registry.get_plugin(plugin_type, plugin_name)
        if plugin:
            # Call lifecycle method
            plugin.on_unload()
            
            # Unregister
            self.registry.unregister(plugin_type, plugin_name)
            
            # Remove module from cache
            plugin_key = f"{plugin_type}.{plugin_name}"
            if plugin_key in self._loaded_modules:
                module_name = self._loaded_modules[plugin_key]
                if module_name in sys.modules:
                    del sys.modules[module_name]
                del self._loaded_modules[plugin_key]
    
    def get_plugin(self, plugin_type: str, plugin_name: str) -> Optional[Plugin]:
        """Get a loaded plugin."""
        return self.registry.get_plugin(plugin_type, plugin_name)
    
    def list_plugins(self, plugin_type: Optional[str] = None) -> Dict[str, List[str]]:
        """List all registered plugins."""
        return self.registry.list_plugins(plugin_type)
    
    def _is_valid_plugin(self, plugin_dir: Path) -> bool:
        """Check if directory contains a valid plugin."""
        # Must have __init__.py
        if not (plugin_dir / "__init__.py").exists():
            return False
        
        # Should have plugin.py or main.py
        return (plugin_dir / "plugin.py").exists() or (plugin_dir / "main.py").exists()
    
    def _load_module(self, plugin_path: Path, plugin_key: str) -> object:
        """Load a plugin module."""
        # Create module name
        module_name = f"plugins.{plugin_key.replace('.', '_')}"
        
        # Add plugin directory to path temporarily
        sys.path.insert(0, str(plugin_path.parent.parent))
        
        try:
            # Import module
            if (plugin_path / "plugin.py").exists():
                spec = importlib.util.spec_from_file_location(
                    module_name,
                    plugin_path / "plugin.py"
                )
            else:
                spec = importlib.util.spec_from_file_location(
                    module_name,
                    plugin_path / "main.py"
                )
            
            module = importlib.util.module_from_spec(spec)
            sys.modules[module_name] = module
            spec.loader.exec_module(module)
            
            self._loaded_modules[plugin_key] = module_name
            
            return module
            
        finally:
            # Remove from path
            sys.path.pop(0)
    
    def _find_plugin_class(self, module: object, plugin_type: str) -> Optional[Type[Plugin]]:
        """Find the plugin class in a module."""
        # Determine expected base class
        if plugin_type == "models":
            base_class = ModelPlugin
        elif plugin_type == "data_sources":
            base_class = DataSourcePlugin
        elif plugin_type == "outputs":
            base_class = OutputPlugin
        else:
            base_class = Plugin
        
        # Find classes that inherit from base class
        for name, obj in inspect.getmembers(module):
            if (inspect.isclass(obj) and 
                issubclass(obj, base_class) and 
                obj is not base_class and
                not inspect.isabstract(obj)):
                return obj
        
        return None