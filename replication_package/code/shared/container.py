"""Dependency injection container configuration."""

from pathlib import Path

# Try to import dependency_injector, fall back to simple container if not available
try:
    from dependency_injector import containers, providers
    DEPENDENCY_INJECTOR_AVAILABLE = True
except ImportError:
    DEPENDENCY_INJECTOR_AVAILABLE = False
    # Fall back to simple container
    from .container_simple import SimpleContainer
    
    class Container(SimpleContainer):
        """Fallback container when dependency_injector is not available."""
        pass

# Skip application layer imports during container module import
# These will be imported lazily when needed
AnalyzeMarketIntegrationHandler = None
AnalysisOrchestrator = DataPreparationService = PolicyDataAdapter = None
PolicyOrchestrator = ModelEstimatorService = ThreeTierAnalysisService = None

# Skip domain services during container import - they may have pandas dependencies
MarketIntegrationService = PriceTransmissionService = None

# Skip all problematic imports during container module import
# These will be imported lazily when actually needed to avoid pandas/numpy issues
RedisCache = MemoryCache = None
HDXClient = WFPClient = ACLEDClient = None
WFPProcessor = ACLEDProcessor = ACAPSProcessor = PanelBuilder = None
InMemoryEventBus = AsyncEventBus = None
PostgresUnitOfWork = None
PolicyResultsRepository = None
JWTHandler = PasswordHandler = RBACManager = APIKeyManager = RateLimiter = None
AuthenticationService = UserService = UserRepository = None
APIKeyRepository = RefreshTokenRepository = None
PluginManager = None


# Only define the full container if dependency_injector is available
if DEPENDENCY_INJECTOR_AVAILABLE:
    class Container(containers.DeclarativeContainer):
        """Dependency injection container."""
        
        # Configuration
        config = providers.Configuration()
        
        # Infrastructure - Database (conditional)
        unit_of_work = providers.Factory(
            lambda connection_string: None if PostgresUnitOfWork is None else PostgresUnitOfWork(connection_string),
            connection_string=config.database.url
        ) if PostgresUnitOfWork else providers.Factory(lambda: None)
        
        # Infrastructure - Caching (conditional)
        cache = providers.Selector(
            config.cache.type,
            redis=providers.Singleton(
                lambda redis_url, default_ttl: None if RedisCache is None else RedisCache(redis_url, default_ttl),
                redis_url=config.cache.redis.url,
                default_ttl=config.cache.default_ttl
            ) if RedisCache else providers.Singleton(lambda: None),
            memory=providers.Singleton(
                lambda default_ttl, max_size: None if MemoryCache is None else MemoryCache(default_ttl, max_size),
                default_ttl=config.cache.default_ttl,
                max_size=config.cache.memory.max_size
            ) if MemoryCache else providers.Singleton(lambda: None)
            )
        
        # Infrastructure - Event Bus (conditional)
        event_bus = providers.Selector(
            config.events.type,
            inmemory=providers.Singleton(
                lambda: None if InMemoryEventBus is None else InMemoryEventBus()
            ) if InMemoryEventBus else providers.Singleton(lambda: None),
            asynchronous=providers.Singleton(
                lambda max_queue_size: None if AsyncEventBus is None else AsyncEventBus(max_queue_size),
                max_queue_size=config.events.queue_size
            ) if AsyncEventBus else providers.Singleton(lambda: None)
            )
        
        # Infrastructure - External Services (conditional)
        hdx_client = providers.Singleton(
            lambda timeout: None if HDXClient is None else HDXClient(timeout=timeout),
            timeout=config.external.hdx.timeout
            ) if HDXClient else providers.Singleton(lambda: None)
        
        wfp_client = providers.Singleton(
            lambda api_key: None if WFPClient is None else WFPClient(api_key=api_key),
            api_key=config.external.wfp.api_key
            ) if WFPClient else providers.Singleton(lambda: None)
        
        acled_client = providers.Singleton(
            lambda api_key, email: None if ACLEDClient is None else ACLEDClient(api_key=api_key, email=email),
            api_key=config.external.acled.api_key,
            email=config.external.acled.email
        ) if ACLEDClient else providers.Singleton(lambda: None)
        
        # Infrastructure - Plugin System (conditional)
        plugin_manager = providers.Singleton(
            lambda plugin_dirs: None if PluginManager is None else PluginManager(plugin_dirs=plugin_dirs),
            plugin_dirs=providers.List(
                Path("plugins"),
                Path.home() / ".yemen_market/plugins"
            )
            ) if PluginManager else providers.Singleton(lambda: None)
        
        # Domain Services
        price_transmission_service = providers.Factory(
            PriceTransmissionService
            )
        
        market_integration_service = providers.Factory(
            MarketIntegrationService,
            transmission_service=price_transmission_service
            )
        
        # Application Services
        data_preparation_service = providers.Factory(
            DataPreparationService,
            wfp_client=wfp_client,
            hdx_client=hdx_client,
            plugin_manager=plugin_manager
            )
        
        analysis_orchestrator = providers.Factory(
            AnalysisOrchestrator,
            price_repo=providers.Callable(
                lambda uow: uow.prices,
                unit_of_work
            ),
            transmission_service=price_transmission_service,
            integration_service=market_integration_service,
            event_bus=event_bus,
            cache=cache
        )
        
        # Application Services - Model Estimation
        model_estimator_service = providers.Factory(
            ModelEstimatorService,
            cache=cache
        )
        
        # Application Services - Three-Tier Analysis
        three_tier_analysis_service = providers.Factory(
            ThreeTierAnalysisService,
            market_repository=providers.Callable(
                lambda uow: uow.markets,
                unit_of_work
            ),
            price_repository=providers.Callable(
                lambda uow: uow.prices,
                unit_of_work
            ),
            estimator_service=model_estimator_service,
            event_bus=event_bus,
            orchestrator=analysis_orchestrator
        )
        
        # Application Services - Policy
        policy_data_adapter = providers.Factory(
            PolicyDataAdapter,
            market_repo=providers.Callable(
                lambda uow: uow.markets,
                unit_of_work
            ),
            price_repo=providers.Callable(
                lambda uow: uow.prices,
                unit_of_work
            )
        )
        
        policy_orchestrator = providers.Factory(
            PolicyOrchestrator,
            data_adapter=policy_data_adapter,
            analysis_orchestrator=analysis_orchestrator,
            event_bus=event_bus
        )
        
        # Infrastructure - Policy Results Storage
        policy_repository = providers.Singleton(
            PolicyResultsRepository,
            storage_path=config.storage.policy_results_path
        )
        
        # Infrastructure - Data Processors (for policy endpoints)
        wfp_processor = providers.Factory(
            WFPProcessor,
            wfp_client=wfp_client
        )
        
        acled_processor = providers.Factory(
            ACLEDProcessor,
            acled_client=acled_client
        )
        
        acaps_processor = providers.Factory(
            ACAPSProcessor
        )
        
        panel_builder = providers.Factory(
            PanelBuilder
        )
        
        # Command Handlers
        analyze_market_integration_handler = providers.Factory(
            AnalyzeMarketIntegrationHandler,
            uow=unit_of_work,
            market_repo=providers.Callable(
                lambda uow: uow.markets,
                unit_of_work
            ),
            price_repo=providers.Callable(
                lambda uow: uow.prices,
                unit_of_work
            ),
            integration_service=market_integration_service,
            orchestrator=analysis_orchestrator,
            event_bus=event_bus
        )
        
        # Security Infrastructure
        jwt_handler = providers.Singleton(
            JWTHandler,
            config=None  # Uses environment variables
        )
        
        password_handler = providers.Singleton(
            PasswordHandler,
            rounds=12
        )
        
        rbac_manager = providers.Singleton(
            RBACManager
        )
        
        api_key_manager = providers.Singleton(
            APIKeyManager,
            cache=cache
        )
        
        rate_limiter = providers.Singleton(
            RateLimiter,
            cache=cache
        )
        
        # Auth Domain - Repositories (placeholders for now)
        user_repository = providers.Factory(
            lambda: None  # Would be actual UserRepository implementation
        )
        
        api_key_repository = providers.Factory(
            lambda: None  # Would be actual APIKeyRepository implementation
        )
        
        refresh_token_repository = providers.Factory(
            lambda: None  # Would be actual RefreshTokenRepository implementation
        )
        
        # Auth Domain - Services
        auth_service = providers.Factory(
            AuthenticationService,
            user_repository=user_repository,
            refresh_token_repository=refresh_token_repository
        )
        
        user_service = providers.Factory(
            UserService,
            user_repository=user_repository,
            api_key_repository=api_key_repository
        )
        
        # Dependencies - Dependency moved to avoid circular import
        # get_current_user will be configured separately to avoid circular dependency
else:
    # Container is already defined above as SimpleContainer fallback
    pass