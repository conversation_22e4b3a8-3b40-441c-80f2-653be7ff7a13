"""
Data Quality Framework for Yemen Market Integration.

Implements comprehensive data quality checks for prices, exchange rates,
and market data to ensure analysis reliability.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
from scipy import stats
import warnings

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class DataAnomaly:
    """Represents a detected data quality issue."""
    anomaly_type: str
    severity: str  # 'critical', 'warning', 'info'
    affected_data: str
    timestamp: datetime
    description: str
    suggested_action: str
    confidence: float


@dataclass
class QualityReport:
    """Comprehensive data quality assessment report."""
    check_type: str
    passed: bool
    score: float  # 0-100
    n_issues: int
    anomalies: List[DataAnomaly]
    summary: Dict[str, Union[int, float]]
    recommendations: List[str]


@dataclass
class Price:
    """Price data point."""
    commodity: str
    market: str
    price: float
    currency: str
    date: datetime
    source: str


@dataclass
class ExchangeRate:
    """Exchange rate data point."""
    zone: str
    rate: float
    date: datetime
    source: str
    confidence: float


@dataclass
class MarketPair:
    """Market pair for arbitrage analysis."""
    origin: str
    destination: str
    commodity: str
    distance_km: float
    transport_cost: float


class DataQualityFramework:
    """
    Comprehensive data quality monitoring for Yemen market data.
    
    Implements quality checks specific to conflict-affected markets
    with currency fragmentation.
    """
    
    def __init__(self,
                 price_bounds: Optional[Dict[str, Tuple[float, float]]] = None,
                 exchange_bounds: Optional[Dict[str, Tuple[float, float]]] = None,
                 anomaly_threshold: float = 0.95):
        """
        Initialize data quality framework.
        
        Args:
            price_bounds: Min/max prices by commodity
            exchange_bounds: Min/max exchange rates by zone
            anomaly_threshold: Confidence threshold for anomaly detection
        """
        self.anomaly_threshold = anomaly_threshold
        
        # Default price bounds (YER per unit)
        self.price_bounds = price_bounds or {
            'wheat_flour': (300, 3000),
            'rice': (400, 4000),
            'sugar': (350, 3500),
            'oil': (1000, 10000),
            'fuel_petrol': (500, 5000),
            'fuel_diesel': (400, 4000)
        }
        
        # Default exchange rate bounds
        self.exchange_bounds = exchange_bounds or {
            'houthi': (400, 700),      # Relatively stable
            'government': (800, 3000),  # High volatility
            'disputed': (600, 2000)     # Mixed control
        }
        
        # Statistical parameters
        self.outlier_std_threshold = 3.5  # Standard deviations for outlier
        self.min_temporal_correlation = 0.7  # Minimum autocorrelation
        self.max_price_change_pct = 0.5  # 50% max daily change
    
    def check_price_reasonableness(self, prices: List[Price]) -> QualityReport:
        """
        Check if prices fall within reasonable bounds.
        
        Args:
            prices: List of price observations
            
        Returns:
            Quality report with anomalies
        """
        logger.info(f"Checking reasonableness of {len(prices)} prices")
        
        anomalies = []
        issues_by_commodity = {}
        
        for price_obs in prices:
            commodity = price_obs.commodity
            price_val = price_obs.price
            
            # Get bounds
            if commodity in self.price_bounds:
                min_price, max_price = self.price_bounds[commodity]
            else:
                # Use general bounds
                min_price, max_price = 100, 10000
            
            # Check bounds
            if price_val < min_price:
                anomalies.append(DataAnomaly(
                    anomaly_type='price_too_low',
                    severity='warning' if price_val > min_price * 0.5 else 'critical',
                    affected_data=f"{commodity} at {price_obs.market}",
                    timestamp=price_obs.date,
                    description=f"Price {price_val} below minimum {min_price}",
                    suggested_action="Verify data source and check for currency errors",
                    confidence=0.9
                ))
                issues_by_commodity[commodity] = issues_by_commodity.get(commodity, 0) + 1
                
            elif price_val > max_price:
                anomalies.append(DataAnomaly(
                    anomaly_type='price_too_high',
                    severity='warning' if price_val < max_price * 1.5 else 'critical',
                    affected_data=f"{commodity} at {price_obs.market}",
                    timestamp=price_obs.date,
                    description=f"Price {price_val} exceeds maximum {max_price}",
                    suggested_action="Check for data entry errors or extreme market conditions",
                    confidence=0.9
                ))
                issues_by_commodity[commodity] = issues_by_commodity.get(commodity, 0) + 1
            
            # Check for zero/negative prices
            if price_val <= 0:
                anomalies.append(DataAnomaly(
                    anomaly_type='invalid_price',
                    severity='critical',
                    affected_data=f"{commodity} at {price_obs.market}",
                    timestamp=price_obs.date,
                    description=f"Invalid price value: {price_val}",
                    suggested_action="Remove or impute this observation",
                    confidence=1.0
                ))
                issues_by_commodity[commodity] = issues_by_commodity.get(commodity, 0) + 1
        
        # Statistical outlier detection
        price_df = pd.DataFrame([{
            'commodity': p.commodity,
            'market': p.market,
            'price': p.price,
            'date': p.date
        } for p in prices])
        
        if not price_df.empty:
            for commodity in price_df['commodity'].unique():
                commodity_prices = price_df[price_df['commodity'] == commodity]['price']
                
                if len(commodity_prices) > 10:
                    # Z-score outliers
                    z_scores = np.abs(stats.zscore(commodity_prices))
                    outliers = price_df[price_df['commodity'] == commodity][z_scores > self.outlier_std_threshold]
                    
                    for _, outlier in outliers.iterrows():
                        anomalies.append(DataAnomaly(
                            anomaly_type='statistical_outlier',
                            severity='warning',
                            affected_data=f"{commodity} at {outlier['market']}",
                            timestamp=outlier['date'],
                            description=f"Price {outlier['price']} is statistical outlier",
                            suggested_action="Verify with alternative data sources",
                            confidence=0.8
                        ))
        
        # Calculate quality score
        n_issues = len(anomalies)
        n_critical = sum(1 for a in anomalies if a.severity == 'critical')
        score = max(0, 100 - n_critical * 20 - (n_issues - n_critical) * 5)
        
        # Recommendations
        recommendations = []
        if n_critical > 0:
            recommendations.append("Address critical price anomalies before analysis")
        if len(issues_by_commodity) > 2:
            recommendations.append("Multiple commodities show price issues - check data pipeline")
        if score < 80:
            recommendations.append("Consider additional data validation before proceeding")
        
        return QualityReport(
            check_type='price_reasonableness',
            passed=score >= 70,
            score=score,
            n_issues=n_issues,
            anomalies=anomalies,
            summary={
                'total_prices': len(prices),
                'anomaly_rate': n_issues / len(prices) if prices else 0,
                'commodities_affected': len(issues_by_commodity),
                'critical_issues': n_critical
            },
            recommendations=recommendations
        )
    
    def check_exchange_rate_bounds(self, rates: List[ExchangeRate]) -> QualityReport:
        """
        Check if exchange rates are within expected bounds.
        
        Args:
            rates: List of exchange rate observations
            
        Returns:
            Quality report
        """
        logger.info(f"Checking {len(rates)} exchange rates")
        
        anomalies = []
        issues_by_zone = {}
        
        for rate_obs in rates:
            zone = rate_obs.zone
            rate = rate_obs.rate
            
            # Get zone-specific bounds
            if zone in self.exchange_bounds:
                min_rate, max_rate = self.exchange_bounds[zone]
            else:
                min_rate, max_rate = 300, 3000  # General bounds
            
            # Check bounds
            if rate < min_rate:
                anomalies.append(DataAnomaly(
                    anomaly_type='exchange_rate_too_low',
                    severity='critical',
                    affected_data=f"{zone} zone",
                    timestamp=rate_obs.date,
                    description=f"Rate {rate} below minimum {min_rate}",
                    suggested_action="Verify source - possible data error",
                    confidence=0.95
                ))
                issues_by_zone[zone] = issues_by_zone.get(zone, 0) + 1
                
            elif rate > max_rate:
                severity = 'warning' if zone == 'government' else 'critical'
                anomalies.append(DataAnomaly(
                    anomaly_type='exchange_rate_too_high',
                    severity=severity,
                    affected_data=f"{zone} zone",
                    timestamp=rate_obs.date,
                    description=f"Rate {rate} exceeds maximum {max_rate}",
                    suggested_action="Check for currency crisis or data error",
                    confidence=0.9
                ))
                issues_by_zone[zone] = issues_by_zone.get(zone, 0) + 1
            
            # Check confidence scores
            if rate_obs.confidence < 0.5:
                anomalies.append(DataAnomaly(
                    anomaly_type='low_confidence_rate',
                    severity='warning',
                    affected_data=f"{zone} zone from {rate_obs.source}",
                    timestamp=rate_obs.date,
                    description=f"Low confidence score: {rate_obs.confidence}",
                    suggested_action="Corroborate with additional sources",
                    confidence=0.7
                ))
        
        # Check for impossible spreads
        if len(rates) > 1:
            rate_df = pd.DataFrame([{
                'zone': r.zone,
                'rate': r.rate,
                'date': r.date
            } for r in rates])
            
            # Group by date
            for date, date_rates in rate_df.groupby('date'):
                if len(date_rates) > 1:
                    max_spread = date_rates['rate'].max() / date_rates['rate'].min()
                    
                    if max_spread > 5:  # More than 5x difference
                        anomalies.append(DataAnomaly(
                            anomaly_type='excessive_spread',
                            severity='critical',
                            affected_data='Inter-zone spread',
                            timestamp=date,
                            description=f"Exchange rate spread {max_spread:.1f}x exceeds reasonable bounds",
                            suggested_action="Investigate market fragmentation or data errors",
                            confidence=0.85
                        ))
        
        # Calculate score
        n_issues = len(anomalies)
        n_critical = sum(1 for a in anomalies if a.severity == 'critical')
        score = max(0, 100 - n_critical * 25 - (n_issues - n_critical) * 10)
        
        recommendations = []
        if n_critical > 0:
            recommendations.append("Critical exchange rate anomalies detected - verify data sources")
        if 'government' in issues_by_zone and issues_by_zone['government'] > 5:
            recommendations.append("High volatility in government zone - increase monitoring frequency")
        
        return QualityReport(
            check_type='exchange_rate_bounds',
            passed=score >= 60,
            score=score,
            n_issues=n_issues,
            anomalies=anomalies,
            summary={
                'total_rates': len(rates),
                'zones_checked': len(set(r.zone for r in rates)),
                'anomaly_rate': n_issues / len(rates) if rates else 0,
                'avg_confidence': np.mean([r.confidence for r in rates]) if rates else 0
            },
            recommendations=recommendations
        )
    
    def check_temporal_consistency(self, time_series: pd.Series) -> QualityReport:
        """
        Check temporal consistency of time series data.
        
        Args:
            time_series: Pandas series with datetime index
            
        Returns:
            Quality report
        """
        logger.info(f"Checking temporal consistency of series with {len(time_series)} observations")
        
        anomalies = []
        
        if len(time_series) < 2:
            return QualityReport(
                check_type='temporal_consistency',
                passed=False,
                score=0,
                n_issues=1,
                anomalies=[DataAnomaly(
                    anomaly_type='insufficient_data',
                    severity='critical',
                    affected_data='time_series',
                    timestamp=datetime.now(),
                    description="Insufficient data for temporal analysis",
                    suggested_action="Collect more time periods",
                    confidence=1.0
                )],
                summary={'n_obs': len(time_series)},
                recommendations=["Need at least 2 observations for temporal analysis"]
            )
        
        # Check for gaps
        if isinstance(time_series.index, pd.DatetimeIndex):
            date_diffs = time_series.index.to_series().diff()
            median_gap = date_diffs.median()
            
            # Identify large gaps
            large_gaps = date_diffs[date_diffs > median_gap * 3]
            
            for idx, gap in large_gaps.items():
                anomalies.append(DataAnomaly(
                    anomaly_type='temporal_gap',
                    severity='warning',
                    affected_data='time_series',
                    timestamp=idx,
                    description=f"Gap of {gap.days} days detected",
                    suggested_action="Check for missing data or reporting interruptions",
                    confidence=0.9
                ))
        
        # Check for sudden changes
        pct_changes = time_series.pct_change().dropna()
        
        for idx, change in pct_changes.items():
            if abs(change) > self.max_price_change_pct:
                anomalies.append(DataAnomaly(
                    anomaly_type='sudden_change',
                    severity='warning' if abs(change) < 1 else 'critical',
                    affected_data='time_series',
                    timestamp=idx,
                    description=f"Change of {change*100:.1f}% detected",
                    suggested_action="Verify with news/events for this date",
                    confidence=0.8
                ))
        
        # Check autocorrelation
        if len(time_series) > 10:
            autocorr = time_series.autocorr(lag=1)
            
            if autocorr < self.min_temporal_correlation:
                anomalies.append(DataAnomaly(
                    anomaly_type='low_autocorrelation',
                    severity='warning',
                    affected_data='time_series',
                    timestamp=time_series.index[-1],
                    description=f"Low autocorrelation {autocorr:.2f} suggests noisy data",
                    suggested_action="Check data collection methodology",
                    confidence=0.7
                ))
        
        # Check for constant values
        if time_series.std() < 0.01 * time_series.mean():
            anomalies.append(DataAnomaly(
                anomaly_type='constant_values',
                severity='critical',
                affected_data='time_series',
                timestamp=time_series.index[-1],
                description="Series shows no variation",
                suggested_action="Verify data is being updated correctly",
                confidence=0.95
            ))
        
        # Calculate score
        n_issues = len(anomalies)
        n_critical = sum(1 for a in anomalies if a.severity == 'critical')
        score = max(0, 100 - n_critical * 30 - (n_issues - n_critical) * 10)
        
        recommendations = []
        if len(large_gaps) > 0:
            recommendations.append("Address temporal gaps through interpolation or additional data collection")
        if n_critical > 0:
            recommendations.append("Critical temporal inconsistencies require investigation")
        
        return QualityReport(
            check_type='temporal_consistency',
            passed=score >= 70,
            score=score,
            n_issues=n_issues,
            anomalies=anomalies,
            summary={
                'n_observations': len(time_series),
                'date_range': f"{time_series.index.min()} to {time_series.index.max()}",
                'autocorrelation': time_series.autocorr(lag=1) if len(time_series) > 1 else None,
                'cv': time_series.std() / time_series.mean() if time_series.mean() != 0 else None
            },
            recommendations=recommendations
        )
    
    def check_arbitrage_violations(self, market_pairs: List[MarketPair]) -> QualityReport:
        """
        Check for arbitrage violations between market pairs.
        
        Args:
            market_pairs: List of market pair observations
            
        Returns:
            Quality report
        """
        logger.info(f"Checking arbitrage conditions for {len(market_pairs)} market pairs")
        
        anomalies = []
        violations_by_commodity = {}
        
        for pair in market_pairs:
            # Expected maximum price differential based on transport
            max_differential = pair.transport_cost * 1.2  # 20% margin
            
            # This is simplified - in practice would need actual price data
            # For now, flag if transport cost seems unreasonable
            if pair.transport_cost > 0.5 * 1000:  # More than 500 YER/unit
                anomalies.append(DataAnomaly(
                    anomaly_type='excessive_transport_cost',
                    severity='warning',
                    affected_data=f"{pair.origin} to {pair.destination}",
                    timestamp=datetime.now(),
                    description=f"Transport cost {pair.transport_cost} seems high for {pair.distance_km}km",
                    suggested_action="Verify transport cost calculations",
                    confidence=0.7
                ))
                violations_by_commodity[pair.commodity] = violations_by_commodity.get(pair.commodity, 0) + 1
            
            # Check for impossible distances
            if pair.distance_km > 1000:  # Yemen max distance ~1000km
                anomalies.append(DataAnomaly(
                    anomaly_type='invalid_distance',
                    severity='critical',
                    affected_data=f"{pair.origin} to {pair.destination}",
                    timestamp=datetime.now(),
                    description=f"Distance {pair.distance_km}km exceeds Yemen dimensions",
                    suggested_action="Correct geographic data",
                    confidence=0.95
                ))
        
        # Statistical analysis of transport costs
        if len(market_pairs) > 10:
            transport_costs = [p.transport_cost for p in market_pairs]
            distances = [p.distance_km for p in market_pairs]
            
            # Cost per km
            cost_per_km = [c/d for c, d in zip(transport_costs, distances) if d > 0]
            
            if cost_per_km:
                mean_cost_km = np.mean(cost_per_km)
                std_cost_km = np.std(cost_per_km)
                
                # Flag outliers
                for i, (pair, cpk) in enumerate(zip(market_pairs, cost_per_km)):
                    if abs(cpk - mean_cost_km) > 2 * std_cost_km:
                        anomalies.append(DataAnomaly(
                            anomaly_type='transport_cost_outlier',
                            severity='warning',
                            affected_data=f"{pair.origin} to {pair.destination}",
                            timestamp=datetime.now(),
                            description=f"Cost/km {cpk:.2f} is outlier (mean: {mean_cost_km:.2f})",
                            suggested_action="Check for special circumstances (conflict, infrastructure)",
                            confidence=0.75
                        ))
        
        # Calculate score
        n_issues = len(anomalies)
        n_critical = sum(1 for a in anomalies if a.severity == 'critical')
        score = max(0, 100 - n_critical * 20 - (n_issues - n_critical) * 5)
        
        recommendations = []
        if n_critical > 0:
            recommendations.append("Fix critical geographic data errors")
        if len(violations_by_commodity) > 3:
            recommendations.append("Systematic transport cost issues across commodities")
        
        return QualityReport(
            check_type='arbitrage_violations',
            passed=score >= 80,
            score=score,
            n_issues=n_issues,
            anomalies=anomalies,
            summary={
                'pairs_checked': len(market_pairs),
                'commodities': len(set(p.commodity for p in market_pairs)),
                'avg_distance': np.mean([p.distance_km for p in market_pairs]) if market_pairs else 0,
                'avg_transport_cost': np.mean([p.transport_cost for p in market_pairs]) if market_pairs else 0
            },
            recommendations=recommendations
        )
    
    def flag_suspicious_patterns(self, data: pd.DataFrame) -> List[DataAnomaly]:
        """
        Flag suspicious patterns in panel data.
        
        Args:
            data: Panel data with prices, markets, dates
            
        Returns:
            List of detected anomalies
        """
        logger.info("Scanning for suspicious patterns in panel data")
        
        anomalies = []
        
        # Pattern 1: Identical prices across markets (collusion indicator)
        if 'market' in data.columns and 'price' in data.columns:
            for date, date_data in data.groupby('date'):
                market_prices = date_data.groupby('market')['price'].mean()
                
                # Check for identical prices
                unique_prices = market_prices.unique()
                if len(unique_prices) < len(market_prices) * 0.8:  # 80% have same price
                    anomalies.append(DataAnomaly(
                        anomaly_type='price_collusion_pattern',
                        severity='warning',
                        affected_data=f"Multiple markets on {date}",
                        timestamp=date,
                        description="Identical prices across markets suggest coordination",
                        suggested_action="Investigate market competition",
                        confidence=0.7
                    ))
        
        # Pattern 2: Synchronized price movements
        if 'commodity' in data.columns:
            for commodity, comm_data in data.groupby('commodity'):
                if len(comm_data['market'].unique()) > 3:
                    # Create price matrix
                    price_matrix = comm_data.pivot(index='date', columns='market', values='price')
                    
                    # Calculate correlation matrix
                    corr_matrix = price_matrix.corr()
                    
                    # Check for excessive correlation
                    high_corr_pairs = []
                    for i in range(len(corr_matrix.columns)):
                        for j in range(i+1, len(corr_matrix.columns)):
                            corr = corr_matrix.iloc[i, j]
                            if corr > 0.95:  # Very high correlation
                                high_corr_pairs.append((
                                    corr_matrix.columns[i],
                                    corr_matrix.columns[j],
                                    corr
                                ))
                    
                    if len(high_corr_pairs) > len(corr_matrix) * 0.3:  # 30% of pairs
                        anomalies.append(DataAnomaly(
                            anomaly_type='synchronized_prices',
                            severity='warning',
                            affected_data=f"{commodity} across markets",
                            timestamp=price_matrix.index[-1],
                            description=f"{len(high_corr_pairs)} market pairs show synchronized pricing",
                            suggested_action="Check for common suppliers or price-setting mechanisms",
                            confidence=0.8
                        ))
        
        # Pattern 3: Reporting clusters (all updates on same day)
        if 'date' in data.columns:
            update_counts = data.groupby('date').size()
            
            # Check for suspicious clustering
            if len(update_counts) > 10:
                update_std = update_counts.std()
                update_mean = update_counts.mean()
                
                for date, count in update_counts.items():
                    if count > update_mean + 3 * update_std:
                        anomalies.append(DataAnomaly(
                            anomaly_type='reporting_cluster',
                            severity='info',
                            affected_data='Data updates',
                            timestamp=date,
                            description=f"Unusual number of updates ({count}) on single day",
                            suggested_action="Verify data collection process",
                            confidence=0.6
                        ))
        
        # Pattern 4: Round number bias
        if 'price' in data.columns:
            prices = data['price'].dropna()
            
            # Check for round numbers
            round_prices = prices[prices % 50 == 0]  # Divisible by 50
            round_pct = len(round_prices) / len(prices)
            
            if round_pct > 0.5:  # More than 50% round numbers
                anomalies.append(DataAnomaly(
                    anomaly_type='round_number_bias',
                    severity='warning',
                    affected_data='Price data',
                    timestamp=data['date'].max() if 'date' in data.columns else datetime.now(),
                    description=f"{round_pct*100:.1f}% of prices are round numbers",
                    suggested_action="Check for estimation vs actual price collection",
                    confidence=0.75
                ))
        
        # Pattern 5: Missing data patterns
        if data.isnull().any().any():
            missing_pct = data.isnull().sum() / len(data)
            
            # Check for systematic missing data
            for col, pct in missing_pct.items():
                if pct > 0.3:  # More than 30% missing
                    anomalies.append(DataAnomaly(
                        anomaly_type='systematic_missing_data',
                        severity='warning' if pct < 0.5 else 'critical',
                        affected_data=f"Column: {col}",
                        timestamp=datetime.now(),
                        description=f"{pct*100:.1f}% missing values",
                        suggested_action="Investigate data collection gaps",
                        confidence=0.9
                    ))
        
        return anomalies
    
    def generate_quality_dashboard(self, 
                                 price_report: QualityReport,
                                 exchange_report: QualityReport,
                                 temporal_reports: List[QualityReport]) -> Dict:
        """
        Generate comprehensive quality dashboard.
        
        Args:
            price_report: Price quality report
            exchange_report: Exchange rate quality report
            temporal_reports: List of temporal consistency reports
            
        Returns:
            Dashboard dictionary
        """
        # Overall health score
        all_scores = [price_report.score, exchange_report.score]
        all_scores.extend([r.score for r in temporal_reports])
        overall_score = np.mean(all_scores)
        
        # Aggregate anomalies
        all_anomalies = price_report.anomalies + exchange_report.anomalies
        for report in temporal_reports:
            all_anomalies.extend(report.anomalies)
        
        # Count by severity
        severity_counts = {
            'critical': sum(1 for a in all_anomalies if a.severity == 'critical'),
            'warning': sum(1 for a in all_anomalies if a.severity == 'warning'),
            'info': sum(1 for a in all_anomalies if a.severity == 'info')
        }
        
        # Top issues
        anomaly_types = {}
        for anomaly in all_anomalies:
            anomaly_types[anomaly.anomaly_type] = anomaly_types.get(anomaly.anomaly_type, 0) + 1
        
        top_issues = sorted(anomaly_types.items(), key=lambda x: x[1], reverse=True)[:5]
        
        # Recommendations
        all_recommendations = set()
        all_recommendations.update(price_report.recommendations)
        all_recommendations.update(exchange_report.recommendations)
        for report in temporal_reports:
            all_recommendations.update(report.recommendations)
        
        dashboard = {
            'overall_health': {
                'score': overall_score,
                'status': 'good' if overall_score >= 80 else 'warning' if overall_score >= 60 else 'critical',
                'total_anomalies': len(all_anomalies),
                'critical_issues': severity_counts['critical']
            },
            'component_scores': {
                'price_quality': price_report.score,
                'exchange_rate_quality': exchange_report.score,
                'temporal_consistency': np.mean([r.score for r in temporal_reports]) if temporal_reports else None
            },
            'severity_breakdown': severity_counts,
            'top_issues': top_issues,
            'recommendations': list(all_recommendations),
            'data_coverage': {
                'price_observations': price_report.summary.get('total_prices', 0),
                'exchange_observations': exchange_report.summary.get('total_rates', 0),
                'time_series_checked': len(temporal_reports)
            }
        }
        
        return dashboard