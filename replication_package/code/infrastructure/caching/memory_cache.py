"""In-memory cache implementation."""

import asyncio
import time
from typing import Any, Dict, Optional, Tuple

from ...application.interfaces import Cache


class MemoryCache(Cache):
    """In-memory implementation of cache interface."""
    
    def __init__(self, default_ttl: int = 3600, max_size: int = 1000):
        """Initialize memory cache."""
        self.default_ttl = default_ttl
        self.max_size = max_size
        self._cache: Dict[str, Tuple[Any, float]] = {}
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        async with self._lock:
            if key not in self._cache:
                return None
            
            value, expires_at = self._cache[key]
            
            # Check if expired
            if expires_at < time.time():
                del self._cache[key]
                return None
            
            return value
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache with optional TTL in seconds."""
        ttl = ttl or self.default_ttl
        expires_at = time.time() + ttl
        
        async with self._lock:
            # Implement simple LRU by removing oldest entries if at capacity
            if len(self._cache) >= self.max_size and key not in self._cache:
                # Remove the entry that expires soonest
                oldest_key = min(
                    self._cache.keys(),
                    key=lambda k: self._cache[k][1]
                )
                del self._cache[oldest_key]
            
            self._cache[key] = (value, expires_at)
    
    async def delete(self, key: str) -> None:
        """Delete value from cache."""
        async with self._lock:
            self._cache.pop(key, None)
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        async with self._lock:
            self._cache.clear()
    
    async def cleanup_expired(self) -> None:
        """Remove expired entries from cache."""
        current_time = time.time()
        
        async with self._lock:
            expired_keys = [
                key for key, (_, expires_at) in self._cache.items()
                if expires_at < current_time
            ]
            
            for key in expired_keys:
                del self._cache[key]