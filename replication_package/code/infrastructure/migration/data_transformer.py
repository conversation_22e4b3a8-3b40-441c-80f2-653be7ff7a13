"""Data transformation layer for V1 to V2 migration."""

import pandas as pd
import numpy as np
import json
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Union
import logging
from dataclasses import dataclass, asdict
import uuid
from decimal import Decimal
import re

from src.infrastructure.observability.structured_logging import StructuredLogger
from src.core.domain.market.value_objects import MarketId, Price, Commodity
from src.core.domain.shared.exceptions import ValidationError


@dataclass
class TransformationRule:
    """Definition of a data transformation rule."""
    source_field: str
    target_field: str
    transformation_type: str  # 'direct', 'mapping', 'calculation', 'validation'
    parameters: Dict[str, Any]
    validation_rules: List[str]
    is_required: bool


@dataclass
class TransformationResult:
    """Result of a data transformation operation."""
    success: bool
    records_processed: int
    records_transformed: int
    records_failed: int
    validation_errors: List[str]
    data_quality_warnings: List[str]
    transformation_metadata: Dict[str, Any]


class DataTransformer:
    """Handles transformation of V1 data to V2 schema format."""
    
    def __init__(self, transformation_rules_path: Optional[Path] = None):
        """Initialize the data transformer."""
        self.logger = StructuredLogger("DataTransformer")
        
        # Load transformation rules
        if transformation_rules_path and transformation_rules_path.exists():
            self.transformation_rules = self._load_transformation_rules(transformation_rules_path)
        else:
            self.transformation_rules = self._get_default_transformation_rules()
        
        # Transformation statistics
        self.transformation_stats = {
            'markets': {},
            'commodities': {},
            'prices': {},
            'conflicts': {},
            'analysis_results': {}
        }
        
        # Data quality thresholds
        self.quality_thresholds = {
            'min_price': 0.01,
            'max_price': 1000000,
            'min_date': pd.Timestamp('2019-01-01'),
            'max_date': pd.Timestamp.now() + pd.Timedelta(days=30),
            'required_coverage': 0.80
        }
    
    def transform_markets(self, markets_data: pd.DataFrame) -> Tuple[pd.DataFrame, TransformationResult]:
        """Transform market data to V2 schema."""
        self.logger.info("Transforming market data to V2 schema")
        
        start_time = datetime.now()
        errors = []
        warnings = []
        
        try:
            # Initialize result dataframe with V2 schema
            v2_markets = pd.DataFrame(columns=[
                'id', 'market_id', 'name', 'latitude', 'longitude',
                'market_type', 'governorate', 'district', 
                'active_since', 'active_until', 'version', 
                'created_at', 'updated_at'
            ])
            
            if markets_data.empty:
                warnings.append("No market data to transform")
                return v2_markets, self._create_transformation_result(True, 0, 0, 0, [], warnings)
            
            processed_records = 0
            transformed_records = 0
            failed_records = 0
            
            for idx, row in markets_data.iterrows():
                processed_records += 1
                
                try:
                    # Generate UUID for primary key
                    market_uuid = str(uuid.uuid4())
                    
                    # Transform market_id
                    market_id = self._transform_market_id(row.get('market_id', ''))
                    if not market_id:
                        market_id = self._generate_market_id_from_name(row.get('name', ''))
                    
                    # Validate and transform coordinates
                    latitude, longitude = self._validate_coordinates(
                        row.get('latitude', 0), 
                        row.get('longitude', 0)
                    )
                    
                    # Transform market type
                    market_type = self._transform_market_type(row.get('market_type', 'retail'))
                    
                    # Transform dates
                    active_since = self._transform_date(row.get('active_since', '2019-01-01'))
                    active_until = self._transform_date(row.get('active_until', None)) if pd.notna(row.get('active_until')) else None
                    
                    # Validate required fields
                    if not market_id or not row.get('name') or not row.get('governorate'):
                        raise ValidationError(f"Missing required fields for market {idx}")
                    
                    # Create transformed record
                    transformed_row = {
                        'id': market_uuid,
                        'market_id': market_id,
                        'name': str(row.get('name', '')).strip(),
                        'latitude': latitude,
                        'longitude': longitude,
                        'market_type': market_type,
                        'governorate': str(row.get('governorate', '')).strip(),
                        'district': str(row.get('district', '')).strip(),
                        'active_since': active_since,
                        'active_until': active_until,
                        'version': 0,
                        'created_at': datetime.now(),
                        'updated_at': datetime.now()
                    }
                    
                    # Add to result dataframe
                    v2_markets = pd.concat([v2_markets, pd.DataFrame([transformed_row])], ignore_index=True)
                    transformed_records += 1
                    
                except Exception as e:
                    self.logger.warning(f"Failed to transform market record {idx}: {e}")
                    errors.append(f"Market {idx}: {str(e)}")
                    failed_records += 1
            
            # Post-processing validations
            duplicate_ids = v2_markets['market_id'].duplicated().sum()
            if duplicate_ids > 0:
                warnings.append(f"Found {duplicate_ids} duplicate market IDs after transformation")
            
            # Update transformation statistics
            self.transformation_stats['markets'] = {
                'processed': processed_records,
                'transformed': transformed_records,
                'failed': failed_records,
                'duplicates_found': duplicate_ids
            }
            
            result = TransformationResult(
                success=failed_records == 0,
                records_processed=processed_records,
                records_transformed=transformed_records,
                records_failed=failed_records,
                validation_errors=errors,
                data_quality_warnings=warnings,
                transformation_metadata={
                    'transformation_time': (datetime.now() - start_time).total_seconds(),
                    'duplicate_market_ids': duplicate_ids,
                    'coverage_rate': transformed_records / processed_records if processed_records > 0 else 0
                }
            )
            
            return v2_markets, result
            
        except Exception as e:
            self.logger.error(f"Market transformation failed: {e}")
            raise
    
    def transform_commodities(self, commodities_data: pd.DataFrame) -> Tuple[pd.DataFrame, TransformationResult]:
        """Transform commodity data to V2 schema."""
        self.logger.info("Transforming commodity data to V2 schema")
        
        start_time = datetime.now()
        errors = []
        warnings = []
        
        try:
            # Initialize result dataframe
            v2_commodities = pd.DataFrame(columns=[
                'code', 'name', 'category', 'standard_unit', 'created_at'
            ])
            
            if commodities_data.empty:
                warnings.append("No commodity data to transform")
                return v2_commodities, self._create_transformation_result(True, 0, 0, 0, [], warnings)
            
            processed_records = 0
            transformed_records = 0
            failed_records = 0
            
            for idx, row in commodities_data.iterrows():
                processed_records += 1
                
                try:
                    # Transform commodity code
                    commodity_code = self._transform_commodity_code(row.get('code', ''))
                    if not commodity_code:
                        commodity_code = self._generate_commodity_code_from_name(row.get('name', ''))
                    
                    # Validate required fields
                    if not commodity_code or not row.get('name'):
                        raise ValidationError(f"Missing required fields for commodity {idx}")
                    
                    # Transform category and unit
                    category = self._standardize_commodity_category(row.get('category', 'food'))
                    standard_unit = self._standardize_unit(row.get('standard_unit', 'kg'))
                    
                    # Create transformed record
                    transformed_row = {
                        'code': commodity_code,
                        'name': str(row.get('name', '')).strip(),
                        'category': category,
                        'standard_unit': standard_unit,
                        'created_at': datetime.now()
                    }
                    
                    # Add to result dataframe
                    v2_commodities = pd.concat([v2_commodities, pd.DataFrame([transformed_row])], ignore_index=True)
                    transformed_records += 1
                    
                except Exception as e:
                    self.logger.warning(f"Failed to transform commodity record {idx}: {e}")
                    errors.append(f"Commodity {idx}: {str(e)}")
                    failed_records += 1
            
            # Post-processing validations
            duplicate_codes = v2_commodities['code'].duplicated().sum()
            if duplicate_codes > 0:
                warnings.append(f"Found {duplicate_codes} duplicate commodity codes after transformation")
            
            # Update transformation statistics
            self.transformation_stats['commodities'] = {
                'processed': processed_records,
                'transformed': transformed_records,
                'failed': failed_records,
                'duplicates_found': duplicate_codes
            }
            
            result = TransformationResult(
                success=failed_records == 0,
                records_processed=processed_records,
                records_transformed=transformed_records,
                records_failed=failed_records,
                validation_errors=errors,
                data_quality_warnings=warnings,
                transformation_metadata={
                    'transformation_time': (datetime.now() - start_time).total_seconds(),
                    'duplicate_commodity_codes': duplicate_codes,
                    'coverage_rate': transformed_records / processed_records if processed_records > 0 else 0
                }
            )
            
            return v2_commodities, result
            
        except Exception as e:
            self.logger.error(f"Commodity transformation failed: {e}")
            raise
    
    def transform_prices(self, prices_data: pd.DataFrame) -> Tuple[pd.DataFrame, TransformationResult]:
        """Transform price data to V2 schema."""
        self.logger.info("Transforming price data to V2 schema")
        
        start_time = datetime.now()
        errors = []
        warnings = []
        
        try:
            # Initialize result dataframe
            v2_prices = pd.DataFrame(columns=[
                'id', 'market_id', 'commodity_code', 'price_amount', 'price_currency',
                'price_unit', 'observed_date', 'source', 'quality', 
                'observations_count', 'created_at'
            ])
            
            if prices_data.empty:
                warnings.append("No price data to transform")
                return v2_prices, self._create_transformation_result(True, 0, 0, 0, [], warnings)
            
            processed_records = 0
            transformed_records = 0
            failed_records = 0
            
            # Process in chunks for large datasets
            chunk_size = 10000
            for chunk_start in range(0, len(prices_data), chunk_size):
                chunk_end = min(chunk_start + chunk_size, len(prices_data))
                chunk = prices_data.iloc[chunk_start:chunk_end]
                
                for idx, row in chunk.iterrows():
                    processed_records += 1
                    
                    try:
                        # Generate UUID for primary key
                        price_uuid = str(uuid.uuid4())
                        
                        # Transform and validate fields
                        market_id = self._transform_market_id(row.get('market_id', ''))
                        commodity_code = self._transform_commodity_code(row.get('commodity_code', ''))
                        
                        # Validate and transform price
                        price_amount = self._validate_price_amount(row.get('price_amount', 0))
                        
                        # Transform currency and unit
                        price_currency = self._standardize_currency(row.get('price_currency', 'YER'))
                        price_unit = self._standardize_unit(row.get('price_unit', 'kg'))
                        
                        # Transform date
                        observed_date = self._transform_date(row.get('observed_date'))
                        
                        # Validate required fields
                        if not market_id or not commodity_code or price_amount <= 0 or not observed_date:
                            raise ValidationError(f"Missing or invalid required fields for price {idx}")
                        
                        # Transform optional fields
                        source = str(row.get('source', 'V1_MIGRATION')).strip()
                        quality = self._standardize_quality(row.get('quality', 'standard'))
                        observations_count = max(1, int(row.get('observations_count', 1)))
                        
                        # Create transformed record
                        transformed_row = {
                            'id': price_uuid,
                            'market_id': market_id,
                            'commodity_code': commodity_code,
                            'price_amount': price_amount,
                            'price_currency': price_currency,
                            'price_unit': price_unit,
                            'observed_date': observed_date,
                            'source': source,
                            'quality': quality,
                            'observations_count': observations_count,
                            'created_at': datetime.now()
                        }
                        
                        # Add to result dataframe
                        v2_prices = pd.concat([v2_prices, pd.DataFrame([transformed_row])], ignore_index=True)
                        transformed_records += 1
                        
                    except Exception as e:
                        self.logger.warning(f"Failed to transform price record {idx}: {e}")
                        errors.append(f"Price {idx}: {str(e)}")
                        failed_records += 1
                
                # Log progress for large datasets
                if chunk_end % 50000 == 0:
                    self.logger.info(f"Processed {chunk_end} price records")
            
            # Post-processing validations and cleanup
            
            # Check for duplicates
            duplicate_subset = ['market_id', 'commodity_code', 'observed_date', 'source']
            duplicates = v2_prices.duplicated(subset=duplicate_subset).sum()
            if duplicates > 0:
                warnings.append(f"Found {duplicates} duplicate price observations")
                v2_prices = v2_prices.drop_duplicates(subset=duplicate_subset, keep='first')
            
            # Data quality checks
            outliers = self._detect_price_outliers(v2_prices)
            if len(outliers) > 0:
                warnings.append(f"Detected {len(outliers)} potential price outliers")
            
            # Update transformation statistics
            self.transformation_stats['prices'] = {
                'processed': processed_records,
                'transformed': transformed_records,
                'failed': failed_records,
                'duplicates_removed': duplicates,
                'outliers_detected': len(outliers)
            }
            
            result = TransformationResult(
                success=failed_records < processed_records * 0.05,  # Allow 5% failure rate
                records_processed=processed_records,
                records_transformed=transformed_records,
                records_failed=failed_records,
                validation_errors=errors,
                data_quality_warnings=warnings,
                transformation_metadata={
                    'transformation_time': (datetime.now() - start_time).total_seconds(),
                    'duplicates_removed': duplicates,
                    'outliers_detected': len(outliers),
                    'coverage_rate': transformed_records / processed_records if processed_records > 0 else 0,
                    'price_range': {
                        'min': float(v2_prices['price_amount'].min()) if not v2_prices.empty else 0,
                        'max': float(v2_prices['price_amount'].max()) if not v2_prices.empty else 0,
                        'median': float(v2_prices['price_amount'].median()) if not v2_prices.empty else 0
                    },
                    'date_range': {
                        'min': str(v2_prices['observed_date'].min()) if not v2_prices.empty else None,
                        'max': str(v2_prices['observed_date'].max()) if not v2_prices.empty else None
                    }
                }
            )
            
            return v2_prices, result
            
        except Exception as e:
            self.logger.error(f"Price transformation failed: {e}")
            raise
    
    def transform_conflicts(self, conflicts_data: pd.DataFrame) -> Tuple[pd.DataFrame, TransformationResult]:
        """Transform conflict events data to V2 schema."""
        self.logger.info("Transforming conflict data to V2 schema")
        
        start_time = datetime.now()
        errors = []
        warnings = []
        
        try:
            # Initialize result dataframe
            v2_conflicts = pd.DataFrame(columns=[
                'id', 'event_id', 'event_type', 'sub_event_type', 'event_date',
                'latitude', 'longitude', 'governorate', 'district', 'fatalities',
                'notes', 'source', 'source_scale', 'created_at'
            ])
            
            if conflicts_data.empty:
                warnings.append("No conflict data to transform")
                return v2_conflicts, self._create_transformation_result(True, 0, 0, 0, [], warnings)
            
            processed_records = 0
            transformed_records = 0
            failed_records = 0
            
            for idx, row in conflicts_data.iterrows():
                processed_records += 1
                
                try:
                    # Generate UUID for primary key
                    conflict_uuid = str(uuid.uuid4())
                    
                    # Transform event ID
                    event_id = str(row.get('event_id', f'CONFLICT_{idx}')).strip()
                    
                    # Validate and transform coordinates
                    latitude, longitude = self._validate_coordinates(
                        row.get('latitude', 0),
                        row.get('longitude', 0)
                    )
                    
                    # Transform event date
                    event_date = self._transform_date(row.get('event_date'))
                    
                    # Validate required fields
                    if not event_id or not event_date:
                        raise ValidationError(f"Missing required fields for conflict {idx}")
                    
                    # Transform event types
                    event_type = str(row.get('event_type', 'Unknown')).strip()
                    sub_event_type = str(row.get('sub_event_type', '')).strip() if pd.notna(row.get('sub_event_type')) else None
                    
                    # Transform numeric fields
                    fatalities = max(0, int(row.get('fatalities', 0)))
                    
                    # Transform text fields
                    governorate = str(row.get('governorate', '')).strip()
                    district = str(row.get('district', '')).strip() if pd.notna(row.get('district')) else None
                    notes = str(row.get('notes', '')).strip() if pd.notna(row.get('notes')) else None
                    source = str(row.get('source', 'V1_MIGRATION')).strip()
                    source_scale = str(row.get('source_scale', '')).strip() if pd.notna(row.get('source_scale')) else None
                    
                    # Create transformed record
                    transformed_row = {
                        'id': conflict_uuid,
                        'event_id': event_id,
                        'event_type': event_type,
                        'sub_event_type': sub_event_type,
                        'event_date': event_date,
                        'latitude': latitude,
                        'longitude': longitude,
                        'governorate': governorate,
                        'district': district,
                        'fatalities': fatalities,
                        'notes': notes,
                        'source': source,
                        'source_scale': source_scale,
                        'created_at': datetime.now()
                    }
                    
                    # Add to result dataframe
                    v2_conflicts = pd.concat([v2_conflicts, pd.DataFrame([transformed_row])], ignore_index=True)
                    transformed_records += 1
                    
                except Exception as e:
                    self.logger.warning(f"Failed to transform conflict record {idx}: {e}")
                    errors.append(f"Conflict {idx}: {str(e)}")
                    failed_records += 1
            
            # Post-processing validations
            duplicate_events = v2_conflicts['event_id'].duplicated().sum()
            if duplicate_events > 0:
                warnings.append(f"Found {duplicate_events} duplicate event IDs")
                v2_conflicts = v2_conflicts.drop_duplicates(subset=['event_id'], keep='first')
            
            # Update transformation statistics
            self.transformation_stats['conflicts'] = {
                'processed': processed_records,
                'transformed': transformed_records,
                'failed': failed_records,
                'duplicates_removed': duplicate_events
            }
            
            result = TransformationResult(
                success=failed_records == 0,
                records_processed=processed_records,
                records_transformed=transformed_records,
                records_failed=failed_records,
                validation_errors=errors,
                data_quality_warnings=warnings,
                transformation_metadata={
                    'transformation_time': (datetime.now() - start_time).total_seconds(),
                    'duplicates_removed': duplicate_events,
                    'coverage_rate': transformed_records / processed_records if processed_records > 0 else 0,
                    'fatalities_stats': {
                        'total': int(v2_conflicts['fatalities'].sum()) if not v2_conflicts.empty else 0,
                        'avg_per_event': float(v2_conflicts['fatalities'].mean()) if not v2_conflicts.empty else 0,
                        'max_single_event': int(v2_conflicts['fatalities'].max()) if not v2_conflicts.empty else 0
                    }
                }
            )
            
            return v2_conflicts, result
            
        except Exception as e:
            self.logger.error(f"Conflict transformation failed: {e}")
            raise
    
    def transform_analysis_results(self, analysis_data: pd.DataFrame) -> Tuple[pd.DataFrame, TransformationResult]:
        """Transform analysis results to V2 schema."""
        self.logger.info("Transforming analysis results to V2 schema")
        
        start_time = datetime.now()
        errors = []
        warnings = []
        
        try:
            # Initialize result dataframe
            v2_analysis = pd.DataFrame(columns=[
                'id', 'analysis_type', 'parameters', 'results', 'status',
                'started_at', 'completed_at', 'error_message', 'created_by',
                'created_at', 'expires_at'
            ])
            
            if analysis_data.empty:
                warnings.append("No analysis results data to transform")
                return v2_analysis, self._create_transformation_result(True, 0, 0, 0, [], warnings)
            
            processed_records = 0
            transformed_records = 0
            failed_records = 0
            
            for idx, row in analysis_data.iterrows():
                processed_records += 1
                
                try:
                    # Generate UUID for primary key
                    analysis_uuid = str(uuid.uuid4())
                    
                    # Transform analysis type
                    analysis_type = str(row.get('analysis_type', 'v1_migration')).strip()
                    
                    # Transform parameters and results (should be JSON strings)
                    parameters = self._ensure_json_string(row.get('parameters', '{}'))
                    results = self._ensure_json_string(row.get('results', '{}'))
                    
                    # Transform status
                    status = self._standardize_analysis_status(row.get('status', 'completed'))
                    
                    # Transform dates
                    started_at = self._transform_date(row.get('started_at', row.get('created_at', datetime.now())))
                    completed_at = self._transform_date(row.get('completed_at', row.get('started_at', datetime.now())))
                    
                    # Optional fields
                    error_message = str(row.get('error_message', '')).strip() if pd.notna(row.get('error_message')) else None
                    created_by = str(row.get('created_by', 'V1_MIGRATION')).strip()
                    
                    # Set expiration (1 year from creation)
                    expires_at = datetime.now() + timedelta(days=365)
                    
                    # Create transformed record
                    transformed_row = {
                        'id': analysis_uuid,
                        'analysis_type': analysis_type,
                        'parameters': parameters,
                        'results': results,
                        'status': status,
                        'started_at': started_at,
                        'completed_at': completed_at,
                        'error_message': error_message,
                        'created_by': created_by,
                        'created_at': datetime.now(),
                        'expires_at': expires_at
                    }
                    
                    # Add to result dataframe
                    v2_analysis = pd.concat([v2_analysis, pd.DataFrame([transformed_row])], ignore_index=True)
                    transformed_records += 1
                    
                except Exception as e:
                    self.logger.warning(f"Failed to transform analysis record {idx}: {e}")
                    errors.append(f"Analysis {idx}: {str(e)}")
                    failed_records += 1
            
            # Update transformation statistics
            self.transformation_stats['analysis_results'] = {
                'processed': processed_records,
                'transformed': transformed_records,
                'failed': failed_records
            }
            
            result = TransformationResult(
                success=failed_records == 0,
                records_processed=processed_records,
                records_transformed=transformed_records,
                records_failed=failed_records,
                validation_errors=errors,
                data_quality_warnings=warnings,
                transformation_metadata={
                    'transformation_time': (datetime.now() - start_time).total_seconds(),
                    'coverage_rate': transformed_records / processed_records if processed_records > 0 else 0
                }
            )
            
            return v2_analysis, result
            
        except Exception as e:
            self.logger.error(f"Analysis results transformation failed: {e}")
            raise
    
    # Helper methods for field transformations
    
    def _transform_market_id(self, market_id: str) -> str:
        """Transform and standardize market ID."""
        if pd.isna(market_id) or not market_id:
            return ""
        
        # Remove special characters and standardize
        cleaned = re.sub(r'[^\w\s-]', '', str(market_id))
        cleaned = re.sub(r'\s+', '_', cleaned.strip())
        cleaned = cleaned.upper()
        
        # Apply known mappings
        mappings = {
            "SANA'A_CITY": "SANAA_CITY",
            "SANA_A_CITY": "SANAA_CITY",
            "AL_BAYDA_CITY": "ALBAYDA_CITY",
            "AL_HAZUM": "ALHAZUM",
            "AL_JABEEN": "ALJABEEN",
            "AL_HAWTAH_MAIN_ROAD": "ALHAWTAH_MAIN_ROAD"
        }
        
        return mappings.get(cleaned, cleaned)
    
    def _generate_market_id_from_name(self, name: str) -> str:
        """Generate market ID from market name."""
        if pd.isna(name) or not name:
            return f"MARKET_{uuid.uuid4().hex[:8].upper()}"
        
        # Clean and standardize
        cleaned = re.sub(r'[^\w\s]', '', str(name))
        cleaned = re.sub(r'\s+', '_', cleaned.strip())
        return cleaned.upper()
    
    def _transform_commodity_code(self, commodity_code: str) -> str:
        """Transform and standardize commodity code."""
        if pd.isna(commodity_code) or not commodity_code:
            return ""
        
        # Remove special characters and standardize
        cleaned = re.sub(r'[^\w\s-]', '', str(commodity_code))
        cleaned = re.sub(r'\s+', '_', cleaned.strip())
        cleaned = cleaned.upper()
        
        # Apply known mappings
        mappings = {
            "BEANS_KIDNEY_RED": "BEANS_RED",
            "RICE_IMPORTED": "RICE",
            "OIL_VEGETABLE": "OIL_VEG",
            "FUEL_PETROL_GASOLINE": "FUEL_PETROL",
            "PEAS_YELLOW_SPLIT": "PEAS_YELLOW",
            "SOQATRA_HUDAIBO": "SOCOTRA"
        }
        
        return mappings.get(cleaned, cleaned)
    
    def _generate_commodity_code_from_name(self, name: str) -> str:
        """Generate commodity code from commodity name."""
        if pd.isna(name) or not name:
            return f"COMMODITY_{uuid.uuid4().hex[:8].upper()}"
        
        # Extract key words and create code
        cleaned = re.sub(r'[^\w\s]', '', str(name))
        words = cleaned.split()
        
        if len(words) >= 2:
            return f"{words[0].upper()}_{words[1].upper()}"
        elif len(words) == 1:
            return words[0].upper()
        else:
            return f"COMMODITY_{uuid.uuid4().hex[:8].upper()}"
    
    def _validate_coordinates(self, latitude: float, longitude: float) -> Tuple[float, float]:
        """Validate and clean geographic coordinates."""
        try:
            lat = float(latitude) if pd.notna(latitude) else 0.0
            lon = float(longitude) if pd.notna(longitude) else 0.0
            
            # Validate ranges
            if lat < -90 or lat > 90:
                lat = 0.0
            if lon < -180 or lon > 180:
                lon = 0.0
            
            # Round to reasonable precision
            lat = round(lat, 8)
            lon = round(lon, 8)
            
            return lat, lon
            
        except (ValueError, TypeError):
            return 0.0, 0.0
    
    def _transform_market_type(self, market_type: str) -> str:
        """Transform market type to valid V2 values."""
        if pd.isna(market_type):
            return "retail"
        
        type_mappings = {
            "retail": "retail",
            "wholesale": "wholesale", 
            "border": "border",
            "border_crossing": "border",
            "port": "port",
            "rural": "rural",
            "urban": "urban"
        }
        
        cleaned = str(market_type).lower().strip()
        return type_mappings.get(cleaned, "retail")
    
    def _transform_date(self, date_value: Any) -> Optional[datetime]:
        """Transform various date formats to datetime."""
        if pd.isna(date_value) or date_value is None:
            return None
        
        try:
            if isinstance(date_value, datetime):
                return date_value
            elif isinstance(date_value, pd.Timestamp):
                return date_value.to_pydatetime()
            else:
                # Try to parse string dates
                parsed = pd.to_datetime(date_value)
                return parsed.to_pydatetime() if pd.notna(parsed) else None
        except:
            return None
    
    def _validate_price_amount(self, price: Any) -> float:
        """Validate and clean price amount."""
        try:
            price_val = float(price) if pd.notna(price) else 0.0
            
            # Apply quality thresholds
            if price_val < self.quality_thresholds['min_price']:
                raise ValidationError(f"Price below minimum threshold: {price_val}")
            if price_val > self.quality_thresholds['max_price']:
                raise ValidationError(f"Price above maximum threshold: {price_val}")
            
            return round(price_val, 2)
            
        except (ValueError, TypeError):
            raise ValidationError(f"Invalid price value: {price}")
    
    def _standardize_currency(self, currency: str) -> str:
        """Standardize currency codes."""
        if pd.isna(currency):
            return "YER"
        
        currency_mappings = {
            "yer": "YER",
            "usd": "USD", 
            "sar": "SAR",
            "euro": "EUR",
            "eur": "EUR"
        }
        
        cleaned = str(currency).lower().strip()
        return currency_mappings.get(cleaned, "YER")
    
    def _standardize_unit(self, unit: str) -> str:
        """Standardize measurement units."""
        if pd.isna(unit):
            return "kg"
        
        unit_mappings = {
            "kg": "kg",
            "kilogram": "kg",
            "kilograms": "kg",
            "liter": "liter",
            "litre": "liter",
            "l": "liter",
            "dozen": "dozen",
            "piece": "piece",
            "pieces": "piece",
            "gram": "gram",
            "g": "gram"
        }
        
        cleaned = str(unit).lower().strip()
        return unit_mappings.get(cleaned, "kg")
    
    def _standardize_quality(self, quality: str) -> str:
        """Standardize quality ratings."""
        if pd.isna(quality):
            return "standard"
        
        quality_mappings = {
            "high": "high",
            "standard": "standard",
            "low": "low",
            "good": "high",
            "medium": "standard",
            "poor": "low",
            "real": "high"  # For real prices vs nominal
        }
        
        cleaned = str(quality).lower().strip()
        return quality_mappings.get(cleaned, "standard")
    
    def _standardize_commodity_category(self, category: str) -> str:
        """Standardize commodity categories."""
        if pd.isna(category):
            return "food"
        
        category_mappings = {
            "food": "food",
            "grains": "grains",
            "legumes": "legumes",
            "vegetables": "vegetables",
            "protein": "protein",
            "fuel": "fuel",
            "cereals": "grains",
            "pulses": "legumes"
        }
        
        cleaned = str(category).lower().strip()
        return category_mappings.get(cleaned, "food")
    
    def _standardize_analysis_status(self, status: str) -> str:
        """Standardize analysis status."""
        if pd.isna(status):
            return "completed"
        
        status_mappings = {
            "completed": "completed",
            "complete": "completed",
            "success": "completed",
            "successful": "completed",
            "failed": "failed",
            "error": "failed",
            "running": "running",
            "pending": "pending",
            "cancelled": "cancelled",
            "canceled": "cancelled"
        }
        
        cleaned = str(status).lower().strip()
        return status_mappings.get(cleaned, "completed")
    
    def _ensure_json_string(self, value: Any) -> str:
        """Ensure value is a valid JSON string."""
        if pd.isna(value) or value is None:
            return "{}"
        
        if isinstance(value, str):
            # Try to parse to ensure it's valid JSON
            try:
                json.loads(value)
                return value
            except json.JSONDecodeError:
                # If not valid JSON, wrap as string value
                return json.dumps({"value": value})
        else:
            # Convert other types to JSON
            try:
                return json.dumps(value, default=str)
            except TypeError:
                return json.dumps({"value": str(value)})
    
    def _detect_price_outliers(self, prices_df: pd.DataFrame) -> List[int]:
        """Detect potential price outliers."""
        if prices_df.empty or 'price_amount' not in prices_df.columns:
            return []
        
        outliers = []
        
        # Group by commodity and detect outliers within each group
        for commodity, group in prices_df.groupby('commodity_code'):
            if len(group) < 5:  # Skip commodities with too few observations
                continue
            
            Q1 = group['price_amount'].quantile(0.25)
            Q3 = group['price_amount'].quantile(0.75)
            IQR = Q3 - Q1
            
            # Define outlier bounds (1.5 * IQR is standard, but we use 3 * IQR for more tolerance)
            lower_bound = Q1 - 3 * IQR
            upper_bound = Q3 + 3 * IQR
            
            # Find outliers
            commodity_outliers = group[
                (group['price_amount'] < lower_bound) | 
                (group['price_amount'] > upper_bound)
            ].index.tolist()
            
            outliers.extend(commodity_outliers)
        
        return outliers
    
    def _create_transformation_result(
        self, 
        success: bool, 
        processed: int, 
        transformed: int, 
        failed: int, 
        errors: List[str], 
        warnings: List[str]
    ) -> TransformationResult:
        """Create a standard transformation result."""
        return TransformationResult(
            success=success,
            records_processed=processed,
            records_transformed=transformed,
            records_failed=failed,
            validation_errors=errors,
            data_quality_warnings=warnings,
            transformation_metadata={}
        )
    
    def _load_transformation_rules(self, rules_path: Path) -> Dict[str, List[TransformationRule]]:
        """Load transformation rules from configuration file."""
        try:
            with open(rules_path) as f:
                rules_config = json.load(f)
            
            transformation_rules = {}
            for entity_type, rules_list in rules_config.items():
                transformation_rules[entity_type] = [
                    TransformationRule(**rule) for rule in rules_list
                ]
            
            return transformation_rules
            
        except Exception as e:
            self.logger.warning(f"Failed to load transformation rules: {e}")
            return self._get_default_transformation_rules()
    
    def _get_default_transformation_rules(self) -> Dict[str, List[TransformationRule]]:
        """Get default transformation rules."""
        return {
            'markets': [
                TransformationRule(
                    source_field='market_id',
                    target_field='market_id',
                    transformation_type='mapping',
                    parameters={'mapping_function': '_transform_market_id'},
                    validation_rules=['required', 'unique'],
                    is_required=True
                ),
                TransformationRule(
                    source_field='name',
                    target_field='name',
                    transformation_type='direct',
                    parameters={},
                    validation_rules=['required'],
                    is_required=True
                )
            ],
            'commodities': [
                TransformationRule(
                    source_field='code',
                    target_field='code',
                    transformation_type='mapping',
                    parameters={'mapping_function': '_transform_commodity_code'},
                    validation_rules=['required', 'unique'],
                    is_required=True
                )
            ],
            'prices': [
                TransformationRule(
                    source_field='price_amount',
                    target_field='price_amount',
                    transformation_type='validation',
                    parameters={'min_value': 0.01, 'max_value': 1000000},
                    validation_rules=['required', 'positive'],
                    is_required=True
                )
            ]
        }
    
    def get_transformation_summary(self) -> Dict[str, Any]:
        """Get comprehensive transformation summary."""
        return {
            "transformation_timestamp": datetime.now().isoformat(),
            "entity_statistics": self.transformation_stats,
            "overall_success_rate": self._calculate_overall_success_rate(),
            "data_quality_summary": self._generate_data_quality_summary(),
            "recommendations": self._generate_transformation_recommendations()
        }
    
    def _calculate_overall_success_rate(self) -> float:
        """Calculate overall transformation success rate."""
        total_processed = sum(stats.get('processed', 0) for stats in self.transformation_stats.values())
        total_transformed = sum(stats.get('transformed', 0) for stats in self.transformation_stats.values())
        
        return total_transformed / total_processed if total_processed > 0 else 0.0
    
    def _generate_data_quality_summary(self) -> Dict[str, Any]:
        """Generate data quality summary."""
        return {
            "overall_coverage": self._calculate_overall_success_rate(),
            "entity_coverage": {
                entity: stats.get('transformed', 0) / stats.get('processed', 1) 
                for entity, stats in self.transformation_stats.items()
            },
            "quality_issues": {
                entity: {
                    "duplicates": stats.get('duplicates_found', 0),
                    "outliers": stats.get('outliers_detected', 0),
                    "failed_records": stats.get('failed', 0)
                }
                for entity, stats in self.transformation_stats.items()
            }
        }
    
    def _generate_transformation_recommendations(self) -> List[str]:
        """Generate recommendations based on transformation results."""
        recommendations = []
        
        # Check success rates
        overall_rate = self._calculate_overall_success_rate()
        if overall_rate < 0.95:
            recommendations.append(f"Overall success rate is {overall_rate:.1%}, consider reviewing failed records")
        
        # Check for high duplicate rates
        for entity, stats in self.transformation_stats.items():
            if stats.get('duplicates_found', 0) > 0:
                recommendations.append(f"High duplicate rate in {entity}, consider data cleanup")
        
        # Check for outliers in prices
        if self.transformation_stats.get('prices', {}).get('outliers_detected', 0) > 0:
            recommendations.append("Price outliers detected, review for data quality issues")
        
        return recommendations