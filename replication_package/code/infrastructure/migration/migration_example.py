#!/usr/bin/env python3
"""Example usage of the V1 to V2 migration system."""

import asyncio
import logging
from pathlib import Path
from datetime import datetime

from migration_orchestrator import (
    MigrationOrchestrator, 
    MigrationConfiguration,
    MigrationStatus
)
from migration_validator import MigrationValidator
from backup_rollback_manager import BackupRollbackManager


def setup_logging():
    """Setup logging for the example."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'migration_example_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )


def migration_progress_callback(migration_state):
    """Progress callback for migration monitoring."""
    status_symbols = {
        MigrationStatus.NOT_STARTED: "⏳",
        MigrationStatus.IN_PROGRESS: "🔄",
        MigrationStatus.COMPLETED: "✅",
        MigrationStatus.FAILED: "❌",
        MigrationStatus.PAUSED: "⏸️",
        MigrationStatus.ROLLING_BACK: "🔄",
        MigrationStatus.ROLLED_BACK: "🔙"
    }
    
    symbol = status_symbols.get(migration_state.overall_status, "❓")
    
    print(f"\r{symbol} Phase: {migration_state.current_phase.value} | "
          f"Progress: {migration_state.overall_progress_percentage:.1f}% | "
          f"Status: {migration_state.overall_status.value}", end="")
    
    if migration_state.overall_status in [MigrationStatus.COMPLETED, MigrationStatus.FAILED]:
        print()  # New line for completion


async def example_full_migration():
    """Example of complete migration process."""
    print("=== Yemen Market Integration V1 to V2 Migration Example ===\n")
    
    # Configuration
    config = MigrationConfiguration(
        migration_id=f"example_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        v1_data_path=Path("/path/to/yemen-market-integration"),  # Adjust this path
        v2_connection_string="postgresql://user:password@localhost:5432/yemen_market_v2",  # Adjust connection
        backup_storage_path=Path("./backups"),
        export_storage_path=Path("./exports"),
        
        # Performance settings
        batch_size=1000,
        parallel_workers=4,
        enable_compression=True,
        
        # Safety settings
        create_backups=True,
        enable_rollback=True,
        validate_before_import=True,
        
        # Quality requirements
        min_data_coverage=0.80,
        target_data_coverage=0.884,
        max_validation_failures=5,
        timeout_minutes=480  # 8 hours
    )
    
    print(f"Migration ID: {config.migration_id}")
    print(f"V1 Data Path: {config.v1_data_path}")
    print(f"V2 Database: {config.v2_connection_string.split('@')[-1]}")
    print(f"Target Coverage: {config.target_data_coverage:.1%}")
    print()
    
    # Create migration orchestrator
    orchestrator = MigrationOrchestrator(config)
    orchestrator.add_progress_callback(migration_progress_callback)
    
    try:
        # Execute migration
        print("Starting migration...")
        migration_state = await orchestrator.execute_migration()
        
        # Display results
        print("\n" + "="*60)
        if migration_state.overall_status == MigrationStatus.COMPLETED:
            print("✅ Migration completed successfully!")
            
            # Performance metrics
            perf = migration_state.performance_summary
            print(f"📊 Performance Summary:")
            print(f"   Duration: {perf.get('total_duration_seconds', 0) / 3600:.1f} hours")
            print(f"   Records: {perf.get('total_records_migrated', 0):,}")
            print(f"   Throughput: {perf.get('average_throughput_rps', 0):.0f} rec/s")
            print(f"   Coverage: {perf.get('data_coverage_achieved', 0):.1%}")
            
            # Data statistics
            data_stats = migration_state.data_statistics
            if 'import_results' in data_stats:
                print(f"\n📈 Data Import Summary:")
                for table, stats in data_stats['import_results'].items():
                    print(f"   {table}: {stats['imported_records']:,} records")
            
        elif migration_state.overall_status == MigrationStatus.FAILED:
            print("❌ Migration failed!")
            print("\n🔍 Error Summary:")
            for error in migration_state.error_summary[-5:]:  # Last 5 errors
                print(f"   • {error}")
            
        elif migration_state.overall_status == MigrationStatus.ROLLED_BACK:
            print("🔄 Migration was rolled back")
            print("   System restored to pre-migration state")
        
        # Save migration state
        state_file = config.export_storage_path / f"migration_state_{config.migration_id}.json"
        orchestrator.export_migration_state(state_file)
        print(f"\n💾 Migration state saved to: {state_file}")
        
        return migration_state
        
    except KeyboardInterrupt:
        print("\n⏸️ Migration interrupted by user")
        await orchestrator.cancel_migration()
        return None
    except Exception as e:
        print(f"\n💥 Migration failed with exception: {e}")
        return None


async def example_validation_only():
    """Example of running validation without migration."""
    print("=== Validation Example ===\n")
    
    connection_string = "postgresql://user:password@localhost:5432/yemen_market_v2"
    
    async with MigrationValidator(connection_string) as validator:
        print("Running quick validation...")
        quick_results = await validator.quick_validation()
        
        print(f"Status: {quick_results.get('status', 'unknown')}")
        print(f"Table Counts:")
        
        counts = quick_results.get('table_counts', {})
        for table, count in counts.items():
            print(f"   {table}: {count:,} records")
        
        coverage = quick_results.get('market_commodity_coverage', 0)
        print(f"Market-Commodity Coverage: {coverage:.1%}")
        
        orphaned = quick_results.get('orphaned_price_records', 0)
        if orphaned > 0:
            print(f"⚠️ Orphaned price records: {orphaned}")
        
        print("\nRunning comprehensive validation...")
        validation_report = await validator.validate_migration()
        
        print(f"Overall Status: {validation_report.overall_status}")
        print(f"Rules Passed: {validation_report.passed_rules}/{validation_report.total_rules}")
        
        if validation_report.critical_failures:
            print(f"❌ Critical Failures: {len(validation_report.critical_failures)}")
        
        if validation_report.warnings:
            print(f"⚠️ Warnings: {len(validation_report.warnings)}")
        
        overall_coverage = validation_report.data_coverage_report.get('overall_coverage', 0)
        print(f"Overall Data Coverage: {overall_coverage:.1%}")
        
        # Save validation report
        report_file = Path("validation_report.json")
        validator.export_validation_report(validation_report, report_file)
        print(f"\n💾 Validation report saved to: {report_file}")


async def example_backup_management():
    """Example of backup and rollback operations."""
    print("=== Backup & Rollback Example ===\n")
    
    connection_string = "postgresql://user:password@localhost:5432/yemen_market_v2"
    backup_path = Path("./backups")
    
    async with BackupRollbackManager(connection_string, backup_path) as backup_manager:
        
        # Create a backup
        print("Creating pre-migration backup...")
        backup_metadata = await backup_manager.create_pre_migration_backup()
        
        print(f"✅ Backup created: {backup_metadata.backup_id}")
        print(f"   Size: {backup_metadata.backup_size_bytes / (1024*1024):.1f} MB")
        print(f"   Duration: {backup_metadata.backup_duration_seconds:.1f} seconds")
        print(f"   Status: {backup_metadata.status}")
        
        # List all backups
        status = backup_manager.get_backup_status()
        print(f"\n📦 Backup Status:")
        print(f"   Total Backups: {status['total_backups']}")
        print(f"   Total Size: {status['total_backup_size_mb']:.1f} MB")
        
        # Create rollback plan
        print(f"\n🔄 Creating rollback plan...")
        rollback_plan = await backup_manager.create_rollback_plan("pre_migration")
        
        print(f"   Plan ID: {rollback_plan.rollback_id}")
        print(f"   Target: {rollback_plan.backup_to_restore}")
        print(f"   Estimated Duration: {rollback_plan.estimated_duration_minutes} minutes")
        print(f"   Risk Assessment: {rollback_plan.risk_assessment}")
        
        print(f"\n📋 Rollback Steps:")
        for i, step in enumerate(rollback_plan.rollback_steps, 1):
            critical = "🔴" if step.get('critical') else "🟡"
            print(f"   {i}. {critical} {step['name']} ({step.get('estimated_duration_minutes', 0)}min)")
        
        # Note: Uncomment to actually execute rollback
        # print("\n⚠️ Executing rollback (COMMENT OUT FOR SAFETY)...")
        # rollback_result = await backup_manager.execute_rollback(rollback_plan)
        # print(f"   Success: {rollback_result.success}")


async def example_monitoring_and_control():
    """Example of migration monitoring and control."""
    print("=== Migration Monitoring & Control Example ===\n")
    
    # This would be a separate process monitoring a running migration
    
    # Example of pausing migration
    # await orchestrator.pause_migration()
    # print("Migration paused")
    
    # Example of resuming migration  
    # await orchestrator.resume_migration()
    # print("Migration resumed")
    
    # Example of cancelling migration
    # await orchestrator.cancel_migration()
    # print("Migration cancelled and rolled back")
    
    # Example of getting status
    # status = orchestrator.get_migration_status()
    # print(f"Status: {status}")
    
    print("This example shows the API for migration control.")
    print("In practice, these would be called from monitoring scripts or UI.")


async def main():
    """Main example runner."""
    setup_logging()
    
    print("Yemen Market Integration V1 to V2 Migration Examples")
    print("=" * 60)
    
    examples = [
        ("Full Migration", example_full_migration),
        ("Validation Only", example_validation_only), 
        ("Backup Management", example_backup_management),
        ("Monitoring & Control", example_monitoring_and_control)
    ]
    
    for i, (name, example_func) in enumerate(examples, 1):
        print(f"\n{i}. {name}")
    
    try:
        choice = input("\nEnter example number to run (1-4), or 'q' to quit: ").strip()
        
        if choice.lower() == 'q':
            print("Exiting...")
            return
        
        choice_num = int(choice)
        if 1 <= choice_num <= len(examples):
            example_name, example_func = examples[choice_num - 1]
            print(f"\nRunning: {example_name}")
            print("-" * 40)
            
            result = await example_func()
            
            print(f"\n✅ Example completed: {example_name}")
            
        else:
            print("Invalid choice. Please enter 1-4 or 'q'.")
            
    except ValueError:
        print("Invalid input. Please enter a number or 'q'.")
    except KeyboardInterrupt:
        print("\n\nExiting...")
    except Exception as e:
        print(f"\nExample failed: {e}")
        logging.exception("Example execution failed")


if __name__ == "__main__":
    asyncio.run(main())