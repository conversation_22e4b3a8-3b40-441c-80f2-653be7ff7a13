"""Migration orchestrator with comprehensive progress tracking and management."""

import asyncio
import signal
import sys
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
import logging
from dataclasses import dataclass, asdict
import json
import time
from enum import Enum
import uuid

from src.infrastructure.observability.structured_logging import StructuredLogger
from .v1_data_exporter import V1DataExporter
from .data_transformer import DataTransformer
from .postgres_importer import PostgreSQLImporter
from .migration_validator import MigrationValidator
from .backup_rollback_manager import BackupRollbackManager


class MigrationPhase(Enum):
    """Migration phases."""
    PREPARATION = "preparation"
    PRE_BACKUP = "pre_backup"
    EXPORT = "export"
    TRANSFORM = "transform"
    IMPORT = "import"
    VALIDATION = "validation"
    POST_BACKUP = "post_backup"
    CLEANUP = "cleanup"
    COMPLETED = "completed"
    FAILED = "failed"
    ROLLED_BACK = "rolled_back"


class MigrationStatus(Enum):
    """Migration status."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    ROLLING_BACK = "rolling_back"
    ROLLED_BACK = "rolled_back"


@dataclass
class MigrationConfiguration:
    """Configuration for migration operation."""
    migration_id: str
    v1_data_path: Path
    v2_connection_string: str
    backup_storage_path: Path
    export_storage_path: Path
    
    # Processing options
    batch_size: int = 1000
    parallel_workers: int = 4
    enable_compression: bool = True
    validate_before_import: bool = True
    create_backups: bool = True
    
    # Safety options
    enable_rollback: bool = True
    max_validation_failures: int = 5
    timeout_minutes: int = 480  # 8 hours
    
    # Coverage requirements
    min_data_coverage: float = 0.80
    target_data_coverage: float = 0.884  # V1 target


@dataclass
class PhaseProgress:
    """Progress tracking for individual phase."""
    phase: MigrationPhase
    status: MigrationStatus
    start_time: Optional[datetime]
    end_time: Optional[datetime]
    progress_percentage: float
    current_operation: str
    total_items: int
    completed_items: int
    failed_items: int
    estimated_completion: Optional[datetime]
    performance_metrics: Dict[str, Any]
    error_messages: List[str]


@dataclass
class MigrationState:
    """Complete migration state."""
    migration_id: str
    configuration: MigrationConfiguration
    overall_status: MigrationStatus
    current_phase: MigrationPhase
    start_time: datetime
    end_time: Optional[datetime]
    phase_progress: Dict[MigrationPhase, PhaseProgress]
    overall_progress_percentage: float
    estimated_completion: Optional[datetime]
    data_statistics: Dict[str, Any]
    validation_results: Dict[str, Any]
    backup_ids: List[str]
    rollback_available: bool
    error_summary: List[str]
    performance_summary: Dict[str, Any]


class MigrationOrchestrator:
    """Comprehensive migration orchestrator with progress tracking."""
    
    def __init__(self, config: MigrationConfiguration):
        """Initialize the migration orchestrator."""
        self.config = config
        self.logger = StructuredLogger("MigrationOrchestrator")
        
        # Migration state
        self.migration_state = self._initialize_migration_state()
        self.is_cancelled = False
        self.pause_requested = False
        
        # Progress callbacks
        self.progress_callbacks: List[Callable] = []
        
        # Migration components
        self.exporter = None
        self.transformer = None
        self.importer = None
        self.validator = None
        self.backup_manager = None
        
        # Setup signal handlers for graceful shutdown
        self._setup_signal_handlers()
    
    def add_progress_callback(self, callback: Callable[[MigrationState], None]) -> None:
        """Add progress callback function."""
        self.progress_callbacks.append(callback)
    
    def remove_progress_callback(self, callback: Callable) -> None:
        """Remove progress callback function."""
        if callback in self.progress_callbacks:
            self.progress_callbacks.remove(callback)
    
    async def execute_migration(self) -> MigrationState:
        """Execute complete migration process."""
        self.logger.info(f"Starting migration: {self.config.migration_id}")
        
        try:
            # Update overall status
            self._update_overall_status(MigrationStatus.IN_PROGRESS)
            
            # Execute migration phases in order
            phases = [
                (MigrationPhase.PREPARATION, self._execute_preparation_phase),
                (MigrationPhase.PRE_BACKUP, self._execute_pre_backup_phase),
                (MigrationPhase.EXPORT, self._execute_export_phase),
                (MigrationPhase.TRANSFORM, self._execute_transform_phase),
                (MigrationPhase.IMPORT, self._execute_import_phase),
                (MigrationPhase.VALIDATION, self._execute_validation_phase),
                (MigrationPhase.POST_BACKUP, self._execute_post_backup_phase),
                (MigrationPhase.CLEANUP, self._execute_cleanup_phase)
            ]
            
            for phase, phase_executor in phases:
                # Check for cancellation or pause
                await self._check_execution_control()
                
                try:
                    await self._execute_phase(phase, phase_executor)
                    
                    # Check if phase failed
                    phase_progress = self.migration_state.phase_progress[phase]
                    if phase_progress.status == MigrationStatus.FAILED:
                        raise Exception(f"Phase {phase.value} failed")
                
                except Exception as e:
                    self.logger.error(f"Phase {phase.value} failed: {e}")
                    await self._handle_phase_failure(phase, str(e))
                    
                    # Determine if we should continue or abort
                    if phase in [MigrationPhase.PREPARATION, MigrationPhase.PRE_BACKUP, MigrationPhase.IMPORT]:
                        # Critical phases - abort migration
                        raise
                    else:
                        # Non-critical phases - log and continue
                        self.migration_state.error_summary.append(f"Phase {phase.value} failed: {e}")
            
            # Migration completed successfully
            self._update_overall_status(MigrationStatus.COMPLETED)
            self._update_current_phase(MigrationPhase.COMPLETED)
            
            self.logger.info(f"Migration completed successfully: {self.config.migration_id}")
            
        except Exception as e:
            self.logger.error(f"Migration failed: {e}")
            await self._handle_migration_failure(str(e))
        
        # Finalize migration state
        self.migration_state.end_time = datetime.now()
        self._calculate_final_metrics()
        self._notify_progress_callbacks()
        
        return self.migration_state
    
    async def pause_migration(self) -> None:
        """Pause migration at next safe point."""
        self.logger.info("Migration pause requested")
        self.pause_requested = True
        self._update_overall_status(MigrationStatus.PAUSED)
    
    async def resume_migration(self) -> None:
        """Resume paused migration."""
        self.logger.info("Resuming migration")
        self.pause_requested = False
        self._update_overall_status(MigrationStatus.IN_PROGRESS)
    
    async def cancel_migration(self) -> None:
        """Cancel migration and initiate rollback if configured."""
        self.logger.info("Migration cancellation requested")
        self.is_cancelled = True
        
        if self.config.enable_rollback and self.migration_state.rollback_available:
            await self._execute_rollback()
        else:
            self._update_overall_status(MigrationStatus.FAILED)
    
    async def _execute_phase(
        self, 
        phase: MigrationPhase, 
        phase_executor: Callable
    ) -> None:
        """Execute a single migration phase."""
        self.logger.info(f"Starting phase: {phase.value}")
        
        # Initialize phase progress
        self._initialize_phase_progress(phase)
        self._update_current_phase(phase)
        
        try:
            # Execute phase
            await phase_executor()
            
            # Mark phase as completed
            phase_progress = self.migration_state.phase_progress[phase]
            phase_progress.status = MigrationStatus.COMPLETED
            phase_progress.end_time = datetime.now()
            phase_progress.progress_percentage = 100.0
            
            self.logger.info(f"Phase completed: {phase.value}")
            
        except Exception as e:
            # Mark phase as failed
            phase_progress = self.migration_state.phase_progress[phase]
            phase_progress.status = MigrationStatus.FAILED
            phase_progress.end_time = datetime.now()
            phase_progress.error_messages.append(str(e))
            
            self.logger.error(f"Phase failed: {phase.value} - {e}")
            raise
        
        finally:
            self._update_overall_progress()
            self._notify_progress_callbacks()
    
    async def _execute_preparation_phase(self) -> None:
        """Execute preparation phase."""
        phase_progress = self.migration_state.phase_progress[MigrationPhase.PREPARATION]
        phase_progress.current_operation = "Initializing migration components"
        
        # Initialize migration components
        self.exporter = V1DataExporter(
            v1_data_path=self.config.v1_data_path,
            export_path=self.config.export_storage_path,
            compress_output=self.config.enable_compression,
            parallel_workers=self.config.parallel_workers
        )
        
        self.transformer = DataTransformer()
        
        self.importer = PostgreSQLImporter(
            connection_string=self.config.v2_connection_string
        )
        
        self.validator = MigrationValidator(
            v2_connection_string=self.config.v2_connection_string,
            v1_data_path=self.config.v1_data_path,
            coverage_target=self.config.target_data_coverage
        )
        
        if self.config.create_backups:
            self.backup_manager = BackupRollbackManager(
                connection_string=self.config.v2_connection_string,
                backup_storage_path=self.config.backup_storage_path
            )
        
        phase_progress.progress_percentage = 50.0
        
        # Validate configuration
        phase_progress.current_operation = "Validating configuration"
        await self._validate_migration_configuration()
        
        # Create necessary directories
        phase_progress.current_operation = "Creating storage directories"
        self.config.export_storage_path.mkdir(parents=True, exist_ok=True)
        self.config.backup_storage_path.mkdir(parents=True, exist_ok=True)
        
        phase_progress.progress_percentage = 100.0
    
    async def _execute_pre_backup_phase(self) -> None:
        """Execute pre-migration backup phase."""
        if not self.config.create_backups:
            return
        
        phase_progress = self.migration_state.phase_progress[MigrationPhase.PRE_BACKUP]
        phase_progress.current_operation = "Creating pre-migration backup"
        
        async with self.backup_manager:
            backup_metadata = await self.backup_manager.create_pre_migration_backup()
            self.migration_state.backup_ids.append(backup_metadata.backup_id)
            self.migration_state.rollback_available = True
            
            phase_progress.current_operation = f"Backup created: {backup_metadata.backup_id}"
            phase_progress.progress_percentage = 100.0
    
    async def _execute_export_phase(self) -> None:
        """Execute data export phase."""
        phase_progress = self.migration_state.phase_progress[MigrationPhase.EXPORT]
        phase_progress.current_operation = "Exporting V1 data"
        
        # Setup progress tracking for export
        def export_progress_callback(export_type: str, progress: float):
            phase_progress.current_operation = f"Exporting {export_type}"
            phase_progress.progress_percentage = progress
            self._notify_progress_callbacks()
        
        # Execute export
        export_results = self.exporter.export_all()
        
        # Store export statistics
        self.migration_state.data_statistics['export_results'] = {
            export_type: {
                'record_count': result.record_count,
                'file_size_bytes': result.file_size_bytes,
                'validation_passed': result.validation_passed
            }
            for export_type, result in export_results.items()
        }
        
        # Check for export failures
        failed_exports = [
            export_type for export_type, result in export_results.items()
            if not result.validation_passed
        ]
        
        if failed_exports:
            raise Exception(f"Export validation failed for: {', '.join(failed_exports)}")
        
        phase_progress.progress_percentage = 100.0
    
    async def _execute_transform_phase(self) -> None:
        """Execute data transformation phase."""
        phase_progress = self.migration_state.phase_progress[MigrationPhase.TRANSFORM]
        phase_progress.current_operation = "Transforming data to V2 format"
        
        # Get export file paths
        export_files = {
            'markets': self.config.export_storage_path / 'market_data_*.parquet',
            'commodities': self.config.export_storage_path / 'commodity_data_*.parquet',
            'prices': self.config.export_storage_path / 'price_data_*.parquet',
            'conflicts': self.config.export_storage_path / 'conflict_data_*.parquet',
            'analysis_results': self.config.export_storage_path / 'analysis_results_*.parquet'
        }
        
        transformation_results = {}
        total_transformations = len(export_files)
        
        for i, (data_type, file_pattern) in enumerate(export_files.items()):
            # Find actual file (glob pattern)
            matching_files = list(self.config.export_storage_path.glob(file_pattern.name))
            if not matching_files:
                continue
            
            file_path = matching_files[0]  # Take first match
            
            phase_progress.current_operation = f"Transforming {data_type}"
            
            # Load and transform data
            if file_path.suffix == '.parquet':
                import pandas as pd
                df = pd.read_parquet(file_path)
            else:
                continue
            
            # Apply transformation based on data type
            if data_type == 'markets':
                transformed_df, result = self.transformer.transform_markets(df)
            elif data_type == 'commodities':
                transformed_df, result = self.transformer.transform_commodities(df)
            elif data_type == 'prices':
                transformed_df, result = self.transformer.transform_prices(df)
            elif data_type == 'conflicts':
                transformed_df, result = self.transformer.transform_conflicts(df)
            elif data_type == 'analysis_results':
                transformed_df, result = self.transformer.transform_analysis_results(df)
            else:
                continue
            
            # Save transformed data
            output_path = self.config.export_storage_path / f"transformed_{data_type}.parquet"
            transformed_df.to_parquet(output_path)
            
            transformation_results[data_type] = result
            
            # Update progress
            progress = (i + 1) / total_transformations * 100
            phase_progress.progress_percentage = progress
            self._notify_progress_callbacks()
        
        # Store transformation statistics
        self.migration_state.data_statistics['transformation_results'] = {
            data_type: {
                'records_processed': result.records_processed,
                'records_transformed': result.records_transformed,
                'records_failed': result.records_failed,
                'success': result.success
            }
            for data_type, result in transformation_results.items()
        }
        
        # Check for transformation failures
        failed_transformations = [
            data_type for data_type, result in transformation_results.items()
            if not result.success
        ]
        
        if failed_transformations:
            raise Exception(f"Transformation failed for: {', '.join(failed_transformations)}")
    
    async def _execute_import_phase(self) -> None:
        """Execute data import phase."""
        phase_progress = self.migration_state.phase_progress[MigrationPhase.IMPORT]
        phase_progress.current_operation = "Importing data to V2 database"
        
        # Setup progress tracking for import
        def import_progress_callback(import_progress):
            phase_progress.current_operation = f"Importing {import_progress.table_name}"
            table_progress = (import_progress.imported_records / import_progress.total_records * 100 
                            if import_progress.total_records > 0 else 0)
            phase_progress.progress_percentage = table_progress
            self._notify_progress_callbacks()
        
        # Get transformed data files
        transformed_files = {
            'markets': self.config.export_storage_path / 'transformed_markets.parquet',
            'commodities': self.config.export_storage_path / 'transformed_commodities.parquet',
            'price_observations': self.config.export_storage_path / 'transformed_prices.parquet',
            'conflict_events': self.config.export_storage_path / 'transformed_conflicts.parquet',
            'analysis_results': self.config.export_storage_path / 'transformed_analysis_results.parquet'
        }
        
        # Filter to existing files
        existing_files = {
            table: path for table, path in transformed_files.items()
            if path.exists()
        }
        
        # Execute import
        async with self.importer:
            import_results = await self.importer.import_all_data(
                existing_files,
                progress_callback=import_progress_callback
            )
        
        # Store import statistics
        self.migration_state.data_statistics['import_results'] = {
            table: {
                'total_records': result.total_records,
                'imported_records': result.imported_records,
                'failed_records': result.failed_records,
                'success': result.success,
                'throughput_rps': result.throughput_records_per_second
            }
            for table, result in import_results.items()
        }
        
        # Check for import failures
        failed_imports = [
            table for table, result in import_results.items()
            if not result.success
        ]
        
        if failed_imports:
            raise Exception(f"Import failed for tables: {', '.join(failed_imports)}")
        
        phase_progress.progress_percentage = 100.0
    
    async def _execute_validation_phase(self) -> None:
        """Execute migration validation phase."""
        phase_progress = self.migration_state.phase_progress[MigrationPhase.VALIDATION]
        phase_progress.current_operation = "Validating migrated data"
        
        async with self.validator:
            # Run comprehensive validation
            validation_report = await self.validator.validate_migration()
            
            # Store validation results
            self.migration_state.validation_results = {
                'overall_status': validation_report.overall_status,
                'total_rules': validation_report.total_rules,
                'passed_rules': validation_report.passed_rules,
                'failed_rules': validation_report.failed_rules,
                'critical_failures': len(validation_report.critical_failures),
                'data_coverage': validation_report.data_coverage_report,
                'recommendations': validation_report.recommendations
            }
            
            # Check if validation passed
            if validation_report.overall_status == 'failed':
                critical_count = len(validation_report.critical_failures)
                if critical_count > self.config.max_validation_failures:
                    raise Exception(f"Validation failed with {critical_count} critical failures")
            
            # Check data coverage
            overall_coverage = validation_report.data_coverage_report.get('overall_coverage', 0)
            if overall_coverage < self.config.min_data_coverage:
                raise Exception(
                    f"Data coverage ({overall_coverage:.1%}) below minimum requirement "
                    f"({self.config.min_data_coverage:.1%})"
                )
        
        phase_progress.progress_percentage = 100.0
    
    async def _execute_post_backup_phase(self) -> None:
        """Execute post-migration backup phase."""
        if not self.config.create_backups:
            return
        
        phase_progress = self.migration_state.phase_progress[MigrationPhase.POST_BACKUP]
        phase_progress.current_operation = "Creating post-migration backup"
        
        async with self.backup_manager:
            backup_metadata = await self.backup_manager.create_post_migration_backup()
            self.migration_state.backup_ids.append(backup_metadata.backup_id)
            
            phase_progress.current_operation = f"Backup created: {backup_metadata.backup_id}"
            phase_progress.progress_percentage = 100.0
    
    async def _execute_cleanup_phase(self) -> None:
        """Execute cleanup phase."""
        phase_progress = self.migration_state.phase_progress[MigrationPhase.CLEANUP]
        phase_progress.current_operation = "Cleaning up temporary files"
        
        # Clean up export files if requested
        if hasattr(self.config, 'cleanup_exports') and self.config.cleanup_exports:
            for export_file in self.config.export_storage_path.glob("*.parquet"):
                if export_file.name.startswith(('export_', 'transformed_')):
                    export_file.unlink()
        
        # Generate final migration report
        phase_progress.current_operation = "Generating migration report"
        await self._generate_migration_report()
        
        phase_progress.progress_percentage = 100.0
    
    async def _handle_phase_failure(self, phase: MigrationPhase, error_message: str) -> None:
        """Handle phase failure."""
        self.migration_state.error_summary.append(f"Phase {phase.value} failed: {error_message}")
        
        # Update phase status
        phase_progress = self.migration_state.phase_progress[phase]
        phase_progress.status = MigrationStatus.FAILED
        phase_progress.end_time = datetime.now()
        phase_progress.error_messages.append(error_message)
    
    async def _handle_migration_failure(self, error_message: str) -> None:
        """Handle overall migration failure."""
        self.logger.error(f"Migration failed: {error_message}")
        
        self._update_overall_status(MigrationStatus.FAILED)
        self._update_current_phase(MigrationPhase.FAILED)
        self.migration_state.error_summary.append(f"Migration failed: {error_message}")
        
        # Attempt rollback if configured
        if self.config.enable_rollback and self.migration_state.rollback_available:
            await self._execute_rollback()
    
    async def _execute_rollback(self) -> None:
        """Execute rollback operation."""
        self.logger.info("Initiating rollback")
        
        try:
            self._update_overall_status(MigrationStatus.ROLLING_BACK)
            
            if self.backup_manager:
                async with self.backup_manager:
                    # Create rollback plan
                    rollback_plan = await self.backup_manager.create_rollback_plan("pre_migration")
                    
                    # Execute rollback
                    rollback_result = await self.backup_manager.execute_rollback(rollback_plan)
                    
                    if rollback_result.success:
                        self._update_overall_status(MigrationStatus.ROLLED_BACK)
                        self._update_current_phase(MigrationPhase.ROLLED_BACK)
                        self.logger.info("Rollback completed successfully")
                    else:
                        self.logger.error("Rollback failed")
                        self.migration_state.error_summary.extend(rollback_result.error_messages)
        
        except Exception as e:
            self.logger.error(f"Rollback execution failed: {e}")
            self.migration_state.error_summary.append(f"Rollback failed: {e}")
    
    async def _check_execution_control(self) -> None:
        """Check for pause or cancellation requests."""
        if self.is_cancelled:
            raise Exception("Migration cancelled by user")
        
        while self.pause_requested:
            await asyncio.sleep(1)  # Wait while paused
    
    async def _validate_migration_configuration(self) -> None:
        """Validate migration configuration."""
        # Check V1 data path exists
        if not self.config.v1_data_path.exists():
            raise ValueError(f"V1 data path does not exist: {self.config.v1_data_path}")
        
        # Test V2 database connection
        try:
            async with self.validator:
                health = await self.validator.connection_pool.fetchval("SELECT 1")
                if health != 1:
                    raise ValueError("V2 database connection test failed")
        except Exception as e:
            raise ValueError(f"Cannot connect to V2 database: {e}")
        
        # Validate storage paths are writable
        test_file = self.config.export_storage_path / "test_write.tmp"
        try:
            test_file.touch()
            test_file.unlink()
        except Exception as e:
            raise ValueError(f"Export storage path not writable: {e}")
    
    def _initialize_migration_state(self) -> MigrationState:
        """Initialize migration state."""
        # Initialize phase progress for all phases
        phase_progress = {}
        for phase in MigrationPhase:
            if phase not in [MigrationPhase.COMPLETED, MigrationPhase.FAILED, MigrationPhase.ROLLED_BACK]:
                phase_progress[phase] = PhaseProgress(
                    phase=phase,
                    status=MigrationStatus.NOT_STARTED,
                    start_time=None,
                    end_time=None,
                    progress_percentage=0.0,
                    current_operation="",
                    total_items=0,
                    completed_items=0,
                    failed_items=0,
                    estimated_completion=None,
                    performance_metrics={},
                    error_messages=[]
                )
        
        return MigrationState(
            migration_id=self.config.migration_id,
            configuration=self.config,
            overall_status=MigrationStatus.NOT_STARTED,
            current_phase=MigrationPhase.PREPARATION,
            start_time=datetime.now(),
            end_time=None,
            phase_progress=phase_progress,
            overall_progress_percentage=0.0,
            estimated_completion=None,
            data_statistics={},
            validation_results={},
            backup_ids=[],
            rollback_available=False,
            error_summary=[],
            performance_summary={}
        )
    
    def _initialize_phase_progress(self, phase: MigrationPhase) -> None:
        """Initialize progress tracking for a phase."""
        phase_progress = self.migration_state.phase_progress[phase]
        phase_progress.status = MigrationStatus.IN_PROGRESS
        phase_progress.start_time = datetime.now()
        phase_progress.progress_percentage = 0.0
        phase_progress.error_messages = []
    
    def _update_overall_status(self, status: MigrationStatus) -> None:
        """Update overall migration status."""
        self.migration_state.overall_status = status
        self._notify_progress_callbacks()
    
    def _update_current_phase(self, phase: MigrationPhase) -> None:
        """Update current migration phase."""
        self.migration_state.current_phase = phase
        self._notify_progress_callbacks()
    
    def _update_overall_progress(self) -> None:
        """Update overall migration progress."""
        completed_phases = sum(
            1 for progress in self.migration_state.phase_progress.values()
            if progress.status == MigrationStatus.COMPLETED
        )
        total_phases = len(self.migration_state.phase_progress)
        
        self.migration_state.overall_progress_percentage = (completed_phases / total_phases) * 100
        
        # Estimate completion time
        if completed_phases > 0:
            elapsed_time = datetime.now() - self.migration_state.start_time
            estimated_total_time = elapsed_time * (total_phases / completed_phases)
            self.migration_state.estimated_completion = self.migration_state.start_time + estimated_total_time
    
    def _notify_progress_callbacks(self) -> None:
        """Notify all progress callbacks."""
        for callback in self.progress_callbacks:
            try:
                callback(self.migration_state)
            except Exception as e:
                self.logger.warning(f"Progress callback failed: {e}")
    
    def _calculate_final_metrics(self) -> None:
        """Calculate final performance metrics."""
        if self.migration_state.end_time:
            total_duration = (self.migration_state.end_time - self.migration_state.start_time).total_seconds()
            
            # Calculate total records processed
            import_results = self.migration_state.data_statistics.get('import_results', {})
            total_records = sum(
                result.get('imported_records', 0) 
                for result in import_results.values()
            )
            
            self.migration_state.performance_summary = {
                'total_duration_seconds': total_duration,
                'total_records_migrated': total_records,
                'average_throughput_rps': total_records / total_duration if total_duration > 0 else 0,
                'successful_phases': sum(
                    1 for progress in self.migration_state.phase_progress.values()
                    if progress.status == MigrationStatus.COMPLETED
                ),
                'failed_phases': sum(
                    1 for progress in self.migration_state.phase_progress.values()
                    if progress.status == MigrationStatus.FAILED
                ),
                'data_coverage_achieved': self.migration_state.validation_results.get(
                    'data_coverage', {}
                ).get('overall_coverage', 0)
            }
    
    async def _generate_migration_report(self) -> None:
        """Generate comprehensive migration report."""
        report = {
            'migration_summary': {
                'migration_id': self.migration_state.migration_id,
                'status': self.migration_state.overall_status.value,
                'start_time': self.migration_state.start_time.isoformat(),
                'end_time': self.migration_state.end_time.isoformat() if self.migration_state.end_time else None,
                'duration_hours': ((self.migration_state.end_time or datetime.now()) - self.migration_state.start_time).total_seconds() / 3600
            },
            'phase_summary': {
                phase.value: {
                    'status': progress.status.value,
                    'duration_minutes': ((progress.end_time or datetime.now()) - (progress.start_time or datetime.now())).total_seconds() / 60 if progress.start_time else 0,
                    'error_count': len(progress.error_messages)
                }
                for phase, progress in self.migration_state.phase_progress.items()
            },
            'data_statistics': self.migration_state.data_statistics,
            'validation_results': self.migration_state.validation_results,
            'performance_summary': self.migration_state.performance_summary,
            'backup_ids': self.migration_state.backup_ids,
            'error_summary': self.migration_state.error_summary,
            'recommendations': self._generate_migration_recommendations()
        }
        
        # Save report
        report_path = self.config.export_storage_path / f"migration_report_{self.config.migration_id}.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        self.logger.info(f"Migration report saved: {report_path}")
    
    def _generate_migration_recommendations(self) -> List[str]:
        """Generate recommendations based on migration results."""
        recommendations = []
        
        if self.migration_state.overall_status == MigrationStatus.COMPLETED:
            recommendations.append("Migration completed successfully")
            
            # Performance recommendations
            performance = self.migration_state.performance_summary
            if performance.get('average_throughput_rps', 0) < 100:
                recommendations.append("Consider optimizing batch sizes or parallel processing for better performance")
            
            # Coverage recommendations
            coverage = performance.get('data_coverage_achieved', 0)
            if coverage < self.config.target_data_coverage:
                recommendations.append(f"Data coverage ({coverage:.1%}) is below target ({self.config.target_data_coverage:.1%})")
        
        elif self.migration_state.overall_status == MigrationStatus.FAILED:
            recommendations.append("Migration failed - review error messages and retry")
            recommendations.append("Consider adjusting configuration or fixing data quality issues")
        
        elif self.migration_state.overall_status == MigrationStatus.ROLLED_BACK:
            recommendations.append("Migration was rolled back - system restored to pre-migration state")
            recommendations.append("Address identified issues before retrying migration")
        
        return recommendations
    
    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, initiating graceful shutdown")
            asyncio.create_task(self.cancel_migration())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def get_migration_status(self) -> Dict[str, Any]:
        """Get current migration status."""
        return {
            'migration_id': self.migration_state.migration_id,
            'overall_status': self.migration_state.overall_status.value,
            'current_phase': self.migration_state.current_phase.value,
            'overall_progress_percentage': self.migration_state.overall_progress_percentage,
            'estimated_completion': self.migration_state.estimated_completion.isoformat() if self.migration_state.estimated_completion else None,
            'rollback_available': self.migration_state.rollback_available,
            'error_count': len(self.migration_state.error_summary)
        }
    
    def export_migration_state(self, output_path: Path) -> None:
        """Export complete migration state to file."""
        try:
            # Convert to serializable format
            state_dict = asdict(self.migration_state)
            
            # Convert datetime objects and enums
            def convert_datetime_enum(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                elif isinstance(obj, (MigrationStatus, MigrationPhase)):
                    return obj.value
                elif isinstance(obj, dict):
                    return {k: convert_datetime_enum(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_datetime_enum(item) for item in obj]
                else:
                    return obj
            
            serializable_state = convert_datetime_enum(state_dict)
            
            # Write to file
            with open(output_path, 'w') as f:
                json.dump(serializable_state, f, indent=2, default=str)
            
            self.logger.info(f"Migration state exported to {output_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to export migration state: {e}")
            raise