"""PostgreSQL import utilities with batch processing and performance optimization."""

import asyncio
import asyncpg
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, AsyncGenerator
import logging
from dataclasses import dataclass, asdict
import json
import time
import psutil
from concurrent.futures import ThreadPoolExecutor
import uuid

from src.infrastructure.observability.structured_logging import StructuredLogger
from src.core.domain.shared.exceptions import ValidationError


@dataclass
class ImportConfiguration:
    """Configuration for import operations."""
    batch_size: int = 1000
    max_concurrent_batches: int = 5
    connection_pool_size: int = 10
    timeout_seconds: int = 300
    retry_attempts: int = 3
    enable_parallel_processing: bool = True
    validate_before_import: bool = True
    create_indexes_after_import: bool = True
    vacuum_after_import: bool = True


@dataclass
class ImportProgress:
    """Progress tracking for import operations."""
    table_name: str
    total_records: int
    imported_records: int
    failed_records: int
    current_batch: int
    total_batches: int
    start_time: datetime
    estimated_completion: Optional[datetime]
    throughput_records_per_second: float
    last_update: datetime


@dataclass
class ImportResult:
    """Result of an import operation."""
    table_name: str
    success: bool
    total_records: int
    imported_records: int
    failed_records: int
    skipped_records: int
    duration_seconds: float
    throughput_records_per_second: float
    validation_errors: List[str]
    performance_metrics: Dict[str, Any]
    final_table_stats: Dict[str, Any]


class PostgreSQLImporter:
    """High-performance PostgreSQL data importer with batch processing."""
    
    def __init__(
        self, 
        connection_string: str,
        config: Optional[ImportConfiguration] = None
    ):
        """Initialize the PostgreSQL importer."""
        self.connection_string = connection_string
        self.config = config or ImportConfiguration()
        self.logger = StructuredLogger("PostgreSQLImporter")
        
        # Connection pool and performance tracking
        self.connection_pool = None
        self.import_progress = {}
        self.performance_metrics = {}
        
        # Batch processing queues
        self.import_queues = {}
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self._initialize_connection_pool()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self._cleanup_connection_pool()
    
    async def _initialize_connection_pool(self):
        """Initialize connection pool."""
        try:
            self.connection_pool = await asyncpg.create_pool(
                self.connection_string,
                min_size=2,
                max_size=self.config.connection_pool_size,
                command_timeout=self.config.timeout_seconds
            )
            self.logger.info("Database connection pool initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize connection pool: {e}")
            raise
    
    async def _cleanup_connection_pool(self):
        """Cleanup connection pool."""
        if self.connection_pool:
            await self.connection_pool.close()
            self.logger.info("Database connection pool closed")
    
    async def import_all_data(
        self, 
        data_files: Dict[str, Path],
        progress_callback: Optional[callable] = None
    ) -> Dict[str, ImportResult]:
        """Import all data files to PostgreSQL."""
        self.logger.info("Starting comprehensive data import")
        
        # Import order (respecting foreign key dependencies)
        import_order = [
            ('commodities', 'import_commodities'),
            ('markets', 'import_markets'),
            ('price_observations', 'import_prices'),
            ('conflict_events', 'import_conflicts'),
            ('analysis_results', 'import_analysis_results')
        ]
        
        results = {}
        overall_start_time = datetime.now()
        
        try:
            # Prepare database for bulk import
            await self._prepare_database_for_import()
            
            # Import data in dependency order
            for table_name, import_method in import_order:
                if table_name in data_files:
                    data_file = data_files[table_name]
                    
                    if data_file.exists():
                        self.logger.info(f"Importing {table_name} from {data_file}")
                        
                        # Call appropriate import method
                        import_func = getattr(self, import_method)
                        result = await import_func(data_file, progress_callback)
                        results[table_name] = result
                        
                        if not result.success:
                            self.logger.error(f"Import failed for {table_name}")
                            # Continue with other tables but log the failure
                    else:
                        self.logger.warning(f"Data file not found for {table_name}: {data_file}")
                        results[table_name] = ImportResult(
                            table_name=table_name,
                            success=False,
                            total_records=0,
                            imported_records=0,
                            failed_records=0,
                            skipped_records=0,
                            duration_seconds=0,
                            throughput_records_per_second=0,
                            validation_errors=[f"Data file not found: {data_file}"],
                            performance_metrics={},
                            final_table_stats={}
                        )
            
            # Post-import optimizations
            await self._post_import_optimizations()
            
            # Calculate overall metrics
            total_duration = (datetime.now() - overall_start_time).total_seconds()
            total_imported = sum(r.imported_records for r in results.values())
            
            self.logger.info(
                f"Import completed: {total_imported} records in {total_duration:.2f}s "
                f"({total_imported/total_duration:.0f} rec/s)"
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Import process failed: {e}")
            raise
    
    async def import_commodities(
        self, 
        data_file: Path,
        progress_callback: Optional[callable] = None
    ) -> ImportResult:
        """Import commodity data."""
        self.logger.info(f"Importing commodities from {data_file}")
        
        start_time = datetime.now()
        
        try:
            # Load data
            df = self._load_data_file(data_file)
            total_records = len(df)
            
            if total_records == 0:
                return self._create_empty_import_result("commodities")
            
            # Validate data structure
            required_columns = ['code', 'name', 'category', 'standard_unit']
            self._validate_data_structure(df, required_columns, "commodities")
            
            # Prepare insert statement
            insert_query = """
                INSERT INTO commodities (code, name, category, standard_unit, created_at)
                VALUES ($1, $2, $3, $4, $5)
                ON CONFLICT (code) DO UPDATE SET
                    name = EXCLUDED.name,
                    category = EXCLUDED.category,
                    standard_unit = EXCLUDED.standard_unit
            """
            
            # Process in batches
            result = await self._batch_import(
                df, 
                insert_query, 
                self._prepare_commodity_batch,
                "commodities",
                progress_callback
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Commodity import failed: {e}")
            return self._create_failed_import_result("commodities", str(e))
    
    async def import_markets(
        self, 
        data_file: Path,
        progress_callback: Optional[callable] = None
    ) -> ImportResult:
        """Import market data."""
        self.logger.info(f"Importing markets from {data_file}")
        
        start_time = datetime.now()
        
        try:
            # Load data
            df = self._load_data_file(data_file)
            total_records = len(df)
            
            if total_records == 0:
                return self._create_empty_import_result("markets")
            
            # Validate data structure
            required_columns = ['market_id', 'name', 'latitude', 'longitude', 'governorate']
            self._validate_data_structure(df, required_columns, "markets")
            
            # Prepare insert statement
            insert_query = """
                INSERT INTO markets (
                    id, market_id, name, latitude, longitude, market_type,
                    governorate, district, active_since, active_until,
                    version, created_at, updated_at
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                ON CONFLICT (market_id) DO UPDATE SET
                    name = EXCLUDED.name,
                    latitude = EXCLUDED.latitude,
                    longitude = EXCLUDED.longitude,
                    market_type = EXCLUDED.market_type,
                    governorate = EXCLUDED.governorate,
                    district = EXCLUDED.district,
                    updated_at = EXCLUDED.updated_at
            """
            
            # Process in batches
            result = await self._batch_import(
                df,
                insert_query,
                self._prepare_market_batch,
                "markets",
                progress_callback
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Market import failed: {e}")
            return self._create_failed_import_result("markets", str(e))
    
    async def import_prices(
        self, 
        data_file: Path,
        progress_callback: Optional[callable] = None
    ) -> ImportResult:
        """Import price observation data."""
        self.logger.info(f"Importing prices from {data_file}")
        
        start_time = datetime.now()
        
        try:
            # Load data
            df = self._load_data_file(data_file)
            total_records = len(df)
            
            if total_records == 0:
                return self._create_empty_import_result("price_observations")
            
            # Validate data structure
            required_columns = [
                'market_id', 'commodity_code', 'price_amount', 
                'price_currency', 'observed_date'
            ]
            self._validate_data_structure(df, required_columns, "price_observations")
            
            # Prepare insert statement
            insert_query = """
                INSERT INTO price_observations (
                    id, market_id, commodity_code, price_amount, price_currency,
                    price_unit, observed_date, source, quality, 
                    observations_count, created_at
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                ON CONFLICT (market_id, commodity_code, observed_date, source) 
                DO UPDATE SET
                    price_amount = EXCLUDED.price_amount,
                    price_currency = EXCLUDED.price_currency,
                    price_unit = EXCLUDED.price_unit,
                    quality = EXCLUDED.quality,
                    observations_count = EXCLUDED.observations_count
            """
            
            # Process in batches (larger batches for price data)
            original_batch_size = self.config.batch_size
            self.config.batch_size = min(5000, max(1000, total_records // 100))
            
            try:
                result = await self._batch_import(
                    df,
                    insert_query,
                    self._prepare_price_batch,
                    "price_observations",
                    progress_callback
                )
            finally:
                self.config.batch_size = original_batch_size
            
            return result
            
        except Exception as e:
            self.logger.error(f"Price import failed: {e}")
            return self._create_failed_import_result("price_observations", str(e))
    
    async def import_conflicts(
        self, 
        data_file: Path,
        progress_callback: Optional[callable] = None
    ) -> ImportResult:
        """Import conflict events data."""
        self.logger.info(f"Importing conflicts from {data_file}")
        
        start_time = datetime.now()
        
        try:
            # Load data
            df = self._load_data_file(data_file)
            total_records = len(df)
            
            if total_records == 0:
                return self._create_empty_import_result("conflict_events")
            
            # Validate data structure
            required_columns = ['event_id', 'event_type', 'event_date', 'latitude', 'longitude']
            self._validate_data_structure(df, required_columns, "conflict_events")
            
            # Prepare insert statement
            insert_query = """
                INSERT INTO conflict_events (
                    id, event_id, event_type, sub_event_type, event_date,
                    latitude, longitude, governorate, district, fatalities,
                    notes, source, source_scale, created_at
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                ON CONFLICT (event_id) DO UPDATE SET
                    event_type = EXCLUDED.event_type,
                    sub_event_type = EXCLUDED.sub_event_type,
                    event_date = EXCLUDED.event_date,
                    latitude = EXCLUDED.latitude,
                    longitude = EXCLUDED.longitude,
                    governorate = EXCLUDED.governorate,
                    district = EXCLUDED.district,
                    fatalities = EXCLUDED.fatalities,
                    notes = EXCLUDED.notes,
                    source = EXCLUDED.source,
                    source_scale = EXCLUDED.source_scale
            """
            
            # Process in batches
            result = await self._batch_import(
                df,
                insert_query,
                self._prepare_conflict_batch,
                "conflict_events",
                progress_callback
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Conflict import failed: {e}")
            return self._create_failed_import_result("conflict_events", str(e))
    
    async def import_analysis_results(
        self, 
        data_file: Path,
        progress_callback: Optional[callable] = None
    ) -> ImportResult:
        """Import analysis results data."""
        self.logger.info(f"Importing analysis results from {data_file}")
        
        start_time = datetime.now()
        
        try:
            # Load data
            df = self._load_data_file(data_file)
            total_records = len(df)
            
            if total_records == 0:
                return self._create_empty_import_result("analysis_results")
            
            # Validate data structure
            required_columns = ['analysis_type', 'results', 'status']
            self._validate_data_structure(df, required_columns, "analysis_results")
            
            # Prepare insert statement
            insert_query = """
                INSERT INTO analysis_results (
                    id, analysis_type, parameters, results, status,
                    started_at, completed_at, error_message, created_by,
                    created_at, expires_at
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                ON CONFLICT (id) DO UPDATE SET
                    analysis_type = EXCLUDED.analysis_type,
                    parameters = EXCLUDED.parameters,
                    results = EXCLUDED.results,
                    status = EXCLUDED.status,
                    completed_at = EXCLUDED.completed_at,
                    error_message = EXCLUDED.error_message
            """
            
            # Process in batches
            result = await self._batch_import(
                df,
                insert_query,
                self._prepare_analysis_batch,
                "analysis_results",
                progress_callback
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Analysis results import failed: {e}")
            return self._create_failed_import_result("analysis_results", str(e))
    
    async def _batch_import(
        self,
        df: pd.DataFrame,
        insert_query: str,
        batch_preparer: callable,
        table_name: str,
        progress_callback: Optional[callable] = None
    ) -> ImportResult:
        """Generic batch import implementation."""
        start_time = datetime.now()
        total_records = len(df)
        imported_records = 0
        failed_records = 0
        skipped_records = 0
        validation_errors = []
        
        # Calculate batches
        total_batches = (total_records + self.config.batch_size - 1) // self.config.batch_size
        
        # Initialize progress tracking
        progress = ImportProgress(
            table_name=table_name,
            total_records=total_records,
            imported_records=0,
            failed_records=0,
            current_batch=0,
            total_batches=total_batches,
            start_time=start_time,
            estimated_completion=None,
            throughput_records_per_second=0,
            last_update=start_time
        )
        self.import_progress[table_name] = progress
        
        try:
            # Process batches
            async with self.connection_pool.acquire() as connection:
                for batch_num in range(total_batches):
                    batch_start = batch_num * self.config.batch_size
                    batch_end = min(batch_start + self.config.batch_size, total_records)
                    batch_df = df.iloc[batch_start:batch_end]
                    
                    batch_start_time = time.time()
                    
                    try:
                        # Prepare batch data
                        batch_data = batch_preparer(batch_df)
                        
                        # Validate batch if enabled
                        if self.config.validate_before_import:
                            batch_validation_errors = self._validate_batch_data(batch_data, table_name)
                            if batch_validation_errors:
                                validation_errors.extend(batch_validation_errors)
                                failed_records += len(batch_df)
                                continue
                        
                        # Execute batch insert
                        await connection.executemany(insert_query, batch_data)
                        
                        imported_records += len(batch_df)
                        
                    except Exception as e:
                        self.logger.warning(f"Batch {batch_num + 1} failed for {table_name}: {e}")
                        failed_records += len(batch_df)
                        validation_errors.append(f"Batch {batch_num + 1}: {str(e)}")
                    
                    # Update progress
                    batch_duration = time.time() - batch_start_time
                    progress.current_batch = batch_num + 1
                    progress.imported_records = imported_records
                    progress.failed_records = failed_records
                    progress.last_update = datetime.now()
                    
                    # Calculate throughput
                    elapsed_time = (progress.last_update - start_time).total_seconds()
                    if elapsed_time > 0:
                        progress.throughput_records_per_second = imported_records / elapsed_time
                        
                        # Estimate completion time
                        remaining_records = total_records - imported_records - failed_records
                        if progress.throughput_records_per_second > 0:
                            remaining_seconds = remaining_records / progress.throughput_records_per_second
                            progress.estimated_completion = progress.last_update + timedelta(seconds=remaining_seconds)
                    
                    # Call progress callback
                    if progress_callback:
                        progress_callback(progress)
                    
                    # Log progress periodically
                    if batch_num % 10 == 0 or batch_num == total_batches - 1:
                        self.logger.info(
                            f"{table_name}: Batch {batch_num + 1}/{total_batches}, "
                            f"Imported: {imported_records}, Failed: {failed_records}, "
                            f"Rate: {progress.throughput_records_per_second:.0f} rec/s"
                        )
        
        except Exception as e:
            self.logger.error(f"Batch import failed for {table_name}: {e}")
            validation_errors.append(f"Import process error: {str(e)}")
        
        # Calculate final metrics
        duration = (datetime.now() - start_time).total_seconds()
        throughput = imported_records / duration if duration > 0 else 0
        
        # Get final table statistics
        final_stats = await self._get_table_statistics(table_name)
        
        # Performance metrics
        performance_metrics = {
            "batches_processed": progress.current_batch,
            "average_batch_time": duration / progress.current_batch if progress.current_batch > 0 else 0,
            "memory_usage_mb": psutil.Process().memory_info().rss / 1024 / 1024,
            "cpu_usage_percent": psutil.cpu_percent()
        }
        
        return ImportResult(
            table_name=table_name,
            success=failed_records == 0,
            total_records=total_records,
            imported_records=imported_records,
            failed_records=failed_records,
            skipped_records=skipped_records,
            duration_seconds=duration,
            throughput_records_per_second=throughput,
            validation_errors=validation_errors,
            performance_metrics=performance_metrics,
            final_table_stats=final_stats
        )
    
    # Batch preparation methods
    
    def _prepare_commodity_batch(self, batch_df: pd.DataFrame) -> List[Tuple]:
        """Prepare commodity batch data for insertion."""
        batch_data = []
        for _, row in batch_df.iterrows():
            batch_data.append((
                row['code'],
                row['name'],
                row['category'],
                row['standard_unit'],
                datetime.now()
            ))
        return batch_data
    
    def _prepare_market_batch(self, batch_df: pd.DataFrame) -> List[Tuple]:
        """Prepare market batch data for insertion."""
        batch_data = []
        for _, row in batch_df.iterrows():
            batch_data.append((
                row.get('id', str(uuid.uuid4())),
                row['market_id'],
                row['name'],
                float(row['latitude']),
                float(row['longitude']),
                row.get('market_type', 'retail'),
                row['governorate'],
                row.get('district', ''),
                pd.to_datetime(row.get('active_since', '2019-01-01')),
                pd.to_datetime(row['active_until']) if pd.notna(row.get('active_until')) else None,
                int(row.get('version', 0)),
                datetime.now(),
                datetime.now()
            ))
        return batch_data
    
    def _prepare_price_batch(self, batch_df: pd.DataFrame) -> List[Tuple]:
        """Prepare price batch data for insertion."""
        batch_data = []
        for _, row in batch_df.iterrows():
            batch_data.append((
                row.get('id', str(uuid.uuid4())),
                row['market_id'],
                row['commodity_code'],
                float(row['price_amount']),
                row['price_currency'],
                row.get('price_unit', 'kg'),
                pd.to_datetime(row['observed_date']),
                row.get('source', 'V1_MIGRATION'),
                row.get('quality', 'standard'),
                int(row.get('observations_count', 1)),
                datetime.now()
            ))
        return batch_data
    
    def _prepare_conflict_batch(self, batch_df: pd.DataFrame) -> List[Tuple]:
        """Prepare conflict batch data for insertion."""
        batch_data = []
        for _, row in batch_df.iterrows():
            batch_data.append((
                row.get('id', str(uuid.uuid4())),
                row['event_id'],
                row['event_type'],
                row.get('sub_event_type'),
                pd.to_datetime(row['event_date']),
                float(row['latitude']),
                float(row['longitude']),
                row.get('governorate', ''),
                row.get('district'),
                int(row.get('fatalities', 0)),
                row.get('notes'),
                row.get('source', 'V1_MIGRATION'),
                row.get('source_scale'),
                datetime.now()
            ))
        return batch_data
    
    def _prepare_analysis_batch(self, batch_df: pd.DataFrame) -> List[Tuple]:
        """Prepare analysis results batch data for insertion."""
        batch_data = []
        for _, row in batch_df.iterrows():
            batch_data.append((
                row.get('id', str(uuid.uuid4())),
                row['analysis_type'],
                row.get('parameters', '{}'),
                row['results'],
                row['status'],
                pd.to_datetime(row.get('started_at', datetime.now())),
                pd.to_datetime(row.get('completed_at', datetime.now())),
                row.get('error_message'),
                row.get('created_by', 'V1_MIGRATION'),
                datetime.now(),
                pd.to_datetime(row.get('expires_at', datetime.now() + timedelta(days=365)))
            ))
        return batch_data
    
    # Utility methods
    
    def _load_data_file(self, data_file: Path) -> pd.DataFrame:
        """Load data file (supports parquet, CSV, JSON)."""
        try:
            if data_file.suffix.lower() == '.parquet':
                return pd.read_parquet(data_file)
            elif data_file.suffix.lower() == '.csv':
                return pd.read_csv(data_file)
            elif data_file.suffix.lower() == '.json':
                return pd.read_json(data_file, orient='records')
            else:
                raise ValueError(f"Unsupported file format: {data_file.suffix}")
        except Exception as e:
            self.logger.error(f"Failed to load data file {data_file}: {e}")
            raise
    
    def _validate_data_structure(self, df: pd.DataFrame, required_columns: List[str], table_name: str):
        """Validate that dataframe has required columns."""
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValidationError(f"Missing required columns for {table_name}: {missing_columns}")
    
    def _validate_batch_data(self, batch_data: List[Tuple], table_name: str) -> List[str]:
        """Validate batch data before import."""
        errors = []
        
        # Basic validation - check for None values in required positions
        for i, row in enumerate(batch_data):
            if table_name == "commodities":
                if not row[0] or not row[1]:  # code, name
                    errors.append(f"Row {i}: Missing code or name")
            elif table_name == "markets":
                if not row[1] or not row[2]:  # market_id, name
                    errors.append(f"Row {i}: Missing market_id or name")
            elif table_name == "price_observations":
                if not row[1] or not row[2] or row[3] <= 0:  # market_id, commodity_code, price_amount
                    errors.append(f"Row {i}: Missing or invalid required fields")
            elif table_name == "conflict_events":
                if not row[1] or not row[2]:  # event_id, event_type
                    errors.append(f"Row {i}: Missing event_id or event_type")
            elif table_name == "analysis_results":
                if not row[1] or not row[3]:  # analysis_type, results
                    errors.append(f"Row {i}: Missing analysis_type or results")
        
        return errors
    
    async def _prepare_database_for_import(self):
        """Prepare database for bulk import operations."""
        async with self.connection_pool.acquire() as connection:
            # Disable foreign key checks temporarily for better performance
            await connection.execute("SET foreign_key_checks = 0")
            
            # Adjust PostgreSQL settings for bulk import
            await connection.execute("SET synchronous_commit = OFF")
            await connection.execute("SET wal_buffers = '16MB'")
            await connection.execute("SET checkpoint_segments = 32")
            
            self.logger.info("Database prepared for bulk import")
    
    async def _post_import_optimizations(self):
        """Perform post-import optimizations."""
        async with self.connection_pool.acquire() as connection:
            # Re-enable foreign key checks
            await connection.execute("SET foreign_key_checks = 1")
            
            # Reset PostgreSQL settings
            await connection.execute("SET synchronous_commit = ON")
            
            # Create indexes if enabled
            if self.config.create_indexes_after_import:
                await self._create_performance_indexes(connection)
            
            # Vacuum tables if enabled
            if self.config.vacuum_after_import:
                await self._vacuum_tables(connection)
            
            # Update table statistics
            await connection.execute("ANALYZE")
            
            self.logger.info("Post-import optimizations completed")
    
    async def _create_performance_indexes(self, connection):
        """Create performance indexes after import."""
        indexes = [
            # Price observations indexes
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_prices_market_date ON price_observations(market_id, observed_date)",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_prices_commodity_date ON price_observations(commodity_code, observed_date)",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_prices_date_range ON price_observations(observed_date) WHERE observed_date >= '2019-01-01'",
            
            # Market indexes
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_markets_governorate_active ON markets(governorate) WHERE active_until IS NULL",
            
            # Conflict indexes
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_conflicts_date_range ON conflict_events(event_date) WHERE event_date >= '2019-01-01'",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_conflicts_governorate_date ON conflict_events(governorate, event_date)",
            
            # Analysis results indexes
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analysis_type_created ON analysis_results(analysis_type, created_at)",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analysis_status_active ON analysis_results(status) WHERE expires_at > NOW()"
        ]
        
        for index_sql in indexes:
            try:
                await connection.execute(index_sql)
                self.logger.info(f"Created index: {index_sql.split()[-1]}")
            except Exception as e:
                self.logger.warning(f"Failed to create index: {e}")
    
    async def _vacuum_tables(self, connection):
        """Vacuum tables after import."""
        tables = ['commodities', 'markets', 'price_observations', 'conflict_events', 'analysis_results']
        
        for table in tables:
            try:
                await connection.execute(f"VACUUM ANALYZE {table}")
                self.logger.info(f"Vacuumed table: {table}")
            except Exception as e:
                self.logger.warning(f"Failed to vacuum {table}: {e}")
    
    async def _get_table_statistics(self, table_name: str) -> Dict[str, Any]:
        """Get table statistics after import."""
        try:
            async with self.connection_pool.acquire() as connection:
                # Get row count
                row_count = await connection.fetchval(f"SELECT COUNT(*) FROM {table_name}")
                
                # Get table size
                table_size = await connection.fetchval(
                    "SELECT pg_total_relation_size($1)",
                    table_name
                )
                
                # Get additional statistics based on table type
                stats = {
                    "row_count": row_count,
                    "table_size_bytes": table_size,
                    "table_size_mb": table_size / (1024 * 1024) if table_size else 0
                }
                
                if table_name == "price_observations":
                    # Price-specific statistics
                    price_stats = await connection.fetchrow("""
                        SELECT 
                            MIN(observed_date) as min_date,
                            MAX(observed_date) as max_date,
                            COUNT(DISTINCT market_id) as unique_markets,
                            COUNT(DISTINCT commodity_code) as unique_commodities
                        FROM price_observations
                    """)
                    stats.update(dict(price_stats))
                
                elif table_name == "conflict_events":
                    # Conflict-specific statistics
                    conflict_stats = await connection.fetchrow("""
                        SELECT 
                            MIN(event_date) as min_date,
                            MAX(event_date) as max_date,
                            SUM(fatalities) as total_fatalities,
                            COUNT(DISTINCT governorate) as unique_governorates
                        FROM conflict_events
                    """)
                    stats.update(dict(conflict_stats))
                
                return stats
                
        except Exception as e:
            self.logger.warning(f"Failed to get statistics for {table_name}: {e}")
            return {}
    
    def _create_empty_import_result(self, table_name: str) -> ImportResult:
        """Create result for empty data import."""
        return ImportResult(
            table_name=table_name,
            success=True,
            total_records=0,
            imported_records=0,
            failed_records=0,
            skipped_records=0,
            duration_seconds=0,
            throughput_records_per_second=0,
            validation_errors=[],
            performance_metrics={},
            final_table_stats={}
        )
    
    def _create_failed_import_result(self, table_name: str, error_message: str) -> ImportResult:
        """Create result for failed import."""
        return ImportResult(
            table_name=table_name,
            success=False,
            total_records=0,
            imported_records=0,
            failed_records=0,
            skipped_records=0,
            duration_seconds=0,
            throughput_records_per_second=0,
            validation_errors=[error_message],
            performance_metrics={},
            final_table_stats={}
        )
    
    def get_import_progress(self, table_name: Optional[str] = None) -> Dict[str, ImportProgress]:
        """Get current import progress."""
        if table_name:
            return {table_name: self.import_progress.get(table_name)}
        return self.import_progress.copy()
    
    async def get_database_health(self) -> Dict[str, Any]:
        """Check database health and performance."""
        try:
            async with self.connection_pool.acquire() as connection:
                # Connection pool status
                pool_stats = {
                    "total_connections": self.connection_pool.get_size(),
                    "available_connections": self.connection_pool.get_idle_size(),
                    "in_use_connections": self.connection_pool.get_size() - self.connection_pool.get_idle_size()
                }
                
                # Database size
                db_size = await connection.fetchval("SELECT pg_database_size(current_database())")
                
                # Active connections
                active_connections = await connection.fetchval(
                    "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'"
                )
                
                # Table sizes
                table_sizes = await connection.fetch("""
                    SELECT 
                        tablename,
                        pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
                    FROM pg_tables 
                    WHERE schemaname = 'public'
                    ORDER BY size_bytes DESC
                """)
                
                return {
                    "connection_pool": pool_stats,
                    "database_size_bytes": db_size,
                    "database_size_mb": db_size / (1024 * 1024) if db_size else 0,
                    "active_connections": active_connections,
                    "table_sizes": [dict(row) for row in table_sizes],
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get database health: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}