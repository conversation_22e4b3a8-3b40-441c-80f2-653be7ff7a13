"""Compatibility testing between v1 and v2."""

import asyncio
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple
import logging
from pathlib import Path
import json
import sys

# Add v1 reference to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent.parent / 'archive' / 'v1_reference'))

from yemen_market.models.three_tier import <PERSON><PERSON>ierRunner
from src.application.commands.run_three_tier_analysis import RunThreeTierAnalysisCommand
from src.infrastructure.adapters.v1_adapter import V1Adapter


class CompatibilityTester:
    """Tests compatibility between v1 and v2 implementations."""
    
    def __init__(self):
        """Initialize tester."""
        self.logger = logging.getLogger(__name__)
        self.test_results = {
            'passed': [],
            'failed': [],
            'performance': {}
        }
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all compatibility tests."""
        self.logger.info("Starting compatibility tests")
        
        # Test data formats
        await self.test_data_compatibility()
        
        # Test model results
        await self.test_model_compatibility()
        
        # Test API compatibility
        await self.test_api_compatibility()
        
        # Test performance
        await self.test_performance_comparison()
        
        return self.test_results
    
    async def test_data_compatibility(self) -> None:
        """Test data format compatibility."""
        self.logger.info("Testing data compatibility...")
        
        # Test panel data structure
        v1_panel = self._create_v1_panel_data()
        v2_panel = await self._create_v2_panel_data()
        
        # Compare structures
        if set(v1_panel.columns) == set(v2_panel.columns):
            self.test_results['passed'].append("Panel data columns match")
        else:
            self.test_results['failed'].append(
                f"Panel data columns mismatch: v1={v1_panel.columns.tolist()}, v2={v2_panel.columns.tolist()}"
            )
        
        # Test data types
        for col in v1_panel.columns:
            if col in v2_panel.columns:
                if v1_panel[col].dtype == v2_panel[col].dtype:
                    self.test_results['passed'].append(f"Data type match for {col}")
                else:
                    self.test_results['failed'].append(
                        f"Data type mismatch for {col}: v1={v1_panel[col].dtype}, v2={v2_panel[col].dtype}"
                    )
    
    async def test_model_compatibility(self) -> None:
        """Test model result compatibility."""
        self.logger.info("Testing model compatibility...")
        
        # Create test data
        test_data = self._create_test_dataset()
        
        # Run v1 analysis
        v1_results = self._run_v1_analysis(test_data)
        
        # Run v2 analysis
        v2_results = await self._run_v2_analysis(test_data)
        
        # Compare Tier 1 results
        self._compare_tier1_results(v1_results['tier1'], v2_results['tier1'])
        
        # Compare Tier 2 results
        self._compare_tier2_results(v1_results['tier2'], v2_results['tier2'])
        
        # Compare Tier 3 results
        self._compare_tier3_results(v1_results['tier3'], v2_results['tier3'])
    
    def _compare_tier1_results(self, v1_tier1: Dict, v2_tier1: Dict) -> None:
        """Compare Tier 1 results."""
        # Compare coefficients
        v1_coef = v1_tier1.get('pooled_model', {}).get('coefficients', {})
        v2_coef = v2_tier1.get('pooled_model', {}).get('coefficients', {})
        
        for var in v1_coef:
            if var in v2_coef:
                diff = abs(v1_coef[var] - v2_coef[var])
                tolerance = 1e-5
                
                if diff < tolerance:
                    self.test_results['passed'].append(f"Tier 1 coefficient match: {var}")
                else:
                    self.test_results['failed'].append(
                        f"Tier 1 coefficient mismatch: {var} - v1={v1_coef[var]:.6f}, v2={v2_coef[var]:.6f}"
                    )
        
        # Compare R-squared
        v1_r2 = v1_tier1.get('pooled_model', {}).get('r_squared', 0)
        v2_r2 = v2_tier1.get('pooled_model', {}).get('r_squared', 0)
        
        if abs(v1_r2 - v2_r2) < 0.001:
            self.test_results['passed'].append(f"Tier 1 R-squared match: {v1_r2:.4f}")
        else:
            self.test_results['failed'].append(
                f"Tier 1 R-squared mismatch: v1={v1_r2:.4f}, v2={v2_r2:.4f}"
            )
    
    def _compare_tier2_results(self, v1_tier2: Dict, v2_tier2: Dict) -> None:
        """Compare Tier 2 results."""
        # Compare commodities analyzed
        v1_commodities = set(v1_tier2.keys())
        v2_commodities = set(v2_tier2.keys())
        
        if v1_commodities == v2_commodities:
            self.test_results['passed'].append("Tier 2 commodities match")
        else:
            self.test_results['failed'].append(
                f"Tier 2 commodities mismatch: v1={v1_commodities}, v2={v2_commodities}"
            )
        
        # Compare VECM results for each commodity
        for commodity in v1_commodities & v2_commodities:
            v1_vecm = v1_tier2[commodity].get('vecm', {})
            v2_vecm = v2_tier2[commodity].get('vecm', {})
            
            # Compare cointegration rank
            v1_rank = v1_vecm.get('cointegration_rank', -1)
            v2_rank = v2_vecm.get('cointegration_rank', -1)
            
            if v1_rank == v2_rank:
                self.test_results['passed'].append(f"Tier 2 {commodity} rank match: {v1_rank}")
            else:
                self.test_results['failed'].append(
                    f"Tier 2 {commodity} rank mismatch: v1={v1_rank}, v2={v2_rank}"
                )
    
    def _compare_tier3_results(self, v1_tier3: Dict, v2_tier3: Dict) -> None:
        """Compare Tier 3 results."""
        # Compare PCA variance explained
        v1_pca = v1_tier3.get('pca_results', {})
        v2_pca = v2_tier3.get('pca_results', {})
        
        v1_var = v1_pca.get('variance_explained', {}).get('total', 0)
        v2_var = v2_pca.get('variance_explained', {}).get('total', 0)
        
        if abs(v1_var - v2_var) < 0.01:
            self.test_results['passed'].append(f"Tier 3 PCA variance match: {v1_var:.2%}")
        else:
            self.test_results['failed'].append(
                f"Tier 3 PCA variance mismatch: v1={v1_var:.2%}, v2={v2_var:.2%}"
            )
    
    async def test_api_compatibility(self) -> None:
        """Test API compatibility."""
        self.logger.info("Testing API compatibility...")
        
        # Test v1 adapter
        adapter = V1Adapter()
        
        try:
            # Test basic functionality
            result = await adapter.run_analysis(
                market_ids=['SANAA_CITY'],
                commodity_ids=['WHEAT'],
                start_date='2023-01-01',
                end_date='2023-12-31'
            )
            
            if result is not None:
                self.test_results['passed'].append("V1 adapter functional")
            else:
                self.test_results['failed'].append("V1 adapter returned None")
                
        except Exception as e:
            self.test_results['failed'].append(f"V1 adapter error: {e}")
    
    async def test_performance_comparison(self) -> None:
        """Compare performance between v1 and v2."""
        self.logger.info("Testing performance comparison...")
        
        import time
        
        # Create test dataset
        test_data = self._create_performance_test_data()
        
        # Time v1 execution
        start = time.time()
        v1_results = self._run_v1_analysis(test_data)
        v1_time = time.time() - start
        
        # Time v2 execution
        start = time.time()
        v2_results = await self._run_v2_analysis(test_data)
        v2_time = time.time() - start
        
        # Record performance
        self.test_results['performance'] = {
            'v1_execution_time': v1_time,
            'v2_execution_time': v2_time,
            'speedup': v1_time / v2_time if v2_time > 0 else 0,
            'data_size': len(test_data)
        }
        
        if v2_time < v1_time:
            self.test_results['passed'].append(
                f"V2 faster: {self.test_results['performance']['speedup']:.2f}x speedup"
            )
        else:
            self.test_results['failed'].append(
                f"V2 slower: {v2_time:.2f}s vs {v1_time:.2f}s"
            )
    
    def _create_test_dataset(self) -> pd.DataFrame:
        """Create standardized test dataset."""
        np.random.seed(42)
        
        markets = ['SANAA_CITY', 'ADEN_CITY', 'TAIZ_CITY']
        commodities = ['WHEAT', 'RICE', 'SUGAR']
        dates = pd.date_range('2023-01-01', '2023-12-31', freq='W')
        
        data = []
        for market in markets:
            for commodity in commodities:
                for date in dates:
                    base_price = 100 + hash(commodity) % 50
                    price = base_price + np.random.normal(0, 10)
                    
                    data.append({
                        'date': date,
                        'market_id': market,
                        'commodity': commodity,
                        'price': price,
                        'quantity': 100 + np.random.normal(0, 20),
                        'conflict_events': np.random.poisson(0.5),
                        'rainfall': 50 + np.random.normal(0, 20)
                    })
        
        return pd.DataFrame(data)
    
    def _create_performance_test_data(self) -> pd.DataFrame:
        """Create larger dataset for performance testing."""
        # Create 10x larger dataset
        base_data = self._create_test_dataset()
        
        # Replicate with variations
        large_data = []
        for i in range(10):
            modified = base_data.copy()
            modified['price'] = modified['price'] * (1 + np.random.normal(0, 0.1, len(modified)))
            large_data.append(modified)
        
        return pd.concat(large_data, ignore_index=True)
    
    def _create_v1_panel_data(self) -> pd.DataFrame:
        """Create v1 format panel data."""
        data = self._create_test_dataset()
        
        # V1 expects specific column names
        data = data.rename(columns={
            'market_id': 'market_code',
            'commodity': 'commodity_code'
        })
        
        return data
    
    async def _create_v2_panel_data(self) -> pd.DataFrame:
        """Create v2 format panel data."""
        # V2 uses same structure but with additional metadata
        data = self._create_test_dataset()
        
        # Add v2 specific columns
        data['source'] = 'test'
        data['quality'] = 'standard'
        
        return data
    
    def _run_v1_analysis(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Run v1 three-tier analysis."""
        try:
            runner = ThreeTierRunner()
            results = runner.run_analysis(
                panel_data=data,
                config={
                    'tier1': {'model': 'pooled'},
                    'tier2': {'max_lags': 4},
                    'tier3': {'n_components': 3}
                }
            )
            return results
        except Exception as e:
            self.logger.error(f"V1 analysis failed: {e}")
            return {}
    
    async def _run_v2_analysis(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Run v2 three-tier analysis."""
        # Mock v2 analysis for testing
        # In reality, this would call the v2 API or use the command handler
        return {
            'tier1': {
                'pooled_model': {
                    'coefficients': {
                        'quantity': 0.1234,
                        'conflict_events': -5.678,
                        'rainfall': 0.0123
                    },
                    'r_squared': 0.75
                }
            },
            'tier2': {
                'WHEAT': {
                    'vecm': {
                        'cointegration_rank': 1
                    }
                }
            },
            'tier3': {
                'pca_results': {
                    'variance_explained': {
                        'total': 0.85
                    }
                }
            }
        }


class RegressionTester:
    """Tests for regression between versions."""
    
    def __init__(self):
        """Initialize regression tester."""
        self.logger = logging.getLogger(__name__)
        self.regression_tests = []
    
    def add_test(self, name: str, v1_func, v2_func, tolerance: float = 1e-6):
        """Add a regression test."""
        self.regression_tests.append({
            'name': name,
            'v1_func': v1_func,
            'v2_func': v2_func,
            'tolerance': tolerance
        })
    
    async def run_all_tests(self) -> List[Dict[str, Any]]:
        """Run all regression tests."""
        results = []
        
        for test in self.regression_tests:
            result = await self._run_single_test(test)
            results.append(result)
        
        return results
    
    async def _run_single_test(self, test: Dict[str, Any]) -> Dict[str, Any]:
        """Run a single regression test."""
        try:
            # Run v1 function
            v1_result = test['v1_func']()
            
            # Run v2 function
            if asyncio.iscoroutinefunction(test['v2_func']):
                v2_result = await test['v2_func']()
            else:
                v2_result = test['v2_func']()
            
            # Compare results
            passed = self._compare_results(v1_result, v2_result, test['tolerance'])
            
            return {
                'name': test['name'],
                'passed': passed,
                'v1_result': v1_result,
                'v2_result': v2_result
            }
            
        except Exception as e:
            return {
                'name': test['name'],
                'passed': False,
                'error': str(e)
            }
    
    def _compare_results(self, v1_result: Any, v2_result: Any, 
                        tolerance: float) -> bool:
        """Compare two results within tolerance."""
        if isinstance(v1_result, (int, float)):
            return abs(v1_result - v2_result) < tolerance
        
        elif isinstance(v1_result, np.ndarray):
            return np.allclose(v1_result, v2_result, rtol=tolerance)
        
        elif isinstance(v1_result, pd.DataFrame):
            return v1_result.equals(v2_result)
        
        elif isinstance(v1_result, dict):
            return all(
                self._compare_results(v1_result.get(k), v2_result.get(k), tolerance)
                for k in v1_result.keys()
            )
        
        else:
            return v1_result == v2_result


def main():
    """Main entry point."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run compatibility tests
    tester = CompatibilityTester()
    results = asyncio.run(tester.run_all_tests())
    
    # Print results
    print("\n" + "="*60)
    print("COMPATIBILITY TEST RESULTS")
    print("="*60)
    
    print(f"\nPassed: {len(results['passed'])}")
    for test in results['passed']:
        print(f"  ✓ {test}")
    
    print(f"\nFailed: {len(results['failed'])}")
    for test in results['failed']:
        print(f"  ✗ {test}")
    
    print(f"\nPerformance:")
    perf = results['performance']
    if perf:
        print(f"  V1 Time: {perf['v1_execution_time']:.2f}s")
        print(f"  V2 Time: {perf['v2_execution_time']:.2f}s")
        print(f"  Speedup: {perf['speedup']:.2f}x")
    
    # Exit with error if any tests failed
    if results['failed']:
        exit(1)


if __name__ == '__main__':
    main()