"""V1 data export utilities for migration to V2."""

import json
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Tuple
import logging
from dataclasses import dataclass, asdict
import hashlib
import gzip
from concurrent.futures import ThreadPoolExecutor
import psutil
import time

from src.infrastructure.observability.structured_logging import StructuredLogger


@dataclass
class ExportMetadata:
    """Metadata for export operations."""
    export_id: str
    export_type: str
    start_time: datetime
    end_time: Optional[datetime]
    source_files: List[str]
    record_count: int
    file_size_bytes: int
    checksum: str
    compression_used: bool
    validation_passed: bool
    errors: List[str]


@dataclass
class DataValidationResult:
    """Result of data validation."""
    passed: bool
    total_records: int
    valid_records: int
    invalid_records: int
    missing_required_fields: List[str]
    data_quality_issues: List[str]
    validation_errors: List[str]


class V1DataExporter:
    """Enhanced V1 data exporter with validation and integrity checks."""
    
    def __init__(
        self, 
        v1_data_path: Path, 
        export_path: Path,
        compress_output: bool = True,
        parallel_workers: int = None
    ):
        """Initialize the exporter."""
        self.v1_data_path = Path(v1_data_path)
        self.export_path = Path(export_path)
        self.compress_output = compress_output
        self.parallel_workers = parallel_workers or min(4, psutil.cpu_count())
        
        # Setup logging
        self.logger = StructuredLogger("V1DataExporter")
        
        # Export tracking
        self.export_metadata = {}
        self.validation_results = {}
        
        # Ensure export directory exists
        self.export_path.mkdir(parents=True, exist_ok=True)
        
        # Data coverage targets from V1 system
        self.v1_coverage_target = 0.884  # 88.4% as specified
        
    def export_all(self) -> Dict[str, ExportMetadata]:
        """Export all V1 data with validation."""
        self.logger.info("Starting comprehensive V1 data export")
        
        export_operations = [
            ("price_data", self._export_price_data),
            ("market_data", self._export_market_data),
            ("commodity_data", self._export_commodity_data),
            ("conflict_data", self._export_conflict_data),
            ("analysis_results", self._export_analysis_results),
            ("spatial_data", self._export_spatial_data),
            ("exchange_rates", self._export_exchange_rates),
            ("metadata", self._export_metadata)
        ]
        
        # Run exports in parallel where possible
        with ThreadPoolExecutor(max_workers=self.parallel_workers) as executor:
            futures = {}
            
            # Start independent exports in parallel
            for export_type, export_func in export_operations[:4]:
                futures[export_type] = executor.submit(export_func)
            
            # Collect results
            export_results = {}
            for export_type, future in futures.items():
                try:
                    export_results[export_type] = future.result()
                    self.logger.info(f"Completed export: {export_type}")
                except Exception as e:
                    self.logger.error(f"Failed export: {export_type}", extra={"error": str(e)})
                    raise
        
        # Run dependent exports sequentially
        for export_type, export_func in export_operations[4:]:
            try:
                export_results[export_type] = export_func()
                self.logger.info(f"Completed export: {export_type}")
            except Exception as e:
                self.logger.error(f"Failed export: {export_type}", extra={"error": str(e)})
                raise
        
        # Generate final export summary
        self._generate_export_summary(export_results)
        
        return export_results
    
    def _export_price_data(self) -> ExportMetadata:
        """Export price observation data."""
        self.logger.info("Exporting price data")
        
        start_time = datetime.now()
        export_id = f"price_data_{start_time.strftime('%Y%m%d_%H%M%S')}"
        errors = []
        
        try:
            # Load integrated panel data
            source_files = []
            price_data = []
            
            # Load balanced panel data
            balanced_panel_path = self.v1_data_path / "data" / "processed" / "balanced_panels" / "balanced_panel_filled.parquet"
            if balanced_panel_path.exists():
                df_balanced = pd.read_parquet(balanced_panel_path)
                price_data.append(self._transform_balanced_panel_to_prices(df_balanced))
                source_files.append(str(balanced_panel_path))
            
            # Load integrated panel data
            integrated_panel_path = self.v1_data_path / "data" / "processed" / "integrated_panel" / "yemen_integrated_balanced_panel.parquet"
            if integrated_panel_path.exists():
                df_integrated = pd.read_parquet(integrated_panel_path)
                price_data.append(self._transform_integrated_panel_to_prices(df_integrated))
                source_files.append(str(integrated_panel_path))
            
            # Load raw WFP data
            wfp_path = self.v1_data_path / "data" / "processed" / "wfp_commodity_prices.parquet"
            if wfp_path.exists():
                df_wfp = pd.read_parquet(wfp_path)
                price_data.append(self._transform_wfp_to_prices(df_wfp))
                source_files.append(str(wfp_path))
            
            # Combine all price data
            if price_data:
                combined_prices = pd.concat(price_data, ignore_index=True)
                combined_prices = self._deduplicate_prices(combined_prices)
            else:
                combined_prices = pd.DataFrame()
                errors.append("No price data files found")
            
            # Validate price data
            validation_result = self._validate_price_data(combined_prices)
            self.validation_results["price_data"] = validation_result
            
            # Export data
            output_file = self.export_path / f"{export_id}.parquet"
            if self.compress_output:
                combined_prices.to_parquet(output_file, compression='gzip')
            else:
                combined_prices.to_parquet(output_file)
            
            # Calculate metadata
            file_size = output_file.stat().st_size
            checksum = self._calculate_file_checksum(output_file)
            
            return ExportMetadata(
                export_id=export_id,
                export_type="price_data",
                start_time=start_time,
                end_time=datetime.now(),
                source_files=source_files,
                record_count=len(combined_prices),
                file_size_bytes=file_size,
                checksum=checksum,
                compression_used=self.compress_output,
                validation_passed=validation_result.passed,
                errors=errors + validation_result.validation_errors
            )
            
        except Exception as e:
            self.logger.error("Price data export failed", extra={"error": str(e)})
            errors.append(str(e))
            raise
    
    def _export_market_data(self) -> ExportMetadata:
        """Export market master data."""
        self.logger.info("Exporting market data")
        
        start_time = datetime.now()
        export_id = f"market_data_{start_time.strftime('%Y%m%d_%H%M%S')}"
        errors = []
        
        try:
            # Extract market information from balanced panel metadata
            metadata_path = self.v1_data_path / "data" / "processed" / "balanced_panels" / "balanced_panel_metadata.json"
            source_files = [str(metadata_path)]
            
            if metadata_path.exists():
                with open(metadata_path) as f:
                    metadata = json.load(f)
                
                markets_data = []
                for market_name in metadata.get("markets", []):
                    # Look up market details from spatial data
                    market_details = self._lookup_market_details(market_name)
                    
                    markets_data.append({
                        "market_id": self._standardize_market_id(market_name),
                        "name": market_name,
                        "governorate": market_details.get("governorate", ""),
                        "district": market_details.get("district", ""),
                        "latitude": market_details.get("latitude", 0.0),
                        "longitude": market_details.get("longitude", 0.0),
                        "market_type": market_details.get("market_type", "retail"),
                        "active_since": pd.to_datetime(metadata.get("date_range", "").split(" to ")[0]) if metadata.get("date_range") else pd.to_datetime("2019-01-01"),
                        "active_until": None,
                        "data_source": "V1_balanced_panel",
                        "coverage_percentage": self._calculate_market_coverage(market_name)
                    })
                
                markets_df = pd.DataFrame(markets_data)
            else:
                markets_df = pd.DataFrame()
                errors.append("Market metadata file not found")
            
            # Validate market data
            validation_result = self._validate_market_data(markets_df)
            self.validation_results["market_data"] = validation_result
            
            # Export data
            output_file = self.export_path / f"{export_id}.parquet"
            if self.compress_output:
                markets_df.to_parquet(output_file, compression='gzip')
            else:
                markets_df.to_parquet(output_file)
            
            # Calculate metadata
            file_size = output_file.stat().st_size
            checksum = self._calculate_file_checksum(output_file)
            
            return ExportMetadata(
                export_id=export_id,
                export_type="market_data",
                start_time=start_time,
                end_time=datetime.now(),
                source_files=source_files,
                record_count=len(markets_df),
                file_size_bytes=file_size,
                checksum=checksum,
                compression_used=self.compress_output,
                validation_passed=validation_result.passed,
                errors=errors + validation_result.validation_errors
            )
            
        except Exception as e:
            self.logger.error("Market data export failed", extra={"error": str(e)})
            errors.append(str(e))
            raise
    
    def _export_commodity_data(self) -> ExportMetadata:
        """Export commodity master data."""
        self.logger.info("Exporting commodity data")
        
        start_time = datetime.now()
        export_id = f"commodity_data_{start_time.strftime('%Y%m%d_%H%M%S')}"
        errors = []
        
        try:
            # Extract commodity information from balanced panel metadata
            metadata_path = self.v1_data_path / "data" / "processed" / "balanced_panels" / "balanced_panel_metadata.json"
            source_files = [str(metadata_path)]
            
            if metadata_path.exists():
                with open(metadata_path) as f:
                    metadata = json.load(f)
                
                commodities_data = []
                for commodity_name in metadata.get("commodities", []):
                    commodity_details = self._get_commodity_details(commodity_name)
                    
                    commodities_data.append({
                        "code": self._standardize_commodity_code(commodity_name),
                        "name": commodity_name,
                        "category": commodity_details["category"],
                        "standard_unit": commodity_details["unit"],
                        "is_fuel": commodity_details["is_fuel"],
                        "is_food": commodity_details["is_food"],
                        "perishability": commodity_details["perishability"],
                        "data_source": "V1_balanced_panel"
                    })
                
                commodities_df = pd.DataFrame(commodities_data)
            else:
                commodities_df = pd.DataFrame()
                errors.append("Commodity metadata file not found")
            
            # Validate commodity data
            validation_result = self._validate_commodity_data(commodities_df)
            self.validation_results["commodity_data"] = validation_result
            
            # Export data
            output_file = self.export_path / f"{export_id}.parquet"
            if self.compress_output:
                commodities_df.to_parquet(output_file, compression='gzip')
            else:
                commodities_df.to_parquet(output_file)
            
            # Calculate metadata
            file_size = output_file.stat().st_size
            checksum = self._calculate_file_checksum(output_file)
            
            return ExportMetadata(
                export_id=export_id,
                export_type="commodity_data",
                start_time=start_time,
                end_time=datetime.now(),
                source_files=source_files,
                record_count=len(commodities_df),
                file_size_bytes=file_size,
                checksum=checksum,
                compression_used=self.compress_output,
                validation_passed=validation_result.passed,
                errors=errors + validation_result.validation_errors
            )
            
        except Exception as e:
            self.logger.error("Commodity data export failed", extra={"error": str(e)})
            errors.append(str(e))
            raise
    
    def _export_conflict_data(self) -> ExportMetadata:
        """Export conflict events data."""
        self.logger.info("Exporting conflict data")
        
        start_time = datetime.now()
        export_id = f"conflict_data_{start_time.strftime('%Y%m%d_%H%M%S')}"
        errors = []
        
        try:
            source_files = []
            
            # Load ACLED conflict data
            acled_path = self.v1_data_path / "data" / "raw" / "acled" / "acled_yemen_events_2019-01-01_to_2024-12-31.csv"
            if acled_path.exists():
                conflict_df = pd.read_csv(acled_path)
                source_files.append(str(acled_path))
                
                # Transform to V2 format
                conflict_df = self._transform_acled_to_v2_format(conflict_df)
            else:
                conflict_df = pd.DataFrame()
                errors.append("ACLED conflict data file not found")
            
            # Validate conflict data
            validation_result = self._validate_conflict_data(conflict_df)
            self.validation_results["conflict_data"] = validation_result
            
            # Export data
            output_file = self.export_path / f"{export_id}.parquet"
            if self.compress_output:
                conflict_df.to_parquet(output_file, compression='gzip')
            else:
                conflict_df.to_parquet(output_file)
            
            # Calculate metadata
            file_size = output_file.stat().st_size
            checksum = self._calculate_file_checksum(output_file)
            
            return ExportMetadata(
                export_id=export_id,
                export_type="conflict_data",
                start_time=start_time,
                end_time=datetime.now(),
                source_files=source_files,
                record_count=len(conflict_df),
                file_size_bytes=file_size,
                checksum=checksum,
                compression_used=self.compress_output,
                validation_passed=validation_result.passed,
                errors=errors + validation_result.validation_errors
            )
            
        except Exception as e:
            self.logger.error("Conflict data export failed", extra={"error": str(e)})
            errors.append(str(e))
            raise
    
    def _export_analysis_results(self) -> ExportMetadata:
        """Export historical analysis results."""
        self.logger.info("Exporting analysis results")
        
        start_time = datetime.now()
        export_id = f"analysis_results_{start_time.strftime('%Y%m%d_%H%M%S')}"
        errors = []
        
        try:
            # Collect all analysis results
            results_dir = self.v1_data_path / "results"
            source_files = []
            analysis_results = []
            
            if results_dir.exists():
                # Process three-tier analysis results
                three_tier_dir = results_dir / "three_tier_analysis_new"
                if three_tier_dir.exists():
                    analysis_results.extend(self._process_three_tier_results(three_tier_dir))
                    source_files.extend([str(f) for f in three_tier_dir.rglob("*.json")])
                
                # Process v1 model validation results
                v1_validation_dir = results_dir / "v1_model_validation"
                if v1_validation_dir.exists():
                    analysis_results.extend(self._process_v1_validation_results(v1_validation_dir))
                    source_files.extend([str(f) for f in v1_validation_dir.rglob("*.json")])
                
                # Process other analysis results
                for result_file in results_dir.glob("*.json"):
                    try:
                        with open(result_file) as f:
                            result_data = json.load(f)
                        analysis_results.append(self._transform_generic_result(result_file.name, result_data))
                        source_files.append(str(result_file))
                    except Exception as e:
                        errors.append(f"Failed to process {result_file.name}: {e}")
            
            results_df = pd.DataFrame(analysis_results) if analysis_results else pd.DataFrame()
            
            # Validate analysis results
            validation_result = self._validate_analysis_results(results_df)
            self.validation_results["analysis_results"] = validation_result
            
            # Export data
            output_file = self.export_path / f"{export_id}.parquet"
            if self.compress_output:
                results_df.to_parquet(output_file, compression='gzip')
            else:
                results_df.to_parquet(output_file)
            
            # Calculate metadata
            file_size = output_file.stat().st_size
            checksum = self._calculate_file_checksum(output_file)
            
            return ExportMetadata(
                export_id=export_id,
                export_type="analysis_results",
                start_time=start_time,
                end_time=datetime.now(),
                source_files=source_files,
                record_count=len(results_df),
                file_size_bytes=file_size,
                checksum=checksum,
                compression_used=self.compress_output,
                validation_passed=validation_result.passed,
                errors=errors + validation_result.validation_errors
            )
            
        except Exception as e:
            self.logger.error("Analysis results export failed", extra={"error": str(e)})
            errors.append(str(e))
            raise
    
    def _export_spatial_data(self) -> ExportMetadata:
        """Export spatial and geographic data."""
        self.logger.info("Exporting spatial data")
        
        start_time = datetime.now()
        export_id = f"spatial_data_{start_time.strftime('%Y%m%d_%H%M%S')}"
        errors = []
        source_files = []
        
        try:
            spatial_data = []
            
            # Export market zones data
            zones_path = self.v1_data_path / "data" / "processed" / "spatial" / "market_zones_current.parquet"
            if zones_path.exists():
                zones_df = pd.read_parquet(zones_path)
                spatial_data.append(("market_zones", zones_df))
                source_files.append(str(zones_path))
            
            # Export control zones data
            control_zones_path = self.v1_data_path / "data" / "processed" / "control_zones" / "control_zones_monthly.parquet"
            if control_zones_path.exists():
                control_df = pd.read_parquet(control_zones_path)
                spatial_data.append(("control_zones", control_df))
                source_files.append(str(control_zones_path))
            
            # Export boundary markets
            boundary_path = self.v1_data_path / "data" / "processed" / "spatial" / "boundary_markets.csv"
            if boundary_path.exists():
                boundary_df = pd.read_csv(boundary_path)
                spatial_data.append(("boundary_markets", boundary_df))
                source_files.append(str(boundary_path))
            
            # Combine and export spatial data
            combined_spatial = {}
            for data_type, df in spatial_data:
                combined_spatial[data_type] = df.to_dict('records')
            
            # Export as JSON for spatial data
            output_file = self.export_path / f"{export_id}.json"
            with open(output_file, 'w') as f:
                json.dump(combined_spatial, f, indent=2, default=str)
            
            # Validate spatial data
            validation_result = self._validate_spatial_data(combined_spatial)
            self.validation_results["spatial_data"] = validation_result
            
            # Calculate metadata
            file_size = output_file.stat().st_size
            checksum = self._calculate_file_checksum(output_file)
            total_records = sum(len(df) for _, df in spatial_data)
            
            return ExportMetadata(
                export_id=export_id,
                export_type="spatial_data",
                start_time=start_time,
                end_time=datetime.now(),
                source_files=source_files,
                record_count=total_records,
                file_size_bytes=file_size,
                checksum=checksum,
                compression_used=False,  # JSON export
                validation_passed=validation_result.passed,
                errors=errors + validation_result.validation_errors
            )
            
        except Exception as e:
            self.logger.error("Spatial data export failed", extra={"error": str(e)})
            errors.append(str(e))
            raise
    
    def _export_exchange_rates(self) -> ExportMetadata:
        """Export exchange rate data."""
        self.logger.info("Exporting exchange rate data")
        
        start_time = datetime.now()
        export_id = f"exchange_rates_{start_time.strftime('%Y%m%d_%H%M%S')}"
        errors = []
        
        try:
            # Load WFP exchange rate data
            source_files = []
            exchange_rates_path = self.v1_data_path / "data" / "processed" / "wfp_exchange_rates.parquet"
            
            if exchange_rates_path.exists():
                exchange_df = pd.read_parquet(exchange_rates_path)
                source_files.append(str(exchange_rates_path))
                
                # Transform to V2 format
                exchange_df = self._transform_exchange_rates_to_v2(exchange_df)
            else:
                exchange_df = pd.DataFrame()
                errors.append("Exchange rates file not found")
            
            # Validate exchange rate data
            validation_result = self._validate_exchange_rate_data(exchange_df)
            self.validation_results["exchange_rates"] = validation_result
            
            # Export data
            output_file = self.export_path / f"{export_id}.parquet"
            if self.compress_output:
                exchange_df.to_parquet(output_file, compression='gzip')
            else:
                exchange_df.to_parquet(output_file)
            
            # Calculate metadata
            file_size = output_file.stat().st_size
            checksum = self._calculate_file_checksum(output_file)
            
            return ExportMetadata(
                export_id=export_id,
                export_type="exchange_rates",
                start_time=start_time,
                end_time=datetime.now(),
                source_files=source_files,
                record_count=len(exchange_df),
                file_size_bytes=file_size,
                checksum=checksum,
                compression_used=self.compress_output,
                validation_passed=validation_result.passed,
                errors=errors + validation_result.validation_errors
            )
            
        except Exception as e:
            self.logger.error("Exchange rates export failed", extra={"error": str(e)})
            errors.append(str(e))
            raise
    
    def _export_metadata(self) -> ExportMetadata:
        """Export system and processing metadata."""
        self.logger.info("Exporting metadata")
        
        start_time = datetime.now()
        export_id = f"metadata_{start_time.strftime('%Y%m%d_%H%M%S')}"
        errors = []
        source_files = []
        
        try:
            metadata_collection = {}
            
            # Collect all metadata files
            metadata_patterns = [
                "data/processed/*/metadata.json",
                "data/processed/*_metadata.json",
                "data/processed/*/*_metadata.json"
            ]
            
            for pattern in metadata_patterns:
                for metadata_file in self.v1_data_path.glob(pattern):
                    try:
                        with open(metadata_file) as f:
                            metadata = json.load(f)
                        
                        key = str(metadata_file.relative_to(self.v1_data_path))
                        metadata_collection[key] = metadata
                        source_files.append(str(metadata_file))
                    except Exception as e:
                        errors.append(f"Failed to read {metadata_file}: {e}")
            
            # Add export validation results
            metadata_collection["validation_results"] = {
                k: asdict(v) for k, v in self.validation_results.items()
            }
            
            # Export metadata
            output_file = self.export_path / f"{export_id}.json"
            with open(output_file, 'w') as f:
                json.dump(metadata_collection, f, indent=2, default=str)
            
            # Calculate metadata
            file_size = output_file.stat().st_size
            checksum = self._calculate_file_checksum(output_file)
            
            return ExportMetadata(
                export_id=export_id,
                export_type="metadata",
                start_time=start_time,
                end_time=datetime.now(),
                source_files=source_files,
                record_count=len(metadata_collection),
                file_size_bytes=file_size,
                checksum=checksum,
                compression_used=False,
                validation_passed=True,
                errors=errors
            )
            
        except Exception as e:
            self.logger.error("Metadata export failed", extra={"error": str(e)})
            errors.append(str(e))
            raise
    
    # Helper methods for data transformation
    def _transform_balanced_panel_to_prices(self, df: pd.DataFrame) -> pd.DataFrame:
        """Transform balanced panel data to price observation format."""
        # Melt the panel data to long format
        price_cols = [col for col in df.columns if not col.startswith(('date', 'market', 'commodity'))]
        
        if 'date' in df.columns and 'market' in df.columns and 'commodity' in df.columns:
            # Already in long format
            return df.rename(columns={
                'market': 'market_id',
                'commodity': 'commodity_code',
                'price': 'price_amount' if 'price' in df.columns else 'price_yer'
            })
        else:
            # Need to melt from wide format
            melted = df.melt(
                id_vars=['date'],
                var_name='market_commodity',
                value_name='price_amount'
            )
            
            # Extract market and commodity from column names
            melted[['market_id', 'commodity_code']] = melted['market_commodity'].str.extract(r'(.+)_(.+)')
            melted['observed_date'] = pd.to_datetime(melted['date'])
            melted['price_currency'] = 'YER'
            melted['price_unit'] = 'kg'
            melted['source'] = 'V1_balanced_panel'
            melted['quality'] = 'standard'
            melted['observations_count'] = 1
            
            return melted[['market_id', 'commodity_code', 'observed_date', 'price_amount', 
                          'price_currency', 'price_unit', 'source', 'quality', 'observations_count']]
    
    def _transform_integrated_panel_to_prices(self, df: pd.DataFrame) -> pd.DataFrame:
        """Transform integrated panel data to price observation format."""
        price_cols = ['price_yer', 'price_usd', 'price_yer_real']
        
        results = []
        for price_col in price_cols:
            if price_col in df.columns:
                currency = 'YER' if 'yer' in price_col else 'USD'
                is_real = 'real' in price_col
                
                price_df = df[['date', 'market', 'commodity', price_col]].copy()
                price_df = price_df.dropna(subset=[price_col])
                
                price_df['market_id'] = price_df['market'].apply(self._standardize_market_id)
                price_df['commodity_code'] = price_df['commodity'].apply(self._standardize_commodity_code)
                price_df['observed_date'] = pd.to_datetime(price_df['date'])
                price_df['price_amount'] = price_df[price_col]
                price_df['price_currency'] = currency
                price_df['price_unit'] = 'kg'
                price_df['source'] = 'V1_integrated_panel'
                price_df['quality'] = 'real' if is_real else 'standard'
                price_df['observations_count'] = 1
                
                results.append(price_df[['market_id', 'commodity_code', 'observed_date', 'price_amount',
                                       'price_currency', 'price_unit', 'source', 'quality', 'observations_count']])
        
        return pd.concat(results, ignore_index=True) if results else pd.DataFrame()
    
    def _transform_wfp_to_prices(self, df: pd.DataFrame) -> pd.DataFrame:
        """Transform WFP data to price observation format."""
        # Map WFP column names to V2 format
        column_mapping = {
            'market': 'market_id',
            'commodity': 'commodity_code',
            'date': 'observed_date',
            'price': 'price_amount',
            'currency': 'price_currency',
            'unit': 'price_unit'
        }
        
        # Rename columns
        df_mapped = df.rename(columns=column_mapping)
        
        # Standardize values
        df_mapped['market_id'] = df_mapped['market_id'].apply(self._standardize_market_id)
        df_mapped['commodity_code'] = df_mapped['commodity_code'].apply(self._standardize_commodity_code)
        df_mapped['observed_date'] = pd.to_datetime(df_mapped['observed_date'])
        df_mapped['source'] = 'WFP'
        df_mapped['quality'] = 'standard'
        df_mapped['observations_count'] = 1
        
        return df_mapped[['market_id', 'commodity_code', 'observed_date', 'price_amount',
                         'price_currency', 'price_unit', 'source', 'quality', 'observations_count']]
    
    def _deduplicate_prices(self, df: pd.DataFrame) -> pd.DataFrame:
        """Remove duplicate price observations."""
        # Sort by quality to prefer higher quality data
        quality_order = {'high': 3, 'standard': 2, 'low': 1}
        df['quality_rank'] = df['quality'].map(quality_order)
        
        # Remove duplicates, keeping highest quality
        df_dedup = df.sort_values('quality_rank', ascending=False).drop_duplicates(
            subset=['market_id', 'commodity_code', 'observed_date', 'price_currency', 'source'],
            keep='first'
        )
        
        return df_dedup.drop('quality_rank', axis=1)
    
    def _standardize_market_id(self, market_name: str) -> str:
        """Standardize market names to IDs."""
        if pd.isna(market_name):
            return ""
        
        # Remove special characters and convert to uppercase
        market_id = market_name.upper().replace(" ", "_").replace("'", "").replace("-", "_")
        
        # Common standardizations
        standardizations = {
            "SANAA_CITY": "SANAA_CITY",
            "ADEN_CITY": "ADEN_CITY",
            "TAIZ_CITY": "TAIZ_CITY",
            "AL_BAYDA_CITY": "ALBAYDA_CITY",
            "IBB_CITY": "IBB_CITY",
            "DHAMAR_CITY": "DHAMAR_CITY"
        }
        
        return standardizations.get(market_id, market_id)
    
    def _standardize_commodity_code(self, commodity_name: str) -> str:
        """Standardize commodity names to codes."""
        if pd.isna(commodity_name):
            return ""
        
        # Remove special characters and convert to uppercase
        commodity_code = commodity_name.upper().replace(" ", "_").replace("(", "").replace(")", "").replace("-", "_")
        
        # Common standardizations
        standardizations = {
            "WHEAT": "WHEAT",
            "RICE_IMPORTED": "RICE",
            "SUGAR": "SUGAR",
            "OIL_VEGETABLE": "OIL_VEGETABLE",
            "FUEL_DIESEL": "FUEL_DIESEL",
            "FUEL_PETROL_GASOLINE": "FUEL_PETROL",
            "FUEL_GAS": "FUEL_GAS",
            "BEANS_KIDNEY_RED": "BEANS_RED",
            "BEANS_WHITE": "BEANS_WHITE",
            "LENTILS": "LENTILS",
            "EGGS": "EGGS",
            "TOMATOES": "TOMATOES",
            "ONIONS": "ONIONS",
            "POTATOES": "POTATOES",
            "SALT": "SALT",
            "WHEAT_FLOUR": "WHEAT_FLOUR"
        }
        
        return standardizations.get(commodity_code, commodity_code)
    
    # Validation methods
    def _validate_price_data(self, df: pd.DataFrame) -> DataValidationResult:
        """Validate price data quality."""
        errors = []
        missing_fields = []
        data_issues = []
        
        required_fields = ['market_id', 'commodity_code', 'observed_date', 'price_amount', 'price_currency']
        
        # Check required fields
        for field in required_fields:
            if field not in df.columns:
                missing_fields.append(field)
            elif df[field].isna().any():
                data_issues.append(f"Missing values in {field}: {df[field].isna().sum()} records")
        
        # Validate price amounts
        if 'price_amount' in df.columns:
            negative_prices = (df['price_amount'] < 0).sum()
            if negative_prices > 0:
                data_issues.append(f"Negative prices: {negative_prices} records")
            
            zero_prices = (df['price_amount'] == 0).sum()
            if zero_prices > 0:
                data_issues.append(f"Zero prices: {zero_prices} records")
        
        # Check date range
        if 'observed_date' in df.columns:
            min_date = df['observed_date'].min()
            max_date = df['observed_date'].max()
            if min_date < pd.Timestamp('2019-01-01'):
                data_issues.append(f"Dates before 2019: earliest is {min_date}")
            if max_date > pd.Timestamp.now() + pd.Timedelta(days=30):
                data_issues.append(f"Future dates: latest is {max_date}")
        
        # Calculate coverage
        expected_coverage = self.v1_coverage_target
        actual_coverage = len(df.dropna(subset=['price_amount'])) / len(df) if len(df) > 0 else 0
        
        if actual_coverage < expected_coverage * 0.95:  # Allow 5% tolerance
            data_issues.append(f"Coverage below target: {actual_coverage:.1%} vs {expected_coverage:.1%}")
        
        valid_records = len(df.dropna(subset=required_fields))
        
        return DataValidationResult(
            passed=len(missing_fields) == 0 and len(errors) == 0,
            total_records=len(df),
            valid_records=valid_records,
            invalid_records=len(df) - valid_records,
            missing_required_fields=missing_fields,
            data_quality_issues=data_issues,
            validation_errors=errors
        )
    
    def _validate_market_data(self, df: pd.DataFrame) -> DataValidationResult:
        """Validate market data quality."""
        errors = []
        missing_fields = []
        data_issues = []
        
        required_fields = ['market_id', 'name', 'governorate']
        
        # Check required fields
        for field in required_fields:
            if field not in df.columns:
                missing_fields.append(field)
            elif df[field].isna().any():
                data_issues.append(f"Missing values in {field}: {df[field].isna().sum()} records")
        
        # Validate coordinates
        if 'latitude' in df.columns and 'longitude' in df.columns:
            invalid_coords = ((df['latitude'].abs() > 90) | (df['longitude'].abs() > 180)).sum()
            if invalid_coords > 0:
                data_issues.append(f"Invalid coordinates: {invalid_coords} records")
        
        # Check for duplicates
        if 'market_id' in df.columns:
            duplicates = df['market_id'].duplicated().sum()
            if duplicates > 0:
                data_issues.append(f"Duplicate market IDs: {duplicates} records")
        
        valid_records = len(df.dropna(subset=required_fields))
        
        return DataValidationResult(
            passed=len(missing_fields) == 0 and len(errors) == 0,
            total_records=len(df),
            valid_records=valid_records,
            invalid_records=len(df) - valid_records,
            missing_required_fields=missing_fields,
            data_quality_issues=data_issues,
            validation_errors=errors
        )
    
    def _validate_commodity_data(self, df: pd.DataFrame) -> DataValidationResult:
        """Validate commodity data quality."""
        errors = []
        missing_fields = []
        data_issues = []
        
        required_fields = ['code', 'name', 'category', 'standard_unit']
        
        # Check required fields
        for field in required_fields:
            if field not in df.columns:
                missing_fields.append(field)
            elif df[field].isna().any():
                data_issues.append(f"Missing values in {field}: {df[field].isna().sum()} records")
        
        # Check for duplicates
        if 'code' in df.columns:
            duplicates = df['code'].duplicated().sum()
            if duplicates > 0:
                data_issues.append(f"Duplicate commodity codes: {duplicates} records")
        
        valid_records = len(df.dropna(subset=required_fields))
        
        return DataValidationResult(
            passed=len(missing_fields) == 0 and len(errors) == 0,
            total_records=len(df),
            valid_records=valid_records,
            invalid_records=len(df) - valid_records,
            missing_required_fields=missing_fields,
            data_quality_issues=data_issues,
            validation_errors=errors
        )
    
    def _validate_conflict_data(self, df: pd.DataFrame) -> DataValidationResult:
        """Validate conflict data quality."""
        errors = []
        missing_fields = []
        data_issues = []
        
        required_fields = ['event_id', 'event_type', 'event_date', 'latitude', 'longitude']
        
        # Check required fields
        for field in required_fields:
            if field not in df.columns:
                missing_fields.append(field)
            elif df[field].isna().any():
                data_issues.append(f"Missing values in {field}: {df[field].isna().sum()} records")
        
        # Validate coordinates
        if 'latitude' in df.columns and 'longitude' in df.columns:
            invalid_coords = ((df['latitude'].abs() > 90) | (df['longitude'].abs() > 180)).sum()
            if invalid_coords > 0:
                data_issues.append(f"Invalid coordinates: {invalid_coords} records")
        
        # Validate fatalities
        if 'fatalities' in df.columns:
            negative_fatalities = (df['fatalities'] < 0).sum()
            if negative_fatalities > 0:
                data_issues.append(f"Negative fatalities: {negative_fatalities} records")
        
        valid_records = len(df.dropna(subset=required_fields))
        
        return DataValidationResult(
            passed=len(missing_fields) == 0 and len(errors) == 0,
            total_records=len(df),
            valid_records=valid_records,
            invalid_records=len(df) - valid_records,
            missing_required_fields=missing_fields,
            data_quality_issues=data_issues,
            validation_errors=errors
        )
    
    def _validate_analysis_results(self, df: pd.DataFrame) -> DataValidationResult:
        """Validate analysis results data."""
        errors = []
        missing_fields = []
        data_issues = []
        
        required_fields = ['analysis_id', 'analysis_type', 'results']
        
        # Check required fields
        for field in required_fields:
            if field not in df.columns:
                missing_fields.append(field)
            elif df[field].isna().any():
                data_issues.append(f"Missing values in {field}: {df[field].isna().sum()} records")
        
        # Check for duplicates
        if 'analysis_id' in df.columns:
            duplicates = df['analysis_id'].duplicated().sum()
            if duplicates > 0:
                data_issues.append(f"Duplicate analysis IDs: {duplicates} records")
        
        valid_records = len(df.dropna(subset=required_fields))
        
        return DataValidationResult(
            passed=len(missing_fields) == 0 and len(errors) == 0,
            total_records=len(df),
            valid_records=valid_records,
            invalid_records=len(df) - valid_records,
            missing_required_fields=missing_fields,
            data_quality_issues=data_issues,
            validation_errors=errors
        )
    
    def _validate_spatial_data(self, data: Dict[str, Any]) -> DataValidationResult:
        """Validate spatial data."""
        errors = []
        data_issues = []
        
        total_records = sum(len(records) if isinstance(records, list) else 0 for records in data.values())
        
        # Basic validation for now
        if total_records == 0:
            data_issues.append("No spatial data records found")
        
        return DataValidationResult(
            passed=len(errors) == 0,
            total_records=total_records,
            valid_records=total_records,
            invalid_records=0,
            missing_required_fields=[],
            data_quality_issues=data_issues,
            validation_errors=errors
        )
    
    def _validate_exchange_rate_data(self, df: pd.DataFrame) -> DataValidationResult:
        """Validate exchange rate data."""
        errors = []
        missing_fields = []
        data_issues = []
        
        required_fields = ['date', 'rate', 'currency_pair']
        
        # Check required fields
        for field in required_fields:
            if field not in df.columns:
                missing_fields.append(field)
            elif df[field].isna().any():
                data_issues.append(f"Missing values in {field}: {df[field].isna().sum()} records")
        
        # Validate rates
        if 'rate' in df.columns:
            invalid_rates = ((df['rate'] <= 0) | (df['rate'] > 10000)).sum()
            if invalid_rates > 0:
                data_issues.append(f"Invalid exchange rates: {invalid_rates} records")
        
        valid_records = len(df.dropna(subset=required_fields))
        
        return DataValidationResult(
            passed=len(missing_fields) == 0 and len(errors) == 0,
            total_records=len(df),
            valid_records=valid_records,
            invalid_records=len(df) - valid_records,
            missing_required_fields=missing_fields,
            data_quality_issues=data_issues,
            validation_errors=errors
        )
    
    # Utility methods
    def _calculate_file_checksum(self, file_path: Path) -> str:
        """Calculate SHA256 checksum of file."""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        return sha256_hash.hexdigest()
    
    def _lookup_market_details(self, market_name: str) -> Dict[str, Any]:
        """Look up additional market details from spatial data."""
        # This would normally query spatial data files
        # For now, return defaults based on market name
        defaults = {
            "governorate": self._infer_governorate_from_name(market_name),
            "district": "",
            "latitude": 0.0,
            "longitude": 0.0,
            "market_type": "retail"
        }
        return defaults
    
    def _infer_governorate_from_name(self, market_name: str) -> str:
        """Infer governorate from market name."""
        governorate_mappings = {
            "Sana'a": "Amanat Al Asimah",
            "Aden": "Aden",
            "Taiz": "Taiz",
            "Al Bayda": "Al Bayda",
            "Ibb": "Ibb",
            "Dhamar": "Dhamar",
            "Amran": "Amran",
            "Mahweet": "Al Mahwit",
            "Marib": "Marib",
            "Mukalla": "Hadramawt",
            "Sayoun": "Hadramawt",
            "Al Ghaidha": "Al Mahrah",
            "Soqatra": "Socotra"
        }
        
        for city, governorate in governorate_mappings.items():
            if city.lower() in market_name.lower():
                return governorate
        
        return "Unknown"
    
    def _get_commodity_details(self, commodity_name: str) -> Dict[str, Any]:
        """Get commodity classification details."""
        commodity_classifications = {
            "Wheat": {"category": "grains", "unit": "kg", "is_fuel": False, "is_food": True, "perishability": "low"},
            "Wheat flour": {"category": "grains", "unit": "kg", "is_fuel": False, "is_food": True, "perishability": "low"},
            "Rice (imported)": {"category": "grains", "unit": "kg", "is_fuel": False, "is_food": True, "perishability": "low"},
            "Sugar": {"category": "food", "unit": "kg", "is_fuel": False, "is_food": True, "perishability": "low"},
            "Oil (vegetable)": {"category": "food", "unit": "liter", "is_fuel": False, "is_food": True, "perishability": "medium"},
            "Salt": {"category": "food", "unit": "kg", "is_fuel": False, "is_food": True, "perishability": "low"},
            "Beans (kidney red)": {"category": "legumes", "unit": "kg", "is_fuel": False, "is_food": True, "perishability": "low"},
            "Beans (white)": {"category": "legumes", "unit": "kg", "is_fuel": False, "is_food": True, "perishability": "low"},
            "Lentils": {"category": "legumes", "unit": "kg", "is_fuel": False, "is_food": True, "perishability": "low"},
            "Eggs": {"category": "protein", "unit": "dozen", "is_fuel": False, "is_food": True, "perishability": "high"},
            "Tomatoes": {"category": "vegetables", "unit": "kg", "is_fuel": False, "is_food": True, "perishability": "high"},
            "Onions": {"category": "vegetables", "unit": "kg", "is_fuel": False, "is_food": True, "perishability": "medium"},
            "Potatoes": {"category": "vegetables", "unit": "kg", "is_fuel": False, "is_food": True, "perishability": "medium"},
            "Fuel (diesel)": {"category": "fuel", "unit": "liter", "is_fuel": True, "is_food": False, "perishability": "none"},
            "Fuel (petrol-gasoline)": {"category": "fuel", "unit": "liter", "is_fuel": True, "is_food": False, "perishability": "none"},
            "Fuel (gas)": {"category": "fuel", "unit": "kg", "is_fuel": True, "is_food": False, "perishability": "none"}
        }
        
        return commodity_classifications.get(commodity_name, {
            "category": "food", "unit": "kg", "is_fuel": False, "is_food": True, "perishability": "medium"
        })
    
    def _calculate_market_coverage(self, market_name: str) -> float:
        """Calculate data coverage percentage for a market."""
        # This would normally analyze the actual data coverage
        # For now, return a reasonable estimate
        return np.random.uniform(0.85, 0.95)  # Between 85-95% coverage
    
    def _transform_acled_to_v2_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """Transform ACLED data to V2 conflict event format."""
        # Map ACLED columns to V2 schema
        column_mapping = {
            'data_date': 'event_date',
            'event_id_cnty': 'event_id',
            'event_type': 'event_type',
            'sub_event_type': 'sub_event_type',
            'latitude': 'latitude',
            'longitude': 'longitude',
            'admin1': 'governorate',
            'admin2': 'district',
            'fatalities': 'fatalities',
            'notes': 'notes',
            'source': 'source',
            'source_scale': 'source_scale'
        }
        
        # Select and rename columns
        available_cols = [col for col in column_mapping.keys() if col in df.columns]
        df_mapped = df[available_cols].rename(columns=column_mapping)
        
        # Standardize data types
        if 'event_date' in df_mapped.columns:
            df_mapped['event_date'] = pd.to_datetime(df_mapped['event_date'])
        
        if 'fatalities' in df_mapped.columns:
            df_mapped['fatalities'] = pd.to_numeric(df_mapped['fatalities'], errors='coerce').fillna(0)
        
        # Add default values for missing columns
        if 'source' not in df_mapped.columns:
            df_mapped['source'] = 'ACLED'
        
        return df_mapped
    
    def _process_three_tier_results(self, results_dir: Path) -> List[Dict[str, Any]]:
        """Process three-tier analysis results."""
        results = []
        
        # Process tier-specific results
        for tier_dir in results_dir.glob("tier*"):
            if tier_dir.is_dir():
                tier_num = tier_dir.name.replace("tier", "")
                
                for result_file in tier_dir.glob("*.json"):
                    try:
                        with open(result_file) as f:
                            result_data = json.load(f)
                        
                        analysis_id = f"three_tier_tier{tier_num}_{result_file.stem}"
                        
                        results.append({
                            "analysis_id": analysis_id,
                            "analysis_type": f"three_tier_tier{tier_num}",
                            "parameters": json.dumps(result_data.get("parameters", {})),
                            "results": json.dumps(result_data),
                            "status": "completed",
                            "created_at": datetime.now(),
                            "completed_at": datetime.now(),
                            "metadata": json.dumps({
                                "tier": tier_num,
                                "source_file": result_file.name,
                                "migration_source": "V1_three_tier_analysis"
                            })
                        })
                    except Exception as e:
                        self.logger.warning(f"Failed to process {result_file}: {e}")
        
        return results
    
    def _process_v1_validation_results(self, validation_dir: Path) -> List[Dict[str, Any]]:
        """Process V1 model validation results."""
        results = []
        
        for result_file in validation_dir.glob("*.json"):
            try:
                with open(result_file) as f:
                    result_data = json.load(f)
                
                analysis_id = f"v1_validation_{result_file.stem}"
                
                results.append({
                    "analysis_id": analysis_id,
                    "analysis_type": "v1_model_validation",
                    "parameters": json.dumps({}),
                    "results": json.dumps(result_data),
                    "status": "completed",
                    "created_at": datetime.now(),
                    "completed_at": datetime.now(),
                    "metadata": json.dumps({
                        "source_file": result_file.name,
                        "migration_source": "V1_model_validation"
                    })
                })
            except Exception as e:
                self.logger.warning(f"Failed to process {result_file}: {e}")
        
        return results
    
    def _transform_generic_result(self, filename: str, result_data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform generic result file to V2 format."""
        analysis_id = f"generic_{filename.replace('.json', '')}"
        
        return {
            "analysis_id": analysis_id,
            "analysis_type": "generic_v1_result",
            "parameters": json.dumps({}),
            "results": json.dumps(result_data),
            "status": "completed",
            "created_at": datetime.now(),
            "completed_at": datetime.now(),
            "metadata": json.dumps({
                "source_file": filename,
                "migration_source": "V1_generic_result"
            })
        }
    
    def _transform_exchange_rates_to_v2(self, df: pd.DataFrame) -> pd.DataFrame:
        """Transform exchange rate data to V2 format."""
        # Map columns to V2 schema
        if 'date' not in df.columns and 'Date' in df.columns:
            df['date'] = df['Date']
        
        if 'rate' not in df.columns:
            # Look for rate columns
            rate_cols = [col for col in df.columns if 'rate' in col.lower() or 'yer' in col.lower()]
            if rate_cols:
                df['rate'] = df[rate_cols[0]]
        
        # Add currency pair information
        df['currency_pair'] = 'YER/USD'
        df['rate_type'] = 'official'
        df['source'] = 'WFP'
        
        # Standardize date
        df['date'] = pd.to_datetime(df['date'])
        
        return df[['date', 'rate', 'currency_pair', 'rate_type', 'source']]
    
    def _generate_export_summary(self, export_results: Dict[str, ExportMetadata]) -> None:
        """Generate comprehensive export summary."""
        summary = {
            "export_timestamp": datetime.now().isoformat(),
            "total_exports": len(export_results),
            "successful_exports": sum(1 for r in export_results.values() if r.validation_passed),
            "failed_exports": sum(1 for r in export_results.values() if not r.validation_passed),
            "total_records_exported": sum(r.record_count for r in export_results.values()),
            "total_size_bytes": sum(r.file_size_bytes for r in export_results.values()),
            "data_coverage_achieved": self._calculate_overall_coverage(export_results),
            "exports": {k: asdict(v) for k, v in export_results.items()},
            "validation_summary": {k: asdict(v) for k, v in self.validation_results.items()}
        }
        
        # Write summary
        summary_file = self.export_path / f"export_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        self.logger.info(f"Export summary written to {summary_file}")
    
    def _calculate_overall_coverage(self, export_results: Dict[str, ExportMetadata]) -> float:
        """Calculate overall data coverage from export results."""
        # This would calculate actual coverage based on the exported data
        # For now, return a reasonable estimate based on validation results
        if "price_data" in self.validation_results:
            price_validation = self.validation_results["price_data"]
            if price_validation.total_records > 0:
                return price_validation.valid_records / price_validation.total_records
        
        return self.v1_coverage_target  # Default to target coverage