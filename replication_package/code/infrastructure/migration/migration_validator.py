"""Comprehensive migration validation and integrity checking system."""

import asyncio
import asyncpg
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Set
import logging
from dataclasses import dataclass, asdict
import json
from decimal import Decimal
import hashlib
import time

from src.infrastructure.observability.structured_logging import StructuredLogger


@dataclass
class ValidationRule:
    """Definition of a validation rule."""
    rule_id: str
    rule_name: str
    rule_type: str  # 'count', 'integrity', 'quality', 'consistency', 'performance'
    severity: str  # 'critical', 'warning', 'info'
    query: str
    expected_result: Any
    tolerance: Optional[float] = None
    description: str = ""


@dataclass
class ValidationResult:
    """Result of a validation check."""
    rule_id: str
    rule_name: str
    rule_type: str
    severity: str
    passed: bool
    actual_result: Any
    expected_result: Any
    deviation: Optional[float]
    execution_time_ms: float
    error_message: Optional[str]
    recommendations: List[str]


@dataclass
class MigrationValidationReport:
    """Comprehensive migration validation report."""
    validation_id: str
    validation_timestamp: datetime
    overall_status: str  # 'passed', 'passed_with_warnings', 'failed'
    total_rules: int
    passed_rules: int
    failed_rules: int
    warning_rules: int
    critical_failures: List[ValidationResult]
    warnings: List[ValidationResult]
    data_coverage_report: Dict[str, float]
    performance_metrics: Dict[str, Any]
    recommendations: List[str]
    detailed_results: List[ValidationResult]


class MigrationValidator:
    """Comprehensive migration validation system."""
    
    def __init__(
        self,
        v2_connection_string: str,
        v1_data_path: Optional[Path] = None,
        coverage_target: float = 0.884  # 88.4% from V1 system
    ):
        """Initialize the migration validator."""
        self.v2_connection_string = v2_connection_string
        self.v1_data_path = v1_data_path
        self.coverage_target = coverage_target
        self.logger = StructuredLogger("MigrationValidator")
        
        # Load validation rules
        self.validation_rules = self._get_validation_rules()
        
        # Validation state
        self.connection_pool = None
        self.validation_cache = {}
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._initialize_connection_pool()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self._cleanup_connection_pool()
    
    async def _initialize_connection_pool(self):
        """Initialize database connection pool."""
        try:
            self.connection_pool = await asyncpg.create_pool(
                self.v2_connection_string,
                min_size=2,
                max_size=5,
                command_timeout=300
            )
            self.logger.info("Validation connection pool initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize connection pool: {e}")
            raise
    
    async def _cleanup_connection_pool(self):
        """Cleanup connection pool."""
        if self.connection_pool:
            await self.connection_pool.close()
            self.logger.info("Validation connection pool closed")
    
    async def validate_migration(
        self,
        rule_types: Optional[List[str]] = None,
        severity_filter: Optional[List[str]] = None
    ) -> MigrationValidationReport:
        """Run comprehensive migration validation."""
        validation_id = f"migration_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.logger.info(f"Starting migration validation: {validation_id}")
        
        start_time = datetime.now()
        
        # Filter rules based on criteria
        rules_to_run = self._filter_validation_rules(rule_types, severity_filter)
        
        # Run validation rules
        validation_results = []
        for rule in rules_to_run:
            try:
                result = await self._execute_validation_rule(rule)
                validation_results.append(result)
                
                # Log critical failures immediately
                if result.severity == 'critical' and not result.passed:
                    self.logger.error(f"Critical validation failure: {result.rule_name}")
                
            except Exception as e:
                self.logger.error(f"Validation rule execution failed: {rule.rule_id} - {e}")
                validation_results.append(ValidationResult(
                    rule_id=rule.rule_id,
                    rule_name=rule.rule_name,
                    rule_type=rule.rule_type,
                    severity=rule.severity,
                    passed=False,
                    actual_result=None,
                    expected_result=rule.expected_result,
                    deviation=None,
                    execution_time_ms=0,
                    error_message=str(e),
                    recommendations=[f"Review rule implementation: {rule.rule_id}"]
                ))
        
        # Analyze results
        passed_results = [r for r in validation_results if r.passed]
        failed_results = [r for r in validation_results if not r.passed]
        critical_failures = [r for r in failed_results if r.severity == 'critical']
        warnings = [r for r in failed_results if r.severity == 'warning']
        
        # Determine overall status
        if critical_failures:
            overall_status = 'failed'
        elif warnings:
            overall_status = 'passed_with_warnings'
        else:
            overall_status = 'passed'
        
        # Generate data coverage report
        coverage_report = await self._generate_coverage_report()
        
        # Generate performance metrics
        performance_metrics = await self._generate_performance_metrics()
        
        # Generate recommendations
        recommendations = self._generate_recommendations(validation_results, coverage_report)
        
        # Create comprehensive report
        report = MigrationValidationReport(
            validation_id=validation_id,
            validation_timestamp=start_time,
            overall_status=overall_status,
            total_rules=len(validation_results),
            passed_rules=len(passed_results),
            failed_rules=len(failed_results),
            warning_rules=len(warnings),
            critical_failures=critical_failures,
            warnings=warnings,
            data_coverage_report=coverage_report,
            performance_metrics=performance_metrics,
            recommendations=recommendations,
            detailed_results=validation_results
        )
        
        self.logger.info(
            f"Migration validation completed: {len(passed_results)}/{len(validation_results)} passed, "
            f"Status: {overall_status}"
        )
        
        return report
    
    async def _execute_validation_rule(self, rule: ValidationRule) -> ValidationResult:
        """Execute a single validation rule."""
        start_time = time.perf_counter()
        
        try:
            async with self.connection_pool.acquire() as connection:
                # Execute the validation query
                if rule.rule_type == 'count':
                    actual_result = await connection.fetchval(rule.query)
                elif rule.rule_type in ['integrity', 'quality', 'consistency']:
                    # These typically return multiple rows or complex results
                    rows = await connection.fetch(rule.query)
                    actual_result = [dict(row) for row in rows] if rows else []
                elif rule.rule_type == 'performance':
                    # Performance queries might return timing or resource metrics
                    actual_result = await connection.fetchval(rule.query)
                else:
                    # Generic query execution
                    result = await connection.fetch(rule.query)
                    actual_result = [dict(row) for row in result] if result else []
                
                execution_time_ms = (time.perf_counter() - start_time) * 1000
                
                # Evaluate the result
                passed, deviation = self._evaluate_validation_result(
                    rule, actual_result
                )
                
                # Generate recommendations
                recommendations = self._generate_rule_recommendations(rule, actual_result, passed)
                
                return ValidationResult(
                    rule_id=rule.rule_id,
                    rule_name=rule.rule_name,
                    rule_type=rule.rule_type,
                    severity=rule.severity,
                    passed=passed,
                    actual_result=actual_result,
                    expected_result=rule.expected_result,
                    deviation=deviation,
                    execution_time_ms=execution_time_ms,
                    error_message=None,
                    recommendations=recommendations
                )
                
        except Exception as e:
            execution_time_ms = (time.perf_counter() - start_time) * 1000
            return ValidationResult(
                rule_id=rule.rule_id,
                rule_name=rule.rule_name,
                rule_type=rule.rule_type,
                severity=rule.severity,
                passed=False,
                actual_result=None,
                expected_result=rule.expected_result,
                deviation=None,
                execution_time_ms=execution_time_ms,
                error_message=str(e),
                recommendations=[f"Fix query execution error: {str(e)}"]
            )
    
    def _evaluate_validation_result(
        self, 
        rule: ValidationRule, 
        actual_result: Any
    ) -> Tuple[bool, Optional[float]]:
        """Evaluate if validation result passes the rule."""
        if rule.rule_type == 'count':
            # For count rules, check if actual is within tolerance of expected
            if isinstance(rule.expected_result, (int, float)) and isinstance(actual_result, (int, float)):
                if rule.tolerance:
                    deviation = abs(actual_result - rule.expected_result) / rule.expected_result
                    passed = deviation <= rule.tolerance
                    return passed, deviation
                else:
                    passed = actual_result == rule.expected_result
                    deviation = abs(actual_result - rule.expected_result) if rule.expected_result != 0 else None
                    return passed, deviation
            else:
                return actual_result == rule.expected_result, None
        
        elif rule.rule_type in ['integrity', 'quality', 'consistency']:
            # For these rules, typically empty result means pass
            if isinstance(actual_result, list):
                passed = len(actual_result) == 0
                return passed, len(actual_result) if not passed else None
            else:
                return actual_result == rule.expected_result, None
        
        elif rule.rule_type == 'performance':
            # For performance rules, check if actual is below threshold
            if isinstance(rule.expected_result, (int, float)) and isinstance(actual_result, (int, float)):
                passed = actual_result <= rule.expected_result
                deviation = (actual_result - rule.expected_result) / rule.expected_result if rule.expected_result > 0 else None
                return passed, deviation
            else:
                return actual_result == rule.expected_result, None
        
        else:
            # Default comparison
            return actual_result == rule.expected_result, None
    
    def _generate_rule_recommendations(
        self, 
        rule: ValidationRule, 
        actual_result: Any, 
        passed: bool
    ) -> List[str]:
        """Generate recommendations based on rule result."""
        recommendations = []
        
        if not passed:
            if rule.rule_type == 'count':
                if isinstance(actual_result, (int, float)) and isinstance(rule.expected_result, (int, float)):
                    if actual_result < rule.expected_result:
                        recommendations.append(f"Data count is lower than expected. Check for incomplete migration.")
                    else:
                        recommendations.append(f"Data count is higher than expected. Check for duplicates.")
            
            elif rule.rule_type == 'integrity':
                recommendations.append("Data integrity issues found. Review foreign key relationships and constraints.")
            
            elif rule.rule_type == 'quality':
                recommendations.append("Data quality issues detected. Review data cleaning and transformation processes.")
            
            elif rule.rule_type == 'consistency':
                recommendations.append("Data consistency issues found. Check transformation logic and business rules.")
            
            elif rule.rule_type == 'performance':
                recommendations.append("Performance threshold exceeded. Consider indexing or query optimization.")
        
        return recommendations
    
    async def _generate_coverage_report(self) -> Dict[str, float]:
        """Generate data coverage report."""
        coverage_report = {}
        
        try:
            async with self.connection_pool.acquire() as connection:
                # Market coverage
                market_stats = await connection.fetchrow("""
                    SELECT 
                        COUNT(*) as total_markets,
                        COUNT(CASE WHEN latitude != 0 OR longitude != 0 THEN 1 END) as markets_with_coordinates,
                        COUNT(CASE WHEN active_until IS NULL THEN 1 END) as active_markets
                    FROM markets
                """)
                
                if market_stats['total_markets'] > 0:
                    coverage_report['market_coordinate_coverage'] = market_stats['markets_with_coordinates'] / market_stats['total_markets']
                    coverage_report['active_market_ratio'] = market_stats['active_markets'] / market_stats['total_markets']
                
                # Price data coverage
                price_stats = await connection.fetchrow("""
                    SELECT 
                        COUNT(*) as total_prices,
                        COUNT(DISTINCT market_id) as markets_with_prices,
                        COUNT(DISTINCT commodity_code) as commodities_with_prices,
                        COUNT(DISTINCT DATE_TRUNC('month', observed_date)) as months_with_data
                    FROM price_observations
                """)
                
                if price_stats['total_prices'] > 0:
                    # Calculate expected vs actual observations
                    expected_observations = await connection.fetchval("""
                        SELECT 
                            (SELECT COUNT(*) FROM markets) * 
                            (SELECT COUNT(*) FROM commodities) *
                            (SELECT DATE_PART('year', AGE(MAX(observed_date), MIN(observed_date))) * 12 + 
                                    DATE_PART('month', AGE(MAX(observed_date), MIN(observed_date))) + 1
                             FROM price_observations)
                    """)
                    
                    if expected_observations and expected_observations > 0:
                        coverage_report['price_data_coverage'] = price_stats['total_prices'] / expected_observations
                
                # Temporal coverage
                date_range = await connection.fetchrow("""
                    SELECT 
                        MIN(observed_date) as min_date,
                        MAX(observed_date) as max_date,
                        COUNT(DISTINCT DATE_TRUNC('month', observed_date)) as unique_months
                    FROM price_observations
                """)
                
                if date_range['min_date'] and date_range['max_date']:
                    total_months = (date_range['max_date'].year - date_range['min_date'].year) * 12 + \
                                 (date_range['max_date'].month - date_range['min_date'].month) + 1
                    coverage_report['temporal_coverage'] = date_range['unique_months'] / total_months if total_months > 0 else 0
                
                # Conflict data coverage
                conflict_stats = await connection.fetchrow("""
                    SELECT 
                        COUNT(*) as total_conflicts,
                        COUNT(CASE WHEN fatalities > 0 THEN 1 END) as conflicts_with_fatalities,
                        COUNT(DISTINCT governorate) as governorates_with_conflicts
                    FROM conflict_events
                """)
                
                if conflict_stats['total_conflicts'] > 0:
                    coverage_report['conflict_fatality_coverage'] = conflict_stats['conflicts_with_fatalities'] / conflict_stats['total_conflicts']
                
                # Overall coverage assessment
                coverage_values = [v for v in coverage_report.values() if isinstance(v, (int, float))]
                if coverage_values:
                    coverage_report['overall_coverage'] = sum(coverage_values) / len(coverage_values)
                
        except Exception as e:
            self.logger.error(f"Failed to generate coverage report: {e}")
            coverage_report['error'] = str(e)
        
        return coverage_report
    
    async def _generate_performance_metrics(self) -> Dict[str, Any]:
        """Generate performance metrics for migrated data."""
        metrics = {}
        
        try:
            async with self.connection_pool.acquire() as connection:
                # Table sizes and row counts
                table_stats = await connection.fetch("""
                    SELECT 
                        schemaname,
                        tablename,
                        n_tup_ins as inserts,
                        n_tup_upd as updates,
                        n_tup_del as deletes,
                        n_live_tup as live_tuples,
                        n_dead_tup as dead_tuples
                    FROM pg_stat_user_tables
                    WHERE schemaname = 'public'
                    ORDER BY n_live_tup DESC
                """)
                
                metrics['table_statistics'] = [dict(row) for row in table_stats]
                
                # Index usage
                index_stats = await connection.fetch("""
                    SELECT 
                        schemaname,
                        tablename,
                        indexname,
                        idx_tup_read,
                        idx_tup_fetch
                    FROM pg_stat_user_indexes
                    WHERE schemaname = 'public'
                    ORDER BY idx_tup_read DESC
                    LIMIT 20
                """)
                
                metrics['index_usage'] = [dict(row) for row in index_stats]
                
                # Database size
                db_size = await connection.fetchval("SELECT pg_database_size(current_database())")
                metrics['database_size_mb'] = db_size / (1024 * 1024) if db_size else 0
                
                # Query performance samples
                query_performance = {}
                
                # Sample complex queries and measure performance
                sample_queries = {
                    'price_aggregation': """
                        SELECT market_id, commodity_code, AVG(price_amount)
                        FROM price_observations 
                        WHERE observed_date >= NOW() - INTERVAL '1 year'
                        GROUP BY market_id, commodity_code
                        LIMIT 100
                    """,
                    'market_lookup': """
                        SELECT * FROM markets 
                        WHERE governorate = 'Amanat Al Asimah'
                    """,
                    'conflict_summary': """
                        SELECT governorate, COUNT(*), SUM(fatalities)
                        FROM conflict_events
                        WHERE event_date >= NOW() - INTERVAL '1 year'
                        GROUP BY governorate
                    """
                }
                
                for query_name, query in sample_queries.items():
                    start_time = time.perf_counter()
                    try:
                        await connection.fetch(query)
                        execution_time = (time.perf_counter() - start_time) * 1000
                        query_performance[query_name] = execution_time
                    except Exception as e:
                        query_performance[query_name] = f"Error: {str(e)}"
                
                metrics['query_performance_ms'] = query_performance
                
        except Exception as e:
            self.logger.error(f"Failed to generate performance metrics: {e}")
            metrics['error'] = str(e)
        
        return metrics
    
    def _generate_recommendations(
        self, 
        validation_results: List[ValidationResult],
        coverage_report: Dict[str, float]
    ) -> List[str]:
        """Generate overall recommendations based on validation results."""
        recommendations = []
        
        # Analyze critical failures
        critical_failures = [r for r in validation_results if r.severity == 'critical' and not r.passed]
        if critical_failures:
            recommendations.append(f"CRITICAL: {len(critical_failures)} critical validation failures detected. Migration should not proceed to production.")
            for failure in critical_failures[:3]:  # Show top 3
                recommendations.append(f"  - {failure.rule_name}: {failure.error_message or 'Check detailed results'}")
        
        # Analyze warnings
        warnings = [r for r in validation_results if r.severity == 'warning' and not r.passed]
        if warnings:
            recommendations.append(f"WARNING: {len(warnings)} validation warnings. Review before production deployment.")
        
        # Coverage analysis
        overall_coverage = coverage_report.get('overall_coverage', 0)
        if overall_coverage < self.coverage_target:
            recommendations.append(
                f"Data coverage ({overall_coverage:.1%}) is below target ({self.coverage_target:.1%}). "
                "Review migration completeness."
            )
        
        # Price data specific recommendations
        price_coverage = coverage_report.get('price_data_coverage', 0)
        if price_coverage < 0.8:
            recommendations.append(
                "Price data coverage is low. Verify all price sources were migrated correctly."
            )
        
        # Temporal coverage
        temporal_coverage = coverage_report.get('temporal_coverage', 0)
        if temporal_coverage < 0.9:
            recommendations.append(
                "Temporal coverage gaps detected. Check for missing time periods in price data."
            )
        
        # Performance recommendations
        failed_performance = [r for r in validation_results if r.rule_type == 'performance' and not r.passed]
        if failed_performance:
            recommendations.append(
                "Performance issues detected. Consider adding indexes or optimizing queries."
            )
        
        # Success recommendations
        if not critical_failures and len(warnings) <= 2:
            recommendations.append(
                "Migration validation passed successfully. Ready for production deployment."
            )
        
        return recommendations
    
    def _filter_validation_rules(
        self,
        rule_types: Optional[List[str]] = None,
        severity_filter: Optional[List[str]] = None
    ) -> List[ValidationRule]:
        """Filter validation rules based on criteria."""
        filtered_rules = self.validation_rules
        
        if rule_types:
            filtered_rules = [r for r in filtered_rules if r.rule_type in rule_types]
        
        if severity_filter:
            filtered_rules = [r for r in filtered_rules if r.severity in severity_filter]
        
        return filtered_rules
    
    def _get_validation_rules(self) -> List[ValidationRule]:
        """Get comprehensive set of validation rules."""
        return [
            # Count validation rules
            ValidationRule(
                rule_id="count_markets",
                rule_name="Market Count Validation",
                rule_type="count",
                severity="critical",
                query="SELECT COUNT(*) FROM markets",
                expected_result=21,  # From V1 balanced panel metadata
                tolerance=0.05,
                description="Verify expected number of markets migrated"
            ),
            
            ValidationRule(
                rule_id="count_commodities",
                rule_name="Commodity Count Validation",
                rule_type="count",
                severity="critical",
                query="SELECT COUNT(*) FROM commodities",
                expected_result=16,  # From V1 balanced panel metadata
                tolerance=0.1,
                description="Verify expected number of commodities migrated"
            ),
            
            ValidationRule(
                rule_id="count_prices_minimum",
                rule_name="Minimum Price Observations",
                rule_type="count",
                severity="critical",
                query="SELECT COUNT(*) FROM price_observations",
                expected_result=20000,  # Minimum threshold
                tolerance=None,
                description="Verify minimum number of price observations"
            ),
            
            # Integrity validation rules
            ValidationRule(
                rule_id="integrity_market_references",
                rule_name="Market Reference Integrity",
                rule_type="integrity",
                severity="critical",
                query="""
                    SELECT DISTINCT po.market_id 
                    FROM price_observations po 
                    LEFT JOIN markets m ON po.market_id = m.market_id 
                    WHERE m.market_id IS NULL
                    LIMIT 10
                """,
                expected_result=[],
                description="Check for orphaned market references in price data"
            ),
            
            ValidationRule(
                rule_id="integrity_commodity_references",
                rule_name="Commodity Reference Integrity",
                rule_type="integrity",
                severity="critical",
                query="""
                    SELECT DISTINCT po.commodity_code 
                    FROM price_observations po 
                    LEFT JOIN commodities c ON po.commodity_code = c.code 
                    WHERE c.code IS NULL
                    LIMIT 10
                """,
                expected_result=[],
                description="Check for orphaned commodity references in price data"
            ),
            
            # Quality validation rules
            ValidationRule(
                rule_id="quality_negative_prices",
                rule_name="Negative Price Detection",
                rule_type="quality",
                severity="critical",
                query="""
                    SELECT market_id, commodity_code, price_amount, observed_date
                    FROM price_observations 
                    WHERE price_amount <= 0
                    LIMIT 10
                """,
                expected_result=[],
                description="Check for negative or zero prices"
            ),
            
            ValidationRule(
                rule_id="quality_extreme_prices",
                rule_name="Extreme Price Detection",
                rule_type="quality",
                severity="warning",
                query="""
                    WITH price_stats AS (
                        SELECT 
                            commodity_code,
                            AVG(price_amount) as avg_price,
                            STDDEV(price_amount) as stddev_price
                        FROM price_observations
                        GROUP BY commodity_code
                    )
                    SELECT po.market_id, po.commodity_code, po.price_amount
                    FROM price_observations po
                    JOIN price_stats ps ON po.commodity_code = ps.commodity_code
                    WHERE po.price_amount > ps.avg_price + 5 * ps.stddev_price
                       OR po.price_amount < ps.avg_price - 5 * ps.stddev_price
                    LIMIT 10
                """,
                expected_result=[],
                description="Check for extreme price outliers"
            ),
            
            ValidationRule(
                rule_id="quality_invalid_coordinates",
                rule_name="Invalid Coordinates Detection",
                rule_type="quality",
                severity="warning",
                query="""
                    SELECT market_id, latitude, longitude
                    FROM markets 
                    WHERE latitude < -90 OR latitude > 90 
                       OR longitude < -180 OR longitude > 180
                    LIMIT 10
                """,
                expected_result=[],
                description="Check for invalid geographic coordinates"
            ),
            
            ValidationRule(
                rule_id="quality_future_dates",
                rule_name="Future Date Detection",
                rule_type="quality",
                severity="warning",
                query="""
                    SELECT market_id, commodity_code, observed_date
                    FROM price_observations 
                    WHERE observed_date > NOW() + INTERVAL '30 days'
                    LIMIT 10
                """,
                expected_result=[],
                description="Check for future dates in price data"
            ),
            
            ValidationRule(
                rule_id="quality_old_dates",
                rule_name="Pre-2019 Date Detection",
                rule_type="quality",
                severity="warning",
                query="""
                    SELECT market_id, commodity_code, observed_date
                    FROM price_observations 
                    WHERE observed_date < '2019-01-01'
                    LIMIT 10
                """,
                expected_result=[],
                description="Check for dates before expected data range"
            ),
            
            # Consistency validation rules
            ValidationRule(
                rule_id="consistency_market_names",
                rule_name="Market Name Consistency",
                rule_type="consistency",
                severity="warning",
                query="""
                    SELECT market_id, COUNT(DISTINCT name) as name_variations
                    FROM (
                        SELECT market_id, name FROM markets
                        UNION ALL
                        SELECT DISTINCT market_id, market_id as name FROM price_observations
                    ) combined
                    GROUP BY market_id
                    HAVING COUNT(DISTINCT name) > 1
                    LIMIT 10
                """,
                expected_result=[],
                description="Check for inconsistent market naming"
            ),
            
            ValidationRule(
                rule_id="consistency_price_currencies",
                rule_name="Price Currency Distribution",
                rule_type="consistency",
                severity="info",
                query="""
                    SELECT price_currency, COUNT(*) as count
                    FROM price_observations
                    GROUP BY price_currency
                    ORDER BY count DESC
                """,
                expected_result=None,  # Informational only
                description="Review currency distribution in price data"
            ),
            
            # Performance validation rules
            ValidationRule(
                rule_id="performance_price_query",
                rule_name="Price Query Performance",
                rule_type="performance",
                severity="warning",
                query="""
                    SELECT COUNT(*)
                    FROM price_observations 
                    WHERE observed_date >= NOW() - INTERVAL '1 year'
                      AND market_id = 'SANAA_CITY'
                      AND commodity_code = 'WHEAT'
                """,
                expected_result=2000,  # Maximum execution time in ms (measured separately)
                description="Check query performance on indexed columns"
            ),
            
            ValidationRule(
                rule_id="performance_market_lookup",
                rule_name="Market Lookup Performance",
                rule_type="performance",
                severity="warning",
                query="SELECT COUNT(*) FROM markets WHERE governorate = 'Amanat Al Asimah'",
                expected_result=1000,  # Maximum execution time in ms
                description="Check market lookup performance"
            ),
            
            # Coverage validation rules
            ValidationRule(
                rule_id="coverage_temporal",
                rule_name="Temporal Coverage Validation",
                rule_type="count",
                severity="warning",
                query="""
                    SELECT COUNT(DISTINCT DATE_TRUNC('month', observed_date))
                    FROM price_observations
                    WHERE observed_date >= '2019-01-01'
                """,
                expected_result=75,  # Expected months from V1 metadata
                tolerance=0.1,
                description="Verify temporal coverage matches V1 system"
            ),
            
            ValidationRule(
                rule_id="coverage_market_commodity_pairs",
                rule_name="Market-Commodity Coverage",
                rule_type="count",
                severity="warning",
                query="""
                    SELECT COUNT(DISTINCT CONCAT(market_id, '_', commodity_code))
                    FROM price_observations
                """,
                expected_result=336,  # 21 markets * 16 commodities
                tolerance=0.2,
                description="Verify market-commodity pair coverage"
            ),
            
            # Analysis results validation
            ValidationRule(
                rule_id="analysis_results_present",
                rule_name="Analysis Results Migration",
                rule_type="count",
                severity="info",
                query="SELECT COUNT(*) FROM analysis_results",
                expected_result=1,  # At least some analysis results
                tolerance=None,
                description="Verify analysis results were migrated"
            ),
            
            ValidationRule(
                rule_id="analysis_json_validity",
                rule_name="Analysis JSON Validity",
                rule_type="quality",
                severity="warning",
                query="""
                    SELECT id, analysis_type
                    FROM analysis_results
                    WHERE NOT (parameters::text ~ '^\\{.*\\}$' AND results::text ~ '^\\{.*\\}$')
                    LIMIT 5
                """,
                expected_result=[],
                description="Check for invalid JSON in analysis results"
            )
        ]
    
    async def quick_validation(self) -> Dict[str, Any]:
        """Run quick validation checks for basic migration health."""
        quick_results = {}
        
        try:
            async with self.connection_pool.acquire() as connection:
                # Basic counts
                quick_results['table_counts'] = {
                    'markets': await connection.fetchval("SELECT COUNT(*) FROM markets"),
                    'commodities': await connection.fetchval("SELECT COUNT(*) FROM commodities"),
                    'price_observations': await connection.fetchval("SELECT COUNT(*) FROM price_observations"),
                    'conflict_events': await connection.fetchval("SELECT COUNT(*) FROM conflict_events"),
                    'analysis_results': await connection.fetchval("SELECT COUNT(*) FROM analysis_results")
                }
                
                # Date ranges
                price_dates = await connection.fetchrow("""
                    SELECT MIN(observed_date) as min_date, MAX(observed_date) as max_date
                    FROM price_observations
                """)
                quick_results['price_date_range'] = dict(price_dates) if price_dates else {}
                
                # Basic integrity checks
                orphaned_prices = await connection.fetchval("""
                    SELECT COUNT(*)
                    FROM price_observations po
                    LEFT JOIN markets m ON po.market_id = m.market_id
                    WHERE m.market_id IS NULL
                """)
                quick_results['orphaned_price_records'] = orphaned_prices
                
                # Coverage estimate
                total_expected = quick_results['table_counts']['markets'] * quick_results['table_counts']['commodities']
                actual_pairs = await connection.fetchval("""
                    SELECT COUNT(DISTINCT CONCAT(market_id, '_', commodity_code))
                    FROM price_observations
                """)
                quick_results['market_commodity_coverage'] = actual_pairs / total_expected if total_expected > 0 else 0
                
                # Status summary
                quick_results['status'] = 'healthy' if (
                    quick_results['table_counts']['markets'] >= 20 and
                    quick_results['table_counts']['commodities'] >= 15 and
                    quick_results['table_counts']['price_observations'] >= 10000 and
                    quick_results['orphaned_price_records'] == 0
                ) else 'issues_detected'
                
        except Exception as e:
            quick_results['error'] = str(e)
            quick_results['status'] = 'error'
        
        return quick_results
    
    async def generate_data_quality_report(self) -> Dict[str, Any]:
        """Generate comprehensive data quality report."""
        report = {
            'timestamp': datetime.now().isoformat(),
            'tables': {}
        }
        
        try:
            async with self.connection_pool.acquire() as connection:
                # Market data quality
                market_quality = await connection.fetchrow("""
                    SELECT 
                        COUNT(*) as total_markets,
                        COUNT(CASE WHEN name IS NOT NULL AND name != '' THEN 1 END) as markets_with_names,
                        COUNT(CASE WHEN latitude != 0 OR longitude != 0 THEN 1 END) as markets_with_coordinates,
                        COUNT(CASE WHEN governorate IS NOT NULL AND governorate != '' THEN 1 END) as markets_with_governorate,
                        COUNT(DISTINCT market_id) as unique_market_ids,
                        AVG(CASE WHEN latitude != 0 THEN latitude END) as avg_latitude,
                        AVG(CASE WHEN longitude != 0 THEN longitude END) as avg_longitude
                    FROM markets
                """)
                report['tables']['markets'] = dict(market_quality)
                
                # Price data quality
                price_quality = await connection.fetchrow("""
                    SELECT 
                        COUNT(*) as total_prices,
                        COUNT(CASE WHEN price_amount > 0 THEN 1 END) as positive_prices,
                        AVG(price_amount) as avg_price,
                        MIN(price_amount) as min_price,
                        MAX(price_amount) as max_price,
                        COUNT(DISTINCT market_id) as unique_markets,
                        COUNT(DISTINCT commodity_code) as unique_commodities,
                        COUNT(DISTINCT observed_date) as unique_dates,
                        COUNT(DISTINCT price_currency) as unique_currencies
                    FROM price_observations
                """)
                report['tables']['price_observations'] = dict(price_quality)
                
                # Conflict data quality
                conflict_quality = await connection.fetchrow("""
                    SELECT 
                        COUNT(*) as total_conflicts,
                        COUNT(CASE WHEN fatalities > 0 THEN 1 END) as conflicts_with_fatalities,
                        SUM(fatalities) as total_fatalities,
                        AVG(fatalities) as avg_fatalities,
                        COUNT(DISTINCT event_type) as unique_event_types,
                        COUNT(DISTINCT governorate) as unique_governorates
                    FROM conflict_events
                """)
                report['tables']['conflict_events'] = dict(conflict_quality)
                
                # Data completeness analysis
                completeness = {}
                
                # Price data completeness by market-commodity pairs
                expected_combinations = await connection.fetchval("""
                    SELECT 
                        (SELECT COUNT(*) FROM markets) * 
                        (SELECT COUNT(*) FROM commodities)
                """)
                
                actual_combinations = await connection.fetchval("""
                    SELECT COUNT(DISTINCT CONCAT(market_id, '_', commodity_code))
                    FROM price_observations
                """)
                
                completeness['market_commodity_coverage'] = actual_combinations / expected_combinations if expected_combinations > 0 else 0
                
                report['data_completeness'] = completeness
                
        except Exception as e:
            report['error'] = str(e)
        
        return report
    
    def export_validation_report(
        self, 
        report: MigrationValidationReport, 
        output_path: Path
    ) -> None:
        """Export validation report to file."""
        try:
            # Convert report to serializable format
            report_dict = asdict(report)
            
            # Convert datetime objects to strings
            def convert_datetime(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                elif isinstance(obj, dict):
                    return {k: convert_datetime(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_datetime(item) for item in obj]
                else:
                    return obj
            
            serializable_report = convert_datetime(report_dict)
            
            # Write to file
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w') as f:
                json.dump(serializable_report, f, indent=2, default=str)
            
            self.logger.info(f"Validation report exported to {output_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to export validation report: {e}")
            raise