"""V1 to V2 data migration tool."""

import asyncio
import argparse
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging
from sqlalchemy import create_engine
import asyncpg

from src.core.domain.market.entities import Market, PriceObservation
from src.core.domain.market.value_objects import MarketId, Price, Commodity
from src.infrastructure.persistence.repositories.postgres import (
    MarketRepository, PriceRepository
)


class V1ToV2Migrator:
    """Handles migration of data from v1 to v2."""
    
    def __init__(self, v1_data_path: Path, v2_db_url: str):
        """Initialize migrator."""
        self.v1_data_path = v1_data_path
        self.v2_db_url = v2_db_url
        self.logger = logging.getLogger(__name__)
        
        # Migration statistics
        self.stats = {
            'markets_migrated': 0,
            'prices_migrated': 0,
            'analyses_migrated': 0,
            'errors': []
        }
    
    async def migrate_all(self) -> Dict[str, Any]:
        """Run complete migration."""
        self.logger.info("Starting v1 to v2 migration")
        
        try:
            # Connect to v2 database
            self.conn = await asyncpg.connect(self.v2_db_url)
            
            # Migrate in order
            await self.migrate_markets()
            await self.migrate_commodities()
            await self.migrate_prices()
            await self.migrate_analysis_results()
            
            self.logger.info(f"Migration completed: {self.stats}")
            return self.stats
            
        except Exception as e:
            self.logger.error(f"Migration failed: {e}")
            self.stats['errors'].append(str(e))
            raise
        finally:
            if hasattr(self, 'conn'):
                await self.conn.close()
    
    async def migrate_markets(self) -> None:
        """Migrate market data."""
        self.logger.info("Migrating markets...")
        
        # Load v1 market data
        v1_markets_file = self.v1_data_path / 'data' / 'processed' / 'markets.csv'
        if not v1_markets_file.exists():
            self.logger.warning("No v1 markets file found")
            return
        
        v1_markets = pd.read_csv(v1_markets_file)
        
        # Transform and insert
        for _, row in v1_markets.iterrows():
            try:
                # Map v1 fields to v2 schema
                market_data = {
                    'market_id': row['market_code'],
                    'name': row['market_name'],
                    'governorate': row['governorate'],
                    'district': row.get('district', ''),
                    'latitude': float(row.get('latitude', 0)),
                    'longitude': float(row.get('longitude', 0)),
                    'market_type': self._map_market_type(row.get('type', 'retail')),
                    'active_since': pd.to_datetime(row.get('active_since', '2019-01-01')),
                    'active_until': pd.to_datetime(row.get('active_until')) if pd.notna(row.get('active_until')) else None
                }
                
                # Insert into v2
                await self.conn.execute("""
                    INSERT INTO markets (
                        market_id, name, governorate, district,
                        latitude, longitude, market_type,
                        active_since, active_until
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                    ON CONFLICT (market_id) DO UPDATE
                    SET name = EXCLUDED.name,
                        governorate = EXCLUDED.governorate
                """, *market_data.values())
                
                self.stats['markets_migrated'] += 1
                
            except Exception as e:
                self.logger.error(f"Failed to migrate market {row.get('market_code', 'unknown')}: {e}")
                self.stats['errors'].append(f"Market {row.get('market_code')}: {e}")
    
    async def migrate_commodities(self) -> None:
        """Migrate commodity data."""
        self.logger.info("Migrating commodities...")
        
        # Load v1 commodity mapping
        v1_commodities_file = self.v1_data_path / 'data' / 'processed' / 'commodities.csv'
        if not v1_commodities_file.exists():
            # Use default commodities
            commodities = [
                {'code': 'WHEAT', 'name': 'Wheat', 'category': 'grains', 'unit': 'kg'},
                {'code': 'RICE', 'name': 'Rice (Imported)', 'category': 'grains', 'unit': 'kg'},
                {'code': 'SUGAR', 'name': 'Sugar', 'category': 'food', 'unit': 'kg'},
                {'code': 'OIL', 'name': 'Vegetable Oil', 'category': 'food', 'unit': 'liter'},
                {'code': 'FUEL_DIESEL', 'name': 'Diesel', 'category': 'fuel', 'unit': 'liter'},
                {'code': 'FUEL_PETROL', 'name': 'Petrol', 'category': 'fuel', 'unit': 'liter'},
            ]
        else:
            commodities_df = pd.read_csv(v1_commodities_file)
            commodities = commodities_df.to_dict('records')
        
        # Insert commodities
        for commodity in commodities:
            await self.conn.execute("""
                INSERT INTO commodities (code, name, category, standard_unit)
                VALUES ($1, $2, $3, $4)
                ON CONFLICT (code) DO NOTHING
            """, commodity['code'], commodity['name'], 
            commodity.get('category', 'food'), commodity.get('unit', 'kg'))
    
    async def migrate_prices(self) -> None:
        """Migrate price data."""
        self.logger.info("Migrating prices...")
        
        # Load v1 price data
        v1_prices_file = self.v1_data_path / 'data' / 'processed' / 'prices_panel.csv'
        if not v1_prices_file.exists():
            self.logger.warning("No v1 prices file found")
            return
        
        # Read in chunks for large files
        chunk_size = 10000
        for chunk in pd.read_csv(v1_prices_file, chunksize=chunk_size):
            batch_data = []
            
            for _, row in chunk.iterrows():
                try:
                    # Map v1 fields to v2 schema
                    price_data = (
                        row['market_code'],
                        row['commodity_code'],
                        pd.to_datetime(row['date']),
                        float(row['price']),
                        row.get('currency', 'YER'),
                        row.get('unit', 'kg'),
                        float(row.get('quantity', 0)),
                        row.get('source', 'WFP'),
                        row.get('quality', 'standard'),
                        datetime.now()  # created_at
                    )
                    batch_data.append(price_data)
                    
                except Exception as e:
                    self.logger.error(f"Failed to process price row: {e}")
                    self.stats['errors'].append(f"Price row: {e}")
            
            # Batch insert
            if batch_data:
                await self.conn.executemany("""
                    INSERT INTO price_observations (
                        market_id, commodity_code, date, price,
                        currency, unit, quantity, source,
                        quality, created_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    ON CONFLICT (market_id, commodity_code, date, source) 
                    DO UPDATE SET price = EXCLUDED.price
                """, batch_data)
                
                self.stats['prices_migrated'] += len(batch_data)
        
        self.logger.info(f"Migrated {self.stats['prices_migrated']} price observations")
    
    async def migrate_analysis_results(self) -> None:
        """Migrate analysis results."""
        self.logger.info("Migrating analysis results...")
        
        # Find v1 result files
        results_dir = self.v1_data_path / 'results'
        if not results_dir.exists():
            self.logger.warning("No v1 results directory found")
            return
        
        # Process each result file
        for result_file in results_dir.glob('*.json'):
            try:
                import json
                with open(result_file) as f:
                    v1_result = json.load(f)
                
                # Transform to v2 format
                v2_result = self._transform_analysis_result(v1_result)
                
                # Store in v2
                await self.conn.execute("""
                    INSERT INTO analysis_results (
                        analysis_id, analysis_type, parameters,
                        results, metadata, created_at, completed_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                """, 
                    v2_result['analysis_id'],
                    v2_result['analysis_type'],
                    json.dumps(v2_result['parameters']),
                    json.dumps(v2_result['results']),
                    json.dumps(v2_result['metadata']),
                    v2_result['created_at'],
                    v2_result['completed_at']
                )
                
                self.stats['analyses_migrated'] += 1
                
            except Exception as e:
                self.logger.error(f"Failed to migrate result {result_file}: {e}")
                self.stats['errors'].append(f"Result {result_file.name}: {e}")
    
    def _map_market_type(self, v1_type: str) -> str:
        """Map v1 market type to v2."""
        mapping = {
            'retail': 'retail',
            'wholesale': 'wholesale',
            'border': 'border_crossing',
            'port': 'port'
        }
        return mapping.get(v1_type.lower(), 'retail')
    
    def _transform_analysis_result(self, v1_result: Dict[str, Any]) -> Dict[str, Any]:
        """Transform v1 analysis result to v2 format."""
        # Generate new analysis ID
        analysis_id = f"migrated_{v1_result.get('id', datetime.now().isoformat())}"
        
        # Map result structure
        return {
            'analysis_id': analysis_id,
            'analysis_type': v1_result.get('analysis_type', 'three_tier'),
            'parameters': {
                'start_date': v1_result.get('start_date'),
                'end_date': v1_result.get('end_date'),
                'markets': v1_result.get('markets', []),
                'commodities': v1_result.get('commodities', []),
                'config': v1_result.get('config', {})
            },
            'results': {
                'tier1': v1_result.get('tier1_results', {}),
                'tier2': v1_result.get('tier2_results', {}),
                'tier3': v1_result.get('tier3_results', {})
            },
            'metadata': {
                'version': 'v1_migrated',
                'migration_date': datetime.now().isoformat(),
                'original_file': v1_result.get('filename', 'unknown')
            },
            'created_at': pd.to_datetime(v1_result.get('created_at', datetime.now())),
            'completed_at': pd.to_datetime(v1_result.get('completed_at', datetime.now()))
        }


class MigrationValidator:
    """Validates migration results."""
    
    def __init__(self, v1_data_path: Path, v2_db_url: str):
        """Initialize validator."""
        self.v1_data_path = v1_data_path
        self.v2_db_url = v2_db_url
        self.logger = logging.getLogger(__name__)
        self.validation_results = {
            'passed': [],
            'failed': [],
            'warnings': []
        }
    
    async def validate_all(self) -> Dict[str, Any]:
        """Run all validations."""
        self.logger.info("Starting migration validation")
        
        # Connect to v2 database
        self.conn = await asyncpg.connect(self.v2_db_url)
        
        try:
            await self.validate_row_counts()
            await self.validate_data_integrity()
            await self.validate_analysis_results()
            
            return self.validation_results
            
        finally:
            await self.conn.close()
    
    async def validate_row_counts(self) -> None:
        """Validate row counts match."""
        self.logger.info("Validating row counts...")
        
        # Check markets
        v1_markets = len(pd.read_csv(self.v1_data_path / 'data' / 'processed' / 'markets.csv'))
        v2_markets = await self.conn.fetchval("SELECT COUNT(*) FROM markets")
        
        if v1_markets == v2_markets:
            self.validation_results['passed'].append(f"Markets count: {v1_markets}")
        else:
            self.validation_results['failed'].append(
                f"Markets count mismatch: v1={v1_markets}, v2={v2_markets}"
            )
        
        # Check prices
        v1_prices = len(pd.read_csv(self.v1_data_path / 'data' / 'processed' / 'prices_panel.csv'))
        v2_prices = await self.conn.fetchval("SELECT COUNT(*) FROM price_observations")
        
        if abs(v1_prices - v2_prices) / v1_prices < 0.001:  # Allow 0.1% difference
            self.validation_results['passed'].append(f"Prices count: ~{v1_prices}")
        else:
            self.validation_results['failed'].append(
                f"Prices count mismatch: v1={v1_prices}, v2={v2_prices}"
            )
    
    async def validate_data_integrity(self) -> None:
        """Validate data integrity."""
        self.logger.info("Validating data integrity...")
        
        # Sample price comparisons
        sample_size = 1000
        v1_prices = pd.read_csv(
            self.v1_data_path / 'data' / 'processed' / 'prices_panel.csv',
            nrows=sample_size
        )
        
        for _, row in v1_prices.iterrows():
            v2_price = await self.conn.fetchval("""
                SELECT price FROM price_observations
                WHERE market_id = $1 
                AND commodity_code = $2
                AND date = $3
            """, row['market_code'], row['commodity_code'], pd.to_datetime(row['date']))
            
            if v2_price is None:
                self.validation_results['warnings'].append(
                    f"Missing price: {row['market_code']}/{row['commodity_code']}/{row['date']}"
                )
            elif abs(float(v2_price) - float(row['price'])) > 0.01:
                self.validation_results['failed'].append(
                    f"Price mismatch: {row['market_code']}/{row['commodity_code']} - v1={row['price']}, v2={v2_price}"
                )
    
    async def validate_analysis_results(self) -> None:
        """Validate analysis results accuracy."""
        self.logger.info("Validating analysis results...")
        
        # Run test analysis on both versions
        test_params = {
            'start_date': '2023-01-01',
            'end_date': '2023-12-31',
            'markets': ['SANAA_CITY', 'ADEN_CITY'],
            'commodities': ['WHEAT', 'RICE']
        }
        
        # Compare key metrics
        # (Implementation depends on having both v1 and v2 running)
        self.validation_results['warnings'].append(
            "Analysis validation requires both v1 and v2 systems running"
        )


def main():
    """Main migration entry point."""
    parser = argparse.ArgumentParser(description='Migrate Yemen Market Integration from v1 to v2')
    parser.add_argument('--v1-path', type=Path, required=True, help='Path to v1 data')
    parser.add_argument('--v2-db-url', required=True, help='v2 database URL')
    parser.add_argument('--validate-only', action='store_true', help='Only run validation')
    parser.add_argument('--log-level', default='INFO', help='Logging level')
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run migration or validation
    if args.validate_only:
        validator = MigrationValidator(args.v1_path, args.v2_db_url)
        results = asyncio.run(validator.validate_all())
        
        print("\nValidation Results:")
        print(f"Passed: {len(results['passed'])}")
        print(f"Failed: {len(results['failed'])}")
        print(f"Warnings: {len(results['warnings'])}")
        
        if results['failed']:
            print("\nFailures:")
            for failure in results['failed']:
                print(f"  - {failure}")
            exit(1)
    else:
        migrator = V1ToV2Migrator(args.v1_path, args.v2_db_url)
        stats = asyncio.run(migrator.migrate_all())
        
        print("\nMigration Statistics:")
        print(f"Markets migrated: {stats['markets_migrated']}")
        print(f"Prices migrated: {stats['prices_migrated']}")
        print(f"Analyses migrated: {stats['analyses_migrated']}")
        print(f"Errors: {len(stats['errors'])}")
        
        if stats['errors']:
            print("\nErrors encountered:")
            for error in stats['errors'][:10]:  # Show first 10 errors
                print(f"  - {error}")


if __name__ == '__main__':
    main()