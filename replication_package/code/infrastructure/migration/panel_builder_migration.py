"""Migration tool for transitioning from V1 to V2 Panel Builder."""

import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional
import json

import pandas as pd
import click

from v2.src.application.services.panel_builder_service import (
    PanelBuilderService, PanelConfiguration
)
from v2.src.infrastructure.persistence.unit_of_work import SQLAlchemyUnitOfWork
from v2.src.core.domain.geography.services import SpatialService


logger = logging.getLogger(__name__)


class PanelBuilderMigrator:
    """Handles migration from V1 to V2 panel builder."""
    
    def __init__(
        self,
        v1_data_dir: Path,
        v2_service: PanelBuilderService
    ):
        """Initialize migrator.
        
        Args:
            v1_data_dir: Directory containing V1 data
            v2_service: V2 panel builder service
        """
        self.v1_data_dir = Path(v1_data_dir)
        self.v2_service = v2_service
        self.migration_report = {
            "timestamp": datetime.now().isoformat(),
            "v1_panels": {},
            "v2_panels": {},
            "comparisons": {},
            "issues": []
        }
    
    async def migrate_panel(
        self,
        v1_panel_name: str,
        config: PanelConfiguration,
        output_dir: Path
    ) -> Dict[str, Any]:
        """Migrate a specific V1 panel to V2.
        
        Args:
            v1_panel_name: Name of V1 panel file
            config: Configuration for V2 panel
            output_dir: Output directory for V2 panel
            
        Returns:
            Migration results
        """
        logger.info(f"Migrating panel: {v1_panel_name}")
        
        # Load V1 panel
        v1_path = self.v1_data_dir / v1_panel_name
        if not v1_path.exists():
            raise FileNotFoundError(f"V1 panel not found: {v1_path}")
        
        v1_panel = pd.read_parquet(v1_path)
        self.migration_report["v1_panels"][v1_panel_name] = {
            "path": str(v1_path),
            "shape": v1_panel.shape,
            "columns": list(v1_panel.columns),
            "date_range": [
                str(v1_panel['date'].min()) if 'date' in v1_panel.columns else None,
                str(v1_panel['date'].max()) if 'date' in v1_panel.columns else None
            ]
        }
        
        # Extract configuration from V1 panel
        commodities = None
        markets = None
        
        if 'commodity' in v1_panel.columns:
            commodities = v1_panel['commodity'].unique().tolist()
        
        if 'market' in v1_panel.columns:
            markets = v1_panel['market'].unique().tolist()
        elif 'market_id' in v1_panel.columns:
            markets = v1_panel['market_id'].unique().tolist()
        
        # Create V2 panel
        v2_panel = await self.v2_service.create_balanced_panel(
            config=config,
            commodities=commodities,
            markets=markets
        )
        
        # Save V2 panel
        output_path = output_dir / f"v2_{v1_panel_name}"
        v2_panel.to_parquet(output_path)
        
        self.migration_report["v2_panels"][v1_panel_name] = {
            "path": str(output_path),
            "shape": v2_panel.shape,
            "columns": list(v2_panel.columns),
            "date_range": [
                str(v2_panel['date'].min()) if 'date' in v2_panel.columns else None,
                str(v2_panel['date'].max()) if 'date' in v2_panel.columns else None
            ]
        }
        
        # Compare panels
        comparison = await self._compare_panels(v1_panel, v2_panel)
        self.migration_report["comparisons"][v1_panel_name] = comparison
        
        return {
            "v1_shape": v1_panel.shape,
            "v2_shape": v2_panel.shape,
            "coverage_improvement": comparison.get("coverage_delta", 0),
            "new_features": comparison.get("new_features", [])
        }
    
    async def _compare_panels(
        self,
        v1_panel: pd.DataFrame,
        v2_panel: pd.DataFrame
    ) -> Dict[str, Any]:
        """Compare V1 and V2 panels.
        
        Args:
            v1_panel: V1 panel DataFrame
            v2_panel: V2 panel DataFrame
            
        Returns:
            Comparison results
        """
        comparison = {}
        
        # Column mapping
        v1_to_v2_mapping = {
            "market": "market_id",
            "admin1": "governorate",
            "admin2": "district",
            "usdprice": "price_usd",
            "price": "price_yer"
        }
        
        # Compare dimensions
        comparison["dimension_comparison"] = {
            "v1_rows": len(v1_panel),
            "v2_rows": len(v2_panel),
            "v1_columns": len(v1_panel.columns),
            "v2_columns": len(v2_panel.columns)
        }
        
        # Compare coverage
        v1_price_col = 'price' if 'price' in v1_panel.columns else 'usdprice'
        v2_price_col = 'price' if 'price' in v2_panel.columns else 'price_usd'
        
        if v1_price_col in v1_panel.columns and v2_price_col in v2_panel.columns:
            v1_coverage = v1_panel[v1_price_col].notna().sum() / len(v1_panel) * 100
            v2_coverage = v2_panel[v2_price_col].notna().sum() / len(v2_panel) * 100
            
            comparison["coverage"] = {
                "v1": round(v1_coverage, 2),
                "v2": round(v2_coverage, 2),
                "delta": round(v2_coverage - v1_coverage, 2)
            }
            comparison["coverage_delta"] = v2_coverage - v1_coverage
        
        # Compare features
        v1_cols = set(v1_panel.columns)
        v2_cols = set(v2_panel.columns)
        
        # Map V1 columns to V2 equivalents
        v1_mapped = set()
        for v1_col, v2_col in v1_to_v2_mapping.items():
            if v1_col in v1_cols:
                v1_mapped.add(v2_col)
                v1_cols.remove(v1_col)
        v1_cols.update(v1_mapped)
        
        comparison["features"] = {
            "common": list(v1_cols & v2_cols),
            "v1_only": list(v1_cols - v2_cols),
            "v2_only": list(v2_cols - v1_cols)
        }
        comparison["new_features"] = list(v2_cols - v1_cols)
        
        # Check data quality
        quality_checks = []
        
        # Check for duplicates
        v1_duplicates = v1_panel.duplicated().sum()
        v2_duplicates = v2_panel.duplicated().sum()
        
        if v1_duplicates > 0 or v2_duplicates > 0:
            quality_checks.append({
                "issue": "duplicates",
                "v1_count": v1_duplicates,
                "v2_count": v2_duplicates
            })
        
        comparison["quality_checks"] = quality_checks
        
        return comparison
    
    async def migrate_all_panels(
        self,
        output_dir: Path,
        panel_patterns: Optional[List[str]] = None
    ) -> None:
        """Migrate all V1 panels matching patterns.
        
        Args:
            output_dir: Output directory for V2 panels
            panel_patterns: List of file patterns to match
        """
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Default patterns
        if panel_patterns is None:
            panel_patterns = [
                "balanced_panel_*.parquet",
                "*_panel.parquet",
                "integrated_panel.parquet"
            ]
        
        # Find all matching panels
        v1_panels = []
        for pattern in panel_patterns:
            v1_panels.extend(list(self.v1_data_dir.glob(pattern)))
        
        logger.info(f"Found {len(v1_panels)} V1 panels to migrate")
        
        # Migrate each panel
        for v1_path in v1_panels:
            try:
                # Determine configuration from filename
                config = self._infer_config_from_filename(v1_path.name)
                
                # Migrate
                result = await self.migrate_panel(
                    v1_path.name,
                    config,
                    output_dir
                )
                
                logger.info(f"Successfully migrated {v1_path.name}")
                
            except Exception as e:
                logger.error(f"Failed to migrate {v1_path.name}: {e}")
                self.migration_report["issues"].append({
                    "panel": v1_path.name,
                    "error": str(e)
                })
        
        # Save migration report
        report_path = output_dir / "migration_report.json"
        with open(report_path, 'w') as f:
            json.dump(self.migration_report, f, indent=2)
        
        logger.info(f"Migration complete. Report saved to {report_path}")
    
    def _infer_config_from_filename(self, filename: str) -> PanelConfiguration:
        """Infer panel configuration from filename.
        
        Args:
            filename: V1 panel filename
            
        Returns:
            Panel configuration
        """
        # Default configuration
        config = PanelConfiguration(
            start_date=datetime(2019, 1, 1),
            end_date=datetime(2024, 3, 31),
            frequency="M"
        )
        
        # Adjust based on filename patterns
        if "balanced" in filename:
            config.min_coverage_pct = 85.0
        
        if "integrated" in filename:
            config.include_conflict_data = True
            config.include_exchange_rates = True
        
        if "weekly" in filename:
            config.frequency = "W"
        
        if "threshold" in filename or "spatial" in filename:
            config.include_spatial_features = True
        
        return config
    
    def generate_compatibility_report(
        self,
        v1_panel_path: Path,
        v2_panel_path: Path,
        output_path: Path
    ) -> None:
        """Generate detailed compatibility report.
        
        Args:
            v1_panel_path: Path to V1 panel
            v2_panel_path: Path to V2 panel
            output_path: Path for report output
        """
        v1_panel = pd.read_parquet(v1_panel_path)
        v2_panel = pd.read_parquet(v2_panel_path)
        
        report = {
            "v1_panel": str(v1_panel_path),
            "v2_panel": str(v2_panel_path),
            "timestamp": datetime.now().isoformat(),
            "compatibility_checks": []
        }
        
        # Check 1: Entity consistency
        v1_markets = set(v1_panel.get('market', v1_panel.get('market_id', [])))
        v2_markets = set(v2_panel.get('market_id', []))
        
        market_overlap = len(v1_markets & v2_markets) / len(v1_markets) * 100 if v1_markets else 0
        
        report["compatibility_checks"].append({
            "check": "market_consistency",
            "v1_count": len(v1_markets),
            "v2_count": len(v2_markets),
            "overlap_percentage": round(market_overlap, 2),
            "status": "PASS" if market_overlap > 95 else "WARNING"
        })
        
        # Check 2: Time period consistency
        v1_dates = pd.to_datetime(v1_panel['date']) if 'date' in v1_panel.columns else None
        v2_dates = pd.to_datetime(v2_panel['date']) if 'date' in v2_panel.columns else None
        
        if v1_dates is not None and v2_dates is not None:
            date_check = {
                "check": "date_range_consistency",
                "v1_range": [str(v1_dates.min()), str(v1_dates.max())],
                "v2_range": [str(v2_dates.min()), str(v2_dates.max())],
                "status": "PASS" if v1_dates.min() == v2_dates.min() and v1_dates.max() == v2_dates.max() else "WARNING"
            }
            report["compatibility_checks"].append(date_check)
        
        # Check 3: Price data consistency
        # Sample some common entities and compare values
        sample_size = min(100, len(v1_panel))
        sample_indices = v1_panel.sample(sample_size).index
        
        price_diffs = []
        for idx in sample_indices:
            v1_row = v1_panel.loc[idx]
            
            # Find matching V2 row
            v2_match = v2_panel[
                (v2_panel['market_id'] == v1_row.get('market', v1_row.get('market_id'))) &
                (v2_panel['commodity'] == v1_row.get('commodity')) &
                (v2_panel['date'] == v1_row.get('date'))
            ]
            
            if not v2_match.empty:
                v1_price = v1_row.get('price', v1_row.get('usdprice'))
                v2_price = v2_match.iloc[0].get('price', v2_match.iloc[0].get('price_usd'))
                
                if pd.notna(v1_price) and pd.notna(v2_price):
                    price_diffs.append(abs(v1_price - v2_price) / v1_price * 100)
        
        if price_diffs:
            avg_diff = sum(price_diffs) / len(price_diffs)
            report["compatibility_checks"].append({
                "check": "price_consistency",
                "samples_compared": len(price_diffs),
                "avg_percentage_difference": round(avg_diff, 2),
                "status": "PASS" if avg_diff < 1.0 else "WARNING"
            })
        
        # Save report
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2)


@click.command()
@click.option('--v1-data-dir', required=True, help='V1 data directory')
@click.option('--output-dir', required=True, help='Output directory for V2 panels')
@click.option('--pattern', multiple=True, help='Panel file patterns to migrate')
@click.option('--compare-only', is_flag=True, help='Only compare, do not migrate')
async def main(v1_data_dir: str, output_dir: str, pattern: List[str], compare_only: bool):
    """Migrate V1 panels to V2 format."""
    logging.basicConfig(level=logging.INFO)
    
    # Create V2 service (would need proper initialization in production)
    # This is a placeholder - actual implementation would need proper setup
    v2_service = None  # Initialize with proper dependencies
    
    # Create migrator
    migrator = PanelBuilderMigrator(
        v1_data_dir=Path(v1_data_dir),
        v2_service=v2_service
    )
    
    if compare_only:
        # Just generate comparison reports
        logger.info("Running in compare-only mode")
        # Implementation for comparison
    else:
        # Run migration
        await migrator.migrate_all_panels(
            output_dir=Path(output_dir),
            panel_patterns=list(pattern) if pattern else None
        )


if __name__ == "__main__":
    asyncio.run(main())