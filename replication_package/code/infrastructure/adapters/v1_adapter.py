"""Adapter for v1 compatibility."""

import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import pandas as pd

from ...application.interfaces import LegacyAnalysisAdapter
from ...core.domain.market.entities import Market, PriceObservation
from ...core.domain.market.value_objects import MarketId, Commodity, Price, Coordinates, MarketType


class V1Adapter(LegacyAnalysisAdapter):
    """Adapter to use v1 analysis code in v2 architecture."""
    
    def __init__(self, v1_path: Optional[Path] = None):
        """Initialize v1 adapter."""
        self.v1_path = v1_path or Path(__file__).parent.parent.parent.parent.parent
        self._ensure_v1_available()
    
    def _ensure_v1_available(self) -> None:
        """Ensure v1 code is available for import."""
        # Add the archive/v1_reference directory to Python path so that
        # yemen_market imports work with the relocated V1 code
        v1_archive = self.v1_path / "archive" / "v1_reference"
        if v1_archive.exists() and str(v1_archive) not in sys.path:
            sys.path.insert(0, str(v1_archive))
    
    async def run_analysis(
        self,
        market_ids: List[str],
        commodity_ids: List[str],
        start_date: str,
        end_date: str
    ) -> Any:
        """Run v1 analysis and return results."""
        try:
            # Import v1 components (yemen_market path should work due to _ensure_v1_available)
            from yemen_market.models.three_tier import ThreeTierRunner
            from yemen_market.data import PanelBuilder
            
            # Create panel data using v1
            panel_builder = PanelBuilder()
            panel_data = panel_builder.build_balanced_panel(
                start_date=start_date,
                end_date=end_date,
                markets=market_ids,
                commodities=commodity_ids
            )
            
            # Run three-tier analysis
            runner = ThreeTierRunner()
            results = runner.run_all_tiers(panel_data)
            
            # Convert to v2 format
            return self._convert_results(results)
            
        except ImportError as e:
            raise RuntimeError(f"Failed to import v1 components: {e}")
    
    def import_v1_data(self) -> Dict[str, pd.DataFrame]:
        """Import data from v1 format."""
        try:
            from yemen_market.data import PanelBuilder
            
            builder = PanelBuilder()
            
            # Load various datasets
            datasets = {}
            
            # Market data
            if hasattr(builder, 'markets_df'):
                datasets['markets'] = builder.markets_df
            
            # Price data
            if hasattr(builder, 'prices_df'):
                datasets['prices'] = builder.prices_df
            
            # Conflict data
            if hasattr(builder, 'conflict_df'):
                datasets['conflicts'] = builder.conflict_df
            
            return datasets
            
        except ImportError:
            raise RuntimeError("Failed to import v1 data components")
    
    def convert_markets(self, v1_markets: pd.DataFrame) -> List[Market]:
        """Convert v1 market data to v2 domain objects."""
        markets = []
        
        for _, row in v1_markets.iterrows():
            try:
                market = Market(
                    market_id=MarketId(row.get('market_id', f"{row['admin1']}_{row['market']}")),
                    name=row.get('market', 'Unknown'),
                    coordinates=Coordinates(
                        latitude=float(row.get('latitude', 0)),
                        longitude=float(row.get('longitude', 0))
                    ),
                    market_type=self._map_market_type(row.get('market_type', 'retail')),
                    governorate=row.get('admin1', 'Unknown'),
                    district=row.get('admin2', 'Unknown'),
                    active_since=pd.to_datetime(row.get('active_since', '2019-01-01'))
                )
                markets.append(market)
            except Exception as e:
                print(f"Failed to convert market {row}: {e}")
                continue
        
        return markets
    
    def convert_prices(self, v1_prices: pd.DataFrame) -> List[PriceObservation]:
        """Convert v1 price data to v2 domain objects."""
        observations = []
        
        for _, row in v1_prices.iterrows():
            try:
                # Create commodity
                commodity = Commodity(
                    code=self._standardize_commodity_code(row['commodity']),
                    name=row['commodity'],
                    category=self._get_commodity_category(row['commodity']),
                    standard_unit=row.get('unit', 'kg')
                )
                
                # Create price
                price = Price(
                    amount=float(row['price']),
                    currency=row.get('currency', 'YER'),
                    unit=row.get('unit', 'kg')
                )
                
                # Create market ID
                market_id = MarketId(
                    row.get('market_id', f"{row['admin1']}_{row['market']}")
                )
                
                # Create observation
                obs = PriceObservation(
                    market_id=market_id,
                    commodity=commodity,
                    price=price,
                    observed_date=pd.to_datetime(row['date']),
                    source=row.get('source', 'WFP'),
                    quality=row.get('quality', 'standard'),
                    observations_count=row.get('observations', 1)
                )
                
                observations.append(obs)
                
            except Exception as e:
                print(f"Failed to convert price {row}: {e}")
                continue
        
        return observations
    
    def migrate_configuration(self, v1_config: Dict[str, Any]) -> Dict[str, Any]:
        """Migrate v1 configuration to v2 format."""
        v2_config = {
            "analysis": {
                "three_tier": {
                    "tier1": {
                        "entity_effects": v1_config.get("tier1", {}).get("fe", True),
                        "time_effects": v1_config.get("tier1", {}).get("te", True),
                        "cluster_entity": v1_config.get("tier1", {}).get("cluster", True)
                    },
                    "tier2": {
                        "min_markets": v1_config.get("tier2", {}).get("min_markets", 3),
                        "min_periods": v1_config.get("tier2", {}).get("min_obs", 50),
                        "threshold_test": v1_config.get("tier2", {}).get("threshold", True)
                    },
                    "tier3": {
                        "n_factors": v1_config.get("tier3", {}).get("factors", 2),
                        "conflict_validation": v1_config.get("tier3", {}).get("conflict", True)
                    }
                },
                "diagnostics": {
                    "run_all": v1_config.get("diagnostics", {}).get("all", True),
                    "apply_corrections": v1_config.get("diagnostics", {}).get("correct", True)
                }
            },
            "data": {
                "balanced_panel": v1_config.get("data", {}).get("balanced", True),
                "interpolation": v1_config.get("data", {}).get("interpolate", "linear"),
                "outlier_treatment": v1_config.get("data", {}).get("outliers", "winsorize")
            }
        }
        
        return v2_config
    
    def _convert_results(self, v1_results: Dict[str, Any]) -> Dict[str, Any]:
        """Convert v1 results to v2 format."""
        v2_results = {
            "analysis_type": "three_tier_legacy",
            "version": "1.0_adapted",
            "timestamp": datetime.utcnow().isoformat(),
            "tiers": {}
        }
        
        # Convert Tier 1 results
        if "tier1" in v1_results:
            v2_results["tiers"]["tier1"] = {
                "model_type": "pooled_panel",
                "coefficients": v1_results["tier1"].get("params", {}),
                "standard_errors": v1_results["tier1"].get("bse", {}),
                "diagnostics": v1_results["tier1"].get("diagnostics", {}),
                "n_observations": v1_results["tier1"].get("nobs", 0),
                "r_squared": v1_results["tier1"].get("rsquared", 0)
            }
        
        # Convert Tier 2 results
        if "tier2" in v1_results:
            v2_results["tiers"]["tier2"] = {}
            for commodity, results in v1_results["tier2"].items():
                v2_results["tiers"]["tier2"][commodity] = {
                    "model_type": results.get("model", "threshold_vecm"),
                    "threshold": results.get("threshold", None),
                    "regimes": results.get("regimes", {}),
                    "diagnostics": results.get("diagnostics", {})
                }
        
        # Convert Tier 3 results
        if "tier3" in v1_results:
            v2_results["tiers"]["tier3"] = {
                "validation_results": v1_results["tier3"].get("validation", {}),
                "factor_analysis": v1_results["tier3"].get("factors", {}),
                "conflict_impact": v1_results["tier3"].get("conflict", {})
            }
        
        return v2_results
    
    def _map_market_type(self, v1_type: str) -> MarketType:
        """Map v1 market type to v2 enum."""
        mapping = {
            "wholesale": MarketType.WHOLESALE,
            "retail": MarketType.RETAIL,
            "border": MarketType.BORDER,
            "port": MarketType.PORT,
            "rural": MarketType.RURAL,
            "urban": MarketType.URBAN
        }
        return mapping.get(v1_type.lower(), MarketType.RETAIL)
    
    def _standardize_commodity_code(self, name: str) -> str:
        """Standardize commodity code."""
        return name.upper().replace(" ", "_").replace("(", "").replace(")", "")
    
    def _get_commodity_category(self, name: str) -> str:
        """Determine commodity category."""
        name_lower = name.lower()
        
        if any(fuel in name_lower for fuel in ["fuel", "diesel", "petrol", "gas"]):
            return "fuel"
        elif any(protein in name_lower for protein in ["meat", "chicken", "eggs", "fish"]):
            return "protein"
        elif any(grain in name_lower for grain in ["wheat", "rice", "sorghum", "millet"]):
            return "cereal"
        elif any(legume in name_lower for legume in ["beans", "lentils", "peas"]):
            return "legume"
        else:
            return "food"