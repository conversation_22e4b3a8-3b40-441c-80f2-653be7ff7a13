# V1 Model Adapters

This directory contains adapters that integrate V1 econometric models into the V2 architecture, ensuring backward compatibility and maintaining the validated 35% conflict effect findings.

## Overview

The adapter pattern allows V2 to leverage the proven V1 econometric models while providing:
- Async/await support
- Event-driven progress tracking
- Standardized error handling
- Diagnostic integration
- Result validation

## Key Components

### Base Adapter
- `v1_model_adapter.py`: Abstract base class defining the adapter interface
  - Data format conversion (V2 → V1)
  - Model execution
  - Result extraction and validation
  - Diagnostic integration
  - Correction application

### Tier Adapters
1. **Tier1Adapter** (`tier1_adapter.py`)
   - Pooled panel models with fixed effects
   - Validates 35% conflict effect
   - Applies econometric corrections

2. **Tier2Adapter** (`tier2_adapter.py`)
   - Commodity-specific threshold VECM models
   - Cointegration testing
   - Cross-commodity analysis

3. **Tier3Adapter** (`tier3_adapter.py`)
   - Factor analysis models
   - PCA-based integration analysis
   - Conflict validation

## Critical Validation

The adapters ensure the V2 analysis reproduces the key V1 finding:
- **Conflict Effect**: 35% ± 5% price increase (p < 0.001)
- **Model Fit**: R² > 0.6
- **Statistical Significance**: p < 0.001

## Usage Example

```python
from v2.src.infrastructure.adapters import Tier1Adapter
from v2.src.core.domain.market.entities import PanelData

# Create adapter
adapter = Tier1Adapter({
    'entity_effects': True,
    'time_effects': True,
    'validate_conflict_effect': True
})

# Convert V2 data to V1 format
v1_data = await adapter.prepare_data(panel_data)

# Run V1 model
results = await adapter.run_model(v1_data)

# Extract and validate results
validated_results = await adapter.extract_results(results)

# Check conflict effect validation
if validated_results['conflict_effect']['is_valid']:
    print(f"Conflict effect: {validated_results['conflict_effect']['percentage_effect']:.1f}%")
```

## Diagnostic Corrections

The adapters automatically apply corrections based on diagnostic test failures:

| Issue | Detection Test | Correction Applied |
|-------|---------------|-------------------|
| Serial correlation | Wooldridge test | Newey-West HAC standard errors |
| Cross-sectional dependence | Pesaran CD test | Driscoll-Kraay standard errors |
| Both serial & cross-sectional | Both tests fail | Driscoll-Kraay (handles both) |
| Heteroskedasticity | Modified Wald test | Cluster-robust standard errors |
| Unit roots | Im-Pesaran-Shin test | First-differencing transformation |

## Architecture Integration

The adapters integrate with V2's architecture:

```
V2 Domain Models → Adapter → V1 Models → Results → V2 Format
                     ↓           ↓          ↓
                 Conversion  Execution  Validation
```

## Testing

Comprehensive tests ensure V1/V2 parity:
- `test_three_tier_v1_parity.py`: Integration tests
- `test_conflict_effect_validation.py`: Conflict effect validation
- Unit tests for each adapter

## Performance Considerations

- Data conversion overhead is minimal (~100ms for 10k observations)
- V1 model execution time dominates (1-5 minutes for full analysis)
- Results are cached to avoid redundant computations
- Parallel commodity analysis available for Tier 2

## Future Enhancements

1. **Native V2 Models**: Gradually replace V1 models with native implementations
2. **GPU Acceleration**: Use MLX/CUDA for large-scale analysis
3. **Streaming Support**: Process data in chunks for memory efficiency
4. **Real-time Updates**: Support incremental model updates