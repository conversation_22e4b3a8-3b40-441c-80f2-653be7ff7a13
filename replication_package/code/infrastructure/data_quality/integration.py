"""
Integration module for Data Quality Framework.

Provides simplified interfaces and integration points for the comprehensive
data quality framework components.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Tuple, Union, Any
from datetime import datetime

from .data_quality_orchestrator import DataQualityOrchestrator, ProcessingPriority
from .dynamic_exchange_rate_validator import DynamicExchangeRateValidator
from .zone_specific_quality_control import ZoneSpecificQualityControl
from .currency_conversion_timing import CurrencyConversionTiming
from .conflict_aware_imputation import ConflictAwareImputation

from ...core.domain.market.currency_zones import CurrencyZone
from ...core.domain.market.value_objects import Currency
from ...core.domain.market.entities import Market, PriceObservation
from ...core.utils.logging import get_logger

logger = get_logger(__name__)


class DataQualityService:
    """
    Simplified service interface for data quality operations.
    
    This service provides a high-level interface to the comprehensive
    data quality framework, making it easy to integrate into existing
    Yemen market analysis workflows.
    """
    
    def __init__(self):
        """Initialize the data quality service."""
        self.orchestrator = DataQualityOrchestrator()
        self._initialized = True
        
        logger.info("Data Quality Service initialized")
    
    async def process_market_data(
        self,
        observations: List[PriceObservation],
        markets: List[Market],
        target_currency: Currency = Currency.USD,
        enable_imputation: bool = True,
        priority: str = "normal"
    ) -> Tuple[List[PriceObservation], Dict[str, Any]]:
        """
        Process market data through the complete quality pipeline.
        
        Args:
            observations: Raw price observations
            markets: Market definitions
            target_currency: Target currency for output
            enable_imputation: Whether to impute missing data
            priority: Processing priority ('critical', 'high', 'normal', 'low')
            
        Returns:
            Tuple of (processed_observations, quality_report)
        """
        
        # Convert priority string to enum
        priority_map = {
            'critical': ProcessingPriority.CRITICAL,
            'high': ProcessingPriority.HIGH,
            'normal': ProcessingPriority.NORMAL,
            'low': ProcessingPriority.LOW
        }
        priority_enum = priority_map.get(priority.lower(), ProcessingPriority.NORMAL)
        
        # Submit processing request
        request_id = await self.orchestrator.process_data_quality(
            observations=observations,
            markets=markets,
            target_currency=target_currency,
            priority=priority_enum,
            enable_imputation=enable_imputation
        )
        
        # Wait for completion
        while True:
            status = await self.orchestrator.get_processing_status(request_id)
            
            if status['status'] == 'completed':
                break
            elif status['status'] == 'not_found':
                raise ValueError(f"Processing request {request_id} not found")
            
            await asyncio.sleep(1)
        
        # Get results
        result = await self.orchestrator.get_processing_result(request_id)
        
        if not result or not result.success:
            error_msg = f"Data quality processing failed: {result.errors if result else 'Unknown error'}"
            raise RuntimeError(error_msg)
        
        # Prepare quality report
        quality_report = {
            'quality_score': result.quality_score,
            'data_coverage': result.data_coverage,
            'conversion_success_rate': result.conversion_success_rate,
            'imputation_success_rate': result.imputation_success_rate,
            'overall_confidence': result.overall_confidence,
            'processing_time_seconds': result.processing_time_seconds,
            'warnings': result.warnings,
            'recommendations': result.recommendations,
            'next_steps': result.next_steps,
            'stages_completed': [stage.value for stage in result.stages_completed]
        }
        
        return result.processed_observations, quality_report
    
    async def validate_exchange_rates(
        self,
        zone_rates: List[Tuple[CurrencyZone, float, datetime, str]]
    ) -> Dict[str, Any]:
        """
        Validate exchange rates using dynamic validation.
        
        Args:
            zone_rates: List of (zone, rate, date, source) tuples
            
        Returns:
            Validation report
        """
        validator = DynamicExchangeRateValidator()
        
        # Convert to appropriate format
        rate_observations = [(zone, rate, date, source) for zone, rate, date, source in zone_rates]
        
        # Validate
        validations = await validator.validate_batch_rates(rate_observations)
        
        # Generate summary
        summary = validator.get_validation_summary(validations)
        
        return {
            'validation_summary': summary,
            'individual_validations': [
                {
                    'zone': v.zone.value,
                    'rate': float(v.rate),
                    'date': v.date.isoformat(),
                    'validity': v.validity.value,
                    'confidence': v.confidence,
                    'flags': v.validation_flags,
                    'suggested_rate': float(v.suggested_rate) if v.suggested_rate else None
                }
                for v in validations
            ]
        }
    
    async def impute_missing_data(
        self,
        market_data: Any,  # pandas DataFrame or similar
        commodity: str,
        zone: CurrencyZone,
        method: str = "conflict_intensity_model"
    ) -> Dict[str, Any]:
        """
        Impute missing data using conflict-aware methods.
        
        Args:
            market_data: Panel data with missing values
            commodity: Commodity name
            zone: Currency zone
            method: Imputation method to use
            
        Returns:
            Imputation results
        """
        from .conflict_aware_imputation import ImputationMethod, ConflictContext
        
        imputer = ConflictAwareImputation()
        
        # Convert method string to enum
        method_map = {
            'conflict_weighted_mean': ImputationMethod.CONFLICT_WEIGHTED_MEAN,
            'spatial_interpolation': ImputationMethod.SPATIAL_INTERPOLATION,
            'temporal_regression': ImputationMethod.TEMPORAL_REGRESSION,
            'zone_specific_knn': ImputationMethod.ZONE_SPECIFIC_KNN,
            'conflict_intensity_model': ImputationMethod.CONFLICT_INTENSITY_MODEL,
            'cross_commodity_imputation': ImputationMethod.CROSS_COMMODITY_IMPUTATION,
            'survivor_bias_correction': ImputationMethod.SURVIVOR_BIAS_CORRECTION
        }
        
        method_enum = method_map.get(method.lower(), ImputationMethod.CONFLICT_INTENSITY_MODEL)
        
        # Create simple conflict contexts (would be more sophisticated in production)
        conflict_contexts = []
        
        # Apply imputation
        result = await imputer.impute_missing_data(
            market_data=market_data,
            conflict_contexts=conflict_contexts,
            commodity=commodity,
            zone=zone,
            method=method_enum
        )
        
        return {
            'success': result.success,
            'imputed_observations_count': len(result.imputed_observations),
            'confidence_scores': {f"{k[0]}_{k[1].isoformat()}": v for k, v in result.confidence_scores.items()},
            'method_used': result.method_used.value,
            'warnings': result.warnings,
            'quality_metrics': result.quality_metrics
        }
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get current processing statistics."""
        return self.orchestrator.get_processing_statistics()
    
    async def clear_caches(self) -> None:
        """Clear all caches."""
        await self.orchestrator.clear_caches()


# Convenience functions for common operations
async def validate_yemen_market_data(
    observations: List[PriceObservation],
    markets: List[Market],
    target_currency: Currency = Currency.USD
) -> Tuple[List[PriceObservation], Dict[str, Any]]:
    """
    Convenience function to validate and process Yemen market data.
    
    This function applies the complete data quality pipeline with
    sensible defaults for Yemen market analysis.
    """
    service = DataQualityService()
    
    return await service.process_market_data(
        observations=observations,
        markets=markets,
        target_currency=target_currency,
        enable_imputation=True,
        priority="normal"
    )


async def quick_exchange_rate_check(
    zone_rates: Dict[CurrencyZone, float],
    date: datetime = None
) -> Dict[str, Any]:
    """
    Quick validation of exchange rates for all zones.
    
    Args:
        zone_rates: Dictionary mapping zones to rates
        date: Date for validation (defaults to now)
        
    Returns:
        Validation summary
    """
    if date is None:
        date = datetime.now()
    
    service = DataQualityService()
    
    # Convert to required format
    rate_tuples = [
        (zone, rate, date, "user_provided")
        for zone, rate in zone_rates.items()
    ]
    
    return await service.validate_exchange_rates(rate_tuples)


def create_data_quality_pipeline() -> DataQualityService:
    """
    Create a new data quality service instance.
    
    Returns:
        Configured data quality service
    """
    return DataQualityService()


# Integration with existing currency-aware processor
class EnhancedCurrencyAwareProcessor:
    """
    Enhanced version of the currency-aware processor that uses
    the new data quality framework.
    """
    
    def __init__(self):
        """Initialize enhanced processor."""
        self.data_quality_service = DataQualityService()
        
    async def process_with_quality_control(
        self,
        observations: List[PriceObservation],
        markets: List[Market],
        target_currency: Currency = Currency.USD,
        enable_advanced_validation: bool = True
    ) -> Tuple[List[PriceObservation], Dict[str, Any]]:
        """
        Process observations with comprehensive quality control.
        
        This method replaces the hard-coded exchange rate multipliers
        with dynamic validation and provides comprehensive quality reporting.
        """
        
        if enable_advanced_validation:
            # Use full data quality pipeline
            processed_observations, quality_report = await self.data_quality_service.process_market_data(
                observations=observations,
                markets=markets,
                target_currency=target_currency,
                enable_imputation=True,
                priority="high"
            )
            
            # Add quality flags to observations
            for obs in processed_observations:
                if hasattr(obs, 'metadata'):
                    obs.metadata = obs.metadata or {}
                    obs.metadata['quality_validated'] = True
                    obs.metadata['quality_score'] = quality_report['quality_score']
            
            return processed_observations, quality_report
        
        else:
            # Use basic validation only
            # This could fall back to the original currency-aware processor
            logger.warning("Advanced validation disabled - using basic processing")
            
            # Basic currency conversion without full quality pipeline
            basic_quality_report = {
                'quality_score': 70,  # Neutral score
                'data_coverage': 100,
                'warnings': ['Advanced validation disabled'],
                'recommendations': ['Enable advanced validation for production use']
            }
            
            return observations, basic_quality_report


# Export main components for easy importing
__all__ = [
    'DataQualityService',
    'EnhancedCurrencyAwareProcessor',
    'validate_yemen_market_data',
    'quick_exchange_rate_check',
    'create_data_quality_pipeline'
]