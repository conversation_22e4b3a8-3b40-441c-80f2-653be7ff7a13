"""
Currency Conversion Timing Protocol.

Ensures proper sequencing of currency conversion operations to prevent
the Yemen Paradox and other analytical errors caused by improper timing
of exchange rate applications.
"""

import logging
import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict

from ...core.domain.market.currency_zones import CurrencyZone
from ...core.domain.market.value_objects import Cur<PERSON>cy, Price, ExchangeRate
from ...core.domain.market.entities import PriceObservation
from ...core.utils.logging import get_logger

logger = get_logger(__name__)


class ConversionStage(Enum):
    """Stages in the currency conversion pipeline."""
    RAW_DATA_VALIDATION = "raw_data_validation"
    ZONE_CLASSIFICATION = "zone_classification"
    EXCHANGE_RATE_RESOLUTION = "exchange_rate_resolution"
    CURRENCY_CONVERSION = "currency_conversion"
    POST_CONVERSION_VALIDATION = "post_conversion_validation"
    ANALYSIS_READY = "analysis_ready"


class ConversionPriority(Enum):
    """Priority levels for conversion operations."""
    CRITICAL = "critical"  # Time-sensitive analysis
    HIGH = "high"         # Regular analysis pipeline
    NORMAL = "normal"     # Batch processing
    LOW = "low"          # Background updates


@dataclass
class ConversionRequest:
    """Request for currency conversion with timing constraints."""
    request_id: str
    observations: List[PriceObservation]
    target_currency: Currency
    priority: ConversionPriority
    requested_at: datetime
    deadline: Optional[datetime] = None
    zone_mappings: Optional[Dict[str, CurrencyZone]] = None
    exchange_rates: Optional[Dict[Tuple[datetime, CurrencyZone], Decimal]] = None
    
    # Tracking
    current_stage: ConversionStage = ConversionStage.RAW_DATA_VALIDATION
    completed_stages: List[ConversionStage] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # Results
    converted_observations: List[PriceObservation] = field(default_factory=list)
    conversion_metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConversionResult:
    """Result of currency conversion operation."""
    request_id: str
    success: bool
    converted_observations: List[PriceObservation]
    processing_time_seconds: float
    stages_completed: List[ConversionStage]
    errors: List[str]
    warnings: List[str]
    quality_metrics: Dict[str, Any]
    metadata: Dict[str, Any]


class CurrencyConversionTiming:
    """
    Manages the timing and sequencing of currency conversion operations.
    
    This system ensures that currency conversion happens in the correct order
    to prevent analytical errors like the Yemen Paradox, where improper
    currency handling leads to incorrect conclusions.
    """
    
    def __init__(
        self,
        max_concurrent_conversions: int = 5,
        default_timeout_seconds: int = 300,
        enable_caching: bool = True
    ):
        """
        Initialize currency conversion timing controller.
        
        Args:
            max_concurrent_conversions: Maximum parallel conversion operations
            default_timeout_seconds: Default timeout for conversion operations
            enable_caching: Whether to cache conversion results
        """
        self.max_concurrent_conversions = max_concurrent_conversions
        self.default_timeout_seconds = default_timeout_seconds
        self.enable_caching = enable_caching
        
        # Conversion queue and processing tracking
        self._conversion_queue: Dict[ConversionPriority, List[ConversionRequest]] = {
            priority: [] for priority in ConversionPriority
        }
        self._active_conversions: Dict[str, ConversionRequest] = {}
        self._completed_conversions: Dict[str, ConversionResult] = {}
        
        # Conversion pipeline components
        self._stage_processors = {
            ConversionStage.RAW_DATA_VALIDATION: self._validate_raw_data,
            ConversionStage.ZONE_CLASSIFICATION: self._classify_zones,
            ConversionStage.EXCHANGE_RATE_RESOLUTION: self._resolve_exchange_rates,
            ConversionStage.CURRENCY_CONVERSION: self._perform_conversion,
            ConversionStage.POST_CONVERSION_VALIDATION: self._validate_conversion,
            ConversionStage.ANALYSIS_READY: self._finalize_conversion
        }
        
        # Caching for performance
        self._conversion_cache: Dict[str, ConversionResult] = {}
        self._rate_cache: Dict[Tuple[datetime, CurrencyZone, Currency], Decimal] = {}
        
        # Processing lock for thread safety
        self._processing_lock = asyncio.Lock()
        
    async def request_conversion(
        self,
        observations: List[PriceObservation],
        target_currency: Currency,
        priority: ConversionPriority = ConversionPriority.NORMAL,
        deadline: Optional[datetime] = None,
        zone_mappings: Optional[Dict[str, CurrencyZone]] = None,
        exchange_rates: Optional[Dict[Tuple[datetime, CurrencyZone], Decimal]] = None
    ) -> str:
        """
        Request currency conversion with proper timing control.
        
        Args:
            observations: Price observations to convert
            target_currency: Target currency for conversion
            priority: Processing priority
            deadline: Optional deadline for completion
            zone_mappings: Pre-computed zone mappings to speed up processing
            exchange_rates: Pre-computed exchange rates
            
        Returns:
            Request ID for tracking
        """
        request_id = f"conv_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self._active_conversions)}"
        
        # Check cache first
        cache_key = self._generate_cache_key(observations, target_currency)
        if self.enable_caching and cache_key in self._conversion_cache:
            cached_result = self._conversion_cache[cache_key]
            logger.info(f"Returning cached conversion result for request {request_id}")
            self._completed_conversions[request_id] = cached_result
            return request_id
        
        # Create conversion request
        request = ConversionRequest(
            request_id=request_id,
            observations=observations,
            target_currency=target_currency,
            priority=priority,
            requested_at=datetime.now(),
            deadline=deadline,
            zone_mappings=zone_mappings,
            exchange_rates=exchange_rates
        )
        
        # Add to appropriate priority queue
        async with self._processing_lock:
            self._conversion_queue[priority].append(request)
            
        logger.info(
            f"Queued conversion request {request_id} with {len(observations)} observations "
            f"(priority: {priority.value})"
        )
        
        # Start processing if capacity available
        asyncio.create_task(self._process_queue())
        
        return request_id
    
    async def get_conversion_status(self, request_id: str) -> Dict[str, Any]:
        """Get the current status of a conversion request."""
        
        # Check completed conversions
        if request_id in self._completed_conversions:
            result = self._completed_conversions[request_id]
            return {
                'status': 'completed',
                'success': result.success,
                'processing_time': result.processing_time_seconds,
                'stages_completed': [stage.value for stage in result.stages_completed],
                'errors': result.errors,
                'warnings': result.warnings,
                'observations_converted': len(result.converted_observations)
            }
        
        # Check active conversions
        if request_id in self._active_conversions:
            request = self._active_conversions[request_id]
            return {
                'status': 'processing',
                'current_stage': request.current_stage.value,
                'stages_completed': [stage.value for stage in request.completed_stages],
                'errors': request.errors,
                'warnings': request.warnings,
                'observations_count': len(request.observations)
            }
        
        # Check queued requests
        for priority, queue in self._conversion_queue.items():
            for request in queue:
                if request.request_id == request_id:
                    position = queue.index(request)
                    return {
                        'status': 'queued',
                        'priority': priority.value,
                        'queue_position': position,
                        'estimated_wait_minutes': position * 2  # Rough estimate
                    }
        
        return {'status': 'not_found'}
    
    async def get_conversion_result(self, request_id: str) -> Optional[ConversionResult]:
        """Get the result of a completed conversion."""
        return self._completed_conversions.get(request_id)
    
    async def _process_queue(self) -> None:
        """Process conversion requests from the queue."""
        async with self._processing_lock:
            # Check if we have capacity
            if len(self._active_conversions) >= self.max_concurrent_conversions:
                return
            
            # Get next request by priority
            next_request = self._get_next_request()
            if not next_request:
                return
            
            # Move to active processing
            self._active_conversions[next_request.request_id] = next_request
        
        # Process the request
        try:
            result = await self._process_conversion_request(next_request)
            
            # Store result and clean up
            async with self._processing_lock:
                self._completed_conversions[next_request.request_id] = result
                self._active_conversions.pop(next_request.request_id, None)
                
                # Cache if successful
                if self.enable_caching and result.success:
                    cache_key = self._generate_cache_key(
                        next_request.observations, 
                        next_request.target_currency
                    )
                    self._conversion_cache[cache_key] = result
            
            logger.info(f"Completed conversion request {next_request.request_id}")
            
        except Exception as e:
            logger.error(f"Error processing conversion {next_request.request_id}: {e}")
            
            # Create error result
            error_result = ConversionResult(
                request_id=next_request.request_id,
                success=False,
                converted_observations=[],
                processing_time_seconds=0,
                stages_completed=next_request.completed_stages,
                errors=[str(e)],
                warnings=next_request.warnings,
                quality_metrics={},
                metadata={}
            )
            
            async with self._processing_lock:
                self._completed_conversions[next_request.request_id] = error_result
                self._active_conversions.pop(next_request.request_id, None)
        
        # Continue processing queue
        await self._process_queue()
    
    def _get_next_request(self) -> Optional[ConversionRequest]:
        """Get the next request to process based on priority and timing."""
        
        # Process by priority order
        for priority in [ConversionPriority.CRITICAL, ConversionPriority.HIGH, 
                        ConversionPriority.NORMAL, ConversionPriority.LOW]:
            queue = self._conversion_queue[priority]
            
            if not queue:
                continue
            
            # Check for deadline-critical requests first
            now = datetime.now()
            for i, request in enumerate(queue):
                if request.deadline and request.deadline <= now + timedelta(minutes=5):
                    return queue.pop(i)
            
            # Otherwise take first in queue
            return queue.pop(0)
        
        return None
    
    async def _process_conversion_request(self, request: ConversionRequest) -> ConversionResult:
        """Process a single conversion request through all stages."""
        start_time = datetime.now()
        
        # Process through each stage
        for stage in ConversionStage:
            try:
                request.current_stage = stage
                
                # Get stage processor
                processor = self._stage_processors.get(stage)
                if not processor:
                    request.errors.append(f"No processor found for stage {stage.value}")
                    break
                
                # Process stage with timeout
                await asyncio.wait_for(
                    processor(request),
                    timeout=self.default_timeout_seconds / len(ConversionStage)
                )
                
                request.completed_stages.append(stage)
                
                # Check for errors that should stop processing
                if any('critical' in error.lower() for error in request.errors):
                    break
                    
            except asyncio.TimeoutError:
                request.errors.append(f"Timeout in stage {stage.value}")
                break
            except Exception as e:
                request.errors.append(f"Error in stage {stage.value}: {str(e)}")
                logger.error(f"Stage {stage.value} failed for request {request.request_id}: {e}")
                break
        
        # Calculate processing time
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # Determine success
        success = (
            ConversionStage.ANALYSIS_READY in request.completed_stages and
            not any('critical' in error.lower() for error in request.errors)
        )
        
        # Calculate quality metrics
        quality_metrics = self._calculate_quality_metrics(request)
        
        return ConversionResult(
            request_id=request.request_id,
            success=success,
            converted_observations=request.converted_observations,
            processing_time_seconds=processing_time,
            stages_completed=request.completed_stages,
            errors=request.errors,
            warnings=request.warnings,
            quality_metrics=quality_metrics,
            metadata=request.conversion_metadata
        )
    
    async def _validate_raw_data(self, request: ConversionRequest) -> None:
        """Stage 1: Validate raw price observation data."""
        logger.debug(f"Validating raw data for request {request.request_id}")
        
        validation_issues = []
        
        for i, obs in enumerate(request.observations):
            # Check for valid prices
            if obs.price.amount <= 0:
                validation_issues.append(f"Observation {i}: Invalid price {obs.price.amount}")
            
            # Check for valid currencies
            if obs.price.currency not in [Currency.YER, Currency.USD]:
                validation_issues.append(f"Observation {i}: Unsupported currency {obs.price.currency}")
            
            # Check for valid dates
            if obs.observed_date > datetime.now():
                validation_issues.append(f"Observation {i}: Future date {obs.observed_date}")
        
        # Store validation results
        request.conversion_metadata['raw_validation'] = {
            'total_observations': len(request.observations),
            'validation_issues': len(validation_issues),
            'issues': validation_issues[:10]  # Limit for performance
        }
        
        if validation_issues:
            request.warnings.extend(validation_issues[:5])  # Add first 5 as warnings
    
    async def _classify_zones(self, request: ConversionRequest) -> None:
        """Stage 2: Classify markets into currency zones."""
        logger.debug(f"Classifying zones for request {request.request_id}")
        
        zone_classifications = {}
        
        # Use provided zone mappings if available
        if request.zone_mappings:
            zone_classifications = request.zone_mappings.copy()
        else:
            # Classify each unique market
            unique_markets = set(str(obs.market_id.value) for obs in request.observations)
            
            for market_id in unique_markets:
                # This would use actual zone classification logic
                # For now, use a simple heuristic
                zone_classifications[market_id] = self._infer_zone_from_market_id(market_id)
        
        request.conversion_metadata['zone_classifications'] = zone_classifications
        
        # Warn about unknown zones
        unknown_zones = [
            market_id for market_id, zone in zone_classifications.items()
            if zone == CurrencyZone.UNKNOWN
        ]
        
        if unknown_zones:
            request.warnings.append(f"Could not classify {len(unknown_zones)} markets into zones")
    
    async def _resolve_exchange_rates(self, request: ConversionRequest) -> None:
        """Stage 3: Resolve exchange rates for conversion."""
        logger.debug(f"Resolving exchange rates for request {request.request_id}")
        
        zone_classifications = request.conversion_metadata.get('zone_classifications', {})
        required_rates = set()
        
        # Determine required exchange rates
        for obs in request.observations:
            market_id = str(obs.market_id.value)
            zone = zone_classifications.get(market_id, CurrencyZone.UNKNOWN)
            
            # Only need rates if converting between currencies
            if obs.price.currency != request.target_currency:
                required_rates.add((obs.observed_date.date(), zone, obs.price.currency))
        
        # Resolve rates
        resolved_rates = {}
        missing_rates = []
        
        for date, zone, from_currency in required_rates:
            # Check provided rates first
            if request.exchange_rates:
                rate_key = (datetime.combine(date, datetime.min.time()), zone)
                if rate_key in request.exchange_rates:
                    resolved_rates[(date, zone, from_currency)] = request.exchange_rates[rate_key]
                    continue
            
            # Check cache
            cache_key = (datetime.combine(date, datetime.min.time()), zone, from_currency)
            if cache_key in self._rate_cache:
                resolved_rates[(date, zone, from_currency)] = self._rate_cache[cache_key]
                continue
            
            # Estimate rate if not available
            estimated_rate = self._estimate_exchange_rate(date, zone, from_currency, request.target_currency)
            if estimated_rate:
                resolved_rates[(date, zone, from_currency)] = estimated_rate
                self._rate_cache[cache_key] = estimated_rate
            else:
                missing_rates.append((date, zone, from_currency))
        
        request.conversion_metadata['resolved_rates'] = resolved_rates
        request.conversion_metadata['missing_rates'] = missing_rates
        
        if missing_rates:
            request.warnings.append(f"Could not resolve {len(missing_rates)} exchange rates")
    
    async def _perform_conversion(self, request: ConversionRequest) -> None:
        """Stage 4: Perform the actual currency conversion."""
        logger.debug(f"Performing conversion for request {request.request_id}")
        
        zone_classifications = request.conversion_metadata.get('zone_classifications', {})
        resolved_rates = request.conversion_metadata.get('resolved_rates', {})
        
        converted_observations = []
        conversion_failures = []
        
        for obs in request.observations:
            try:
                market_id = str(obs.market_id.value)
                zone = zone_classifications.get(market_id, CurrencyZone.UNKNOWN)
                
                # Check if conversion needed
                if obs.price.currency == request.target_currency:
                    # No conversion needed
                    converted_observations.append(obs)
                    continue
                
                # Get exchange rate
                rate_key = (obs.observed_date.date(), zone, obs.price.currency)
                if rate_key not in resolved_rates:
                    conversion_failures.append(f"No rate for {market_id} on {obs.observed_date.date()}")
                    continue
                
                exchange_rate = resolved_rates[rate_key]
                
                # Perform conversion
                if obs.price.currency == Currency.YER and request.target_currency == Currency.USD:
                    # YER to USD
                    converted_amount = obs.price.amount / exchange_rate
                elif obs.price.currency == Currency.USD and request.target_currency == Currency.YER:
                    # USD to YER
                    converted_amount = obs.price.amount * exchange_rate
                else:
                    conversion_failures.append(f"Unsupported conversion {obs.price.currency} to {request.target_currency}")
                    continue
                
                # Create converted observation
                converted_price = Price(
                    amount=converted_amount,
                    currency=request.target_currency
                )
                
                converted_obs = PriceObservation(
                    market_id=obs.market_id,
                    commodity=obs.commodity,
                    price=converted_price,
                    observed_date=obs.observed_date,
                    source=f"{obs.source}_converted",
                    quality=obs.quality,
                    observations_count=obs.observations_count
                )
                
                converted_observations.append(converted_obs)
                
            except Exception as e:
                conversion_failures.append(f"Error converting observation {market_id}: {str(e)}")
        
        request.converted_observations = converted_observations
        request.conversion_metadata['conversion_failures'] = conversion_failures
        
        if conversion_failures:
            request.warnings.extend(conversion_failures[:5])
    
    async def _validate_conversion(self, request: ConversionRequest) -> None:
        """Stage 5: Validate the conversion results."""
        logger.debug(f"Validating conversion for request {request.request_id}")
        
        validation_results = {
            'observations_converted': len(request.converted_observations),
            'observations_failed': len(request.observations) - len(request.converted_observations),
            'conversion_rate': len(request.converted_observations) / len(request.observations) * 100
        }
        
        # Check for reasonable price ranges in target currency
        if request.converted_observations:
            prices = [float(obs.price.amount) for obs in request.converted_observations]
            
            validation_results.update({
                'min_price': min(prices),
                'max_price': max(prices),
                'mean_price': sum(prices) / len(prices),
                'price_range_ratio': max(prices) / min(prices) if min(prices) > 0 else float('inf')
            })
            
            # Flag suspicious ranges
            if validation_results['price_range_ratio'] > 100:  # 100x difference
                request.warnings.append("Extremely wide price range after conversion - check exchange rates")
        
        request.conversion_metadata['validation_results'] = validation_results
        
        # Critical error if conversion rate too low
        if validation_results['conversion_rate'] < 50:
            request.errors.append("Critical: Less than 50% of observations converted successfully")
    
    async def _finalize_conversion(self, request: ConversionRequest) -> None:
        """Stage 6: Finalize conversion and prepare for analysis."""
        logger.debug(f"Finalizing conversion for request {request.request_id}")
        
        # Sort observations by date for analysis
        request.converted_observations.sort(key=lambda obs: obs.observed_date)
        
        # Add final metadata
        request.conversion_metadata['finalized_at'] = datetime.now()
        request.conversion_metadata['ready_for_analysis'] = True
        
        # Final validation that we have usable data
        if not request.converted_observations:
            request.errors.append("Critical: No observations available after conversion")
    
    def _infer_zone_from_market_id(self, market_id: str) -> CurrencyZone:
        """Infer currency zone from market ID (simplified heuristic)."""
        # This is a simplified heuristic - in practice would use proper classification
        market_lower = market_id.lower()
        
        if any(indicator in market_lower for indicator in ['sanaa', 'saada', 'amran', 'hajjah']):
            return CurrencyZone.HOUTHI
        elif any(indicator in market_lower for indicator in ['aden', 'hadramaut', 'lahj']):
            return CurrencyZone.GOVERNMENT
        elif any(indicator in market_lower for indicator in ['taiz', 'marib']):
            return CurrencyZone.CONTESTED
        else:
            return CurrencyZone.UNKNOWN
    
    def _estimate_exchange_rate(
        self,
        date: datetime.date,
        zone: CurrencyZone,
        from_currency: Currency,
        to_currency: Currency
    ) -> Optional[Decimal]:
        """Estimate exchange rate if not available (fallback only)."""
        
        # Base rates (these should come from actual data)
        base_rates = {
            CurrencyZone.HOUTHI: Decimal('535'),
            CurrencyZone.GOVERNMENT: Decimal('1800'),
            CurrencyZone.CONTESTED: Decimal('1200'),
            CurrencyZone.UNKNOWN: Decimal('1000')
        }
        
        if from_currency == Currency.YER and to_currency == Currency.USD:
            return base_rates.get(zone, Decimal('1000'))
        elif from_currency == Currency.USD and to_currency == Currency.YER:
            return base_rates.get(zone, Decimal('1000'))
        
        return None
    
    def _generate_cache_key(
        self,
        observations: List[PriceObservation],
        target_currency: Currency
    ) -> str:
        """Generate cache key for conversion request."""
        # Simple hash based on observation dates, markets, and target currency
        obs_summary = f"{len(observations)}_{target_currency.value}"
        
        if observations:
            date_range = f"{observations[0].observed_date.date()}_{observations[-1].observed_date.date()}"
            market_count = len(set(str(obs.market_id.value) for obs in observations))
            obs_summary += f"_{date_range}_{market_count}"
        
        return obs_summary
    
    def _calculate_quality_metrics(self, request: ConversionRequest) -> Dict[str, Any]:
        """Calculate quality metrics for the conversion."""
        return {
            'conversion_success_rate': (
                len(request.converted_observations) / len(request.observations) * 100
                if request.observations else 0
            ),
            'stages_completed': len(request.completed_stages),
            'total_stages': len(ConversionStage),
            'error_count': len(request.errors),
            'warning_count': len(request.warnings),
            'processing_efficiency': (
                len(request.completed_stages) / len(ConversionStage) * 100
            )
        }
    
    async def clear_cache(self) -> None:
        """Clear conversion and rate caches."""
        async with self._processing_lock:
            self._conversion_cache.clear()
            self._rate_cache.clear()
            
        logger.info("Cleared currency conversion caches")
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get current status of conversion queues."""
        return {
            'active_conversions': len(self._active_conversions),
            'queued_by_priority': {
                priority.value: len(queue)
                for priority, queue in self._conversion_queue.items()
            },
            'completed_conversions': len(self._completed_conversions),
            'cache_size': len(self._conversion_cache) if self.enable_caching else 0
        }