"""
Data Quality Orchestrator for Yemen Market Integration.

Coordinates all data quality operations including exchange rate validation,
zone-specific quality control, currency conversion timing, and conflict-aware
imputation to provide a comprehensive data quality framework.
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass, field
from enum import Enum

from .dynamic_exchange_rate_validator import DynamicExchangeRateValidator, ExchangeRateValidation
from .zone_specific_quality_control import ZoneSpecificQualityControl, ZoneQualityReport
from .currency_conversion_timing import CurrencyConversionTiming, ConversionResult
from .conflict_aware_imputation import ConflictAwareImputation, ImputationResult

from ...core.domain.market.currency_zones import CurrencyZone
from ...core.domain.market.value_objects import Currency, ExchangeRate
from ...core.domain.market.entities import Market, PriceObservation
from ...core.utils.logging import get_logger

logger = get_logger(__name__)


class DataQualityStage(Enum):
    """Stages in the comprehensive data quality pipeline."""
    INITIAL_VALIDATION = "initial_validation"
    EXCHANGE_RATE_VALIDATION = "exchange_rate_validation"
    ZONE_CLASSIFICATION = "zone_classification"
    ZONE_QUALITY_CONTROL = "zone_quality_control"
    CURRENCY_CONVERSION = "currency_conversion"
    MISSING_DATA_IMPUTATION = "missing_data_imputation"
    FINAL_VALIDATION = "final_validation"
    QUALITY_REPORTING = "quality_reporting"


class ProcessingPriority(Enum):
    """Priority levels for data quality processing."""
    CRITICAL = "critical"    # Real-time analysis requirements
    HIGH = "high"           # Standard analysis pipeline
    NORMAL = "normal"       # Batch processing
    LOW = "low"            # Background quality improvements


@dataclass
class QualityProcessingRequest:
    """Request for comprehensive data quality processing."""
    request_id: str
    raw_observations: List[PriceObservation]
    markets: List[Market]
    target_currency: Currency = Currency.USD
    priority: ProcessingPriority = ProcessingPriority.NORMAL
    
    # Optional pre-computed data to speed up processing
    zone_mappings: Optional[Dict[str, CurrencyZone]] = None
    exchange_rates: Optional[Dict[Tuple[datetime, CurrencyZone], ExchangeRate]] = None
    
    # Processing options
    enable_imputation: bool = True
    enable_survivor_bias_correction: bool = True
    confidence_threshold: float = 0.7
    
    # Tracking
    current_stage: DataQualityStage = DataQualityStage.INITIAL_VALIDATION
    completed_stages: List[DataQualityStage] = field(default_factory=list)
    stage_results: Dict[DataQualityStage, Any] = field(default_factory=dict)
    processing_errors: List[str] = field(default_factory=list)
    processing_warnings: List[str] = field(default_factory=list)
    
    # Results
    processed_observations: List[PriceObservation] = field(default_factory=list)
    quality_metrics: Dict[str, Any] = field(default_factory=dict)
    processing_metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DataQualityResult:
    """Comprehensive result of data quality processing."""
    request_id: str
    success: bool
    processed_observations: List[PriceObservation]
    quality_score: float  # 0-100 overall quality score
    
    # Stage-specific results
    exchange_rate_validations: List[ExchangeRateValidation]
    zone_quality_reports: Dict[CurrencyZone, ZoneQualityReport]
    conversion_result: Optional[ConversionResult]
    imputation_result: Optional[ImputationResult]
    
    # Processing metadata
    processing_time_seconds: float
    stages_completed: List[DataQualityStage]
    warnings: List[str]
    errors: List[str]
    
    # Quality metrics
    data_coverage: float  # Percentage of observations with data
    conversion_success_rate: float
    imputation_success_rate: float
    overall_confidence: float
    
    # Recommendations
    recommendations: List[str]
    next_steps: List[str]


class DataQualityOrchestrator:
    """
    Orchestrates comprehensive data quality processing for Yemen market data.
    
    This system coordinates all data quality operations to ensure that:
    1. Exchange rates are validated and dynamically adjusted
    2. Zone-specific quality controls are applied
    3. Currency conversion happens in the correct sequence
    4. Missing data is imputed using conflict-aware methods
    5. Results meet quality standards for economic analysis
    """
    
    def __init__(
        self,
        max_concurrent_requests: int = 3,
        default_timeout_minutes: int = 30,
        enable_caching: bool = True
    ):
        """
        Initialize the data quality orchestrator.
        
        Args:
            max_concurrent_requests: Maximum parallel quality processing requests
            default_timeout_minutes: Default timeout for complete processing
            enable_caching: Whether to cache results for performance
        """
        self.max_concurrent_requests = max_concurrent_requests
        self.default_timeout_minutes = default_timeout_minutes
        self.enable_caching = enable_caching
        
        # Initialize component services
        self.exchange_rate_validator = DynamicExchangeRateValidator()
        self.zone_quality_control = ZoneSpecificQualityControl()
        self.currency_conversion = CurrencyConversionTiming(enable_caching=enable_caching)
        self.conflict_imputation = ConflictAwareImputation()
        
        # Request management
        self._active_requests: Dict[str, QualityProcessingRequest] = {}
        self._completed_requests: Dict[str, DataQualityResult] = {}
        self._request_queue: List[QualityProcessingRequest] = []
        
        # Performance tracking
        self._processing_stats: Dict[str, Any] = {
            'total_requests': 0,
            'successful_requests': 0,
            'average_processing_time': 0,
            'stage_performance': {stage: {'count': 0, 'avg_time': 0} for stage in DataQualityStage}
        }
        
        # Caching
        self._result_cache: Dict[str, DataQualityResult] = {}
        
        # Processing lock
        self._processing_lock = asyncio.Lock()
    
    async def process_data_quality(
        self,
        observations: List[PriceObservation],
        markets: List[Market],
        target_currency: Currency = Currency.USD,
        priority: ProcessingPriority = ProcessingPriority.NORMAL,
        enable_imputation: bool = True,
        enable_survivor_bias_correction: bool = True,
        zone_mappings: Optional[Dict[str, CurrencyZone]] = None,
        exchange_rates: Optional[Dict[Tuple[datetime, CurrencyZone], ExchangeRate]] = None
    ) -> str:
        """
        Request comprehensive data quality processing.
        
        Args:
            observations: Raw price observations to process
            markets: Market definitions for the observations
            target_currency: Target currency for final output
            priority: Processing priority
            enable_imputation: Whether to impute missing data
            enable_survivor_bias_correction: Whether to apply survivor bias corrections
            zone_mappings: Pre-computed zone mappings (optional)
            exchange_rates: Pre-computed exchange rates (optional)
            
        Returns:
            Request ID for tracking
        """
        request_id = f"dq_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self._active_requests)}"
        
        # Check cache first
        cache_key = self._generate_cache_key(observations, target_currency)
        if self.enable_caching and cache_key in self._result_cache:
            cached_result = self._result_cache[cache_key]
            self._completed_requests[request_id] = cached_result
            logger.info(f"Returning cached data quality result for request {request_id}")
            return request_id
        
        # Create processing request
        request = QualityProcessingRequest(
            request_id=request_id,
            raw_observations=observations,
            markets=markets,
            target_currency=target_currency,
            priority=priority,
            zone_mappings=zone_mappings,
            exchange_rates=exchange_rates,
            enable_imputation=enable_imputation,
            enable_survivor_bias_correction=enable_survivor_bias_correction
        )
        
        # Add to queue
        async with self._processing_lock:
            self._request_queue.append(request)
            self._processing_stats['total_requests'] += 1
        
        logger.info(
            f"Queued data quality request {request_id} with {len(observations)} observations "
            f"(priority: {priority.value})"
        )
        
        # Start processing
        asyncio.create_task(self._process_queue())
        
        return request_id
    
    async def get_processing_status(self, request_id: str) -> Dict[str, Any]:
        """Get current status of a data quality processing request."""
        
        # Check completed requests
        if request_id in self._completed_requests:
            result = self._completed_requests[request_id]
            return {
                'status': 'completed',
                'success': result.success,
                'quality_score': result.quality_score,
                'processing_time': result.processing_time_seconds,
                'stages_completed': [stage.value for stage in result.stages_completed],
                'observations_processed': len(result.processed_observations),
                'warnings': len(result.warnings),
                'errors': len(result.errors)
            }
        
        # Check active requests
        if request_id in self._active_requests:
            request = self._active_requests[request_id]
            return {
                'status': 'processing',
                'current_stage': request.current_stage.value,
                'stages_completed': [stage.value for stage in request.completed_stages],
                'progress': len(request.completed_stages) / len(DataQualityStage) * 100,
                'errors': len(request.processing_errors),
                'warnings': len(request.processing_warnings)
            }
        
        # Check queue
        for i, request in enumerate(self._request_queue):
            if request.request_id == request_id:
                return {
                    'status': 'queued',
                    'queue_position': i,
                    'priority': request.priority.value,
                    'estimated_wait_minutes': i * 10  # Rough estimate
                }
        
        return {'status': 'not_found'}
    
    async def get_processing_result(self, request_id: str) -> Optional[DataQualityResult]:
        """Get the result of completed data quality processing."""
        return self._completed_requests.get(request_id)
    
    async def _process_queue(self) -> None:
        """Process data quality requests from the queue."""
        async with self._processing_lock:
            if len(self._active_requests) >= self.max_concurrent_requests:
                return
            
            if not self._request_queue:
                return
            
            # Get next request by priority
            next_request = self._get_next_request()
            if not next_request:
                return
            
            # Move to active processing
            self._active_requests[next_request.request_id] = next_request
        
        # Process the request
        try:
            result = await self._process_quality_request(next_request)
            
            # Store result and clean up
            async with self._processing_lock:
                self._completed_requests[next_request.request_id] = result
                self._active_requests.pop(next_request.request_id, None)
                
                # Update stats
                if result.success:
                    self._processing_stats['successful_requests'] += 1
                
                # Cache if successful
                if self.enable_caching and result.success:
                    cache_key = self._generate_cache_key(
                        next_request.raw_observations,
                        next_request.target_currency
                    )
                    self._result_cache[cache_key] = result
            
            logger.info(f"Completed data quality processing {next_request.request_id}")
            
        except Exception as e:
            logger.error(f"Error processing data quality request {next_request.request_id}: {e}")
            
            # Create error result
            error_result = DataQualityResult(
                request_id=next_request.request_id,
                success=False,
                processed_observations=[],
                quality_score=0,
                exchange_rate_validations=[],
                zone_quality_reports={},
                conversion_result=None,
                imputation_result=None,
                processing_time_seconds=0,
                stages_completed=next_request.completed_stages,
                warnings=next_request.processing_warnings,
                errors=next_request.processing_errors + [str(e)],
                data_coverage=0,
                conversion_success_rate=0,
                imputation_success_rate=0,
                overall_confidence=0,
                recommendations=[],
                next_steps=[]
            )
            
            async with self._processing_lock:
                self._completed_requests[next_request.request_id] = error_result
                self._active_requests.pop(next_request.request_id, None)
        
        # Continue processing queue
        await self._process_queue()
    
    def _get_next_request(self) -> Optional[QualityProcessingRequest]:
        """Get next request to process based on priority."""
        
        if not self._request_queue:
            return None
        
        # Sort by priority
        priority_order = {
            ProcessingPriority.CRITICAL: 0,
            ProcessingPriority.HIGH: 1,
            ProcessingPriority.NORMAL: 2,
            ProcessingPriority.LOW: 3
        }
        
        self._request_queue.sort(key=lambda r: priority_order[r.priority])
        return self._request_queue.pop(0)
    
    async def _process_quality_request(self, request: QualityProcessingRequest) -> DataQualityResult:
        """Process a single data quality request through all stages."""
        start_time = datetime.now()
        
        try:
            # Stage 1: Initial validation
            await self._stage_initial_validation(request)
            
            # Stage 2: Exchange rate validation
            await self._stage_exchange_rate_validation(request)
            
            # Stage 3: Zone classification
            await self._stage_zone_classification(request)
            
            # Stage 4: Zone-specific quality control
            await self._stage_zone_quality_control(request)
            
            # Stage 5: Currency conversion
            await self._stage_currency_conversion(request)
            
            # Stage 6: Missing data imputation (if enabled)
            if request.enable_imputation:
                await self._stage_missing_data_imputation(request)
            
            # Stage 7: Final validation
            await self._stage_final_validation(request)
            
            # Stage 8: Quality reporting
            await self._stage_quality_reporting(request)
            
        except Exception as e:
            request.processing_errors.append(f"Processing failed: {str(e)}")
            logger.error(f"Error in data quality processing: {e}")
        
        # Calculate processing time
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # Determine overall success
        success = (
            DataQualityStage.QUALITY_REPORTING in request.completed_stages and
            len(request.processed_observations) > 0 and
            not any('critical' in error.lower() for error in request.processing_errors)
        )
        
        # Calculate quality score
        quality_score = self._calculate_overall_quality_score(request)
        
        # Generate recommendations
        recommendations, next_steps = self._generate_recommendations(request)
        
        return DataQualityResult(
            request_id=request.request_id,
            success=success,
            processed_observations=request.processed_observations,
            quality_score=quality_score,
            exchange_rate_validations=request.stage_results.get(DataQualityStage.EXCHANGE_RATE_VALIDATION, []),
            zone_quality_reports=request.stage_results.get(DataQualityStage.ZONE_QUALITY_CONTROL, {}),
            conversion_result=request.stage_results.get(DataQualityStage.CURRENCY_CONVERSION),
            imputation_result=request.stage_results.get(DataQualityStage.MISSING_DATA_IMPUTATION),
            processing_time_seconds=processing_time,
            stages_completed=request.completed_stages,
            warnings=request.processing_warnings,
            errors=request.processing_errors,
            data_coverage=request.quality_metrics.get('data_coverage', 0),
            conversion_success_rate=request.quality_metrics.get('conversion_success_rate', 0),
            imputation_success_rate=request.quality_metrics.get('imputation_success_rate', 0),
            overall_confidence=request.quality_metrics.get('overall_confidence', 0),
            recommendations=recommendations,
            next_steps=next_steps
        )
    
    async def _stage_initial_validation(self, request: QualityProcessingRequest) -> None:
        """Stage 1: Initial validation of raw data."""
        request.current_stage = DataQualityStage.INITIAL_VALIDATION
        
        validation_issues = []
        valid_observations = []
        
        for obs in request.raw_observations:
            # Basic validation
            if obs.price.amount <= 0:
                validation_issues.append(f"Invalid price {obs.price.amount} for market {obs.market_id}")
                continue
            
            if obs.observed_date > datetime.now():
                validation_issues.append(f"Future date {obs.observed_date} for market {obs.market_id}")
                continue
            
            valid_observations.append(obs)
        
        request.stage_results[DataQualityStage.INITIAL_VALIDATION] = {
            'input_observations': len(request.raw_observations),
            'valid_observations': len(valid_observations),
            'validation_issues': validation_issues[:10]  # Limit for performance
        }
        
        # Update working dataset
        request.processed_observations = valid_observations
        
        if validation_issues:
            request.processing_warnings.extend(validation_issues[:5])
        
        request.completed_stages.append(DataQualityStage.INITIAL_VALIDATION)
    
    async def _stage_exchange_rate_validation(self, request: QualityProcessingRequest) -> None:
        """Stage 2: Validate exchange rates."""
        request.current_stage = DataQualityStage.EXCHANGE_RATE_VALIDATION
        
        # Collect exchange rates from observations and external sources
        rate_observations = []
        
        # Extract rates from request if provided
        if request.exchange_rates:
            for (date, zone), rate in request.exchange_rates.items():
                rate_observations.append((zone, rate.rate, date, "provided"))
        
        # Validate rates
        if rate_observations:
            validations = await self.exchange_rate_validator.validate_batch_rates(rate_observations)
            request.stage_results[DataQualityStage.EXCHANGE_RATE_VALIDATION] = validations
            
            # Check for critical rate issues
            invalid_rates = [v for v in validations if v.validity.value == 'invalid']
            if invalid_rates:
                request.processing_warnings.append(f"Found {len(invalid_rates)} invalid exchange rates")
        
        request.completed_stages.append(DataQualityStage.EXCHANGE_RATE_VALIDATION)
    
    async def _stage_zone_classification(self, request: QualityProcessingRequest) -> None:
        """Stage 3: Classify markets into currency zones."""
        request.current_stage = DataQualityStage.ZONE_CLASSIFICATION
        
        zone_mappings = request.zone_mappings or {}
        
        # Classify any unmapped markets
        for market in request.markets:
            market_id = str(market.market_id.value)
            if market_id not in zone_mappings:
                # Use simple heuristic (would use proper classifier in production)
                zone_mappings[market_id] = self._classify_market_to_zone(market)
        
        request.zone_mappings = zone_mappings
        request.stage_results[DataQualityStage.ZONE_CLASSIFICATION] = zone_mappings
        
        request.completed_stages.append(DataQualityStage.ZONE_CLASSIFICATION)
    
    async def _stage_zone_quality_control(self, request: QualityProcessingRequest) -> None:
        """Stage 4: Apply zone-specific quality control."""
        request.current_stage = DataQualityStage.ZONE_QUALITY_CONTROL
        
        # Group observations by zone
        zone_observations = []
        for obs in request.processed_observations:
            market_id = str(obs.market_id.value)
            zone = request.zone_mappings.get(market_id, CurrencyZone.UNKNOWN)
            
            # Find corresponding market
            market = next((m for m in request.markets if str(m.market_id.value) == market_id), None)
            if market:
                zone_observations.append((obs, market, zone))
        
        # Apply zone-specific quality control
        if zone_observations:
            zone_reports = await self.zone_quality_control.validate_batch_observations(zone_observations)
            request.stage_results[DataQualityStage.ZONE_QUALITY_CONTROL] = zone_reports
            
            # Check for critical quality issues
            for zone, report in zone_reports.items():
                critical_issues = [i for i in report.issues_detected if i.severity == 'critical']
                if critical_issues:
                    request.processing_warnings.append(f"Critical quality issues in {zone.value}: {len(critical_issues)}")
        
        request.completed_stages.append(DataQualityStage.ZONE_QUALITY_CONTROL)
    
    async def _stage_currency_conversion(self, request: QualityProcessingRequest) -> None:
        """Stage 5: Perform currency conversion."""
        request.current_stage = DataQualityStage.CURRENCY_CONVERSION
        
        # Request currency conversion
        conversion_request_id = await self.currency_conversion.request_conversion(
            observations=request.processed_observations,
            target_currency=request.target_currency,
            priority=request.priority.value,
            zone_mappings=request.zone_mappings,
            exchange_rates=request.exchange_rates
        )
        
        # Wait for conversion to complete
        timeout_seconds = self.default_timeout_minutes * 60 * 0.3  # 30% of total timeout
        start_time = datetime.now()
        
        while True:
            status = await self.currency_conversion.get_conversion_status(conversion_request_id)
            
            if status['status'] == 'completed':
                conversion_result = await self.currency_conversion.get_conversion_result(conversion_request_id)
                request.stage_results[DataQualityStage.CURRENCY_CONVERSION] = conversion_result
                
                if conversion_result and conversion_result.success:
                    request.processed_observations = conversion_result.converted_observations
                    request.quality_metrics['conversion_success_rate'] = (
                        len(conversion_result.converted_observations) / len(request.processed_observations) * 100
                        if request.processed_observations else 0
                    )
                else:
                    request.processing_errors.append("Currency conversion failed")
                break
            
            elif status['status'] == 'not_found':
                request.processing_errors.append("Currency conversion request not found")
                break
            
            # Check timeout
            if (datetime.now() - start_time).total_seconds() > timeout_seconds:
                request.processing_errors.append("Currency conversion timeout")
                break
            
            # Wait before checking again
            await asyncio.sleep(2)
        
        request.completed_stages.append(DataQualityStage.CURRENCY_CONVERSION)
    
    async def _stage_missing_data_imputation(self, request: QualityProcessingRequest) -> None:
        """Stage 6: Impute missing data using conflict-aware methods."""
        request.current_stage = DataQualityStage.MISSING_DATA_IMPUTATION
        
        # Convert observations to DataFrame for imputation
        market_data = self._convert_observations_to_dataframe(request.processed_observations)
        
        if market_data.isnull().any().any():  # Has missing data
            # Create conflict contexts (simplified - would use actual conflict data)
            conflict_contexts = self._create_conflict_contexts(request)
            
            # Apply imputation for each commodity/zone combination
            imputation_results = []
            
            commodities = list(set(obs.commodity.name for obs in request.processed_observations))
            zones = list(set(request.zone_mappings.values()))
            
            for commodity in commodities:
                for zone in zones:
                    if zone != CurrencyZone.UNKNOWN:
                        # Filter data for this commodity/zone
                        zone_markets = [m for m, z in request.zone_mappings.items() if z == zone]
                        commodity_data = market_data[market_data.index.isin(zone_markets)]
                        
                        if not commodity_data.empty and commodity_data.isnull().any().any():
                            imputation_result = await self.conflict_imputation.impute_missing_data(
                                market_data=commodity_data,
                                conflict_contexts=conflict_contexts,
                                commodity=commodity,
                                zone=zone
                            )
                            
                            imputation_results.append(imputation_result)
                            
                            if imputation_result.success:
                                # Add imputed observations to processed set
                                request.processed_observations.extend(imputation_result.imputed_observations)
            
            request.stage_results[DataQualityStage.MISSING_DATA_IMPUTATION] = imputation_results
            
            # Calculate imputation success rate
            successful_imputations = [r for r in imputation_results if r.success]
            request.quality_metrics['imputation_success_rate'] = (
                len(successful_imputations) / len(imputation_results) * 100
                if imputation_results else 0
            )
        
        request.completed_stages.append(DataQualityStage.MISSING_DATA_IMPUTATION)
    
    async def _stage_final_validation(self, request: QualityProcessingRequest) -> None:
        """Stage 7: Final validation of processed data."""
        request.current_stage = DataQualityStage.FINAL_VALIDATION
        
        final_validation = {
            'total_observations': len(request.processed_observations),
            'target_currency_compliance': sum(
                1 for obs in request.processed_observations 
                if obs.price.currency == request.target_currency
            ),
            'price_range_check': self._validate_price_ranges(request.processed_observations),
            'temporal_coverage': self._validate_temporal_coverage(request.processed_observations)
        }
        
        request.stage_results[DataQualityStage.FINAL_VALIDATION] = final_validation
        
        # Calculate data coverage
        request.quality_metrics['data_coverage'] = (
            final_validation['target_currency_compliance'] / final_validation['total_observations'] * 100
            if final_validation['total_observations'] > 0 else 0
        )
        
        request.completed_stages.append(DataQualityStage.FINAL_VALIDATION)
    
    async def _stage_quality_reporting(self, request: QualityProcessingRequest) -> None:
        """Stage 8: Generate quality reports and metrics."""
        request.current_stage = DataQualityStage.QUALITY_REPORTING
        
        # Calculate overall confidence
        confidence_scores = []
        
        # Exchange rate confidence
        if DataQualityStage.EXCHANGE_RATE_VALIDATION in request.stage_results:
            validations = request.stage_results[DataQualityStage.EXCHANGE_RATE_VALIDATION]
            if validations:
                confidence_scores.extend([v.confidence for v in validations])
        
        # Conversion confidence
        if DataQualityStage.CURRENCY_CONVERSION in request.stage_results:
            conversion_result = request.stage_results[DataQualityStage.CURRENCY_CONVERSION]
            if conversion_result and 'confidence' in conversion_result.quality_metrics:
                confidence_scores.append(conversion_result.quality_metrics['confidence'])
        
        # Imputation confidence
        if DataQualityStage.MISSING_DATA_IMPUTATION in request.stage_results:
            imputation_results = request.stage_results[DataQualityStage.MISSING_DATA_IMPUTATION]
            for result in imputation_results:
                if result.confidence_scores:
                    confidence_scores.extend(result.confidence_scores.values())
        
        request.quality_metrics['overall_confidence'] = (
            sum(confidence_scores) / len(confidence_scores) * 100
            if confidence_scores else 50  # Default neutral confidence
        )
        
        request.completed_stages.append(DataQualityStage.QUALITY_REPORTING)
    
    def _classify_market_to_zone(self, market: Market) -> CurrencyZone:
        """Classify a market to a currency zone (simplified heuristic)."""
        # This is a simplified implementation - would use proper classification in production
        if hasattr(market, 'governorate'):
            gov_lower = market.governorate.lower()
            
            if any(indicator in gov_lower for indicator in ['sanaa', 'saada', 'amran']):
                return CurrencyZone.HOUTHI
            elif any(indicator in gov_lower for indicator in ['aden', 'hadramaut']):
                return CurrencyZone.GOVERNMENT
            elif any(indicator in gov_lower for indicator in ['taiz', 'marib']):
                return CurrencyZone.CONTESTED
        
        return CurrencyZone.UNKNOWN
    
    def _convert_observations_to_dataframe(self, observations: List[PriceObservation]) -> pd.DataFrame:
        """Convert observations to DataFrame for imputation."""
        import pandas as pd
        
        # Create pivot table: markets x dates
        data_rows = []
        for obs in observations:
            data_rows.append({
                'market_id': str(obs.market_id.value),
                'date': obs.observed_date,
                'price': float(obs.price.amount)
            })
        
        if not data_rows:
            return pd.DataFrame()
        
        df = pd.DataFrame(data_rows)
        return df.pivot(index='market_id', columns='date', values='price')
    
    def _create_conflict_contexts(self, request: QualityProcessingRequest) -> List:
        """Create conflict contexts for imputation (simplified)."""
        from .conflict_aware_imputation import ConflictContext
        
        contexts = []
        
        # Create basic conflict contexts for each market/date combination
        for obs in request.processed_observations:
            market_id = str(obs.market_id.value)
            zone = request.zone_mappings.get(market_id, CurrencyZone.UNKNOWN)
            
            # Simple conflict intensity based on zone
            conflict_intensity = {
                CurrencyZone.HOUTHI: 0.3,
                CurrencyZone.GOVERNMENT: 0.4,
                CurrencyZone.CONTESTED: 0.8,
                CurrencyZone.UNKNOWN: 0.5
            }.get(zone, 0.5)
            
            context = ConflictContext(
                date=obs.observed_date,
                market_id=market_id,
                zone=zone,
                conflict_intensity=conflict_intensity,
                siege_status=False,
                infrastructure_damage=conflict_intensity * 0.6,
                trader_presence=1.0 - conflict_intensity * 0.5,
                accessibility=1.0 - conflict_intensity * 0.4,
                nearby_conflict_events=int(conflict_intensity * 10),
                days_since_last_report=7
            )
            
            contexts.append(context)
        
        return contexts
    
    def _validate_price_ranges(self, observations: List[PriceObservation]) -> Dict[str, Any]:
        """Validate price ranges are reasonable."""
        if not observations:
            return {'valid': True}
        
        prices = [float(obs.price.amount) for obs in observations]
        
        return {
            'min_price': min(prices),
            'max_price': max(prices),
            'mean_price': sum(prices) / len(prices),
            'price_range_ratio': max(prices) / min(prices) if min(prices) > 0 else float('inf'),
            'negative_prices': sum(1 for p in prices if p < 0),
            'zero_prices': sum(1 for p in prices if p == 0),
            'extreme_values': sum(1 for p in prices if p > 1000)  # Prices > $1000
        }
    
    def _validate_temporal_coverage(self, observations: List[PriceObservation]) -> Dict[str, Any]:
        """Validate temporal coverage of observations."""
        if not observations:
            return {'valid': True}
        
        dates = [obs.observed_date for obs in observations]
        
        return {
            'start_date': min(dates),
            'end_date': max(dates),
            'date_range_days': (max(dates) - min(dates)).days,
            'unique_dates': len(set(dates)),
            'observations_per_day': len(observations) / len(set(dates)) if set(dates) else 0
        }
    
    def _calculate_overall_quality_score(self, request: QualityProcessingRequest) -> float:
        """Calculate overall quality score (0-100)."""
        
        base_score = 100
        penalties = 0
        
        # Penalty for processing errors
        critical_errors = [e for e in request.processing_errors if 'critical' in e.lower()]
        penalties += len(critical_errors) * 30
        penalties += (len(request.processing_errors) - len(critical_errors)) * 10
        
        # Penalty for warnings
        penalties += len(request.processing_warnings) * 2
        
        # Penalty for incomplete stages
        incomplete_stages = len(DataQualityStage) - len(request.completed_stages)
        penalties += incomplete_stages * 15
        
        # Bonus for high confidence
        overall_confidence = request.quality_metrics.get('overall_confidence', 50)
        if overall_confidence > 80:
            penalties -= 5  # Bonus
        elif overall_confidence < 40:
            penalties += 10  # Additional penalty
        
        return max(0, min(100, base_score - penalties))
    
    def _generate_recommendations(self, request: QualityProcessingRequest) -> Tuple[List[str], List[str]]:
        """Generate recommendations and next steps based on processing results."""
        
        recommendations = []
        next_steps = []
        
        # Analyze results and generate specific recommendations
        quality_score = self._calculate_overall_quality_score(request)
        
        if quality_score < 70:
            recommendations.append("Data quality below acceptable threshold - requires attention")
            next_steps.append("Review and address critical quality issues before analysis")
        
        if request.processing_errors:
            recommendations.append("Processing errors detected - data pipeline needs review")
            next_steps.append("Investigate and fix data processing errors")
        
        coverage = request.quality_metrics.get('data_coverage', 0)
        if coverage < 80:
            recommendations.append("Low data coverage - consider additional data sources")
            next_steps.append("Improve data collection or imputation methods")
        
        conversion_rate = request.quality_metrics.get('conversion_success_rate', 0)
        if conversion_rate < 90:
            recommendations.append("Currency conversion issues - check exchange rate data")
            next_steps.append("Update exchange rate sources and validation rules")
        
        confidence = request.quality_metrics.get('overall_confidence', 50)
        if confidence < 60:
            recommendations.append("Low confidence in data quality - use results with caution")
            next_steps.append("Improve data validation and quality control procedures")
        
        return recommendations, next_steps
    
    def _generate_cache_key(self, observations: List[PriceObservation], target_currency: Currency) -> str:
        """Generate cache key for processing request."""
        # Simple hash based on observation count, date range, and target currency
        if not observations:
            return f"empty_{target_currency.value}"
        
        dates = [obs.observed_date for obs in observations]
        return f"{len(observations)}_{min(dates).date()}_{max(dates).date()}_{target_currency.value}"
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get current processing statistics."""
        return {
            'total_requests': self._processing_stats['total_requests'],
            'successful_requests': self._processing_stats['successful_requests'],
            'success_rate': (
                self._processing_stats['successful_requests'] / self._processing_stats['total_requests'] * 100
                if self._processing_stats['total_requests'] > 0 else 0
            ),
            'active_requests': len(self._active_requests),
            'queued_requests': len(self._request_queue),
            'completed_requests': len(self._completed_requests),
            'cache_size': len(self._result_cache) if self.enable_caching else 0
        }
    
    async def clear_caches(self) -> None:
        """Clear all caches to free memory."""
        async with self._processing_lock:
            self._result_cache.clear()
            
        await self.currency_conversion.clear_cache()
        
        logger.info("Cleared data quality orchestrator caches")