# Comprehensive Data Quality Framework

This package implements a comprehensive data quality framework specifically designed for Yemen market integration analysis. It addresses critical technical gaps identified in Priority 2 of the improvement plan, particularly replacing hard-coded exchange rate multipliers with dynamic, data-driven validation systems.

## Overview

The framework consists of five integrated components that work together to ensure data quality for conflict-affected market analysis:

1. **Dynamic Exchange Rate Validator** - Replaces hard-coded multipliers with adaptive validation
2. **Zone-Specific Quality Control** - Implements quality bounds specific to currency zones
3. **Currency Conversion Timing** - Ensures proper sequencing of conversion operations
4. **Conflict-Aware Imputation** - Handles missing data that is NOT missing at random
5. **Data Quality Orchestrator** - Coordinates all operations with comprehensive reporting

## Key Features

### 🔄 Dynamic Exchange Rate Validation
- **Replaces hard-coded multipliers** with data-driven validation
- Validates rates against historical patterns and cross-zone consistency
- Provides confidence scores and suggested corrections
- Adapts constraints based on actual market data

### 🎯 Zone-Specific Quality Control
- **Different quality bounds for each currency zone** (Houthi, Government, Contested)
- Accounts for conflict volatility and seasonal patterns
- Detects currency mismatches and implausible price levels
- Applies survivor bias corrections for conflict areas

### ⏱️ Currency Conversion Timing
- **Ensures proper sequencing** to prevent Yemen Paradox errors
- Priority-based processing queue with timeout management
- Comprehensive validation at each stage
- Caching for performance optimization

### 📊 Conflict-Aware Imputation
- **Recognizes that missing data is NOT random** in conflict settings
- Multiple imputation methods (weighted mean, spatial, temporal, ML-based)
- Accounts for conflict intensity, infrastructure damage, trader presence
- Applies survivor bias corrections where appropriate

### 🎼 Comprehensive Orchestration
- **Coordinates all quality operations** in proper sequence
- Real-time status tracking and quality reporting
- Configurable processing priorities and timeouts
- Integration with existing currency-aware processor

## Quick Start

### Basic Usage

```python
from src.infrastructure.data_quality.integration import validate_yemen_market_data
from src.core.domain.market.value_objects import Currency

# Process market data with full quality pipeline
processed_observations, quality_report = await validate_yemen_market_data(
    observations=raw_observations,
    markets=markets,
    target_currency=Currency.USD
)

print(f"Quality score: {quality_report['quality_score']}/100")
print(f"Data coverage: {quality_report['data_coverage']}%")
```

### Advanced Usage

```python
from src.infrastructure.data_quality.integration import DataQualityService

# Create service with full control
service = DataQualityService()

processed_observations, quality_report = await service.process_market_data(
    observations=observations,
    markets=markets,
    target_currency=Currency.USD,
    enable_imputation=True,
    priority="high"  # 'critical', 'high', 'normal', 'low'
)
```

### Enhanced Currency-Aware Processor

```python
from src.infrastructure.processors.currency_aware_wfp_processor import CurrencyAwareWFPProcessor

# Initialize with advanced quality control
processor = CurrencyAwareWFPProcessor(
    enable_advanced_quality_control=True
)

# Process WFP data with comprehensive quality framework
markets, observations, rates, metrics = await processor.process(
    data_source="path/to/wfp_data.csv",
    zone_aware=True
)

print(f"Advanced quality control: {metrics['advanced_quality_control']}")
print(f"Quality score: {metrics.get('quality_score', 'N/A')}")
```

## Component Details

### Dynamic Exchange Rate Validator

Addresses the critical issue of hard-coded exchange rate multipliers:

```python
from src.infrastructure.data_quality.dynamic_exchange_rate_validator import DynamicExchangeRateValidator

validator = DynamicExchangeRateValidator()

# Validate individual rate
validation = await validator.validate_exchange_rate(
    zone=CurrencyZone.HOUTHI,
    rate=535.0,
    date=datetime.now(),
    source="central_bank"
)

print(f"Validity: {validation.validity.value}")
print(f"Confidence: {validation.confidence:.2f}")
print(f"Flags: {validation.validation_flags}")
```

**Key Features:**
- Historical consistency validation
- Cross-zone spread analysis  
- Temporal consistency checking
- Market condition validation
- Dynamic constraint updates
- Suggested rate corrections

### Zone-Specific Quality Control

Implements different quality standards for each currency zone:

```python
from src.infrastructure.data_quality.zone_specific_quality_control import ZoneSpecificQualityControl

qc = ZoneSpecificQualityControl()

# Validate observation with zone-specific bounds
issues = await qc.validate_price_observation(
    observation=price_obs,
    market=market,
    zone=CurrencyZone.GOVERNMENT
)

for issue in issues:
    print(f"{issue.issue_type.value}: {issue.description}")
```

**Quality Bounds by Zone:**
- **Houthi Zone**: Lower volatility, baseline exchange rate ~535 YER/USD
- **Government Zone**: Higher volatility, exchange rate ~1800 YER/USD  
- **Contested Zone**: Highest volatility, mixed exchange rates
- **Unknown Zone**: Conservative bounds for unclassified markets

### Conflict-Aware Imputation

Handles missing data with conflict context:

```python
from src.infrastructure.data_quality.conflict_aware_imputation import ConflictAwareImputation, ImputationMethod

imputer = ConflictAwareImputation()

result = await imputer.impute_missing_data(
    market_data=panel_data_with_gaps,
    conflict_contexts=conflict_contexts,
    commodity="wheat_flour",
    zone=CurrencyZone.CONTESTED,
    method=ImputationMethod.CONFLICT_INTENSITY_MODEL
)

print(f"Imputed {len(result.imputed_observations)} observations")
print(f"Average confidence: {np.mean(list(result.confidence_scores.values())):.2f}")
```

**Imputation Methods:**
- `CONFLICT_WEIGHTED_MEAN` - Weight by conflict similarity
- `SPATIAL_INTERPOLATION` - Use nearby market data
- `TEMPORAL_REGRESSION` - Model with conflict features
- `ZONE_SPECIFIC_KNN` - K-nearest neighbors within zone
- `CONFLICT_INTENSITY_MODEL` - Random forest with conflict variables
- `SURVIVOR_BIAS_CORRECTION` - Adjust for trader bias

## Configuration

### Exchange Rate Validation Settings

```python
validator = DynamicExchangeRateValidator(
    validation_window_days=30,        # Historical data window
    confidence_threshold=0.7          # Minimum confidence for acceptance
)
```

### Quality Control Bounds

```python
qc = ZoneSpecificQualityControl()

# Update bounds for specific commodity/zone
qc.update_zone_bounds(
    zone=CurrencyZone.HOUTHI,
    commodity="wheat_flour",
    new_bounds=ZoneQualityBounds(
        zone=CurrencyZone.HOUTHI,
        commodity="wheat_flour",
        min_price_usd=Decimal("0.25"),
        max_price_usd=Decimal("2.00"),
        # ... other parameters
    )
)
```

### Processing Priorities

```python
# Critical: Real-time analysis requirements
# High: Standard analysis pipeline  
# Normal: Batch processing
# Low: Background quality improvements

request_id = await orchestrator.process_data_quality(
    observations=observations,
    markets=markets,
    priority=ProcessingPriority.CRITICAL
)
```

## Error Handling

The framework provides comprehensive error handling and graceful degradation:

```python
try:
    processed_obs, quality_report = await validate_yemen_market_data(
        observations=observations,
        markets=markets
    )
    
    # Check quality score
    if quality_report['quality_score'] < 70:
        logger.warning("Low quality score - review recommendations")
        
    # Check for critical errors
    if any('critical' in warning.lower() for warning in quality_report['warnings']):
        logger.error("Critical data quality issues detected")
        
except Exception as e:
    logger.error(f"Data quality processing failed: {e}")
    # Fall back to basic processing
```

## Integration with Existing Code

The framework is designed to integrate seamlessly with existing systems:

### Drop-in Replacement

```python
# Old code
from src.infrastructure.processors.currency_aware_wfp_processor import CurrencyAwareWFPProcessor

processor = CurrencyAwareWFPProcessor()

# New code (just add one parameter)
processor = CurrencyAwareWFPProcessor(
    enable_advanced_quality_control=True  # Enable new framework
)
```

### Backward Compatibility

The framework maintains full backward compatibility:
- Legacy processing available if advanced features disabled
- Existing code continues to work without changes
- Progressive migration path available

## Performance Considerations

### Caching

```python
# Enable caching for better performance
orchestrator = DataQualityOrchestrator(enable_caching=True)

# Clear caches when needed
await orchestrator.clear_caches()
```

### Parallel Processing

```python
# Configure concurrent processing
orchestrator = DataQualityOrchestrator(
    max_concurrent_requests=5,      # Parallel requests
    default_timeout_minutes=30      # Per-request timeout
)
```

### Memory Management

```python
# Process large datasets in batches
for batch in data_batches:
    processed_batch, quality_report = await service.process_market_data(
        observations=batch,
        markets=markets,
        priority="normal"
    )
    
    # Process results immediately to free memory
    analyze_batch(processed_batch)
```

## Quality Metrics

The framework provides comprehensive quality metrics:

```python
quality_report = {
    'quality_score': 85.2,                    # 0-100 overall score
    'data_coverage': 94.1,                    # % observations with data
    'conversion_success_rate': 98.5,          # % successful conversions
    'imputation_success_rate': 87.3,          # % successful imputations
    'overall_confidence': 82.7,               # Average confidence
    'processing_time_seconds': 12.4,          # Performance metric
    'warnings': ['Low confidence in 3 rates'], # Issues detected
    'recommendations': [                       # Suggested improvements
        'Update exchange rate sources',
        'Improve data collection in contested areas'
    ],
    'stages_completed': [                      # Processing stages
        'initial_validation',
        'exchange_rate_validation', 
        'zone_classification',
        'zone_quality_control',
        'currency_conversion',
        'missing_data_imputation',
        'final_validation',
        'quality_reporting'
    ]
}
```

## Yemen Paradox Resolution

The framework specifically addresses the Yemen Paradox - where improper currency handling led to incorrect conclusions about price relationships:

### Before (Incorrect)
```
Sanaa (Houthi): 400 YER/kg - appears cheaper
Aden (Government): 1500 YER/kg - appears more expensive
Conclusion: Houthi areas have lower prices (WRONG)
```

### After (Correct)
```
Sanaa (Houthi): 400 YER ÷ 535 = $0.75/kg
Aden (Government): 1500 YER ÷ 1800 = $0.83/kg  
Conclusion: Similar real prices with proper conversion (CORRECT)
```

The framework ensures this conversion happens correctly every time.

## Testing

Comprehensive tests are available:

```bash
# Run all data quality tests
pytest tests/integration/test_data_quality_framework.py -v

# Run specific component tests
pytest tests/integration/test_data_quality_framework.py::TestDynamicExchangeRateValidator -v
```

## Examples

See `examples/data_quality_example.py` for comprehensive usage examples including:
- Exchange rate validation
- Comprehensive data processing  
- Missing data imputation
- Enhanced processor usage
- Yemen Paradox demonstration

## Troubleshooting

### Common Issues

1. **Low Quality Scores**
   - Check exchange rate data sources
   - Verify zone classifications
   - Review missing data patterns

2. **Conversion Failures**
   - Ensure proper currency fields
   - Check exchange rate availability
   - Verify date ranges

3. **Performance Issues**
   - Enable caching
   - Reduce concurrent requests
   - Process data in smaller batches

### Debug Information

```python
# Get processing statistics
stats = orchestrator.get_processing_statistics()
print(f"Success rate: {stats['success_rate']:.1f}%")

# Get queue status
queue_status = converter.get_queue_status()
print(f"Active conversions: {queue_status['active_conversions']}")

# Check validation summary
validation_summary = validator.get_validation_summary(validations)
print(f"Data quality: {validation_summary['data_quality_score']:.1f}%")
```

## Architecture

```
DataQualityOrchestrator
├── DynamicExchangeRateValidator
│   ├── Historical validation
│   ├── Cross-zone consistency  
│   ├── Temporal analysis
│   └── Dynamic constraints
├── ZoneSpecificQualityControl
│   ├── Zone-specific bounds
│   ├── Conflict adjustments
│   ├── Seasonal patterns
│   └── Survivor bias detection
├── CurrencyConversionTiming
│   ├── Stage sequencing
│   ├── Priority queuing
│   ├── Timeout management
│   └── Error handling
└── ConflictAwareImputation
    ├── Pattern analysis
    ├── Multiple methods
    ├── Confidence scoring
    └── Bias correction
```

## Contributing

When contributing to the data quality framework:

1. Maintain academic rigor and econometric precision
2. Include comprehensive tests for new features
3. Update documentation with examples
4. Consider performance implications
5. Ensure backward compatibility

## Future Enhancements

Planned improvements include:
- Integration with real-time conflict data
- Machine learning model optimization
- Geographic distance calculations
- Advanced survivor bias models
- Cross-commodity price relationships
- Automated quality threshold tuning

## References

This framework implements methodological approaches from:
- World Bank economic analysis standards
- Conflict economics literature
- Missing data analysis in economics
- Exchange rate fragmentation studies
- Market integration econometrics

## License

This framework is part of the Yemen Market Integration research project and follows the project's licensing terms.