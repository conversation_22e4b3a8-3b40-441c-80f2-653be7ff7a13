"""Data migration from V1 to V2 repository structures."""

import asyncio
import logging
from datetime import datetime
from decimal import Decimal
from pathlib import Path
from typing import Dict, List, Optional
from uuid import uuid4

import pandas as pd
import asyncpg

from ....core.domain.market.entities import Market, PriceObservation
from ....core.domain.market.value_objects import (
    MarketId, Coordinates, MarketType, Commodity, Price
)
from ....core.domain.conflict.entities import ConflictEvent
from ....core.domain.conflict.value_objects import (
    ConflictEventId, EventType, ConflictLocation, FatalityCount
)
from ..unit_of_work import PostgresUnitOfWork

logger = logging.getLogger(__name__)


class V1ToV2DataMigrator:
    """Migrates data from V1 structure to V2 domain models."""
    
    def __init__(self, v1_data_path: str, v2_connection_string: str):
        """Initialize migrator.
        
        Args:
            v1_data_path: Path to V1 processed data directory
            v2_connection_string: PostgreSQL connection string for V2
        """
        self.v1_data_path = Path(v1_data_path)
        self.v2_connection_string = v2_connection_string
        
        # V1 to V2 commodity mappings
        self.commodity_mappings = {
            'wheat': 'WHEAT',
            'wheat_flour': 'WHEAT_FLOUR',
            'rice_imported': 'RICE_IMPORTED',
            'sugar': 'SUGAR',
            'oil_vegetable': 'OIL_VEGETABLE',
            'beans_kidney_red': 'BEANS_KIDNEY_RED',
            'beans_white': 'BEANS_WHITE',
            'salt': 'SALT',
            'fuel_diesel': 'FUEL_DIESEL',
            'fuel_petrol': 'FUEL_PETROL'
        }
        
        # Governorate name standardization
        self.governorate_mappings = {
            "Al Dhale'e": "Ad Dale'",
            "Al Hudaydah": "Al Hodeidah", 
            "Amanat Al Asimah": "Sana'a City",
            "Hadramaut": "Hadramawt",
            "Sa'ada": "Sa'dah",
            "Taizz": "Ta'iz"
        }
    
    async def migrate_all_data(self) -> Dict[str, int]:
        """Migrate all data from V1 to V2."""
        logger.info("Starting V1 to V2 data migration")
        
        results = {
            'markets': 0,
            'commodities': 0,
            'price_observations': 0,
            'conflict_events': 0
        }
        
        async with PostgresUnitOfWork(self.v2_connection_string) as uow:
            # 1. Migrate commodities first (reference data)
            results['commodities'] = await self._migrate_commodities(uow)
            
            # 2. Migrate markets
            results['markets'] = await self._migrate_markets(uow)
            
            # 3. Migrate price observations
            results['price_observations'] = await self._migrate_price_observations(uow)
            
            # 4. Migrate conflict events
            results['conflict_events'] = await self._migrate_conflict_events(uow)
            
            await uow.commit()
        
        logger.info(f"Migration completed: {results}")
        return results
    
    async def _migrate_commodities(self, uow: PostgresUnitOfWork) -> int:
        """Migrate commodity reference data."""
        logger.info("Migrating commodities")
        
        commodities = [
            Commodity(
                code='WHEAT',
                name='Wheat',
                category='Cereals',
                standard_unit='kg'
            ),
            Commodity(
                code='WHEAT_FLOUR',
                name='Wheat Flour',
                category='Cereals',
                standard_unit='kg'
            ),
            Commodity(
                code='RICE_IMPORTED',
                name='Rice (Imported)',
                category='Cereals',
                standard_unit='kg'
            ),
            Commodity(
                code='SUGAR',
                name='Sugar',
                category='Food',
                standard_unit='kg'
            ),
            Commodity(
                code='OIL_VEGETABLE',
                name='Oil (Vegetable)',
                category='Food',
                standard_unit='litre'
            ),
            Commodity(
                code='BEANS_KIDNEY_RED',
                name='Beans (Kidney Red)',
                category='Pulses',
                standard_unit='kg'
            ),
            Commodity(
                code='BEANS_WHITE',
                name='Beans (White)',
                category='Pulses',
                standard_unit='kg'
            ),
            Commodity(
                code='SALT',
                name='Salt',
                category='Food',
                standard_unit='kg'
            ),
            Commodity(
                code='FUEL_DIESEL',
                name='Fuel (Diesel)',
                category='Fuel',
                standard_unit='litre'
            ),
            Commodity(
                code='FUEL_PETROL',
                name='Fuel (Petrol-Gasoline)',
                category='Fuel',
                standard_unit='litre'
            )
        ]
        
        await uow.commodities.save_batch(commodities)
        return len(commodities)
    
    async def _migrate_markets(self, uow: PostgresUnitOfWork) -> int:
        """Migrate market data from V1 panel."""
        logger.info("Migrating markets")
        
        # Load V1 panel data to extract market information
        panel_file = self.v1_data_path / "balanced_panel.csv"
        if not panel_file.exists():
            logger.warning(f"Panel file not found: {panel_file}")
            return 0
        
        df = pd.read_csv(panel_file)
        
        # Extract unique markets
        market_cols = ['market_id', 'market_name', 'lat', 'lon', 'governorate', 'district']
        markets_df = df[market_cols].drop_duplicates()
        
        markets = []
        for _, row in markets_df.iterrows():
            # Standardize governorate name
            governorate = self.governorate_mappings.get(
                row['governorate'], row['governorate']
            )
            
            # Determine market type based on name/location
            market_type = self._infer_market_type(row['market_name'])
            
            market = Market(
                market_id=MarketId(str(row['market_id'])),
                name=row['market_name'],
                coordinates=Coordinates(
                    latitude=float(row['lat']),
                    longitude=float(row['lon'])
                ),
                market_type=market_type,
                governorate=governorate,
                district=row['district'],
                active_since=datetime(2019, 1, 1),  # From V1 analysis period
                active_until=None  # Still active
            )
            markets.append(market)
        
        # Save markets one by one (for proper event handling)
        for market in markets:
            await uow.markets.save(market)
        
        return len(markets)
    
    async def _migrate_price_observations(self, uow: PostgresUnitOfWork) -> int:
        """Migrate price observations from V1 panel."""
        logger.info("Migrating price observations")
        
        panel_file = self.v1_data_path / "balanced_panel.csv"
        if not panel_file.exists():
            logger.warning(f"Panel file not found: {panel_file}")
            return 0
        
        df = pd.read_csv(panel_file)
        df['date'] = pd.to_datetime(df['date'])
        
        observations = []
        batch_size = 1000
        
        for _, row in df.iterrows():
            # Skip rows with missing price data
            if pd.isna(row.get('price_local')) and pd.isna(row.get('price_usd')):
                continue
            
            # Map V1 commodity to V2
            v1_commodity = row.get('commodity', '').lower().replace(' ', '_')
            commodity_code = self.commodity_mappings.get(v1_commodity)
            if not commodity_code:
                continue
            
            # Get commodity details
            commodity = await uow.commodities.find_by_code(commodity_code)
            if not commodity:
                continue
            
            # Create price (prefer local currency)
            price_amount = row.get('price_local') if not pd.isna(row.get('price_local')) else row.get('price_usd')
            currency = 'YER' if not pd.isna(row.get('price_local')) else 'USD'
            
            if pd.isna(price_amount) or price_amount <= 0:
                continue
            
            price = Price(
                amount=Decimal(str(price_amount)),
                currency=currency,
                unit=commodity.standard_unit
            )
            
            observation = PriceObservation(
                market_id=MarketId(str(row['market_id'])),
                commodity=commodity,
                price=price,
                observed_date=row['date'],
                source='WFP',
                quality='standard',
                observations_count=1
            )
            
            observations.append(observation)
            
            # Batch save for performance
            if len(observations) >= batch_size:
                await uow.prices.save_batch(observations)
                observations = []
        
        # Save remaining observations
        if observations:
            await uow.prices.save_batch(observations)
        
        return len(observations)
    
    async def _migrate_conflict_events(self, uow: PostgresUnitOfWork) -> int:
        """Migrate conflict events from V1 ACLED data."""
        logger.info("Migrating conflict events")
        
        acled_file = self.v1_data_path / "acled_yemen_processed.csv"
        if not acled_file.exists():
            logger.warning(f"ACLED file not found: {acled_file}")
            return 0
        
        df = pd.read_csv(acled_file)
        df['event_date'] = pd.to_datetime(df['event_date'])
        
        events = []
        batch_size = 1000
        
        for _, row in df.iterrows():
            # Skip invalid events
            if pd.isna(row.get('latitude')) or pd.isna(row.get('longitude')):
                continue
            
            # Standardize governorate name
            governorate = self.governorate_mappings.get(
                row['admin1'], row['admin1']
            )
            
            event = ConflictEvent(
                event_id=ConflictEventId(str(row['data_event_id'])),
                event_type=EventType(row['event_type']),
                sub_event_type=row.get('sub_event_type', ''),
                event_date=row['event_date'],
                location=ConflictLocation(
                    latitude=float(row['latitude']),
                    longitude=float(row['longitude']),
                    governorate=governorate,
                    district=row.get('admin2', '')
                ),
                fatality_count=FatalityCount(int(row.get('fatalities', 0))),
                notes=row.get('notes', ''),
                source='ACLED',
                source_scale=row.get('source_scale', '')
            )
            
            events.append(event)
            
            # Batch save for performance
            if len(events) >= batch_size:
                await uow.conflict_events.save_batch(events)
                events = []
        
        # Save remaining events
        if events:
            await uow.conflict_events.save_batch(events)
        
        return len(events)
    
    def _infer_market_type(self, market_name: str) -> MarketType:
        """Infer market type from market name."""
        name_lower = market_name.lower()
        
        if 'border' in name_lower or 'crossing' in name_lower:
            return MarketType.BORDER
        elif 'port' in name_lower or 'harbor' in name_lower:
            return MarketType.PORT
        elif 'wholesale' in name_lower or 'central' in name_lower:
            return MarketType.WHOLESALE
        elif any(word in name_lower for word in ['village', 'rural', 'countryside']):
            return MarketType.RURAL
        elif any(word in name_lower for word in ['city', 'urban', 'municipal']):
            return MarketType.URBAN
        else:
            return MarketType.RETAIL  # Default
    
    async def validate_migration(self) -> Dict[str, bool]:
        """Validate the migration results."""
        logger.info("Validating migration")
        
        validation_results = {}
        
        async with PostgresUnitOfWork(self.v2_connection_string) as uow:
            # Check markets
            markets = await uow.markets.find_all()
            validation_results['markets_exist'] = len(markets) > 0
            
            # Check commodities
            commodities = await uow.commodities.find_all()
            validation_results['commodities_exist'] = len(commodities) >= 10
            
            # Check price observations
            if markets and commodities:
                prices = await uow.prices.find_by_markets(
                    [markets[0].market_id], 
                    datetime(2019, 1, 1),
                    datetime(2019, 2, 1)
                )
                validation_results['prices_exist'] = len(prices) > 0
            
            # Check conflict events
            events = await uow.conflict_events.find_by_date_range(
                datetime(2019, 1, 1),
                datetime(2019, 2, 1)
            )
            validation_results['conflict_events_exist'] = len(events) > 0
        
        logger.info(f"Validation results: {validation_results}")
        return validation_results


async def run_migration(v1_data_path: str, v2_connection_string: str) -> Dict[str, int]:
    """Run the complete V1 to V2 data migration."""
    migrator = V1ToV2DataMigrator(v1_data_path, v2_connection_string)
    
    try:
        # Run migration
        results = await migrator.migrate_all_data()
        
        # Validate results
        validation = await migrator.validate_migration()
        
        if all(validation.values()):
            logger.info("Migration completed successfully")
        else:
            logger.warning(f"Migration validation failed: {validation}")
        
        return results
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise


if __name__ == "__main__":
    import os
    
    # Configuration
    V1_DATA_PATH = os.getenv(
        'V1_DATA_PATH', 
        '/Users/<USER>/Documents/GitHub/yemen-market-integration/data/processed'
    )
    V2_CONNECTION_STRING = os.getenv(
        'V2_DATABASE_URL',
        'postgresql://postgres:password@localhost:5432/yemen_market_v2'
    )
    
    # Run migration
    asyncio.run(run_migration(V1_DATA_PATH, V2_CONNECTION_STRING))