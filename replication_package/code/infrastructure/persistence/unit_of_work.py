"""Unit of Work implementation for transactional consistency."""

from typing import Optional

import asyncpg

from ...application.interfaces import UnitOfWork
from ...core.domain.market.repositories import MarketRepository, PriceRepository, CommodityRepository
from ...core.domain.conflict.repositories import ConflictEventRepository
from .repository_impls.postgres import (
    PostgresMarketRepository, PostgresPriceRepository, 
    PostgresCommodityRepository, PostgresConflictEventRepository
)


class PostgresUnitOfWork(UnitOfWork):
    """PostgreSQL implementation of Unit of Work pattern."""
    
    def __init__(self, connection_string: str):
        """Initialize with connection string."""
        self.connection_string = connection_string
        self._pool: Optional[asyncpg.Pool] = None
        self._connection: Optional[asyncpg.Connection] = None
        self._transaction: Optional[asyncpg.transaction.Transaction] = None
        
        # Repositories
        self._markets: Optional[MarketRepository] = None
        self._prices: Optional[PriceRepository] = None
        self._commodities: Optional[CommodityRepository] = None
        self._conflict_events: Optional[ConflictEventRepository] = None
    
    async def __aenter__(self) -> 'PostgresUnitOfWork':
        """Enter context manager - start transaction."""
        if not self._pool:
            self._pool = await asyncpg.create_pool(self.connection_string)
        
        self._connection = await self._pool.acquire()
        self._transaction = self._connection.transaction()
        await self._transaction.start()
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Exit context manager - commit or rollback."""
        if exc_type:
            await self.rollback()
        else:
            await self.commit()
        
        # Release connection
        if self._connection and self._pool:
            await self._pool.release(self._connection)
            self._connection = None
            self._transaction = None
    
    async def commit(self) -> None:
        """Commit the transaction."""
        if self._transaction:
            await self._transaction.commit()
    
    async def rollback(self) -> None:
        """Rollback the transaction."""
        if self._transaction:
            await self._transaction.rollback()
    
    @property
    def markets(self) -> MarketRepository:
        """Get market repository."""
        if not self._markets:
            if not self._connection:
                raise RuntimeError("Unit of work not started")
            self._markets = PostgresMarketRepository(self._connection)
        return self._markets
    
    @property
    def prices(self) -> PriceRepository:
        """Get price repository."""
        if not self._prices:
            if not self._connection:
                raise RuntimeError("Unit of work not started")
            self._prices = PostgresPriceRepository(self._connection)
        return self._prices
    
    @property
    def commodities(self) -> CommodityRepository:
        """Get commodity repository."""
        if not self._commodities:
            if not self._connection:
                raise RuntimeError("Unit of work not started")
            self._commodities = PostgresCommodityRepository(self._connection)
        return self._commodities
    
    @property
    def conflict_events(self) -> ConflictEventRepository:
        """Get conflict event repository."""
        if not self._conflict_events:
            if not self._connection:
                raise RuntimeError("Unit of work not started")
            self._conflict_events = PostgresConflictEventRepository(self._connection)
        return self._conflict_events
    
    async def close(self) -> None:
        """Close the connection pool."""
        if self._pool:
            await self._pool.close()
            self._pool = None