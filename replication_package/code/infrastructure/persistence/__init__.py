"""Infrastructure persistence layer."""

from .unit_of_work import PostgresUnitOfWork
from .repository_factory import (
    RepositoryFactory, DatabaseConfig, 
    get_repository_factory, initialize_persistence_layer, cleanup_persistence_layer
)
from .bulk_operations import BulkOperationManager, QueryOptimizer, ConnectionPoolManager
from .error_handling import (
    RepositoryException, ConnectionError, DataIntegrityError, ConcurrencyError,
    resilient_operation, CircuitBreaker, RetryStrategy, get_error_collector
)
from .repositories import InMemoryMarketRepository, InMemoryPriceRepository
from .validated_repositories import ValidatedPriceRepository, ValidatedMarketRepository

__all__ = [
    # Core components
    'PostgresUnitOfWork',
    'RepositoryFactory',
    'DatabaseConfig',
    
    # Factory functions
    'get_repository_factory',
    'initialize_persistence_layer', 
    'cleanup_persistence_layer',
    
    # Performance optimization
    'BulkOperationManager',
    'QueryOptimizer',
    'ConnectionPoolManager',
    
    # Error handling
    'RepositoryException',
    'ConnectionError',
    'DataIntegrityError', 
    'ConcurrencyError',
    'resilient_operation',
    'CircuitBreaker',
    'RetryStrategy',
    'get_error_collector',
    
    # In-memory repositories
    'InMemoryMarketRepository',
    'InMemoryPriceRepository',
    
    # Validated repositories
    'ValidatedPriceRepository',
    'ValidatedMarketRepository',
]