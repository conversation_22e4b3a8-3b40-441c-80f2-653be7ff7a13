"""Error handling and resilience for PostgreSQL repositories."""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, Optional, Type, TypeVar, Union
from functools import wraps
from dataclasses import dataclass
from enum import Enum

import asyncpg

logger = logging.getLogger(__name__)

T = TypeVar('T')


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification."""
    CONNECTION = "connection"
    QUERY = "query"
    DATA_INTEGRITY = "data_integrity"
    CONCURRENCY = "concurrency"
    PERFORMANCE = "performance"
    UNKNOWN = "unknown"


@dataclass
class RepositoryError:
    """Repository error details."""
    timestamp: datetime
    error_type: str
    error_message: str
    category: ErrorCategory
    severity: ErrorSeverity
    operation: str
    context: Dict[str, Any]
    retry_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging."""
        return {
            'timestamp': self.timestamp.isoformat(),
            'error_type': self.error_type,
            'error_message': self.error_message,
            'category': self.category.value,
            'severity': self.severity.value,
            'operation': self.operation,
            'context': self.context,
            'retry_count': self.retry_count
        }


class RepositoryException(Exception):
    """Base exception for repository operations."""
    
    def __init__(
        self,
        message: str,
        category: ErrorCategory = ErrorCategory.UNKNOWN,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.context = context or {}
        self.timestamp = datetime.now()


class ConnectionError(RepositoryException):
    """Database connection error."""
    
    def __init__(self, message: str, context: Optional[Dict[str, Any]] = None):
        super().__init__(
            message,
            category=ErrorCategory.CONNECTION,
            severity=ErrorSeverity.HIGH,
            context=context
        )


class DataIntegrityError(RepositoryException):
    """Data integrity violation error."""
    
    def __init__(self, message: str, context: Optional[Dict[str, Any]] = None):
        super().__init__(
            message,
            category=ErrorCategory.DATA_INTEGRITY,
            severity=ErrorSeverity.HIGH,
            context=context
        )


class ConcurrencyError(RepositoryException):
    """Concurrency control error."""
    
    def __init__(self, message: str, context: Optional[Dict[str, Any]] = None):
        super().__init__(
            message,
            category=ErrorCategory.CONCURRENCY,
            severity=ErrorSeverity.MEDIUM,
            context=context
        )


class PerformanceError(RepositoryException):
    """Performance-related error."""
    
    def __init__(self, message: str, context: Optional[Dict[str, Any]] = None):
        super().__init__(
            message,
            category=ErrorCategory.PERFORMANCE,
            severity=ErrorSeverity.LOW,
            context=context
        )


class ErrorClassifier:
    """Classifies database errors into categories."""
    
    ERROR_MAPPINGS = {
        # Connection errors
        asyncpg.ConnectionDoesNotExistError: (ErrorCategory.CONNECTION, ErrorSeverity.HIGH),
        asyncpg.ConnectionFailureError: (ErrorCategory.CONNECTION, ErrorSeverity.HIGH),
        asyncpg.InterfaceError: (ErrorCategory.CONNECTION, ErrorSeverity.HIGH),
        
        # Data integrity errors
        asyncpg.IntegrityConstraintViolationError: (ErrorCategory.DATA_INTEGRITY, ErrorSeverity.HIGH),
        asyncpg.ForeignKeyViolationError: (ErrorCategory.DATA_INTEGRITY, ErrorSeverity.HIGH),
        asyncpg.UniqueViolationError: (ErrorCategory.DATA_INTEGRITY, ErrorSeverity.MEDIUM),
        asyncpg.CheckViolationError: (ErrorCategory.DATA_INTEGRITY, ErrorSeverity.HIGH),
        asyncpg.NotNullViolationError: (ErrorCategory.DATA_INTEGRITY, ErrorSeverity.HIGH),
        
        # Query errors
        asyncpg.PostgresSyntaxError: (ErrorCategory.QUERY, ErrorSeverity.HIGH),
        asyncpg.UndefinedColumnError: (ErrorCategory.QUERY, ErrorSeverity.HIGH),
        asyncpg.UndefinedTableError: (ErrorCategory.QUERY, ErrorSeverity.HIGH),
        
        # Concurrency errors
        asyncpg.DeadlockDetectedError: (ErrorCategory.CONCURRENCY, ErrorSeverity.MEDIUM),
        asyncpg.SerializationError: (ErrorCategory.CONCURRENCY, ErrorSeverity.MEDIUM),
        
        # Performance errors
        asyncio.TimeoutError: (ErrorCategory.PERFORMANCE, ErrorSeverity.LOW),
    }
    
    @classmethod
    def classify_error(
        cls, 
        error: Exception
    ) -> tuple[ErrorCategory, ErrorSeverity]:
        """Classify an error into category and severity."""
        error_type = type(error)
        
        # Direct mapping
        if error_type in cls.ERROR_MAPPINGS:
            return cls.ERROR_MAPPINGS[error_type]
        
        # Check inheritance
        for mapped_type, (category, severity) in cls.ERROR_MAPPINGS.items():
            if isinstance(error, mapped_type):
                return category, severity
        
        # Default classification
        return ErrorCategory.UNKNOWN, ErrorSeverity.MEDIUM


class CircuitBreaker:
    """Circuit breaker pattern for database operations."""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        timeout_seconds: int = 60,
        expected_exception: Type[Exception] = Exception
    ):
        self.failure_threshold = failure_threshold
        self.timeout = timedelta(seconds=timeout_seconds)
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def __call__(self, func: Callable[..., T]) -> Callable[..., T]:
        """Decorator to apply circuit breaker to a function."""
        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            if self.state == "OPEN":
                if self._should_attempt_reset():
                    self.state = "HALF_OPEN"
                else:
                    raise ConnectionError(
                        f"Circuit breaker is OPEN. Last failure: {self.last_failure_time}",
                        context={'failure_count': self.failure_count}
                    )
            
            try:
                result = await func(*args, **kwargs)
                self._on_success()
                return result
            
            except self.expected_exception as e:
                self._on_failure()
                raise
        
        return wrapper
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt to reset."""
        if self.last_failure_time is None:
            return True
        
        return datetime.now() - self.last_failure_time > self.timeout
    
    def _on_success(self):
        """Handle successful operation."""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def _on_failure(self):
        """Handle failed operation."""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"


class RetryStrategy:
    """Retry strategy for transient failures."""
    
    def __init__(
        self,
        max_retries: int = 3,
        initial_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True
    ):
        self.max_retries = max_retries
        self.initial_delay = initial_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate delay for given attempt number."""
        delay = self.initial_delay * (self.exponential_base ** attempt)
        delay = min(delay, self.max_delay)
        
        if self.jitter:
            # Add jitter to prevent thundering herd
            import random
            delay = delay * (0.5 + random.random() * 0.5)
        
        return delay
    
    def should_retry(self, error: Exception, attempt: int) -> bool:
        """Determine if operation should be retried."""
        if attempt >= self.max_retries:
            return False
        
        category, severity = ErrorClassifier.classify_error(error)
        
        # Don't retry critical data integrity errors
        if category == ErrorCategory.DATA_INTEGRITY and severity == ErrorSeverity.HIGH:
            return False
        
        # Retry connection and concurrency errors
        if category in [ErrorCategory.CONNECTION, ErrorCategory.CONCURRENCY]:
            return True
        
        # Retry performance issues
        if category == ErrorCategory.PERFORMANCE:
            return True
        
        return False


def resilient_operation(
    max_retries: int = 3,
    circuit_breaker: Optional[CircuitBreaker] = None,
    retry_strategy: Optional[RetryStrategy] = None
):
    """Decorator for resilient database operations."""
    
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            operation_name = f"{func.__module__}.{func.__qualname__}"
            strategy = retry_strategy or RetryStrategy(max_retries=max_retries)
            
            last_error = None
            
            for attempt in range(max_retries + 1):
                try:
                    # Apply circuit breaker if provided
                    if circuit_breaker:
                        return await circuit_breaker(func)(*args, **kwargs)
                    else:
                        return await func(*args, **kwargs)
                
                except Exception as e:
                    last_error = e
                    category, severity = ErrorClassifier.classify_error(e)
                    
                    # Log error
                    error_details = RepositoryError(
                        timestamp=datetime.now(),
                        error_type=type(e).__name__,
                        error_message=str(e),
                        category=category,
                        severity=severity,
                        operation=operation_name,
                        context={'attempt': attempt, 'args': str(args)[:200]},
                        retry_count=attempt
                    )
                    
                    if attempt < max_retries and strategy.should_retry(e, attempt):
                        delay = strategy.calculate_delay(attempt)
                        logger.warning(
                            f"Operation failed, retrying in {delay:.2f}s: {error_details.to_dict()}"
                        )
                        await asyncio.sleep(delay)
                        continue
                    else:
                        logger.error(f"Operation failed permanently: {error_details.to_dict()}")
                        break
            
            # All retries exhausted, raise the last error
            if last_error:
                raise last_error
        
        return wrapper
    return decorator


class ErrorCollector:
    """Collects and analyzes repository errors for monitoring."""
    
    def __init__(self, max_errors: int = 1000):
        self.max_errors = max_errors
        self.errors: list[RepositoryError] = []
        self.error_counts: Dict[str, int] = {}
    
    def record_error(self, error: RepositoryError):
        """Record an error for analysis."""
        self.errors.append(error)
        
        # Maintain size limit
        if len(self.errors) > self.max_errors:
            self.errors = self.errors[-self.max_errors:]
        
        # Update counts
        error_key = f"{error.category.value}:{error.error_type}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
    
    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get error summary for the last N hours."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_errors = [e for e in self.errors if e.timestamp > cutoff_time]
        
        if not recent_errors:
            return {
                'total_errors': 0,
                'error_rate': 0.0,
                'categories': {},
                'severities': {},
                'top_errors': []
            }
        
        # Count by category
        categories = {}
        for error in recent_errors:
            cat = error.category.value
            categories[cat] = categories.get(cat, 0) + 1
        
        # Count by severity
        severities = {}
        for error in recent_errors:
            sev = error.severity.value
            severities[sev] = severities.get(sev, 0) + 1
        
        # Top error types
        error_types = {}
        for error in recent_errors:
            error_types[error.error_type] = error_types.get(error.error_type, 0) + 1
        
        top_errors = sorted(error_types.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            'total_errors': len(recent_errors),
            'error_rate': len(recent_errors) / hours,
            'categories': categories,
            'severities': severities,
            'top_errors': top_errors
        }
    
    def get_health_score(self) -> float:
        """Calculate health score based on recent errors (0-100)."""
        summary = self.get_error_summary(hours=1)
        
        if summary['total_errors'] == 0:
            return 100.0
        
        # Penalize based on error count and severity
        penalty = 0
        penalty += summary['total_errors'] * 2  # Base penalty per error
        penalty += summary['severities'].get('critical', 0) * 20
        penalty += summary['severities'].get('high', 0) * 10
        penalty += summary['severities'].get('medium', 0) * 5
        penalty += summary['severities'].get('low', 0) * 1
        
        score = max(0, 100 - penalty)
        return float(score)


# Global error collector
error_collector = ErrorCollector()


def get_error_collector() -> ErrorCollector:
    """Get the global error collector instance."""
    return error_collector