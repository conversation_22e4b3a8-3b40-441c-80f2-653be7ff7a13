"""In-memory repository implementations for testing and development."""

from typing import Dict, List, Optional
from datetime import datetime
from collections import defaultdict

from ...core.domain.market.entities import Market, PriceObservation
from ...core.domain.market.repositories import MarketRepository, PriceRepository
from ...core.domain.market.value_objects import MarketId, Commodity


class InMemoryMarketRepository(MarketRepository):
    """In-memory implementation of MarketRepository for testing."""
    
    def __init__(self):
        self._markets: Dict[str, Market] = {}
        
    async def find_by_id(self, market_id: MarketId) -> Optional[Market]:
        """Find market by ID."""
        return self._markets.get(market_id.value)
        
    async def find_by_ids(self, market_ids: List[MarketId]) -> List[Market]:
        """Find markets by list of IDs."""
        market_id_values = [m.value for m in market_ids]
        return [
            market for market_id, market in self._markets.items()
            if market_id in market_id_values
        ]
        
    async def find_all(self) -> List[Market]:
        """Find all markets."""
        return list(self._markets.values())
        
    async def find_by_governorate(self, governorate: str) -> List[Market]:
        """Find markets by governorate."""
        return [
            market for market in self._markets.values()
            if market.governorate == governorate
        ]
        
    async def find_by_market_type(self, market_type: str) -> List[Market]:
        """Find markets by type."""
        return [
            market for market in self._markets.values()
            if market.market_type.value == market_type
        ]
        
    async def save(self, market: Market) -> None:
        """Save market."""
        self._markets[market.market_id.value] = market
        
    async def delete(self, market_id: MarketId) -> None:
        """Delete market."""
        if market_id.value in self._markets:
            del self._markets[market_id.value]
    
    async def find_active_at(self, date: datetime) -> List[Market]:
        """Find all markets active at a given date."""
        return [
            market for market in self._markets.values()
            if market.is_active_at(date)
        ]


class InMemoryPriceRepository(PriceRepository):
    """In-memory implementation of PriceRepository for testing."""
    
    def __init__(self):
        self._observations: List[PriceObservation] = []
        self._commodities: Dict[str, Commodity] = {}
        
    async def find_by_market_and_commodity(
        self,
        market_id: MarketId,
        commodity: Commodity,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[PriceObservation]:
        """Find price observations by market and commodity."""
        results = []
        for obs in self._observations:
            if (obs.market_id.value == market_id.value and
                obs.commodity.code == commodity.code):
                
                # Apply date filters
                if start_date and obs.observed_date < start_date:
                    continue
                if end_date and obs.observed_date > end_date:
                    continue
                    
                results.append(obs)
        
        return sorted(results, key=lambda x: x.observed_date)
        
    async def find_by_date_range(
        self,
        start_date: datetime,
        end_date: datetime,
        market_ids: Optional[List[MarketId]] = None,
        commodity_codes: Optional[List[str]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[PriceObservation]:
        """Find price observations by date range."""
        results = []
        market_id_values = [m.value for m in market_ids] if market_ids else None
        
        for obs in self._observations:
            # Date filter
            if not (start_date <= obs.observed_date <= end_date):
                continue
                
            # Market filter
            if market_id_values and obs.market_id.value not in market_id_values:
                continue
                
            # Commodity filter
            if commodity_codes and obs.commodity.code not in commodity_codes:
                continue
                
            results.append(obs)
            
        sorted_results = sorted(results, key=lambda x: (x.market_id.value, x.observed_date))
        
        # Apply limit and offset
        if offset:
            sorted_results = sorted_results[offset:]
        if limit:
            sorted_results = sorted_results[:limit]
            
        return sorted_results
        
    async def find_commodity_by_code(self, code: str) -> Optional[Commodity]:
        """Find commodity by code."""
        return self._commodities.get(code)
        
    async def get_distinct_commodities(self) -> List[Commodity]:
        """Get all distinct commodities."""
        commodity_codes = set()
        for obs in self._observations:
            commodity_codes.add(obs.commodity.code)
            self._commodities[obs.commodity.code] = obs.commodity
            
        return list(self._commodities.values())
        
    async def get_distinct_markets(self) -> List[MarketId]:
        """Get all distinct market IDs."""
        market_ids = set()
        for obs in self._observations:
            market_ids.add(obs.market_id.value)
            
        return [MarketId(value=mid) for mid in market_ids]
        
    async def save(self, observation: PriceObservation) -> None:
        """Save price observation."""
        self._observations.append(observation)
        self._commodities[observation.commodity.code] = observation.commodity
        
    async def save_batch(self, observations: List[PriceObservation]) -> None:
        """Save multiple price observations."""
        for obs in observations:
            await self.save(obs)
            
    async def delete_by_market_and_date(
        self,
        market_id: MarketId,
        date: datetime
    ) -> None:
        """Delete observations by market and date."""
        self._observations = [
            obs for obs in self._observations
            if not (obs.market_id.value == market_id.value and
                   obs.observed_date.date() == date.date())
        ]
    
    async def delete_by_date_range(
        self,
        market_id: MarketId,
        start_date: datetime,
        end_date: datetime
    ) -> int:
        """Delete observations within date range. Returns count of deleted records."""
        initial_count = len(self._observations)
        self._observations = [
            obs for obs in self._observations
            if not (obs.market_id.value == market_id.value and
                   start_date <= obs.observed_date <= end_date)
        ]
        return initial_count - len(self._observations)
    
    async def find_by_markets_and_commodity(
        self,
        market_ids: List[MarketId],
        commodity: Commodity,
        start_date: datetime,
        end_date: datetime
    ) -> List[PriceObservation]:
        """Find price observations for multiple markets and a commodity."""
        market_id_values = [m.value for m in market_ids]
        results = []
        for obs in self._observations:
            if (obs.market_id.value in market_id_values and
                obs.commodity.code == commodity.code and
                start_date <= obs.observed_date <= end_date):
                results.append(obs)
        return sorted(results, key=lambda x: (x.market_id.value, x.observed_date))
    
    async def find_by_markets(
        self,
        market_ids: List[MarketId],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[PriceObservation]:
        """Find price observations for multiple markets."""
        market_id_values = [m.value for m in market_ids]
        results = []
        for obs in self._observations:
            if obs.market_id.value in market_id_values:
                if start_date and obs.observed_date < start_date:
                    continue
                if end_date and obs.observed_date > end_date:
                    continue
                results.append(obs)
        return sorted(results, key=lambda x: (x.market_id.value, x.observed_date))
    
    async def find_by_commodities(
        self,
        commodities: List[Commodity],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[PriceObservation]:
        """Find price observations for multiple commodities."""
        commodity_codes = [c.code for c in commodities]
        results = []
        for obs in self._observations:
            if obs.commodity.code in commodity_codes:
                if start_date and obs.observed_date < start_date:
                    continue
                if end_date and obs.observed_date > end_date:
                    continue
                results.append(obs)
        return sorted(results, key=lambda x: (x.commodity.code, x.observed_date))