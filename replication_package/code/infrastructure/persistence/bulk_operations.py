"""Bulk operations and performance optimizations for PostgreSQL repositories."""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable, TypeVar
from dataclasses import dataclass
from contextlib import asynccontextmanager

import asyncpg

from ...core.domain.market.entities import Market, PriceObservation
from ...core.domain.conflict.entities import ConflictEvent
from ...core.domain.market.value_objects import Commodity

logger = logging.getLogger(__name__)

T = TypeVar('T')


@dataclass
class BulkOperationStats:
    """Statistics for bulk operations."""
    operation: str
    total_records: int
    processed_records: int
    failed_records: int
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage."""
        if self.total_records == 0:
            return 100.0
        return (self.processed_records / self.total_records) * 100
    
    def mark_completed(self):
        """Mark operation as completed and calculate duration."""
        self.end_time = datetime.now()
        if self.start_time:
            self.duration_seconds = (self.end_time - self.start_time).total_seconds()


class BulkOperationManager:
    """Manager for efficient bulk database operations."""
    
    def __init__(self, connection: asyncpg.Connection, batch_size: int = 1000):
        """Initialize bulk operation manager.
        
        Args:
            connection: Database connection
            batch_size: Number of records to process in each batch
        """
        self.connection = connection
        self.batch_size = batch_size
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def bulk_insert_markets(
        self, 
        markets: List[Market],
        on_conflict: str = 'update'
    ) -> BulkOperationStats:
        """Bulk insert markets with conflict resolution.
        
        Args:
            markets: List of market entities
            on_conflict: How to handle conflicts ('update', 'ignore', 'error')
        """
        stats = BulkOperationStats(
            operation="bulk_insert_markets",
            total_records=len(markets),
            processed_records=0,
            failed_records=0,
            start_time=datetime.now()
        )
        
        try:
            # Prepare data for COPY
            records = [
                (
                    market.id or str(market.market_id.value),  # Use market_id as fallback
                    market.market_id.value,
                    market.name,
                    market.coordinates.latitude,
                    market.coordinates.longitude,
                    market.market_type.value,
                    market.governorate,
                    market.district,
                    market.active_since,
                    market.active_until,
                    market.version or 0
                )
                for market in markets
            ]
            
            if on_conflict == 'update':
                # Use INSERT ... ON CONFLICT for upsert
                await self._batch_upsert(
                    table='markets',
                    records=records,
                    columns=[
                        'id', 'market_id', 'name', 'latitude', 'longitude',
                        'market_type', 'governorate', 'district', 'active_since',
                        'active_until', 'version'
                    ],
                    conflict_columns=['market_id'],
                    update_columns=[
                        'name', 'latitude', 'longitude', 'market_type',
                        'governorate', 'district', 'active_until', 'version'
                    ]
                )
            elif on_conflict == 'ignore':
                # Use INSERT ... ON CONFLICT DO NOTHING
                await self._batch_insert_ignore(
                    table='markets',
                    records=records,
                    columns=[
                        'id', 'market_id', 'name', 'latitude', 'longitude',
                        'market_type', 'governorate', 'district', 'active_since',
                        'active_until', 'version'
                    ],
                    conflict_columns=['market_id']
                )
            else:
                # Direct COPY (will fail on conflicts)
                await self.connection.copy_records_to_table(
                    'markets',
                    records=records,
                    columns=[
                        'id', 'market_id', 'name', 'latitude', 'longitude',
                        'market_type', 'governorate', 'district', 'active_since',
                        'active_until', 'version'
                    ]
                )
            
            stats.processed_records = len(markets)
            self.logger.info(f"Successfully inserted {len(markets)} markets")
            
        except Exception as e:
            stats.failed_records = len(markets)
            stats.errors.append(str(e))
            self.logger.error(f"Failed to bulk insert markets: {e}")
            raise
        
        finally:
            stats.mark_completed()
        
        return stats
    
    async def bulk_insert_price_observations(
        self,
        observations: List[PriceObservation],
        on_conflict: str = 'update'
    ) -> BulkOperationStats:
        """Bulk insert price observations."""
        stats = BulkOperationStats(
            operation="bulk_insert_price_observations",
            total_records=len(observations),
            processed_records=0,
            failed_records=0,
            start_time=datetime.now()
        )
        
        try:
            # Process in batches to manage memory
            for i in range(0, len(observations), self.batch_size):
                batch = observations[i:i + self.batch_size]
                
                records = [
                    (
                        obs.id or asyncpg.UUID(bytes=obs.market_id.value.encode()[:16].ljust(16, b'\0')),
                        obs.market_id.value,
                        obs.commodity.code,
                        obs.price.amount,
                        obs.price.currency,
                        obs.price.unit,
                        obs.observed_date,
                        obs.source,
                        obs.quality,
                        obs.observations_count
                    )
                    for obs in batch
                ]
                
                if on_conflict == 'update':
                    await self._batch_upsert(
                        table='price_observations',
                        records=records,
                        columns=[
                            'id', 'market_id', 'commodity_code', 'price_amount',
                            'price_currency', 'price_unit', 'observed_date',
                            'source', 'quality', 'observations_count'
                        ],
                        conflict_columns=['market_id', 'commodity_code', 'observed_date', 'source'],
                        update_columns=[
                            'price_amount', 'price_currency', 'price_unit',
                            'quality', 'observations_count'
                        ]
                    )
                else:
                    await self.connection.copy_records_to_table(
                        'price_observations',
                        records=records,
                        columns=[
                            'id', 'market_id', 'commodity_code', 'price_amount',
                            'price_currency', 'price_unit', 'observed_date',
                            'source', 'quality', 'observations_count'
                        ]
                    )
                
                stats.processed_records += len(batch)
                
                if i % (self.batch_size * 10) == 0:  # Log every 10 batches
                    self.logger.info(f"Processed {stats.processed_records}/{len(observations)} price observations")
        
        except Exception as e:
            stats.failed_records = len(observations) - stats.processed_records
            stats.errors.append(str(e))
            self.logger.error(f"Failed to bulk insert price observations: {e}")
            raise
        
        finally:
            stats.mark_completed()
        
        return stats
    
    async def bulk_insert_conflict_events(
        self,
        events: List[ConflictEvent],
        on_conflict: str = 'update'
    ) -> BulkOperationStats:
        """Bulk insert conflict events."""
        stats = BulkOperationStats(
            operation="bulk_insert_conflict_events",
            total_records=len(events),
            processed_records=0,
            failed_records=0,
            start_time=datetime.now()
        )
        
        try:
            records = [
                (
                    event.id or asyncpg.UUID(bytes=event.event_id.value.encode()[:16].ljust(16, b'\0')),
                    event.event_id.value,
                    event.event_type.value,
                    event.sub_event_type,
                    event.event_date,
                    event.location.latitude,
                    event.location.longitude,
                    event.location.governorate,
                    event.location.district,
                    event.fatality_count.value,
                    event.notes,
                    event.source,
                    event.source_scale
                )
                for event in events
            ]
            
            if on_conflict == 'update':
                await self._batch_upsert(
                    table='conflict_events',
                    records=records,
                    columns=[
                        'id', 'event_id', 'event_type', 'sub_event_type',
                        'event_date', 'latitude', 'longitude', 'governorate',
                        'district', 'fatalities', 'notes', 'source', 'source_scale'
                    ],
                    conflict_columns=['event_id'],
                    update_columns=[
                        'event_type', 'sub_event_type', 'fatalities', 'notes'
                    ]
                )
            else:
                await self.connection.copy_records_to_table(
                    'conflict_events',
                    records=records,
                    columns=[
                        'id', 'event_id', 'event_type', 'sub_event_type',
                        'event_date', 'latitude', 'longitude', 'governorate',
                        'district', 'fatalities', 'notes', 'source', 'source_scale'
                    ]
                )
            
            stats.processed_records = len(events)
            self.logger.info(f"Successfully inserted {len(events)} conflict events")
        
        except Exception as e:
            stats.failed_records = len(events)
            stats.errors.append(str(e))
            self.logger.error(f"Failed to bulk insert conflict events: {e}")
            raise
        
        finally:
            stats.mark_completed()
        
        return stats
    
    async def _batch_upsert(
        self,
        table: str,
        records: List[tuple],
        columns: List[str],
        conflict_columns: List[str],
        update_columns: List[str]
    ):
        """Perform batch upsert operation."""
        if not records:
            return
        
        # Create temporary table
        temp_table = f"temp_{table}_{asyncio.current_task().get_name()}"
        
        try:
            # Create temporary table with same structure
            await self.connection.execute(f"""
                CREATE TEMP TABLE {temp_table} 
                (LIKE {table} INCLUDING DEFAULTS)
            """)
            
            # Copy data to temporary table
            await self.connection.copy_records_to_table(
                temp_table,
                records=records,
                columns=columns
            )
            
            # Perform upsert from temporary table
            conflict_clause = ", ".join(conflict_columns)
            update_clause = ", ".join([f"{col} = EXCLUDED.{col}" for col in update_columns])
            
            await self.connection.execute(f"""
                INSERT INTO {table} 
                SELECT * FROM {temp_table}
                ON CONFLICT ({conflict_clause}) 
                DO UPDATE SET {update_clause}
            """)
        
        finally:
            # Clean up temporary table
            await self.connection.execute(f"DROP TABLE IF EXISTS {temp_table}")
    
    async def _batch_insert_ignore(
        self,
        table: str,
        records: List[tuple],
        columns: List[str],
        conflict_columns: List[str]
    ):
        """Perform batch insert with conflict ignore."""
        if not records:
            return
        
        # Create temporary table
        temp_table = f"temp_{table}_{asyncio.current_task().get_name()}"
        
        try:
            # Create temporary table
            await self.connection.execute(f"""
                CREATE TEMP TABLE {temp_table} 
                (LIKE {table} INCLUDING DEFAULTS)
            """)
            
            # Copy data to temporary table
            await self.connection.copy_records_to_table(
                temp_table,
                records=records,
                columns=columns
            )
            
            # Perform insert ignore from temporary table
            conflict_clause = ", ".join(conflict_columns)
            
            await self.connection.execute(f"""
                INSERT INTO {table} 
                SELECT * FROM {temp_table}
                ON CONFLICT ({conflict_clause}) DO NOTHING
            """)
        
        finally:
            # Clean up temporary table
            await self.connection.execute(f"DROP TABLE IF EXISTS {temp_table}")


class QueryOptimizer:
    """Query optimization utilities."""
    
    def __init__(self, connection: asyncpg.Connection):
        self.connection = connection
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def analyze_table_statistics(self, table_name: str) -> Dict[str, Any]:
        """Analyze table statistics for query optimization."""
        stats = {}
        
        # Basic table stats
        row = await self.connection.fetchrow(f"""
            SELECT 
                schemaname,
                tablename,
                attname,
                n_distinct,
                correlation
            FROM pg_stats 
            WHERE tablename = $1
            LIMIT 1
        """, table_name)
        
        if row:
            stats['schema'] = row['schemaname']
            stats['table'] = row['tablename']
        
        # Table size
        row = await self.connection.fetchrow(f"""
            SELECT 
                pg_size_pretty(pg_total_relation_size($1)) as total_size,
                pg_size_pretty(pg_relation_size($1)) as table_size,
                (SELECT reltuples::BIGINT FROM pg_class WHERE relname = $1) as estimated_rows
        """, table_name)
        
        if row:
            stats.update({
                'total_size': row['total_size'],
                'table_size': row['table_size'],
                'estimated_rows': row['estimated_rows']
            })
        
        # Index usage
        rows = await self.connection.fetch(f"""
            SELECT 
                indexname,
                idx_scan,
                idx_tup_read,
                idx_tup_fetch
            FROM pg_stat_user_indexes 
            WHERE relname = $1
        """, table_name)
        
        stats['indexes'] = [dict(row) for row in rows]
        
        return stats
    
    async def suggest_indexes(self, table_name: str) -> List[str]:
        """Suggest indexes based on query patterns."""
        suggestions = []
        
        # Analyze slow queries (if pg_stat_statements is available)
        try:
            rows = await self.connection.fetch("""
                SELECT query, calls, mean_time 
                FROM pg_stat_statements 
                WHERE query LIKE $1
                ORDER BY mean_time DESC
                LIMIT 10
            """, f"%{table_name}%")
            
            for row in rows:
                if row['mean_time'] > 100:  # Slow queries > 100ms
                    suggestions.append(f"Consider optimizing: {row['query'][:100]}...")
        
        except asyncpg.UndefinedTableError:
            # pg_stat_statements not available
            pass
        
        return suggestions
    
    @asynccontextmanager
    async def query_performance_monitor(self, query_name: str):
        """Context manager to monitor query performance."""
        start_time = datetime.now()
        
        try:
            yield
        finally:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            if duration > 1.0:  # Log slow queries
                self.logger.warning(f"Slow query '{query_name}': {duration:.2f}s")
            else:
                self.logger.debug(f"Query '{query_name}': {duration:.3f}s")


class ConnectionPoolManager:
    """Manages connection pool for high-performance operations."""
    
    def __init__(self, connection_string: str, min_size: int = 10, max_size: int = 20):
        self.connection_string = connection_string
        self.min_size = min_size
        self.max_size = max_size
        self._pool: Optional[asyncpg.Pool] = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def initialize(self):
        """Initialize connection pool."""
        self._pool = await asyncpg.create_pool(
            self.connection_string,
            min_size=self.min_size,
            max_size=self.max_size,
            command_timeout=60
        )
        self.logger.info(f"Connection pool initialized with {self.min_size}-{self.max_size} connections")
    
    async def close(self):
        """Close connection pool."""
        if self._pool:
            await self._pool.close()
            self._pool = None
    
    @asynccontextmanager
    async def get_connection(self):
        """Get connection from pool."""
        if not self._pool:
            await self.initialize()
        
        async with self._pool.acquire() as connection:
            yield connection
    
    async def execute_parallel(
        self,
        operations: List[Callable],
        max_concurrent: int = 5
    ) -> List[Any]:
        """Execute operations in parallel using connection pool."""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def run_operation(operation):
            async with semaphore:
                async with self.get_connection() as conn:
                    return await operation(conn)
        
        tasks = [run_operation(op) for op in operations]
        return await asyncio.gather(*tasks, return_exceptions=True)