"""
Multi-Source Exchange Rate Collection System V2

This version implements actual data collection patterns for Yemen exchange rates.
It demonstrates how to integrate with real data sources while maintaining
the robustness required for research-grade data collection.

Data Sources Implementation:
1. Central Bank APIs (when available)
2. Financial data providers (XE, OANDA, etc.)
3. NGO/humanitarian data feeds
4. Web scraping with proper error handling
5. CSV/Excel file imports from partner organizations
"""

import asyncio
import aiohttp
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Union
import pandas as pd
import numpy as np
from decimal import Decimal
import json
import re
from bs4 import BeautifulSoup
from pathlib import Path
import logging

from ...core.domain.market.currency_zones import CurrencyZone, ZoneExchangeRate
from ...core.domain.market.value_objects import Currency

logger = logging.getLogger(__name__)


@dataclass
class ExchangeRateObservation:
    """Single exchange rate observation from a source."""
    source: str
    zone: CurrencyZone
    from_currency: Currency
    to_currency: Currency
    rate: Decimal
    date: datetime
    rate_type: str  # 'official', 'parallel', 'bank'
    confidence: float  # 0-1 confidence score
    metadata: Dict[str, any] = field(default_factory=dict)
    raw_data: Optional[str] = None  # Store raw response for audit


class RealDataSource(ABC):
    """Base class for real exchange rate data sources."""
    
    def __init__(self, config: Dict[str, any] = None):
        """Initialize with configuration."""
        self.config = config or {}
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limit_delay = self.config.get('rate_limit_delay', 1.0)
        self.max_retries = self.config.get('max_retries', 3)
        self.timeout = self.config.get('timeout', 30)
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    @abstractmethod
    async def fetch_rates(self, date: datetime) -> List[ExchangeRateObservation]:
        """Fetch exchange rates for a given date."""
        pass
    
    @abstractmethod
    def get_source_name(self) -> str:
        """Get the name of this source."""
        pass
    
    @abstractmethod
    def get_reliability_score(self) -> float:
        """Get reliability score for this source (0-1)."""
        pass
    
    async def _make_request(self, url: str, headers: Dict = None, params: Dict = None) -> Dict:
        """Make HTTP request with retry logic."""
        headers = headers or {}
        params = params or {}
        
        for attempt in range(self.max_retries):
            try:
                async with self.session.get(url, headers=headers, params=params) as response:
                    response.raise_for_status()
                    return await response.json()
            except aiohttp.ClientError as e:
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.rate_limit_delay * (attempt + 1))
                else:
                    raise
        
        return {}


class XEDataProvider(RealDataSource):
    """Integration with XE.com API for international exchange rates."""
    
    def __init__(self, api_key: str, config: Dict[str, any] = None):
        super().__init__(config)
        self.api_key = api_key
        self.base_url = "https://xecdapi.xe.com/v1"
        self.reliability = 0.95  # High reliability for major currencies
    
    async def fetch_rates(self, date: datetime) -> List[ExchangeRateObservation]:
        """Fetch USD/YER rates from XE API."""
        logger.info(f"Fetching rates from XE.com for {date}")
        
        rates = []
        
        # XE API endpoint for historical rates
        url = f"{self.base_url}/historic_rate.json"
        headers = {
            "Authorization": f"Basic {self.api_key}",
            "Content-Type": "application/json"
        }
        params = {
            "from": "USD",
            "to": "YER",
            "date": date.strftime("%Y-%m-%d")
        }
        
        try:
            data = await self._make_request(url, headers, params)
            
            if data and 'rates' in data:
                rate_value = Decimal(str(data['rates'][0]['rate']))
                
                # XE provides international rates, which typically reflect government zone rates
                rates.append(ExchangeRateObservation(
                    source=self.get_source_name(),
                    zone=CurrencyZone.GOVERNMENT,
                    from_currency=Currency.USD,
                    to_currency=Currency.YER,
                    rate=rate_value,
                    date=date,
                    rate_type="international",
                    confidence=0.90,
                    metadata={"provider": "XE", "rate_pair": "USD/YER"},
                    raw_data=json.dumps(data)
                ))
                
        except Exception as e:
            logger.error(f"Failed to fetch XE rates: {e}")
        
        return rates
    
    def get_source_name(self) -> str:
        return "XE_International"
    
    def get_reliability_score(self) -> float:
        return self.reliability


class OandaDataProvider(RealDataSource):
    """Integration with OANDA forex data."""
    
    def __init__(self, api_key: str, account_id: str, config: Dict[str, any] = None):
        super().__init__(config)
        self.api_key = api_key
        self.account_id = account_id
        self.base_url = "https://api-fxtrade.oanda.com/v3"
        self.reliability = 0.93
    
    async def fetch_rates(self, date: datetime) -> List[ExchangeRateObservation]:
        """Fetch forex rates from OANDA."""
        logger.info(f"Fetching rates from OANDA for {date}")
        
        rates = []
        
        # OANDA candles endpoint for historical data
        instrument = "USD_YER"  # Note: YER might not be available
        url = f"{self.base_url}/instruments/{instrument}/candles"
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Accept-Datetime-Format": "RFC3339"
        }
        
        params = {
            "price": "M",  # Midpoint prices
            "granularity": "D",  # Daily
            "count": 1,
            "from": date.strftime("%Y-%m-%dT00:00:00Z"),
            "to": (date + timedelta(days=1)).strftime("%Y-%m-%dT00:00:00Z")
        }
        
        try:
            data = await self._make_request(url, headers, params)
            
            if data and 'candles' in data and len(data['candles']) > 0:
                candle = data['candles'][0]
                mid_price = candle['mid']['c']  # Close price
                
                rates.append(ExchangeRateObservation(
                    source=self.get_source_name(),
                    zone=CurrencyZone.GOVERNMENT,
                    from_currency=Currency.USD,
                    to_currency=Currency.YER,
                    rate=Decimal(str(mid_price)),
                    date=date,
                    rate_type="forex_mid",
                    confidence=0.88,
                    metadata={"provider": "OANDA", "granularity": "daily"},
                    raw_data=json.dumps(data)
                ))
                
        except Exception as e:
            logger.error(f"Failed to fetch OANDA rates: {e}")
        
        return rates
    
    def get_source_name(self) -> str:
        return "OANDA_Forex"
    
    def get_reliability_score(self) -> float:
        return self.reliability


class WFPDataIngestion(RealDataSource):
    """Ingest exchange rate data from WFP reports and datasets."""
    
    def __init__(self, data_directory: str, config: Dict[str, any] = None):
        super().__init__(config)
        self.data_directory = Path(data_directory)
        self.reliability = 0.85
        self.wfp_api_base = "https://dataviz.vam.wfp.org/api"  # WFP data API
    
    async def fetch_rates(self, date: datetime) -> List[ExchangeRateObservation]:
        """Fetch rates from WFP market monitoring data."""
        logger.info(f"Fetching rates from WFP data for {date}")
        
        rates = []
        
        # Try API first
        rates.extend(await self._fetch_from_api(date))
        
        # Also check local CSV/Excel files
        rates.extend(self._fetch_from_files(date))
        
        return rates
    
    async def _fetch_from_api(self, date: datetime) -> List[ExchangeRateObservation]:
        """Fetch from WFP DataViz API."""
        rates = []
        
        # WFP market prices endpoint
        url = f"{self.wfp_api_base}/MarketPrices"
        params = {
            "CountryCode": "YE",  # Yemen
            "Year": date.year,
            "Month": date.month
        }
        
        try:
            data = await self._make_request(url, params=params)
            
            if data and 'MarketPrices' in data:
                # Extract exchange rate data from market price metadata
                for market_data in data['MarketPrices']:
                    if 'ExchangeRate' in market_data:
                        rate_info = market_data['ExchangeRate']
                        
                        # Determine zone from market location
                        market_name = market_data.get('MarketName', '').lower()
                        if any(city in market_name for city in ['sanaa', 'sadah', 'hajjah']):
                            zone = CurrencyZone.HOUTHI
                        elif any(city in market_name for city in ['aden', 'marib', 'mukalla']):
                            zone = CurrencyZone.GOVERNMENT
                        else:
                            zone = CurrencyZone.CONTESTED
                        
                        rates.append(ExchangeRateObservation(
                            source=self.get_source_name(),
                            zone=zone,
                            from_currency=Currency.USD,
                            to_currency=Currency.YER,
                            rate=Decimal(str(rate_info['Rate'])),
                            date=datetime.strptime(market_data['Date'], '%Y-%m-%d'),
                            rate_type="market_survey",
                            confidence=0.82,
                            metadata={
                                "market": market_data.get('MarketName'),
                                "survey_id": market_data.get('SurveyID')
                            }
                        ))
                        
        except Exception as e:
            logger.error(f"Failed to fetch WFP API data: {e}")
        
        return rates
    
    def _fetch_from_files(self, date: datetime) -> List[ExchangeRateObservation]:
        """Parse local WFP data files."""
        rates = []
        
        # Look for monthly market monitoring reports
        month_str = date.strftime('%Y%m')
        pattern = f"*{month_str}*.{{'csv', 'xlsx', 'xls'}}"
        
        for file_path in self.data_directory.glob(pattern):
            try:
                if file_path.suffix == '.csv':
                    df = pd.read_csv(file_path)
                else:
                    df = pd.read_excel(file_path)
                
                # Look for exchange rate columns
                rate_columns = [col for col in df.columns if 'exchange' in col.lower() or 'rate' in col.lower()]
                
                if rate_columns:
                    # Extract rates from the dataframe
                    # This is simplified - real implementation would need careful parsing
                    for _, row in df.iterrows():
                        if pd.notna(row[rate_columns[0]]):
                            rates.append(ExchangeRateObservation(
                                source=f"{self.get_source_name()}_File",
                                zone=CurrencyZone.UNKNOWN,  # Would need location mapping
                                from_currency=Currency.USD,
                                to_currency=Currency.YER,
                                rate=Decimal(str(row[rate_columns[0]])),
                                date=date,
                                rate_type="file_import",
                                confidence=0.75,
                                metadata={"file": file_path.name}
                            ))
                            
            except Exception as e:
                logger.error(f"Failed to parse file {file_path}: {e}")
        
        return rates
    
    def get_source_name(self) -> str:
        return "WFP_MarketMonitoring"
    
    def get_reliability_score(self) -> float:
        return self.reliability


class CentralBankWebScraper(RealDataSource):
    """Web scraper for central bank websites."""
    
    def __init__(self, bank_config: Dict[str, any], config: Dict[str, any] = None):
        super().__init__(config)
        self.bank_config = bank_config
        self.reliability = 0.90 if bank_config['bank'] == 'CBY_Aden' else 0.85
    
    async def fetch_rates(self, date: datetime) -> List[ExchangeRateObservation]:
        """Scrape rates from central bank website."""
        logger.info(f"Scraping {self.bank_config['bank']} for {date}")
        
        rates = []
        
        try:
            # Fetch the webpage
            async with self.session.get(self.bank_config['url']) as response:
                html = await response.text()
            
            # Parse HTML
            soup = BeautifulSoup(html, 'html.parser')
            
            # Look for rate tables - this is site-specific
            rate_tables = soup.find_all('table', class_=self.bank_config.get('table_class', 'rates'))
            
            for table in rate_tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all('td')
                    if len(cells) >= 3:  # Currency, Buy, Sell
                        currency = cells[0].text.strip()
                        if 'USD' in currency:
                            # Extract rate - handling various formats
                            rate_text = cells[2].text.strip()  # Sell rate
                            rate_match = re.search(r'[\d,]+\.?\d*', rate_text)
                            
                            if rate_match:
                                rate_value = Decimal(rate_match.group().replace(',', ''))
                                
                                zone = CurrencyZone.GOVERNMENT if self.bank_config['bank'] == 'CBY_Aden' else CurrencyZone.HOUTHI
                                
                                rates.append(ExchangeRateObservation(
                                    source=self.get_source_name(),
                                    zone=zone,
                                    from_currency=Currency.USD,
                                    to_currency=Currency.YER,
                                    rate=rate_value,
                                    date=date,
                                    rate_type="official_bank",
                                    confidence=0.88,
                                    metadata={
                                        "bank": self.bank_config['bank'],
                                        "rate_type": "sell"
                                    },
                                    raw_data=str(row)
                                ))
                                
        except Exception as e:
            logger.error(f"Failed to scrape {self.bank_config['bank']}: {e}")
        
        return rates
    
    def get_source_name(self) -> str:
        return f"{self.bank_config['bank']}_Scraper"
    
    def get_reliability_score(self) -> float:
        return self.reliability


class TelegramChannelMonitor(RealDataSource):
    """Monitor Telegram channels for real-time exchange rates."""
    
    def __init__(self, api_id: str, api_hash: str, channels: List[str], config: Dict[str, any] = None):
        super().__init__(config)
        self.api_id = api_id
        self.api_hash = api_hash
        self.channels = channels
        self.reliability = 0.65  # Lower reliability but real-time
    
    async def fetch_rates(self, date: datetime) -> List[ExchangeRateObservation]:
        """Fetch rates mentioned in Telegram channels."""
        # This would use telethon or pyrogram to connect to Telegram
        # and parse messages for exchange rate mentions
        
        # For now, return empty list as this requires Telegram API setup
        logger.info(f"Telegram monitoring not implemented - requires API credentials")
        return []
    
    def get_source_name(self) -> str:
        return "Telegram_Monitor"
    
    def get_reliability_score(self) -> float:
        return self.reliability


class ExchangeRateCollectorV2:
    """Enhanced exchange rate collector with real data source integration."""
    
    def __init__(self, config: Dict[str, any]):
        """Initialize with configuration for all data sources."""
        self.config = config
        self.sources = self._initialize_sources()
        self.cache_directory = Path(config.get('cache_dir', './exchange_rate_cache'))
        self.cache_directory.mkdir(exist_ok=True)
        
        # Import validation and imputation engines from V1
        from .exchange_rate_collector import RateValidationEngine, RateImputationEngine
        self.validation_engine = RateValidationEngine()
        self.imputation_engine = RateImputationEngine()
    
    def _initialize_sources(self) -> List[RealDataSource]:
        """Initialize all configured data sources."""
        sources = []
        
        # XE.com integration
        if 'xe_api_key' in self.config:
            sources.append(XEDataProvider(
                api_key=self.config['xe_api_key'],
                config=self.config.get('xe_config', {})
            ))
        
        # OANDA integration
        if 'oanda_api_key' in self.config:
            sources.append(OandaDataProvider(
                api_key=self.config['oanda_api_key'],
                account_id=self.config['oanda_account_id'],
                config=self.config.get('oanda_config', {})
            ))
        
        # WFP data ingestion
        if 'wfp_data_directory' in self.config:
            sources.append(WFPDataIngestion(
                data_directory=self.config['wfp_data_directory'],
                config=self.config.get('wfp_config', {})
            ))
        
        # Central bank scrapers
        if 'cby_aden_url' in self.config:
            sources.append(CentralBankWebScraper(
                bank_config={
                    'bank': 'CBY_Aden',
                    'url': self.config['cby_aden_url'],
                    'table_class': 'exchange-rates'
                }
            ))
        
        if 'cby_sanaa_url' in self.config:
            sources.append(CentralBankWebScraper(
                bank_config={
                    'bank': 'CBY_Sanaa',
                    'url': self.config['cby_sanaa_url'],
                    'table_class': 'rates-table'
                }
            ))
        
        # Telegram monitoring (if configured)
        if 'telegram_api_id' in self.config:
            sources.append(TelegramChannelMonitor(
                api_id=self.config['telegram_api_id'],
                api_hash=self.config['telegram_api_hash'],
                channels=self.config.get('telegram_channels', [])
            ))
        
        return sources
    
    async def collect_daily_rates(self, date: Optional[datetime] = None) -> List[ZoneExchangeRate]:
        """Collect rates from all sources for a given date."""
        if date is None:
            date = datetime.now()
        
        logger.info(f"Collecting exchange rates for {date} from {len(self.sources)} sources")
        
        # Check cache first
        cached_rates = self._load_from_cache(date)
        if cached_rates:
            logger.info(f"Using cached rates for {date}")
            return cached_rates
        
        all_observations = []
        
        # Fetch from all sources concurrently
        for source in self.sources:
            try:
                async with source:
                    observations = await source.fetch_rates(date)
                    all_observations.extend(observations)
                    logger.info(f"Collected {len(observations)} rates from {source.get_source_name()}")
            except Exception as e:
                logger.error(f"Failed to collect from {source.get_source_name()}: {e}")
        
        # Validate observations
        validated_rates = []
        for obs in all_observations:
            validation = self.validation_engine.validate_rate(obs)
            
            if validation.is_valid:
                # Convert to ZoneExchangeRate
                zone_rate = ZoneExchangeRate(
                    zone=obs.zone,
                    from_currency=obs.from_currency,
                    to_currency=obs.to_currency,
                    rate=validation.adjusted_rate or obs.rate,
                    date=obs.date,
                    rate_type=obs.rate_type,
                    source=obs.source,
                    confidence=obs.confidence + validation.confidence_adjustment
                )
                validated_rates.append(zone_rate)
            else:
                logger.warning(f"Invalid rate from {obs.source}: {validation.issues}")
        
        # Aggregate rates by zone
        final_rates = self._aggregate_by_zone(validated_rates)
        
        # Save to cache
        self._save_to_cache(date, final_rates)
        
        logger.info(f"Collected {len(final_rates)} valid exchange rates for {date}")
        
        return final_rates
    
    def _aggregate_by_zone(self, rates: List[ZoneExchangeRate]) -> List[ZoneExchangeRate]:
        """Aggregate multiple rates per zone using weighted average."""
        from .exchange_rate_collector import ExchangeRateCollector
        
        # Reuse aggregation logic from V1
        temp_collector = ExchangeRateCollector()
        temp_collector.sources = self.sources  # Use our sources for reliability scores
        
        return temp_collector._aggregate_by_zone(rates)
    
    def _load_from_cache(self, date: datetime) -> Optional[List[ZoneExchangeRate]]:
        """Load rates from cache if available and fresh."""
        cache_file = self.cache_directory / f"rates_{date.strftime('%Y%m%d')}.json"
        
        if cache_file.exists():
            # Check if cache is fresh (less than 24 hours old)
            cache_age = datetime.now() - datetime.fromtimestamp(cache_file.stat().st_mtime)
            if cache_age < timedelta(hours=24):
                try:
                    with open(cache_file, 'r') as f:
                        data = json.load(f)
                    
                    rates = []
                    for item in data:
                        rates.append(ZoneExchangeRate(
                            zone=CurrencyZone(item['zone']),
                            from_currency=Currency(item['from_currency']),
                            to_currency=Currency(item['to_currency']),
                            rate=Decimal(item['rate']),
                            date=datetime.fromisoformat(item['date']),
                            rate_type=item['rate_type'],
                            source=item['source'],
                            confidence=item['confidence']
                        ))
                    
                    return rates
                    
                except Exception as e:
                    logger.error(f"Failed to load cache: {e}")
        
        return None
    
    def _save_to_cache(self, date: datetime, rates: List[ZoneExchangeRate]):
        """Save rates to cache."""
        cache_file = self.cache_directory / f"rates_{date.strftime('%Y%m%d')}.json"
        
        data = []
        for rate in rates:
            data.append({
                'zone': rate.zone.value,
                'from_currency': rate.from_currency.value,
                'to_currency': rate.to_currency.value,
                'rate': str(rate.rate),
                'date': rate.date.isoformat(),
                'rate_type': rate.rate_type,
                'source': rate.source,
                'confidence': rate.confidence
            })
        
        try:
            with open(cache_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save cache: {e}")


# Example configuration
EXAMPLE_CONFIG = {
    # API Keys (obtain from providers)
    'xe_api_key': 'your_xe_api_key_here',
    'oanda_api_key': 'your_oanda_api_key_here',
    'oanda_account_id': 'your_oanda_account_id',
    
    # Data directories
    'wfp_data_directory': './data/raw/wfp',
    'cache_dir': './data/processed/exchange_rates',
    
    # Web scraping URLs
    'cby_aden_url': 'https://cby-aden.gov.ye/exchange-rates',
    'cby_sanaa_url': 'https://cby-ye.com/exchange-rates',
    
    # Telegram monitoring (optional)
    'telegram_api_id': 'your_telegram_api_id',
    'telegram_api_hash': 'your_telegram_api_hash',
    'telegram_channels': ['yemen_exchange_rates', 'aden_money_changers'],
    
    # Source-specific configurations
    'xe_config': {
        'rate_limit_delay': 1.0,
        'max_retries': 3
    },
    'wfp_config': {
        'file_patterns': ['*.csv', '*.xlsx'],
        'date_column': 'date'
    }
}


# Usage example
async def main():
    """Example usage of the enhanced exchange rate collector."""
    
    # Initialize collector with configuration
    collector = ExchangeRateCollectorV2(EXAMPLE_CONFIG)
    
    # Collect rates for specific date
    target_date = datetime(2024, 1, 15)
    rates = await collector.collect_daily_rates(target_date)
    
    # Display collected rates
    print(f"\nCollected {len(rates)} exchange rates for {target_date}:")
    for rate in rates:
        print(f"  {rate.zone.value}: {rate.rate} YER/USD (confidence: {rate.confidence:.2f})")
    
    # Collect historical rates
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 31)
    
    historical_rates = []
    current = start_date
    while current <= end_date:
        daily_rates = await collector.collect_daily_rates(current)
        historical_rates.extend(daily_rates)
        current += timedelta(days=1)
    
    print(f"\nCollected {len(historical_rates)} historical rates for January 2024")


if __name__ == "__main__":
    # Run the example
    asyncio.run(main())