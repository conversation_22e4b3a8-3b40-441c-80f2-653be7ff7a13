"""WFP (World Food Programme) data client."""

import asyncio
from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List, Optional

import httpx
import pandas as pd

from ...core.domain.market.entities import PriceObservation
from ...core.domain.market.value_objects import MarketId, Commodity, Price


class WFPClient:
    """Client for WFP data sources."""
    
    WFP_API_URL = "https://api.wfp.org/vam/v2"
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize WFP client."""
        self.api_key = api_key
        self.headers = {
            "Accept": "application/json",
            "User-Agent": "Yemen-Market-Integration/2.0"
        }
        if api_key:
            self.headers["X-API-Key"] = api_key
    
    async def get_market_prices(
        self,
        country_code: str = "YEM",
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        commodity_codes: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Get market prices from WFP API."""
        params = {
            "country": country_code,
            "page": 1,
            "format": "json"
        }
        
        if start_date:
            params["startdate"] = start_date.strftime("%Y-%m-%d")
        if end_date:
            params["enddate"] = end_date.strftime("%Y-%m-%d")
        if commodity_codes:
            params["commodity"] = ",".join(commodity_codes)
        
        all_data = []
        
        async with httpx.AsyncClient() as client:
            while True:
                response = await client.get(
                    f"{self.WFP_API_URL}/market-prices",
                    params=params,
                    headers=self.headers,
                    timeout=30
                )
                response.raise_for_status()
                
                data = response.json()
                items = data.get("items", [])
                all_data.extend(items)
                
                # Check if there are more pages
                if len(items) < data.get("page_size", 1000):
                    break
                
                params["page"] += 1
        
        return all_data
    
    def parse_wfp_csv(self, file_path: str) -> pd.DataFrame:
        """Parse WFP CSV file into DataFrame."""
        # Read CSV with specific WFP format
        df = pd.read_csv(
            file_path,
            parse_dates=["date"],
            dtype={
                "admin1": str,
                "admin2": str,
                "market": str,
                "commodity": str,
                "unit": str,
                "currency": str,
                "price": float,
                "usdprice": float
            }
        )
        
        # Standardize column names
        df.columns = df.columns.str.lower().str.replace(" ", "_")
        
        # Filter for Yemen if not already filtered
        if "country" in df.columns:
            df = df[df["country"] == "Yemen"]
        
        return df
    
    def to_price_observations(
        self,
        wfp_data: pd.DataFrame,
        market_mapping: Dict[str, str]
    ) -> List[PriceObservation]:
        """Convert WFP data to domain price observations."""
        observations = []
        
        for _, row in wfp_data.iterrows():
            # Map WFP market name to our market ID
            market_name = f"{row['admin1']}_{row['market']}"
            market_id = market_mapping.get(market_name)
            
            if not market_id:
                continue  # Skip unmapped markets
            
            # Create commodity
            commodity = Commodity(
                code=self._standardize_commodity_code(row["commodity"]),
                name=row["commodity"],
                category=self._get_commodity_category(row["commodity"]),
                standard_unit=self._standardize_unit(row["unit"])
            )
            
            # Create price
            price = Price(
                amount=Decimal(str(row["price"])),
                currency=row["currency"],
                unit=row["unit"]
            )
            
            # Create observation
            observation = PriceObservation(
                market_id=MarketId(market_id),
                commodity=commodity,
                price=price,
                observed_date=pd.to_datetime(row["date"]),
                source="WFP",
                quality="standard",
                observations_count=row.get("observations", 1)
            )
            
            observations.append(observation)
        
        return observations
    
    def _standardize_commodity_code(self, commodity_name: str) -> str:
        """Convert WFP commodity name to standardized code."""
        # Mapping of common WFP names to codes
        commodity_map = {
            "Wheat flour": "WHEAT_FLOUR",
            "Wheat": "WHEAT",
            "Rice (imported)": "RICE_IMPORTED",
            "Sugar": "SUGAR",
            "Vegetable oil": "OIL_VEGETABLE",
            "Beans (kidney red)": "BEANS_RED",
            "Salt": "SALT",
            "Eggs": "EGGS",
            "Chicken meat": "MEAT_CHICKEN",
            "Fuel (diesel)": "FUEL_DIESEL",
            "Fuel (petrol-gasoline)": "FUEL_PETROL"
        }
        
        return commodity_map.get(commodity_name, commodity_name.upper().replace(" ", "_"))
    
    def _get_commodity_category(self, commodity_name: str) -> str:
        """Determine commodity category."""
        name_lower = commodity_name.lower()
        
        if "fuel" in name_lower or "diesel" in name_lower or "petrol" in name_lower:
            return "fuel"
        elif "meat" in name_lower or "chicken" in name_lower or "eggs" in name_lower:
            return "protein"
        elif "oil" in name_lower or "sugar" in name_lower:
            return "processed"
        elif "wheat" in name_lower or "rice" in name_lower:
            return "cereal"
        elif "beans" in name_lower or "lentils" in name_lower:
            return "legume"
        else:
            return "food"
    
    def _standardize_unit(self, unit: str) -> str:
        """Standardize measurement units."""
        unit_lower = unit.lower()
        
        if "kg" in unit_lower:
            return "kg"
        elif "litre" in unit_lower or "liter" in unit_lower:
            return "liter"
        elif "50 kg" in unit_lower:
            return "50kg_bag"
        elif "dozen" in unit_lower:
            return "dozen"
        else:
            return unit