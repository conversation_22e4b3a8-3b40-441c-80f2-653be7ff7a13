"""ACAPS areas of control processor for V2 architecture.

Adapts V1 ACAPS processor logic to work with V2 domain models and async patterns.
"""

import asyncio
import zipfile
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import logging

import geopandas as gpd
import pandas as pd
import numpy as np
from shapely.geometry import Point

from ...core.domain.geography.entities import District, Governorate
from ...core.domain.market.entities import Market
from ...core.domain.market.value_objects import ControlStatus, Coordinates
from ...shared.plugins.interfaces import DataProcessor


logger = logging.getLogger(__name__)


class ACAPSProcessor(DataProcessor):
    """Process ACAPS areas of control data for Yemen territorial analysis.

    Handles:
    - Extracting control zone data from ACAPS downloads
    - Processing bi-weekly territorial control updates
    - Creating time series of control changes by district
    - Mapping districts to control zones over time
    - Converting to domain entities
    """

    # Control zone mappings to domain control status
    CONTROL_ZONE_MAPPINGS = {
        'DFA': ControlStatus.HOUTHI,
        'De-facto Authority': ControlStatus.HOUTHI,
        'Houthi': ControlStatus.HOUTHI,
        'Ansar <PERSON>': ControlStatus.HOUTHI,
        'SPC': ControlStatus.HOUTHI,
        'Sana\'a': ControlStatus.HOUTHI,

        'IRG': ControlStatus.GOVERNMENT,
        'Government': ControlStatus.GOVERNMENT,
        'Internationally Recognized Government': ControlStatus.GOVERNMENT,
        'Hadi': ControlStatus.GOVERNMENT,
        'Aden': ControlStatus.GOVERNMENT,
        'GOY': ControlStatus.GOVERNMENT,
        'ROYG': ControlStatus.GOVERNMENT,

        'STC': ControlStatus.GOVERNMENT,  # Aligned with government for now
        'Southern Transitional Council': ControlStatus.GOVERNMENT,

        'AQAP': ControlStatus.CONTESTED,  # Non-state actor treated as contested
        'AQAP Presence': ControlStatus.CONTESTED,
        'Al-Qaeda': ControlStatus.CONTESTED,

        'Contested': ControlStatus.CONTESTED,
        'Disputed': ControlStatus.CONTESTED,
        'Mixed': ControlStatus.CONTESTED,
        'Unclear': ControlStatus.CONTESTED,
        'DFA,IRG': ControlStatus.CONTESTED,
        'IRG,STC': ControlStatus.CONTESTED,

        'Other': ControlStatus.UNKNOWN,
        'Local': ControlStatus.UNKNOWN,
        'Tribal': ControlStatus.UNKNOWN,
        'Unknown': ControlStatus.UNKNOWN
    }

    def __init__(self):
        """Initialize ACAPS processor."""
        self._districts_cache: Dict[str, District] = {}
        self._governorates_cache: Dict[str, Governorate] = {}

    async def process(
        self,
        data_source: Union[str, Path, gpd.GeoDataFrame],
        extract_date: Optional[datetime] = None
    ) -> Tuple[List[District], List[Governorate], pd.DataFrame]:
        """Process ACAPS data and return domain entities.

        Args:
            data_source: Path to ZIP/shapefile or GeoDataFrame
            extract_date: Date when control data was extracted

        Returns:
            Tuple of (districts, governorates, control_timeline)
        """
        try:
            # Load data
            if isinstance(data_source, (str, Path)):
                logger.info(f"Loading ACAPS data from {data_source}")
                gdf = await self._load_geospatial_data(data_source)
            else:
                gdf = data_source.copy()

            # Set extract date if not provided
            if extract_date is None:
                extract_date = self._infer_extract_date(data_source)

            # Basic preprocessing
            gdf = self._preprocess_data(gdf)

            # Extract entities
            districts, governorates = await self._extract_geographic_entities(gdf)

            # Create control timeline
            control_timeline = await self._create_control_timeline(gdf, extract_date)

            logger.info(
                f"Processed ACAPS data: {len(districts)} districts, "
                f"{len(governorates)} governorates"
            )

            return districts, governorates, control_timeline

        except Exception as e:
            logger.error(f"Error processing ACAPS data: {e}")
            raise

    async def _load_geospatial_data(
        self,
        file_path: Union[str, Path]
    ) -> gpd.GeoDataFrame:
        """Load geospatial data from file."""
        file_path = Path(file_path)

        if file_path.suffix.lower() == '.zip':
            # Extract ZIP file and find shapefile
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                # Look for shapefile
                shp_files = [f for f in zip_file.namelist() if f.endswith('.shp')]
                if not shp_files:
                    raise ValueError(f"No shapefile found in {file_path}")

                # Extract to temporary directory and read
                extract_path = file_path.parent / f"temp_{file_path.stem}"
                zip_file.extractall(extract_path)

                # Read the first shapefile found
                gdf = gpd.read_file(extract_path / shp_files[0])

                # Clean up temporary files
                import shutil
                shutil.rmtree(extract_path)

        else:
            # Direct shapefile or other format
            loop = asyncio.get_event_loop()
            gdf = await loop.run_in_executor(None, gpd.read_file, str(file_path))

        return gdf

    def _infer_extract_date(self, data_source: Union[str, Path]) -> datetime:
        """Infer extract date from filename."""
        if isinstance(data_source, (str, Path)):
            filename = Path(data_source).name
            # Look for date patterns in filename like YYYYMMDD
            import re
            date_match = re.search(r'(\d{8})', filename)
            if date_match:
                date_str = date_match.group(1)
                return datetime.strptime(date_str, '%Y%m%d')

        # Default to current time if can't infer
        return datetime.now()

    def _preprocess_data(self, gdf: gpd.GeoDataFrame) -> gpd.GeoDataFrame:
        """Preprocess ACAPS geospatial data."""
        # Standardize column names
        column_mappings = {
            'ADM1_EN': 'governorate',
            'ADM1_AR': 'governorate_ar',
            'ADM2_EN': 'district',
            'ADM2_AR': 'district_ar',
            'ADM1_PCODE': 'gov_pcode',
            'ADM2_PCODE': 'dist_pcode',
            'Control': 'control_status',
            'CONTROL': 'control_status',
            'control': 'control_status'
        }

        # Apply mappings for existing columns
        gdf = gdf.rename(columns={
            old: new for old, new in column_mappings.items()
            if old in gdf.columns
        })

        # Ensure we have required columns
        required_columns = ['governorate', 'district']
        for col in required_columns:
            if col not in gdf.columns:
                # Try to find alternative column names
                alt_cols = [c for c in gdf.columns if col.lower() in c.lower()]
                if alt_cols:
                    gdf[col] = gdf[alt_cols[0]]
                else:
                    logger.warning(f"Required column '{col}' not found")

        # Clean text columns
        text_columns = ['governorate', 'district', 'control_status']
        for col in text_columns:
            if col in gdf.columns:
                gdf[col] = gdf[col].astype(str).str.strip()

        # Calculate centroids for districts
        gdf['centroid'] = gdf.geometry.centroid
        gdf['longitude'] = gdf.centroid.x
        gdf['latitude'] = gdf.centroid.y

        return gdf

    async def _extract_geographic_entities(
        self,
        gdf: gpd.GeoDataFrame
    ) -> Tuple[List[District], List[Governorate]]:
        """Extract district and governorate entities."""
        districts = []
        governorates = []

        # Group by governorate first
        for gov_name, gov_group in gdf.groupby('governorate'):
            if pd.isna(gov_name) or gov_name == 'nan':
                continue

            # Skip if already processed
            if gov_name in self._governorates_cache:
                governorate = self._governorates_cache[gov_name]
            else:
                # Create governorate entity
                gov_pcode = gov_group['gov_pcode'].iloc[0] if 'gov_pcode' in gov_group.columns else None

                # Calculate governorate centroid
                gov_centroid = gov_group.geometry.unary_union.centroid
                gov_coordinates = Coordinates(
                    latitude=gov_centroid.y,
                    longitude=gov_centroid.x
                )

                governorate = Governorate(
                    name=str(gov_name),
                    pcode=str(gov_pcode) if gov_pcode else None,
                    coordinates=gov_coordinates
                )

                self._governorates_cache[gov_name] = governorate

            governorates.append(governorate)

            # Process districts in this governorate
            for _, district_row in gov_group.iterrows():
                district_name = str(district_row['district'])

                if pd.isna(district_name) or district_name == 'nan':
                    continue

                # Create unique key for district within governorate
                district_key = f"{gov_name}_{district_name}"

                if district_key in self._districts_cache:
                    district = self._districts_cache[district_key]
                else:
                    # Create district entity
                    dist_pcode = district_row.get('dist_pcode')

                    district_coordinates = Coordinates(
                        latitude=float(district_row['latitude']),
                        longitude=float(district_row['longitude'])
                    )

                    # Determine current control status
                    control_status = self._map_control_status(
                        district_row.get('control_status', 'Unknown')
                    )

                    district = District(
                        name=district_name,
                        governorate_name=gov_name,
                        pcode=str(dist_pcode) if dist_pcode else None,
                        coordinates=district_coordinates,
                        control_status=control_status
                    )

                    self._districts_cache[district_key] = district

                districts.append(district)

        return districts, governorates

    async def _create_control_timeline(
        self,
        gdf: gpd.GeoDataFrame,
        extract_date: datetime
    ) -> pd.DataFrame:
        """Create control status timeline from current snapshot."""
        timeline_data = []

        for _, row in gdf.iterrows():
            if pd.isna(row.get('district')) or pd.isna(row.get('governorate')):
                continue

            control_status = self._map_control_status(
                row.get('control_status', 'Unknown')
            )

            timeline_data.append({
                'date': extract_date,
                'governorate': str(row['governorate']),
                'district': str(row['district']),
                'control_status': control_status.value,
                'dist_pcode': row.get('dist_pcode'),
                'gov_pcode': row.get('gov_pcode')
            })

        return pd.DataFrame(timeline_data)

    def _map_control_status(self, control_value: str) -> ControlStatus:
        """Map ACAPS control value to domain control status."""
        if pd.isna(control_value):
            return ControlStatus.UNKNOWN

        control_str = str(control_value).strip()

        # Direct mapping
        if control_str in self.CONTROL_ZONE_MAPPINGS:
            return self.CONTROL_ZONE_MAPPINGS[control_str]

        # Fuzzy matching for partial matches
        control_lower = control_str.lower()

        # Check for Houthi indicators
        houthi_keywords = ['houthi', 'ansar allah', 'dfa', 'spc', 'sana']
        if any(keyword in control_lower for keyword in houthi_keywords):
            return ControlStatus.HOUTHI

        # Check for Government indicators
        gov_keywords = ['government', 'irg', 'hadi', 'aden', 'goy', 'royg']
        if any(keyword in control_lower for keyword in gov_keywords):
            return ControlStatus.GOVERNMENT

        # Check for contested indicators
        contested_keywords = ['contested', 'disputed', 'mixed', 'unclear', ',']
        if any(keyword in control_lower for keyword in contested_keywords):
            return ControlStatus.CONTESTED

        # Default to unknown
        logger.warning(f"Unknown control status: {control_str}")
        return ControlStatus.UNKNOWN

    async def create_market_control_mapping(
        self,
        districts: List[District],
        markets: List['Market']
    ) -> pd.DataFrame:
        """Create mapping between markets and their control status.

        Args:
            districts: List of districts with control status
            markets: List of markets to map

        Returns:
            DataFrame with market-control mappings
        """
        mappings = []

        # Create spatial index for efficient lookup
        districts_gdf = self._districts_to_geodataframe(districts)

        for market in markets:
            if not market.coordinates:
                continue

            # Find containing district
            market_point = Point(
                market.coordinates.longitude,
                market.coordinates.latitude
            )

            # Spatial query (simplified - in real implementation would use proper spatial index)
            containing_district = None
            min_distance = float('inf')

            for _, district_row in districts_gdf.iterrows():
                try:
                    if district_row.geometry.contains(market_point):
                        containing_district = district_row
                        break
                    else:
                        # Fallback to distance-based matching
                        distance = district_row.geometry.distance(market_point)
                        if distance < min_distance:
                            min_distance = distance
                            containing_district = district_row
                except Exception:
                    continue

            if containing_district is not None:
                mappings.append({
                    'market_id': market.market_id.value,
                    'market_name': market.name,
                    'district': containing_district['name'],
                    'governorate': containing_district['governorate'],
                    'control_status': containing_district['control_status']
                })
            else:
                # Fallback based on governorate name
                control_status = ControlStatus.UNKNOWN
                for district in districts:
                    if district.governorate_name.lower() in market.governorate_name.lower():
                        control_status = district.control_status
                        break

                mappings.append({
                    'market_id': market.market_id.value,
                    'market_name': market.name,
                    'district': 'Unknown',
                    'governorate': market.governorate_name,
                    'control_status': control_status.value
                })

        return pd.DataFrame(mappings)

    def _districts_to_geodataframe(self, districts: List[District]) -> gpd.GeoDataFrame:
        """Convert districts to GeoDataFrame for spatial operations."""
        # This is a simplified version - in real implementation would need
        # to load actual district boundaries
        data = []
        for district in districts:
            if district.coordinates:
                # Create a small buffer around the centroid as placeholder
                point = Point(
                    district.coordinates.longitude,
                    district.coordinates.latitude
                )
                geometry = point.buffer(0.1)  # ~11km buffer

                data.append({
                    'name': district.name,
                    'governorate': district.governorate_name,
                    'control_status': district.control_status.value,
                    'geometry': geometry
                })

        return gpd.GeoDataFrame(data, crs='EPSG:4326')
