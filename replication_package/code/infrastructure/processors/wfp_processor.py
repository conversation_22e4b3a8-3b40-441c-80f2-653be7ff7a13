"""WFP data processor for V2 architecture.

Adapts V1 WFP processor logic to work with V2 domain models and async patterns.
"""

import asyncio
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Union
import logging

import pandas as pd
import numpy as np

from ...core.domain.market.entities import Market, PriceObservation
from ...core.domain.market.value_objects import (
    MarketId, Commodity, Price, Currency, ExchangeRate,
    Coordinates, MarketType, ControlStatus
)
from ...core.domain.geography.entities import Governorate, District
from ..external_services.wfp_client import WFPClient
from ...shared.plugins.interfaces import DataProcessor


logger = logging.getLogger(__name__)


class WFPProcessor(DataProcessor):
    """Process WFP food price data for Yemen market analysis.
    
    Handles:
    - Parsing WFP CSV/API data with proper data types
    - Extracting and standardizing market information
    - Handling exchange rate data (official and parallel)
    - Creating derived features (exchange rate differentials)
    - Converting to domain entities
    - Handling missing data appropriately
    """
    
    # Governorate name mappings to standard pcode names
    GOVERNORATE_MAPPINGS = {
        "Al Dhale'e": "Ad Dale'",
        "Al Hudaydah": "<PERSON>", 
        "Amanat Al Asimah": "Sana'a City",
        "Hadramaut": "Hadramawt",
        "Sa'ada": "Sa'dah",
        "Taizz": "Ta'iz"
    }
    
    # Commodity mappings for standardization
    COMMODITY_MAPPINGS = {
        "Wheat flour": "Wheat Flour",
        "Rice (imported)": "Rice (Imported)",
        "Sugar (white)": "Sugar",
        "Cooking oil": "Oil (Vegetable)",
        "Red beans": "Beans (Kidney Red)",
        "White beans": "Beans (White)",
        "Yellow split peas": "Peas (Yellow, Split)",
        "Fuel (diesel)": "Fuel (Diesel)",
        "Fuel (petrol)": "Fuel (Petrol-Gasoline)",
        "LPG": "Fuel (Gas)"
    }
    
    def __init__(
        self, 
        wfp_client: Optional[WFPClient] = None,
        min_market_coverage: float = 0.05
    ):
        """Initialize WFP processor.
        
        Args:
            wfp_client: Optional WFP API client
            min_market_coverage: Minimum required market coverage for commodities
        """
        self.wfp_client = wfp_client
        self.min_market_coverage = min_market_coverage
        self._markets_cache: Dict[str, Market] = {}
        self._governorates_cache: Dict[str, Governorate] = {}
        
    async def process(
        self,
        data_source: Union[str, pd.DataFrame],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        commodities: Optional[List[str]] = None
    ) -> Tuple[List[Market], List[PriceObservation], List[ExchangeRate]]:
        """Process WFP data and return domain entities.
        
        Args:
            data_source: Path to CSV file or DataFrame
            start_date: Start date for filtering
            end_date: End date for filtering
            commodities: List of commodities to include
            
        Returns:
            Tuple of (markets, price_observations, exchange_rates)
        """
        try:
            # Load data
            if isinstance(data_source, str):
                logger.info(f"Loading WFP data from {data_source}")
                df = await self._load_csv_async(data_source)
            else:
                df = data_source.copy()
                
            # Basic preprocessing
            df = self._preprocess_data(df)
            
            # Filter by date range
            if start_date or end_date:
                df = self._filter_by_date(df, start_date, end_date)
                
            # Filter commodities
            if commodities:
                df = self._filter_commodities(df, commodities)
            else:
                # Smart commodity filtering based on coverage
                df = await self._smart_commodity_filter(df)
                
            # Extract entities
            markets = await self._extract_markets(df)
            price_observations = await self._extract_price_observations(df, markets)
            exchange_rates = await self._extract_exchange_rates(df, markets)
            
            logger.info(
                f"Processed WFP data: {len(markets)} markets, "
                f"{len(price_observations)} price observations, "
                f"{len(exchange_rates)} exchange rates"
            )
            
            return markets, price_observations, exchange_rates
            
        except Exception as e:
            logger.error(f"Error processing WFP data: {e}")
            raise
            
    async def _load_csv_async(self, filepath: str) -> pd.DataFrame:
        """Load CSV file asynchronously."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, pd.read_csv, filepath)
        
    def _preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Preprocess WFP data with standard cleaning."""
        # Rename columns to standard names
        column_mappings = {
            'date': 'date',
            'market': 'market_name',
            'commodity': 'commodity',
            'price': 'price',
            'currency': 'currency',
            'unit': 'unit',
            'governorate': 'governorate',
            'district': 'district',
            'lat': 'latitude',
            'lon': 'longitude'
        }
        
        # Apply mappings for existing columns
        df = df.rename(columns={
            old: new for old, new in column_mappings.items() 
            if old in df.columns
        })
        
        # Convert date
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
            
        # Standardize governorate names
        if 'governorate' in df.columns:
            df['governorate'] = df['governorate'].map(
                lambda x: self.GOVERNORATE_MAPPINGS.get(x, x)
            )
            
        # Standardize commodity names
        if 'commodity' in df.columns:
            df['commodity'] = df['commodity'].map(
                lambda x: self.COMMODITY_MAPPINGS.get(x, x)
            )
            
        # Convert price to numeric
        if 'price' in df.columns:
            df['price'] = pd.to_numeric(df['price'], errors='coerce')
            
        # Remove rows with missing critical data
        critical_columns = ['date', 'market_name', 'commodity', 'price']
        existing_critical = [col for col in critical_columns if col in df.columns]
        df = df.dropna(subset=existing_critical)
        
        return df
        
    def _filter_by_date(
        self, 
        df: pd.DataFrame,
        start_date: Optional[datetime],
        end_date: Optional[datetime]
    ) -> pd.DataFrame:
        """Filter dataframe by date range."""
        if 'date' not in df.columns:
            return df
            
        if start_date:
            df = df[df['date'] >= start_date]
        if end_date:
            df = df[df['date'] <= end_date]
            
        return df
        
    def _filter_commodities(
        self,
        df: pd.DataFrame,
        commodities: List[str]
    ) -> pd.DataFrame:
        """Filter dataframe by commodity list."""
        if 'commodity' not in df.columns:
            return df
            
        return df[df['commodity'].isin(commodities)]
        
    async def _smart_commodity_filter(self, df: pd.DataFrame) -> pd.DataFrame:
        """Filter commodities based on market coverage."""
        if 'commodity' not in df.columns or 'market_name' not in df.columns:
            return df
            
        # Calculate market coverage for each commodity
        total_markets = df['market_name'].nunique()
        commodity_coverage = (
            df.groupby('commodity')['market_name']
            .nunique() / total_markets
        )
        
        # Keep commodities with sufficient coverage
        valid_commodities = commodity_coverage[
            commodity_coverage >= self.min_market_coverage
        ].index.tolist()
        
        logger.info(
            f"Keeping {len(valid_commodities)} commodities with "
            f">= {self.min_market_coverage*100:.1f}% market coverage"
        )
        
        return df[df['commodity'].isin(valid_commodities)]
        
    async def _extract_markets(self, df: pd.DataFrame) -> List[Market]:
        """Extract unique markets from dataframe."""
        markets = []
        
        # Group by market to get unique markets
        market_groups = df.groupby('market_name').first()
        
        for market_name, row in market_groups.iterrows():
            # Skip if already processed
            if market_name in self._markets_cache:
                markets.append(self._markets_cache[market_name])
                continue
                
            # Create market entity
            market_id = MarketId(value=self._generate_market_id(market_name, row))
            
            # Determine coordinates
            coordinates = None
            if 'latitude' in row and 'longitude' in row:
                if not pd.isna(row['latitude']) and not pd.isna(row['longitude']):
                    coordinates = Coordinates(
                        latitude=float(row['latitude']),
                        longitude=float(row['longitude'])
                    )
                    
            # Determine market type (heuristic based on name)
            market_type = self._infer_market_type(market_name)
            
            # Create market
            market = Market(
                market_id=market_id,
                name=str(market_name),
                governorate=str(row.get('governorate', 'Unknown')),
                district=str(row.get('district', 'Unknown')),
                coordinates=coordinates,
                market_type=market_type,
                active_since=datetime.now()
            )
            
            markets.append(market)
            self._markets_cache[market_name] = market
            
        return markets
        
    async def _extract_price_observations(
        self,
        df: pd.DataFrame,
        markets: List[Market]
    ) -> List[PriceObservation]:
        """Extract price observations from dataframe."""
        observations = []
        market_lookup = {m.name: m for m in markets}
        
        for _, row in df.iterrows():
            market = market_lookup.get(row['market_name'])
            if not market:
                continue
                
            # Create commodity value object
            commodity_name = str(row['commodity'])
            commodity = Commodity(
                code=commodity_name.lower().replace(" ", "_"),
                name=commodity_name,
                category=self._infer_commodity_category(commodity_name),
                standard_unit=str(row.get('unit', 'kg'))
            )
            
            # Determine currency
            currency_str = str(row.get('currency', 'YER')).upper()
            try:
                currency = Currency[currency_str]
            except KeyError:
                currency = Currency.YER
                
            # Create price value object
            price = Price(
                amount=Decimal(str(row['price'])),
                currency=currency,
                unit=commodity.standard_unit
            )
            
            # Create observation
            observation = PriceObservation(
                market_id=market.market_id,
                commodity=commodity,
                price=price,
                observed_date=row['date'],
                source="WFP",
                quality=self._map_quality_score(self._calculate_quality_score(row))
            )
            
            observations.append(observation)
            
        return observations
        
    async def _extract_exchange_rates(
        self,
        df: pd.DataFrame,
        markets: List[Market]
    ) -> List[ExchangeRate]:
        """Extract exchange rates if available in data."""
        exchange_rates = []
        
        # Check if exchange rate columns exist
        fx_columns = [col for col in df.columns if 'exchange' in col.lower() or 'fx' in col.lower()]
        if not fx_columns:
            return exchange_rates
            
        market_lookup = {m.name: m for m in markets}
        
        for _, row in df.iterrows():
            market = market_lookup.get(row['market_name'])
            if not market:
                continue
                
            for fx_col in fx_columns:
                if pd.isna(row[fx_col]):
                    continue
                    
                # Determine rate type from column name
                is_parallel = 'parallel' in fx_col.lower() or 'black' in fx_col.lower()
                
                # Create exchange rate
                rate = ExchangeRate(
                    from_currency=Currency.USD,
                    to_currency=Currency.YER,
                    rate=Decimal(str(row[fx_col])),
                    rate_type="parallel" if is_parallel else "official_cby_aden"
                )
                
                exchange_rates.append(rate)
                
        return exchange_rates
        
    def _generate_market_id(self, market_name: str, row: pd.Series) -> str:
        """Generate unique market ID."""
        # Use combination of market name and location
        parts = [market_name]
        if 'governorate' in row and not pd.isna(row['governorate']):
            parts.append(str(row['governorate']))
        if 'district' in row and not pd.isna(row['district']):
            parts.append(str(row['district']))
            
        return "_".join(parts).lower().replace(" ", "_")
        
    def _infer_market_type(self, market_name: str) -> MarketType:
        """Infer market type from name."""
        name_lower = market_name.lower()
        
        if 'wholesale' in name_lower:
            return MarketType.WHOLESALE
        elif 'port' in name_lower:
            return MarketType.PORT
        elif 'border' in name_lower:
            return MarketType.BORDER
        elif 'rural' in name_lower:
            return MarketType.RURAL
        else:
            return MarketType.RETAIL
            
    def _infer_commodity_category(self, commodity_name: str) -> str:
        """Infer commodity category from name."""
        name_lower = commodity_name.lower()
        
        if any(x in name_lower for x in ['wheat', 'rice', 'sorghum', 'millet']):
            return "cereals"
        elif any(x in name_lower for x in ['bean', 'lentil', 'pea']):
            return "pulses"
        elif any(x in name_lower for x in ['oil', 'fat']):
            return "oils_fats"
        elif any(x in name_lower for x in ['fuel', 'diesel', 'petrol', 'gas', 'lpg']):
            return "fuel"
        elif any(x in name_lower for x in ['meat', 'chicken', 'fish', 'egg']):
            return "protein"
        elif any(x in name_lower for x in ['vegetable', 'tomato', 'onion', 'potato']):
            return "vegetables"
        elif any(x in name_lower for x in ['sugar', 'salt']):
            return "other"
        else:
            return "other"
            
    def _calculate_quality_score(self, row: pd.Series) -> float:
        """Calculate data quality score for observation."""
        score = 1.0
        
        # Reduce score for missing optional fields
        optional_fields = ['unit', 'latitude', 'longitude', 'currency']
        for field in optional_fields:
            if field in row and pd.isna(row[field]):
                score -= 0.1
                
        # Check for outliers (simple z-score method would need full context)
        # For now, just ensure score is valid
        return max(0.0, min(1.0, score))
        
    def _map_quality_score(self, score: float) -> str:
        """Map quality score to qualitative assessment."""
        if score >= 0.9:
            return "high"
        elif score >= 0.7:
            return "standard"
        elif score >= 0.5:
            return "low"
        else:
            return "poor"