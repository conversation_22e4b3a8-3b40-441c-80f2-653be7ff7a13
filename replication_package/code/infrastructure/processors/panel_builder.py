"""Panel dataset builder for V2 architecture.

Adapts V1 panel builder logic to work with V2 domain models and async patterns.
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union
import logging

import pandas as pd
import numpy as np

from ...core.domain.market.entities import Market, PriceObservation
from ...core.domain.conflict.entities import ConflictEvent
from ...core.domain.geography.entities import District, Governorate
from ...core.domain.market.value_objects import MarketId, Commodity, Currency
from ...shared.plugins.interfaces import DataProcessor


logger = logging.getLogger(__name__)


class PanelBuilder(DataProcessor):
    """Build integrated panel datasets for econometric analysis.
    
    Handles:
    - Merging price observations, conflict events, and geographic data
    - Creating balanced panel structures
    - Calculating derived features and transformations
    - Handling missing data appropriately
    - Converting to analysis-ready formats
    """
    
    def __init__(
        self,
        frequency: str = 'M',
        min_observations_per_series: int = 12,
        balance_requirement: float = 0.8
    ):
        """Initialize panel builder.
        
        Args:
            frequency: Panel frequency ('M' for monthly, 'W' for weekly)
            min_observations_per_series: Minimum observations required per series
            balance_requirement: Minimum data availability ratio for balanced panel
        """
        self.frequency = frequency
        self.min_observations_per_series = min_observations_per_series
        self.balance_requirement = balance_requirement
        
    async def process(
        self,
        markets: List[Market],
        price_observations: List[PriceObservation],
        conflict_events: Optional[List[ConflictEvent]] = None,
        districts: Optional[List[District]] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """Build integrated panel dataset.
        
        Args:
            markets: List of market entities
            price_observations: List of price observations
            conflict_events: Optional list of conflict events
            districts: Optional list of districts (for control status)
            start_date: Start date for panel
            end_date: End date for panel
            
        Returns:
            DataFrame with integrated panel data
        """
        try:
            logger.info("Building integrated panel dataset")
            
            # Convert to working dataframes
            prices_df = self._price_observations_to_dataframe(price_observations)
            markets_df = self._markets_to_dataframe(markets)
            
            # Create base panel structure
            panel_df = await self._create_base_panel(
                prices_df, markets_df, start_date, end_date
            )
            
            # Add conflict data if provided
            if conflict_events:
                conflict_df = self._conflict_events_to_dataframe(conflict_events, markets)
                panel_df = await self._integrate_conflict_data(panel_df, conflict_df)
                
            # Add geographic/control data if provided
            if districts:
                districts_df = self._districts_to_dataframe(districts)
                panel_df = await self._integrate_geographic_data(panel_df, districts_df)
                
            # Calculate derived features
            panel_df = await self._calculate_derived_features(panel_df)
            
            # Apply transformations
            panel_df = await self._apply_transformations(panel_df)
            
            logger.info(f"Built panel with shape {panel_df.shape}")
            
            return panel_df
            
        except Exception as e:
            logger.error(f"Error building panel dataset: {e}")
            raise
            
    async def create_balanced_panel(
        self,
        markets: List[Market],
        price_observations: List[PriceObservation],
        commodities: Optional[List[str]] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """Create balanced panel dataset with complete time series.
        
        Args:
            markets: List of market entities
            price_observations: List of price observations
            commodities: Optional list of commodities to include
            start_date: Start date for panel
            end_date: End date for panel
            
        Returns:
            Balanced panel DataFrame
        """
        try:
            logger.info("Creating balanced panel dataset")
            
            # Convert to working format
            prices_df = self._price_observations_to_dataframe(price_observations)
            
            # Filter commodities if specified
            if commodities:
                prices_df = prices_df[prices_df['commodity'].isin(commodities)]
                
            # Determine date range
            if start_date is None:
                start_date = prices_df['date'].min()
            if end_date is None:
                end_date = prices_df['date'].max()
                
            # Create balanced structure
            balanced_df = await self._create_balanced_structure(
                prices_df, start_date, end_date
            )
            
            # Filter for sufficient data availability
            balanced_df = await self._filter_balanced_series(balanced_df)
            
            logger.info(f"Created balanced panel with shape {balanced_df.shape}")
            
            return balanced_df
            
        except Exception as e:
            logger.error(f"Error creating balanced panel: {e}")
            raise
            
    def _price_observations_to_dataframe(
        self, 
        observations: List[PriceObservation]
    ) -> pd.DataFrame:
        """Convert price observations to DataFrame."""
        data = []
        
        for obs in observations:
            data.append({
                'market_id': obs.market_id.value,
                'commodity': obs.commodity.name,
                'date': obs.observed_date,
                'price': float(obs.price.amount),
                'currency': obs.price.currency.value,
                'unit': obs.price.unit,
                'source': obs.source,
                'quality_score': 1.0 if obs.quality == 'high' else 0.8 if obs.quality == 'standard' else 0.5
            })
            
        df = pd.DataFrame(data)
        
        # Convert date to datetime
        df['date'] = pd.to_datetime(df['date'])
        
        return df
        
    def _markets_to_dataframe(self, markets: List[Market]) -> pd.DataFrame:
        """Convert markets to DataFrame."""
        data = []
        
        for market in markets:
            data.append({
                'market_id': market.market_id.value,
                'market_name': market.name,
                'governorate': market.governorate,
                'district': market.district,
                'market_type': market.market_type.value,
                'latitude': market.coordinates.latitude if market.coordinates else None,
                'longitude': market.coordinates.longitude if market.coordinates else None,
                'is_active': market.active_until is None
            })
            
        return pd.DataFrame(data)
        
    def _conflict_events_to_dataframe(
        self, 
        events: List[ConflictEvent],
        markets: List[Market]
    ) -> pd.DataFrame:
        """Convert conflict events to market-month aggregated DataFrame."""
        # This would typically involve spatial joins and temporal aggregation
        # For now, creating a simplified structure
        
        # Create market lookup
        market_lookup = {m.market_id.value: m for m in markets}
        
        # Aggregate by market-month (simplified approach)
        data = []
        for event in events:
            # Find nearest market (simplified - in real implementation would use spatial index)
            nearest_market = None
            min_distance = float('inf')
            
            for market in markets:
                if market.coordinates and event.location:
                    distance = market.coordinates.distance_to(event.location)
                    if distance < min_distance and distance <= 50:  # 50km threshold
                        min_distance = distance
                        nearest_market = market
                        
            if nearest_market:
                # Extract year-month
                year_month = event.event_date.to_period('M')
                
                data.append({
                    'market_id': nearest_market.market_id.value,
                    'year_month': year_month,
                    'conflict_events': 1,
                    'fatalities': event.fatalities,
                    'conflict_intensity': event.intensity.value
                })
                
        df = pd.DataFrame(data)
        
        # Aggregate by market-month
        if not df.empty:
            df = df.groupby(['market_id', 'year_month']).agg({
                'conflict_events': 'sum',
                'fatalities': 'sum',
                'conflict_intensity': 'mean'
            }).reset_index()
            
        return df
        
    def _districts_to_dataframe(self, districts: List[District]) -> pd.DataFrame:
        """Convert districts to DataFrame."""
        data = []
        
        for district in districts:
            data.append({
                'district': district.name,
                'governorate': district.governorate_name,
                'control_status': district.control_status.value,
                'district_pcode': district.pcode
            })
            
        return pd.DataFrame(data)
        
    async def _create_base_panel(
        self,
        prices_df: pd.DataFrame,
        markets_df: pd.DataFrame,
        start_date: Optional[datetime],
        end_date: Optional[datetime]
    ) -> pd.DataFrame:
        """Create base panel structure with price data."""
        # Merge prices with market information
        panel_df = prices_df.merge(
            markets_df,
            on='market_id',
            how='left'
        )
        
        # Filter by date range
        if start_date:
            panel_df = panel_df[panel_df['date'] >= start_date]
        if end_date:
            panel_df = panel_df[panel_df['date'] <= end_date]
            
        # Create time period column
        if self.frequency == 'M':
            panel_df['time_period'] = panel_df['date'].dt.to_period('M')
        elif self.frequency == 'W':
            panel_df['time_period'] = panel_df['date'].dt.to_period('W')
        else:
            panel_df['time_period'] = panel_df['date'].dt.to_period('D')
            
        return panel_df
        
    async def _create_balanced_structure(
        self,
        prices_df: pd.DataFrame,
        start_date: datetime,
        end_date: datetime
    ) -> pd.DataFrame:
        """Create balanced panel structure."""
        # Get unique combinations
        markets = prices_df['market_id'].unique()
        commodities = prices_df['commodity'].unique()
        
        # Create date range
        if self.frequency == 'M':
            date_range = pd.period_range(
                start=start_date, 
                end=end_date, 
                freq='M'
            )
        elif self.frequency == 'W':
            date_range = pd.period_range(
                start=start_date, 
                end=end_date, 
                freq='W'
            )
        else:
            date_range = pd.period_range(
                start=start_date, 
                end=end_date, 
                freq='D'
            )
            
        # Create balanced structure
        balanced_index = pd.MultiIndex.from_product(
            [markets, commodities, date_range],
            names=['market_id', 'commodity', 'time_period']
        ).to_frame(index=False)
        
        # Add date column
        balanced_index['date'] = balanced_index['time_period'].dt.start_time
        
        # Aggregate prices to panel frequency
        prices_agg = prices_df.copy()
        if self.frequency == 'M':
            prices_agg['time_period'] = prices_agg['date'].dt.to_period('M')
        elif self.frequency == 'W':
            prices_agg['time_period'] = prices_agg['date'].dt.to_period('W')
        else:
            prices_agg['time_period'] = prices_agg['date'].dt.to_period('D')
            
        # Group by panel dimensions and take mean price
        prices_panel = prices_agg.groupby(
            ['market_id', 'commodity', 'time_period']
        ).agg({
            'price': 'mean',
            'currency': 'first',
            'quality_score': 'mean'
        }).reset_index()
        
        # Merge with balanced structure
        balanced_df = balanced_index.merge(
            prices_panel,
            on=['market_id', 'commodity', 'time_period'],
            how='left'
        )
        
        return balanced_df
        
    async def _filter_balanced_series(self, df: pd.DataFrame) -> pd.DataFrame:
        """Filter series based on data availability requirements."""
        # Calculate data availability by market-commodity
        availability = df.groupby(['market_id', 'commodity']).agg({
            'price': lambda x: x.notna().sum() / len(x)
        }).reset_index()
        
        # Filter based on availability threshold
        valid_series = availability[
            availability['price'] >= self.balance_requirement
        ][['market_id', 'commodity']]
        
        # Keep only valid series
        filtered_df = df.merge(
            valid_series,
            on=['market_id', 'commodity'],
            how='inner'
        )
        
        logger.info(
            f"Filtered to {len(valid_series)} series with "
            f">= {self.balance_requirement*100:.1f}% data availability"
        )
        
        return filtered_df
        
    async def _integrate_conflict_data(
        self, 
        panel_df: pd.DataFrame,
        conflict_df: pd.DataFrame
    ) -> pd.DataFrame:
        """Integrate conflict data into panel."""
        if conflict_df.empty:
            # Add empty conflict columns
            panel_df['conflict_events'] = 0
            panel_df['fatalities'] = 0
            panel_df['conflict_intensity'] = 0
            return panel_df
            
        # Merge conflict data
        merged_df = panel_df.merge(
            conflict_df,
            on=['market_id', 'year_month'],
            how='left'
        )
        
        # Fill missing conflict data with zeros
        conflict_cols = ['conflict_events', 'fatalities', 'conflict_intensity']
        for col in conflict_cols:
            if col in merged_df.columns:
                merged_df[col] = merged_df[col].fillna(0)
                
        return merged_df
        
    async def _integrate_geographic_data(
        self,
        panel_df: pd.DataFrame,
        districts_df: pd.DataFrame
    ) -> pd.DataFrame:
        """Integrate geographic and control data into panel."""
        # Merge on district information
        merged_df = panel_df.merge(
            districts_df,
            on=['district', 'governorate'],
            how='left'
        )
        
        # Fill missing control status
        if 'control_status' in merged_df.columns:
            merged_df['control_status'] = merged_df['control_status'].fillna('unknown')
            
        return merged_df
        
    async def _calculate_derived_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate derived features for econometric analysis."""
        # Log prices
        if 'price' in df.columns:
            df['log_price'] = np.log(df['price'].replace(0, np.nan))
            
        # Price changes
        df = df.sort_values(['market_id', 'commodity', 'time_period'])
        df['price_change'] = df.groupby(['market_id', 'commodity'])['price'].pct_change()
        df['log_price_change'] = df.groupby(['market_id', 'commodity'])['log_price'].diff()
        
        # Temporal features
        if 'date' in df.columns:
            df['year'] = df['date'].dt.year
            df['month'] = df['date'].dt.month
            df['quarter'] = df['date'].dt.quarter
            
        # Conflict intensity transformations
        if 'conflict_events' in df.columns:
            df['log_conflict'] = np.log(df['conflict_events'] + 1)
            
        return df
        
    async def _apply_transformations(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply final transformations for econometric analysis."""
        # Create dummy variables for categorical data
        if 'control_status' in df.columns:
            control_dummies = pd.get_dummies(
                df['control_status'], 
                prefix='control'
            )
            df = pd.concat([df, control_dummies], axis=1)
            
        if 'market_type' in df.columns:
            type_dummies = pd.get_dummies(
                df['market_type'],
                prefix='type'
            )
            df = pd.concat([df, type_dummies], axis=1)
            
        # Sort by panel dimensions
        df = df.sort_values(['market_id', 'commodity', 'time_period'])
        
        return df