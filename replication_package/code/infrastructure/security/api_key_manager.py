"""API Key management for machine-to-machine authentication."""

import secrets
import hashlib
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from pydantic import BaseModel

from src.core.utils.logging import get_logger
from ...infrastructure.caching.redis_cache import RedisCache

logger = get_logger(__name__)


class APIKey(BaseModel):
    """API Key model."""
    id: str
    name: str
    key_hash: str  # Store only the hash
    key_prefix: str  # First 8 chars for identification
    created_at: datetime
    expires_at: Optional[datetime] = None
    last_used_at: Optional[datetime] = None
    is_active: bool = True
    permissions: List[str] = []
    rate_limit: Optional[int] = None  # Requests per minute
    metadata: Dict[str, Any] = {}
    usage_count: int = 0


class APIKeyManager:
    """Manages API keys for authentication."""
    
    def __init__(self, cache: Optional[RedisCache] = None):
        """
        Initialize API key manager.
        
        Args:
            cache: Optional Redis cache for storing key data
        """
        self.cache = cache
        self.key_prefix_length = 8
        self.key_length = 32
        
    def generate_api_key(
        self,
        name: str,
        permissions: Optional[List[str]] = None,
        expires_at: Optional[datetime] = None,
        rate_limit: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> tuple[str, APIKey]:
        """
        Generate a new API key.
        
        Args:
            name: Name/description for the API key
            permissions: List of permissions for this key
            expires_at: Optional expiration date
            rate_limit: Optional rate limit (requests per minute)
            metadata: Optional metadata
            
        Returns:
            Tuple of (plain_api_key, api_key_object)
        """
        # Generate secure random key
        plain_key = f"ymi_{secrets.token_urlsafe(self.key_length)}"
        
        # Extract prefix for identification
        key_prefix = plain_key[:self.key_prefix_length]
        
        # Hash the key for storage
        key_hash = self._hash_key(plain_key)
        
        # Create API key object
        api_key = APIKey(
            id=secrets.token_urlsafe(16),
            name=name,
            key_hash=key_hash,
            key_prefix=key_prefix,
            created_at=datetime.now(timezone.utc),
            expires_at=expires_at,
            permissions=permissions or [],
            rate_limit=rate_limit,
            metadata=metadata or {}
        )
        
        # Store in cache if available
        if self.cache:
            cache_key = f"api_key:{api_key.id}"
            self.cache.set(cache_key, api_key.model_dump_json(), ttl=3600)  # 1 hour cache
        
        logger.info(f"Generated API key '{name}' with ID: {api_key.id}")
        return plain_key, api_key
    
    def verify_api_key(self, api_key: str) -> Optional[APIKey]:
        """
        Verify an API key and return the associated data.
        
        Args:
            api_key: The API key to verify
            
        Returns:
            APIKey object if valid, None if invalid
        """
        # Extract prefix
        if len(api_key) < self.key_prefix_length:
            return None
        
        key_prefix = api_key[:self.key_prefix_length]
        
        # Look up keys by prefix (in production, this would query a database)
        # For now, we'll simulate this with cache lookup
        if self.cache:
            # In production, we'd have an index of prefix -> key_id
            # For demonstration, we'll scan (not efficient for production)
            pattern = "api_key:*"
            for key in self.cache.scan(pattern):
                key_data = self.cache.get(key)
                if key_data:
                    api_key_obj = APIKey.model_validate_json(key_data)
                    if api_key_obj.key_prefix == key_prefix:
                        # Verify the full key
                        if self._verify_key_hash(api_key, api_key_obj.key_hash):
                            # Check if key is valid
                            if not self._is_key_valid(api_key_obj):
                                return None
                            
                            # Update usage statistics
                            self._update_key_usage(api_key_obj)
                            
                            return api_key_obj
        
        return None
    
    def revoke_api_key(self, key_id: str) -> bool:
        """
        Revoke an API key.
        
        Args:
            key_id: ID of the key to revoke
            
        Returns:
            True if revoked successfully
        """
        if self.cache:
            cache_key = f"api_key:{key_id}"
            key_data = self.cache.get(cache_key)
            if key_data:
                api_key = APIKey.model_validate_json(key_data)
                api_key.is_active = False
                self.cache.set(cache_key, api_key.model_dump_json(), ttl=3600)
                logger.info(f"Revoked API key: {key_id}")
                return True
        
        return False
    
    def rotate_api_key(
        self,
        old_key_id: str,
        grace_period_minutes: int = 60
    ) -> Optional[tuple[str, APIKey]]:
        """
        Rotate an API key, creating a new one with the same permissions.
        
        Args:
            old_key_id: ID of the key to rotate
            grace_period_minutes: Minutes to keep old key active
            
        Returns:
            Tuple of (new_plain_key, new_api_key) if successful
        """
        if self.cache:
            cache_key = f"api_key:{old_key_id}"
            key_data = self.cache.get(cache_key)
            if key_data:
                old_api_key = APIKey.model_validate_json(key_data)
                
                # Create new key with same permissions
                new_plain_key, new_api_key = self.generate_api_key(
                    name=f"{old_api_key.name} (rotated)",
                    permissions=old_api_key.permissions,
                    rate_limit=old_api_key.rate_limit,
                    metadata={**old_api_key.metadata, "rotated_from": old_key_id}
                )
                
                # Set expiration on old key
                from datetime import timedelta
                old_api_key.expires_at = datetime.now(timezone.utc) + timedelta(minutes=grace_period_minutes)
                self.cache.set(cache_key, old_api_key.model_dump_json(), ttl=grace_period_minutes * 60)
                
                logger.info(f"Rotated API key {old_key_id} -> {new_api_key.id}")
                return new_plain_key, new_api_key
        
        return None
    
    def get_key_usage_stats(self, key_id: str) -> Optional[Dict[str, Any]]:
        """
        Get usage statistics for an API key.
        
        Args:
            key_id: ID of the key
            
        Returns:
            Usage statistics dictionary
        """
        if self.cache:
            # Get usage data from cache
            usage_key = f"api_key_usage:{key_id}"
            usage_data = self.cache.get(usage_key)
            if usage_data:
                return {
                    "total_requests": usage_data.get("total", 0),
                    "requests_last_minute": usage_data.get("last_minute", 0),
                    "requests_last_hour": usage_data.get("last_hour", 0),
                    "last_used": usage_data.get("last_used")
                }
        
        return None
    
    def _hash_key(self, api_key: str) -> str:
        """Hash an API key for storage."""
        return hashlib.sha256(api_key.encode()).hexdigest()
    
    def _verify_key_hash(self, api_key: str, key_hash: str) -> bool:
        """Verify an API key against its hash."""
        return self._hash_key(api_key) == key_hash
    
    def _is_key_valid(self, api_key: APIKey) -> bool:
        """Check if an API key is valid (active and not expired)."""
        if not api_key.is_active:
            logger.warning(f"API key {api_key.id} is inactive")
            return False
        
        if api_key.expires_at and api_key.expires_at < datetime.now(timezone.utc):
            logger.warning(f"API key {api_key.id} has expired")
            return False
        
        return True
    
    def _update_key_usage(self, api_key: APIKey) -> None:
        """Update usage statistics for an API key."""
        api_key.last_used_at = datetime.now(timezone.utc)
        api_key.usage_count += 1
        
        if self.cache:
            # Update key data
            cache_key = f"api_key:{api_key.id}"
            self.cache.set(cache_key, api_key.model_dump_json(), ttl=3600)
            
            # Update usage statistics
            usage_key = f"api_key_usage:{api_key.id}"
            # In production, this would use Redis atomic operations
            self.cache.increment(f"{usage_key}:total")
            self.cache.increment(f"{usage_key}:minute", ttl=60)
            self.cache.increment(f"{usage_key}:hour", ttl=3600)