"""Security headers middleware for API protection."""

from typing import Optional, Dict, Any

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


class SecurityHeadersConfig:
    """Configuration for security headers."""
    
    def __init__(
        self,
        enable_hsts: bool = True,
        enable_csp: bool = True,
        enable_x_frame: bool = True,
        enable_x_content_type: bool = True,
        enable_x_xss_protection: bool = True,
        enable_referrer_policy: bool = True,
        enable_permissions_policy: bool = True,
        custom_headers: Optional[Dict[str, str]] = None
    ):
        self.enable_hsts = enable_hsts
        self.enable_csp = enable_csp
        self.enable_x_frame = enable_x_frame
        self.enable_x_content_type = enable_x_content_type
        self.enable_x_xss_protection = enable_x_xss_protection
        self.enable_referrer_policy = enable_referrer_policy
        self.enable_permissions_policy = enable_permissions_policy
        self.custom_headers = custom_headers or {}


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add security headers to all responses."""
    
    def __init__(
        self,
        app,
        config: Optional[SecurityHeadersConfig] = None
    ):
        super().__init__(app)
        self.config = config or SecurityHeadersConfig()
        
    async def dispatch(self, request: Request, call_next) -> Response:
        """Add security headers to response."""
        # Process request
        response = await call_next(request)
        
        # Add security headers
        
        # HTTP Strict Transport Security (HSTS)
        if self.config.enable_hsts:
            response.headers["Strict-Transport-Security"] = (
                "max-age=31536000; includeSubDomains; preload"
            )
        
        # Content Security Policy (CSP)
        if self.config.enable_csp:
            # Restrictive CSP for API
            csp_directives = [
                "default-src 'none'",
                "frame-ancestors 'none'",
                "base-uri 'self'",
                "form-action 'self'"
            ]
            
            # Allow specific sources for API documentation
            if request.url.path in ["/docs", "/redoc"]:
                csp_directives = [
                    "default-src 'self'",
                    "img-src 'self' data: https:",
                    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net",
                    "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net",
                    "font-src 'self' https://cdn.jsdelivr.net",
                    "connect-src 'self'",
                    "frame-ancestors 'none'",
                    "base-uri 'self'",
                    "form-action 'self'"
                ]
            
            response.headers["Content-Security-Policy"] = "; ".join(csp_directives)
        
        # X-Frame-Options
        if self.config.enable_x_frame:
            response.headers["X-Frame-Options"] = "DENY"
        
        # X-Content-Type-Options
        if self.config.enable_x_content_type:
            response.headers["X-Content-Type-Options"] = "nosniff"
        
        # X-XSS-Protection (legacy but still useful)
        if self.config.enable_x_xss_protection:
            response.headers["X-XSS-Protection"] = "1; mode=block"
        
        # Referrer-Policy
        if self.config.enable_referrer_policy:
            response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Permissions-Policy (formerly Feature-Policy)
        if self.config.enable_permissions_policy:
            permissions_policy = [
                "accelerometer=()",
                "camera=()",
                "geolocation=()",
                "gyroscope=()",
                "magnetometer=()",
                "microphone=()",
                "payment=()",
                "usb=()"
            ]
            response.headers["Permissions-Policy"] = ", ".join(permissions_policy)
        
        # Custom headers
        for header, value in self.config.custom_headers.items():
            response.headers[header] = value
        
        # Remove potentially sensitive headers
        headers_to_remove = ["Server", "X-Powered-By"]
        for header in headers_to_remove:
            if header in response.headers:
                del response.headers[header]
        
        return response


def get_security_headers(
    request_path: str,
    config: Optional[SecurityHeadersConfig] = None
) -> Dict[str, str]:
    """
    Get security headers for a specific request path.
    
    This can be used in individual endpoints if middleware is not desired.
    
    Args:
        request_path: The request path
        config: Security headers configuration
        
    Returns:
        Dictionary of security headers
    """
    config = config or SecurityHeadersConfig()
    headers = {}
    
    # HSTS
    if config.enable_hsts:
        headers["Strict-Transport-Security"] = (
            "max-age=31536000; includeSubDomains; preload"
        )
    
    # CSP
    if config.enable_csp:
        if request_path in ["/docs", "/redoc"]:
            # Relaxed CSP for documentation
            csp = (
                "default-src 'self'; "
                "img-src 'self' data: https:; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; "
                "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
                "font-src 'self' https://cdn.jsdelivr.net; "
                "connect-src 'self'; "
                "frame-ancestors 'none'; "
                "base-uri 'self'; "
                "form-action 'self'"
            )
        else:
            # Strict CSP for API endpoints
            csp = (
                "default-src 'none'; "
                "frame-ancestors 'none'; "
                "base-uri 'self'; "
                "form-action 'self'"
            )
        headers["Content-Security-Policy"] = csp
    
    # Other headers
    if config.enable_x_frame:
        headers["X-Frame-Options"] = "DENY"
    
    if config.enable_x_content_type:
        headers["X-Content-Type-Options"] = "nosniff"
    
    if config.enable_x_xss_protection:
        headers["X-XSS-Protection"] = "1; mode=block"
    
    if config.enable_referrer_policy:
        headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    
    if config.enable_permissions_policy:
        headers["Permissions-Policy"] = (
            "accelerometer=(), camera=(), geolocation=(), "
            "gyroscope=(), magnetometer=(), microphone=(), "
            "payment=(), usb=()"
        )
    
    # Add custom headers
    headers.update(config.custom_headers)
    
    return headers