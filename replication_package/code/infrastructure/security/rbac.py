"""Role-Based Access Control (RBAC) implementation."""

from enum import Enum
from typing import Set, Dict, List, Optional, Callable
from functools import wraps

from fastapi import HTTPException, status, Depends

from src.core.utils.logging import get_logger
from .jwt_handler import verify_token

logger = get_logger(__name__)


class Permission(str, Enum):
    """System permissions."""
    # Analysis permissions
    ANALYSIS_READ = "analysis:read"
    ANALYSIS_CREATE = "analysis:create"
    ANALYSIS_UPDATE = "analysis:update"
    ANALYSIS_DELETE = "analysis:delete"
    
    # Market data permissions
    MARKET_READ = "market:read"
    MARKET_CREATE = "market:create"
    MARKET_UPDATE = "market:update"
    MARKET_DELETE = "market:delete"
    
    # Commodity permissions
    COMMODITY_READ = "commodity:read"
    COMMODITY_CREATE = "commodity:create"
    COMMODITY_UPDATE = "commodity:update"
    COMMODITY_DELETE = "commodity:delete"
    
    # Price data permissions
    PRICE_READ = "price:read"
    PRICE_CREATE = "price:create"
    PRICE_UPDATE = "price:update"
    PRICE_DELETE = "price:delete"
    
    # Conflict data permissions
    CONFLICT_READ = "conflict:read"
    CONFLICT_CREATE = "conflict:create"
    CONFLICT_UPDATE = "conflict:update"
    CONFLICT_DELETE = "conflict:delete"
    
    # User management permissions
    USER_READ = "user:read"
    USER_CREATE = "user:create"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    
    # API key permissions
    API_KEY_READ = "api_key:read"
    API_KEY_CREATE = "api_key:create"
    API_KEY_UPDATE = "api_key:update"
    API_KEY_DELETE = "api_key:delete"
    
    # Admin permissions
    ADMIN_ACCESS = "admin:access"
    ADMIN_SETTINGS = "admin:settings"
    ADMIN_AUDIT = "admin:audit"
    
    # Policy permissions
    POLICY_READ = "policy:read"
    POLICY_CREATE = "policy:create"
    POLICY_UPDATE = "policy:update"
    POLICY_DELETE = "policy:delete"


class Role(str, Enum):
    """User roles."""
    ADMIN = "admin"
    ANALYST = "analyst"
    VIEWER = "viewer"
    API_USER = "api_user"
    POLICY_MAKER = "policy_maker"


class RBACManager:
    """Manages role-based access control."""
    
    def __init__(self):
        """Initialize RBAC manager with default role permissions."""
        self.role_permissions: Dict[Role, Set[Permission]] = {
            Role.ADMIN: {
                # Admin has all permissions
                Permission.ANALYSIS_READ, Permission.ANALYSIS_CREATE, 
                Permission.ANALYSIS_UPDATE, Permission.ANALYSIS_DELETE,
                Permission.MARKET_READ, Permission.MARKET_CREATE,
                Permission.MARKET_UPDATE, Permission.MARKET_DELETE,
                Permission.COMMODITY_READ, Permission.COMMODITY_CREATE,
                Permission.COMMODITY_UPDATE, Permission.COMMODITY_DELETE,
                Permission.PRICE_READ, Permission.PRICE_CREATE,
                Permission.PRICE_UPDATE, Permission.PRICE_DELETE,
                Permission.CONFLICT_READ, Permission.CONFLICT_CREATE,
                Permission.CONFLICT_UPDATE, Permission.CONFLICT_DELETE,
                Permission.USER_READ, Permission.USER_CREATE,
                Permission.USER_UPDATE, Permission.USER_DELETE,
                Permission.API_KEY_READ, Permission.API_KEY_CREATE,
                Permission.API_KEY_UPDATE, Permission.API_KEY_DELETE,
                Permission.ADMIN_ACCESS, Permission.ADMIN_SETTINGS,
                Permission.ADMIN_AUDIT,
                Permission.POLICY_READ, Permission.POLICY_CREATE,
                Permission.POLICY_UPDATE, Permission.POLICY_DELETE,
            },
            Role.ANALYST: {
                # Analysts can read all data and create/update analyses
                Permission.ANALYSIS_READ, Permission.ANALYSIS_CREATE,
                Permission.ANALYSIS_UPDATE,
                Permission.MARKET_READ,
                Permission.COMMODITY_READ,
                Permission.PRICE_READ,
                Permission.CONFLICT_READ,
                Permission.POLICY_READ,
            },
            Role.VIEWER: {
                # Viewers have read-only access
                Permission.ANALYSIS_READ,
                Permission.MARKET_READ,
                Permission.COMMODITY_READ,
                Permission.PRICE_READ,
                Permission.CONFLICT_READ,
                Permission.POLICY_READ,
            },
            Role.API_USER: {
                # API users have limited read access
                Permission.ANALYSIS_READ,
                Permission.MARKET_READ,
                Permission.COMMODITY_READ,
                Permission.PRICE_READ,
                Permission.API_KEY_READ,
            },
            Role.POLICY_MAKER: {
                # Policy makers can read data and manage policies
                Permission.ANALYSIS_READ,
                Permission.MARKET_READ,
                Permission.COMMODITY_READ,
                Permission.PRICE_READ,
                Permission.CONFLICT_READ,
                Permission.POLICY_READ, Permission.POLICY_CREATE,
                Permission.POLICY_UPDATE, Permission.POLICY_DELETE,
            }
        }
    
    def get_role_permissions(self, role: Role) -> Set[Permission]:
        """Get permissions for a role."""
        return self.role_permissions.get(role, set())
    
    def get_user_permissions(self, roles: List[Role]) -> Set[Permission]:
        """Get all permissions for a user based on their roles."""
        permissions = set()
        for role in roles:
            permissions.update(self.get_role_permissions(role))
        return permissions
    
    def has_permission(
        self,
        user_roles: List[Role],
        required_permission: Permission
    ) -> bool:
        """Check if user has a specific permission."""
        user_permissions = self.get_user_permissions(user_roles)
        return required_permission in user_permissions
    
    def has_any_permission(
        self,
        user_roles: List[Role],
        required_permissions: List[Permission]
    ) -> bool:
        """Check if user has any of the required permissions."""
        user_permissions = self.get_user_permissions(user_roles)
        return any(perm in user_permissions for perm in required_permissions)
    
    def has_all_permissions(
        self,
        user_roles: List[Role],
        required_permissions: List[Permission]
    ) -> bool:
        """Check if user has all of the required permissions."""
        user_permissions = self.get_user_permissions(user_roles)
        return all(perm in user_permissions for perm in required_permissions)
    
    def add_permission_to_role(self, role: Role, permission: Permission) -> None:
        """Add a permission to a role."""
        if role not in self.role_permissions:
            self.role_permissions[role] = set()
        self.role_permissions[role].add(permission)
        logger.info(f"Added permission {permission} to role {role}")
    
    def remove_permission_from_role(self, role: Role, permission: Permission) -> None:
        """Remove a permission from a role."""
        if role in self.role_permissions:
            self.role_permissions[role].discard(permission)
            logger.info(f"Removed permission {permission} from role {role}")


# Global RBAC manager instance
_rbac_manager = RBACManager()


def require_permission(permission: Permission) -> Callable:
    """
    Decorator to require a specific permission for an endpoint.
    
    Usage:
        @router.get("/protected")
        @require_permission(Permission.ANALYSIS_READ)
        async def protected_endpoint(current_user: User = Depends(get_current_user)):
            return {"message": "Access granted"}
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract current user from kwargs
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Check permission
            user_roles = [Role(role) for role in current_user.get('roles', [])]
            if not _rbac_manager.has_permission(user_roles, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required: {permission}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_any_permission(permissions: List[Permission]) -> Callable:
    """
    Decorator to require any of the specified permissions for an endpoint.
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract current user from kwargs
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Check permissions
            user_roles = [Role(role) for role in current_user.get('roles', [])]
            if not _rbac_manager.has_any_permission(user_roles, permissions):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required any of: {permissions}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_all_permissions(permissions: List[Permission]) -> Callable:
    """
    Decorator to require all of the specified permissions for an endpoint.
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract current user from kwargs
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Check permissions
            user_roles = [Role(role) for role in current_user.get('roles', [])]
            if not _rbac_manager.has_all_permissions(user_roles, permissions):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required all of: {permissions}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_role(role: Role) -> Callable:
    """
    Decorator to require a specific role for an endpoint.
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract current user from kwargs
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Check role
            user_roles = current_user.get('roles', [])
            if role.value not in user_roles:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient role. Required: {role}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator