"""JWT token handling for authentication."""

import os
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any

import jwt
from jwt.exceptions import PyJWTError
from pydantic import BaseModel

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


class TokenData(BaseModel):
    """Token payload data."""
    sub: str  # Subject (user ID)
    username: str
    email: Optional[str] = None
    roles: list[str] = []
    permissions: list[str] = []
    exp: Optional[datetime] = None
    iat: Optional[datetime] = None
    jti: Optional[str] = None  # JWT ID for token tracking
    type: str = "access"  # "access" or "refresh"


class JWTConfig(BaseModel):
    """JWT configuration."""
    secret_key: str
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    issuer: str = "yemen-market-integration-api"
    audience: str = "yemen-market-integration-client"


class JWTHandler:
    """Handles JWT token creation and verification."""
    
    def __init__(self, config: Optional[JWTConfig] = None):
        """Initialize JWT handler with configuration."""
        if config is None:
            # Load from environment - enforce production security
            secret_key = os.getenv("JWT_SECRET_KEY")
            if not secret_key:
                if os.getenv("ENVIRONMENT", "development") == "production":
                    raise ValueError("JWT_SECRET_KEY must be set in production environment")
                else:
                    from ...utils.logging import warning
                    warning("Using default JWT secret key. Change this in production!")
                    secret_key = "your-secret-key-change-in-production"
            
            config = JWTConfig(
                secret_key=secret_key,
                algorithm=os.getenv("JWT_ALGORITHM", "HS256"),
                access_token_expire_minutes=int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30")),
                refresh_token_expire_days=int(os.getenv("JWT_REFRESH_TOKEN_EXPIRE_DAYS", "7")),
                issuer=os.getenv("JWT_ISSUER", "yemen-market-integration-api"),
                audience=os.getenv("JWT_AUDIENCE", "yemen-market-integration-client")
            )
        self.config = config
        
        # Validate configuration
        if self.config.secret_key == "your-secret-key-change-in-production":
            logger.warning("Using default JWT secret key. Change this in production!")
    
    def create_access_token(
        self,
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create a new access token.
        
        Args:
            data: Token payload data
            expires_delta: Optional custom expiration time
            
        Returns:
            Encoded JWT token
        """
        to_encode = data.copy()
        
        # Set expiration
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(
                minutes=self.config.access_token_expire_minutes
            )
        
        # Add standard claims
        to_encode.update({
            "exp": expire,
            "iat": datetime.now(timezone.utc),
            "iss": self.config.issuer,
            "aud": self.config.audience,
            "type": "access"
        })
        
        # Generate JWT ID for tracking
        import uuid
        to_encode["jti"] = str(uuid.uuid4())
        
        # Encode token
        encoded_jwt = jwt.encode(
            to_encode,
            self.config.secret_key,
            algorithm=self.config.algorithm
        )
        
        logger.info(f"Created access token for user: {data.get('sub')}")
        return encoded_jwt
    
    def create_refresh_token(
        self,
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create a new refresh token.
        
        Args:
            data: Token payload data
            expires_delta: Optional custom expiration time
            
        Returns:
            Encoded JWT refresh token
        """
        to_encode = data.copy()
        
        # Set expiration
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(
                days=self.config.refresh_token_expire_days
            )
        
        # Add standard claims
        to_encode.update({
            "exp": expire,
            "iat": datetime.now(timezone.utc),
            "iss": self.config.issuer,
            "aud": self.config.audience,
            "type": "refresh"
        })
        
        # Generate JWT ID for tracking
        import uuid
        to_encode["jti"] = str(uuid.uuid4())
        
        # Encode token
        encoded_jwt = jwt.encode(
            to_encode,
            self.config.secret_key,
            algorithm=self.config.algorithm
        )
        
        logger.info(f"Created refresh token for user: {data.get('sub')}")
        return encoded_jwt
    
    def verify_token(
        self,
        token: str,
        token_type: str = "access",
        verify_exp: bool = True
    ) -> Optional[TokenData]:
        """
        Verify and decode a JWT token.
        
        Args:
            token: JWT token to verify
            token_type: Expected token type ("access" or "refresh")
            verify_exp: Whether to verify expiration
            
        Returns:
            TokenData if valid, None if invalid
        """
        try:
            # Decode token
            payload = jwt.decode(
                token,
                self.config.secret_key,
                algorithms=[self.config.algorithm],
                issuer=self.config.issuer,
                audience=self.config.audience,
                options={"verify_exp": verify_exp}
            )
            
            # Verify token type
            if payload.get("type") != token_type:
                logger.warning(f"Token type mismatch. Expected {token_type}, got {payload.get('type')}")
                return None
            
            # Create TokenData object
            token_data = TokenData(
                sub=payload.get("sub"),
                username=payload.get("username"),
                email=payload.get("email"),
                roles=payload.get("roles", []),
                permissions=payload.get("permissions", []),
                exp=datetime.fromtimestamp(payload.get("exp")) if payload.get("exp") else None,
                iat=datetime.fromtimestamp(payload.get("iat")) if payload.get("iat") else None,
                jti=payload.get("jti"),
                type=payload.get("type", "access")
            )
            
            return token_data
            
        except jwt.ExpiredSignatureError:
            logger.warning("Token has expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid token: {e}")
            return None
        except Exception as e:
            logger.error(f"Error verifying token: {e}")
            return None
    
    def refresh_access_token(self, refresh_token: str) -> Optional[tuple[str, str]]:
        """
        Use a refresh token to get a new access token.
        
        Args:
            refresh_token: Valid refresh token
            
        Returns:
            Tuple of (new_access_token, new_refresh_token) if valid, None if invalid
        """
        # Verify refresh token
        token_data = self.verify_token(refresh_token, token_type="refresh")
        if not token_data:
            return None
        
        # Create new token data
        new_data = {
            "sub": token_data.sub,
            "username": token_data.username,
            "email": token_data.email,
            "roles": token_data.roles,
            "permissions": token_data.permissions
        }
        
        # Create new tokens
        new_access_token = self.create_access_token(new_data)
        new_refresh_token = self.create_refresh_token(new_data)
        
        logger.info(f"Refreshed tokens for user: {token_data.sub}")
        return new_access_token, new_refresh_token
    
    def revoke_token(self, jti: str) -> None:
        """
        Revoke a token by its JWT ID.
        
        In production, this would add the JTI to a blacklist in Redis/database.
        
        Args:
            jti: JWT ID to revoke
        """
        # TODO: Implement token blacklist in Redis
        logger.info(f"Token revoked: {jti}")


# Module-level convenience functions
_jwt_handler = JWTHandler()

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create an access token."""
    return _jwt_handler.create_access_token(data, expires_delta)

def create_refresh_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create a refresh token."""
    return _jwt_handler.create_refresh_token(data, expires_delta)

def verify_token(token: str, token_type: str = "access") -> Optional[TokenData]:
    """Verify a token."""
    return _jwt_handler.verify_token(token, token_type)