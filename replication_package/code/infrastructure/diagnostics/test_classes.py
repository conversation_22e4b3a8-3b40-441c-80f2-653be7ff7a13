"""
Test class wrappers for econometric tests.

Provides object-oriented interfaces to the functional test implementations.
"""

from typing import Dict, Any, Optional, Tuple
import pandas as pd
import numpy as np

from .test_implementations import (
    wooldridge_serial_correlation,
    pesaran_cd_test,
    modified_wald_heteroskedasticity,
    breusch_pagan_lm_test,
    chow_structural_break_test
)


class EconometricTest:
    """Base class for econometric tests."""
    
    def __init__(self, name: str):
        self.name = name
        
    def run(self, *args, **kwargs) -> Dict[str, Any]:
        """Run the test and return standardized results."""
        raise NotImplementedError


class BreuschPaganLMTest(EconometricTest):
    """Breusch-Pagan LM test for cross-sectional dependence."""
    
    def __init__(self):
        super().__init__("Breusch-Pagan LM Test")
        
    def run(self, residuals: np.ndarray, entity_ids: np.ndarray) -> Dict[str, Any]:
        """Run the Breusch-Pagan LM test."""
        try:
            result = breusch_pagan_lm_test(residuals, entity_ids)
            return {
                'test_name': self.name,
                'statistic': result.get('lm_statistic'),
                'p_value': result.get('p_value'),
                'critical_values': result.get('critical_values'),
                'reject_null': result.get('reject_null', False),
                'interpretation': result.get('interpretation', ''),
                'status': 'success'
            }
        except Exception as e:
            return {
                'test_name': self.name,
                'status': 'failed',
                'error': str(e)
            }


class PesaranCDTest(EconometricTest):
    """Pesaran Cross-sectional Dependence test."""
    
    def __init__(self):
        super().__init__("Pesaran CD Test")
        
    def run(self, residuals: np.ndarray, entity_ids: np.ndarray) -> Dict[str, Any]:
        """Run the Pesaran CD test."""
        try:
            result = pesaran_cd_test(residuals, entity_ids)
            return {
                'test_name': self.name,
                'statistic': result.get('cd_statistic'),
                'p_value': result.get('p_value'),
                'reject_null': result.get('reject_null', False),
                'interpretation': result.get('interpretation', ''),
                'status': 'success'
            }
        except Exception as e:
            return {
                'test_name': self.name,
                'status': 'failed',
                'error': str(e)
            }


class WooldridgeSerialCorrelationTest(EconometricTest):
    """Wooldridge test for serial correlation in panel data."""
    
    def __init__(self):
        super().__init__("Wooldridge Serial Correlation Test")
        
    def run(self, residuals: np.ndarray, entity_ids: np.ndarray, 
           time_ids: np.ndarray) -> Dict[str, Any]:
        """Run the Wooldridge serial correlation test."""
        try:
            result = wooldridge_serial_correlation(residuals, entity_ids, time_ids)
            return {
                'test_name': self.name,
                'statistic': result.get('f_statistic'),
                'p_value': result.get('p_value'),
                'reject_null': result.get('reject_null', False),
                'interpretation': result.get('interpretation', ''),
                'status': 'success'
            }
        except Exception as e:
            return {
                'test_name': self.name,
                'status': 'failed',
                'error': str(e)
            }


class ModifiedWaldTest(EconometricTest):
    """Modified Wald test for heteroskedasticity."""
    
    def __init__(self):
        super().__init__("Modified Wald Test")
        
    def run(self, residuals: np.ndarray, entity_ids: np.ndarray) -> Dict[str, Any]:
        """Run the Modified Wald test."""
        try:
            result = modified_wald_heteroskedasticity(residuals, entity_ids)
            return {
                'test_name': self.name,
                'statistic': result.get('chi2_statistic'),
                'p_value': result.get('p_value'),
                'reject_null': result.get('reject_null', False),
                'interpretation': result.get('interpretation', ''),
                'status': 'success'
            }
        except Exception as e:
            return {
                'test_name': self.name,
                'status': 'failed',
                'error': str(e)
            }


class ChowStructuralBreakTest(EconometricTest):
    """Chow test for structural breaks."""
    
    def __init__(self):
        super().__init__("Chow Structural Break Test")
        
    def run(self, residuals: np.ndarray, X: np.ndarray, 
           break_point: int) -> Dict[str, Any]:
        """Run the Chow structural break test."""
        try:
            result = chow_structural_break_test(residuals, X, break_point)
            return {
                'test_name': self.name,
                'statistic': result.get('f_statistic'),
                'p_value': result.get('p_value'),
                'reject_null': result.get('reject_null', False),
                'interpretation': result.get('interpretation', ''),
                'break_point': break_point,
                'status': 'success'
            }
        except Exception as e:
            return {
                'test_name': self.name,
                'status': 'failed',
                'error': str(e)
            }