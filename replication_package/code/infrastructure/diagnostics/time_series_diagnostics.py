"""Time series diagnostic tests."""

from typing import Any, Dict, Optional
import pandas as pd
import numpy as np

from ..logging import Logger # Corrected import path
from ...core.models.interfaces import Estimation<PERSON><PERSON>ult, DiagnosticResult
from .helpers.time_series_diagnostic_helpers import (
    test_stationarity,
    test_autocorrelation,
    test_heteroskedasticity_ts,
    test_normality,
    test_cointegration_engle_granger,
    test_cointegration_johansen,
    calculate_granger_causality
)

logger = get_logger(__name__)


class TimeSeriesDiagnosticTests:
    """
    A class to run a suite of diagnostic tests for time series models.
    """

    def __init__(self):
        pass

    def run_all_diagnostics(
        self,
        residuals: pd.Series,
        data: pd.DataFrame, # Original data used for estimation
        model_result: EstimationResult,
        lags: int = 10
    ) -> Dict[str, DiagnosticResult]:
        """
        Run all relevant diagnostic tests for time series data.

        Parameters
        ----------
        residuals : pd.Series
            Residuals from the fitted time series model.
        data : pd.DataFrame
            Original data used for estimation (needed for stationarity, cointegration, causality).
        model_result : EstimationResult
            The full estimation result object, containing fitted values, etc.
        lags : int
            Number of lags to use for autocorrelation and heteroskedasticity tests.

        Returns
        -------
        Dict[str, DiagnosticResult]
            A dictionary of diagnostic test results.
        """
        logger.info("Running all time series diagnostic tests...")
        diagnostics: Dict[str, DiagnosticResult] = {}

        # 1. Stationarity Tests (ADF and KPSS) - run on all numeric columns
        if not data.empty and len(data.columns) > 0:
            # Test each numeric column for stationarity
            numeric_cols = data.select_dtypes(include=[np.number]).columns
            stationarity_results_all = {}
            
            for col in numeric_cols[:5]:  # Limit to first 5 columns for performance
                try:
                    series = data[col].dropna()
                    if len(series) > 10:  # Minimum observations needed
                        stationarity_results_all[col] = test_stationarity(series)
                except Exception as e:
                    logger.warning(f"Could not test stationarity for column {col}: {e}")
            
            if stationarity_results_all:
                # Aggregate results
                adf_rejects = sum(1 for r in stationarity_results_all.values() 
                                if r.get('adf', {}).get('reject_null', False))
                kpss_rejects = sum(1 for r in stationarity_results_all.values() 
                                 if r.get('kpss', {}).get('reject_null', False))
                
                # Use first column results as representative
                first_col = list(stationarity_results_all.keys())[0]
                sample_results = stationarity_results_all[first_col]
                
                diagnostics["stationarity"] = DiagnosticResult(
                    test_name="Stationarity Tests (ADF & KPSS)",
                    test_statistic=sample_results.get('adf', {}).get('test_statistic', np.nan),
                    p_value=sample_results.get('adf', {}).get('p_value', np.nan),
                    reject_null=sample_results.get('adf', {}).get('reject_null', False),
                    interpretation=f"Tested {len(stationarity_results_all)} columns: "
                                 f"{adf_rejects}/{len(stationarity_results_all)} reject ADF null (stationary), "
                                 f"{kpss_rejects}/{len(stationarity_results_all)} reject KPSS null (non-stationary)",
                    corrective_action="Consider differencing non-stationary series" if adf_rejects < len(stationarity_results_all)/2 else None,
                    metadata={'detailed_results': stationarity_results_all}
                )
            else:
                diagnostics["stationarity"] = DiagnosticResult(
                    test_name="Stationarity Tests (ADF & KPSS)",
                    test_statistic=np.nan,
                    p_value=np.nan,
                    reject_null=False,
                    interpretation="No numeric columns found for stationarity testing",
                    corrective_action=None
                )
        else:
            diagnostics["stationarity"] = DiagnosticResult(
                test_name="Stationarity Tests (ADF & KPSS)",
                test_statistic=np.nan,
                p_value=np.nan,
                reject_null=False,
                interpretation="Test skipped: Original data not available or empty.",
                corrective_action=None
            )

        # 2. Autocorrelation Test (Ljung-Box)
        if residuals is not None and not residuals.empty:
            autocorr_results = test_autocorrelation(residuals, lags)
            diagnostics["autocorrelation"] = DiagnosticResult(
                test_name="Ljung-Box Test for Autocorrelation",
                test_statistic=autocorr_results.get('ljung_box', {}).get('test_statistic', np.nan),
                p_value=autocorr_results.get('ljung_box', {}).get('p_value', np.nan),
                reject_null=autocorr_results.get('ljung_box', {}).get('reject_null', False),
                interpretation=autocorr_results.get('ljung_box', {}).get('interpretation', 'N/A'),
                corrective_action="Adjust model lag order or use HAC standard errors" if autocorr_results.get('ljung_box', {}).get('reject_null', False) else None
            )
        else:
            diagnostics["autocorrelation"] = DiagnosticResult(
                test_name="Ljung-Box Test for Autocorrelation",
                test_statistic=np.nan,
                p_value=np.nan,
                reject_null=False,
                interpretation="Test skipped: Residuals not available or empty.",
                corrective_action=None
            )

        # 3. Heteroskedasticity Test (ARCH)
        if residuals is not None and not residuals.empty:
            hetero_ts_results = test_heteroskedasticity_ts(residuals, lags)
            diagnostics["heteroskedasticity"] = DiagnosticResult(
                test_name="ARCH Test for Heteroskedasticity",
                test_statistic=hetero_ts_results.get('arch', {}).get('test_statistic', np.nan),
                p_value=hetero_ts_results.get('arch', {}).get('p_value', np.nan),
                reject_null=hetero_ts_results.get('arch', {}).get('reject_null', False),
                interpretation=hetero_ts_results.get('arch', {}).get('interpretation', 'N/A'),
                corrective_action="Use robust standard errors or GARCH models" if hetero_ts_results.get('arch', {}).get('reject_null', False) else None
            )
        else:
            diagnostics["heteroskedasticity"] = DiagnosticResult(
                test_name="ARCH Test for Heteroskedasticity",
                test_statistic=np.nan,
                p_value=np.nan,
                reject_null=False,
                interpretation="Test skipped: Residuals not available or empty.",
                corrective_action=None
            )

        # 4. Normality Test (Jarque-Bera)
        if residuals is not None and not residuals.empty:
            normality_results = test_normality(residuals)
            diagnostics["normality"] = DiagnosticResult(
                test_name="Jarque-Bera Test for Normality",
                test_statistic=normality_results.get('jarque_bera', {}).get('test_statistic', np.nan),
                p_value=normality_results.get('jarque_bera', {}).get('p_value', np.nan),
                reject_null=normality_results.get('jarque_bera', {}).get('reject_null', False),
                interpretation=normality_results.get('jarque_bera', {}).get('interpretation', 'N/A'),
                corrective_action="Consider non-linear transformations or different error distributions" if normality_results.get('jarque_bera', {}).get('reject_null', False) else None
            )
        else:
            diagnostics["normality"] = DiagnosticResult(
                test_name="Jarque-Bera Test for Normality",
                test_statistic=np.nan,
                p_value=np.nan,
                reject_null=False,
                interpretation="Test skipped: Residuals not available or empty.",
                corrective_action=None
            )

        # 5. Cointegration Tests (Engle-Granger and Johansen) - if multiple series in data
        if not data.empty and data.shape[1] >= 2:
            # Engle-Granger (for 2 variables)
            if data.shape[1] == 2:
                eg_results = test_cointegration_engle_granger(data.iloc[:, 0], data.iloc[:, 1])
                diagnostics["engle_granger_cointegration"] = DiagnosticResult(
                    test_name="Engle-Granger Cointegration Test",
                    test_statistic=eg_results.get('engle_granger', {}).get('test_statistic', np.nan),
                    p_value=eg_results.get('engle_granger', {}).get('p_value', np.nan),
                    reject_null=eg_results.get('engle_granger', {}).get('reject_null', False),
                    interpretation=eg_results.get('engle_granger', {}).get('interpretation', 'N/A'),
                    corrective_action="Use VECM if cointegrated" if eg_results.get('engle_granger', {}).get('reject_null', False) else None
                )
            
            # Johansen (for multiple variables)
            johansen_results = test_cointegration_johansen(data)
            diagnostics["johansen_cointegration"] = DiagnosticResult(
                test_name="Johansen Cointegration Test",
                test_statistic=johansen_results.get('johansen', {}).get('trace_statistic', [np.nan])[-1], # Last trace stat
                p_value=np.nan, # Johansen doesn't give single p-value
                reject_null=False,
                interpretation=johansen_results.get('johansen', {}).get('interpretation', 'N/A'),
                corrective_action="Use VECM if cointegrated"
            )
        else:
            diagnostics["cointegration"] = DiagnosticResult(
                test_name="Cointegration Tests",
                test_statistic=np.nan,
                p_value=np.nan,
                reject_null=False,
                interpretation="Test skipped: Insufficient series for cointegration (need >= 2).",
                corrective_action=None
            )

        # 6. Granger Causality Test
        if not data.empty and data.shape[1] >= 2:
            granger_results = calculate_granger_causality(data, lags)
            diagnostics["granger_causality"] = DiagnosticResult(
                test_name="Granger Causality Tests",
                test_statistic=np.nan, # Aggregate statistic not easily available
                p_value=np.nan,
                reject_null=False,
                interpretation="See individual pair results for causality",
                corrective_action="Consider directed relationships in model"
            )
            # Add detailed results if needed
            diagnostics["granger_causality_details"] = granger_results
        else:
            diagnostics["granger_causality"] = DiagnosticResult(
                test_name="Granger Causality Tests",
                test_statistic=np.nan,
                p_value=np.nan,
                reject_null=False,
                interpretation="Test skipped: Insufficient series for Granger causality (need >= 2).",
                corrective_action=None
            )

        logger.info("Time series diagnostic tests complete.")
        return diagnostics
