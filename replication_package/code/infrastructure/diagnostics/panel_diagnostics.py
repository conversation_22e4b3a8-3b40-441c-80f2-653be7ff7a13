"""Panel data diagnostic tests."""

from typing import Any, Dict, List, Optional
import pandas as pd
import numpy as np # Added numpy import

from ..logging import Logger # Corrected import path
from ...core.models.interfaces import EstimationR<PERSON>ult, DiagnosticResult
from .helpers.panel_diagnostic_helpers import (
    test_heteroskedasticity,
    test_serial_correlation,
    test_cross_sectional_dependence,
    test_hausman,
    test_time_fixed_effects,
    test_joint_significance_time_effects,
    test_common_trends
)
from .test_implementations import (
    wooldridge_serial_correlation,
    pesaran_cd_test,
    ips_unit_root_test,
    modified_wald_heteroskedasticity,
    breusch_pagan_lm_test,
    ramsey_reset_test,
    chow_structural_break_test,
    quandt_likelihood_ratio_test
)

logger = get_logger(__name__)

PanelInfo = Dict[str, Any]


class PanelDiagnosticTests:
    """
    A class to run a suite of diagnostic tests for panel data models.
    """

    def __init__(self):
        pass

    def run_all_diagnostics(
        self,
        residuals: pd.Series,
        X: pd.DataFrame, # Exogenous variables from the model
        panel_info: PanelInfo,
        fitted_values: Optional[pd.Series] = None
    ) -> Dict[str, DiagnosticResult]:
        """
        Run all relevant diagnostic tests for panel data.

        Parameters
        ----------
        residuals : pd.Series
            Residuals from the fitted panel model.
        X : pd.DataFrame
            Exogenous variables used in the model.
        panel_info : PanelInfo
            Dictionary containing panel structure information (e.g., 'entity_id', 'time_id', 'N', 'nobs').
        fitted_values : Optional[pd.Series]
            Fitted values from the model, if available.

        Returns
        -------
        Dict[str, DiagnosticResult]
            A dictionary of diagnostic test results.
        """
        logger.info("Running all panel diagnostic tests...")
        diagnostics: Dict[str, DiagnosticResult] = {}

        # 1. Heteroskedasticity Test (Breusch-Pagan or Modified Wald)
        # Using Modified Wald for groupwise heteroskedasticity as it's more specific to panel
        wald_stat, wald_pvalue, wald_rec = modified_wald_heteroskedasticity(
            residuals, panel_info, fitted_values
        )
        diagnostics["heteroskedasticity"] = DiagnosticResult(
            test_name="Modified Wald Test for Heteroskedasticity",
            test_statistic=wald_stat,
            p_value=wald_pvalue,
            reject_null=wald_pvalue < 0.05,
            interpretation=wald_rec,
            corrective_action="Use cluster-robust standard errors" if wald_pvalue < 0.05 else None
        )

        # 2. Serial Correlation Test (Wooldridge)
        wooldridge_stat, wooldridge_pvalue, wooldridge_rec = wooldridge_serial_correlation(
            residuals, panel_info
        )
        diagnostics["serial_correlation"] = DiagnosticResult(
            test_name="Wooldridge Test for Serial Correlation",
            test_statistic=wooldridge_stat,
            p_value=wooldridge_pvalue,
            reject_null=wooldridge_pvalue < 0.05,
            interpretation=wooldridge_rec,
            corrective_action="Use HAC or cluster-robust standard errors" if wooldridge_pvalue < 0.05 else None
        )

        # 3. Cross-sectional Dependence Test (Pesaran CD or Breusch-Pagan LM)
        # Prefer Pesaran CD for large N, small T; Breusch-Pagan LM for small N, large T
        # For a general case, run both or choose based on N/T ratio
        # Here, we'll run Pesaran CD as it's more robust to unbalanced panels
        pesaran_stat, pesaran_pvalue, pesaran_rec = pesaran_cd_test(
            residuals, panel_info
        )
        diagnostics["cross_sectional_dependence"] = DiagnosticResult(
            test_name="Pesaran CD Test for Cross-sectional Dependence",
            test_statistic=pesaran_stat,
            p_value=pesaran_pvalue,
            reject_null=pesaran_pvalue < 0.05,
            interpretation=pesaran_rec,
            corrective_action="Use Driscoll-Kraay standard errors" if pesaran_pvalue < 0.05 else None
        )
        
        # Optional: Breusch-Pagan LM for comparison
        bp_lm_stat, bp_lm_pvalue, bp_lm_rec = breusch_pagan_lm_test(
            residuals, panel_info
        )
        diagnostics["breusch_pagan_lm"] = DiagnosticResult(
            test_name="Breusch-Pagan LM Test for Cross-sectional Dependence",
            test_statistic=bp_lm_stat,
            p_value=bp_lm_pvalue,
            reject_null=bp_lm_pvalue < 0.05,
            interpretation=bp_lm_rec,
            corrective_action="Use Driscoll-Kraay standard errors" if bp_lm_pvalue < 0.05 else None
        )

        # 4. Functional Form Misspecification (Ramsey RESET test)
        if fitted_values is not None and X is not None:
            reset_stat, reset_pvalue, reset_rec = ramsey_reset_test(
                residuals.index.get_level_values(0).to_series().reset_index(drop=True), # Dummy y for patsy
                X.reset_index(drop=True), # X needs to be reset for patsy
                fitted_values.reset_index(drop=True) # Fitted values need to be reset for patsy
            )
            diagnostics["functional_form"] = DiagnosticResult(
                test_name="Ramsey RESET Test",
                test_statistic=reset_stat,
                p_value=reset_pvalue,
                reject_null=reset_pvalue < 0.05,
                interpretation=reset_rec,
                corrective_action="Re-specify model, add non-linear terms" if reset_pvalue < 0.05 else None
            )
        else:
            diagnostics["functional_form"] = DiagnosticResult(
                test_name="Ramsey RESET Test",
                test_statistic=np.nan,
                p_value=np.nan,
                reject_null=False,
                interpretation="Test skipped: Fitted values or X not available",
                corrective_action=None
            )

        # 5. Unit Root Test (Im-Pesaran-Shin for panel unit root)
        # Try to extract dependent variable from X if available
        if X is not None and hasattr(X, 'columns'):
            # Check if we have the original dependent variable
            # This would typically be provided in panel_info
            dep_var_data = panel_info.get('dependent_variable_series', None)
            
            if dep_var_data is not None:
                try:
                    ips_stat, ips_pvalue, ips_interpretation = ips_unit_root_test(
                        dep_var_data, panel_info
                    )
                    diagnostics["panel_unit_root"] = DiagnosticResult(
                        test_name="Im-Pesaran-Shin Panel Unit Root Test",
                        test_statistic=ips_stat,
                        p_value=ips_pvalue,
                        reject_null=ips_pvalue < 0.05,
                        interpretation=ips_interpretation,
                        corrective_action="Consider differencing variables" if ips_pvalue >= 0.05 else None
                    )
                except Exception as e:
                    logger.warning(f"IPS unit root test failed: {e}")
                    diagnostics["panel_unit_root"] = DiagnosticResult(
                        test_name="Im-Pesaran-Shin Panel Unit Root Test",
                        test_statistic=np.nan,
                        p_value=np.nan,
                        reject_null=False,
                        interpretation=f"Test failed: {str(e)}",
                        corrective_action=None
                    )
            else:
                # Try to run on residuals as a proxy (not ideal but informative)
                try:
                    # Reshape residuals for panel structure if needed
                    if isinstance(residuals, pd.Series):
                        resid_data = residuals.values
                    else:
                        resid_data = residuals
                    
                    ips_stat, ips_pvalue, ips_interpretation = ips_unit_root_test(
                        resid_data, panel_info
                    )
                    diagnostics["panel_unit_root"] = DiagnosticResult(
                        test_name="Im-Pesaran-Shin Panel Unit Root Test (on residuals)",
                        test_statistic=ips_stat,
                        p_value=ips_pvalue,
                        reject_null=ips_pvalue < 0.05,
                        interpretation=f"{ips_interpretation} (Note: test run on residuals, not levels)",
                        corrective_action="Residuals appear non-stationary" if ips_pvalue >= 0.05 else None
                    )
                except:
                    diagnostics["panel_unit_root"] = DiagnosticResult(
                        test_name="Im-Pesaran-Shin Panel Unit Root Test",
                        test_statistic=np.nan,
                        p_value=np.nan,
                        reject_null=False,
                        interpretation="Test requires dependent variable series data",
                        corrective_action="Provide dependent_variable_series in panel_info"
                    )
        else:
            diagnostics["panel_unit_root"] = DiagnosticResult(
                test_name="Im-Pesaran-Shin Panel Unit Root Test",
                test_statistic=np.nan,
                p_value=np.nan,
                reject_null=False,
                interpretation="Test requires panel data structure",
                corrective_action=None
            )

        logger.info("Panel diagnostic tests complete.")
        return diagnostics

    def test_structural_breaks(
        self,
        data: pd.DataFrame,
        formula: str,
        entity_col: str = 'market_id',
        time_col: str = 'date',
        known_break_date: Optional[str] = None,
        run_qlr: bool = True
    ) -> Dict[str, DiagnosticResult]:
        """
        Run structural break tests (Chow and Quandt Likelihood Ratio).

        Parameters
        ----------
        data : pd.DataFrame
            Panel data.
        formula : str
            Model formula (e.g., 'price ~ conflict + exchange_rate').
        entity_col : str
            Name of entity column.
        time_col : str
            Name of time column.
        known_break_date : Optional[str]
            A specific date to test for a structural break (for Chow test).
        run_qlr : bool
            Whether to run the Quandt Likelihood Ratio test for unknown break dates.

        Returns
        -------
        Dict[str, DiagnosticResult]
            A dictionary of structural break test results.
        """
        logger.info("Running structural break tests...")
        break_diagnostics: Dict[str, DiagnosticResult] = {}

        # Chow Test (if a known break date is provided)
        if known_break_date:
            chow_stat, chow_pvalue, chow_rec = chow_structural_break_test(
                data, formula, known_break_date, entity_col, time_col
            )
            break_diagnostics["chow_test"] = DiagnosticResult(
                test_name=f"Chow Test at {known_break_date}",
                test_statistic=chow_stat,
                p_value=chow_pvalue,
                reject_null=chow_pvalue < 0.05,
                interpretation=chow_rec,
                corrective_action="Consider regime-specific models" if chow_pvalue < 0.05 else None
            )

        # Quandt Likelihood Ratio Test (for unknown break date)
        if run_qlr:
            qlr_stat, qlr_break_date, qlr_pvalue, qlr_rec = quandt_likelihood_ratio_test(
                data, formula, entity_col=entity_col, time_col=time_col
            )
            break_diagnostics["quandt_likelihood_ratio_test"] = DiagnosticResult(
                test_name="Quandt Likelihood Ratio Test",
                test_statistic=qlr_stat,
                p_value=qlr_pvalue,
                reject_null=qlr_pvalue < 0.05,
                interpretation=f"{qlr_rec} (Potential break around {qlr_break_date})" if not pd.isna(qlr_stat) else qlr_rec,
                corrective_action="Consider regime-specific models or time-varying parameters" if qlr_pvalue < 0.05 else None
            )
        
        logger.info("Structural break tests complete.")
        return break_diagnostics
