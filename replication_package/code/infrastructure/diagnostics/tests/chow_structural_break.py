"""
Chow test for structural break at a known date in panel data.
"""

from typing import <PERSON>ple, List
import pandas as pd
import numpy as np
import statsmodels.api as sm
from scipy import stats
import patsy

from ...logging import Logger

logger = get_logger(__name__)


def chow_structural_break_test(
    data: pd.DataFrame,
    formula: str,
    break_date: str,
    entity_col: str = 'entity',
    time_col: str = 'date'
) -> Tuple[float, float, str]:
    """
    Chow test for structural break at a known date in panel data.
    
    Tests whether regression coefficients are stable before and after a break date.
    
    Parameters
    ----------
    data : pd.DataFrame
        Panel data with entity and time identifiers
    formula : str
        Model formula (e.g., 'price ~ conflict + exchange_rate')
    break_date : str
        Date of potential structural break
    entity_col : str
        Name of entity column
    time_col : str
        Name of time column
        
    Returns
    -------
    Tuple[float, float, str]
        F-statistic, p-value, and recommendation
        
    References
    ----------
    <PERSON>, <PERSON><PERSON> (1960). Tests of equality between sets of coefficients in two 
    linear regressions. Econometrica, 28(3), 591-605.
    """
    logger.info(f"Running Chow test for structural break at {break_date}...")
    
    try:
        # Convert break_date to datetime
        break_date = pd.to_datetime(break_date)
        data[time_col] = pd.to_datetime(data[time_col])
        
        # Split data into two periods
        data_before = data[data[time_col] < break_date].copy()
        data_after = data[data[time_col] >= break_date].copy()
        
        # Check sample sizes
        n1 = len(data_before)
        n2 = len(data_after)
        
        if n1 < 30 or n2 < 30:
            logger.warning(f"Small sample sizes: {n1} obs before, {n2} obs after break")
            if n1 < 10 or n2 < 10:
                return np.nan, np.nan, "Insufficient data for test"
        
        # Fit models for each period
        # Full model (pooled)
        y_full, X_full = patsy.dmatrices(formula, data, return_type='dataframe')
        model_full = sm.OLS(y_full, X_full).fit()
        rss_full = model_full.ssr
        
        # Period 1 model
        y1, X1 = patsy.dmatrices(formula, data_before, return_type='dataframe')
        model1 = sm.OLS(y1, X1).fit()
        rss1 = model1.ssr
        
        # Period 2 model
        y2, X2 = patsy.dmatrices(formula, data_after, return_type='dataframe')
        model2 = sm.OLS(y2, X2).fit()
        rss2 = model2.ssr
        
        # Calculate Chow F-statistic
        k = X_full.shape[1]  # Number of parameters
        rss_restricted = rss_full
        rss_unrestricted = rss1 + rss2
        
        f_stat = ((rss_restricted - rss_unrestricted) / k) / (rss_unrestricted / (n1 + n2 - 2*k))
        
        # Degrees of freedom
        df1 = k
        df2 = n1 + n2 - 2*k
        
        p_value = 1 - stats.f.cdf(f_stat, df1, df2)
        
        logger.info(f"Chow test: F({df1},{df2}) = {f_stat:.4f}, p-value = {p_value:.4f}")
        logger.info(f"Sample sizes: {n1} before, {n2} after break")
        
        coef_change = {}
        for var in model1.params.index:
            if var in model2.params.index:
                change = abs(model2.params[var] - model1.params[var])
                coef_change[var] = change
        
        if coef_change:
            max_change_var = max(coef_change, key=coef_change.get)
            logger.info(f"Largest coefficient change: {max_change_var}")
        
        if p_value <= 0.01:
            recommendation = (
                f"Strong evidence of structural break at {break_date.date()} (p < 0.01). "
                "Consider: (1) Separate models for each period, "
                "(2) Time-varying parameter models, "
                "(3) Interaction terms with period indicators"
            )
        elif p_value <= 0.05:
            recommendation = (
                f"Evidence of structural break at {break_date.date()} (p < 0.05). "
                "Consider allowing parameters to vary across periods."
            )
        elif p_value <= 0.10:
            recommendation = (
                f"Weak evidence of structural break at {break_date.date()} (p < 0.10). "
                "Current specification may be adequate but monitor stability."
            )
        else:
            recommendation = (
                "No significant evidence of structural break. "
                "Parameters appear stable across periods."
            )
        
        return f_stat, p_value, recommendation
        
    except Exception as e:
        logger.error(f"Error in Chow test: {e}")
        return np.nan, np.nan, f"Error: {e}"
