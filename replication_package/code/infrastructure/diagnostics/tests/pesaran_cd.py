"""
<PERSON><PERSON><PERSON> (2004, 2015) test for cross-sectional dependence in panels.
"""

from typing import <PERSON>ple, Any, Dict
import pandas as pd
import numpy as np
from scipy import stats

from ...logging import Logger

logger = get_logger(__name__)

PanelInfo = Dict[str, Any]


def pesaran_cd_test(
    residuals: pd.Series, panel_info: PanelInfo
) -> Tuple[float, float, str]:
    """
    <PERSON><PERSON><PERSON> (2004, 2015) test for cross-sectional dependence in panels.

    Parameters
    ----------
    residuals : pd.Series
        The residuals from the panel model, indexed by entity and time.
    panel_info : PanelInfo
        A dictionary containing panel structure information. (See wooldridge_serial_correlation)

    Returns
    -------
    Tuple[float, float, str]
        Test statistic, p-value, and a recommendation string.
    """
    logger.info("Running Pesaran CD test for cross-sectional dependence...")
    entity_id = panel_info.get("entity_id", "entity")
    time_id = panel_info.get("time_id", "time")
    N = panel_info.get("N")
    T_avg = panel_info.get("nobs") / N if N else 0 # Average T

    if not isinstance(residuals.index, pd.MultiIndex):
        logger.error("Pesaran CD test requires residuals with a MultiIndex (entity, time).")
        return np.nan, np.nan, "Error: Residuals must have MultiIndex."

    if N is None or N < 2 or T_avg < 3: # Need at least 2 entities and some time periods
        logger.warning("Not enough entities or time periods for Pesaran CD test.")
        return np.nan, np.nan, "Not enough data for test (N<2 or T_avg<3)."

    # Reshape residuals to wide format (T x N)
    resid_df = residuals.reset_index()
    try:
        wide_residuals = resid_df.pivot(index=time_id, columns=entity_id, values=residuals.name)
    except Exception as e:
        logger.error(f"Could not pivot residuals for Pesaran CD test: {e}")
        if residuals.name is None: # Try a default name
             wide_residuals = resid_df.pivot(index=time_id, columns=entity_id, values='resid')
        else:
            return np.nan, np.nan, f"Error pivoting data: {e}"


    # Drop entities with all NaNs (if any, due to unbalanced panel)
    wide_residuals = wide_residuals.dropna(axis=1, how='all')

    # Drop time periods where all entities have NaNs
    wide_residuals = wide_residuals.dropna(axis=0, how='all')

    N_actual = wide_residuals.shape[1]
    if N_actual < 2:
        logger.warning("Not enough entities with non-missing residuals for Pesaran CD test.")
        return np.nan, np.nan, "Not enough entities after handling NaNs."

    sum_sqrt_Tij_rho_ij = 0
    actual_pairs_for_cd = 0

    entities = wide_residuals.columns
    for i in range(N_actual):
        for j in range(i + 1, N_actual):
            resid_i = wide_residuals[entities[i]]
            resid_j = wide_residuals[entities[j]]

            common_mask = ~resid_i.isnull() & ~resid_j.isnull()
            T_ij = common_mask.sum()

            if T_ij < 2: # Need at least 2 common obs for correlation
                continue

            rho_ij = np.corrcoef(resid_i[common_mask], resid_j[common_mask])[0, 1]

            if np.isnan(rho_ij):
                logger.warning(f"NaN correlation for pair ({entities[i]}, {entities[j]}). Skipping this pair for CD test.")
                continue

            sum_sqrt_Tij_rho_ij += np.sqrt(T_ij) * rho_ij
            actual_pairs_for_cd +=1

    if actual_pairs_for_cd == 0:
        logger.warning("No valid pairs contributed to the Pesaran CD statistic sum.")
        return np.nan, np.nan, "No valid pairs for CD statistic sum."

    cd_statistic = np.sqrt(2 / (N_actual * (N_actual - 1))) * sum_sqrt_Tij_rho_ij

    p_value = 2 * (1 - stats.norm.cdf(np.abs(cd_statistic)))

    recommendation = ""
    if p_value <= 0.05:
        recommendation = "Evidence of cross-sectional dependence. Consider Driscoll-Kraay standard errors or spatial panel models."
    else:
        recommendation = "No significant evidence of cross-sectional dependence."

    return cd_statistic, p_value, recommendation
