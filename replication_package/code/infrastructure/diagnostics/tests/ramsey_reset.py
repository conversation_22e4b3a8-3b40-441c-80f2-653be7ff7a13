"""
Ramsey RESET test for functional form misspecification.
"""

from typing import <PERSON>ple, List
import pandas as pd
import numpy as np
import statsmodels.api as sm
from scipy import stats

from ...logging import Logger

logger = get_logger(__name__)


def ramsey_reset_test(
    y: pd.Series,
    X: pd.DataFrame,
    fitted_values: pd.Series,
    powers: List[int] = [2, 3]
) -> <PERSON>ple[float, float, str]:
    """
    Ramsey RESET test for functional form misspecification.
    
    Tests whether non-linear combinations of fitted values help explain the 
    dependent variable, which would indicate functional form misspecification.
    
    Parameters
    ----------
    y : pd.Series
        Dependent variable
    X : pd.DataFrame
        Independent variables used in original regression
    fitted_values : pd.Series
        Fitted values from the original model
    powers : List[int], optional
        Powers of fitted values to include (default: [2, 3])
        
    Returns
    -------
    Tuple[float, float, str]
        F-statistic, p-value, and recommendation
        
    References
    ----------
    <PERSON>, <PERSON><PERSON> (1969). Tests for specification errors in classical linear 
    least-squares regression analysis. Journal of the Royal Statistical Society, 
    Series B, 31(2), 350-371.
    """
    logger.info(f"Running Ramsey RESET test with powers {powers}...")
    
    # Validate inputs
    if len(y) != len(X) or len(y) != len(fitted_values):
        logger.error("RESET test: Input dimensions do not match")
        return np.nan, np.nan, "Error: Dimension mismatch"
    
    # Create augmented regression matrix
    X_reset = X.copy()
    
    # Add powers of fitted values
    for power in powers:
        X_reset[f'yhat_power_{power}'] = fitted_values ** power
    
    # Run unrestricted regression
    try:
        # Add constant if not present
        if 'const' not in X_reset.columns and not (X_reset == 1).any(axis=1).all():
            X_reset = sm.add_constant(X_reset)
        
        unrestricted = sm.OLS(y, X_reset).fit()
        
        # Run restricted regression (original model)
        X_restricted = X.copy()
        if 'const' not in X_restricted.columns and not (X_restricted == 1).any(axis=1).all():
            X_restricted = sm.add_constant(X_restricted)
            
        restricted = sm.OLS(y, X_restricted).fit()
        
        # Calculate F-statistic
        r2_unrestricted = unrestricted.rsquared
        r2_restricted = restricted.rsquared
        
        # Number of restrictions (number of powers added)
        q = len(powers)
        # Degrees of freedom
        n = len(y)
        k_unrestricted = X_reset.shape[1]
        
        # F-statistic
        f_stat = ((r2_unrestricted - r2_restricted) / q) / ((1 - r2_unrestricted) / (n - k_unrestricted))
        
        # Calculate p-value
        df1 = q
        df2 = n - k_unrestricted
        p_value = 1 - stats.f.cdf(f_stat, df1, df2)
        
        # Additional diagnostics
        logger.info(f"RESET test: F({df1},{df2}) = {f_stat:.4f}, p-value = {p_value:.4f}")
        logger.info(f"R-squared change: {r2_restricted:.4f} → {r2_unrestricted:.4f}")
        
        # Recommendations
        if p_value <= 0.01:
            recommendation = (
                "Strong evidence of functional form misspecification (p < 0.01). "
                "Consider: (1) Adding quadratic/interaction terms, "
                "(2) Log transformations of variables, "
                "(3) Different model specification"
            )
        elif p_value <= 0.05:
            recommendation = (
                "Evidence of functional form misspecification (p < 0.05). "
                "Review model specification, consider non-linear transformations."
            )
        elif p_value <= 0.10:
            recommendation = (
                "Weak evidence of misspecification (p < 0.10). "
                "Current specification may be adequate but consider robustness checks."
            )
        else:
            recommendation = (
                "No significant evidence of functional form misspecification. "
                "Current model specification appears appropriate."
            )
        
        return f_stat, p_value, recommendation
        
    except Exception as e:
        logger.error(f"Error in RESET test: {e}")
        return np.nan, np.nan, f"Error: {e}"
