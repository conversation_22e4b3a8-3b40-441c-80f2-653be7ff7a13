"""
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (2003) panel unit root test.
"""

from typing import Tu<PERSON>, Any, Dict, Optional, List
import pandas as pd
import numpy as np
from statsmodels.tsa.stattools import adfuller
from scipy import stats

try:
    from linearmodels.panel import IPS
    HAS_LINEARMODELS_UNITROOT = True
except ImportError:
    HAS_LINEARMODELS_UNITROOT = False
    IPS = None

from ...logging import Logger

logger = get_logger(__name__)

PanelInfo = Dict[str, Any]


def ips_unit_root_test(
    series: pd.Series, panel_info: PanelInfo
) -> Tuple[float, float, str]:
    """
    I<PERSON>-<PERSON><PERSON><PERSON>-<PERSON> (2003) panel unit root test.

    Parameters
    ----------
    series : pd.Series
        The series to test for unit roots, indexed by entity and time.
    panel_info : PanelInfo
        A dictionary containing panel structure information.

    Returns
    -------
    Tuple[float, float, str]
        Test statistic, p-value, and a recommendation string.
    """
    logger.info("Running I<PERSON>-<PERSON><PERSON><PERSON>-<PERSON> (IPS) unit root test...")
    entity_id = panel_info.get("entity_id", "entity")
    time_id = panel_info.get("time_id", "time")
    entities = panel_info.get("entities")

    if not isinstance(series.index, pd.MultiIndex):
        logger.error("IPS test requires series with a MultiIndex (entity, time).")
        return np.nan, np.nan, "Error: Series must have MultiIndex."

    if entities is None or len(entities) == 0:
        logger.warning("No entities found for IPS test.")
        return np.nan, np.nan, "No entities for test."

    # Ensure the series is properly named for conversion to DataFrame column
    if series.name is None:
        series = series.rename("value_for_ips")

    # Convert to DataFrame
    df_for_ips = series.to_frame()

    # Check if linearmodels unitroot module is available
    if not HAS_LINEARMODELS_UNITROOT:
        logger.warning("linearmodels.panel.unitroot not available. Using simplified IPS calculation.")
        
        adf_t_stats = []
        valid_entities = 0

        for entity in entities:
            try:
                entity_series = series.xs(entity, level=entity_id).dropna()
                if len(entity_series) < 10:
                    logger.warning(f"Skipping entity {entity} for IPS test: too few observations ({len(entity_series)}).")
                    continue

                adf_result = adfuller(entity_series, regression='c', autolag='AIC')
                adf_t_stats.append(adf_result[0])
                valid_entities +=1
            except KeyError:
                logger.warning(f"Entity {entity} not found in series for IPS test.")
            except Exception as e:
                logger.error(f"Error running ADF for entity {entity} in IPS test: {e}")

        if valid_entities == 0:
            return np.nan, np.nan, "No valid ADF statistics for IPS test."

        t_bar = np.mean(adf_t_stats)

        # Simplified critical values for illustration
        if t_bar < -2.5:
            p_value_approx = 0.01
            recommendation = "Strong evidence against unit roots (simplified test)."
        elif t_bar < -2.0:
            p_value_approx = 0.05
            recommendation = "Evidence against unit roots (simplified test)."
        else:
            p_value_approx = 0.10
            recommendation = "Cannot reject unit roots (simplified test). Consider differencing."

        return t_bar, p_value_approx, recommendation

    try:
        # linearmodels expects the entity to be the first level and time the second.
        # If not, we might need to reorder.
        if df_for_ips.index.names[0] != entity_id or df_for_ips.index.names[1] != time_id:
            logger.info(f"Reordering MultiIndex for linearmodels IPS test: {df_for_ips.index.names} -> ({entity_id}, {time_id})")
            df_for_ips = df_for_ips.reorder_levels([entity_id, time_id])

        ips_test_instance = IPS(df_for_ips[series.name], trend='c', lags=None, test_type='t-stat')

        ips_statistic = ips_test_instance.statistic
        ips_p_value = ips_test_instance.pvalue

        recommendation = ""
        if ips_p_value <= 0.05:
            recommendation = "Evidence against unit roots (some series may be stationary)."
        else:
            recommendation = "Cannot reject unit roots. Consider differencing."

        return ips_statistic, ips_p_value, recommendation

    except Exception as e:
        logger.error(f"Error running ImPesaranShin test using linearmodels: {e}")
        return np.nan, np.nan, f"Error during IPS test execution: {e}"
