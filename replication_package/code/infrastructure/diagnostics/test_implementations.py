"""
Econometric Test Implementations for Panel Data.

This module provides a central interface for various econometric tests
suitable for panel data, designed to work with v2 architecture.
Individual test implementations are located in the 'tests' subdirectory.
"""

from typing import Tuple, Any, Dict, Optional, List
import pandas as pd
import numpy as np

from ..logging import Logger
from .tests.wooldridge import wooldridge_serial_correlation
from .tests.pesaran_cd import pesaran_cd_test
from .tests.ips_unit_root import ips_unit_root_test
from .tests.modified_wald import modified_wald_heteroskedasticity
from .tests.breusch_pagan_lm import breusch_pagan_lm_test
from .tests.ramsey_reset import ramsey_reset_test
from .tests.chow_structural_break import chow_structural_break_test
from .tests.quandt_likelihood_ratio import quandt_likelihood_ratio_test

logger = get_logger(__name__)

PanelInfo = Dict[str, Any]

# Re-export functions for easier access
__all__ = [
    "wooldridge_serial_correlation",
    "pesaran_cd_test",
    "ips_unit_root_test",
    "modified_wald_heteroskedasticity",
    "breusch_pagan_lm_test",
    "ramsey_reset_test",
    "chow_structural_break_test",
    "quandt_likelihood_ratio_test",
]

# The original content of this file (the functions themselves) has been moved
# to the 'tests' subdirectory to adhere to the <300 lines/file> code quality standard.
# This file now serves as an aggregation point.
