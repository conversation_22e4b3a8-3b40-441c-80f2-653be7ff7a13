"""Helper functions for panel diagnostic tests."""

from typing import <PERSON><PERSON>, Any, Dict, Optional, List
import pandas as pd
import numpy as np
from statsmodels.stats.diagnostic import het_breuschpagan
from scipy import stats
from scipy.stats import chi2, f

from ...logging import Logger # Corrected import path

logger = get_logger(__name__)

PanelInfo = Dict[str, Any]


def test_heteroskedasticity(
    residuals: pd.Series,
    fitted: pd.Series
) -> Dict[str, Any]:
    """Breusch-Pagan test for heteroskedasticity."""
    try:
        bp_stat, bp_pvalue, _, _ = het_breuschpagan(
            residuals.values,
            fitted.values.reshape(-1, 1)
        )
        
        return {
            "test_name": "Breusch-Pagan Test",
            "test_statistic": float(bp_stat),
            "p_value": float(bp_pvalue),
            "reject_null": bp_pvalue < 0.05,
            "interpretation": "Heteroskedasticity detected" if bp_pvalue < 0.05 else "Homoskedasticity",
            "corrective_action": "Use robust standard errors" if bp_pvalue < 0.05 else None
        }
    except Exception as e:
        return {
            "test_name": "Breusch-Pagan Test",
            "test_statistic": np.nan,
            "p_value": 1.0,
            "reject_null": False,
            "interpretation": f"Test failed: {str(e)}"
        }


def test_serial_correlation(residuals: pd.Series) -> Dict[str, Any]:
    """Test for serial correlation in panel residuals (simplified Wooldridge)."""
    # Simplified Wooldridge test
    lag_resid = residuals.shift(1)
    valid_idx = ~(residuals.isna() | lag_resid.isna())
    
    if valid_idx.sum() < 10:
        return {
            "test_name": "Serial Correlation Test",
            "test_statistic": np.nan,
            "p_value": 1.0,
            "reject_null": False,
            "interpretation": "Insufficient data for test"
        }
    
    corr = residuals[valid_idx].corr(lag_resid[valid_idx])
    n = valid_idx.sum()
    test_stat = corr * np.sqrt(n - 2) / np.sqrt(1 - corr**2)
    
    p_value = 2 * (1 - stats.t.cdf(abs(test_stat), n - 2))
    
    return {
        "test_name": "Serial Correlation Test",
        "test_statistic": float(test_stat),
        "p_value": float(p_value),
        "reject_null": p_value < 0.05,
        "interpretation": "Serial correlation detected" if p_value < 0.05 else "No serial correlation",
        "corrective_action": "Use HAC standard errors" if p_value < 0.05 else None
    }


def test_cross_sectional_dependence(result: Any) -> Dict[str, Any]:
    """Pesaran CD test for cross-sectional dependence (simplified)."""
    # Simplified implementation
    return {
        "test_name": "Pesaran CD Test",
        "test_statistic": 0.0,
        "p_value": 0.5,
        "reject_null": False,
        "interpretation": "Test not fully implemented",
        "corrective_action": "Use Driscoll-Kraay standard errors if significant"
    }


def test_hausman(
    fe_result: Dict[str, Any],
    re_result: Dict[str, Any]
) -> Dict[str, Any]:
    """Hausman test for fixed vs random effects.
    
    H0: Random effects model is consistent and efficient
    H1: Random effects model is inconsistent, use fixed effects
    """
    try:
        # Extract coefficients and variance-covariance matrices
        beta_fe = np.array(fe_result.get('params', []))
        beta_re = np.array(re_result.get('params', []))
        
        # Ensure same variables in both models
        if len(beta_fe) != len(beta_re):
            return {
                "test_name": "Hausman Test",
                "test_statistic": np.nan,
                "p_value": np.nan,
                "reject_null": False,
                "interpretation": "Cannot compare models with different variables"
            }
        
        # Calculate difference in coefficients
        beta_diff = beta_fe - beta_re
        
        # Get variance-covariance matrices
        var_fe = np.array(fe_result.get('cov', np.eye(len(beta_fe))))
        var_re = np.array(re_result.get('cov', np.eye(len(beta_re))))
        
        # Calculate variance of the difference
        var_diff = var_fe - var_re
        
        # Add small regularization to avoid singular matrix
        var_diff = var_diff + np.eye(len(beta_diff)) * 1e-10
        
        try:
            # Compute Hausman test statistic
            var_diff_inv = np.linalg.inv(var_diff)
            hausman_stat = beta_diff.T @ var_diff_inv @ beta_diff
            
            # Degrees of freedom = number of coefficients
            df = len(beta_diff)
            
            # Calculate p-value from chi-square distribution
            p_value = 1 - chi2.cdf(hausman_stat, df)
            
            reject_null = p_value < 0.05
            
            interpretation = (
                "Use fixed effects model" if reject_null 
                else "Random effects model is appropriate"
            )
            
            return {
                "test_name": "Hausman Test",
                "test_statistic": float(hausman_stat),
                "p_value": float(p_value),
                "reject_null": reject_null,
                "interpretation": interpretation,
                "degrees_of_freedom": df
            }
            
        except np.linalg.LinAlgError:
            return {
                "test_name": "Hausman Test",
                "test_statistic": np.nan,
                "p_value": np.nan,
                "reject_null": False,
                "interpretation": "Variance matrix is singular, test cannot be computed"
            }
            
    except Exception as e:
        return {
            "test_name": "Hausman Test",
            "test_statistic": np.nan,
            "p_value": np.nan,
            "reject_null": False,
            "interpretation": f"Error computing test: {str(e)}"
        }


def test_time_fixed_effects(
    model_result: Any, # linearmodels result object
    panel_info: PanelInfo
) -> Dict[str, Any]:
    """Test joint significance of time fixed effects.
    
    H0: All time fixed effects are jointly zero
    H1: At least one time fixed effect is non-zero
    """
    try:
        # Check if model has time effects
        if not hasattr(model_result, 'params') or not hasattr(model_result, 'model'):
            return {
                "test_name": "Time Fixed Effects Test",
                "test_statistic": np.nan,
                "p_value": np.nan,
                "reject_null": False,
                "interpretation": "Model does not support time effects testing"
            }
        
        # Get time effect parameters (usually prefixed with T. or time.)
        params = model_result.params
        time_params = [p for p in params.index if p.startswith('T.') or p.startswith('time.')]
        
        if not time_params:
            return {
                "test_name": "Time Fixed Effects Test",
                "test_statistic": np.nan,
                "p_value": np.nan,
                "reject_null": False,
                "interpretation": "No time fixed effects found in model"
            }
        
        # Perform F-test for joint significance
        # F = (R'β)' [R(X'X)^(-1)R']^(-1) (R'β) / q
        # where R is restriction matrix, q is number of restrictions
        
        # Get coefficient values for time effects
        time_coefs = params[time_params].values
        q = len(time_coefs)  # number of restrictions
        
        # Get covariance matrix for time effects
        if hasattr(model_result, 'cov'):
            full_cov = model_result.cov
            time_indices = [params.index.get_loc(p) for p in time_params]
            time_cov = full_cov.iloc[time_indices, time_indices].values
        else:
            # Use identity matrix as fallback
            time_cov = np.eye(q)
        
        try:
            # Calculate F-statistic
            time_cov_inv = np.linalg.inv(time_cov + np.eye(q) * 1e-10)
            f_stat = (time_coefs.T @ time_cov_inv @ time_coefs) / q
            
            # Calculate p-value
            # Degrees of freedom: q (numerator), n - k (denominator)
            n = panel_info.get('nobs', 100)
            k = len(params)
            df1, df2 = q, n - k
            
            p_value = 1 - f.cdf(f_stat, df1, df2)
            reject_null = p_value < 0.05
            
            interpretation = (
                "Time fixed effects are jointly significant" if reject_null
                else "Time fixed effects are not jointly significant"
            )
            
            return {
                "test_name": "Time Fixed Effects Test",
                "test_statistic": float(f_stat),
                "p_value": float(p_value),
                "reject_null": reject_null,
                "interpretation": interpretation,
                "n_time_effects": q,
                "degrees_of_freedom": (df1, df2)
            }
            
        except np.linalg.LinAlgError:
            return {
                "test_name": "Time Fixed Effects Test",
                "test_statistic": np.nan,
                "p_value": np.nan,
                "reject_null": False,
                "interpretation": "Covariance matrix is singular"
            }
            
    except Exception as e:
        return {
            "test_name": "Time Fixed Effects Test",
            "test_statistic": np.nan,
            "p_value": np.nan,
            "reject_null": False,
            "interpretation": f"Error computing test: {str(e)}"
        }


def test_joint_significance_time_effects(
    model_result: Any, # linearmodels result object
    panel_info: PanelInfo
) -> Dict[str, Any]:
    """Test joint significance of time fixed effects for two-way FE.
    
    This is essentially the same as test_time_fixed_effects but 
    specifically for two-way fixed effects models.
    """
    # Delegate to the general time fixed effects test
    return test_time_fixed_effects(model_result, panel_info)


def test_common_trends(
    data: pd.DataFrame,
    dependent_var: str,
    treatment_var: str,
    time_var: str
) -> Dict[str, Any]:
    """Test common trends assumption for DiD-type models.
    
    Tests whether treatment and control groups had parallel trends
    before treatment (crucial for difference-in-differences).
    
    H0: Pre-treatment trends are parallel
    H1: Pre-treatment trends are not parallel
    """
    try:
        # Ensure required columns exist
        if not all(col in data.columns for col in [dependent_var, treatment_var, time_var]):
            return {
                "test_name": "Common Trends Test",
                "test_statistic": np.nan,
                "p_value": np.nan,
                "reject_null": False,
                "interpretation": "Required columns not found in data"
            }
        
        # Convert time to numeric if needed
        if not pd.api.types.is_numeric_dtype(data[time_var]):
            data = data.copy()
            data['time_numeric'] = pd.Categorical(data[time_var]).codes
            time_var_numeric = 'time_numeric'
        else:
            time_var_numeric = time_var
        
        # Identify pre-treatment period (assuming treatment=0 is control)
        pre_treatment_data = data[data[treatment_var] == 0].copy()
        
        if len(pre_treatment_data) == 0:
            return {
                "test_name": "Common Trends Test",
                "test_statistic": np.nan,
                "p_value": np.nan,
                "reject_null": False,
                "interpretation": "No pre-treatment data available"
            }
        
        # Create interaction term: time * treatment_group
        # We need to identify which units will eventually be treated
        treated_units = data[data[treatment_var] == 1]['entity'].unique() if 'entity' in data.columns else []
        
        if len(treated_units) == 0:
            # Use a different approach: compare trends across different values of treatment
            # Create treatment group indicator based on maximum treatment value per entity
            if 'entity' in data.columns:
                max_treatment = data.groupby('entity')[treatment_var].max()
                data['treatment_group'] = data['entity'].map(max_treatment)
            else:
                data['treatment_group'] = data[treatment_var]
        else:
            data['treatment_group'] = data['entity'].isin(treated_units).astype(int)
        
        # Run regression: y = α + β1*time + β2*treatment_group + β3*(time*treatment_group) + ε
        # The coefficient on the interaction term tests for differential trends
        
        from statsmodels.formula.api import ols
        
        # Use pre-treatment data only
        pre_data = data[data[treatment_var] == 0].copy()
        
        if len(pre_data) < 10:  # Need sufficient observations
            return {
                "test_name": "Common Trends Test",
                "test_statistic": np.nan,
                "p_value": np.nan,
                "reject_null": False,
                "interpretation": "Insufficient pre-treatment observations"
            }
        
        # Estimate the model
        formula = f"{dependent_var} ~ {time_var_numeric} * treatment_group"
        model = ols(formula, data=pre_data).fit()
        
        # Test the interaction coefficient
        interaction_param = f"{time_var_numeric}:treatment_group"
        if interaction_param in model.params:
            coef = model.params[interaction_param]
            se = model.bse[interaction_param]
            t_stat = coef / se
            p_value = model.pvalues[interaction_param]
            
            reject_null = p_value < 0.05
            
            interpretation = (
                "Pre-treatment trends differ between groups (parallel trends violated)"
                if reject_null else
                "Pre-treatment trends are parallel (assumption satisfied)"
            )
            
            return {
                "test_name": "Common Trends Test",
                "test_statistic": float(t_stat),
                "p_value": float(p_value),
                "reject_null": reject_null,
                "interpretation": interpretation,
                "interaction_coefficient": float(coef),
                "standard_error": float(se)
            }
        else:
            return {
                "test_name": "Common Trends Test",
                "test_statistic": np.nan,
                "p_value": np.nan,
                "reject_null": False,
                "interpretation": "Could not estimate interaction term"
            }
            
    except Exception as e:
        return {
            "test_name": "Common Trends Test",
            "test_statistic": np.nan,
            "p_value": np.nan,
            "reject_null": False,
            "interpretation": f"Error computing test: {str(e)}"
        }
