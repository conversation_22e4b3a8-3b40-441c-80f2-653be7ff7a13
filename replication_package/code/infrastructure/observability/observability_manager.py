"""Unified observability manager integrating metrics, logging, tracing, and error tracking."""

from typing import Dict, Any, Optional, List, Callable
from contextlib import contextmanager
import time
import os
from pathlib import Path

from prometheus_client import CollectorRegistry, generate_latest, CONTENT_TYPE_LATEST
from prometheus_client import push_to_gateway

from .metrics import metrics_collector, REGISTRY
from .business_metrics import business_metrics, BUSINESS_REGISTRY
from .structured_logging import (
    StructuredLogger, configure_structured_logging, 
    set_correlation_id, get_correlation_id, LogAggregator
)
from .tracing import tracing_manager, traced
from .sentry_integration import sentry_manager, track_performance, capture_errors

from ..logging import Logger

logger = get_logger(__name__)


class ObservabilityManager:
    """Unified manager for all observability components."""
    
    def __init__(self,
                 service_name: str = "yemen-market-integration",
                 environment: str = "production",
                 config: Optional[Dict[str, Any]] = None):
        """Initialize observability manager."""
        self.service_name = service_name
        self.environment = environment
        self.config = config or {}
        
        # Initialize components
        self._init_logging()
        self._init_metrics()
        self._init_tracing()
        self._init_error_tracking()
        
        # Create unified registry
        self.registry = CollectorRegistry()
        self.registry.register(REGISTRY)
        self.registry.register(BUSINESS_REGISTRY)
        
        # Log aggregator
        self.log_aggregator = LogAggregator(
            log_dir=Path(self.config.get('log_dir', 'logs'))
        )
        
        logger.info("Observability manager initialized", 
                   service_name=service_name,
                   environment=environment)
    
    def _init_logging(self) -> None:
        """Initialize structured logging."""
        configure_structured_logging(
            environment=self.environment,
            service_name=self.service_name,
            service_version=self.config.get('version', '2.0.0'),
            log_level=self.config.get('log_level', 'INFO'),
            log_file=Path(self.config.get('log_file', 'logs/app.jsonl'))
        )
    
    def _init_metrics(self) -> None:
        """Initialize metrics collection."""
        # Configure Prometheus push gateway if enabled
        if push_gateway := self.config.get('prometheus_push_gateway'):
            self.push_gateway = push_gateway
            self.push_interval = self.config.get('push_interval', 60)
            self._start_metrics_push()
    
    def _init_tracing(self) -> None:
        """Initialize distributed tracing."""
        # Tracing already initialized in tracing.py
        # Just configure endpoints
        if otlp_endpoint := self.config.get('otlp_endpoint'):
            tracing_manager.otlp_endpoint = otlp_endpoint
    
    def _init_error_tracking(self) -> None:
        """Initialize error tracking."""
        # Sentry already initialized if DSN provided
        # Just log status
        if sentry_manager.dsn:
            logger.info("Sentry error tracking enabled")
    
    def _start_metrics_push(self) -> None:
        """Start periodic metrics push to Prometheus gateway."""
        import threading
        
        def push_metrics():
            while True:
                try:
                    push_to_gateway(
                        self.push_gateway,
                        job=self.service_name,
                        registry=self.registry
                    )
                except Exception as e:
                    logger.error("Failed to push metrics", error=str(e))
                
                time.sleep(self.push_interval)
        
        thread = threading.Thread(target=push_metrics, daemon=True)
        thread.start()
    
    def get_logger(self, name: str) -> StructuredLogger:
        """Get a structured logger instance."""
        return StructuredLogger(name)
    
    @contextmanager
    def operation_context(self, 
                         operation_name: str,
                         operation_type: str = "analysis",
                         attributes: Optional[Dict[str, Any]] = None):
        """Context manager for tracking operations across all systems."""
        # Generate correlation ID
        correlation_id = set_correlation_id()
        
        # Create logger with correlation
        op_logger = self.get_logger(operation_name).with_correlation(correlation_id)
        
        # Start timing
        start_time = time.time()
        
        # Create trace span
        with tracing_manager.span(operation_name, attributes) as span:
            # Add to Sentry context
            sentry_manager.add_breadcrumb(
                f"Starting {operation_name}",
                category=operation_type,
                data=attributes or {}
            )
            
            try:
                # Log operation start
                op_logger.info(f"Starting {operation_type} operation",
                             operation_type=operation_type,
                             attributes=attributes)
                
                yield {
                    'correlation_id': correlation_id,
                    'logger': op_logger,
                    'span': span,
                    'start_time': start_time
                }
                
                # Calculate duration
                duration = time.time() - start_time
                
                # Log success
                op_logger.info(f"Completed {operation_type} operation",
                             operation_type=operation_type,
                             duration_seconds=duration,
                             status="success")
                
                # Record metrics
                if operation_type == "analysis":
                    metrics_collector.record_analysis_end(
                        analysis_type=attributes.get('analysis_type', 'unknown'),
                        tier=attributes.get('tier', 'unknown'),
                        duration=duration
                    )
                
            except Exception as e:
                # Calculate duration
                duration = time.time() - start_time
                
                # Log failure
                op_logger.error(f"Failed {operation_type} operation",
                              operation_type=operation_type,
                              duration_seconds=duration,
                              status="error",
                              error=str(e))
                
                # Capture in Sentry
                sentry_manager.capture_exception(e, extras={
                    'operation_name': operation_name,
                    'operation_type': operation_type,
                    'correlation_id': correlation_id,
                    'duration': duration
                })
                
                raise
    
    def track_analysis(self, analysis_id: str, analysis_type: str,
                      commodity: str, tier: Optional[str] = None):
        """Track an analysis operation."""
        return self.operation_context(
            operation_name=f"analysis_{analysis_id}",
            operation_type="analysis",
            attributes={
                'analysis_id': analysis_id,
                'analysis_type': analysis_type,
                'commodity': commodity,
                'tier': tier
            }
        )
    
    def track_data_processing(self, dataset: str, operation: str,
                            source: str):
        """Track data processing operation."""
        return self.operation_context(
            operation_name=f"data_{operation}_{dataset}",
            operation_type="data_processing",
            attributes={
                'dataset': dataset,
                'operation': operation,
                'source': source
            }
        )
    
    def record_sli(self, sli_name: str, value: float, 
                  labels: Optional[Dict[str, str]] = None) -> None:
        """Record Service Level Indicator."""
        # This would integrate with SLO monitoring
        logger.info("SLI recorded",
                   sli_name=sli_name,
                   value=value,
                   labels=labels or {})
    
    def check_slo(self, slo_name: str) -> Dict[str, Any]:
        """Check Service Level Objective status."""
        # Example SLO definitions
        slos = {
            "availability": {
                "target": 0.99,
                "window": "30d",
                "current": self._calculate_availability()
            },
            "analysis_latency": {
                "target": 300,  # seconds
                "window": "7d",
                "current": self._calculate_latency_p95()
            },
            "data_quality": {
                "target": 0.95,
                "window": "24h",
                "current": self._calculate_data_quality()
            }
        }
        
        if slo_name in slos:
            slo = slos[slo_name]
            slo['status'] = 'met' if slo['current'] >= slo['target'] else 'violated'
            slo['error_budget'] = max(0, slo['target'] - slo['current'])
            return slo
        
        return {"error": f"Unknown SLO: {slo_name}"}
    
    def _calculate_availability(self) -> float:
        """Calculate service availability."""
        # Placeholder - would query metrics
        return 0.995
    
    def _calculate_latency_p95(self) -> float:
        """Calculate 95th percentile latency."""
        # Placeholder - would query metrics
        return 250.0
    
    def _calculate_data_quality(self) -> float:
        """Calculate data quality score."""
        # Placeholder - would query metrics
        return 0.97
    
    def get_metrics_endpoint(self) -> bytes:
        """Get Prometheus metrics endpoint data."""
        return generate_latest(self.registry)
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status."""
        return {
            "status": "healthy",
            "service": self.service_name,
            "environment": self.environment,
            "components": {
                "logging": {"status": "healthy"},
                "metrics": {"status": "healthy"},
                "tracing": {"status": "healthy" if tracing_manager.tracer else "disabled"},
                "error_tracking": {"status": "healthy" if sentry_manager.dsn else "disabled"}
            },
            "slos": {
                "availability": self.check_slo("availability"),
                "latency": self.check_slo("analysis_latency"),
                "data_quality": self.check_slo("data_quality")
            }
        }
    
    def create_runbook_link(self, alert_name: str) -> str:
        """Create link to runbook for alert."""
        base_url = self.config.get('runbook_base_url', 
                                  'https://docs.yemen-market.org/runbooks')
        return f"{base_url}/{alert_name}"


# Unified decorators combining all observability
def observable(operation_name: str, operation_type: str = "function"):
    """Decorator combining metrics, tracing, and error tracking."""
    def decorator(func: Callable) -> Callable:
        # Apply all decorators
        func = traced(operation_name)(func)
        func = track_performance(operation_name, operation_type)(func)
        func = capture_errors(capture_args=False)(func)
        
        return func
    
    return decorator


# Global observability manager instance
observability = ObservabilityManager(
    config={
        "log_level": os.environ.get("LOG_LEVEL", "INFO"),
        "otlp_endpoint": os.environ.get("OTLP_ENDPOINT"),
        "prometheus_push_gateway": os.environ.get("PROMETHEUS_PUSH_GATEWAY"),
        "version": "2.0.0"
    }
)