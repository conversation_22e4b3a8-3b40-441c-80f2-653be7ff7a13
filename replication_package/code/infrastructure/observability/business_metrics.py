"""Business-specific metrics for Yemen Market Integration."""

from typing import Dict, Optional, List
from prometheus_client import Counter, Histogram, Gauge, Summary
from prometheus_client import CollectorRegistry

from ..logging import Logger

logger = get_logger(__name__)

# Create registry for business metrics
BUSINESS_REGISTRY = CollectorRegistry()

# Data Quality Metrics
data_coverage_gauge = Gauge(
    'yemen_market_data_coverage_ratio',
    'Ratio of markets with price data',
    ['commodity', 'region', 'data_source'],
    registry=BUSINESS_REGISTRY
)

missing_data_counter = Counter(
    'yemen_market_missing_data_points_total',
    'Count of missing data points',
    ['commodity', 'market', 'reason'],
    registry=BUSINESS_REGISTRY
)

data_freshness_gauge = Gauge(
    'yemen_market_data_freshness_hours',
    'Hours since last data update',
    ['data_source', 'commodity'],
    registry=BUSINESS_REGISTRY
)

# Analysis Metrics
analysis_request_counter = Counter(
    'yemen_market_analysis_requests_total',
    'Total analysis requests',
    ['analysis_type', 'tier', 'commodity', 'status'],
    registry=BUSINESS_REGISTRY
)

analysis_duration_histogram = Histogram(
    'yemen_market_analysis_duration_seconds',
    'Analysis completion time',
    ['analysis_type', 'tier', 'commodity'],
    buckets=[1, 5, 10, 30, 60, 120, 300, 600, 1800],
    registry=BUSINESS_REGISTRY
)

model_convergence_counter = Counter(
    'yemen_market_model_convergence_total',
    'Model convergence results',
    ['model_type', 'commodity', 'converged'],
    registry=BUSINESS_REGISTRY
)

# Market Integration Metrics
market_integration_score = Gauge(
    'yemen_market_integration_score',
    'Market integration score (0-1)',
    ['market_pair', 'commodity', 'period'],
    registry=BUSINESS_REGISTRY
)

price_correlation_gauge = Gauge(
    'yemen_market_price_correlation',
    'Price correlation between markets',
    ['market1', 'market2', 'commodity'],
    registry=BUSINESS_REGISTRY
)

cointegration_test_counter = Counter(
    'yemen_market_cointegration_tests_total',
    'Cointegration test results',
    ['market_pair', 'commodity', 'test_type', 'result'],
    registry=BUSINESS_REGISTRY
)

# Conflict Impact Metrics
conflict_events_gauge = Gauge(
    'yemen_market_conflict_events_count',
    'Number of conflict events',
    ['governorate', 'event_type', 'period'],
    registry=BUSINESS_REGISTRY
)

conflict_impact_score = Gauge(
    'yemen_market_conflict_impact_score',
    'Estimated conflict impact on prices',
    ['market', 'commodity', 'impact_type'],
    registry=BUSINESS_REGISTRY
)

# Exchange Rate Metrics
exchange_rate_gauge = Gauge(
    'yemen_market_exchange_rate',
    'Exchange rate YER/USD',
    ['currency_zone', 'rate_type'],
    registry=BUSINESS_REGISTRY
)

exchange_rate_divergence = Gauge(
    'yemen_market_exchange_rate_divergence_ratio',
    'Divergence between currency zones',
    ['comparison_type'],
    registry=BUSINESS_REGISTRY
)

# Aid Distribution Metrics
aid_distribution_gauge = Gauge(
    'yemen_market_aid_distribution_usd',
    'Aid distribution amount',
    ['governorate', 'aid_type', 'organization'],
    registry=BUSINESS_REGISTRY
)

aid_price_impact = Gauge(
    'yemen_market_aid_price_impact_percent',
    'Estimated aid impact on prices',
    ['market', 'commodity', 'aid_type'],
    registry=BUSINESS_REGISTRY
)

# Policy Impact Metrics
policy_intervention_counter = Counter(
    'yemen_market_policy_interventions_total',
    'Policy intervention events',
    ['intervention_type', 'governorate', 'effectiveness'],
    registry=BUSINESS_REGISTRY
)

welfare_impact_gauge = Gauge(
    'yemen_market_welfare_impact_score',
    'Estimated welfare impact',
    ['population_group', 'metric_type'],
    registry=BUSINESS_REGISTRY
)

# Performance Metrics
computation_time_summary = Summary(
    'yemen_market_computation_time_seconds',
    'Computation time for various operations',
    ['operation_type', 'complexity_level'],
    registry=BUSINESS_REGISTRY
)

memory_usage_gauge = Gauge(
    'yemen_market_memory_usage_mb',
    'Memory usage by component',
    ['component', 'operation'],
    registry=BUSINESS_REGISTRY
)

# Alert Metrics
alert_triggered_counter = Counter(
    'yemen_market_alerts_triggered_total',
    'Alerts triggered',
    ['alert_type', 'severity', 'component'],
    registry=BUSINESS_REGISTRY
)

slo_violation_counter = Counter(
    'yemen_market_slo_violations_total',
    'SLO violations',
    ['slo_type', 'service', 'severity'],
    registry=BUSINESS_REGISTRY
)


class BusinessMetricsCollector:
    """Collector for business-specific metrics."""
    
    def __init__(self):
        """Initialize business metrics collector."""
        self.registry = BUSINESS_REGISTRY
    
    def record_data_coverage(self, commodity: str, region: str, 
                           data_source: str, coverage_ratio: float) -> None:
        """Record data coverage metrics."""
        data_coverage_gauge.labels(
            commodity=commodity,
            region=region,
            data_source=data_source
        ).set(coverage_ratio)
    
    def record_missing_data(self, commodity: str, market: str, reason: str) -> None:
        """Record missing data point."""
        missing_data_counter.labels(
            commodity=commodity,
            market=market,
            reason=reason
        ).inc()
    
    def record_data_freshness(self, data_source: str, commodity: str, 
                            hours_since_update: float) -> None:
        """Record data freshness."""
        data_freshness_gauge.labels(
            data_source=data_source,
            commodity=commodity
        ).set(hours_since_update)
    
    def record_analysis_request(self, analysis_type: str, tier: str,
                              commodity: str, status: str) -> None:
        """Record analysis request."""
        analysis_request_counter.labels(
            analysis_type=analysis_type,
            tier=tier,
            commodity=commodity,
            status=status
        ).inc()
    
    def record_analysis_duration(self, analysis_type: str, tier: str,
                               commodity: str, duration_seconds: float) -> None:
        """Record analysis duration."""
        analysis_duration_histogram.labels(
            analysis_type=analysis_type,
            tier=tier,
            commodity=commodity
        ).observe(duration_seconds)
    
    def record_model_convergence(self, model_type: str, commodity: str,
                               converged: bool) -> None:
        """Record model convergence result."""
        model_convergence_counter.labels(
            model_type=model_type,
            commodity=commodity,
            converged=str(converged)
        ).inc()
    
    def record_market_integration(self, market_pair: str, commodity: str,
                                period: str, score: float) -> None:
        """Record market integration score."""
        market_integration_score.labels(
            market_pair=market_pair,
            commodity=commodity,
            period=period
        ).set(score)
    
    def record_price_correlation(self, market1: str, market2: str,
                               commodity: str, correlation: float) -> None:
        """Record price correlation."""
        price_correlation_gauge.labels(
            market1=market1,
            market2=market2,
            commodity=commodity
        ).set(correlation)
    
    def record_cointegration_test(self, market_pair: str, commodity: str,
                                test_type: str, result: str) -> None:
        """Record cointegration test result."""
        cointegration_test_counter.labels(
            market_pair=market_pair,
            commodity=commodity,
            test_type=test_type,
            result=result
        ).inc()
    
    def record_conflict_events(self, governorate: str, event_type: str,
                             period: str, count: int) -> None:
        """Record conflict events."""
        conflict_events_gauge.labels(
            governorate=governorate,
            event_type=event_type,
            period=period
        ).set(count)
    
    def record_conflict_impact(self, market: str, commodity: str,
                             impact_type: str, score: float) -> None:
        """Record conflict impact score."""
        conflict_impact_score.labels(
            market=market,
            commodity=commodity,
            impact_type=impact_type
        ).set(score)
    
    def record_exchange_rate(self, currency_zone: str, rate_type: str,
                           rate: float) -> None:
        """Record exchange rate."""
        exchange_rate_gauge.labels(
            currency_zone=currency_zone,
            rate_type=rate_type
        ).set(rate)
    
    def record_exchange_divergence(self, comparison_type: str,
                                 divergence_ratio: float) -> None:
        """Record exchange rate divergence."""
        exchange_rate_divergence.labels(
            comparison_type=comparison_type
        ).set(divergence_ratio)
    
    def record_aid_distribution(self, governorate: str, aid_type: str,
                              organization: str, amount_usd: float) -> None:
        """Record aid distribution."""
        aid_distribution_gauge.labels(
            governorate=governorate,
            aid_type=aid_type,
            organization=organization
        ).set(amount_usd)
    
    def record_aid_impact(self, market: str, commodity: str,
                        aid_type: str, impact_percent: float) -> None:
        """Record aid price impact."""
        aid_price_impact.labels(
            market=market,
            commodity=commodity,
            aid_type=aid_type
        ).set(impact_percent)
    
    def record_policy_intervention(self, intervention_type: str,
                                 governorate: str, effectiveness: str) -> None:
        """Record policy intervention."""
        policy_intervention_counter.labels(
            intervention_type=intervention_type,
            governorate=governorate,
            effectiveness=effectiveness
        ).inc()
    
    def record_welfare_impact(self, population_group: str,
                            metric_type: str, score: float) -> None:
        """Record welfare impact."""
        welfare_impact_gauge.labels(
            population_group=population_group,
            metric_type=metric_type
        ).set(score)
    
    def record_computation_time(self, operation_type: str,
                              complexity_level: str, duration_seconds: float) -> None:
        """Record computation time."""
        computation_time_summary.labels(
            operation_type=operation_type,
            complexity_level=complexity_level
        ).observe(duration_seconds)
    
    def record_memory_usage(self, component: str, operation: str,
                          usage_mb: float) -> None:
        """Record memory usage."""
        memory_usage_gauge.labels(
            component=component,
            operation=operation
        ).set(usage_mb)
    
    def record_alert_triggered(self, alert_type: str, severity: str,
                             component: str) -> None:
        """Record alert triggered."""
        alert_triggered_counter.labels(
            alert_type=alert_type,
            severity=severity,
            component=component
        ).inc()
    
    def record_slo_violation(self, slo_type: str, service: str,
                           severity: str) -> None:
        """Record SLO violation."""
        slo_violation_counter.labels(
            slo_type=slo_type,
            service=service,
            severity=severity
        ).inc()


# Global business metrics collector
business_metrics = BusinessMetricsCollector()