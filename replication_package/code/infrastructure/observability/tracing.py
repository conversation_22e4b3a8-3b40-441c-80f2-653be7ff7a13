"""Distributed tracing implementation."""

from typing import Dict, Any, Optional, Callable
import functools
from contextlib import contextmanager
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.resources import Resource
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.httpx import HTTPXClientInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor

from ..logging import Logger

logger = get_logger(__name__)


class TracingManager:
    """Manager for distributed tracing."""
    
    def __init__(self, service_name: str = "yemen-market-integration",
                 otlp_endpoint: Optional[str] = None):
        """Initialize tracing manager."""
        self.service_name = service_name
        self.otlp_endpoint = otlp_endpoint or "localhost:4317"
        self.tracer = None
        self._setup_tracing()
    
    def _setup_tracing(self) -> None:
        """Set up OpenTelemetry tracing."""
        # Create resource
        resource = Resource.create({
            "service.name": self.service_name,
            "service.version": "2.0.0",
            "deployment.environment": "production"
        })
        
        # Set up tracer provider
        provider = TracerProvider(resource=resource)
        
        # Configure OTLP exporter
        otlp_exporter = OTLPSpanExporter(
            endpoint=self.otlp_endpoint,
            insecure=True  # Use False in production with TLS
        )
        
        # Add span processor
        span_processor = BatchSpanProcessor(otlp_exporter)
        provider.add_span_processor(span_processor)
        
        # Set global tracer provider
        trace.set_tracer_provider(provider)
        
        # Get tracer
        self.tracer = trace.get_tracer(__name__)
        
        logger.info(f"Tracing initialized for service: {self.service_name}")
    
    def instrument_fastapi(self, app) -> None:
        """Instrument FastAPI application."""
        FastAPIInstrumentor.instrument_app(app)
        logger.info("FastAPI instrumentation enabled")
    
    def instrument_httpx(self) -> None:
        """Instrument HTTPX client."""
        HTTPXClientInstrumentor().instrument()
        logger.info("HTTPX instrumentation enabled")
    
    def instrument_sqlalchemy(self, engine) -> None:
        """Instrument SQLAlchemy engine."""
        SQLAlchemyInstrumentor().instrument(
            engine=engine,
            service=f"{self.service_name}-db"
        )
        logger.info("SQLAlchemy instrumentation enabled")
    
    def instrument_redis(self) -> None:
        """Instrument Redis client."""
        RedisInstrumentor().instrument()
        logger.info("Redis instrumentation enabled")
    
    @contextmanager
    def span(self, name: str, attributes: Optional[Dict[str, Any]] = None):
        """Create a new span."""
        with self.tracer.start_as_current_span(name) as span:
            if attributes:
                for key, value in attributes.items():
                    span.set_attribute(key, value)
            
            try:
                yield span
            except Exception as e:
                span.record_exception(e)
                span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                raise
    
    def trace(self, name: Optional[str] = None):
        """Decorator for tracing functions."""
        def decorator(func: Callable) -> Callable:
            span_name = name or f"{func.__module__}.{func.__name__}"
            
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                with self.span(span_name, {"function": func.__name__}):
                    return await func(*args, **kwargs)
            
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                with self.span(span_name, {"function": func.__name__}):
                    return func(*args, **kwargs)
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        
        return decorator


# Global tracing manager
import asyncio
tracing_manager = TracingManager()


# Convenience decorators
def traced(name: Optional[str] = None):
    """Decorator for adding tracing to functions."""
    return tracing_manager.trace(name)


# Trace context utilities
def get_current_span() -> Optional[trace.Span]:
    """Get current active span."""
    return trace.get_current_span()


def add_span_attribute(key: str, value: Any) -> None:
    """Add attribute to current span."""
    span = get_current_span()
    if span:
        span.set_attribute(key, value)


def add_span_event(name: str, attributes: Optional[Dict[str, Any]] = None) -> None:
    """Add event to current span."""
    span = get_current_span()
    if span:
        span.add_event(name, attributes or {})


def record_exception(exception: Exception) -> None:
    """Record exception in current span."""
    span = get_current_span()
    if span:
        span.record_exception(exception)
        span.set_status(trace.Status(trace.StatusCode.ERROR, str(exception)))