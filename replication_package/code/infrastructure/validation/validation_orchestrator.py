"""
Validation Orchestrator

This module orchestrates the complete V1/V2 parallel validation process,
coordinating all validation components and generating comprehensive reports.
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

from .parallel_validation import (
    ParallelValidator, ValidationConfiguration, ValidationReport
)
from .performance_benchmarker import (
    PerformanceBenchmarker, BenchmarkConfiguration, BenchmarkReport
)
from .conflict_effect_validator import (
    ConflictEffectValidator, ConflictEffectConfiguration, ConflictValidationReport
)
from .validation_dashboard import ValidationMonitor, DashboardConfiguration
from ...infrastructure.logging import get_logger

logger = get_logger(__name__)


@dataclass
class ValidationSuite:
    """Configuration for complete validation suite."""
    
    # Component configurations
    parallel_validation_config: ValidationConfiguration
    performance_benchmark_config: BenchmarkConfiguration
    conflict_effect_config: ConflictEffectConfiguration
    dashboard_config: DashboardConfiguration
    
    # Orchestration settings
    run_parallel_validation: bool = True
    run_performance_benchmark: bool = True
    run_conflict_validation: bool = True
    enable_real_time_monitoring: bool = True
    
    # Output settings
    output_directory: Path = None
    generate_executive_summary: bool = True
    generate_detailed_reports: bool = True
    save_raw_data: bool = True
    
    # Go/No-Go criteria
    minimum_overall_score: float = 90.0
    minimum_performance_improvement: float = 10.0
    minimum_accuracy_score: float = 95.0
    require_conflict_validation: bool = True
    
    def __post_init__(self):
        if self.output_directory is None:
            self.output_directory = Path("./validation_results")


@dataclass
class ValidationSummary:
    """Summary of all validation results."""
    
    validation_id: str
    timestamp: datetime
    suite_configuration: ValidationSuite
    
    # Component results
    parallel_validation_result: Optional[ValidationReport] = None
    performance_benchmark_result: Optional[BenchmarkReport] = None
    conflict_validation_result: Optional[ConflictValidationReport] = None
    
    # Aggregated metrics
    overall_score: float = 0.0
    performance_improvement_factor: float = 0.0
    numerical_accuracy_percentage: float = 0.0
    conflict_finding_validated: bool = False
    
    # Go/No-Go decision
    passes_go_no_go: bool = False
    critical_blockers: List[str] = None
    warnings: List[str] = None
    recommendations: List[str] = None
    
    # Execution metadata
    total_execution_time: float = 0.0
    components_executed: List[str] = None
    errors_encountered: List[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.critical_blockers is None:
            self.critical_blockers = []
        if self.warnings is None:
            self.warnings = []
        if self.recommendations is None:
            self.recommendations = []
        if self.components_executed is None:
            self.components_executed = []
        if self.errors_encountered is None:
            self.errors_encountered = []


class ValidationOrchestrator:
    """Main orchestrator for V1/V2 validation suite."""
    
    def __init__(self, suite_config: ValidationSuite):
        self.config = suite_config
        self.monitor = ValidationMonitor(suite_config.dashboard_config) if suite_config.enable_real_time_monitoring else None
        
        # Ensure output directory exists
        self.config.output_directory.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.parallel_validator = ParallelValidator(suite_config.parallel_validation_config)
        self.performance_benchmarker = PerformanceBenchmarker(suite_config.performance_benchmark_config)
        self.conflict_validator = ConflictEffectValidator(suite_config.conflict_effect_config)
    
    async def execute_validation_suite(self) -> ValidationSummary:
        """Execute the complete validation suite."""
        validation_id = f"validation_suite_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        logger.info(f"🚀 Starting validation suite: {validation_id}")
        
        start_time = time.time()
        
        # Initialize monitoring
        if self.monitor:
            self.monitor.start_monitoring()
            self.monitor.add_alert("info", "Validation Started", f"Validation suite {validation_id} initiated", "system")
        
        summary = ValidationSummary(
            validation_id=validation_id,
            timestamp=datetime.now(),
            suite_configuration=self.config
        )
        
        try:
            # Execute validation components
            await self._execute_validation_components(summary)
            
            # Aggregate results
            self._aggregate_results(summary)
            
            # Make go/no-go decision
            self._make_go_no_go_decision(summary)
            
            # Generate reports
            await self._generate_reports(summary)
            
            summary.total_execution_time = time.time() - start_time
            
            # Final alert
            if self.monitor:
                severity = "success" if summary.passes_go_no_go else "warning"
                title = "Validation Completed" if summary.passes_go_no_go else "Validation Completed with Issues"
                message = f"Overall score: {summary.overall_score:.1f}%, Go/No-Go: {'PASS' if summary.passes_go_no_go else 'FAIL'}"
                self.monitor.add_alert(severity, title, message, "summary")
                self.monitor.stop_monitoring()
            
            logger.info(f"✅ Validation suite completed: {validation_id}")
            logger.info(f"📊 Overall score: {summary.overall_score:.1f}%")
            logger.info(f"🎯 Go/No-Go: {'PASS' if summary.passes_go_no_go else 'FAIL'}")
            
            return summary
            
        except Exception as e:
            summary.errors_encountered.append(str(e))
            summary.total_execution_time = time.time() - start_time
            
            if self.monitor:
                self.monitor.add_alert("error", "Validation Failed", str(e), "system")
                self.monitor.stop_monitoring()
            
            logger.error(f"❌ Validation suite failed: {e}")
            raise
    
    async def _execute_validation_components(self, summary: ValidationSummary):
        """Execute all validation components."""
        
        # Component 1: Parallel Validation
        if self.config.run_parallel_validation:
            try:
                logger.info("🔄 Running parallel validation...")
                if self.monitor:
                    self.monitor.add_alert("info", "Parallel Validation Started", "Comparing V1 and V2 systems", "parallel_validation")
                
                summary.parallel_validation_result = await self.parallel_validator.execute_parallel_validation()
                summary.components_executed.append("parallel_validation")
                
                if self.monitor:
                    score = summary.parallel_validation_result.overall_score
                    self.monitor.add_alert("info", "Parallel Validation Completed", f"Score: {score:.1f}%", "parallel_validation")
                
            except Exception as e:
                logger.error(f"Parallel validation failed: {e}")
                summary.errors_encountered.append(f"Parallel validation: {e}")
        
        # Component 2: Performance Benchmarking
        if self.config.run_performance_benchmark:
            try:
                logger.info("⚡ Running performance benchmark...")
                if self.monitor:
                    self.monitor.add_alert("info", "Performance Benchmark Started", "Measuring system performance", "performance_benchmark")
                
                v1_path = self.config.parallel_validation_config.v1_data_path
                v2_url = self.config.parallel_validation_config.v2_database_url
                
                summary.performance_benchmark_result = await self.performance_benchmarker.run_comprehensive_benchmark(v1_path, v2_url)
                summary.components_executed.append("performance_benchmark")
                
                if self.monitor:
                    improvement = summary.performance_benchmark_result.overall_improvement
                    self.monitor.add_alert("info", "Performance Benchmark Completed", f"Improvement: {improvement:.1f}x", "performance_benchmark")
                
            except Exception as e:
                logger.error(f"Performance benchmark failed: {e}")
                summary.errors_encountered.append(f"Performance benchmark: {e}")
        
        # Component 3: Conflict Effect Validation
        if self.config.run_conflict_validation:
            try:
                logger.info("🎯 Running conflict effect validation...")
                if self.monitor:
                    self.monitor.add_alert("info", "Conflict Validation Started", "Validating 35% conflict effect finding", "conflict_validation")
                
                v1_path = self.config.parallel_validation_config.v1_data_path
                v2_url = self.config.parallel_validation_config.v2_database_url
                
                summary.conflict_validation_result = await self.conflict_validator.validate_conflict_effect(v1_path, v2_url)
                summary.components_executed.append("conflict_validation")
                
                if self.monitor:
                    validated = summary.conflict_validation_result.finding_validated
                    confidence = summary.conflict_validation_result.confidence_score
                    self.monitor.add_alert(
                        "success" if validated else "warning",
                        "Conflict Validation Completed", 
                        f"Validated: {validated}, Confidence: {confidence:.1f}%", 
                        "conflict_validation"
                    )
                
            except Exception as e:
                logger.error(f"Conflict validation failed: {e}")
                summary.errors_encountered.append(f"Conflict validation: {e}")
    
    def _aggregate_results(self, summary: ValidationSummary):
        """Aggregate results from all validation components."""
        logger.info("📊 Aggregating validation results...")
        
        scores = []
        
        # Aggregate parallel validation
        if summary.parallel_validation_result:
            summary.overall_score = summary.parallel_validation_result.overall_score
            summary.numerical_accuracy_percentage = summary.parallel_validation_result.numerical_accuracy_score
            scores.append(summary.parallel_validation_result.overall_score)
        
        # Aggregate performance benchmark
        if summary.performance_benchmark_result:
            summary.performance_improvement_factor = summary.performance_benchmark_result.overall_improvement
            
            # Convert performance to score (capped at 100%)
            perf_score = min(100, (summary.performance_improvement_factor / self.config.minimum_performance_improvement) * 100)
            scores.append(perf_score)
        
        # Aggregate conflict validation
        if summary.conflict_validation_result:
            summary.conflict_finding_validated = summary.conflict_validation_result.finding_validated
            scores.append(summary.conflict_validation_result.confidence_score)
        
        # Calculate weighted overall score
        if scores:
            summary.overall_score = sum(scores) / len(scores)
        
        logger.info(f"📈 Aggregated overall score: {summary.overall_score:.1f}%")
    
    def _make_go_no_go_decision(self, summary: ValidationSummary):
        """Make go/no-go decision based on validation results."""
        logger.info("🎯 Making go/no-go decision...")
        
        # Check minimum overall score
        if summary.overall_score < self.config.minimum_overall_score:
            summary.critical_blockers.append(f"Overall score {summary.overall_score:.1f}% below minimum {self.config.minimum_overall_score}%")
        
        # Check performance improvement
        if summary.performance_improvement_factor < self.config.minimum_performance_improvement:
            summary.critical_blockers.append(f"Performance improvement {summary.performance_improvement_factor:.1f}x below target {self.config.minimum_performance_improvement}x")
        
        # Check numerical accuracy
        if summary.numerical_accuracy_percentage < self.config.minimum_accuracy_score:
            summary.critical_blockers.append(f"Numerical accuracy {summary.numerical_accuracy_percentage:.1f}% below minimum {self.config.minimum_accuracy_score}%")
        
        # Check conflict validation if required
        if self.config.require_conflict_validation and not summary.conflict_finding_validated:
            summary.critical_blockers.append("Critical conflict effect finding not validated")
        
        # Check for execution errors
        if summary.errors_encountered:
            summary.critical_blockers.append(f"Execution errors: {len(summary.errors_encountered)} components failed")
        
        # Make decision
        summary.passes_go_no_go = len(summary.critical_blockers) == 0
        
        # Generate recommendations
        if summary.passes_go_no_go:
            summary.recommendations.append("✅ V2 system validated for production deployment")
            summary.recommendations.append("All validation criteria met successfully")
        else:
            summary.recommendations.append("❌ V2 system not ready for production deployment")
            summary.recommendations.append("Address critical blockers before proceeding")
        
        # Add specific recommendations based on results
        if summary.performance_improvement_factor < self.config.minimum_performance_improvement:
            summary.recommendations.append("Optimize V2 system performance to meet improvement targets")
        
        if summary.numerical_accuracy_percentage < 100:
            summary.recommendations.append("Investigate and resolve numerical discrepancies")
        
        if not summary.conflict_finding_validated:
            summary.recommendations.append("Validate conflict effect finding before publication")
        
        # Warnings for near-misses
        if summary.overall_score < self.config.minimum_overall_score + 5:
            summary.warnings.append("Overall score close to minimum threshold")
        
        if summary.performance_improvement_factor < self.config.minimum_performance_improvement * 1.2:
            summary.warnings.append("Performance improvement close to minimum target")
        
        logger.info(f"🎯 Go/No-Go decision: {'PASS' if summary.passes_go_no_go else 'FAIL'}")
        if summary.critical_blockers:
            logger.warning(f"❌ Critical blockers: {len(summary.critical_blockers)}")
            for blocker in summary.critical_blockers:
                logger.warning(f"  - {blocker}")
    
    async def _generate_reports(self, summary: ValidationSummary):
        """Generate validation reports."""
        logger.info("📄 Generating validation reports...")
        
        # Save summary
        summary_file = self.config.output_directory / f"validation_summary_{summary.validation_id}.json"
        with open(summary_file, 'w') as f:
            json.dump(asdict(summary), f, indent=2, default=str)
        
        # Generate executive summary if requested
        if self.config.generate_executive_summary:
            await self._generate_executive_summary(summary)
        
        # Generate detailed reports if requested
        if self.config.generate_detailed_reports:
            await self._generate_detailed_reports(summary)
        
        logger.info(f"📄 Reports saved to: {self.config.output_directory}")
    
    async def _generate_executive_summary(self, summary: ValidationSummary):
        """Generate executive summary report."""
        
        executive_summary = f"""
# V1/V2 Validation Executive Summary

**Validation ID:** {summary.validation_id}  
**Date:** {summary.timestamp.strftime('%Y-%m-%d %H:%M:%S')}  
**Duration:** {summary.total_execution_time:.1f} seconds

## 🎯 Go/No-Go Decision: {'✅ PASS' if summary.passes_go_no_go else '❌ FAIL'}

## 📊 Key Metrics

| Metric | Value | Target | Status |
|--------|--------|--------|--------|
| Overall Score | {summary.overall_score:.1f}% | {self.config.minimum_overall_score}% | {'✅' if summary.overall_score >= self.config.minimum_overall_score else '❌'} |
| Performance Improvement | {summary.performance_improvement_factor:.1f}x | {self.config.minimum_performance_improvement}x | {'✅' if summary.performance_improvement_factor >= self.config.minimum_performance_improvement else '❌'} |
| Numerical Accuracy | {summary.numerical_accuracy_percentage:.1f}% | {self.config.minimum_accuracy_score}% | {'✅' if summary.numerical_accuracy_percentage >= self.config.minimum_accuracy_score else '❌'} |
| Conflict Finding Validated | {'Yes' if summary.conflict_finding_validated else 'No'} | Required | {'✅' if summary.conflict_finding_validated else '❌'} |

## 🚨 Critical Issues

{chr(10).join(f"- {blocker}" for blocker in summary.critical_blockers) if summary.critical_blockers else "None"}

## ⚠️ Warnings

{chr(10).join(f"- {warning}" for warning in summary.warnings) if summary.warnings else "None"}

## 💡 Recommendations

{chr(10).join(f"- {rec}" for rec in summary.recommendations)}

## 📈 Component Results

### Parallel Validation
{f"- Overall Score: {summary.parallel_validation_result.overall_score:.1f}%" if summary.parallel_validation_result else "- Not executed"}
{f"- Numerical Accuracy: {summary.parallel_validation_result.numerical_accuracy_score:.1f}%" if summary.parallel_validation_result else ""}

### Performance Benchmark  
{f"- Performance Improvement: {summary.performance_benchmark_result.overall_improvement:.1f}x" if summary.performance_benchmark_result else "- Not executed"}
{f"- Meets Targets: {summary.performance_benchmark_result.meets_targets}" if summary.performance_benchmark_result else ""}

### Conflict Effect Validation
{f"- Finding Validated: {summary.conflict_validation_result.finding_validated}" if summary.conflict_validation_result else "- Not executed"}
{f"- Confidence Score: {summary.conflict_validation_result.confidence_score:.1f}%" if summary.conflict_validation_result else ""}

## 🎬 Next Steps

{'**Ready for Production:** V2 system meets all validation criteria and is ready for deployment.' if summary.passes_go_no_go else '**Not Ready:** Address critical blockers before considering production deployment.'}

---
*Generated by Yemen Market Integration V2 Validation Suite*
"""
        
        exec_summary_file = self.config.output_directory / f"executive_summary_{summary.validation_id}.md"
        with open(exec_summary_file, 'w') as f:
            f.write(executive_summary)
    
    async def _generate_detailed_reports(self, summary: ValidationSummary):
        """Generate detailed component reports."""
        
        # Save individual component results
        if summary.parallel_validation_result:
            parallel_file = self.config.output_directory / f"parallel_validation_{summary.validation_id}.json"
            with open(parallel_file, 'w') as f:
                json.dump(asdict(summary.parallel_validation_result), f, indent=2, default=str)
        
        if summary.performance_benchmark_result:
            benchmark_file = self.config.output_directory / f"performance_benchmark_{summary.validation_id}.json"
            with open(benchmark_file, 'w') as f:
                json.dump(asdict(summary.performance_benchmark_result), f, indent=2, default=str)
        
        if summary.conflict_validation_result:
            conflict_file = self.config.output_directory / f"conflict_validation_{summary.validation_id}.json"
            with open(conflict_file, 'w') as f:
                json.dump(asdict(summary.conflict_validation_result), f, indent=2, default=str)


def create_default_validation_suite(v1_data_path: Path, v2_database_url: str, 
                                   output_directory: Optional[Path] = None) -> ValidationSuite:
    """Create a validation suite with default configurations."""
    
    if output_directory is None:
        output_directory = Path("./validation_results")
    
    # Parallel validation config
    parallel_config = ValidationConfiguration(
        v1_data_path=v1_data_path,
        v2_database_url=v2_database_url,
        v1_analysis_path=v1_data_path.parent / "results",
        output_path=output_directory / "parallel_validation",
        numerical_tolerance=0.001,
        performance_improvement_target=10.0
    )
    
    # Performance benchmark config
    benchmark_config = BenchmarkConfiguration(
        test_iterations=3,
        test_data_sizes=[1000, 5000, 10000, 25000],
        enable_profiling=True
    )
    
    # Conflict effect config
    conflict_config = ConflictEffectConfiguration(
        coefficient_tolerance=0.05,
        effect_size_tolerance=0.02,
        expected_effect_magnitude=-0.35
    )
    
    # Dashboard config
    dashboard_config = DashboardConfiguration(
        refresh_interval_seconds=5,
        enable_alerts=True
    )
    
    return ValidationSuite(
        parallel_validation_config=parallel_config,
        performance_benchmark_config=benchmark_config,
        conflict_effect_config=conflict_config,
        dashboard_config=dashboard_config,
        output_directory=output_directory,
        minimum_overall_score=90.0,
        minimum_performance_improvement=10.0,
        minimum_accuracy_score=95.0,
        require_conflict_validation=True
    )


async def run_validation_suite(suite_config: ValidationSuite) -> ValidationSummary:
    """Run the complete validation suite."""
    orchestrator = ValidationOrchestrator(suite_config)
    return await orchestrator.execute_validation_suite()


if __name__ == "__main__":
    # Example usage
    import asyncio
    
    v1_path = Path("/Users/<USER>/Documents/GitHub/yemen-market-integration/data")
    v2_url = "postgresql://user:pass@localhost:5432/yemen_market_v2"
    output_dir = Path("./validation_results")
    
    suite = create_default_validation_suite(v1_path, v2_url, output_dir)
    summary = asyncio.run(run_validation_suite(suite))
    
    print(f"🎯 Validation suite completed: {summary.validation_id}")
    print(f"📊 Overall score: {summary.overall_score:.1f}%")
    print(f"🚀 Go/No-Go: {'PASS' if summary.passes_go_no_go else 'FAIL'}")
    
    if summary.critical_blockers:
        print("\n❌ Critical blockers:")
        for blocker in summary.critical_blockers:
            print(f"  - {blocker}")
    
    print(f"\n📄 Reports saved to: {suite.output_directory}")