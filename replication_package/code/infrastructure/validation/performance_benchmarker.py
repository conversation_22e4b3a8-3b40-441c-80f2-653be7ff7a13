"""
Performance Benchmarking System for V1/V2 Comparison

This module provides detailed performance analysis and benchmarking capabilities
for comparing V1 and V2 system performance across multiple dimensions.
"""

import asyncio
import time
import psutil
import tracemalloc
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import json
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import logging

from ...infrastructure.logging import get_logger

logger = get_logger(__name__)


@dataclass
class BenchmarkConfiguration:
    """Configuration for performance benchmarking."""
    
    # Test parameters
    test_iterations: int = 5
    warmup_iterations: int = 2
    timeout_seconds: int = 1800  # 30 minutes
    
    # Data volumes for testing
    test_data_sizes: List[int] = None  # [1000, 5000, 10000, 25000]
    
    # Resource monitoring
    monitor_memory: bool = True
    monitor_cpu: bool = True
    monitor_io: bool = True
    monitor_network: bool = False
    
    # Profiling options
    enable_profiling: bool = True
    profile_memory: bool = True
    profile_cpu: bool = True
    
    # Output settings
    save_detailed_logs: bool = True
    generate_visualizations: bool = True
    
    def __post_init__(self):
        if self.test_data_sizes is None:
            self.test_data_sizes = [1000, 5000, 10000, 25000]


@dataclass
class ResourceUsage:
    """System resource usage metrics."""
    
    timestamp: datetime
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    io_read_bytes: int
    io_write_bytes: int
    io_operations: int
    network_bytes_sent: Optional[int] = None
    network_bytes_recv: Optional[int] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class BenchmarkResult:
    """Results from a single benchmark run."""
    
    test_name: str
    system_version: str  # 'v1' or 'v2'
    data_size: int
    iteration: int
    
    # Timing metrics
    execution_time: float
    initialization_time: float
    processing_time: float
    finalization_time: float
    
    # Resource metrics
    peak_memory_mb: float
    avg_cpu_percent: float
    total_io_bytes: int
    
    # Throughput metrics
    records_per_second: float
    mbytes_per_second: float
    
    # Quality metrics
    success: bool
    error_message: Optional[str] = None
    
    # Additional metrics
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class ComparisonAnalysis:
    """Analysis comparing V1 and V2 performance."""
    
    metric_name: str
    v1_mean: float
    v1_std: float
    v2_mean: float
    v2_std: float
    improvement_factor: float  # v1/v2 ratio
    improvement_percentage: float
    statistical_significance: bool
    confidence_interval: Tuple[float, float]


@dataclass
class BenchmarkReport:
    """Comprehensive benchmark report."""
    
    benchmark_id: str
    timestamp: datetime
    configuration: BenchmarkConfiguration
    
    # Raw results
    v1_results: List[BenchmarkResult]
    v2_results: List[BenchmarkResult]
    
    # Analysis
    performance_analysis: List[ComparisonAnalysis]
    scalability_analysis: Dict[str, Any]
    resource_efficiency: Dict[str, Any]
    
    # Summary
    overall_improvement: float
    meets_targets: bool
    recommendations: List[str]
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class ResourceMonitor:
    """Monitor system resource usage during benchmarks."""
    
    def __init__(self, monitoring_interval: float = 0.1):
        self.monitoring_interval = monitoring_interval
        self.monitoring = False
        self.measurements: List[ResourceUsage] = []
        self.process = None
        
    def start_monitoring(self, process_id: Optional[int] = None):
        """Start monitoring system resources."""
        self.monitoring = True
        self.measurements = []
        
        if process_id:
            self.process = psutil.Process(process_id)
        else:
            self.process = psutil.Process()
        
        # Start monitoring in background
        asyncio.create_task(self._monitor_loop())
        
    def stop_monitoring(self) -> List[ResourceUsage]:
        """Stop monitoring and return measurements."""
        self.monitoring = False
        return self.measurements.copy()
    
    async def _monitor_loop(self):
        """Background monitoring loop."""
        while self.monitoring:
            try:
                # Get system metrics
                cpu_percent = psutil.cpu_percent(interval=None)
                memory = psutil.virtual_memory()
                
                # Get process-specific metrics if available
                if self.process:
                    try:
                        process_memory = self.process.memory_info()
                        process_cpu = self.process.cpu_percent()
                        io_counters = self.process.io_counters()
                        
                        usage = ResourceUsage(
                            timestamp=datetime.now(),
                            cpu_percent=process_cpu,
                            memory_mb=process_memory.rss / 1024 / 1024,
                            memory_percent=process_memory.rss / memory.total * 100,
                            io_read_bytes=io_counters.read_bytes,
                            io_write_bytes=io_counters.write_bytes,
                            io_operations=io_counters.read_count + io_counters.write_count
                        )
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        # Process might have ended
                        usage = ResourceUsage(
                            timestamp=datetime.now(),
                            cpu_percent=cpu_percent,
                            memory_mb=memory.used / 1024 / 1024,
                            memory_percent=memory.percent,
                            io_read_bytes=0,
                            io_write_bytes=0,
                            io_operations=0
                        )
                else:
                    usage = ResourceUsage(
                        timestamp=datetime.now(),
                        cpu_percent=cpu_percent,
                        memory_mb=memory.used / 1024 / 1024,
                        memory_percent=memory.percent,
                        io_read_bytes=0,
                        io_write_bytes=0,
                        io_operations=0
                    )
                
                self.measurements.append(usage)
                
            except Exception as e:
                logger.warning(f"Resource monitoring error: {e}")
            
            await asyncio.sleep(self.monitoring_interval)


class V1Benchmarker:
    """Benchmarking adapter for V1 system."""
    
    def __init__(self, data_path: Path):
        self.data_path = data_path
        
    async def run_benchmark(self, test_name: str, data_size: int, iteration: int) -> BenchmarkResult:
        """Run V1 benchmark for specified test and data size."""
        logger.info(f"Running V1 benchmark: {test_name}, size: {data_size}, iteration: {iteration}")
        
        # Initialize resource monitoring
        monitor = ResourceMonitor()
        monitor.start_monitoring()
        
        # Start memory profiling
        tracemalloc.start()
        
        try:
            start_time = time.time()
            
            # Initialize V1 system
            init_start = time.time()
            await self._initialize_v1_system()
            init_time = time.time() - init_start
            
            # Process data
            process_start = time.time()
            await self._process_v1_data(data_size)
            process_time = time.time() - process_start
            
            # Finalize
            final_start = time.time()
            await self._finalize_v1_processing()
            final_time = time.time() - final_start
            
            total_time = time.time() - start_time
            
            # Get memory usage
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            # Stop resource monitoring
            resource_measurements = monitor.stop_monitoring()
            
            # Calculate metrics
            avg_cpu = np.mean([m.cpu_percent for m in resource_measurements]) if resource_measurements else 0
            peak_memory = peak / 1024 / 1024  # Convert to MB
            total_io = sum(m.io_read_bytes + m.io_write_bytes for m in resource_measurements)
            
            records_per_sec = data_size / total_time if total_time > 0 else 0
            mbytes_per_sec = (total_io / 1024 / 1024) / total_time if total_time > 0 else 0
            
            return BenchmarkResult(
                test_name=test_name,
                system_version='v1',
                data_size=data_size,
                iteration=iteration,
                execution_time=total_time,
                initialization_time=init_time,
                processing_time=process_time,
                finalization_time=final_time,
                peak_memory_mb=peak_memory,
                avg_cpu_percent=avg_cpu,
                total_io_bytes=total_io,
                records_per_second=records_per_sec,
                mbytes_per_second=mbytes_per_sec,
                success=True
            )
            
        except Exception as e:
            logger.error(f"V1 benchmark failed: {e}")
            tracemalloc.stop()
            monitor.stop_monitoring()
            
            return BenchmarkResult(
                test_name=test_name,
                system_version='v1',
                data_size=data_size,
                iteration=iteration,
                execution_time=0,
                initialization_time=0,
                processing_time=0,
                finalization_time=0,
                peak_memory_mb=0,
                avg_cpu_percent=0,
                total_io_bytes=0,
                records_per_second=0,
                mbytes_per_second=0,
                success=False,
                error_message=str(e)
            )
    
    async def _initialize_v1_system(self):
        """Initialize V1 system for benchmarking."""
        # Mock V1 initialization - replace with actual V1 system startup
        await asyncio.sleep(0.1)  # Simulate initialization time
        
    async def _process_v1_data(self, data_size: int):
        """Process data using V1 system."""
        # Mock V1 data processing - replace with actual V1 processing
        # Simulate processing time based on data size
        processing_time = data_size / 210  # Based on 210 records/second baseline
        await asyncio.sleep(processing_time)
        
    async def _finalize_v1_processing(self):
        """Finalize V1 processing."""
        await asyncio.sleep(0.05)  # Simulate finalization time


class V2Benchmarker:
    """Benchmarking adapter for V2 system."""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        
    async def run_benchmark(self, test_name: str, data_size: int, iteration: int) -> BenchmarkResult:
        """Run V2 benchmark for specified test and data size."""
        logger.info(f"Running V2 benchmark: {test_name}, size: {data_size}, iteration: {iteration}")
        
        # Initialize resource monitoring
        monitor = ResourceMonitor()
        monitor.start_monitoring()
        
        # Start memory profiling
        tracemalloc.start()
        
        try:
            start_time = time.time()
            
            # Initialize V2 system
            init_start = time.time()
            await self._initialize_v2_system()
            init_time = time.time() - init_start
            
            # Process data
            process_start = time.time()
            await self._process_v2_data(data_size)
            process_time = time.time() - process_start
            
            # Finalize
            final_start = time.time()
            await self._finalize_v2_processing()
            final_time = time.time() - final_start
            
            total_time = time.time() - start_time
            
            # Get memory usage
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            # Stop resource monitoring
            resource_measurements = monitor.stop_monitoring()
            
            # Calculate metrics
            avg_cpu = np.mean([m.cpu_percent for m in resource_measurements]) if resource_measurements else 0
            peak_memory = peak / 1024 / 1024  # Convert to MB
            total_io = sum(m.io_read_bytes + m.io_write_bytes for m in resource_measurements)
            
            records_per_sec = data_size / total_time if total_time > 0 else 0
            mbytes_per_sec = (total_io / 1024 / 1024) / total_time if total_time > 0 else 0
            
            return BenchmarkResult(
                test_name=test_name,
                system_version='v2',
                data_size=data_size,
                iteration=iteration,
                execution_time=total_time,
                initialization_time=init_time,
                processing_time=process_time,
                finalization_time=final_time,
                peak_memory_mb=peak_memory,
                avg_cpu_percent=avg_cpu,
                total_io_bytes=total_io,
                records_per_second=records_per_sec,
                mbytes_per_second=mbytes_per_sec,
                success=True
            )
            
        except Exception as e:
            logger.error(f"V2 benchmark failed: {e}")
            tracemalloc.stop()
            monitor.stop_monitoring()
            
            return BenchmarkResult(
                test_name=test_name,
                system_version='v2',
                data_size=data_size,
                iteration=iteration,
                execution_time=0,
                initialization_time=0,
                processing_time=0,
                finalization_time=0,
                peak_memory_mb=0,
                avg_cpu_percent=0,
                total_io_bytes=0,
                records_per_second=0,
                mbytes_per_second=0,
                success=False,
                error_message=str(e)
            )
    
    async def _initialize_v2_system(self):
        """Initialize V2 system for benchmarking."""
        # Mock V2 initialization - replace with actual V2 system startup
        await asyncio.sleep(0.02)  # Faster initialization
        
    async def _process_v2_data(self, data_size: int):
        """Process data using V2 system."""
        # Mock V2 data processing - replace with actual V2 processing
        # Simulate 10x performance improvement
        processing_time = data_size / 2100  # Based on 2100 records/second target
        await asyncio.sleep(processing_time)
        
    async def _finalize_v2_processing(self):
        """Finalize V2 processing."""
        await asyncio.sleep(0.01)  # Faster finalization


class PerformanceBenchmarker:
    """Main performance benchmarking orchestrator."""
    
    def __init__(self, config: BenchmarkConfiguration):
        self.config = config
        
    async def run_comprehensive_benchmark(self, v1_data_path: Path, v2_database_url: str) -> BenchmarkReport:
        """Run comprehensive performance benchmark comparing V1 and V2."""
        benchmark_id = f"benchmark_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        logger.info(f"Starting comprehensive benchmark: {benchmark_id}")
        
        v1_benchmarker = V1Benchmarker(v1_data_path)
        v2_benchmarker = V2Benchmarker(v2_database_url)
        
        # Test scenarios
        test_scenarios = [
            "data_loading",
            "panel_creation", 
            "analysis_execution",
            "result_generation"
        ]
        
        v1_results = []
        v2_results = []
        
        # Run benchmarks for each scenario and data size
        for test_name in test_scenarios:
            for data_size in self.config.test_data_sizes:
                logger.info(f"Benchmarking {test_name} with {data_size} records")
                
                # Run warmup iterations
                for warmup in range(self.config.warmup_iterations):
                    await v1_benchmarker.run_benchmark(f"{test_name}_warmup", data_size, warmup)
                    await v2_benchmarker.run_benchmark(f"{test_name}_warmup", data_size, warmup)
                
                # Run actual test iterations
                for iteration in range(self.config.test_iterations):
                    # Run V1 and V2 benchmarks
                    v1_result = await v1_benchmarker.run_benchmark(test_name, data_size, iteration)
                    v2_result = await v2_benchmarker.run_benchmark(test_name, data_size, iteration)
                    
                    v1_results.append(v1_result)
                    v2_results.append(v2_result)
                    
                    # Small delay between iterations
                    await asyncio.sleep(1)
        
        # Analyze results
        performance_analysis = self._analyze_performance(v1_results, v2_results)
        scalability_analysis = self._analyze_scalability(v1_results, v2_results)
        resource_efficiency = self._analyze_resource_efficiency(v1_results, v2_results)
        
        # Calculate overall improvement
        overall_improvement = self._calculate_overall_improvement(performance_analysis)
        
        # Check if targets are met
        meets_targets = overall_improvement >= 10.0  # 10x improvement target
        
        # Generate recommendations
        recommendations = self._generate_recommendations(performance_analysis, meets_targets)
        
        return BenchmarkReport(
            benchmark_id=benchmark_id,
            timestamp=datetime.now(),
            configuration=self.config,
            v1_results=v1_results,
            v2_results=v2_results,
            performance_analysis=performance_analysis,
            scalability_analysis=scalability_analysis,
            resource_efficiency=resource_efficiency,
            overall_improvement=overall_improvement,
            meets_targets=meets_targets,
            recommendations=recommendations
        )
    
    def _analyze_performance(self, v1_results: List[BenchmarkResult], 
                           v2_results: List[BenchmarkResult]) -> List[ComparisonAnalysis]:
        """Analyze performance differences between V1 and V2."""
        analyses = []
        
        # Group results by test name and data size
        v1_grouped = {}
        v2_grouped = {}
        
        for result in v1_results:
            key = f"{result.test_name}_{result.data_size}"
            if key not in v1_grouped:
                v1_grouped[key] = []
            v1_grouped[key].append(result)
            
        for result in v2_results:
            key = f"{result.test_name}_{result.data_size}"
            if key not in v2_grouped:
                v2_grouped[key] = []
            v2_grouped[key].append(result)
        
        # Analyze each metric
        metrics = ['execution_time', 'records_per_second', 'peak_memory_mb', 'avg_cpu_percent']
        
        for metric in metrics:
            for key in v1_grouped.keys():
                if key in v2_grouped:
                    v1_values = [getattr(r, metric) for r in v1_grouped[key] if r.success]
                    v2_values = [getattr(r, metric) for r in v2_grouped[key] if r.success]
                    
                    if v1_values and v2_values:
                        v1_mean = np.mean(v1_values)
                        v1_std = np.std(v1_values)
                        v2_mean = np.mean(v2_values)
                        v2_std = np.std(v2_values)
                        
                        # Calculate improvement (for time metrics, lower is better)
                        if metric in ['execution_time', 'peak_memory_mb', 'avg_cpu_percent']:
                            improvement = v1_mean / v2_mean if v2_mean > 0 else 0
                        else:
                            improvement = v2_mean / v1_mean if v1_mean > 0 else 0
                        
                        improvement_pct = (improvement - 1) * 100
                        
                        # Statistical significance (simplified)
                        from scipy import stats
                        try:
                            _, p_value = stats.ttest_ind(v1_values, v2_values)
                            significant = p_value < 0.05
                            
                            # Calculate confidence interval (simplified)
                            ci_lower = improvement - 1.96 * (v2_std / np.sqrt(len(v2_values)))
                            ci_upper = improvement + 1.96 * (v2_std / np.sqrt(len(v2_values)))
                            
                        except:
                            significant = False
                            ci_lower, ci_upper = 0, 0
                        
                        analyses.append(ComparisonAnalysis(
                            metric_name=f"{key}_{metric}",
                            v1_mean=v1_mean,
                            v1_std=v1_std,
                            v2_mean=v2_mean,
                            v2_std=v2_std,
                            improvement_factor=improvement,
                            improvement_percentage=improvement_pct,
                            statistical_significance=significant,
                            confidence_interval=(ci_lower, ci_upper)
                        ))
        
        return analyses
    
    def _analyze_scalability(self, v1_results: List[BenchmarkResult], 
                           v2_results: List[BenchmarkResult]) -> Dict[str, Any]:
        """Analyze scalability characteristics of both systems."""
        scalability = {
            'v1_scaling_factor': 0,
            'v2_scaling_factor': 0,
            'v2_scales_better': False,
            'scaling_improvement': 0
        }
        
        # Analyze how execution time scales with data size
        v1_scaling = self._calculate_scaling_factor(v1_results)
        v2_scaling = self._calculate_scaling_factor(v2_results)
        
        scalability['v1_scaling_factor'] = v1_scaling
        scalability['v2_scaling_factor'] = v2_scaling
        scalability['v2_scales_better'] = v2_scaling < v1_scaling
        scalability['scaling_improvement'] = (v1_scaling - v2_scaling) / v1_scaling * 100 if v1_scaling > 0 else 0
        
        return scalability
    
    def _calculate_scaling_factor(self, results: List[BenchmarkResult]) -> float:
        """Calculate how execution time scales with data size."""
        successful_results = [r for r in results if r.success]
        
        if len(successful_results) < 2:
            return 0
        
        # Group by test name and calculate average time per data size
        test_groups = {}
        for result in successful_results:
            if result.test_name not in test_groups:
                test_groups[result.test_name] = {}
            
            size = result.data_size
            if size not in test_groups[result.test_name]:
                test_groups[result.test_name][size] = []
            
            test_groups[result.test_name][size].append(result.execution_time)
        
        scaling_factors = []
        
        for test_name, size_groups in test_groups.items():
            sizes = sorted(size_groups.keys())
            if len(sizes) >= 2:
                # Calculate time per record for different sizes
                times_per_record = []
                for size in sizes:
                    avg_time = np.mean(size_groups[size])
                    times_per_record.append(avg_time / size)
                
                # Calculate scaling factor (ideally should be constant for linear scaling)
                scaling_factor = max(times_per_record) / min(times_per_record) if min(times_per_record) > 0 else 1
                scaling_factors.append(scaling_factor)
        
        return np.mean(scaling_factors) if scaling_factors else 1.0
    
    def _analyze_resource_efficiency(self, v1_results: List[BenchmarkResult], 
                                   v2_results: List[BenchmarkResult]) -> Dict[str, Any]:
        """Analyze resource efficiency differences."""
        v1_successful = [r for r in v1_results if r.success]
        v2_successful = [r for r in v2_results if r.success]
        
        if not v1_successful or not v2_successful:
            return {}
        
        # Calculate average resource usage
        v1_avg_memory = np.mean([r.peak_memory_mb for r in v1_successful])
        v2_avg_memory = np.mean([r.peak_memory_mb for r in v2_successful])
        
        v1_avg_cpu = np.mean([r.avg_cpu_percent for r in v1_successful])
        v2_avg_cpu = np.mean([r.avg_cpu_percent for r in v2_successful])
        
        memory_efficiency = (v1_avg_memory - v2_avg_memory) / v1_avg_memory * 100 if v1_avg_memory > 0 else 0
        cpu_efficiency = (v1_avg_cpu - v2_avg_cpu) / v1_avg_cpu * 100 if v1_avg_cpu > 0 else 0
        
        return {
            'v1_avg_memory_mb': v1_avg_memory,
            'v2_avg_memory_mb': v2_avg_memory,
            'memory_efficiency_improvement': memory_efficiency,
            'v1_avg_cpu_percent': v1_avg_cpu,
            'v2_avg_cpu_percent': v2_avg_cpu,
            'cpu_efficiency_improvement': cpu_efficiency
        }
    
    def _calculate_overall_improvement(self, performance_analysis: List[ComparisonAnalysis]) -> float:
        """Calculate overall performance improvement factor."""
        execution_time_improvements = [
            a.improvement_factor for a in performance_analysis 
            if 'execution_time' in a.metric_name and a.improvement_factor > 0
        ]
        
        if execution_time_improvements:
            return np.mean(execution_time_improvements)
        else:
            return 1.0
    
    def _generate_recommendations(self, performance_analysis: List[ComparisonAnalysis], 
                                meets_targets: bool) -> List[str]:
        """Generate performance optimization recommendations."""
        recommendations = []
        
        if meets_targets:
            recommendations.append("Performance targets met - V2 ready for production")
        else:
            recommendations.append("Performance targets not met - optimization required")
        
        # Analyze specific performance areas
        memory_issues = [a for a in performance_analysis if 'memory' in a.metric_name and a.improvement_factor < 1.2]
        if memory_issues:
            recommendations.append("Memory usage optimization needed in V2 system")
        
        cpu_issues = [a for a in performance_analysis if 'cpu' in a.metric_name and a.improvement_factor < 1.2]
        if cpu_issues:
            recommendations.append("CPU efficiency optimization needed in V2 system")
        
        time_issues = [a for a in performance_analysis if 'execution_time' in a.metric_name and a.improvement_factor < 5.0]
        if time_issues:
            recommendations.append("Execution time optimization critical for V2 system")
        
        return recommendations


async def run_performance_benchmark(v1_data_path: Path, v2_database_url: str, 
                                  config: Optional[BenchmarkConfiguration] = None) -> BenchmarkReport:
    """Run comprehensive performance benchmark."""
    if config is None:
        config = BenchmarkConfiguration()
    
    benchmarker = PerformanceBenchmarker(config)
    return await benchmarker.run_comprehensive_benchmark(v1_data_path, v2_database_url)


if __name__ == "__main__":
    # Example usage
    config = BenchmarkConfiguration(
        test_iterations=3,
        test_data_sizes=[1000, 5000, 10000],
        enable_profiling=True
    )
    
    v1_path = Path("/Users/<USER>/Documents/GitHub/yemen-market-integration/data")
    v2_url = "postgresql://user:pass@localhost:5432/yemen_market_v2"
    
    import asyncio
    report = asyncio.run(run_performance_benchmark(v1_path, v2_url, config))
    print(f"Benchmark completed: {report.benchmark_id}")
    print(f"Overall improvement: {report.overall_improvement:.1f}x")
    print(f"Meets targets: {report.meets_targets}")