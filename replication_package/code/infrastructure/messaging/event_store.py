"""Event store implementation for domain events."""

from abc import ABC, abstractmethod
from typing import List, Optional
from uuid import UUID

from ...core.domain.shared.events import DomainEvent


class EventStore(ABC):
    """Abstract event store."""
    
    @abstractmethod
    async def append_events(
        self, 
        aggregate_id: UUID, 
        events: List[DomainEvent], 
        expected_version: Optional[int] = None
    ) -> None:
        """Append events to the store."""
        pass
    
    @abstractmethod
    async def get_events(
        self, 
        aggregate_id: UUID, 
        from_version: Optional[int] = None
    ) -> List[DomainEvent]:
        """Get events for an aggregate."""
        pass


class InMemoryEventStore(EventStore):
    """In-memory event store implementation."""
    
    def __init__(self):
        """Initialize empty event store."""
        self._events: dict[UUID, List[DomainEvent]] = {}
    
    async def append_events(
        self, 
        aggregate_id: UUID, 
        events: List[DomainEvent], 
        expected_version: Optional[int] = None
    ) -> None:
        """Append events to the store."""
        if aggregate_id not in self._events:
            self._events[aggregate_id] = []
        
        if expected_version is not None:
            current_version = len(self._events[aggregate_id])
            if current_version != expected_version:
                raise ValueError(f"Version mismatch: expected {expected_version}, got {current_version}")
        
        self._events[aggregate_id].extend(events)
    
    async def get_events(
        self, 
        aggregate_id: UUID, 
        from_version: Optional[int] = None
    ) -> List[DomainEvent]:
        """Get events for an aggregate."""
        if aggregate_id not in self._events:
            return []
        
        events = self._events[aggregate_id]
        if from_version is not None:
            events = events[from_version:]
        
        return events.copy()