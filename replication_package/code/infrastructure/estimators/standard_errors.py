"""Standard error estimation methods."""

from typing import Any, Dict, Optional
import numpy as np
import pandas as pd

from ..logging import Logger # Corrected import path
from .helpers.standard_error_helpers import heteroskedasticity_robust, clustered, driscoll_kraay

logger = get_logger(__name__)


class StandardErrorEstimator:
    """
    A class to provide various standard error estimation methods.
    """

    def __init__(self):
        pass

    def heteroskedasticity_robust(
        self,
        residuals: np.ndarray,
        X: np.ndarray,
        hc_type: str = 'HC3'
    ) -> Dict[str, Any]:
        """
        Calculate heteroskedasticity-robust standard errors (<PERSON>'s, HC0-HC3).

        Parameters
        ----------
        residuals : np.ndarray
            Array of model residuals.
        X : np.ndarray
            Array of independent variables (design matrix).
        hc_type : str, optional
            Type of heteroskedasticity-consistent covariance matrix.
            'HC0': <PERSON>'s original
            'HC1': <PERSON><PERSON><PERSON><PERSON> and <PERSON> (1985)
            'HC2': <PERSON><PERSON><PERSON><PERSON> and <PERSON> (1985)
            'HC3': <PERSON><PERSON><PERSON><PERSON> and <PERSON> (1985) (default, recommended for small samples)

        Returns
        -------
        Dict[str, Any]
            Dictionary containing robust standard errors and covariance matrix.
        """
        logger.info(f"Calculating heteroskedasticity-robust standard errors ({hc_type})...")
        return heteroskedasticity_robust(residuals, X, hc_type)

    def clustered(
        self,
        residuals: np.ndarray,
        X: np.ndarray,
        cluster_ids: np.ndarray
    ) -> Dict[str, Any]:
        """
        Calculate cluster-robust standard errors.

        Parameters
        ----------
        residuals : np.ndarray
            Array of model residuals.
        X : np.ndarray
            Array of independent variables (design matrix).
        cluster_ids : np.ndarray
            Array of cluster identifiers for each observation.

        Returns
        -------
        Dict[str, Any]
            Dictionary containing clustered standard errors and covariance matrix.
        """
        logger.info("Calculating cluster-robust standard errors...")
        return clustered(residuals, X, cluster_ids)

    def driscoll_kraay(
        self,
        residuals: np.ndarray,
        X: np.ndarray,
        entity_ids: np.ndarray,
        time_ids: np.ndarray,
        bandwidth: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Calculate Driscoll-Kraay (1998) standard errors.

        These standard errors are robust to arbitrary forms of cross-sectional
        and serial correlation.

        Parameters
        ----------
        residuals : np.ndarray
            Array of model residuals.
        X : np.ndarray
            Array of independent variables (design matrix).
        entity_ids : np.ndarray
            Array of entity identifiers for each observation.
        time_ids : np.ndarray
            Array of time identifiers for each observation.
        bandwidth : Optional[int], optional
            Bandwidth for the Newey-West kernel. If None, a default is chosen.

        Returns
        -------
        Dict[str, Any]
            Dictionary containing Driscoll-Kraay standard errors and covariance matrix.
        """
        logger.info("Calculating Driscoll-Kraay standard errors...")
        return driscoll_kraay(residuals, X, entity_ids, time_ids, bandwidth)
