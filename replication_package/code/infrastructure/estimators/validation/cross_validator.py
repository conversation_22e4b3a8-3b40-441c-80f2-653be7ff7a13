"""Cross-validation estimator implementation."""

from typing import Any, Dict, List, Tuple, Callable, Awaitable, Optional # Added Optional
import pandas as pd
import numpy as np
from sklearn.model_selection import TimeSeriesSplit, KFold
from sklearn.metrics import mean_squared_error, r2_score

# Avoid circular import - use string type hint instead
from ....core.models.interfaces import EstimationResult, Estimator
from ...logging import Logger # Corrected import path
from .helpers.cross_validation_helpers import (
    perform_time_series_cross_validation,
    perform_k_fold_cross_validation
)

logger = get_logger(__name__)


class CrossValidator(Estimator):
    """Estimator for performing cross-validation."""
    
    def __init__(self):
        """Initialize CrossValidator."""
        pass
        
    async def estimate(self, model: "CrossValidationModel", 
                      data: pd.DataFrame) -> EstimationResult:
        """Perform cross-validation.
        
        Args:
            model: Cross-validation model specification
            data: Data for cross-validation
            
        Returns:
            EstimationResult with cross-validation results
        """
        logger.info(f"Starting cross-validation with method: {model.cv_method}")
        
        # Validate data
        errors = model.validate_data(data)
        if errors:
            raise ValueError(f"Data validation failed: {errors}")
        
        # Prepare data (usually just ensuring it's clean)
        prepared_data = model.prepare_data(data)
        
        # Define the model function to be used in CV
        # This is a placeholder; in a real scenario, this would be passed in
        # or dynamically created based on the model type to be cross-validated.
        # For now, we assume model.model_to_validate is set and can be estimated.
        
        # The model_func needs to be provided externally when calling cross_validate
        # This estimate method is for the CrossValidator itself, not for the model being validated.
        # The cross_validate method is now part of the CrossValidationModel.
        
        # Perform cross-validation based on method
        if model.cv_method == "time_series":
            cv_results = perform_time_series_cross_validation(
                data=prepared_data,
                model_func=model.model_func,
                n_splits=model.n_splits,
                test_size=model.test_size,
                gap=model.gap,
                logger=logger
            )
        else:  # k_fold
            cv_results = perform_k_fold_cross_validation(
                data=prepared_data,
                model_func=model.model_func,
                n_splits=model.n_splits,
                shuffle=model.shuffle,
                random_state=model.random_state,
                logger=logger
            )
        
        # Aggregate results across folds
        all_metrics = cv_results['fold_metrics']
        
        # Calculate average metrics
        avg_mse = np.mean([m['mse'] for m in all_metrics])
        avg_rmse = np.mean([m['rmse'] for m in all_metrics])
        avg_mae = np.mean([m['mae'] for m in all_metrics])
        avg_r2 = np.mean([m['r2'] for m in all_metrics])
        
        # Calculate standard deviations
        std_mse = np.std([m['mse'] for m in all_metrics])
        std_rmse = np.std([m['rmse'] for m in all_metrics])
        std_mae = np.std([m['mae'] for m in all_metrics])
        std_r2 = np.std([m['r2'] for m in all_metrics])
        
        return EstimationResult(
            model_name=model.name,
            estimation_method=f"{model.cv_method}_cross_validation",
            coefficients={
                'avg_mse': avg_mse,
                'avg_rmse': avg_rmse,
                'avg_mae': avg_mae,
                'avg_r2': avg_r2
            },
            standard_errors={
                'std_mse': std_mse,
                'std_rmse': std_rmse,
                'std_mae': std_mae,
                'std_r2': std_r2
            },
            t_statistics={},
            p_values={},
            n_observations=len(prepared_data),
            diagnostics={
                'fold_results': all_metrics,
                'cv_method': model.cv_method,
                'n_splits': model.n_splits,
                'validation_curve': cv_results.get('validation_curve', None)
            },
            metadata={
                'cv_method': model.cv_method,
                'n_splits': model.n_splits,
                'test_size': model.test_size,
                'elapsed_time': cv_results.get('elapsed_time', 0),
                'best_fold': int(np.argmax([m['r2'] for m in all_metrics])),
                'worst_fold': int(np.argmin([m['r2'] for m in all_metrics]))
            }
        )
        
    def diagnose(self, model: "CrossValidationModel", result: EstimationResult) -> Dict[str, Any]:
        """Diagnostics for cross-validation (returns the results themselves)."""
        return result.diagnostics
        
    def predict(self, model: "CrossValidationModel", result: EstimationResult, new_data: pd.DataFrame) -> pd.Series:
        """Prediction is not applicable for cross-validation."""
        raise NotImplementedError("Prediction is not applicable for cross-validation.")
