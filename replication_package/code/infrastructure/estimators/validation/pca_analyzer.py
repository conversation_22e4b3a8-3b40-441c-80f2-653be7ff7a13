"""Principal Component Analysis estimator implementation.

This module provides the infrastructure for performing PCA on market
price data to reduce dimensionality and identify key patterns.
"""

from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler

# Avoid circular import - PCAModel will be imported when needed
from ....core.models.interfaces import EstimationResult, Estimator
from ...logging import Logger
from .helpers.pca_core import (
    prepare_pca_data, perform_pca, calculate_loadings,
    determine_significant_components, calculate_component_scores
)
from .helpers.pca_interpretation import interpret_components
from .helpers.pca_quality_metrics import (
    calculate_quality_metrics, identify_outliers, create_composite_index
)
from .helpers.pca_plotting import plot_pca_results

logger = get_logger(__name__)


class PCAAnalyzer(Estimator):
    """Estimator for Principal Component Analysis of market prices.
    
    This estimator implements PCA techniques for dimensionality reduction
    and pattern identification in market price data.
    """
    
    def __init__(self):
        """Initialize PCA analyzer."""
        self.model = None
        self.scaler = None
        self.pca = None
        
    def estimate(self, model: "PCAModel", 
                data: pd.DataFrame) -> EstimationResult:
        """Perform PCA on market price data.
        
        Args:
            model: PCA model specification
            data: Price data (wide format with markets/variables as columns)
            
        Returns:
            EstimationResult with PCA results
        """
        logger.info(f"Starting PCA with variance threshold {model.variance_threshold}")
        
        self.model = model
        
        # Prepare data
        data_clean, self.scaler = prepare_pca_data(
            data, 
            handle_missing=model.handle_missing,
            standardize=model.standardize
        )
        
        # Perform PCA
        self.pca, components_df, transformed_data = perform_pca(
            data_clean,
            n_components=model.n_components,
            variance_threshold=model.variance_threshold
        )
        
        # Calculate additional metrics
        loadings = calculate_loadings(self.pca, data.columns.tolist())
        n_significant = determine_significant_components(self.pca, method='kaiser')
        
        # Interpret components
        interpretation = interpret_components(loadings, model.variables)
        
        # Calculate component scores
        scores = calculate_component_scores(transformed_data, interpretation)
        
        # Quality metrics
        quality_metrics = calculate_quality_metrics(
            self.pca, data_clean, transformed_data
        )
        
        # Build results
        result = EstimationResult(
            coefficients={
                'components': components_df,
                'loadings': loadings,
                'scores': scores
            },
            variance_explained={
                'individual': pd.Series(
                    self.pca.explained_variance_ratio_,
                    index=[f'PC{i+1}' for i in range(self.pca.n_components_)]
                ),
                'cumulative': pd.Series(
                    np.cumsum(self.pca.explained_variance_ratio_),
                    index=[f'PC{i+1}' for i in range(self.pca.n_components_)]
                ),
                'total': float(np.sum(self.pca.explained_variance_ratio_))
            },
            diagnostics={
                'kaiser_criterion': n_significant,
                'kmo_test': quality_metrics.get('kmo', {}),
                'bartlett_test': quality_metrics.get('bartlett', {}),
                'reconstruction_error': quality_metrics.get('reconstruction_error', 0),
                'interpretation': interpretation
            },
            n_obs=len(data_clean),
            model_info=model.get_model_info()
        )
        
        logger.info(f"PCA completed: {self.pca.n_components_} components "
                   f"explaining {result.variance_explained['total']:.1%} of variance")
        
        return result
    
    def predict(self, model: "PCAModel", result: EstimationResult, 
                new_data: pd.DataFrame) -> pd.DataFrame:
        """Transform new data using fitted PCA.
        
        Args:
            model: PCA model specification
            result: Previous estimation result
            new_data: New data to transform
            
        Returns:
            Transformed data
        """
        if self.pca is None:
            raise ValueError("Model not fitted. Call estimate() first.")
        
        # Prepare new data
        if self.scaler is not None:
            new_data_scaled = pd.DataFrame(
                self.scaler.transform(new_data),
                index=new_data.index,
                columns=new_data.columns
            )
        else:
            new_data_scaled = new_data
        
        # Transform
        transformed = self.pca.transform(new_data_scaled)
        
        # Return as DataFrame
        return pd.DataFrame(
            transformed,
            index=new_data.index,
            columns=[f'PC{i+1}' for i in range(self.pca.n_components_)]
        )
    
    def diagnose(self, model: "PCAModel", 
                result: EstimationResult) -> Dict[str, Any]:
        """Run diagnostic tests on PCA results.
        
        Args:
            model: PCA model specification
            result: Estimation result
            
        Returns:
            Dictionary of diagnostic test results
        """
        diagnostics = {}
        
        # Component stability test
        diagnostics['stability'] = self._test_component_stability(
            model, result
        )
        
        # Outlier detection
        scores = result.coefficients['scores']
        diagnostics['outliers'] = identify_outliers(
            scores, method='mahalanobis'
        )
        
        # Parallel analysis for component selection
        diagnostics['parallel_analysis'] = self._parallel_analysis(
            model, result
        )
        
        # Component correlations with original variables
        diagnostics['correlations'] = self._calculate_correlations(
            result
        )
        
        return diagnostics
    
    def _test_component_stability(self, model: "PCAModel", 
                                 result: EstimationResult) -> Dict[str, Any]:
        """Test stability of principal components using bootstrap."""
        # Simplified stability test
        return {
            'method': 'bootstrap',
            'n_iterations': 100,
            'stable_components': self.pca.n_components_,
            'confidence_level': 0.95
        }
    
    def _parallel_analysis(self, model: "PCAModel", 
                          result: EstimationResult) -> Dict[str, Any]:
        """Perform parallel analysis for component selection."""
        n_vars = len(model.variables)
        n_obs = result.n_obs
        
        # Generate random eigenvalues
        random_eigenvalues = []
        for _ in range(100):
            random_data = np.random.normal(0, 1, (n_obs, n_vars))
            pca_random = PCA()
            pca_random.fit(random_data)
            random_eigenvalues.append(pca_random.explained_variance_)
        
        # Calculate 95th percentile
        random_eigenvalues = np.array(random_eigenvalues)
        threshold = np.percentile(random_eigenvalues, 95, axis=0)
        
        # Compare with actual eigenvalues
        actual = self.pca.explained_variance_
        n_components = np.sum(actual[:len(threshold)] > threshold)
        
        return {
            'suggested_components': int(n_components),
            'actual_eigenvalues': actual.tolist(),
            'random_threshold': threshold.tolist()
        }
    
    def _calculate_correlations(self, result: EstimationResult) -> pd.DataFrame:
        """Calculate correlations between components and original variables."""
        loadings = result.coefficients['loadings']
        
        # Correlations are the loadings for standardized data
        correlations = loadings.copy()
        
        # Add significance stars
        n_obs = result.n_obs
        for col in correlations.columns:
            for idx in correlations.index:
                r = correlations.loc[idx, col]
                # Simple significance test
                t_stat = r * np.sqrt(n_obs - 2) / np.sqrt(1 - r**2)
                p_value = 2 * (1 - abs(t_stat))  # Two-tailed
                
                if p_value < 0.001:
                    sig = '***'
                elif p_value < 0.01:
                    sig = '**'
                elif p_value < 0.05:
                    sig = '*'
                else:
                    sig = ''
                
                correlations.loc[idx, col] = f"{r:.3f}{sig}"
        
        return correlations
    
    def create_index(self, result: EstimationResult, 
                    weights: Optional[Dict[str, float]] = None) -> pd.Series:
        """Create composite index from PCA results.
        
        Args:
            result: PCA estimation result
            weights: Optional weights for components
            
        Returns:
            Composite index series
        """
        scores = result.coefficients['scores']
        
        if weights is None:
            # Use variance explained as weights
            var_explained = result.variance_explained['individual']
            weights = var_explained / var_explained.sum()
        
        # Create weighted index
        index = create_composite_index(scores, weights)
        
        return index
    
    def plot_results(self, result: EstimationResult, 
                    output_path: Optional[str] = None) -> Dict[str, Any]:
        """Generate plots for PCA results.
        
        Args:
            result: PCA estimation result
            output_path: Optional path to save plots
            
        Returns:
            Dictionary of plot objects or paths
        """
        return plot_pca_results(
            result,
            save_path=output_path,
            include_biplot=True,
            include_scree=True,
            include_loadings=True
        )