"""Helper functions for cross-validation."""

from typing import Any, Dict, List, Tuple, Callable, Awaitable, Optional # Added Optional
import pandas as pd
import numpy as np
from sklearn.model_selection import TimeSeriesSplit, KFold
from sklearn.metrics import mean_squared_error, r2_score

from .....core.models.interfaces import EstimationResult
from ....logging import Logger # Corrected import path

logger = get_logger(__name__)

logger = get_logger(__name__)


async def perform_time_series_cross_validation(
    data: pd.DataFrame,
    model_func: Callable[[pd.DataFrame], Awaitable[EstimationResult]],
    n_splits: int = 5,
    test_size: Optional[float] = None
) -> Dict[str, Any]:
    """Perform time series cross-validation."""
    tscv = TimeSeriesSplit(n_splits=n_splits, test_size=test_size)
    
    fold_results = []
    for fold, (train_index, test_index) in enumerate(tscv.split(data)):
        train_data = data.iloc[train_index]
        test_data = data.iloc[test_index]
        
        if len(train_data) == 0 or len(test_data) == 0:
            logger.warning(f"Skipping fold {fold}: Empty train or test set.")
            continue
            
        try:
            # Estimate model on training data
            train_result = await model_func(train_data)
            
            # Predict on test data
            # Assuming train_result.model is the fitted model object
            # and it has a predict method that takes new_data
            predictions = await train_result.model.predict(test_data)
            
            # Evaluate predictions
            actuals = test_data[train_result.model.specification.dependent_variable]
            
            # Ensure predictions and actuals have aligned indices
            common_index = predictions.index.intersection(actuals.index)
            predictions = predictions.loc[common_index]
            actuals = actuals.loc[common_index]

            if len(common_index) == 0:
                logger.warning(f"Skipping evaluation for fold {fold}: No common observations between predictions and actuals.")
                continue

            mse = mean_squared_error(actuals, predictions)
            r2 = r2_score(actuals, predictions)
            
            fold_results.append({
                'fold': fold,
                'train_size': len(train_data),
                'test_size': len(test_data),
                'mse': mse,
                'r_squared': r2
            })
        except Exception as e:
            logger.error(f"Error in time series CV fold {fold}: {e}")
            fold_results.append({'fold': fold, 'error': str(e)})
            
    return {
        'method': 'time_series_split',
        'n_splits': n_splits,
        'fold_results': fold_results,
        'average_mse': np.mean([f['mse'] for f in fold_results if 'mse' in f]),
        'average_r_squared': np.mean([f['r_squared'] for f in fold_results if 'r_squared' in f])
    }


async def perform_k_fold_cross_validation(
    data: pd.DataFrame,
    model_func: Callable[[pd.DataFrame], Awaitable[EstimationResult]],
    n_splits: int = 5,
    shuffle: bool = True,
    random_state: Optional[int] = None
) -> Dict[str, Any]:
    """Perform K-Fold cross-validation."""
    kf = KFold(n_splits=n_splits, shuffle=shuffle, random_state=random_state)
    
    fold_results = []
    for fold, (train_index, test_index) in enumerate(kf.split(data)):
        train_data = data.iloc[train_index]
        test_data = data.iloc[test_index]
        
        if len(train_data) == 0 or len(test_data) == 0:
            logger.warning(f"Skipping fold {fold}: Empty train or test set.")
            continue
            
        try:
            train_result = await model_func(train_data)
            predictions = await train_result.model.predict(test_data)
            
            actuals = test_data[train_result.model.specification.dependent_variable]
            
            common_index = predictions.index.intersection(actuals.index)
            predictions = predictions.loc[common_index]
            actuals = actuals.loc[common_index]

            if len(common_index) == 0:
                logger.warning(f"Skipping evaluation for fold {fold}: No common observations between predictions and actuals.")
                continue

            mse = mean_squared_error(actuals, predictions)
            r2 = r2_score(actuals, predictions)
            
            fold_results.append({
                'fold': fold,
                'train_size': len(train_data),
                'test_size': len(test_data),
                'mse': mse,
                'r_squared': r2
            })
        except Exception as e:
            logger.error(f"Error in K-Fold CV fold {fold}: {e}")
            fold_results.append({'fold': fold, 'error': str(e)})
            
    return {
        'method': 'k_fold',
        'n_splits': n_splits,
        'fold_results': fold_results,
        'average_mse': np.mean([f['mse'] for f in fold_results if 'mse' in f]),
        'average_r_squared': np.mean([f['r_squared'] for f in fold_results if 'r_squared' in f])
    }
