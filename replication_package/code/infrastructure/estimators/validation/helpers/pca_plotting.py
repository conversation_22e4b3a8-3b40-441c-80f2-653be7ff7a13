"""Helper functions for plotting PCA results."""

from typing import Dict, Any, Optional
import pandas as pd
import matplotlib.pyplot as plt

from ....logging import Logger # Corrected import path

logger = get_logger(__name__)


def plot_pca_results(results: Dict[str, Any],
                     save_path: Optional[str] = None) -> None:
    """Plot PCA results.
    
    Args:
        results: PCA results dictionary
        save_path: Optional path to save plots
    """
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 1. Scree plot
    ax = axes[0, 0]
    var_exp = results['explained_variance_ratio']
    ax.bar(range(1, len(var_exp) + 1), var_exp)
    ax.plot(range(1, len(var_exp) + 1), 
            results['cumulative_variance_ratio'], 
            'r-o', label='Cumulative')
    ax.set_xlabel('Principal Component')
    ax.set_ylabel('Variance Explained')
    ax.set_title('Scree Plot')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 2. Biplot (first two components)
    if len(results['loadings'].columns) >= 2:
        ax = axes[0, 1]
        loadings = results['loadings'][['PC1', 'PC2']]
        
        # Plot variables as vectors
        for var in loadings.index:
            ax.arrow(0, 0, loadings.loc[var, 'PC1'], loadings.loc[var, 'PC2'],
                    head_width=0.05, head_length=0.05, fc='red', ec='red')
            ax.text(loadings.loc[var, 'PC1'] * 1.1, 
                   loadings.loc[var, 'PC2'] * 1.1, 
                   var, fontsize=8)
        
        ax.set_xlabel('PC1')
        ax.set_ylabel('PC2')
        ax.set_title('Variable Loadings Biplot')
        ax.grid(True, alpha=0.3)
        ax.axhline(y=0, color='k', linewidth=0.5)
        ax.axvline(x=0, color='k', linewidth=0.5)
    
    # 3. Contribution plot
    ax = axes[1, 0]
    contrib = pd.DataFrame({
        pc: abs(results['loadings'][pc]) * results['explained_variance_ratio'][pc]
        for pc in results['loadings'].columns
    })
    contrib.plot(kind='bar', stacked=True, ax=ax)
    ax.set_xlabel('Variables')
    ax.set_ylabel('Contribution')
    ax.set_title('Variable Contributions to PCs')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # 4. Score plot (first two components)
    if len(results['scores'].columns) >= 2:
        ax = axes[1, 1]
        ax.scatter(results['scores']['PC1'], results['scores']['PC2'], alpha=0.6)
        ax.set_xlabel('PC1')
        ax.set_ylabel('PC2')
        ax.set_title('Observation Scores')
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"PCA plots saved to {save_path}")
    
    plt.close()
