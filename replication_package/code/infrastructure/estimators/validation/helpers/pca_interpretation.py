"""Helper functions for interpreting PCA results."""

from typing import Dict, Any
import pandas as pd


def interpret_components(loadings: pd.DataFrame) -> Dict[str, str]:
    """Interpret principal components based on loadings.
    
    Args:
        loadings: Component loadings matrix
        
    Returns:
        Dictionary with component interpretations
    """
    interpretations = {}
    
    for pc in loadings.columns:
        # Find variables with high absolute loadings
        abs_loadings = abs(loadings[pc])
        high_loadings = loadings[pc][abs_loadings > 0.3].sort_values(
            ascending=False, key=abs
        )
        
        if len(high_loadings) > 0:
            # Create interpretation
            positive_vars = high_loadings[high_loadings > 0].head(3).index.tolist()
            negative_vars = high_loadings[high_loadings < 0].head(3).index.tolist()
            
            interpretation = ""
            if positive_vars:
                interpretation += f"High: {', '.join(positive_vars)}"
            if negative_vars:
                if interpretation:
                    interpretation += " | "
                interpretation += f"Low: {', '.join(negative_vars)}"
            
            # Add general interpretation
            if pc == 'PC1':
                interpretation += " (Overall price level)"
            elif pc == 'PC2':
                interpretation += " (Price contrast)"
            
            interpretations[pc] = interpretation
        else:
            interpretations[pc] = "No clear interpretation"
    
    return interpretations
