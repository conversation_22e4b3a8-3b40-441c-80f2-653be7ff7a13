"""Helper functions for calculating PCA quality metrics."""

from typing import Dict, Any, Optional # Added Optional
import pandas as pd
import numpy as np
from scipy.spatial.distance import mahalanobis
from scipy import stats

from ....logging import Logger # Corrected import path

logger = get_logger(__name__)


def calculate_quality_metrics(original_data: pd.DataFrame,
                              results: Dict[str, Any]) -> Dict[str, Any]:
    """Calculate PCA quality metrics.
    
    Args:
        original_data: Original data before PCA
        results: PCA results
            
    Returns:
        Dictionary with quality metrics
    """
    metrics = {}
    
    # Reconstruction error
    pca = results['pca_object']
    reconstructed = pca.inverse_transform(results['scores'])
    reconstruction_error = np.mean((original_data - reconstructed) ** 2)
    metrics['reconstruction_error'] = float(reconstruction_error)
    
    # Contribution of variables to components
    contributions = {}
    for pc in results['loadings'].columns:
        contrib = (results['loadings'][pc] ** 2 * 
                   results['explained_variance_ratio'][pc])
        contributions[pc] = contrib.sort_values(ascending=False)
    metrics['variable_contributions'] = contributions
    
    # Kaiser criterion
    if 'explained_variance' in results:
        n_kaiser = sum(results['explained_variance'] > 1)
        metrics['kaiser_criterion'] = n_kaiser
    
    # Scree test suggestion
    if len(results['explained_variance']) > 1:
        var_diffs = np.diff(results['explained_variance'])
        elbow = np.argmax(var_diffs > -0.1 * var_diffs[0]) + 1
        metrics['scree_suggestion'] = elbow
    
    return metrics


def identify_outliers(scores: pd.DataFrame,
                      n_std: float = 3) -> Dict[str, Any]:
    """Identify outliers in PC space.
    
    Args:
        scores: PC scores
        n_std: Number of standard deviations for outlier threshold
            
    Returns:
        Dictionary with outlier information
    """
    outliers = {}
    
    # Check each component
    for pc in scores.columns:
        pc_mean = scores[pc].mean()
        pc_std = scores[pc].std()
        
        outlier_mask = abs(scores[pc] - pc_mean) > n_std * pc_std
        outlier_indices = scores.index[outlier_mask].tolist()
        
        if outlier_indices:
            outliers[pc] = {
                'indices': outlier_indices,
                'values': scores.loc[outlier_indices, pc].to_dict()
            }
    
    # Multivariate outliers using Mahalanobis distance
    if len(scores.columns) > 1:
        mean = scores.mean().values
        cov_matrix = scores.cov().values
        
        try:
            inv_cov = np.linalg.inv(cov_matrix)
            distances = []
            
            for idx, row in scores.iterrows():
                dist = mahalanobis(row.values, mean, inv_cov)
                distances.append(dist)
            
            distances = pd.Series(distances, index=scores.index)
            
            # Chi-square threshold
            threshold = stats.chi2.ppf(0.975, len(scores.columns))
            multivariate_outliers = distances[distances > threshold]
            
            if len(multivariate_outliers) > 0:
                outliers['multivariate'] = {
                    'indices': multivariate_outliers.index.tolist(),
                    'distances': multivariate_outliers.to_dict()
                }
        except np.linalg.LinAlgError:
            logger.warning("Could not calculate Mahalanobis distances (singular covariance)")
    
    return outliers


def create_composite_index(results: Dict[str, Any],
                           weights: Optional[Dict[str, float]] = None) -> pd.Series:
    """Create composite index from principal components.
    
    Args:
        results: PCA results dictionary
        weights: Optional weights for components (defaults to variance explained)
            
    Returns:
        Composite index series
    """
    scores = results['scores']
    
    if weights is None:
        # Use variance explained as weights
        weights = results['explained_variance_ratio'].to_dict()
    
    # Normalize weights
    total_weight = sum(weights.values())
    norm_weights = {k: v/total_weight for k, v in weights.items()}
    
    # Calculate weighted sum
    composite = pd.Series(0, index=scores.index)
    for pc, weight in norm_weights.items():
        if pc in scores.columns:
            composite += weight * scores[pc]
    
    # Standardize to 0-100 scale
    composite_min = composite.min()
    composite_max = composite.max()
    if composite_max > composite_min:
        composite = 100 * (composite - composite_min) / (composite_max - composite_min)
    
    return composite
