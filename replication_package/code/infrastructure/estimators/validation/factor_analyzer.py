"""Factor analysis estimator implementation.

This module provides the infrastructure for performing factor analysis on market
price data to identify common drivers.
"""

from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
from sklearn.decomposition import FactorAnalysis
from sklearn.preprocessing import StandardScaler

from ....core.models.validation import FactorModel
from ....core.models.interfaces import EstimationResult, Estimator
from ...logging import Logger # Corrected import path
from ....core.models.validation.helpers.factor_analysis_helpers import (
    determine_n_factors,
    calculate_variance_explained,
    interpret_factors,
    test_factor_adequacy
)

logger = get_logger(__name__)


class FactorAnalyzer(Estimator):
    """Estimator for Factor Analysis of market prices.
    
    This estimator implements Factor Analysis techniques for dimensionality reduction
    and pattern identification in market price data.
    """
    
    def __init__(self):
        """Initialize Factor analyzer."""
        self.model = None
        self.scaler = StandardScaler()
        self.factor_analysis_object = None
        
    def estimate(self, model: FactorModel, 
                data: pd.DataFrame) -> EstimationResult:
        """Perform Factor Analysis on market price data.
        
        Args:
            model: Factor model specification
            data: Price data (wide format with markets/variables as columns)
            
        Returns:
            EstimationResult with Factor Analysis results
        """
        logger.info(f"Starting Factor Analysis with n_factors={model.n_factors}")
        
        self.model = model
        
        # Validate data
        errors = model.validate_data(data)
        if errors:
            raise ValueError(f"Data validation failed: {errors}")
        
        # Prepare data
        prepared_data = model.prepare_data(data)
        
        # Determine optimal number of factors if not specified
        n_factors_to_fit = model.n_factors
        if n_factors_to_fit == 'auto':
            n_factors_to_fit = determine_n_factors(
                prepared_data, 
                max_factors=model.max_factors,
                min_variance_explained=model.min_variance_explained,
                standardize=model.standardize
            )
            logger.info(f"Auto-selected {n_factors_to_fit} factors for estimation")
        
        # Fit factor model
        self.factor_analysis_object = FactorAnalysis(
            n_components=n_factors_to_fit,
            rotation=model.rotation if model.rotation != 'none' else None,
            random_state=42
        )
        
        factor_scores = self.factor_analysis_object.fit_transform(prepared_data)
        
        # Create results dictionary
        results = {
            'n_factors': n_factors_to_fit,
            'factor_loadings': pd.DataFrame(
                self.factor_analysis_object.components_.T,
                index=prepared_data.columns,
                columns=[f'Factor_{i+1}' for i in range(n_factors_to_fit)]
            ),
            'factor_scores': pd.DataFrame(
                factor_scores,
                index=prepared_data.index,
                columns=[f'Factor_{i+1}' for i in range(n_factors_to_fit)]
            ),
            'communalities': pd.Series(
                1 - self.factor_analysis_object.noise_variance_,
                index=prepared_data.columns
            ),
            'variance_explained': calculate_variance_explained(self.factor_analysis_object, prepared_data),
            'rotation': model.rotation,
            'standardized': model.standardize,
            'factor_analysis_object': self.factor_analysis_object # Store for later use
        }
        
        # Interpret factors using helper
        results['factor_interpretation'] = interpret_factors(results['factor_loadings'])
        
        # Test factor adequacy using helper
        results['adequacy_tests'] = test_factor_adequacy(
            prepared_data, results, model.min_variance_explained
        )
        
        return EstimationResult(
            model_name=model.name,
            params=pd.Series(results['factor_loadings'].values.flatten()),
            standard_errors=pd.Series([np.nan] * len(results['factor_loadings'].values.flatten())),
            fitted_values=results['factor_scores'],
            residuals=None, # Factor analysis doesn't directly produce residuals in this context
            diagnostics=results['adequacy_tests'],
            metadata={
                'n_factors': results['n_factors'],
                'rotation': results['rotation'],
                'standardized': results['standardized'],
                'results': results # Store full results for easy access
            }
        )
    
    def diagnose(self, model: FactorModel, result: EstimationResult) -> Dict[str, Any]:
        """Run diagnostic tests for Factor Analysis."""
        diagnostics = result.diagnostics.copy()
        
        # Add more specific diagnostics if needed, e.g., from adequacy_tests
        if 'adequacy_tests' in result.metadata.get('results', {}):
            diagnostics.update(result.metadata['results']['adequacy_tests'])
            
        return diagnostics
    
    def predict(self, model: FactorModel, result: EstimationResult, new_data: pd.DataFrame) -> pd.DataFrame:
        """Transform new data using fitted factors."""
        if self.factor_analysis_object is None:
            raise RuntimeError("Estimator must be fitted before prediction.")
            
        logger.info("Generating factor score predictions for new data.")
        
        numeric_data = new_data.select_dtypes(include=[np.number])
        clean_data = numeric_data.dropna()
        
        if model.standardize and model.scaler: # Use model's scaler if available
            scaled_data = model.scaler.transform(clean_data)
            data_for_transform = pd.DataFrame(
                scaled_data,
                index=clean_data.index,
                columns=clean_data.columns
            )
        else:
            data_for_transform = clean_data
            
        factor_scores = self.factor_analysis_object.transform(data_for_transform)
        
        return pd.DataFrame(
            factor_scores,
            index=data_for_transform.index,
            columns=[f'Factor_{i+1}' for i in range(result.metadata['n_factors'])]
        )
