"""Panel data estimators with advanced standard error corrections."""

import numpy as np
import pandas as pd
from scipy import linalg
from statsmodels.stats.sandwich_covariance import cov_hac

from .standard_errors import StandardErrorEstimator
from infrastructure.logging import Logger

logger = get_logger(__name__)


class DriscollKraayEstimator:
    """
    Driscoll-Kraay standard errors for panel data with cross-sectional dependence.
    
    Implements <PERSON><PERSON><PERSON> and <PERSON><PERSON> (1998) standard errors that are robust to:
    - Heteroskedasticity
    - Serial correlation (up to specified lag)
    - Cross-sectional/spatial correlation
    """
    
    def __init__(self, bandwidth: int = 3):
        """Initialize with bandwidth for kernel."""
        self.bandwidth = bandwidth
    
    def calculate_standard_errors(
        self,
        X: np.ndarray,
        residuals: np.ndarray,
        entity_ids: np.ndarray,
        time_ids: np.ndarray
    ) -> np.ndarray:
        """
        Calculate Driscoll-Kraay standard errors.
        
        Args:
            X: Design matrix (n_obs x k_vars)
            residuals: Regression residuals (n_obs,)
            entity_ids: Entity identifiers (n_obs,)
            time_ids: Time identifiers (n_obs,)
            
        Returns:
            Standard errors (k_vars,)
        """
        n_obs, k_vars = X.shape
        unique_times = np.unique(time_ids)
        n_time = len(unique_times)
        
        # Calculate time-averaged cross products
        V = np.zeros((k_vars, k_vars))
        
        for h in range(-self.bandwidth, self.bandwidth + 1):
            weight = self._bartlett_kernel(h, self.bandwidth)
            
            if weight > 0:
                cov_h = self._calculate_autocovariance(
                    X, residuals, entity_ids, time_ids, h
                )
                V += weight * cov_h
        
        # Calculate robust variance-covariance matrix
        # V = (X'X)^{-1} V (X'X)^{-1}
        XX_inv = linalg.inv(X.T @ X / n_obs)
        vcov = n_obs * XX_inv @ V @ XX_inv / n_time
        
        # Extract standard errors
        return np.sqrt(np.diag(vcov))
    
    def _bartlett_kernel(self, h: int, bandwidth: int) -> float:
        """Bartlett kernel weight."""
        if abs(h) <= bandwidth:
            return 1.0 - abs(h) / (bandwidth + 1)
        return 0.0
    
    def _calculate_autocovariance(
        self,
        X: np.ndarray,
        residuals: np.ndarray,
        entity_ids: np.ndarray,
        time_ids: np.ndarray,
        lag: int
    ) -> np.ndarray:
        """Calculate lag-h autocovariance matrix."""
        unique_times = np.unique(time_ids)
        k_vars = X.shape[1]
        cov_sum = np.zeros((k_vars, k_vars))
        count = 0
        
        for t_idx, t in enumerate(unique_times):
            if lag >= 0 and t_idx + lag < len(unique_times):
                t_plus_lag = unique_times[t_idx + lag]
                
                # Get observations at time t and t+lag
                mask_t = time_ids == t
                mask_t_lag = time_ids == t_plus_lag
                
                # Calculate cross products
                X_t = X[mask_t]
                X_t_lag = X[mask_t_lag]
                r_t = residuals[mask_t]
                r_t_lag = residuals[mask_t_lag]
                
                # Sum over all entities
                for i, entity in enumerate(np.unique(entity_ids[mask_t])):
                    if entity in entity_ids[mask_t_lag]:
                        e_mask_t = entity_ids[mask_t] == entity
                        e_mask_t_lag = entity_ids[mask_t_lag] == entity
                        
                        if e_mask_t.any() and e_mask_t_lag.any():
                            cov_sum += (
                                X_t[e_mask_t].T @ (r_t[e_mask_t] * r_t_lag[e_mask_t_lag])
                            )
                            count += 1
        
        return cov_sum / max(count, 1)
    
    def estimate_with_driscoll_kraay(self, model_result):
        """Apply Driscoll-Kraay correction to existing model results.
        
        Args:
            model_result: Fitted model result object
            
        Returns:
            Updated model result with DK standard errors
        """
        # Use the new StandardErrorEstimator
        se_estimator = StandardErrorEstimator()
        
        # Extract needed components from model result
        residuals = model_result.residuals
        X = model_result.X
        entity_ids = model_result.entity_ids
        time_ids = model_result.time_ids
        
        # Calculate DK standard errors
        dk_results = se_estimator.driscoll_kraay(
            residuals=residuals,
            X=X,
            entity_ids=entity_ids,
            time_ids=time_ids,
            kernel='bartlett',
            bandwidth=self.bandwidth
        )
        
        # Update model results
        model_result.standard_errors = dk_results['standard_errors']
        model_result.variance_matrix = dk_results['variance_matrix']
        model_result.se_type = 'Driscoll-Kraay'
        
        logger.info(f"Applied Driscoll-Kraay standard errors with bandwidth={self.bandwidth}")
        
        return model_result


class ClusteredSEEstimator:
    """
    Clustered standard errors for panel data.
    
    Allows for arbitrary correlation within clusters (e.g., entities)
    but assumes independence across clusters.
    """
    
    def __init__(self, cluster_var: str = "entity"):
        """Initialize with cluster variable."""
        self.cluster_var = cluster_var
    
    def calculate_standard_errors(
        self,
        X: pd.DataFrame,
        residuals: pd.Series,
        cluster_ids: pd.Series,
        XX_inv: np.ndarray = None
    ) -> pd.Series:
        """
        Calculate clustered standard errors.
        
        Args:
            X: Design matrix
            residuals: Regression residuals
            cluster_ids: Cluster identifiers
            XX_inv: Pre-computed (X'X)^{-1}
            
        Returns:
            Series of standard errors
        """
        n_obs = len(X)
        n_clusters = cluster_ids.nunique()
        k_vars = X.shape[1]
        
        # Calculate (X'X)^{-1} if not provided
        if XX_inv is None:
            XX_inv = linalg.inv(X.T.values @ X.values)
        
        # Calculate cluster-robust variance
        V = np.zeros((k_vars, k_vars))
        
        for cluster in cluster_ids.unique():
            cluster_mask = cluster_ids == cluster
            X_c = X[cluster_mask].values
            r_c = residuals[cluster_mask].values
            
            # Cluster contribution to variance
            V += X_c.T @ np.outer(r_c, r_c) @ X_c
        
        # Apply finite-sample correction
        correction = n_clusters / (n_clusters - 1) * n_obs / (n_obs - k_vars)
        vcov = correction * XX_inv @ V @ XX_inv
        
        # Extract standard errors
        se = np.sqrt(np.diag(vcov))
        return pd.Series(se, index=X.columns)


class HACEstimator:
    """
    Heteroskedasticity and Autocorrelation Consistent (HAC) standard errors.
    
    Newey-West style standard errors for time series regression.
    """
    
    def __init__(self, kernel: str = "bartlett", bandwidth: int = None):
        """Initialize HAC estimator."""
        self.kernel = kernel
        self.bandwidth = bandwidth
    
    def calculate_standard_errors(
        self,
        X: np.ndarray,
        residuals: np.ndarray,
        XX_inv: np.ndarray = None
    ) -> np.ndarray:
        """Calculate HAC standard errors."""
        n_obs, k_vars = X.shape
        
        # Automatic bandwidth selection if not specified
        if self.bandwidth is None:
            self.bandwidth = int(np.floor(4 * (n_obs / 100) ** (2/9)))
        
        # Use statsmodels implementation
        vcov = cov_hac(
            results=None,  # Would pass regression results
            nlags=self.bandwidth,
            weights_func=self._get_kernel_func()
        )
        
        return np.sqrt(np.diag(vcov))
    
    def _get_kernel_func(self):
        """Get kernel function."""
        if self.kernel == "bartlett":
            return lambda x: 1 - abs(x) if abs(x) <= 1 else 0
        elif self.kernel == "parzen":
            def parzen(x):
                ax = abs(x)
                if ax <= 0.5:
                    return 1 - 6 * ax**2 + 6 * ax**3
                elif ax <= 1:
                    return 2 * (1 - ax)**3
                else:
                    return 0
            return parzen
        else:
            raise ValueError(f"Unknown kernel: {self.kernel}")