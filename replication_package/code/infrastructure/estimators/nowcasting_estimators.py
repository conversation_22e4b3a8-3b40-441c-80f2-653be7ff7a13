"""
Estimators for nowcasting models in the infrastructure layer.

These estimators bridge the domain nowcasting models with the infrastructure
implementation details.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union
import asyncio
from datetime import datetime

from ...core.models.nowcasting.nowcasting_models import (
    DynamicFactorNowcast,
    SARIMAXNowcast,
    MachineLearningNowcast,
    EnsembleNowcast,
    NowcastResult,
    EarlyWarningSystem,
    EarlyWarningSignal
)
from ...core.models.interfaces import ModelInterface, EstimationResult
from ...core.validation.methodology_validator import MethodologyValidator
from ...core.utils.logging import get_logger

logger = get_logger(__name__)


class NowcastingEstimator:
    """Base estimator for nowcasting models with methodology validation."""
    
    def __init__(self):
        self.methodology_validator = MethodologyValidator()
        
    async def validate_data_for_nowcasting(self, data: pd.DataFrame) -> bool:
        """Ensure data meets methodology requirements before nowcasting."""
        # Run methodology validation
        is_valid, report = self.methodology_validator.validate_analysis_inputs(
            observations=data,
            analysis_type="nowcasting",
            hypothesis_tests=[]  # Nowcasting doesn't test specific hypotheses
        )
        
        if not is_valid:
            logger.error(f"Data validation failed: {report.critical_failures}")
            raise ValueError(
                f"Cannot proceed with nowcasting. Critical failures: {report.critical_failures}"
            )
            
        return True


class DynamicFactorEstimator(NowcastingEstimator):
    """Estimator for Dynamic Factor nowcasting models."""
    
    async def estimate(self,
                      panel_data: pd.DataFrame,
                      n_factors: int = 3,
                      forecast_horizon: int = 1,
                      **kwargs) -> Dict[str, Any]:
        """
        Estimate Dynamic Factor Model for panel nowcasting.
        
        Parameters
        ----------
        panel_data : pd.DataFrame
            Panel data with MultiIndex (market_id, date)
        n_factors : int
            Number of common factors
        forecast_horizon : int
            Periods ahead to forecast
        """
        # Validate data
        await self.validate_data_for_nowcasting(panel_data)
        
        # Initialize model
        model = DynamicFactorNowcast(
            n_factors=n_factors,
            forecast_horizon=forecast_horizon,
            confidence_levels=[0.8, 0.9, 0.95]
        )
        
        # Fit model
        logger.info(f"Fitting Dynamic Factor Model with {n_factors} factors")
        model.fit(panel_data)
        
        # Generate nowcast
        nowcast_result = model.predict()
        
        # Package results
        return {
            'model': model,
            'nowcast': nowcast_result,
            'method': 'DynamicFactor',
            'diagnostics': {
                'n_factors': n_factors,
                'forecast_horizon': forecast_horizon,
                'factor_loadings': nowcast_result.metadata.get('factor_loadings', {}),
                'model_fit': nowcast_result.metadata.get('model_diagnostics', {})
            }
        }


class SARIMAXEstimator(NowcastingEstimator):
    """Estimator for SARIMAX nowcasting models."""
    
    async def estimate(self,
                      time_series_data: pd.DataFrame,
                      market_id: str,
                      commodity: str,
                      exog_vars: Optional[List[str]] = None,
                      forecast_horizon: int = 1,
                      auto_order: bool = True,
                      **kwargs) -> Dict[str, Any]:
        """
        Estimate SARIMAX model for single time series nowcasting.
        
        Parameters
        ----------
        time_series_data : pd.DataFrame
            Time series data for specific market-commodity
        market_id : str
            Market identifier
        commodity : str
            Commodity name
        exog_vars : List[str]
            Exogenous variables to include
        forecast_horizon : int
            Periods ahead to forecast
        auto_order : bool
            Whether to automatically select ARIMA orders
        """
        # Filter data for specific market-commodity
        if isinstance(time_series_data.index, pd.MultiIndex):
            mask = (time_series_data.index.get_level_values('market_id') == market_id)
            series_data = time_series_data[mask].copy()
        else:
            series_data = time_series_data.copy()
            
        # Validate data
        await self.validate_data_for_nowcasting(series_data)
        
        # Prepare exogenous variables if specified
        exog = None
        if exog_vars:
            exog = series_data[exog_vars] if all(v in series_data.columns for v in exog_vars) else None
            
        # Initialize model
        model = SARIMAXNowcast(
            forecast_horizon=forecast_horizon,
            auto_order=auto_order,
            confidence_levels=[0.8, 0.9, 0.95]
        )
        
        # Fit model
        logger.info(f"Fitting SARIMAX for {market_id}-{commodity}")
        model.fit(series_data, exog=exog)
        
        # Generate nowcast
        # For future exog, we'd need to provide forecasted values
        # For now, using None which assumes no exog in forecast period
        nowcast_result = model.predict(exog_future=None)
        
        # Add market/commodity info to result
        nowcast_result.metadata['market_id'] = market_id
        nowcast_result.metadata['commodity'] = commodity
        
        return {
            'model': model,
            'nowcast': nowcast_result,
            'method': 'SARIMAX',
            'market_commodity': f"{market_id}_{commodity}",
            'diagnostics': nowcast_result.metadata.get('model_diagnostics', {})
        }


class MachineLearningEstimator(NowcastingEstimator):
    """Estimator for ML-based nowcasting models."""
    
    async def estimate(self,
                      panel_data: pd.DataFrame,
                      model_type: str = 'random_forest',
                      feature_engineering: bool = True,
                      forecast_horizon: int = 1,
                      **kwargs) -> Dict[str, Any]:
        """
        Estimate ML model for nowcasting.
        
        Parameters
        ----------
        panel_data : pd.DataFrame
            Panel data for training
        model_type : str
            Type of ML model to use
        feature_engineering : bool
            Whether to engineer features automatically
        forecast_horizon : int
            Periods ahead to forecast
        """
        # Validate data
        await self.validate_data_for_nowcasting(panel_data)
        
        # Initialize model
        model = MachineLearningNowcast(
            model_type=model_type,
            forecast_horizon=forecast_horizon,
            confidence_levels=[0.8, 0.9, 0.95],
            **kwargs.get('model_params', {})
        )
        
        # Prepare feature columns
        feature_cols = kwargs.get('feature_cols', [])
        if 'conflict_intensity' in panel_data.columns:
            feature_cols.append('conflict_intensity')
        if 'currency_zone' in panel_data.columns:
            feature_cols.append('currency_zone')
            
        # Fit model
        logger.info(f"Fitting {model_type} ML model")
        model.fit(panel_data, feature_cols=feature_cols)
        
        # Generate nowcast
        nowcast_result = model.predict()
        
        return {
            'model': model,
            'nowcast': nowcast_result,
            'method': f'ML_{model_type}',
            'diagnostics': {
                'model_type': model_type,
                'n_features': len(model.feature_names) if hasattr(model, 'feature_names') else 0,
                'feature_importance': nowcast_result.metadata.get('feature_importance', {})
            }
        }


class EnsembleEstimator(NowcastingEstimator):
    """Estimator for ensemble nowcasting combining multiple methods."""
    
    async def estimate(self,
                      panel_data: pd.DataFrame,
                      methods: List[str] = None,
                      forecast_horizon: int = 1,
                      weight_method: str = 'performance',
                      **kwargs) -> Dict[str, Any]:
        """
        Estimate ensemble nowcast combining multiple methods.
        
        Parameters
        ----------
        panel_data : pd.DataFrame
            Panel data for all models
        methods : List[str]
            List of methods to include in ensemble
        forecast_horizon : int
            Periods ahead to forecast
        weight_method : str
            How to weight models: 'equal', 'performance', 'manual'
        """
        # Validate data
        await self.validate_data_for_nowcasting(panel_data)
        
        # Default methods if not specified
        if methods is None:
            methods = ['dynamic_factor', 'sarimax', 'random_forest']
            
        # Create component models
        models = []
        
        if 'dynamic_factor' in methods:
            models.append(DynamicFactorNowcast(
                n_factors=3,
                forecast_horizon=forecast_horizon
            ))
            
        if 'sarimax' in methods:
            # For panel data, we'd create multiple SARIMAX models
            # Here showing single model for simplicity
            models.append(SARIMAXNowcast(
                forecast_horizon=forecast_horizon,
                auto_order=True
            ))
            
        if 'random_forest' in methods:
            models.append(MachineLearningNowcast(
                model_type='random_forest',
                forecast_horizon=forecast_horizon
            ))
            
        if 'gradient_boosting' in methods:
            models.append(MachineLearningNowcast(
                model_type='gradient_boosting',
                forecast_horizon=forecast_horizon
            ))
            
        # Initialize ensemble
        ensemble = EnsembleNowcast(
            models=models,
            weight_method=weight_method,
            forecast_horizon=forecast_horizon,
            confidence_levels=[0.8, 0.9, 0.95]
        )
        
        # Fit ensemble
        logger.info(f"Fitting ensemble with {len(models)} models")
        ensemble.fit(panel_data)
        
        # Generate ensemble nowcast
        nowcast_result = ensemble.predict()
        
        return {
            'model': ensemble,
            'nowcast': nowcast_result,
            'method': 'Ensemble',
            'diagnostics': {
                'n_models': len(models),
                'weight_method': weight_method,
                'model_weights': ensemble.weights,
                'individual_results': nowcast_result.metadata.get('individual_results', {})
            }
        }


class EarlyWarningEstimator(NowcastingEstimator):
    """Estimator for early warning system based on nowcasts."""
    
    def __init__(self):
        super().__init__()
        self.warning_system = None
        
    async def generate_warnings(self,
                              nowcast_results: Dict[str, NowcastResult],
                              baseline_period: str = '2019',
                              market_metadata: Optional[pd.DataFrame] = None,
                              **kwargs) -> List[EarlyWarningSignal]:
        """
        Generate early warning signals from nowcast results.
        
        Parameters
        ----------
        nowcast_results : Dict[str, NowcastResult]
            Nowcast results by market-commodity pair
        baseline_period : str
            Period to use for baseline prices
        market_metadata : pd.DataFrame
            Additional market information
        """
        # Initialize warning system if not already done
        if self.warning_system is None:
            # Use ensemble model for warnings
            ensemble_model = EnsembleNowcast()
            self.warning_system = EarlyWarningSystem(
                nowcast_model=ensemble_model,
                thresholds=kwargs.get('thresholds', None)
            )
            
        # Calculate baseline prices
        baseline_prices = await self._calculate_baseline_prices(
            nowcast_results, baseline_period
        )
        
        # Generate warnings
        warnings = self.warning_system.generate_warnings(
            nowcast_results=nowcast_results,
            baseline_prices=baseline_prices,
            market_metadata=market_metadata
        )
        
        # Log summary
        logger.info(f"Generated {len(warnings)} early warning signals")
        severity_counts = pd.Series([w.severity for w in warnings]).value_counts()
        for severity, count in severity_counts.items():
            logger.info(f"  {severity}: {count} warnings")
            
        return warnings
        
    async def _calculate_baseline_prices(self,
                                       nowcast_results: Dict[str, NowcastResult],
                                       baseline_period: str) -> pd.DataFrame:
        """Calculate baseline prices for comparison."""
        baseline_prices = {}
        
        # Extract historical averages from training data
        # This is simplified - in practice would use proper baseline calculation
        for key, result in nowcast_results.items():
            if hasattr(result, 'metadata') and 'training_mean' in result.metadata:
                baseline_prices[key] = result.metadata['training_mean']
            else:
                # Use first point of forecast as rough baseline
                baseline_prices[key] = result.point_forecast.iloc[0] * 0.9
                
        return pd.Series(baseline_prices)


class NowcastingOrchestrator:
    """Orchestrates nowcasting across multiple markets and commodities."""
    
    def __init__(self):
        self.dfm_estimator = DynamicFactorEstimator()
        self.sarimax_estimator = SARIMAXEstimator()
        self.ml_estimator = MachineLearningEstimator()
        self.ensemble_estimator = EnsembleEstimator()
        self.warning_estimator = EarlyWarningEstimator()
        
    async def run_comprehensive_nowcasting(self,
                                         panel_data: pd.DataFrame,
                                         markets: List[str],
                                         commodities: List[str],
                                         methods: List[str] = None,
                                         generate_warnings: bool = True,
                                         **kwargs) -> Dict[str, Any]:
        """
        Run comprehensive nowcasting analysis across markets and commodities.
        
        Parameters
        ----------
        panel_data : pd.DataFrame
            Full panel dataset
        markets : List[str]
            Markets to analyze
        commodities : List[str]
            Commodities to analyze
        methods : List[str]
            Nowcasting methods to use
        generate_warnings : bool
            Whether to generate early warning signals
        """
        results = {
            'panel_nowcast': None,
            'market_commodity_nowcasts': {},
            'ensemble_nowcasts': {},
            'warnings': []
        }
        
        # 1. Run panel-wide Dynamic Factor Model
        logger.info("Running panel-wide Dynamic Factor nowcasting")
        try:
            dfm_result = await self.dfm_estimator.estimate(
                panel_data=panel_data,
                n_factors=kwargs.get('n_factors', 3),
                forecast_horizon=kwargs.get('forecast_horizon', 3)
            )
            results['panel_nowcast'] = dfm_result
        except Exception as e:
            logger.error(f"DFM nowcasting failed: {str(e)}")
            
        # 2. Run market-commodity specific nowcasts
        for market in markets:
            for commodity in commodities:
                key = f"{market}_{commodity}"
                logger.info(f"Nowcasting for {key}")
                
                try:
                    # SARIMAX for individual series
                    sarimax_result = await self.sarimax_estimator.estimate(
                        time_series_data=panel_data,
                        market_id=market,
                        commodity=commodity,
                        forecast_horizon=kwargs.get('forecast_horizon', 3)
                    )
                    results['market_commodity_nowcasts'][key] = sarimax_result
                except Exception as e:
                    logger.warning(f"SARIMAX failed for {key}: {str(e)}")
                    
        # 3. Run ensemble nowcasts for robustness
        logger.info("Running ensemble nowcasting")
        try:
            ensemble_result = await self.ensemble_estimator.estimate(
                panel_data=panel_data,
                methods=methods or ['dynamic_factor', 'random_forest'],
                forecast_horizon=kwargs.get('forecast_horizon', 3),
                weight_method='performance'
            )
            results['ensemble_nowcasts']['full_panel'] = ensemble_result
        except Exception as e:
            logger.error(f"Ensemble nowcasting failed: {str(e)}")
            
        # 4. Generate early warnings if requested
        if generate_warnings and results['market_commodity_nowcasts']:
            logger.info("Generating early warning signals")
            
            # Extract NowcastResult objects
            nowcast_results = {
                key: value['nowcast']
                for key, value in results['market_commodity_nowcasts'].items()
                if 'nowcast' in value
            }
            
            try:
                warnings = await self.warning_estimator.generate_warnings(
                    nowcast_results=nowcast_results,
                    baseline_period=kwargs.get('baseline_period', '2019'),
                    market_metadata=kwargs.get('market_metadata', None)
                )
                results['warnings'] = warnings
                
                # Create summary dashboard
                if warnings:
                    dashboard_df = self.warning_estimator.warning_system.generate_dashboard_data(warnings)
                    results['warning_dashboard'] = dashboard_df
                    
            except Exception as e:
                logger.error(f"Warning generation failed: {str(e)}")
                
        # 5. Generate summary statistics
        results['summary'] = self._generate_nowcast_summary(results)
        
        return results
        
    def _generate_nowcast_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary statistics from nowcasting results."""
        summary = {
            'n_markets_analyzed': 0,
            'n_successful_nowcasts': 0,
            'forecast_horizon': 0,
            'warning_summary': {},
            'average_forecast_change': {}
        }
        
        # Count successful nowcasts
        if 'market_commodity_nowcasts' in results:
            summary['n_markets_analyzed'] = len(results['market_commodity_nowcasts'])
            summary['n_successful_nowcasts'] = sum(
                1 for v in results['market_commodity_nowcasts'].values()
                if v is not None and 'nowcast' in v
            )
            
        # Warning summary
        if 'warnings' in results and results['warnings']:
            severity_counts = {}
            for warning in results['warnings']:
                severity_counts[warning.severity] = severity_counts.get(warning.severity, 0) + 1
            summary['warning_summary'] = severity_counts
            
        # Extract forecast horizon
        for mc_result in results.get('market_commodity_nowcasts', {}).values():
            if mc_result and 'nowcast' in mc_result:
                summary['forecast_horizon'] = mc_result['nowcast'].forecast_horizon
                break
                
        return summary