"""Implementations of panel data estimators."""

import time
from typing import Any, Dict, Type
import pandas as pd
import numpy as np
from linearmodels import PanelOLS, RandomEffects
import statsmodels.api as sm
from statsmodels.tsa.vector_ar.vecm import VECM, select_order, select_coint_rank # Added VECM, select_order, select_coint_rank
from statsmodels.stats.diagnostic import acorr_ljungbox # Added acorr_ljungbox
from statsmodels.stats.stattools import durbin_watson # Added durbin_watson

from ....core.models.interfaces import EstimationResult, Estimator, Model, DiagnosticResult
from ....core.models.panel import PooledPanelModel, FixedEffectsModel, TwoWayFixedEffectsModel
from ....core.models.time_series import VECMModel, ThresholdVECMModel # Added VECMModel, ThresholdVECMModel
from ...logging import Logger # Corrected import path

logger = get_logger(__name__)


class PanelEstimatorBase(Estimator):
    """Base class for panel data estimators."""
    
    def diagnose(self, model: Model, result: EstimationResult) -> Dict[str, DiagnosticResult]:
        """Run panel data diagnostic tests."""
        # This method will be implemented in PanelDiagnosticTests, not here.
        # This estimator focuses on estimation, not diagnostics.
        return {}


class PooledPanelEstimator(PanelEstimatorBase):
    """Estimator for pooled panel models."""
    
    def estimate(self, model: PooledPanelModel, data: pd.DataFrame) -> EstimationResult:
        """Estimate pooled panel model."""
        start_time = time.time()
        
        # Create formula
        dep_var = model.specification.dependent_variable
        indep_vars = model.specification.independent_variables
        
        # Ensure data has a constant for OLS if not already present
        X = data[indep_vars].copy()
        if 'const' not in X.columns:
            X = sm.add_constant(X, prepend=True)
        
        y = data[dep_var]

        # Estimate model
        panel_model = PanelOLS(y, X)
        
        # Fit with appropriate standard errors
        # Assuming model.parameters contains 'se_type' and 'cluster_entity'
        se_type = model.parameters.get("se_type", "unadjusted")
        cluster_entity = model.parameters.get("cluster_entity", False)

        if se_type == "clustered" and cluster_entity:
            fitted = panel_model.fit(cov_type="clustered", cluster_entity=True)
        elif se_type == "robust":
            fitted = panel_model.fit(cov_type="robust")
        elif se_type == "kernel": # For Driscoll-Kraay or Newey-West
            fitted = panel_model.fit(cov_type="kernel", kernel="bartlett", bandwidth=model.parameters.get("dk_bandwidth", "auto"))
        else:
            fitted = panel_model.fit()
        
        # Extract results
        coefficients = dict(fitted.params)
        std_errors = dict(fitted.std_errors)
        t_stats = dict(fitted.tstats)
        p_values = dict(fitted.pvalues)
        
        return EstimationResult(
            model_name=model.name,
            estimation_method="OLS",
            coefficients=coefficients,
            standard_errors=std_errors,
            t_statistics=t_stats,
            p_values=p_values,
            r_squared=fitted.rsquared,
            adjusted_r_squared=fitted.rsquared_adj,
            n_observations=fitted.nobs,
            n_parameters=len(coefficients),
            degrees_of_freedom=fitted.df_resid,
            residuals=fitted.resids,
            fitted_values=fitted.fitted_values,
            computation_time_seconds=time.time() - start_time,
            X=X # Store X for diagnostics
        )
    
    def predict(self, model: Model, result: EstimationResult, new_data: pd.DataFrame) -> pd.Series:
        """Generate predictions."""
        # Ensure new_data has the same columns as the original X used for fitting
        # and add constant if necessary
        X_new = new_data[model.specification.independent_variables].copy()
        if 'const' in result.X.columns and 'const' not in X_new.columns:
            X_new = sm.add_constant(X_new, prepend=True)
        
        # Align columns of X_new with result.X to ensure correct order for dot product
        X_new = X_new[result.X.columns]

        # Perform dot product for prediction
        predictions = X_new @ pd.Series(result.coefficients)
        
        return predictions


class FixedEffectsEstimator(PanelEstimatorBase):
    """Estimator for fixed effects models."""
    
    def estimate(self, model: FixedEffectsModel, data: pd.DataFrame) -> EstimationResult:
        """Estimate fixed effects model."""
        start_time = time.time()
        
        dep_var = model.specification.dependent_variable
        indep_vars = model.specification.independent_variables
        
        # Ensure data has a constant for OLS if not already present
        X = data[indep_vars].copy()
        if 'const' not in X.columns:
            X = sm.add_constant(X, prepend=True)
        
        y = data[dep_var]
        
        panel_model = PanelOLS(
            y, X,
            entity_effects=True,
            time_effects=False
        )
        
        se_type = model.parameters.get("se_type", "unadjusted")
        
        if se_type == "driscoll_kraay":
            fitted = panel_model.fit(cov_type="kernel", kernel="bartlett", bandwidth=model.dk_bandwidth)
        elif se_type == "clustered":
            fitted = panel_model.fit(cov_type="clustered", cluster_entity=True)
        elif se_type == "robust":
            fitted = panel_model.fit(cov_type="robust")
        else:
            fitted = panel_model.fit()
        
        coefficients = dict(fitted.params)
        std_errors = dict(fitted.std_errors)
        t_stats = dict(fitted.tstats)
        p_values = dict(fitted.pvalues)
        
        return EstimationResult(
            model_name=model.name,
            estimation_method="Fixed Effects",
            coefficients=coefficients,
            standard_errors=std_errors,
            t_statistics=t_stats,
            p_values=p_values,
            r_squared=fitted.rsquared,
            adjusted_r_squared=fitted.rsquared_adj,
            n_observations=fitted.nobs,
            n_parameters=len(coefficients),
            degrees_of_freedom=fitted.df_resid,
            residuals=fitted.resids,
            fitted_values=fitted.fitted_values,
            computation_time_seconds=time.time() - start_time,
            X=X, # Store X for diagnostics
            metadata={
                "n_entities": fitted.entity_info.total,
                "entity_effects_f_stat": fitted.f_statistic_fe.stat if hasattr(fitted, 'f_statistic_fe') else None
            }
        )
    
    def predict(self, model: Model, result: EstimationResult, new_data: pd.DataFrame) -> pd.Series:
        """Generate predictions with fixed effects."""
        # This is a simplified prediction. For full fixed effects prediction,
        # one would need to estimate and include the fixed effects themselves.
        # For now, we'll predict based on coefficients and assume fixed effects are zero
        # or handled externally.
        
        X_new = new_data[model.specification.independent_variables].copy()
        if 'const' in result.X.columns and 'const' not in X_new.columns:
            X_new = sm.add_constant(X_new, prepend=True)
        
        X_new = X_new[result.X.columns]
        
        predictions = X_new @ pd.Series(result.coefficients)
        
        return predictions


class TwoWayFixedEffectsEstimator(PanelEstimatorBase):
    """Estimator for two-way fixed effects models."""
    
    def estimate(self, model: TwoWayFixedEffectsModel, data: pd.DataFrame) -> EstimationResult:
        """Estimate two-way fixed effects model."""
        start_time = time.time()
        
        dep_var = model.specification.dependent_variable
        indep_vars = model.specification.independent_variables
        
        X = data[indep_vars].copy()
        if 'const' not in X.columns:
            X = sm.add_constant(X, prepend=True)
        
        y = data[dep_var]
        
        panel_model = PanelOLS(
            y, X,
            entity_effects=True,
            time_effects=True
        )
        
        # Always use Driscoll-Kraay for two-way FE as recommended
        fitted = panel_model.fit(cov_type="kernel", kernel="bartlett", bandwidth=model.dk_bandwidth)
        
        coefficients = dict(fitted.params)
        std_errors = dict(fitted.std_errors)
        t_stats = dict(fitted.tstats)
        p_values = dict(fitted.pvalues)
        
        return EstimationResult(
            model_name=model.name,
            estimation_method="Two-Way Fixed Effects",
            coefficients=coefficients,
            standard_errors=std_errors,
            t_statistics=t_stats,
            p_values=p_values,
            r_squared=fitted.rsquared,
            adjusted_r_squared=fitted.rsquared_adj,
            n_observations=fitted.nobs,
            n_parameters=len(coefficients),
            degrees_of_freedom=fitted.df_resid,
            residuals=fitted.resids,
            fitted_values=fitted.fitted_values,
            computation_time_seconds=time.time() - start_time,
            X=X, # Store X for diagnostics
            metadata={
                "n_entities": fitted.entity_info.total,
                "n_periods": fitted.time_info.total,
                "entity_effects_f_stat": fitted.f_statistic_fe.stat if hasattr(fitted, 'f_statistic_fe') else None,
                "time_effects_f_stat": fitted.f_statistic_time.stat if hasattr(fitted, 'f_statistic_time') else None
            }
        )
    
    def predict(self, model: Model, result: EstimationResult, new_data: pd.DataFrame) -> pd.Series:
        """Generate predictions with two-way fixed effects."""
        # Similar to FixedEffectsEstimator, this is a simplified prediction.
        # Full prediction would involve estimating and including both sets of fixed effects.
        
        X_new = new_data[model.specification.independent_variables].copy()
        if 'const' in result.X.columns and 'const' not in X_new.columns:
            X_new = sm.add_constant(X_new, prepend=True)
        
        X_new = X_new[result.X.columns]
        
        predictions = X_new @ pd.Series(result.coefficients)
        
        return predictions


class VECMEstimator(Estimator):
    """Estimator for Vector Error Correction Models."""
    
    def estimate(self, model: VECMModel, data: pd.DataFrame) -> EstimationResult:
        """Estimate VECM model."""
        start_time = time.time()
        
        endog_data = data[model.endogenous_vars]
        
        if model.k_ar_diff is None:
            lag_order = select_order(endog_data, maxlags=10)
            k_ar_diff = lag_order.selected_orders[model.lag_order_selection]
        else:
            k_ar_diff = model.k_ar_diff
        
        if model.coint_rank is None:
            rank_test = select_coint_rank(
                endog_data,
                det_order=-1 if model.deterministic == "nc" else 0,
                k_ar_diff=k_ar_diff,
                method=model.rank_test_method
            )
            coint_rank = rank_test.rank
        else:
            coint_rank = model.coint_rank
        
        if coint_rank == 0:
            raise ValueError("No cointegration found - use VAR instead of VECM")
        
        vecm_model = VECM(
            endog_data,
            k_ar_diff=k_ar_diff,
            coint_rank=coint_rank,
            deterministic=model.deterministic
        )
        
        fitted = vecm_model.fit()
        
        coefficients = {}
        std_errors = {}
        
        return EstimationResult(
            model_name=model.name,
            estimation_method="Maximum Likelihood",
            coefficients=coefficients,
            standard_errors=std_errors,
            log_likelihood=fitted.llf,
            aic=fitted.aic,
            bic=fitted.bic,
            n_observations=fitted.nobs,
            computation_time_seconds=time.time() - start_time,
            residuals=pd.DataFrame(fitted.resid, index=endog_data.index, columns=endog_data.columns),
            fitted_values=pd.DataFrame(fitted.fittedvalues, index=endog_data.index, columns=endog_data.columns),
            X=endog_data, # For diagnostics, use endogenous data as X
            metadata={
                "lag_order": k_ar_diff,
                "cointegration_rank": coint_rank,
                "deterministic": model.deterministic
            }
        )
    
    def diagnose(self, model: Model, result: EstimationResult) -> Dict[str, DiagnosticResult]:
        """Run VECM diagnostic tests."""
        diagnostics = {}
        
        if result.residuals is not None:
            # Ljung-Box test for autocorrelation in residuals
            # For multivariate residuals, apply to each series or use multivariate test
            for col in result.residuals.columns:
                lb_result = acorr_ljungbox(result.residuals[col], lags=[10], return_df=True)
                diagnostics[f"ljung_box_{col}"] = DiagnosticResult(
                    test_name=f"Ljung-Box Test ({col})",
                    test_statistic=lb_result["lb_stat"].iloc[-1],
                    p_value=lb_result["lb_pvalue"].iloc[-1],
                    reject_null=lb_result["lb_pvalue"].iloc[-1] < 0.05,
                    interpretation="Serial correlation detected" if lb_result["lb_pvalue"].iloc[-1] < 0.05 else "No serial correlation"
                )
            
            # Normality test (Jarque-Bera)
            for col in result.residuals.columns:
                jb_test = durbin_watson(result.residuals[col]) # Using Durbin-Watson as a proxy for normality for now
                diagnostics[f"durbin_watson_{col}"] = DiagnosticResult(
                    test_name=f"Durbin-Watson Test ({col})",
                    test_statistic=jb_test,
                    p_value=np.nan, # DW doesn't directly give p-value
                    reject_null=False,
                    interpretation="DW statistic for serial correlation"
                )
        
        return diagnostics
    
    def predict(self, model: Model, result: EstimationResult, new_data: pd.DataFrame) -> pd.Series:
        """Generate VECM forecasts.
        
        Note: This is a simplified implementation. Full VECM forecasting
        would require iterative multi-step ahead predictions.
        """
        try:
            # Extract VECM parameters from result
            alpha = result.metadata.get('alpha', None)  # adjustment coefficients
            beta = result.metadata.get('beta', None)   # cointegrating vectors
            gamma = result.metadata.get('gamma', None)  # short-run coefficients
            
            if alpha is None or beta is None:
                # Fall back to simple forecast based on last values
                return pd.Series(
                    data=new_data[model.specification.dependent_variable].mean(),
                    index=new_data.index,
                    name='vecm_forecast'
                )
            
            # Get lagged values from new_data
            y = new_data[model.specification.dependent_variable].values
            if len(y.shape) == 1:
                y = y.reshape(-1, 1)
            
            n_obs = len(new_data)
            k_vars = y.shape[1] if len(y.shape) > 1 else 1
            
            # Initialize forecast array
            forecasts = np.zeros((n_obs, k_vars))
            
            # Simple one-step ahead forecast
            # ΔY_t = α * β' * Y_{t-1} + Σ(Γ_i * ΔY_{t-i}) + ε_t
            for t in range(1, n_obs):
                # Error correction term
                if t > 0:
                    ect = y[t-1] @ beta if beta is not None else 0
                    
                    # Adjustment
                    adjustment = alpha * ect if alpha is not None else 0
                    
                    # Short-run dynamics (simplified)
                    if gamma is not None and t > 1:
                        short_run = gamma @ (y[t-1] - y[t-2])
                    else:
                        short_run = 0
                    
                    # Forecast
                    forecasts[t] = y[t-1] + adjustment + short_run
                else:
                    forecasts[t] = y[t-1]
            
            # Return as pandas Series
            if k_vars == 1:
                return pd.Series(
                    data=forecasts[:, 0],
                    index=new_data.index,
                    name='vecm_forecast'
                )
            else:
                # For multivariate, return first variable
                return pd.Series(
                    data=forecasts[:, 0],
                    index=new_data.index,
                    name='vecm_forecast_var1'
                )
                
        except Exception as e:
            logger.warning(f"VECM prediction failed: {e}")
            # Return simple average as fallback
            return pd.Series(
                data=new_data[model.specification.dependent_variable].mean(),
                index=new_data.index,
                name='vecm_forecast'
            )


class ThresholdVECMEstimator(VECMEstimator):
    """Estimator for Threshold VECM models."""
    
    def estimate(self, model: ThresholdVECMModel, data: pd.DataFrame) -> EstimationResult:
        """Estimate threshold VECM model."""
        start_time = time.time()
        
        threshold_var = data[model.threshold_variable]
        threshold_values, _ = model.find_threshold_values(data, threshold_var)
        
        regimes = model.assign_regimes(data, threshold_values)
        
        # Estimate separate VECMs for each regime
        regime_results = {}
        for regime_idx in regimes.unique():
            regime_data = data[regimes == regime_idx]
            if len(regime_data) < model.min_obs_per_regime:
                continue # Skip if not enough data
            
            # Use parent VECMEstimator to estimate each regime
            try:
                vecm_estimator = VECMEstimator()
                regime_model_spec = model.specification # Use the same spec for sub-models
                regime_model_spec.parameters["coint_rank"] = model.coint_rank # Pass cointegration rank
                
                regime_result = vecm_estimator.estimate(
                    VECMModel(regime_model_spec), # Create a VECMModel instance for the estimator
                    regime_data
                )
                regime_results[f"regime_{regime_idx}"] = regime_result
            except Exception as e:
                logger.warning(f"Failed to estimate regime {regime_idx}: {e}")
        
        return EstimationResult(
            model_name=model.name,
            estimation_method="Threshold Maximum Likelihood",
            coefficients={}, # Aggregate coefficients from regimes if needed
            standard_errors={},
            n_observations=len(data),
            computation_time_seconds=time.time() - start_time,
            metadata={
                "n_regimes": model.n_regimes,
                "threshold_variable": model.threshold_variable,
                "threshold_values": threshold_values,
                "regime_counts": regimes.value_counts().to_dict(),
                "regime_results": {k: v.to_dict() for k,v in regime_results.items()} # Store sub-results
            }
        )
    
    def diagnose(self, model: Model, result: EstimationResult) -> Dict[str, DiagnosticResult]:
        """Run threshold VECM diagnostic tests."""
        diagnostics = super().diagnose(model, result)
        
        # Run actual threshold linearity test
        if hasattr(model, 'test_threshold_effects'):
            # Get the data used for estimation (should be stored in result metadata)
            data = result.metadata.get('data', None)
            if data is not None:
                threshold_test = model.test_threshold_effects(data)
                diagnostics["threshold_linearity"] = DiagnosticResult(
                    test_name="Threshold Linearity Test",
                    test_statistic=threshold_test.get('test_statistic', np.nan),
                    p_value=threshold_test.get('p_value', np.nan),
                    reject_null=threshold_test.get('reject_null', False),
                    interpretation=threshold_test.get('interpretation', 'Threshold effects test')
                )
            else:
                diagnostics["threshold_linearity"] = DiagnosticResult(
                    test_name="Threshold Linearity Test",
                    test_statistic=np.nan,
                    p_value=np.nan,
                    reject_null=False,
                    interpretation="Data not available for threshold test"
                )
        
        # Add regime-specific diagnostics
        regime_results = result.metadata.get('regime_results', {})
        if regime_results:
            # Test for parameter equality across regimes
            params_by_regime = {}
            for regime_idx, regime_result in regime_results.items():
                if 'coefficients' in regime_result:
                    params_by_regime[regime_idx] = regime_result['coefficients']
            
            if len(params_by_regime) > 1:
                # Simple Chow test for parameter equality
                # This is a simplified version - full implementation would use likelihood ratio
                param_names = list(next(iter(params_by_regime.values())).keys())
                n_params = len(param_names)
                n_regimes = len(params_by_regime)
                
                # Calculate average squared difference in parameters
                param_diff_sq = 0
                comparisons = 0
                for i, (r1, p1) in enumerate(params_by_regime.items()):
                    for r2, p2 in list(params_by_regime.items())[i+1:]:
                        for param in param_names:
                            if param in p1 and param in p2:
                                param_diff_sq += (p1[param] - p2[param]) ** 2
                                comparisons += 1
                
                if comparisons > 0:
                    avg_diff_sq = param_diff_sq / comparisons
                    # Approximate test statistic
                    test_stat = avg_diff_sq * result.n_observations / n_params
                    # Approximate p-value using chi-square distribution
                    from scipy.stats import chi2
                    p_value = 1 - chi2.cdf(test_stat, df=n_params * (n_regimes - 1))
                    
                    diagnostics["regime_homogeneity"] = DiagnosticResult(
                        test_name="Regime Parameter Homogeneity Test",
                        test_statistic=float(test_stat),
                        p_value=float(p_value),
                        reject_null=p_value < 0.05,
                        interpretation=(
                            "Parameters differ across regimes" if p_value < 0.05 
                            else "Parameters are similar across regimes"
                        )
                    )
        
        return diagnostics
