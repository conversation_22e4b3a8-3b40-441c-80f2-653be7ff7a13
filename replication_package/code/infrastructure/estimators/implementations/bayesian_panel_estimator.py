"""Bayesian Panel Model estimator implementation."""

from typing import Dict, Any
import pandas as pd
import numpy as np
from datetime import datetime

from ....core.models.interfaces import Estimator, EstimationResult, DiagnosticResult
from ....core.models.panel.bayesian_panel_wrapper import BayesianPanelModelWrapper
from ....core.utils.logging import get_logger

logger = get_logger(__name__)


class BayesianPanelEstimator(Estimator):
    """Estimator for Bayesian panel models."""

    def estimate(
        self, model: BayesianPanelModelWrapper, data: pd.DataFrame
    ) -> EstimationResult:
        """
        Estimate Bayesian panel model parameters using MCMC.

        Args:
            model: Bayesian panel model wrapper instance
            data: Prepared panel data

        Returns:
            EstimationResult with posterior summaries
        """
        logger.info(f"Estimating {model.name}")

        # Fit the model (wrapper handles the Bayesian fitting)
        model.fit(data)

        # Get results from the fitted model
        results_dict = model.get_results_dict()

        # Create EstimationResult
        result = EstimationResult(
            model_type=f"bayesian_{results_dict['model_type']}",
            estimation_date=datetime.now(),
            n_observations=results_dict["n_observations"],
            parameters=results_dict["coefficients"],
            standard_errors=results_dict["standard_errors"],
            t_statistics=results_dict["t_statistics"],
            p_values=results_dict["p_values"],
            r_squared=results_dict["r_squared"],
            adjusted_r_squared=results_dict["adjusted_r_squared"],
            residuals=np.array([]),  # Bayesian models don't have traditional residuals
            metadata={
                "converged": results_dict["converged"],
                "loo": results_dict["loo"],
                "waic": results_dict["waic"],
                "r_hat": results_dict["r_hat"],
                "zone_effects": results_dict["zone_effects"],
                "heterogeneity_measures": results_dict["heterogeneity_measures"],
                "posterior_intervals": results_dict["posterior_intervals"],
                "robust": results_dict["robust"],
                "structural_breaks": results_dict["structural_breaks"],
                "zone_heterogeneity": results_dict["zone_heterogeneity"],
                "n_chains": model.n_chains,
                "n_samples": model.n_samples,
            },
        )

        logger.info(
            f"Bayesian estimation complete: "
            f"Converged = {results_dict['converged']}, "
            f"LOO = {results_dict['loo']:.2f}, "
            f"Zone heterogeneity = {results_dict['zone_heterogeneity']}"
        )

        return result

    def predict(
        self,
        model: BayesianPanelModelWrapper,
        result: EstimationResult,
        data: pd.DataFrame,
    ) -> np.ndarray:
        """
        Generate predictions using fitted Bayesian model.

        Uses posterior predictive distribution for uncertainty quantification.
        """
        return model.predict(data)

    def residuals(
        self,
        model: BayesianPanelModelWrapper,
        result: EstimationResult,
        data: pd.DataFrame,
    ) -> np.ndarray:
        """
        Calculate residuals (observed - predicted).

        Note: For Bayesian models, these are based on posterior mean predictions.
        """
        predictions = self.predict(model, result, data)
        observed = data[model.specification.dependent_variable].values
        return observed - predictions

    def diagnose(
        self, model: BayesianPanelModelWrapper, result: EstimationResult
    ) -> Dict[str, DiagnosticResult]:
        """
        Run diagnostic tests for Bayesian model.

        Tests include:
        - Convergence diagnostics (R-hat)
        - Effective sample size
        - Model comparison metrics (LOO, WAIC)
        - Zone heterogeneity tests
        """
        diagnostics = {}

        # Convergence check
        r_hat_values = result.metadata.get("r_hat", {})
        max_r_hat = max(r_hat_values.values()) if r_hat_values else 1.0

        convergence_diag = DiagnosticResult(
            test_name="Convergence Diagnostic (R-hat)",
            statistic=max_r_hat,
            p_value=None,
            passed=max_r_hat <= 1.01,
            message=f"Maximum R-hat = {max_r_hat:.3f} ({'converged' if max_r_hat <= 1.01 else 'not converged'})",
        )
        diagnostics["convergence_check"] = convergence_diag

        # Leave-one-out cross-validation
        loo_value = result.metadata.get("loo", 0)
        loo_diag = DiagnosticResult(
            test_name="Leave-One-Out Cross-Validation",
            statistic=loo_value,
            p_value=None,
            passed=True,  # LOO is informative, not pass/fail
            message=f"LOO elpd = {loo_value:.2f} (higher is better)",
        )
        diagnostics["leave_one_out_cv"] = loo_diag

        # WAIC
        waic_value = result.metadata.get("waic", 0)
        waic_diag = DiagnosticResult(
            test_name="Widely Applicable Information Criterion",
            statistic=waic_value,
            p_value=None,
            passed=True,
            message=f"WAIC elpd = {waic_value:.2f} (higher is better)",
        )
        diagnostics["widely_applicable_ic"] = waic_diag

        # Zone heterogeneity test (if applicable)
        if result.metadata.get("zone_heterogeneity", False):
            zone_effects = result.metadata.get("zone_effects", {})
            if zone_effects:
                # Calculate difference in effects
                north_exchange = zone_effects.get("north", {}).get(
                    "exchange_rate_effect", 0
                )
                south_exchange = zone_effects.get("south", {}).get(
                    "exchange_rate_effect", 0
                )
                effect_diff = abs(north_exchange - south_exchange)

                # Check if credible interval excludes zero (simplified)
                posterior_intervals = result.metadata.get("posterior_intervals", {})
                diff_interval = posterior_intervals.get("exchange_effect_diff", (0, 0))
                excludes_zero = diff_interval[0] > 0 or diff_interval[1] < 0

                zone_diag = DiagnosticResult(
                    test_name="Zone Heterogeneity Test",
                    statistic=effect_diff,
                    p_value=None,
                    passed=excludes_zero,
                    message=f"Exchange rate effect difference = {effect_diff:.3f} "
                    f"({'significant' if excludes_zero else 'not significant'} heterogeneity)",
                )
                diagnostics["zone_heterogeneity_test"] = zone_diag

        # Structural break detection (if applicable)
        if result.metadata.get("structural_breaks", False):
            # Check if break point was detected
            # This would require access to the trace, simplified here
            break_diag = DiagnosticResult(
                test_name="Structural Break Detection",
                statistic=None,
                p_value=None,
                passed=True,
                message="Structural break modeling enabled",
            )
            diagnostics["structural_break_detection"] = break_diag

        # Effective sample size (simplified)
        n_samples = result.metadata.get("n_samples", 0)
        n_chains = result.metadata.get("n_chains", 0)
        total_samples = n_samples * n_chains

        ess_diag = DiagnosticResult(
            test_name="Effective Sample Size",
            statistic=float(total_samples),
            p_value=None,
            passed=total_samples >= 400,  # Minimum for reliable inference
            message=f"Total posterior samples = {total_samples}",
        )
        diagnostics["effective_sample_size"] = ess_diag

        return diagnostics
