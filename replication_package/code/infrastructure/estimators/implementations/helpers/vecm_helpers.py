"""Helper functions for VECM estimation."""

from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional
import pandas as pd
import numpy as np
from statsmodels.tsa.vector_ar.vecm import VECM, select_order, select_coint_rank
from statsmodels.stats.diagnostic import acorr_ljungbox
from statsmodels.stats.stattools import durbin_watson

from .....infrastructure.logging import Logger

logger = get_logger(__name__)


def prepare_vecm_data(data: pd.DataFrame, variables: list, 
                     handle_missing: str = 'drop') -> pd.DataFrame:
    """Prepare data for VECM estimation.
    
    Args:
        data: Raw data
        variables: List of variables to use
        handle_missing: How to handle missing values
        
    Returns:
        Prepared DataFrame
    """
    # Select variables
    vecm_data = data[variables].copy()
    
    # Handle missing values
    if handle_missing == 'drop':
        vecm_data = vecm_data.dropna()
    elif handle_missing == 'interpolate':
        vecm_data = vecm_data.interpolate(method='linear')
    else:
        raise ValueError(f"Unknown missing value method: {handle_missing}")
    
    return vecm_data


def select_vecm_specifications(data: pd.DataFrame, max_lags: int = 8,
                             deterministic: str = 'ci') -> Dict[str, Any]:
    """Select optimal lag order and cointegration rank.
    
    Args:
        data: Prepared data
        max_lags: Maximum lags to consider
        deterministic: Deterministic trend specification
        
    Returns:
        Dictionary with optimal specifications
    """
    # Select lag order
    lag_order = select_order(data, maxlags=max_lags, deterministic=deterministic)
    optimal_lag = lag_order.selected_orders['aic']
    
    logger.info(f"Selected lag order: {optimal_lag} (AIC criterion)")
    
    # Select cointegration rank
    rank_test = select_coint_rank(data, det_order=_det_order_map(deterministic), 
                                 k_ar_diff=optimal_lag)
    
    # Use trace test at 5% significance
    trace_stats = rank_test.trace_stat
    crit_vals = rank_test.trace_stat_crit_vals[:, 1]  # 5% critical values
    
    # Find rank where we fail to reject null
    rank = 0
    for i, (stat, crit) in enumerate(zip(trace_stats, crit_vals)):
        if stat < crit:
            rank = i
            break
    else:
        rank = len(trace_stats) - 1
    
    logger.info(f"Selected cointegration rank: {rank}")
    
    return {
        'lag_order': optimal_lag,
        'rank': rank,
        'lag_order_results': lag_order,
        'rank_test_results': rank_test
    }


def estimate_vecm_model(data: pd.DataFrame, rank: int, lag_order: int,
                       deterministic: str = 'ci') -> VECM:
    """Estimate VECM model with given specifications.
    
    Args:
        data: Prepared data
        rank: Cointegration rank
        lag_order: Number of lags
        deterministic: Deterministic trend
        
    Returns:
        Fitted VECM model
    """
    det_order = _det_order_map(deterministic)
    
    # Create and fit model
    model = VECM(data, k_ar_diff=lag_order, coint_rank=rank, 
                deterministic=deterministic)
    result = model.fit()
    
    return result


def extract_vecm_parameters(vecm_result) -> Dict[str, Any]:
    """Extract key parameters from VECM results.
    
    Args:
        vecm_result: Fitted VECM result
        
    Returns:
        Dictionary of parameters
    """
    params = {
        'alpha': vecm_result.alpha,  # Adjustment coefficients
        'beta': vecm_result.beta,    # Cointegrating vectors
        'gamma': vecm_result.gamma,  # Short-run coefficients
        'det_coef': vecm_result.det_coef_coint,  # Deterministic coefficients
    }
    
    # Add coefficient matrices for each lag
    for i in range(vecm_result.k_ar):
        if i < len(vecm_result.gamma):
            params[f'gamma_{i+1}'] = vecm_result.gamma[i]
    
    return params


def calculate_vecm_diagnostics(vecm_result, data: pd.DataFrame) -> Dict[str, Any]:
    """Calculate diagnostic tests for VECM.
    
    Args:
        vecm_result: Fitted VECM result
        data: Original data
        
    Returns:
        Dictionary of diagnostic results
    """
    residuals = vecm_result.resid
    n_vars = residuals.shape[1]
    
    diagnostics = {}
    
    # Serial correlation tests
    diagnostics['serial_correlation'] = {}
    for i in range(n_vars):
        lb_result = acorr_ljungbox(residuals[:, i], lags=10, return_df=True)
        diagnostics['serial_correlation'][f'var_{i+1}'] = {
            'ljung_box': lb_result.to_dict(),
            'durbin_watson': float(durbin_watson(residuals[:, i]))
        }
    
    # Normality test
    from scipy import stats
    diagnostics['normality'] = {}
    for i in range(n_vars):
        jb_stat, jb_pval = stats.jarque_bera(residuals[:, i])
        diagnostics['normality'][f'var_{i+1}'] = {
            'jarque_bera_stat': float(jb_stat),
            'p_value': float(jb_pval),
            'reject_normality': jb_pval < 0.05
        }
    
    # Model fit statistics
    diagnostics['model_fit'] = {
        'log_likelihood': float(vecm_result.llf),
        'aic': float(vecm_result.aic),
        'bic': float(vecm_result.bic),
        'hqic': float(vecm_result.hqic)
    }
    
    return diagnostics


def forecast_vecm(vecm_result, steps: int = 1, 
                 exog_future: Optional[np.ndarray] = None) -> np.ndarray:
    """Generate forecasts from VECM model.
    
    Args:
        vecm_result: Fitted VECM result
        steps: Number of steps ahead
        exog_future: Future exogenous variables
        
    Returns:
        Array of forecasts
    """
    # Get forecasts
    forecast = vecm_result.predict(steps=steps, exog_fc=exog_future)
    
    return forecast


def calculate_impulse_response(vecm_result, periods: int = 10) -> Dict[str, Any]:
    """Calculate impulse response functions.
    
    Args:
        vecm_result: Fitted VECM result
        periods: Number of periods
        
    Returns:
        Dictionary with IRF results
    """
    # Calculate orthogonalized IRF
    irf = vecm_result.irf(periods)
    
    return {
        'irf': irf.irfs,
        'cum_effects': irf.cum_effects,
        'long_run_effects': irf.lr_effects
    }


def _det_order_map(deterministic: str) -> int:
    """Map deterministic string to statsmodels det_order."""
    mapping = {
        'n': -1,   # No deterministic terms
        'co': 0,   # Constant outside
        'ci': 1,   # Constant inside
        'lo': 2,   # Linear trend outside
        'li': 3,   # Linear trend inside
    }
    return mapping.get(deterministic, 1)