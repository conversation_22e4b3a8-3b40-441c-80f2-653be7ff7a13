"""Cointegration test implementations."""

from typing import Dict, <PERSON>, <PERSON><PERSON>
import pandas as pd
import numpy as np
from statsmodels.tsa.stattools import coint
from statsmodels.tsa.vector_ar.vecm import coint_johansen, select_coint_rank

from ....core.models.interfaces import EstimationR<PERSON>ult, Estimator, Model
from ...logging import Logger # Corrected import path
from .helpers.engle_granger_helpers import perform_engle_granger_test
from .helpers.johansen_helpers import perform_johansen_test
from .helpers.cointegration_test_helpers import perform_phillips_ouliaris_test

logger = get_logger(__name__)


class CointegrationTestsEstimator(Estimator):
    """Estimator for various cointegration tests."""
    
    def __init__(self):
        """Initialize cointegration tests estimator."""
        pass
        
    def estimate(self, model: Model, data: pd.DataFrame) -> EstimationResult:
        """Perform various cointegration tests.
        
        Args:
            model: The model specification (not directly used, but for interface compatibility)
            data: DataFrame with time series for cointegration tests.
                  Assumes data contains the series to be tested.
            
        Returns:
            EstimationResult with test results.
        """
        logger.info("Running various cointegration tests...")
        
        if data.shape[1] < 2:
            raise ValueError("Cointegration tests require at least two time series.")
            
        # Ensure data is numeric and has no NaNs
        data_clean = data.dropna().select_dtypes(include=[np.number])
        
        if data_clean.empty:
            raise ValueError("No valid numeric data after dropping NaNs for cointegration tests.")
        
        results = {}
        
        # Engle-Granger test (for 2 variables)
        if data_clean.shape[1] == 2:
            y = data_clean.iloc[:, 0]
            x = data_clean.iloc[:, 1]
            results.update(perform_engle_granger_test(y, x))
            results.update(perform_phillips_ouliaris_test(y, x))
        else:
            logger.info("Skipping Engle-Granger and Phillips-Ouliaris tests (require exactly 2 series).")
        
        # Johansen test (for multiple variables)
        results.update(perform_johansen_test(data_clean, 
                                             det_order=model.parameters.get("deterministic", "c"),
                                             k_ar_diff=model.parameters.get("k_ar_diff", 1)))
        
        return EstimationResult(
            model_name="Cointegration Tests",
            estimation_method="Multiple Tests",
            coefficients={},
            standard_errors={},
            t_statistics={},
            p_values={},
            n_observations=len(data_clean),
            diagnostics=results,
            metadata={
                'tests_run': list(results.keys())
            }
        )
        
    def diagnose(self, model: Model, result: EstimationResult) -> Dict[str, Any]:
        """Diagnostics for cointegration tests (returns the test results themselves)."""
        return result.diagnostics
        
    def predict(self, model: Model, result: EstimationResult, new_data: pd.DataFrame) -> pd.Series:
        """Prediction is not applicable for cointegration tests."""
        raise NotImplementedError("Prediction is not applicable for cointegration tests.")
