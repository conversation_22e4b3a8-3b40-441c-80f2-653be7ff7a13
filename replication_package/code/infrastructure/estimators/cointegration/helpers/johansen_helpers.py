"""Helper functions for <PERSON><PERSON> cointegration test."""

from typing import Dict, Any, Tuple
import pandas as pd
import numpy as np
from statsmodels.tsa.vector_ar.vecm import coint_johansen

from ....logging import Logger # Corrected import path

logger = get_logger(__name__)


def perform_johansen_test(data: pd.DataFrame, det_order: int = 0, k_ar_diff: int = 1) -> Dict[str, Any]:
    """<PERSON><PERSON> test for cointegration rank."""
    results = {}
    try:
        johansen_result = coint_johansen(data.dropna().values, det_order=det_order, k_ar_diff=k_ar_diff)
        
        results['johansen'] = {
            'test_name': 'Johansen Cointegration Test',
            'trace_statistic': johansen_result.lr1.tolist(),
            'trace_critical_values': johansen_result.cvt.tolist(),
            'max_eigen_statistic': johansen_result.lr2.tolist(),
            'max_eigen_critical_values': johansen_result.cvm.tolist(),
            'eigenvalues': johansen_result.eig.tolist(),
            'eigenvectors': johansen_result.evec.tolist(),
            'interpretation': "See trace and max-eigenvalue statistics for cointegration rank"
        }
    except Exception as e:
        logger.warning(f"Johansen test failed: {e}")
        results['johansen'] = {'error': str(e)}
        
    return results
