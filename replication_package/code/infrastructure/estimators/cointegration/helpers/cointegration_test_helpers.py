"""Helper functions for various cointegration tests."""

from typing import Dict, Any, Tuple
import pandas as pd
import numpy as np
from statsmodels.tsa.stattools import coint
from statsmodels.tsa.vector_ar.vecm import coint_johansen

from ....logging import Logger # Corrected import path

logger = get_logger(__name__)


def perform_phillips_ouliaris_test(y: pd.Series, x: pd.Series) -> Dict[str, Any]:
    """Phillips-Ouliaris cointegration test."""
    results = {}
    try:
        # coint returns (t-statistic, p-value, critical_values)
        # Null hypothesis: no cointegration
        po_stat, po_pvalue, po_crit_values = coint(y.dropna(), x.dropna())
        
        results['phillips_ouliaris'] = {
            'test_name': 'Phillips-Ouliaris Cointegration Test',
            'test_statistic': float(po_stat),
            'p_value': float(po_pvalue),
            'critical_values': {str(k): float(v) for k, v in zip([1, 5, 10], po_crit_values)},
            'reject_null': po_pvalue < 0.05,
            'interpretation': "Cointegrated" if po_pvalue < 0.05 else "Not cointegrated"
        }
    except Exception as e:
        logger.warning(f"<PERSON>-Ouliaris test failed: {e}")
        results['phillips_ouliaris'] = {'error': str(e)}
        
    return results
