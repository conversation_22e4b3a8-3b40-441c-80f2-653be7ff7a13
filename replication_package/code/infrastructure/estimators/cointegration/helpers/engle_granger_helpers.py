"""Helper functions for Engle-Granger cointegration test."""

from typing import Dict, Any, Tuple
import pandas as pd
import numpy as np
import statsmodels.api as sm
from statsmodels.tsa.stattools import adfuller

from ....logging import Logger # Corrected import path

logger = get_logger(__name__)


def perform_engle_granger_test(y: pd.Series, x: pd.Series) -> Dict[str, Any]:
    """Engle-Granger two-step cointegration test."""
    results = {}
    try:
        # Step 1: Regress y on x to get residuals
        model = sm.OLS(y, sm.add_constant(x)).fit()
        residuals = model.resid
        
        # Step 2: Test residuals for unit root (ADF test)
        adf_result = adfuller(residuals.dropna())
        
        results['engle_granger'] = {
            'test_name': 'Engle-Granger Cointegration Test',
            'test_statistic': float(adf_result[0]),
            'p_value': float(adf_result[1]),
            'critical_values': {str(k): float(v) for k, v in adf_result[4].items()},
            'reject_null': adf_result[1] < 0.05, # Null is no cointegration (unit root in residuals)
            'interpretation': "Cointegrated" if adf_result[1] < 0.05 else "Not cointegrated"
        }
    except Exception as e:
        logger.warning(f"Engle-Granger test failed: {e}")
        results['engle_granger'] = {'error': str(e)}
        
    return results
