"""Johansen cointegration test implementation."""

from typing import Dict, Any, <PERSON>ple
import pandas as pd
import numpy as np
from statsmodels.tsa.vector_ar.vecm import coint_johans<PERSON>, select_coint_rank

from ....core.models.interfaces import EstimationResult, Estimator, Model
from ...logging import Logger # Corrected import path
from .helpers.johansen_helpers import perform_johansen_test

logger = get_logger(__name__)


class <PERSON><PERSON>Estimator(Estimator):
    """Estimator for the <PERSON>sen cointegration test."""
    
    def __init__(self):
        """Initialize <PERSON><PERSON> estimator."""
        pass
        
    def estimate(self, model: Model, data: pd.DataFrame) -> EstimationResult:
        """Perform <PERSON><PERSON> cointegration test.
        
        Args:
            model: The model specification (used for deterministic, k_ar_diff, rank_test_method)
            data: DataFrame with multiple time series for cointegration test.
            
        Returns:
            EstimationResult with test results.
        """
        logger.info("Running <PERSON><PERSON> cointegration test...")
        
        if data.shape[1] < 2:
            raise ValueError("<PERSON>sen test requires at least two time series.")
            
        # Ensure data is numeric and has no NaNs
        data_clean = data.dropna().select_dtypes(include=[np.number])
        
        if data_clean.empty:
            raise ValueError("No valid numeric data after dropping NaNs for <PERSON>sen test.")
        
        # Extract parameters from model specification
        det_order = model.parameters.get("deterministic", "c")
        k_ar_diff = model.parameters.get("k_ar_diff", 1)
        rank_test_method = model.parameters.get("rank_test_method", "trace")

        # Map deterministic string to integer order for coint_johansen
        det_order_map = {"nc": -1, "c": 0, "ct": 1, "ctt": 2}
        johansen_det_order = det_order_map.get(det_order, 0) # Default to 'c' (0)
        
        results = perform_johansen_test(data_clean, det_order=johansen_det_order, k_ar_diff=k_ar_diff)
        
        # Extract relevant info for EstimationResult
        johansen_result = results.get('johansen', {})
        
        # Determine selected rank
        selected_rank = 0
        if 'trace_statistic' in johansen_result and 'trace_critical_values' in johansen_result:
            # Use select_coint_rank to get the recommended rank
            try:
                rank_selection = select_coint_rank(
                    data_clean.values,
                    det_order=johansen_det_order,
                    k_ar_diff=k_ar_diff,
                    method=rank_test_method
                )
                selected_rank = rank_selection.rank
            except Exception as e:
                logger.warning(f"Failed to select cointegration rank: {e}. Defaulting to 0.")

        return EstimationResult(
            model_name="Johansen Cointegration Test",
            estimation_method="Maximum Likelihood",
            coefficients={'selected_rank': selected_rank},
            standard_errors={},
            t_statistics={},
            p_values={}, # No single p-value for Johansen
            n_observations=len(data_clean),
            diagnostics=results,
            metadata={
                'selected_rank': selected_rank,
                'trace_stats': johansen_result.get('trace_statistic', []),
                'trace_crit_vals': johansen_result.get('trace_critical_values', []),
                'max_eig_stats': johansen_result.get('max_eigen_statistic', []),
                'max_eig_crit_vals': johansen_result.get('max_eigen_critical_values', []),
                'eigenvalues': johansen_result.get('eigenvalues', []),
                'eigenvectors': johansen_result.get('eigenvectors', [])
            }
        )
        
    def diagnose(self, model: Model, result: EstimationResult) -> Dict[str, Any]:
        """Diagnostics for Johansen test (returns the test results themselves)."""
        return result.diagnostics
        
    def predict(self, model: Model, result: EstimationResult, new_data: pd.DataFrame) -> pd.Series:
        """Prediction is not applicable for cointegration tests."""
        raise NotImplementedError("Prediction is not applicable for Johansen cointegration test.")
