"""Authentication middleware for API protection."""

from typing import Optional, List

from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from .....infrastructure.logging import Logger

logger = get_logger(__name__)


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """
    Middleware to enforce authentication on protected routes.
    
    This works in conjunction with the dependency-based auth system.
    """
    
    def __init__(
        self,
        app,
        public_paths: Optional[List[str]] = None,
        public_prefixes: Optional[List[str]] = None
    ):
        super().__init__(app)
        # Paths that don't require authentication
        self.public_paths = public_paths or [
            "/",
            "/health",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/api/v1/auth/login",
            "/api/v1/auth/login/oauth",
            "/api/v1/auth/refresh",
            "/api/v1/auth/password-reset/request",
            "/api/v1/auth/password-reset/confirm",
        ]
        self.public_prefixes = public_prefixes or []
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """Check if request requires authentication."""
        # Check if path is public
        path = request.url.path
        
        if path in self.public_paths:
            return await call_next(request)
        
        # Check public prefixes
        for prefix in self.public_prefixes:
            if path.startswith(prefix):
                return await call_next(request)
        
        # Path requires authentication - it will be handled by dependencies
        # This middleware just logs for monitoring
        if not hasattr(request.state, "user"):
            logger.debug(f"Unauthenticated request to protected path: {path}")
        
        return await call_next(request)