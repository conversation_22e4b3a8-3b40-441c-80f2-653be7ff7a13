"""
Enhanced FastAPI application with SSE support.
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware

from ....shared.container import Container
from src.core.utils.logging import get_logger
from .middleware import (
    RequestIdMiddleware,
    LoggingMiddleware,
    ErrorHandlerMiddleware,
    AuthenticationMiddleware
)
from .routes import analysis, markets, commodities, prices, sse, hypothesis
from .sse import SSEConnectionManager, SSEEventHandler

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Manage application lifecycle events.
    """
    # Startup
    logger.info("Starting Yemen Market Integration API v2")
    
    # For now, we'll skip the SSE services until container is properly configured
    logger.info("API startup complete (simplified)")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Yemen Market Integration API v2")
    logger.info("API shutdown complete")


def create_app(container: Container = None) -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        Configured FastAPI application
    """
    # Set up container if provided
    if container:
        from .dependencies import set_container
        set_container(container)
    
    app = FastAPI(
        title="Yemen Market Integration API",
        description="Econometric analysis of market integration in Yemen",
        version="2.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan
    )
    
    # Add middleware in reverse order (last added is first executed)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    app.add_middleware(ErrorHandlerMiddleware)
    app.add_middleware(AuthenticationMiddleware)
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(RequestIdMiddleware)
    
    # Include routers
    app.include_router(
        analysis.router,
        prefix="/api/v1",
        tags=["analysis"]
    )
    
    app.include_router(
        markets.router,
        prefix="/api/v1",
        tags=["markets"]
    )
    
    app.include_router(
        commodities.router,
        prefix="/api/v1",
        tags=["commodities"]
    )
    
    app.include_router(
        prices.router,
        prefix="/api/v1",
        tags=["prices"]
    )
    
    app.include_router(
        sse.router,
        prefix="/api/v1",
        tags=["sse", "real-time"]
    )
    
    app.include_router(
        hypothesis.router,
        prefix="/api/v1",
        tags=["hypothesis-testing"]
    )
    
    # Root endpoint
    @app.get("/")
    async def root():
        """API root endpoint."""
        return {
            "name": "Yemen Market Integration API",
            "version": "2.0.0",
            "status": "operational",
            "documentation": "/docs",
            "endpoints": {
                "analysis": "/api/v1/analysis",
                "markets": "/api/v1/markets",
                "commodities": "/api/v1/commodities",
                "prices": "/api/v1/prices",
                "sse": "/api/v1/sse",
                "hypothesis": "/api/v1/hypothesis"
            }
        }
    
    # Health check
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "service": "yemen-market-integration-api",
            "version": "2.0.0"
        }
    
    return app


# Create app instance with a basic container
try:
    container = Container()
    # Configure basic settings
    container.config.database.url.from_value("postgresql://localhost/yemen_market")
    container.config.cache.type.from_value("memory")
    container.config.cache.default_ttl.from_value(3600)
    container.config.cache.memory.max_size.from_value(1000)
    container.config.events.type.from_value("inmemory")
    container.config.events.queue_size.from_value(10000)
    container.config.external.hdx.timeout.from_value(30)
    container.config.external.wfp.api_key.from_value("")
    container.config.external.acled.api_key.from_value("")
    container.config.external.acled.email.from_value("")
    container.config.storage.policy_results_path.from_value("data/policy_results")
    
    app = create_app(container)
except Exception as e:
    logger.warning(f"Failed to configure container: {e}")
    app = create_app()


# SSE helper functions for routes
def get_sse_manager() -> SSEConnectionManager:
    """Get SSE manager from app state."""
    return app.state.sse_manager


def get_sse_handler() -> SSEEventHandler:
    """Get SSE handler from app state."""
    return app.state.sse_handler