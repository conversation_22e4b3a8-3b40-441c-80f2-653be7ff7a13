"""Pagination utilities for API responses."""

from typing import Generic, TypeVar, List, Optional
from pydantic import BaseModel, Field
from pydantic.generics import GenericModel


T = TypeVar('T')


class PaginationInfo(BaseModel):
    """Pagination metadata."""
    skip: int = Field(..., description="Number of items skipped")
    limit: int = Field(..., description="Maximum items per page")
    total: int = Field(..., description="Total number of items")
    has_more: bool = Field(..., description="Whether more items exist")
    
    @property
    def pages(self) -> int:
        """Calculate total number of pages."""
        return (self.total + self.limit - 1) // self.limit if self.limit > 0 else 0
    
    @property
    def current_page(self) -> int:
        """Calculate current page number (1-indexed)."""
        return (self.skip // self.limit) + 1 if self.limit > 0 else 1
    
    @property
    def next_skip(self) -> Optional[int]:
        """Calculate skip value for next page."""
        if self.has_more:
            return self.skip + self.limit
        return None
    
    @property
    def prev_skip(self) -> Optional[int]:
        """Calculate skip value for previous page."""
        if self.skip > 0:
            return max(0, self.skip - self.limit)
        return None


class PaginatedResponse(GenericModel, Generic[T]):
    """Generic paginated response."""
    data: List[T] = Field(..., description="List of items")
    pagination: PaginationInfo = Field(..., description="Pagination information")
    
    class Config:
        """Pydantic config."""
        # Required for generics to work properly
        arbitrary_types_allowed = True


class PaginationParams(BaseModel):
    """Common pagination query parameters."""
    skip: int = Field(0, ge=0, description="Number of items to skip")
    limit: int = Field(100, ge=1, le=1000, description="Maximum items to return")


def paginate_query(query, skip: int = 0, limit: int = 100):
    """
    Apply pagination to a SQLAlchemy query.
    
    Args:
        query: SQLAlchemy query object
        skip: Number of items to skip
        limit: Maximum items to return
        
    Returns:
        Paginated query
    """
    return query.offset(skip).limit(limit)


def create_paginated_response(
    items: List[T],
    total: int,
    skip: int,
    limit: int,
    response_model: type
) -> PaginatedResponse[T]:
    """
    Create a paginated response from a list of items.
    
    Args:
        items: List of items to paginate
        total: Total number of items (before pagination)
        skip: Number of items skipped
        limit: Maximum items per page
        response_model: Pydantic model class for items
        
    Returns:
        PaginatedResponse instance
    """
    pagination_info = PaginationInfo(
        skip=skip,
        limit=limit,
        total=total,
        has_more=skip + len(items) < total
    )
    
    # Convert items to response model if needed
    if response_model and items and not isinstance(items[0], response_model):
        items = [response_model.from_entity(item) for item in items]
    
    return PaginatedResponse[response_model](
        data=items,
        pagination=pagination_info
    )


class LinkHeader:
    """Helper class for creating Link headers for pagination."""
    
    @staticmethod
    def create(
        base_url: str,
        skip: int,
        limit: int,
        total: int
    ) -> Optional[str]:
        """
        Create Link header value for pagination.
        
        Returns string like:
        <http://api.example.com/items?skip=0&limit=10>; rel="first",
        <http://api.example.com/items?skip=10&limit=10>; rel="next"
        """
        links = []
        
        # First page
        links.append(f'<{base_url}?skip=0&limit={limit}>; rel="first"')
        
        # Previous page
        if skip > 0:
            prev_skip = max(0, skip - limit)
            links.append(f'<{base_url}?skip={prev_skip}&limit={limit}>; rel="prev"')
        
        # Next page
        if skip + limit < total:
            next_skip = skip + limit
            links.append(f'<{base_url}?skip={next_skip}&limit={limit}>; rel="next"')
        
        # Last page
        last_skip = max(0, total - limit)
        links.append(f'<{base_url}?skip={last_skip}&limit={limit}>; rel="last"')
        
        return ", ".join(links) if links else None