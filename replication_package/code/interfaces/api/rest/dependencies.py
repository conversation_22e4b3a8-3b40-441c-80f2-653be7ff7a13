"""FastAPI dependencies for dependency injection."""

from typing import Optional, Dict, Any

from fastapi import Depends, HTTPException, Header, Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from ....shared.container import Container
from ....infrastructure.security import verify_token, TokenData
from ....infrastructure.security.api_key_manager import APIKeyManager
from ....infrastructure.security.rbac import RBACManager, Permission
from ....infrastructure.logging import Logger

logger = get_logger(__name__)

# Global container instance (set by app factory)
_container: Optional[Container] = None


def set_container(container: Container) -> None:
    """Set the global container instance."""
    global _container
    _container = container


def get_container() -> Container:
    """Get the dependency injection container."""
    if _container is None:
        raise RuntimeError("Container not initialized")
    return _container


# Security
security = HTTPBearer(auto_error=False)


async def get_current_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    api_key: Optional[str] = Header(None, alias="X-API-Key"),
    container: Container = Depends(get_container)
) -> Dict[str, Any]:
    """
    Get current user from auth token or API key.
    
    Returns user data dictionary with permissions.
    """
    # Check JWT token first
    if credentials and credentials.credentials:
        token_data = verify_token(credentials.credentials)
        if not token_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # Get user permissions from roles
        rbac_manager = RBACManager()
        from ....infrastructure.security.rbac import Role
        user_roles = [Role(role) for role in token_data.roles]
        permissions = rbac_manager.get_user_permissions(user_roles)
        
        # Store in request state for middleware access
        user_data = {
            "sub": token_data.sub,
            "username": token_data.username,
            "email": token_data.email,
            "roles": token_data.roles,
            "permissions": [p.value for p in permissions],
            "auth_type": "jwt"
        }
        request.state.user = user_data
        return user_data
    
    # Check API key
    elif api_key:
        api_key_manager = container.api_key_manager()
        api_key_obj = await api_key_manager.verify_api_key(api_key)
        
        if not api_key_obj:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid API key"
            )
        
        # Check IP restrictions
        client_ip = request.client.host if request.client else None
        if client_ip and not api_key_obj.is_ip_allowed(client_ip):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied from this IP address"
            )
        
        # Record usage
        api_key_obj.record_usage()
        # Note: In production, we'd save this asynchronously
        
        # Store in request state
        user_data = {
            "sub": str(api_key_obj.user_id) if api_key_obj.user_id else f"api_key_{api_key_obj.id}",
            "username": f"api_key_{api_key_obj.name}",
            "email": None,
            "roles": ["api_user"],
            "permissions": api_key_obj.permissions,
            "auth_type": "api_key",
            "api_key_id": str(api_key_obj.id)
        }
        request.state.user = user_data
        return user_data
    
    # No authentication provided
    else:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"}
        )


def require_auth(user: Dict[str, Any] = Depends(get_current_user)) -> Dict[str, Any]:
    """Require authentication."""
    if not user or user.get("sub") == "anonymous":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"}
        )
    return user


async def get_current_user_optional(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    api_key: Optional[str] = Header(None, alias="X-API-Key"),
    container: Container = Depends(get_container)
) -> Optional[Dict[str, Any]]:
    """
    Get current user if authenticated, None otherwise.
    
    Use this for endpoints that support both authenticated and anonymous access.
    """
    try:
        return await get_current_user(request, credentials, api_key, container)
    except HTTPException:
        return None


# Command and Query handlers
def get_command_handler(command_name: str):
    """Get command handler from container."""
    def _get_handler(container: Container = Depends(get_container)):
        handler_name = f"{command_name}_handler"
        if not hasattr(container, handler_name):
            raise ValueError(f"Unknown command handler: {command_name}")
        return getattr(container, handler_name)()
    return _get_handler


def get_query_handler(query_name: str):
    """Get query handler from container."""
    def _get_handler(container: Container = Depends(get_container)):
        handler_name = f"{query_name}_handler"
        if not hasattr(container, handler_name):
            raise ValueError(f"Unknown query handler: {query_name}")
        return getattr(container, handler_name)()
    return _get_handler


# Service dependencies
def get_three_tier_analysis_service(container: Container = Depends(get_container)):
    """Get three-tier analysis service from container."""
    return container.three_tier_analysis_service()


def get_analysis_orchestrator(container: Container = Depends(get_container)):
    """Get analysis orchestrator from container."""
    return container.analysis_orchestrator()


def get_data_preparation_service(container: Container = Depends(get_container)):
    """Get data preparation service from container."""
    return container.data_preparation_service()


def get_model_estimator_service(container: Container = Depends(get_container)):
    """Get model estimator service from container."""
    return container.model_estimator_service()


def get_hypothesis_testing_service(container: Container = Depends(get_container)):
    """Get hypothesis testing service from container."""
    # For now, create a new instance since it may not be in the container
    from ....application.services import HypothesisTestingService
    return HypothesisTestingService()