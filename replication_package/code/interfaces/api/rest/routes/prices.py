"""Price endpoints."""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query

from .....core.domain.market.value_objects import MarketId, Commodity
from ..dependencies import get_container
from ..schemas.prices import PriceResponse, PriceListResponse, PriceStatistics


router = APIRouter()


@router.get("/", response_model=PriceListResponse)
async def list_prices(
    market_id: Optional[str] = Query(None, description="Filter by market ID"),
    commodity_code: Optional[str] = Query(None, description="Filter by commodity code"),
    start_date: Optional[datetime] = Query(None, description="Start date for price data"),
    end_date: Optional[datetime] = Query(None, description="End date for price data"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    container = Depends(get_container)
) -> PriceListResponse:
    """List price observations with filters."""
    
    if not market_id or not commodity_code:
        raise HTTPException(
            status_code=400,
            detail="Both market_id and commodity_code are required"
        )
    
    # Default date range if not provided
    if not end_date:
        end_date = datetime.utcnow()
    if not start_date:
        start_date = datetime(2019, 1, 1)
    
    async with container.unit_of_work() as uow:
        # Create commodity object
        # In production, would fetch from commodity repository
        commodity = Commodity(
            code=commodity_code,
            name=commodity_code,
            category="food",
            standard_unit="kg"
        )
        
        # Fetch prices
        prices = await uow.prices.find_by_market_and_commodity(
            MarketId(market_id),
            commodity,
            start_date,
            end_date
        )
        
        # Pagination
        total = len(prices)
        prices = prices[skip:skip + limit]
        
        # Convert to response format
        price_responses = [
            PriceResponse(
                market_id=p.market_id.value,
                commodity_code=p.commodity.code,
                commodity_name=p.commodity.name,
                price_amount=float(p.price.amount),
                price_currency=p.price.currency,
                price_unit=p.price.unit,
                observed_date=p.observed_date,
                source=p.source,
                quality=p.quality,
                observations_count=p.observations_count
            )
            for p in prices
        ]
        
        return PriceListResponse(
            prices=price_responses,
            total=total,
            skip=skip,
            limit=limit
        )


@router.get("/statistics", response_model=PriceStatistics)
async def get_price_statistics(
    market_id: str = Query(..., description="Market ID"),
    commodity_code: str = Query(..., description="Commodity code"),
    start_date: Optional[datetime] = Query(None, description="Start date"),
    end_date: Optional[datetime] = Query(None, description="End date"),
    container = Depends(get_container)
) -> PriceStatistics:
    """Get price statistics for a market-commodity pair."""
    
    # Default date range
    if not end_date:
        end_date = datetime.utcnow()
    if not start_date:
        start_date = datetime(2019, 1, 1)
    
    async with container.unit_of_work() as uow:
        # Create commodity object
        commodity = Commodity(
            code=commodity_code,
            name=commodity_code,
            category="food",
            standard_unit="kg"
        )
        
        # Fetch prices
        prices = await uow.prices.find_by_market_and_commodity(
            MarketId(market_id),
            commodity,
            start_date,
            end_date
        )
        
        if not prices:
            raise HTTPException(
                status_code=404,
                detail=f"No price data found for {market_id} - {commodity_code}"
            )
        
        # Calculate statistics
        price_values = [float(p.price.amount) for p in prices]
        
        import statistics
        
        return PriceStatistics(
            market_id=market_id,
            commodity_code=commodity_code,
            start_date=start_date,
            end_date=end_date,
            count=len(prices),
            mean=statistics.mean(price_values),
            median=statistics.median(price_values),
            std_dev=statistics.stdev(price_values) if len(price_values) > 1 else 0,
            min_price=min(price_values),
            max_price=max(price_values),
            currency=prices[0].price.currency,
            unit=prices[0].price.unit
        )


@router.get("/transmission")
async def analyze_price_transmission(
    source_market: str = Query(..., description="Source market ID"),
    target_market: str = Query(..., description="Target market ID"),
    commodity_code: str = Query(..., description="Commodity code"),
    start_date: Optional[datetime] = Query(None, description="Start date"),
    end_date: Optional[datetime] = Query(None, description="End date"),
    container = Depends(get_container)
):
    """Analyze price transmission between two markets."""
    
    # Default date range
    if not end_date:
        end_date = datetime.utcnow()
    if not start_date:
        start_date = datetime(2019, 1, 1)
    
    async with container.unit_of_work() as uow:
        # Create commodity object
        commodity = Commodity(
            code=commodity_code,
            name=commodity_code,
            category="food",
            standard_unit="kg"
        )
        
        # Fetch prices for both markets
        source_prices = await uow.prices.find_by_market_and_commodity(
            MarketId(source_market),
            commodity,
            start_date,
            end_date
        )
        
        target_prices = await uow.prices.find_by_market_and_commodity(
            MarketId(target_market),
            commodity,
            start_date,
            end_date
        )
        
        if not source_prices or not target_prices:
            raise HTTPException(
                status_code=404,
                detail="Insufficient price data for transmission analysis"
            )
        
        # Calculate transmission using domain service
        from .....core.domain.market.value_objects import MarketPair
        
        market_pair = MarketPair(
            source=MarketId(source_market),
            target=MarketId(target_market)
        )
        
        transmission_service = container.price_transmission_service()
        
        try:
            metrics = transmission_service.calculate_transmission(
                source_prices=source_prices,
                target_prices=target_prices,
                market_pair=market_pair
            )
            
            return {
                "source_market": source_market,
                "target_market": target_market,
                "commodity": commodity_code,
                "period": {
                    "start": metrics.period_start.isoformat(),
                    "end": metrics.period_end.isoformat()
                },
                "metrics": {
                    "correlation": metrics.correlation,
                    "beta_coefficient": metrics.beta_coefficient,
                    "adjustment_speed": metrics.adjustment_speed,
                    "half_life_days": metrics.half_life_days
                }
            }
            
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Transmission analysis failed: {str(e)}"
            )