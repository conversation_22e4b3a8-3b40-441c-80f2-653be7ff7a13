"""Authentication endpoints for login, logout, and token management."""

from datetime import <PERSON><PERSON><PERSON>
from typing import Optional

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status, Response, Body
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel

from ......infrastructure.security import (
    create_access_token,
    create_refresh_token,
    verify_token,
    verify_password,
    J<PERSON><PERSON><PERSON>ler
)
from ......infrastructure.logging import Logger
from ......core.domain.auth import AuthenticationService
from ......shared.container import Container

logger = get_logger(__name__)

router = APIRouter(prefix="/auth", tags=["Authentication"])


# Request/Response models
class LoginRequest(BaseModel):
    """Login request model."""
    username: str
    password: str
    remember_me: bool = False


class LoginResponse(BaseModel):
    """Login response model."""
    access_token: str
    refresh_token: Optional[str] = None
    token_type: str = "bearer"
    expires_in: int  # seconds
    user: dict


class RefreshTokenRequest(BaseModel):
    """Refresh token request."""
    refresh_token: str


class RefreshTokenResponse(BaseModel):
    """Refresh token response."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class LogoutRequest(BaseModel):
    """Logout request."""
    refresh_token: Optional[str] = None
    revoke_all_tokens: bool = False


@router.post("/login", response_model=LoginResponse)
async def login(
    request: LoginRequest,
    auth_service: AuthenticationService = Depends(Container.auth_service)
):
    """
    Authenticate user and return access/refresh tokens.
    
    - **username**: Username or email address
    - **password**: User password
    - **remember_me**: If true, includes refresh token for extended sessions
    """
    try:
        # Authenticate user
        user, error = await auth_service.authenticate_user(
            username=request.username,
            password=request.password,
            password_verifier=verify_password
        )
        
        if error:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=error,
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # Create tokens
        token_data = {
            "sub": str(user.id),
            "username": user.username,
            "email": user.email.value,
            "roles": [role.value for role in user.roles],
            "permissions": []  # Will be populated based on roles
        }
        
        # Set token expiration based on remember_me
        access_expires = timedelta(minutes=30)
        if request.remember_me:
            access_expires = timedelta(hours=24)
        
        access_token = create_access_token(token_data, access_expires)
        
        response_data = {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": int(access_expires.total_seconds()),
            "user": {
                "id": str(user.id),
                "username": user.username,
                "email": user.email.value,
                "full_name": user.full_name,
                "roles": [role.value for role in user.roles],
                "email_verified": user.email_verified
            }
        }
        
        # Include refresh token if remember_me
        if request.remember_me:
            refresh_token = create_refresh_token(token_data)
            response_data["refresh_token"] = refresh_token
            
            # Store refresh token
            await auth_service.create_refresh_token(
                user_id=user.id,
                token_value=refresh_token,
                expires_in_days=7
            )
        
        logger.info(f"User {user.username} logged in successfully")
        return LoginResponse(**response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication failed"
        )


@router.post("/login/oauth", response_model=LoginResponse)
async def oauth_login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    auth_service: AuthenticationService = Depends(Container.auth_service)
):
    """
    OAuth2 compatible login endpoint.
    
    This endpoint follows the OAuth2 password flow standard.
    """
    request = LoginRequest(
        username=form_data.username,
        password=form_data.password,
        remember_me=True  # OAuth flows typically want refresh tokens
    )
    
    return await login(request, auth_service)


@router.post("/refresh", response_model=RefreshTokenResponse)
async def refresh_token(
    request: RefreshTokenRequest,
    auth_service: AuthenticationService = Depends(Container.auth_service)
):
    """
    Refresh access token using refresh token.
    
    Returns new access and refresh tokens.
    """
    try:
        # Validate refresh token
        user, error = await auth_service.validate_refresh_token(request.refresh_token)
        
        if error:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=error
            )
        
        # Create new tokens
        token_data = {
            "sub": str(user.id),
            "username": user.username,
            "email": user.email.value,
            "roles": [role.value for role in user.roles],
            "permissions": []
        }
        
        new_access_token = create_access_token(token_data)
        new_refresh_token = create_refresh_token(token_data)
        
        # Revoke old refresh token
        await auth_service.revoke_refresh_token(request.refresh_token)
        
        # Store new refresh token
        await auth_service.create_refresh_token(
            user_id=user.id,
            token_value=new_refresh_token,
            expires_in_days=7
        )
        
        return RefreshTokenResponse(
            access_token=new_access_token,
            refresh_token=new_refresh_token,
            token_type="bearer",
            expires_in=1800  # 30 minutes
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/logout", status_code=status.HTTP_204_NO_CONTENT)
async def logout(
    request: LogoutRequest,
    current_user: dict = Depends(Container.get_current_user),
    auth_service: AuthenticationService = Depends(Container.auth_service)
):
    """
    Logout user and optionally revoke tokens.
    
    - **refresh_token**: Optional refresh token to revoke
    - **revoke_all_tokens**: If true, revokes all user's refresh tokens
    """
    try:
        user_id = current_user.get("sub")
        
        if request.revoke_all_tokens:
            # Revoke all refresh tokens for user
            await auth_service.revoke_all_user_tokens(user_id)
            logger.info(f"Revoked all tokens for user {user_id}")
        elif request.refresh_token:
            # Revoke specific refresh token
            await auth_service.revoke_refresh_token(request.refresh_token)
            logger.info(f"Revoked refresh token for user {user_id}")
        
        return Response(status_code=status.HTTP_204_NO_CONTENT)
        
    except Exception as e:
        logger.error(f"Logout error: {e}")
        # Don't reveal errors during logout
        return Response(status_code=status.HTTP_204_NO_CONTENT)


@router.get("/verify")
async def verify_token_endpoint(
    current_user: dict = Depends(Container.get_current_user)
):
    """
    Verify current access token and return user info.
    
    This endpoint can be used to check if a token is still valid.
    """
    return {
        "valid": True,
        "user": {
            "id": current_user.get("sub"),
            "username": current_user.get("username"),
            "email": current_user.get("email"),
            "roles": current_user.get("roles", [])
        }
    }


@router.post("/password-reset/request")
async def request_password_reset(
    email: str = Body(..., embed=True),
    user_service = Depends(Container.user_service)
):
    """
    Request password reset email.
    
    Always returns success to prevent user enumeration.
    """
    try:
        # In production, this would send an email
        # For now, we'll just log
        logger.info(f"Password reset requested for email: {email}")
        
        return {
            "message": "If the email exists, a password reset link has been sent."
        }
        
    except Exception as e:
        logger.error(f"Password reset request error: {e}")
        # Don't reveal errors
        return {
            "message": "If the email exists, a password reset link has been sent."
        }


@router.post("/password-reset/confirm")
async def confirm_password_reset(
    token: str = Body(...),
    new_password: str = Body(...),
    user_service = Depends(Container.user_service)
):
    """
    Confirm password reset with token.
    """
    # TODO: Implement password reset confirmation
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Password reset confirmation not yet implemented"
    )