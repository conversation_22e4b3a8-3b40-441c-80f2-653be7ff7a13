"""User management endpoints."""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, EmailStr

from ......infrastructure.security import hash_password, require_permission
from ......infrastructure.security.rbac import Permission, Role
from ......infrastructure.logging import Logger
from ......core.domain.auth import UserService, User
from ......core.domain.auth.value_objects import UserRole, UserStatus
from ......shared.container import Container

logger = get_logger(__name__)

router = APIRouter(prefix="/users", tags=["User Management"])


# Request/Response models
class CreateUserRequest(BaseModel):
    """Create user request."""
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    roles: List[str] = [UserRole.VIEWER.value]


class UpdateUserRequest(BaseModel):
    """Update user request."""
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    roles: Optional[List[str]] = None
    status: Optional[str] = None


class UserResponse(BaseModel):
    """User response model."""
    id: str
    username: str
    email: str
    full_name: Optional[str]
    roles: List[str]
    status: str
    email_verified: bool
    created_at: str
    updated_at: str
    last_login: Optional[str]


class ChangePasswordRequest(BaseModel):
    """Change password request."""
    current_password: str
    new_password: str


@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
@require_permission(Permission.USER_CREATE)
async def create_user(
    request: CreateUserRequest,
    current_user: dict = Depends(Container.get_current_user),
    user_service: UserService = Depends(Container.user_service)
):
    """
    Create a new user.
    
    Requires USER_CREATE permission.
    """
    try:
        # Hash password
        password_hash = hash_password(request.password)
        
        # Create user
        user, error = await user_service.create_user(
            username=request.username,
            email=request.email,
            password_hash=password_hash,
            full_name=request.full_name,
            roles=request.roles
        )
        
        if error:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error
            )
        
        return UserResponse(
            id=str(user.id),
            username=user.username,
            email=user.email.value,
            full_name=user.full_name,
            roles=[role.value for role in user.roles],
            status=user.status.value,
            email_verified=user.email_verified,
            created_at=user.created_at.isoformat(),
            updated_at=user.updated_at.isoformat(),
            last_login=user.last_login.isoformat() if user.last_login else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user"
        )


@router.get("/", response_model=List[UserResponse])
@require_permission(Permission.USER_READ)
async def list_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    include_inactive: bool = Query(False),
    current_user: dict = Depends(Container.get_current_user),
    user_repository = Depends(Container.user_repository)
):
    """
    List users with pagination.
    
    Requires USER_READ permission.
    """
    try:
        users = await user_repository.list_users(
            skip=skip,
            limit=limit,
            include_inactive=include_inactive
        )
        
        return [
            UserResponse(
                id=str(user.id),
                username=user.username,
                email=user.email.value,
                full_name=user.full_name,
                roles=[role.value for role in user.roles],
                status=user.status.value,
                email_verified=user.email_verified,
                created_at=user.created_at.isoformat(),
                updated_at=user.updated_at.isoformat(),
                last_login=user.last_login.isoformat() if user.last_login else None
            )
            for user in users
        ]
        
    except Exception as e:
        logger.error(f"Error listing users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list users"
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user_profile(
    current_user: dict = Depends(Container.get_current_user),
    user_repository = Depends(Container.user_repository)
):
    """Get current user's profile."""
    try:
        user_id = UUID(current_user.get("sub"))
        user = await user_repository.find_by_id(user_id)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return UserResponse(
            id=str(user.id),
            username=user.username,
            email=user.email.value,
            full_name=user.full_name,
            roles=[role.value for role in user.roles],
            status=user.status.value,
            email_verified=user.email_verified,
            created_at=user.created_at.isoformat(),
            updated_at=user.updated_at.isoformat(),
            last_login=user.last_login.isoformat() if user.last_login else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user profile"
        )


@router.get("/{user_id}", response_model=UserResponse)
@require_permission(Permission.USER_READ)
async def get_user(
    user_id: UUID,
    current_user: dict = Depends(Container.get_current_user),
    user_repository = Depends(Container.user_repository)
):
    """
    Get user by ID.
    
    Requires USER_READ permission.
    """
    try:
        user = await user_repository.find_by_id(user_id)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return UserResponse(
            id=str(user.id),
            username=user.username,
            email=user.email.value,
            full_name=user.full_name,
            roles=[role.value for role in user.roles],
            status=user.status.value,
            email_verified=user.email_verified,
            created_at=user.created_at.isoformat(),
            updated_at=user.updated_at.isoformat(),
            last_login=user.last_login.isoformat() if user.last_login else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user"
        )


@router.patch("/{user_id}", response_model=UserResponse)
@require_permission(Permission.USER_UPDATE)
async def update_user(
    user_id: UUID,
    request: UpdateUserRequest,
    current_user: dict = Depends(Container.get_current_user),
    user_service: UserService = Depends(Container.user_service),
    user_repository = Depends(Container.user_repository)
):
    """
    Update user details.
    
    Requires USER_UPDATE permission.
    """
    try:
        user = await user_repository.find_by_id(user_id)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update fields
        if request.email:
            from ......core.domain.auth.value_objects import Email
            user.email = Email(value=request.email)
            user.email_verified = False  # Require re-verification
        
        if request.full_name is not None:
            user.full_name = request.full_name
        
        if request.roles is not None:
            user, error = await user_service.update_user_roles(user_id, request.roles)
            if error:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=error
                )
        
        if request.status:
            try:
                user.status = UserStatus(request.status)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid status: {request.status}"
                )
        
        await user_repository.save(user)
        
        return UserResponse(
            id=str(user.id),
            username=user.username,
            email=user.email.value,
            full_name=user.full_name,
            roles=[role.value for role in user.roles],
            status=user.status.value,
            email_verified=user.email_verified,
            created_at=user.created_at.isoformat(),
            updated_at=user.updated_at.isoformat(),
            last_login=user.last_login.isoformat() if user.last_login else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user"
        )


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
@require_permission(Permission.USER_DELETE)
async def delete_user(
    user_id: UUID,
    cascade_api_keys: bool = Query(True),
    current_user: dict = Depends(Container.get_current_user),
    user_service: UserService = Depends(Container.user_service)
):
    """
    Delete a user.
    
    Requires USER_DELETE permission.
    """
    try:
        # Prevent self-deletion
        if str(user_id) == current_user.get("sub"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete your own account"
            )
        
        success = await user_service.delete_user(
            user_id=user_id,
            cascade_api_keys=cascade_api_keys
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return None
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete user"
        )


@router.post("/me/change-password", status_code=status.HTTP_204_NO_CONTENT)
async def change_password(
    request: ChangePasswordRequest,
    current_user: dict = Depends(Container.get_current_user),
    auth_service = Depends(Container.auth_service),
    user_repository = Depends(Container.user_repository)
):
    """Change current user's password."""
    try:
        user_id = UUID(current_user.get("sub"))
        user = await user_repository.find_by_id(user_id)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Verify current password
        from ......infrastructure.security import verify_password
        if not verify_password(request.current_password, user.password_hash.value):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )
        
        # Update password
        from ......core.domain.auth.value_objects import HashedPassword
        user.password_hash = HashedPassword(value=hash_password(request.new_password))
        await user_repository.save(user)
        
        # Revoke all refresh tokens
        await auth_service.revoke_all_user_tokens(user_id)
        
        return None
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error changing password: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to change password"
        )