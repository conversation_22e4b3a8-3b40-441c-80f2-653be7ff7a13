"""Commodity API routes."""

from typing import List, Optional
from datetime import date
from fastapi import APIRouter, Depends, HTTPException, Query

from .....shared.container import Container
from .....infrastructure.logging import Logger
from ..dependencies import get_container
from ..schemas.commodities import (
    CommodityResponse,
    CommodityListResponse,
    CommodityPricesResponse,
    PriceStatistics
)

logger = get_logger(__name__)

router = APIRouter(prefix="/commodities", tags=["commodities"])


@router.get("", response_model=CommodityListResponse)
async def list_commodities(
    category: Optional[str] = Query(None, description="Filter by category (FOOD, FUEL, etc.)"),
    search: Optional[str] = Query(None, description="Search commodity names"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    container: Container = Depends(get_container)
) -> CommodityListResponse:
    """
    List all commodities with optional filtering.
    
    Query parameters:
    - category: Filter by commodity category (FOOD, FUEL, NON_FOOD)
    - search: Search for commodities by name
    - skip: Pagination offset
    - limit: Maximum results (max 1000)
    """
    try:
        async with container.unit_of_work() as uow:
            # Get all commodities
            commodities = await uow.commodities.find_all()
            
            # Apply filters
            if category:
                commodities = [c for c in commodities if c.category == category]
            
            if search:
                search_lower = search.lower()
                commodities = [
                    c for c in commodities 
                    if search_lower in c.name.lower() or search_lower in c.code.lower()
                ]
            
            # Sort by name
            commodities.sort(key=lambda x: x.name)
            
            # Pagination
            total = len(commodities)
            commodities = commodities[skip:skip + limit]
            
            return CommodityListResponse(
                data=[CommodityResponse.from_entity(c) for c in commodities],
                total=total,
                skip=skip,
                limit=limit,
                has_more=skip + limit < total
            )
            
    except Exception as e:
        logger.error(f"Error listing commodities: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list commodities: {str(e)}"
        )


@router.get("/{commodity_id}", response_model=CommodityResponse)
async def get_commodity(
    commodity_id: str,
    container: Container = Depends(get_container)
) -> CommodityResponse:
    """
    Get details for a specific commodity.
    """
    try:
        async with container.unit_of_work() as uow:
            commodity = await uow.commodities.find_by_id(commodity_id)
            
            if not commodity:
                raise HTTPException(
                    status_code=404,
                    detail=f"Commodity with ID {commodity_id} not found"
                )
            
            return CommodityResponse.from_entity(commodity)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting commodity {commodity_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get commodity: {str(e)}"
        )


@router.get("/{commodity_id}/prices", response_model=CommodityPricesResponse)
async def get_commodity_prices(
    commodity_id: str,
    market_id: Optional[str] = Query(None, description="Filter by specific market"),
    governorate: Optional[str] = Query(None, description="Filter by governorate"),
    start_date: Optional[date] = Query(None, description="Start date for price series"),
    end_date: Optional[date] = Query(None, description="End date for price series"),
    currency: str = Query("USD", enum=["USD", "YER"], description="Currency for prices"),
    aggregation: str = Query("daily", enum=["daily", "weekly", "monthly"], description="Price aggregation level"),
    container: Container = Depends(get_container)
) -> CommodityPricesResponse:
    """
    Get price time series for a specific commodity across markets.
    
    Query parameters:
    - market_id: Filter by specific market
    - governorate: Filter by governorate
    - start_date: Start date for price series
    - end_date: End date for price series
    - currency: Currency for prices (USD or YER)
    - aggregation: Price aggregation level (daily, weekly, monthly)
    """
    try:
        async with container.unit_of_work() as uow:
            # Validate commodity exists
            commodity = await uow.commodities.find_by_id(commodity_id)
            
            if not commodity:
                raise HTTPException(
                    status_code=404,
                    detail=f"Commodity with ID {commodity_id} not found"
                )
            
            # Get prices
            prices = await uow.prices.find_by_commodity(
                commodity_id=commodity_id,
                market_id=market_id,
                governorate=governorate,
                start_date=start_date,
                end_date=end_date
            )
            
            # Convert currency if needed
            if currency != "USD":
                prices = await convert_prices_to_currency(prices, target_currency=currency, uow=uow)
            
            # Aggregate prices if needed
            if aggregation != "daily":
                prices = aggregate_prices(prices, aggregation)
            
            # Group prices by market
            market_prices = {}
            for price in prices:
                if price.market_id not in market_prices:
                    market_prices[price.market_id] = []
                market_prices[price.market_id].append(price)
            
            # Calculate statistics
            statistics = calculate_commodity_statistics(prices)
            
            return CommodityPricesResponse(
                commodity_id=commodity_id,
                commodity_name=commodity.name,
                currency=currency,
                aggregation=aggregation,
                market_prices=market_prices,
                statistics=statistics
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting prices for commodity {commodity_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get commodity prices: {str(e)}"
        )


# Helper functions
async def convert_prices_to_currency(prices: List, target_currency: str, uow) -> List:
    """
    Convert prices to target currency.
    """
    # In a real implementation, this would use exchange rate service
    # For now, just return as-is
    return prices


def aggregate_prices(prices: List, aggregation: str) -> List:
    """
    Aggregate prices by time period.
    """
    import pandas as pd
    
    if not prices:
        return prices
    
    # Convert to DataFrame for easy aggregation
    df = pd.DataFrame([
        {
            'date': p.date,
            'price': p.price,
            'market_id': p.market_id,
            'commodity': p.commodity,
            'currency': p.currency
        }
        for p in prices
    ])
    
    # Set date as index
    df['date'] = pd.to_datetime(df['date'])
    df.set_index('date', inplace=True)
    
    # Aggregate based on period
    if aggregation == "weekly":
        freq = 'W'
    elif aggregation == "monthly":
        freq = 'M'
    else:
        return prices
    
    # Group by market and resample
    aggregated = []
    for (market_id, commodity, currency), group in df.groupby(['market_id', 'commodity', 'currency']):
        resampled = group['price'].resample(freq).mean()
        
        for date, price in resampled.items():
            if pd.notna(price):
                aggregated.append({
                    'date': date.date(),
                    'price': float(price),
                    'market_id': market_id,
                    'commodity': commodity,
                    'currency': currency
                })
    
    # Convert back to price objects
    class PricePoint:
        def __init__(self, date, price, market_id, commodity, currency):
            self.date = date
            self.price = price
            self.market_id = market_id
            self.commodity = commodity
            self.currency = currency
    
    return [PricePoint(**p) for p in aggregated]


def calculate_commodity_statistics(prices: List) -> PriceStatistics:
    """
    Calculate statistics for commodity prices.
    """
    if not prices:
        return PriceStatistics(
            count=0,
            mean=None,
            std=None,
            min=None,
            max=None,
            cv=None,
            trend=None,
            markets_count=0
        )
    
    import numpy as np
    
    price_values = [p.price for p in prices]
    markets = set(p.market_id for p in prices)
    
    mean = float(np.mean(price_values))
    std = float(np.std(price_values))
    
    return PriceStatistics(
        count=len(price_values),
        mean=mean,
        std=std,
        min=float(np.min(price_values)),
        max=float(np.max(price_values)),
        cv=std / mean if mean > 0 else None,
        trend=calculate_trend(price_values),
        markets_count=len(markets)
    )


def calculate_trend(prices: List[float]) -> str:
    """
    Calculate simple trend direction.
    """
    if len(prices) < 2:
        return "stable"
    
    # Simple linear regression slope
    import numpy as np
    x = np.arange(len(prices))
    slope = np.polyfit(x, prices, 1)[0]
    
    if slope > 0.01:
        return "increasing"
    elif slope < -0.01:
        return "decreasing"
    else:
        return "stable"