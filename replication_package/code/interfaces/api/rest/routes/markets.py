"""Market endpoints."""

from datetime import datetime, date
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query

from .....core.domain.market.value_objects import MarketId
from ..dependencies import get_container, get_current_user
from ..schemas.markets import (
    MarketResponse, 
    MarketListResponse,
    PriceSeriesResponse,
    PricePointDTO,
    PaginationInfo
)
from .....infrastructure.logging import Logger

logger = get_logger(__name__)

router = APIRouter(prefix="/markets", tags=["markets"])


@router.get("/", response_model=MarketListResponse)
async def list_markets(
    governorate: Optional[str] = Query(None, description="Filter by governorate"),
    district: Optional[str] = Query(None, description="Filter by district"),
    market_type: Optional[str] = Query(None, description="Filter by market type"),
    active_at: Optional[datetime] = Query(None, description="Filter markets active at date"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    container = Depends(get_container)
) -> MarketListResponse:
    """List markets with optional filters."""
    
    async with container.unit_of_work() as uow:
        # Apply filters
        if governorate:
            markets = await uow.markets.find_by_governorate(governorate)
        elif active_at:
            markets = await uow.markets.find_active_at(active_at)
        else:
            # Get all markets
            markets = await uow.markets.find_all()
        
        # Apply additional filters
        if district:
            markets = [m for m in markets if m.district == district]
        if market_type:
            markets = [m for m in markets if m.market_type.value == market_type]
        
        # Pagination
        total = len(markets)
        markets = markets[skip:skip + limit]
        
        # Convert to response format
        market_responses = [
            MarketResponse(
                market_id=m.market_id.value,
                name=m.name,
                governorate=m.governorate,
                district=m.district,
                market_type=m.market_type.value,
                latitude=m.coordinates.latitude,
                longitude=m.coordinates.longitude,
                active_since=m.active_since,
                active_until=m.active_until
            )
            for m in markets
        ]
        
        return MarketListResponse(
            markets=market_responses,
            total=total,
            skip=skip,
            limit=limit
        )


@router.get("/{market_id}", response_model=MarketResponse)
async def get_market(
    market_id: str,
    container = Depends(get_container)
) -> MarketResponse:
    """Get a specific market by ID."""
    
    async with container.unit_of_work() as uow:
        market = await uow.markets.find_by_id(MarketId(market_id))
        
        if not market:
            raise HTTPException(
                status_code=404,
                detail=f"Market {market_id} not found"
            )
        
        return MarketResponse(
            market_id=market.market_id.value,
            name=market.name,
            governorate=market.governorate,
            district=market.district,
            market_type=market.market_type.value,
            latitude=market.coordinates.latitude,
            longitude=market.coordinates.longitude,
            active_since=market.active_since,
            active_until=market.active_until
        )


@router.get("/{market_id}/accessibility")
async def get_market_accessibility(
    market_id: str,
    k_nearest: int = Query(5, ge=1, le=20, description="Number of nearest markets"),
    container = Depends(get_container)
):
    """Get accessibility metrics for a market."""
    
    async with container.unit_of_work() as uow:
        market = await uow.markets.find_by_id(MarketId(market_id))
        
        if not market:
            raise HTTPException(
                status_code=404,
                detail=f"Market {market_id} not found"
            )
        
        # Get all markets for accessibility calculation
        all_markets = await uow.markets.find_active_at(datetime.utcnow())
        
        # Calculate accessibility using spatial analysis service
        from .....core.domain.geography.services import SpatialAnalysisService
        spatial_service = SpatialAnalysisService()
        
        # Get governorates for control zone analysis
        # Fetch unique governorates from markets
        governorate_names = list(set(m.governorate for m in all_markets))
        
        # Create governorate entities (simplified - would fetch from repository)
        from .....core.domain.geography.entities import Governorate
        governorates = [
            Governorate(
                governorate_id=f"gov_{name.lower().replace(' ', '_')}",
                name=name,
                control_zone="government" if name in ["Sana'a", "Amanat Al Asimah"] else "other",
                population=1000000,  # Would fetch real data
                area_km2=10000  # Would fetch real data
            )
            for name in governorate_names
        ]
        
        accessibility = spatial_service.calculate_market_accessibility(
            market=market,
            all_markets=all_markets,
            governorates=governorates,
            k_nearest=k_nearest
        )
        
        return {
            "market_id": market_id,
            "accessibility_metrics": {
                "isolation_index": accessibility.isolation_index,
                "connectivity_score": accessibility.connectivity_score,
                "average_distance_to_others": accessibility.average_distance_to_others,
                "nearest_markets": [
                    {
                        "market_id": mid.value,
                        "distance_km": distance
                    }
                    for mid, distance in accessibility.nearest_markets
                ],
                "control_zone_accessibility": accessibility.control_zone_accessibility
            }
        }


@router.get("/{market_id}/prices", response_model=PriceSeriesResponse)
async def get_market_prices(
    market_id: str,
    commodity: Optional[str] = Query(None, description="Filter by commodity"),
    start_date: Optional[date] = Query(None, description="Start date for price series"),
    end_date: Optional[date] = Query(None, description="End date for price series"),
    currency: str = Query("USD", enum=["USD", "YER"], description="Currency for prices"),
    container = Depends(get_container)
) -> PriceSeriesResponse:
    """
    Get price time series for a specific market.
    
    Query parameters:
    - commodity: Filter by specific commodity (e.g., 'Wheat', 'Rice')
    - start_date: Start date for price series
    - end_date: End date for price series
    - currency: Currency for prices (USD or YER)
    """
    try:
        async with container.unit_of_work() as uow:
            # Validate market exists
            market = await uow.markets.find_by_id(MarketId(market_id))
            
            if not market:
                raise HTTPException(
                    status_code=404,
                    detail=f"Market with ID {market_id} not found"
                )
            
            # Get prices using the market service or repository
            # In a real implementation, this would use a PriceRepository
            prices = await uow.prices.find_by_market(
                market_id=market_id,
                commodity=commodity,
                start_date=start_date,
                end_date=end_date
            )
            
            # Convert currency if needed
            if currency != "USD":
                prices = await convert_prices_to_currency(prices, target_currency=currency, uow=uow)
            
            # Calculate statistics
            statistics = calculate_price_statistics(prices)
            
            return PriceSeriesResponse(
                market_id=market_id,
                commodity=commodity,
                currency=currency,
                data=[PricePointDTO.from_entity(p) for p in prices],
                statistics=statistics
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting prices for market {market_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get market prices: {str(e)}"
        )


# Helper functions
async def convert_prices_to_currency(prices: List, target_currency: str, uow) -> List:
    """
    Convert prices to target currency.
    """
    # In a real implementation, this would use exchange rate service
    # For now, just return as-is
    return prices


def calculate_price_statistics(prices: List) -> dict:
    """
    Calculate basic statistics for price series.
    """
    if not prices:
        return {
            "count": 0,
            "mean": None,
            "std": None,
            "min": None,
            "max": None,
            "trend": None
        }
    
    import numpy as np
    
    price_values = [p.price for p in prices]
    
    return {
        "count": len(price_values),
        "mean": float(np.mean(price_values)),
        "std": float(np.std(price_values)),
        "min": float(np.min(price_values)),
        "max": float(np.max(price_values)),
        "trend": calculate_trend(price_values)
    }


def calculate_trend(prices: List[float]) -> str:
    """
    Calculate simple trend direction.
    """
    if len(prices) < 2:
        return "stable"
    
    # Simple linear regression slope
    import numpy as np
    x = np.arange(len(prices))
    slope = np.polyfit(x, prices, 1)[0]
    
    if slope > 0.01:
        return "increasing"
    elif slope < -0.01:
        return "decreasing"
    else:
        return "stable"