"""API routes for hypothesis testing framework."""

import asyncio
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, AsyncGenerator
from enum import Enum
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

from .....core.models.hypothesis_testing import (
    HypothesisRegistry,
    HypothesisTest,
    HypothesisOutcome,
    TestResults,
    PolicyInterpretation
)
from .....application.services import HypothesisTestingService
from .....infrastructure.logging import Logger
from ..dependencies import get_hypothesis_testing_service
from ..schemas.hypothesis import (
    HypothesisTestRequest,
    HypothesisTestResponse,
    HypothesisResultsResponse,
    BatchHypothesisRequest,
    BatchHypothesisResponse,
    HypothesisListResponse
)

logger = get_logger(__name__)

router = APIRouter(prefix="/hypothesis", tags=["hypothesis-testing"])


class HypothesisID(str, Enum):
    """Available hypothesis test IDs."""
    H1 = "H1"   # Exchange Rate Mechanism
    H2 = "H2"   # Aid Distribution Channel
    H3 = "H3"   # Demand Destruction
    H4 = "H4"   # Currency Zone Switching
    H5 = "H5"   # Cross-Border Arbitrage
    H6 = "H6"   # Currency Substitution
    H7 = "H7"   # Aid Effectiveness
    H8 = "H8"   # Information Spillover
    H9 = "H9"   # Threshold Effects
    H10 = "H10" # Long-run Convergence
    S1 = "S1"   # Spatial Boundaries
    N1 = "N1"   # Network Density
    P1 = "P1"   # Political Economy


@router.get("/", response_model=HypothesisListResponse)
async def list_hypotheses():
    """
    List all available hypothesis tests.
    
    Returns information about each hypothesis including its ID, name,
    description, and required data types.
    """
    try:
        registry = HypothesisRegistry()
        hypotheses = []
        
        for hypothesis_id in registry.list_all():
            test = registry.get(hypothesis_id)
            if test:
                hypotheses.append({
                    "id": test.hypothesis_id,
                    "name": getattr(test, 'name', test.hypothesis_id),
                    "description": test.description,
                    "required_data": [req.value for req in test.required_data],
                    "category": _categorize_hypothesis(test.hypothesis_id)
                })
        
        return HypothesisListResponse(
            count=len(hypotheses),
            hypotheses=hypotheses
        )
        
    except Exception as e:
        logger.error(f"Error listing hypotheses: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list hypotheses: {e}"
        )


@router.get("/{hypothesis_id}/info", response_model=Dict[str, Any])
async def get_hypothesis_info(hypothesis_id: HypothesisID):
    """
    Get detailed information about a specific hypothesis test.
    
    Returns comprehensive information including methodology,
    expected outcomes, and interpretation guidelines.
    """
    try:
        registry = HypothesisRegistry()
        test = registry.get(hypothesis_id.value)
        
        if not test:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Hypothesis {hypothesis_id} not found"
            )
        
        return {
            "id": test.hypothesis_id,
            "name": getattr(test, 'name', test.hypothesis_id),
            "description": test.description,
            "required_data": [req.value for req in test.required_data],
            "category": _categorize_hypothesis(test.hypothesis_id),
            "methodology": _get_hypothesis_methodology(test.hypothesis_id),
            "expected_outcomes": _get_expected_outcomes(test.hypothesis_id),
            "policy_relevance": _get_policy_relevance(test.hypothesis_id)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting hypothesis info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get hypothesis info: {e}"
        )


@router.post("/{hypothesis_id}/test", response_model=HypothesisTestResponse, status_code=status.HTTP_202_ACCEPTED)
async def run_hypothesis_test(
    hypothesis_id: HypothesisID,
    request: HypothesisTestRequest,
    background_tasks: BackgroundTasks,
    hypothesis_service: HypothesisTestingService = Depends(get_hypothesis_testing_service)
):
    """
    Run a specific hypothesis test.
    
    This endpoint initiates the hypothesis test in the background and
    returns immediately with a test ID for tracking progress.
    """
    try:
        # Generate test ID
        test_id = str(uuid.uuid4())
        
        # Validate hypothesis exists
        registry = HypothesisRegistry()
        test = registry.get(hypothesis_id.value)
        
        if not test:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Hypothesis {hypothesis_id} not found"
            )
        
        # Queue background test
        background_tasks.add_task(
            run_hypothesis_test_task,
            test_id=test_id,
            hypothesis_id=hypothesis_id.value,
            request=request,
            hypothesis_service=hypothesis_service
        )
        
        return HypothesisTestResponse(
            id=test_id,
            hypothesis_id=hypothesis_id.value,
            status="pending",
            message=f"Hypothesis test {hypothesis_id} queued for processing",
            estimated_duration_seconds=_estimate_test_duration(hypothesis_id.value),
            created_at=datetime.utcnow()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating hypothesis test: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create hypothesis test: {e}"
        )


@router.post("/batch", response_model=BatchHypothesisResponse, status_code=status.HTTP_202_ACCEPTED)
async def run_batch_hypothesis_tests(
    request: BatchHypothesisRequest,
    background_tasks: BackgroundTasks,
    hypothesis_service: HypothesisTestingService = Depends(get_hypothesis_testing_service)
):
    """
    Run multiple hypothesis tests in batch.
    
    This endpoint allows running multiple hypothesis tests with the same
    data parameters. Tests can be run in parallel or sequentially based
    on the configuration.
    """
    try:
        # Generate batch ID
        batch_id = str(uuid.uuid4())
        test_ids = []
        
        # Validate all hypotheses exist
        registry = HypothesisRegistry()
        for hypothesis_id in request.hypothesis_ids:
            test = registry.get(hypothesis_id)
            if not test:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Hypothesis {hypothesis_id} not found"
                )
        
        # Create individual test requests
        for hypothesis_id in request.hypothesis_ids:
            test_id = str(uuid.uuid4())
            test_ids.append({
                "test_id": test_id,
                "hypothesis_id": hypothesis_id
            })
            
            if request.parallel:
                # Queue tests to run in parallel
                background_tasks.add_task(
                    run_hypothesis_test_task,
                    test_id=test_id,
                    hypothesis_id=hypothesis_id,
                    request=HypothesisTestRequest(
                        start_date=request.start_date,
                        end_date=request.end_date,
                        markets=request.markets,
                        commodities=request.commodities,
                        config=request.config
                    ),
                    hypothesis_service=hypothesis_service,
                    batch_id=batch_id
                )
        
        if not request.parallel:
            # Queue sequential batch processing
            background_tasks.add_task(
                run_batch_hypothesis_tests_sequential,
                batch_id=batch_id,
                test_ids=test_ids,
                request=request,
                hypothesis_service=hypothesis_service
            )
        
        total_duration = sum(_estimate_test_duration(hid) for hid in request.hypothesis_ids)
        if request.parallel:
            total_duration = max(_estimate_test_duration(hid) for hid in request.hypothesis_ids)
        
        return BatchHypothesisResponse(
            batch_id=batch_id,
            test_ids=test_ids,
            status="pending",
            message=f"Batch of {len(request.hypothesis_ids)} hypothesis tests queued",
            parallel=request.parallel,
            estimated_duration_seconds=total_duration,
            created_at=datetime.utcnow()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating batch hypothesis tests: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create batch hypothesis tests: {e}"
        )


@router.get("/test/{test_id}/status", response_model=Dict[str, Any])
async def get_test_status(
    test_id: str,
    hypothesis_service: HypothesisTestingService = Depends(get_hypothesis_testing_service)
):
    """
    Get the status of a hypothesis test.
    """
    try:
        status = await hypothesis_service.get_test_status(test_id)
        
        if not status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Test {test_id} not found"
            )
        
        return status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting test status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get test status: {e}"
        )


@router.get("/test/{test_id}/results", response_model=HypothesisResultsResponse)
async def get_test_results(
    test_id: str,
    hypothesis_service: HypothesisTestingService = Depends(get_hypothesis_testing_service)
):
    """
    Get the results of a completed hypothesis test.
    
    Returns comprehensive results including test statistics, outcome,
    and policy interpretation.
    """
    try:
        results = await hypothesis_service.get_test_results(test_id)
        
        if not results:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Results for test {test_id} not found"
            )
        
        # Check if test is completed
        status = await hypothesis_service.get_test_status(test_id)
        if status and status.get("status") != "completed":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Test {test_id} is not completed. Current status: {status.get('status')}"
            )
        
        return HypothesisResultsResponse(**results)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting test results: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get test results: {e}"
        )


@router.get("/test/{test_id}/stream")
async def stream_test_progress(
    test_id: str,
    hypothesis_service: HypothesisTestingService = Depends(get_hypothesis_testing_service)
):
    """
    Stream real-time progress updates for a hypothesis test using SSE.
    """
    # Verify test exists
    status = await hypothesis_service.get_test_status(test_id)
    if not status:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Test {test_id} not found"
        )
    
    async def event_generator() -> AsyncGenerator[str, None]:
        """Generate SSE events for test progress."""
        # Send initial status
        yield f"data: {{\n"
        yield f'  "event": "initial",\n'
        yield f'  "test_id": "{test_id}",\n'
        yield f'  "hypothesis_id": "{status.get("hypothesis_id")}",\n'
        yield f'  "status": "{status.get("status")}",\n'
        yield f'  "progress": {status.get("progress", 0)},\n'
        yield f'  "timestamp": "{datetime.utcnow().isoformat()}"\n'
        yield f"}}\n\n"
        
        # Poll for updates (simplified version)
        previous_status = status.get("status")
        previous_progress = status.get("progress", 0)
        
        while True:
            try:
                await asyncio.sleep(1)  # Poll every second
                
                # Get current status
                current_status = await hypothesis_service.get_test_status(test_id)
                if not current_status:
                    break
                
                # Check for changes
                if (current_status.get("status") != previous_status or 
                    current_status.get("progress", 0) != previous_progress):
                    
                    yield f"data: {{\n"
                    yield f'  "event": "update",\n'
                    yield f'  "test_id": "{test_id}",\n'
                    yield f'  "status": "{current_status.get("status")}",\n'
                    yield f'  "progress": {current_status.get("progress", 0)},\n'
                    
                    if current_status.get("message"):
                        yield f'  "message": "{current_status.get("message")}",\n'
                    
                    yield f'  "timestamp": "{datetime.utcnow().isoformat()}"\n'
                    yield f"}}\n\n"
                    
                    previous_status = current_status.get("status")
                    previous_progress = current_status.get("progress", 0)
                
                # Check if completed
                if current_status.get("status") in ["completed", "failed"]:
                    # Send final event
                    yield f"data: {{\n"
                    yield f'  "event": "complete",\n'
                    yield f'  "test_id": "{test_id}",\n'
                    yield f'  "status": "{current_status.get("status")}",\n'
                    
                    if current_status.get("status") == "completed":
                        yield f'  "outcome": "{current_status.get("outcome")}",\n'
                    elif current_status.get("error"):
                        yield f'  "error": "{current_status.get("error")}",\n'
                    
                    yield f'  "timestamp": "{datetime.utcnow().isoformat()}"\n'
                    yield f"}}\n\n"
                    break
                    
                # Send heartbeat
                yield f": heartbeat {datetime.utcnow().isoformat()}\n\n"
                
            except asyncio.CancelledError:
                logger.info(f"SSE connection closed for test {test_id}")
                break
            except Exception as e:
                logger.error(f"Error in SSE stream: {e}")
                break
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
        }
    )


# Helper functions

def _categorize_hypothesis(hypothesis_id: str) -> str:
    """Categorize hypothesis by type."""
    if hypothesis_id.startswith("H"):
        if hypothesis_id in ["H1", "H2", "H3", "H4", "H5"]:
            return "core"
        else:
            return "advanced"
    elif hypothesis_id == "S1":
        return "spatial"
    elif hypothesis_id == "N1":
        return "network"
    elif hypothesis_id == "P1":
        return "political"
    return "other"


def _estimate_test_duration(hypothesis_id: str) -> int:
    """Estimate test duration in seconds."""
    durations = {
        "H1": 60,    # Exchange rate - quick
        "H2": 120,   # Aid distribution - moderate
        "H3": 90,    # Demand destruction - moderate
        "H4": 90,    # Zone switching - moderate
        "H5": 60,    # Arbitrage - quick
        "H6": 120,   # Currency substitution - moderate
        "H7": 150,   # Aid effectiveness - complex
        "H8": 120,   # Information spillover - moderate
        "H9": 180,   # Threshold effects - complex
        "H10": 180,  # Convergence - complex
        "S1": 120,   # Spatial - moderate
        "N1": 150,   # Network - complex
        "P1": 90     # Political economy - moderate
    }
    return durations.get(hypothesis_id, 120)


def _get_hypothesis_methodology(hypothesis_id: str) -> str:
    """Get methodology description for hypothesis."""
    methodologies = {
        "H1": "Panel regression comparing YER vs USD prices across currency zones",
        "H2": "Difference-in-differences with aid distribution timing",
        "H3": "Instrumental variables using displacement intensity",
        "H4": "Regression discontinuity around control change events",
        "H5": "Cross-border price differential decomposition",
        "H6": "Binary choice model for currency denomination",
        "H7": "Triple-difference (aid × currency_match × post)",
        "H8": "Vector autoregression with cross-zone spillovers",
        "H9": "Threshold VECM with exchange rate differentials",
        "H10": "Separate ECM models by currency denomination",
        "S1": "Spatial regression with currency zone interactions",
        "N1": "Network-augmented price transmission models",
        "P1": "Political economy regression with fiscal variables"
    }
    return methodologies.get(hypothesis_id, "Advanced econometric methodology")


def _get_expected_outcomes(hypothesis_id: str) -> Dict[str, Any]:
    """Get expected outcomes for hypothesis."""
    outcomes = {
        "H1": {
            "primary": "Yemen Paradox disappears in USD prices",
            "effect_size": "274% overvaluation in Houthi areas",
            "confidence": "95%"
        },
        "H2": {
            "cash_effect": "-8% price reduction",
            "inkind_effect": "-15% price reduction",
            "spillover_radius": "50km"
        },
        "H3": {
            "demand_effect": "2-3x larger than supply effect",
            "persistence": "6+ months post-conflict",
            "commodity_variation": "Higher for non-essentials"
        },
        # Add more as needed
    }
    return outcomes.get(hypothesis_id, {})


def _get_policy_relevance(hypothesis_id: str) -> str:
    """Get policy relevance description."""
    relevance = {
        "H1": "Enables 25-40% improvement in aid effectiveness through proper exchange rate adjustment",
        "H2": "Guides modality choice (cash vs in-kind) to minimize market disruption",
        "H3": "Identifies need for demand-side interventions beyond supply support",
        "H4": "Predicts price impacts of territorial control changes",
        "H5": "Identifies arbitrage opportunities and market inefficiencies",
        "H6": "Guides currency choice for aid programming",
        "H7": "Optimizes aid effectiveness through currency matching",
        "H8": "Enables cross-zone market monitoring",
        "H9": "Identifies critical thresholds for market breakdown",
        "H10": "Assesses long-term market recovery potential",
        "S1": "Optimizes market selection for interventions",
        "N1": "Leverages trader networks for market strengthening",
        "P1": "Addresses political barriers to currency reunification"
    }
    return relevance.get(hypothesis_id, "Significant policy implications for humanitarian programming")


async def run_hypothesis_test_task(
    test_id: str,
    hypothesis_id: str,
    request: HypothesisTestRequest,
    hypothesis_service: HypothesisTestingService,
    batch_id: Optional[str] = None
):
    """Background task to run a hypothesis test."""
    try:
        logger.info(f"Starting hypothesis test {test_id} for {hypothesis_id}")
        
        # Update status to running
        await hypothesis_service.update_test_status(
            test_id, 
            status="running", 
            hypothesis_id=hypothesis_id,
            batch_id=batch_id
        )
        
        # Run the test
        results = await hypothesis_service.run_hypothesis_test(
            hypothesis_id=hypothesis_id,
            start_date=request.start_date,
            end_date=request.end_date,
            markets=request.markets,
            commodities=request.commodities,
            config=request.config
        )
        
        # Save results
        await hypothesis_service.save_test_results(test_id, results)
        
        # Update status to completed
        await hypothesis_service.update_test_status(
            test_id,
            status="completed",
            outcome=results.outcome.value,
            progress=100
        )
        
        logger.info(f"Hypothesis test {test_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Error in hypothesis test {test_id}: {e}")
        await hypothesis_service.update_test_status(
            test_id,
            status="failed",
            error=str(e)
        )


async def run_batch_hypothesis_tests_sequential(
    batch_id: str,
    test_ids: List[Dict[str, str]],
    request: BatchHypothesisRequest,
    hypothesis_service: HypothesisTestingService
):
    """Run hypothesis tests sequentially in a batch."""
    try:
        for test_info in test_ids:
            await run_hypothesis_test_task(
                test_id=test_info["test_id"],
                hypothesis_id=test_info["hypothesis_id"],
                request=HypothesisTestRequest(
                    start_date=request.start_date,
                    end_date=request.end_date,
                    markets=request.markets,
                    commodities=request.commodities,
                    config=request.config
                ),
                hypothesis_service=hypothesis_service,
                batch_id=batch_id
            )
    except Exception as e:
        logger.error(f"Error in batch hypothesis tests {batch_id}: {e}")