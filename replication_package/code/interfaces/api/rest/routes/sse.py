"""
Enhanced SSE routes for real-time analysis updates.
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.responses import StreamingResponse

from .....shared.container import Container
from .....application.services import AnalysisOrchestrator
from .....infrastructure.logging import Logger
from ..dependencies import require_auth
from ..sse import SSEConnectionManager, SSEEventHandler

logger = get_logger(__name__)

router = APIRouter(prefix="/sse", tags=["sse", "real-time"])

# Global SSE manager and handler (initialized in app startup)
sse_manager: Optional[SSEConnectionManager] = None
sse_handler: Optional[SSEEventHandler] = None


def get_sse_manager() -> SSEConnectionManager:
    """Get SSE connection manager dependency."""
    global sse_manager
    if not sse_manager:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="SSE service not available"
        )
    return sse_manager


def get_sse_handler() -> SSEEventHandler:
    """Get SSE event handler dependency."""
    global sse_handler
    if not sse_handler:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="SSE service not available"
        )
    return sse_handler


# Temporarily disable SSE startup until dependencies are fixed
# @router.on_event("startup")
# async def startup_event():
#     """Initialize SSE services on startup."""
#     global sse_manager, sse_handler
#     
#     # Create SSE manager
#     sse_manager = SSEConnectionManager(
#         heartbeat_interval=30.0,
#         connection_timeout=300.0,
#         max_queue_size=1000
#     )
#     await sse_manager.start()
#     
#     # Create SSE handler
#     # event_bus = await Container.event_bus()  # TODO: Fix this dependency
#     # sse_handler = SSEEventHandler(event_bus, sse_manager)
#     # await sse_handler.start()
#     
#     logger.info("SSE services initialized")


# Temporarily disable SSE shutdown until dependencies are fixed
# @router.on_event("shutdown")
# async def shutdown_event():
#     """Clean up SSE services on shutdown."""
#     global sse_manager, sse_handler
#     
#     if sse_handler:
#         await sse_handler.stop()
#         
#     if sse_manager:
#         await sse_manager.stop()
#         
#     logger.info("SSE services shut down")


@router.get("/analysis/{analysis_id}/status")
async def stream_analysis_status(
    analysis_id: str,
    request: Request,
    current_user: dict = Depends(require_auth),
    # orchestrator: AnalysisOrchestrator = Depends(Container.analysis_orchestrator),  # TODO: Fix dependency
    handler: SSEEventHandler = Depends(get_sse_handler)
):
    """
    Stream real-time status updates for an analysis using Server-Sent Events (SSE).
    
    This endpoint provides:
    - Real-time progress updates
    - Status change notifications
    - Error notifications
    - Completion events
    - Automatic heartbeats to keep connection alive
    
    The connection will automatically close when the analysis completes or fails.
    
    Example client code:
    ```javascript
    const eventSource = new EventSource('/api/v1/sse/analysis/123/status', {
        headers: { 'Authorization': 'Bearer YOUR_TOKEN' }
    });
    
    eventSource.addEventListener('progress', (e) => {
        const data = JSON.parse(e.data);
        console.log(`Progress: ${data.progress}%`);
    });
    
    eventSource.addEventListener('completed', (e) => {
        const data = JSON.parse(e.data);
        console.log('Analysis completed:', data.results_summary);
        eventSource.close();
    });
    ```
    """
    # Verify the analysis exists
    status_data = await orchestrator.get_analysis_status(analysis_id)
    if not status_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Analysis with ID {analysis_id} not found."
        )
    
    # Log connection
    logger.info(f"SSE connection request for analysis {analysis_id} from user {current_user.get('id')}")
    
    # Create SSE response
    return StreamingResponse(
        handler.create_sse_stream(analysis_id, initial_status=status_data),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # Disable nginx buffering
            "Access-Control-Allow-Origin": "*",  # Configure appropriately for production
        }
    )


@router.get("/analysis/{analysis_id}/stream")
async def stream_analysis_events(
    analysis_id: str,
    request: Request,
    event_types: Optional[str] = None,
    # current_user: dict = Depends(require_auth),  # Temporarily disabled for testing
    # orchestrator: AnalysisOrchestrator = Depends(Container.analysis_orchestrator),  # Fix dependency
    # handler: SSEEventHandler = Depends(get_sse_handler)  # Fix dependency
):
    """
    Stream specific event types for an analysis.
    
    Query parameters:
    - event_types: Comma-separated list of event types to filter
                  (e.g., "progress,status,tier_started")
    
    This endpoint allows filtering specific event types for more
    focused real-time updates.
    """
    # Verify the analysis exists
    status_data = await orchestrator.get_analysis_status(analysis_id)
    if not status_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Analysis with ID {analysis_id} not found."
        )
    
    # Parse event types filter
    filter_types = set()
    if event_types:
        filter_types = set(event_types.split(','))
    
    # Create filtered stream
    async def filtered_stream():
        """Generate filtered SSE stream."""
        async for event in handler.create_sse_stream(analysis_id, initial_status=status_data):
            # Check if we should filter
            if filter_types and event:
                # Extract event type from the formatted event
                # This is a simplified check - in production, parse properly
                should_send = any(f'event: {ft}' in event for ft in filter_types)
                if should_send:
                    yield event
            else:
                yield event
    
    return StreamingResponse(
        filtered_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
        }
    )


@router.get("/metrics")
async def get_sse_metrics(
    current_user: dict = Depends(require_auth),
    manager: SSEConnectionManager = Depends(get_sse_manager),
    handler: SSEEventHandler = Depends(get_sse_handler)
):
    """
    Get SSE service metrics.
    
    Returns information about:
    - Active connections
    - Queue sizes
    - Event processing statistics
    """
    return {
        "manager_metrics": manager.get_metrics(),
        "handler_metrics": handler.get_metrics(),
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/health")
async def sse_health_check(
    manager: SSEConnectionManager = Depends(get_sse_manager),
    handler: SSEEventHandler = Depends(get_sse_handler)
):
    """
    Check SSE service health.
    
    Returns service status and basic metrics.
    """
    metrics = manager.get_metrics()
    
    return {
        "status": "healthy",
        "active_connections": metrics["total_connections"],
        "monitored_analyses": metrics["analyses_monitored"],
        "timestamp": datetime.utcnow().isoformat()
    }