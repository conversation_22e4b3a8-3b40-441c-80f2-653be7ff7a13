"""Price API schemas."""

from datetime import datetime
from typing import List

from pydantic import BaseModel, Field


class PriceResponse(BaseModel):
    """Response schema for price observation."""
    
    market_id: str = Field(..., description="Market identifier")
    commodity_code: str = Field(..., description="Commodity code")
    commodity_name: str = Field(..., description="Commodity name")
    price_amount: float = Field(..., description="Price amount")
    price_currency: str = Field(..., description="Currency code")
    price_unit: str = Field(..., description="Measurement unit")
    observed_date: datetime = Field(..., description="Observation date")
    source: str = Field(..., description="Data source")
    quality: str = Field(..., description="Data quality indicator")
    observations_count: int = Field(..., description="Number of observations")
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "market_id": "SANAA_CENTRAL",
                "commodity_code": "WHEAT_FLOUR",
                "commodity_name": "Wheat Flour",
                "price_amount": 450.5,
                "price_currency": "YER",
                "price_unit": "kg",
                "observed_date": "2023-12-01T00:00:00",
                "source": "WFP",
                "quality": "standard",
                "observations_count": 5
            }
        }


class PriceListResponse(BaseModel):
    """Response schema for price list."""
    
    prices: List[PriceResponse] = Field(..., description="List of price observations")
    total: int = Field(..., description="Total number of prices matching filters")
    skip: int = Field(..., description="Number of prices skipped")
    limit: int = Field(..., description="Maximum prices returned")


class PriceStatistics(BaseModel):
    """Price statistics for a market-commodity pair."""
    
    market_id: str = Field(..., description="Market identifier")
    commodity_code: str = Field(..., description="Commodity code")
    start_date: datetime = Field(..., description="Statistics period start")
    end_date: datetime = Field(..., description="Statistics period end")
    count: int = Field(..., description="Number of observations")
    mean: float = Field(..., description="Mean price")
    median: float = Field(..., description="Median price")
    std_dev: float = Field(..., description="Standard deviation")
    min_price: float = Field(..., description="Minimum price")
    max_price: float = Field(..., description="Maximum price")
    currency: str = Field(..., description="Currency code")
    unit: str = Field(..., description="Measurement unit")
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "market_id": "SANAA_CENTRAL",
                "commodity_code": "WHEAT_FLOUR",
                "start_date": "2023-01-01T00:00:00",
                "end_date": "2023-12-31T23:59:59",
                "count": 52,
                "mean": 425.3,
                "median": 420.0,
                "std_dev": 35.7,
                "min_price": 350.0,
                "max_price": 520.0,
                "currency": "YER",
                "unit": "kg"
            }
        }