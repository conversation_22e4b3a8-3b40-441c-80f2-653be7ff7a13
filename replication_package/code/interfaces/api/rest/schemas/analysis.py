"""Analysis API schemas."""

from datetime import datetime, date
from typing import Any, Dict, List, Optional
from enum import Enum

from pydantic import BaseModel, Field, validator, root_validator


class AnalysisStatus(str, Enum):
    """Analysis status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TierType(str, Enum):
    """Analysis tier enumeration."""
    TIER1 = "tier1"
    TIER2 = "tier2"
    TIER3 = "tier3"
    ALL = "all"


class ThreeTierAnalysisRequest(BaseModel):
    """Request model for three-tier analysis."""
    
    name: str = Field(..., description="Analysis name for identification")
    description: Optional[str] = Field(None, description="Analysis description")
    
    # Temporal parameters
    start_date: date = Field(..., description="Analysis start date")
    end_date: date = Field(..., description="Analysis end date")
    
    # Spatial parameters
    markets: Optional[List[str]] = Field(
        None, 
        description="Specific markets to include (all if not specified)"
    )
    governorates: Optional[List[str]] = Field(
        None,
        description="Filter by governorates"
    )
    
    # Commodity parameters
    commodities: Optional[List[str]] = Field(
        None,
        description="Specific commodities to analyze (all if not specified)"
    )
    commodity_groups: Optional[List[str]] = Field(
        None,
        description="Analyze commodity groups (FOOD, FUEL, etc.)"
    )
    
    # Model parameters
    confidence_level: float = Field(
        0.95,
        ge=0.8,
        le=0.99,
        description="Confidence level for intervals"
    )
    include_diagnostics: bool = Field(
        True,
        description="Include diagnostic tests in results"
    )
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('end_date must be after start_date')
        return v
    
    @root_validator(skip_on_failure=True)
    def validate_request_integrity(cls, values):
        """Validate overall request integrity."""
        # Ensure at least one filter is provided if using specific analysis
        markets = values.get('markets')
        commodities = values.get('commodities')
        governorates = values.get('governorates')
        
        # If all filters are None, analysis will use all available data
        # This is valid, so no additional validation needed here
        
        return values


class TierAnalysisRequest(BaseModel):
    """Request model for individual tier analysis."""
    
    name: str = Field(..., description="Analysis name for identification")
    description: Optional[str] = Field(None, description="Analysis description")
    
    # Temporal parameters
    start_date: date = Field(..., description="Analysis start date")
    end_date: date = Field(..., description="Analysis end date")
    
    # Spatial parameters
    markets: Optional[List[str]] = Field(
        None, 
        description="Specific markets to include (all if not specified)"
    )
    
    # Commodity parameters
    commodities: Optional[List[str]] = Field(
        None,
        description="Specific commodities to analyze (all if not specified)"
    )
    
    # Model parameters
    confidence_level: float = Field(
        0.95,
        ge=0.8,
        le=0.99,
        description="Confidence level for intervals"
    )
    
    include_diagnostics: bool = Field(
        True,
        description="Include diagnostic tests in results"
    )
    
    # Tier-specific configuration
    tier_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Tier-specific configuration parameters"
    )
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('end_date must be after start_date')
        return v


class AnalysisRequest(BaseModel):
    """Request schema for starting an analysis."""
    
    start_date: datetime = Field(..., description="Analysis start date")
    end_date: datetime = Field(..., description="Analysis end date")
    market_ids: List[str] = Field(..., min_items=1, description="List of market IDs to analyze")
    commodity_ids: List[str] = Field(..., min_items=1, description="List of commodity codes to analyze")
    config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Analysis configuration")
    
    @validator("end_date")
    def end_after_start(cls, v, values):
        """Validate end date is after start date."""
        if "start_date" in values and v <= values["start_date"]:
            raise ValueError("end_date must be after start_date")
        return v
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "start_date": "2023-01-01T00:00:00",
                "end_date": "2023-12-31T23:59:59",
                "market_ids": ["SANAA_CENTRAL", "ADEN_MAIN", "TAIZ_WHOLESALE"],
                "commodity_ids": ["WHEAT_FLOUR", "RICE_IMPORTED", "SUGAR"],
                "config": {
                    "run_pooled_panel": True,
                    "run_vecm": True,
                    "distance_threshold": 500,
                    "correlation_threshold": 0.7
                }
            }
        }


class AnalysisResponse(BaseModel):
    """Response schema for analysis creation."""
    
    id: str = Field(..., description="Unique analysis identifier")
    status: AnalysisStatus = Field(..., description="Analysis status")
    message: str = Field(..., description="Status message")
    estimated_duration_seconds: int = Field(..., description="Estimated duration in seconds")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "id": "550e8400-e29b-41d4-a716-446655440000",
                "status": "pending",
                "message": "Analysis queued for processing",
                "estimated_duration_seconds": 300,
                "created_at": "2023-01-01T00:00:00"
            }
        }


class AnalysisStatusResponse(BaseModel):
    """Response schema for analysis status."""
    
    analysis_id: str = Field(..., description="Unique analysis identifier")
    status: str = Field(..., enum=["pending", "running", "completed", "failed", "cancelled"])
    progress: int = Field(..., ge=0, le=100, description="Progress percentage")
    current_phase: Optional[str] = Field(None, description="Current analysis phase")
    started_at: datetime = Field(..., description="Analysis start time")
    completed_at: Optional[datetime] = Field(None, description="Analysis completion time")
    updated_at: datetime = Field(..., description="Last update time")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
                "status": "running",
                "progress": 75,
                "current_phase": "econometric_modeling",
                "started_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:03:45"
            }
        }


# DTOs for results
class Tier1ResultsDTO(BaseModel):
    """Tier 1 pooled panel results."""
    coefficients: Dict[str, float]
    standard_errors: Dict[str, float]
    t_statistics: Dict[str, float]
    p_values: Dict[str, float]
    r_squared: float
    n_observations: int
    diagnostics: Optional[Dict[str, Any]] = None


class Tier2ResultsDTO(BaseModel):
    """Tier 2 commodity-specific results."""
    commodity: str
    cointegration_rank: int
    vecm_coefficients: Dict[str, Any]
    adjustment_speeds: Dict[str, float]
    half_life_days: Optional[float] = None
    diagnostics: Optional[Dict[str, Any]] = None


class Tier3ResultsDTO(BaseModel):
    """Tier 3 validation results."""
    n_factors: int
    explained_variance: float
    factor_loadings: Dict[str, List[float]]
    conflict_impact: Dict[str, float]
    validation_metrics: Dict[str, float]


class AnalysisSummaryDTO(BaseModel):
    """Analysis summary statistics."""
    total_markets: int
    total_commodities: int
    date_range: Dict[str, str]
    integrated_pairs: int
    average_half_life: float
    key_findings: List[str]


class TierResultsResponse(BaseModel):
    """Response for individual tier results."""
    
    id: str
    tier: TierType
    status: AnalysisStatus
    results: Dict[str, Any]
    diagnostics: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any]
    started_at: datetime
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "id": "550e8400-e29b-41d4-a716-446655440000",
                "tier": "tier1",
                "status": "completed",
                "results": {
                    "coefficients": {"conflict_intensity": 0.35},
                    "p_values": {"conflict_intensity": 0.001},
                    "r_squared": 0.75
                },
                "diagnostics": {
                    "hausman_test": {"statistic": 12.5, "p_value": 0.01}
                },
                "metadata": {
                    "n_markets": 15,
                    "n_commodities": 8,
                    "date_range": "2019-2024"
                },
                "started_at": "2023-01-01T00:00:00",
                "completed_at": "2023-01-01T00:05:00",
                "duration_seconds": 300.0
            }
        }


class AnalysisResultsResponse(BaseModel):
    """Complete analysis results."""
    
    id: str
    status: AnalysisStatus
    
    # Tier 1 Results
    tier1: Optional[Tier1ResultsDTO] = None
    
    # Tier 2 Results
    tier2: Optional[Dict[str, Tier2ResultsDTO]] = None  # By commodity
    
    # Tier 3 Results
    tier3: Optional[Tier3ResultsDTO] = None
    
    # Summary
    summary: AnalysisSummaryDTO
    
    # Quality metrics
    confidence_scores: Optional[Dict[str, float]] = None
    
    # Metadata
    config: Dict
    started_at: datetime
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "id": "550e8400-e29b-41d4-a716-446655440000",
                "status": "completed",
                "tier1": {
                    "coefficients": {"conflict_intensity": 0.35},
                    "r_squared": 0.75,
                    "n_observations": 12000
                },
                "summary": {
                    "total_markets": 15,
                    "total_commodities": 8,
                    "key_findings": ["Conflict increases prices by 35%"]
                },
                "confidence_scores": {
                    "tier1": 0.95,
                    "overall": 0.85
                },
                "started_at": "2023-01-01T00:00:00",
                "completed_at": "2023-01-01T00:15:00",
                "duration_seconds": 900.0
            }
        }