"""Commodity API schemas."""

from datetime import date
from typing import List, Dict, Optional
from pydantic import BaseModel, Field


class CommodityResponse(BaseModel):
    """Commodity response model."""
    id: str = Field(..., description="Unique commodity identifier")
    code: str = Field(..., description="Commodity code")
    name: str = Field(..., description="Commodity name")
    category: str = Field(..., description="Commodity category (FOOD, FUEL, NON_FOOD)")
    unit: str = Field(..., description="Measurement unit")
    description: Optional[str] = Field(None, description="Commodity description")
    
    @classmethod
    def from_entity(cls, entity):
        """Create response from domain entity."""
        return cls(
            id=entity.id,
            code=entity.code,
            name=entity.name,
            category=entity.category,
            unit=entity.unit,
            description=getattr(entity, 'description', None)
        )
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "id": "wheat_flour_001",
                "code": "WHEAT_FLOUR",
                "name": "Wheat Flour",
                "category": "FOOD",
                "unit": "kg",
                "description": "White wheat flour, imported"
            }
        }


class CommodityListResponse(BaseModel):
    """Commodity list response."""
    data: List[CommodityResponse] = Field(..., description="List of commodities")
    total: int = Field(..., description="Total number of commodities")
    skip: int = Field(..., description="Number of records skipped")
    limit: int = Field(..., description="Maximum records returned")
    has_more: bool = Field(..., description="Whether more records exist")
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "data": [
                    {
                        "id": "wheat_flour_001",
                        "code": "WHEAT_FLOUR",
                        "name": "Wheat Flour",
                        "category": "FOOD",
                        "unit": "kg",
                        "description": "White wheat flour, imported"
                    }
                ],
                "total": 25,
                "skip": 0,
                "limit": 100,
                "has_more": False
            }
        }


class PriceStatistics(BaseModel):
    """Price statistics."""
    count: int = Field(..., description="Number of price observations")
    mean: Optional[float] = Field(None, description="Average price")
    std: Optional[float] = Field(None, description="Standard deviation")
    min: Optional[float] = Field(None, description="Minimum price")
    max: Optional[float] = Field(None, description="Maximum price")
    cv: Optional[float] = Field(None, description="Coefficient of variation")
    trend: Optional[str] = Field(None, description="Price trend (increasing/decreasing/stable)")
    markets_count: int = Field(..., description="Number of markets with prices")


class CommodityPricesResponse(BaseModel):
    """Commodity prices response."""
    commodity_id: str = Field(..., description="Commodity identifier")
    commodity_name: str = Field(..., description="Commodity name")
    currency: str = Field(..., description="Currency of prices")
    aggregation: str = Field(..., description="Aggregation level")
    market_prices: Dict[str, List[Dict]] = Field(..., description="Prices by market")
    statistics: PriceStatistics = Field(..., description="Price statistics")
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "commodity_id": "wheat_flour_001",
                "commodity_name": "Wheat Flour",
                "currency": "USD",
                "aggregation": "weekly",
                "market_prices": {
                    "SANAA_CENTRAL": [
                        {
                            "date": "2023-01-07",
                            "price": 0.75
                        }
                    ]
                },
                "statistics": {
                    "count": 52,
                    "mean": 0.78,
                    "std": 0.05,
                    "min": 0.65,
                    "max": 0.95,
                    "cv": 0.064,
                    "trend": "increasing",
                    "markets_count": 15
                }
            }
        }