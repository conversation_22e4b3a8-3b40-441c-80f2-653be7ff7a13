<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yemen Market Integration - Real-time Analysis Monitor</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ccc;
        }
        
        .status-indicator.connected { background: #28a745; }
        .status-indicator.pending { background: #ffc107; }
        .status-indicator.running { background: #007bff; }
        .status-indicator.completed { background: #28a745; }
        .status-indicator.failed { background: #dc3545; }
        
        .progress-section {
            margin-bottom: 30px;
        }
        
        .overall-progress {
            margin-bottom: 20px;
        }
        
        .progress-bar {
            width: 100%;
            height: 30px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .tier-progress {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        
        .tier-card {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
        }
        
        .tier-card h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            color: #495057;
        }
        
        .tier-status {
            font-size: 14px;
            color: #6c757d;
        }
        
        .log-section {
            margin-top: 30px;
        }
        
        .log-container {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            background: #f8f9fa;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 2px;
        }
        
        .log-entry.info { background: #cfe2ff; }
        .log-entry.success { background: #d1e7dd; }
        .log-entry.warning { background: #fff3cd; }
        .log-entry.error { background: #f8d7da; }
        
        .controls {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }
        
        button.primary {
            background: #007bff;
            color: white;
        }
        
        button.primary:hover {
            background: #0056b3;
        }
        
        button.secondary {
            background: #6c757d;
            color: white;
        }
        
        button.secondary:hover {
            background: #545b62;
        }
        
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .config-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .config-section input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }
        
        .commodity-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .commodity-item {
            padding: 8px;
            background: #e9ecef;
            border-radius: 4px;
            font-size: 14px;
            text-align: center;
        }
        
        .commodity-item.processing {
            background: #cfe2ff;
            animation: pulse 1s infinite;
        }
        
        .commodity-item.completed {
            background: #d1e7dd;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Yemen Market Integration - Real-time Analysis Monitor</h1>
        
        <div class="config-section">
            <h3>Configuration</h3>
            <input type="text" id="apiUrl" placeholder="API URL (e.g., https://api.yemen-market.example.com)" value="http://localhost:8000">
            <input type="text" id="authToken" placeholder="Authentication Token">
            <input type="text" id="analysisId" placeholder="Analysis ID">
        </div>
        
        <div class="status-bar">
            <div class="status-item">
                <span class="status-indicator" id="connectionStatus"></span>
                <span>Connection: <span id="connectionText">Disconnected</span></span>
            </div>
            <div class="status-item">
                <span class="status-indicator" id="analysisStatus"></span>
                <span>Analysis: <span id="analysisText">Not Started</span></span>
            </div>
            <div class="status-item">
                <span>Started: <span id="startTime">-</span></span>
            </div>
            <div class="status-item">
                <span>Duration: <span id="duration">-</span></span>
            </div>
        </div>
        
        <div class="progress-section">
            <div class="overall-progress">
                <h3>Overall Progress</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="overallProgress" style="width: 0%">0%</div>
                </div>
            </div>
            
            <div class="tier-progress">
                <div class="tier-card">
                    <h3>Tier 1: Pooled Panel</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" id="tier1Progress" style="width: 0%">0%</div>
                    </div>
                    <div class="tier-status" id="tier1Status">Waiting...</div>
                </div>
                
                <div class="tier-card">
                    <h3>Tier 2: Commodity VECM</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" id="tier2Progress" style="width: 0%">0%</div>
                    </div>
                    <div class="tier-status" id="tier2Status">Waiting...</div>
                </div>
                
                <div class="tier-card">
                    <h3>Tier 3: Validation</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" id="tier3Progress" style="width: 0%">0%</div>
                    </div>
                    <div class="tier-status" id="tier3Status">Waiting...</div>
                </div>
            </div>
            
            <div class="commodity-section" style="margin-top: 20px; display: none;">
                <h3>Commodity Processing</h3>
                <div class="commodity-grid" id="commodityGrid"></div>
            </div>
        </div>
        
        <div class="log-section">
            <h3>Activity Log</h3>
            <div class="log-container" id="logContainer"></div>
        </div>
        
        <div class="controls">
            <button class="primary" id="connectBtn" onclick="toggleConnection()">Connect</button>
            <button class="secondary" onclick="clearLog()">Clear Log</button>
            <button class="secondary" id="downloadBtn" onclick="downloadResults()" disabled>Download Results</button>
        </div>
    </div>

    <script>
        let eventSource = null;
        let startTime = null;
        let durationInterval = null;
        let analysisComplete = false;
        let commodities = new Map();

        function toggleConnection() {
            if (eventSource) {
                disconnect();
            } else {
                connect();
            }
        }

        function connect() {
            const apiUrl = document.getElementById('apiUrl').value;
            const authToken = document.getElementById('authToken').value;
            const analysisId = document.getElementById('analysisId').value;

            if (!apiUrl || !authToken || !analysisId) {
                alert('Please fill in all configuration fields');
                return;
            }

            const url = `${apiUrl}/api/v1/sse/analysis/${analysisId}/status`;
            
            // Note: EventSource doesn't support headers, so we append token as query param
            // In production, use a proper auth mechanism
            eventSource = new EventSource(`${url}?token=${authToken}`);

            eventSource.onopen = () => {
                updateConnectionStatus(true);
                addLog('Connected to SSE stream', 'success');
                document.getElementById('connectBtn').textContent = 'Disconnect';
            };

            eventSource.onerror = (error) => {
                addLog('Connection error', 'error');
                if (eventSource.readyState === EventSource.CLOSED) {
                    updateConnectionStatus(false);
                    document.getElementById('connectBtn').textContent = 'Connect';
                }
            };

            // Set up event listeners
            setupEventListeners();
        }

        function disconnect() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            updateConnectionStatus(false);
            document.getElementById('connectBtn').textContent = 'Connect';
            addLog('Disconnected from SSE stream', 'info');
        }

        function setupEventListeners() {
            eventSource.addEventListener('initial', (e) => {
                const data = JSON.parse(e.data);
                addLog(`Initial status: ${data.status}, Progress: ${data.progress}%`, 'info');
                updateAnalysisStatus(data.status);
                updateProgress(data.progress);
                startTime = new Date();
                startDurationTimer();
            });

            eventSource.addEventListener('progress', (e) => {
                const data = JSON.parse(e.data);
                updateProgress(data.progress, data.tier);
                if (data.message) {
                    addLog(`Progress: ${data.message}`, 'info');
                }
            });

            eventSource.addEventListener('status', (e) => {
                const data = JSON.parse(e.data);
                updateAnalysisStatus(data.status);
                addLog(`Status changed: ${data.old_status} → ${data.status}`, 'info');
            });

            eventSource.addEventListener('tier_started', (e) => {
                const data = JSON.parse(e.data);
                updateTierStatus(data.tier, 'Running');
                addLog(`${data.tier} started`, 'info');
            });

            eventSource.addEventListener('tier_completed', (e) => {
                const data = JSON.parse(e.data);
                updateTierStatus(data.tier, `Completed (${data.duration_seconds.toFixed(1)}s)`);
                updateTierProgress(data.tier, 100);
                addLog(`${data.tier} completed in ${data.duration_seconds.toFixed(1)}s`, 'success');
            });

            eventSource.addEventListener('commodity_update', (e) => {
                const data = JSON.parse(e.data);
                updateCommodityStatus(data.commodity, data.action);
                if (data.progress !== undefined) {
                    addLog(`${data.commodity}: ${data.action} (${data.progress}%)`, 'info');
                }
            });

            eventSource.addEventListener('completed', (e) => {
                const data = JSON.parse(e.data);
                analysisComplete = true;
                updateAnalysisStatus('completed');
                updateProgress(100);
                stopDurationTimer();
                document.getElementById('downloadBtn').disabled = false;
                addLog(`Analysis completed in ${data.duration_seconds.toFixed(1)}s`, 'success');
                
                // Show results summary
                if (data.results_summary) {
                    addLog(`Results: ${JSON.stringify(data.results_summary, null, 2)}`, 'success');
                }
            });

            eventSource.addEventListener('failed', (e) => {
                const data = JSON.parse(e.data);
                analysisComplete = true;
                updateAnalysisStatus('failed');
                stopDurationTimer();
                addLog(`Analysis failed: ${data.error}`, 'error');
                if (data.tier) {
                    addLog(`Failed at ${data.tier}`, 'error');
                }
            });

            eventSource.addEventListener('heartbeat', (e) => {
                // Heartbeat received - connection is alive
                console.log('Heartbeat received');
            });
        }

        function updateConnectionStatus(connected) {
            const indicator = document.getElementById('connectionStatus');
            const text = document.getElementById('connectionText');
            
            if (connected) {
                indicator.className = 'status-indicator connected';
                text.textContent = 'Connected';
            } else {
                indicator.className = 'status-indicator';
                text.textContent = 'Disconnected';
            }
        }

        function updateAnalysisStatus(status) {
            const indicator = document.getElementById('analysisStatus');
            const text = document.getElementById('analysisText');
            
            indicator.className = `status-indicator ${status}`;
            text.textContent = status.charAt(0).toUpperCase() + status.slice(1);
        }

        function updateProgress(progress, tier = null) {
            if (!tier) {
                // Overall progress
                const progressBar = document.getElementById('overallProgress');
                progressBar.style.width = `${progress}%`;
                progressBar.textContent = `${progress}%`;
            } else {
                // Tier-specific progress
                updateTierProgress(tier, progress);
            }
        }

        function updateTierProgress(tier, progress) {
            const tierNum = tier.replace('tier', '');
            const progressBar = document.getElementById(`tier${tierNum}Progress`);
            if (progressBar) {
                progressBar.style.width = `${progress}%`;
                progressBar.textContent = `${progress}%`;
            }
        }

        function updateTierStatus(tier, status) {
            const tierNum = tier.replace('tier', '');
            const statusEl = document.getElementById(`tier${tierNum}Status`);
            if (statusEl) {
                statusEl.textContent = status;
            }
        }

        function updateCommodityStatus(commodity, action) {
            // Show commodity section if hidden
            const section = document.querySelector('.commodity-section');
            section.style.display = 'block';
            
            // Update commodity grid
            const grid = document.getElementById('commodityGrid');
            
            if (!commodities.has(commodity)) {
                const item = document.createElement('div');
                item.className = 'commodity-item';
                item.textContent = commodity;
                item.id = `commodity-${commodity.replace(/\s+/g, '-')}`;
                grid.appendChild(item);
                commodities.set(commodity, item);
            }
            
            const item = commodities.get(commodity);
            if (action === 'completed') {
                item.className = 'commodity-item completed';
            } else {
                item.className = 'commodity-item processing';
            }
        }

        function startDurationTimer() {
            if (durationInterval) clearInterval(durationInterval);
            
            document.getElementById('startTime').textContent = startTime.toLocaleTimeString();
            
            durationInterval = setInterval(() => {
                if (!analysisComplete) {
                    const duration = Math.floor((new Date() - startTime) / 1000);
                    const minutes = Math.floor(duration / 60);
                    const seconds = duration % 60;
                    document.getElementById('duration').textContent = 
                        `${minutes}:${seconds.toString().padStart(2, '0')}`;
                }
            }, 1000);
        }

        function stopDurationTimer() {
            if (durationInterval) {
                clearInterval(durationInterval);
                durationInterval = null;
            }
        }

        function addLog(message, type = 'info') {
            const container = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            entry.textContent = `[${timestamp}] ${message}`;
            
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }

        function downloadResults() {
            const analysisId = document.getElementById('analysisId').value;
            const apiUrl = document.getElementById('apiUrl').value;
            const authToken = document.getElementById('authToken').value;
            
            // In a real implementation, fetch results from API
            window.open(`${apiUrl}/api/v1/analysis/${analysisId}/results?token=${authToken}`);
        }

        // Auto-disconnect on page unload
        window.addEventListener('beforeunload', () => {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>