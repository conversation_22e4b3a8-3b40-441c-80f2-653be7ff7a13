"""
SSE connection manager for handling multiple concurrent client connections.
"""

import asyncio
import uuid
from datetime import datetime
from typing import Dict, Set, Optional, Callable
from dataclasses import dataclass, field
import logging
from collections import defaultdict

from .....core.domain.shared.events import DomainEvent

logger = logging.getLogger(__name__)


@dataclass
class SSEConnection:
    """Represents a single SSE client connection."""
    id: str
    analysis_id: str
    queue: asyncio.Queue
    created_at: datetime = field(default_factory=datetime.utcnow)
    last_activity: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, any] = field(default_factory=dict)
    
    def update_activity(self):
        """Update last activity timestamp."""
        self.last_activity = datetime.utcnow()


class SSEConnectionManager:
    """
    Manages SSE connections for real-time event streaming.
    
    Features:
    - Connection lifecycle management
    - Event routing to specific connections
    - Heartbeat mechanism
    - Connection cleanup
    - Monitoring and metrics
    """
    
    def __init__(
        self,
        heartbeat_interval: float = 30.0,
        connection_timeout: float = 300.0,
        max_queue_size: int = 1000
    ):
        """
        Initialize SSE connection manager.
        
        Args:
            heartbeat_interval: Seconds between heartbeat messages
            connection_timeout: Seconds before timing out inactive connections
            max_queue_size: Maximum events per connection queue
        """
        self.connections: Dict[str, SSEConnection] = {}
        self.analysis_connections: Dict[str, Set[str]] = defaultdict(set)
        self.heartbeat_interval = heartbeat_interval
        self.connection_timeout = connection_timeout
        self.max_queue_size = max_queue_size
        self._running = False
        self._cleanup_task: Optional[asyncio.Task] = None
        self._event_handlers: Dict[str, Callable] = {}
        
    async def start(self):
        """Start the connection manager and background tasks."""
        if self._running:
            return
            
        self._running = True
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("SSE connection manager started")
        
    async def stop(self):
        """Stop the connection manager and clean up all connections."""
        self._running = False
        
        # Cancel cleanup task
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
                
        # Close all connections
        for conn_id in list(self.connections.keys()):
            await self.disconnect(conn_id)
            
        logger.info("SSE connection manager stopped")
        
    async def create_connection(
        self,
        analysis_id: str,
        metadata: Optional[Dict[str, any]] = None
    ) -> str:
        """
        Create a new SSE connection for an analysis.
        
        Args:
            analysis_id: ID of the analysis to monitor
            metadata: Optional connection metadata
            
        Returns:
            Connection ID
        """
        conn_id = str(uuid.uuid4())
        connection = SSEConnection(
            id=conn_id,
            analysis_id=analysis_id,
            queue=asyncio.Queue(maxsize=self.max_queue_size),
            metadata=metadata or {}
        )
        
        self.connections[conn_id] = connection
        self.analysis_connections[analysis_id].add(conn_id)
        
        logger.info(f"Created SSE connection {conn_id} for analysis {analysis_id}")
        return conn_id
        
    async def disconnect(self, connection_id: str):
        """
        Disconnect and clean up a connection.
        
        Args:
            connection_id: ID of connection to disconnect
        """
        connection = self.connections.get(connection_id)
        if not connection:
            return
            
        # Remove from tracking
        del self.connections[connection_id]
        self.analysis_connections[connection.analysis_id].discard(connection_id)
        
        # Clean up empty analysis entries
        if not self.analysis_connections[connection.analysis_id]:
            del self.analysis_connections[connection.analysis_id]
            
        logger.info(f"Disconnected SSE connection {connection_id}")
        
    async def send_event(
        self,
        analysis_id: str,
        event: DomainEvent,
        event_type: Optional[str] = None
    ):
        """
        Send an event to all connections monitoring an analysis.
        
        Args:
            analysis_id: ID of the analysis
            event: Domain event to send
            event_type: Optional event type override
        """
        conn_ids = self.analysis_connections.get(analysis_id, set())
        
        if not conn_ids:
            return
            
        # Send to all connections for this analysis
        for conn_id in list(conn_ids):
            connection = self.connections.get(conn_id)
            if connection:
                try:
                    # Try to put event in queue without blocking
                    connection.queue.put_nowait(event)
                    connection.update_activity()
                except asyncio.QueueFull:
                    logger.warning(f"Queue full for connection {conn_id}, dropping event")
                except Exception as e:
                    logger.error(f"Error sending event to connection {conn_id}: {e}")
                    await self.disconnect(conn_id)
                    
    async def get_event(
        self,
        connection_id: str,
        timeout: Optional[float] = None
    ) -> Optional[DomainEvent]:
        """
        Get next event for a connection.
        
        Args:
            connection_id: Connection ID
            timeout: Optional timeout in seconds
            
        Returns:
            Domain event or None
        """
        connection = self.connections.get(connection_id)
        if not connection:
            return None
            
        try:
            if timeout:
                event = await asyncio.wait_for(
                    connection.queue.get(),
                    timeout=timeout
                )
            else:
                event = await connection.queue.get()
                
            connection.update_activity()
            return event
            
        except asyncio.TimeoutError:
            return None
        except Exception as e:
            logger.error(f"Error getting event for connection {connection_id}: {e}")
            return None
            
    async def send_heartbeat(self, connection_id: str):
        """
        Send a heartbeat message to keep connection alive.
        
        Args:
            connection_id: Connection ID
        """
        connection = self.connections.get(connection_id)
        if connection:
            connection.update_activity()
            
    def get_connection_info(self, connection_id: str) -> Optional[Dict[str, any]]:
        """
        Get information about a connection.
        
        Args:
            connection_id: Connection ID
            
        Returns:
            Connection information or None
        """
        connection = self.connections.get(connection_id)
        if not connection:
            return None
            
        return {
            "id": connection.id,
            "analysis_id": connection.analysis_id,
            "created_at": connection.created_at.isoformat(),
            "last_activity": connection.last_activity.isoformat(),
            "queue_size": connection.queue.qsize(),
            "metadata": connection.metadata
        }
        
    def get_metrics(self) -> Dict[str, any]:
        """
        Get connection manager metrics.
        
        Returns:
            Dictionary of metrics
        """
        total_queue_size = sum(
            conn.queue.qsize() for conn in self.connections.values()
        )
        
        return {
            "total_connections": len(self.connections),
            "analyses_monitored": len(self.analysis_connections),
            "total_queue_size": total_queue_size,
            "connections_by_analysis": {
                analysis_id: len(conn_ids)
                for analysis_id, conn_ids in self.analysis_connections.items()
            }
        }
        
    async def _cleanup_loop(self):
        """Background task to clean up inactive connections."""
        while self._running:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                
                now = datetime.utcnow()
                inactive_connections = []
                
                # Find inactive connections
                for conn_id, connection in self.connections.items():
                    inactive_seconds = (now - connection.last_activity).total_seconds()
                    
                    if inactive_seconds > self.connection_timeout:
                        inactive_connections.append(conn_id)
                        
                # Clean up inactive connections
                for conn_id in inactive_connections:
                    logger.info(f"Cleaning up inactive connection {conn_id}")
                    await self.disconnect(conn_id)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                
    def register_event_handler(self, event_type: str, handler: Callable):
        """
        Register a handler for specific event types.
        
        Args:
            event_type: Event type to handle
            handler: Async callable to handle events
        """
        self._event_handlers[event_type] = handler
        
    async def handle_event(self, event: DomainEvent):
        """
        Handle incoming domain event and route to connections.
        
        Args:
            event: Domain event to handle
        """
        # Extract analysis ID from event
        analysis_id = getattr(event, 'analysis_id', None)
        if not analysis_id:
            return
            
        # Apply any registered handlers
        event_type = event.event_name.split('.')[-1]
        if event_type in self._event_handlers:
            try:
                await self._event_handlers[event_type](event)
            except Exception as e:
                logger.error(f"Error in event handler for {event_type}: {e}")
                
        # Send to connections
        await self.send_event(analysis_id, event)