"""
SSE event handlers for integrating with the event bus.
"""

import asyncio
import logging
from typing import Optional, Set, Dict, Any
from datetime import datetime

from .connection_manager import SSEConnectionManager
from .event_formatter import SSEEventFormatter
from .....core.domain.shared.events import DomainEvent
from .....application.interfaces import EventBus

logger = logging.getLogger(__name__)


class SSEEventHandler:
    """
    Handles integration between EventBus and SSE connections.
    
    This class subscribes to relevant domain events and forwards
    them to SSE connections for real-time client updates.
    """
    
    def __init__(
        self,
        event_bus: EventBus,
        connection_manager: SSEConnectionManager
    ):
        """
        Initialize SSE event handler.
        
        Args:
            event_bus: Application event bus
            connection_manager: SSE connection manager
        """
        self.event_bus = event_bus
        self.connection_manager = connection_manager
        self._subscribed_patterns: Set[str] = set()
        self._running = False
        
    async def start(self):
        """Start the event handler and subscribe to events."""
        if self._running:
            return
            
        self._running = True
        
        # Subscribe to analysis-related events
        event_patterns = [
            "analysis.progress",
            "analysis.status",
            "analysis.completed",
            "analysis.failed",
            "analysis.tier.started",
            "analysis.tier.completed",
            "analysis.commodity"
        ]
        
        for pattern in event_patterns:
            self.event_bus.subscribe(pattern, self._handle_event)
            self._subscribed_patterns.add(pattern)
            
        # Also subscribe to all events for monitoring
        self.event_bus.subscribe_all(self._handle_all_events)
        
        logger.info("SSE event handler started and subscribed to events")
        
    async def stop(self):
        """Stop the event handler and unsubscribe from events."""
        self._running = False
        
        # Note: In a real implementation, we'd need proper unsubscribe methods
        # For now, we just mark as stopped
        
        logger.info("SSE event handler stopped")
        
    async def _handle_event(self, event: DomainEvent):
        """
        Handle a specific domain event.
        
        Args:
            event: Domain event to handle
        """
        if not self._running:
            return
            
        try:
            # Extract analysis ID
            analysis_id = getattr(event, 'analysis_id', None)
            if not analysis_id:
                # Try to extract from event name
                parts = event.event_name.split('.')
                if len(parts) > 2:
                    analysis_id = parts[-1]
                    
            if analysis_id:
                # Forward to connection manager
                await self.connection_manager.handle_event(event)
                
        except Exception as e:
            logger.error(f"Error handling event {event.event_name}: {e}")
            
    async def _handle_all_events(self, event: DomainEvent):
        """
        Handle all events for monitoring purposes.
        
        Args:
            event: Domain event
        """
        # Log event for monitoring
        if hasattr(event, 'analysis_id'):
            logger.debug(f"Event {event.event_name} for analysis {event.analysis_id}")
            
    async def create_sse_stream(
        self,
        analysis_id: str,
        initial_status: Optional[Dict[str, Any]] = None
    ):
        """
        Create an SSE stream for an analysis.
        
        Args:
            analysis_id: Analysis ID to stream
            initial_status: Optional initial status data
            
        Yields:
            Formatted SSE events
        """
        # Create connection
        conn_id = await self.connection_manager.create_connection(analysis_id)
        
        try:
            # Send initial status if provided
            if initial_status:
                yield SSEEventFormatter.format_initial_status(
                    analysis_id=analysis_id,
                    status=initial_status.get("status", "unknown"),
                    progress=initial_status.get("progress", 0),
                    details=initial_status
                )
                
            # Stream events
            heartbeat_counter = 0
            while True:
                try:
                    # Try to get event with timeout
                    event = await self.connection_manager.get_event(
                        conn_id,
                        timeout=30.0
                    )
                    
                    if event:
                        # Format and yield event
                        yield SSEEventFormatter.format_event(event)
                        
                        # Check if terminal event
                        if hasattr(event, 'event_name'):
                            if event.event_name.endswith('.completed') or \
                               event.event_name.endswith('.failed'):
                                logger.info(f"Terminal event received for {analysis_id}")
                                break
                    else:
                        # Send heartbeat
                        heartbeat_counter += 1
                        yield SSEEventFormatter.format_heartbeat()
                        await self.connection_manager.send_heartbeat(conn_id)
                        
                except asyncio.CancelledError:
                    logger.info(f"SSE stream cancelled for {analysis_id}")
                    raise
                except Exception as e:
                    logger.error(f"Error in SSE stream for {analysis_id}: {e}")
                    yield SSEEventFormatter.format_error(
                        error=str(e),
                        error_type=type(e).__name__
                    )
                    
        finally:
            # Clean up connection
            await self.connection_manager.disconnect(conn_id)
            logger.info(f"SSE stream ended for {analysis_id}")
            
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get handler metrics.
        
        Returns:
            Dictionary of metrics
        """
        return {
            "subscribed_patterns": list(self._subscribed_patterns),
            "running": self._running,
            "connection_metrics": self.connection_manager.get_metrics()
        }