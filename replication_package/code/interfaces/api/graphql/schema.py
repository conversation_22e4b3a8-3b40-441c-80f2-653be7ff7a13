"""GraphQL schema definition."""

import strawberry
from typing import List, Optional, Any # Added Any
from datetime import datetime

from ....application.queries import GetMarketPricesQuery # Added GetMarketPricesQuery
from ....core.domain.market.value_objects import MarketId, Commodity # Added MarketId, Commodity
from ....core.domain.market.repositories import PriceRepository # Added PriceRepository
from ....shared.container import Container # Added Container
from dependency_injector.wiring import Provide, inject # Added Provide, inject

@strawberry.type
class MarketPrice:
    market_id: str
    commodity_code: str
    commodity_name: str
    price_amount: float
    price_currency: str
    price_unit: str
    observed_date: datetime
    source: str
    quality: str
    observations_count: int

@strawberry.type
class Query:
    @strawberry.field
    async def hello(self) -> str:
        return "Hello, GraphQL!"

    @strawberry.field
    @inject
    async def market_prices(
        self,
        market_ids: Optional[List[str]] = None,
        commodity_codes: Optional[List[str]] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        container: Container = Provide[Container]
    ) -> List[MarketPrice]:
        """Query market prices with optional filters."""
        # Get repositories from container
        price_repo = container.price_repository()
        commodity_repo = container.commodity_repository()
        
        # Create query handler
        from ....application.queries.get_market_prices_query import GetMarketPricesQueryHandler
        handler = GetMarketPricesQueryHandler(price_repo, commodity_repo)
        
        # Create and execute query
        query = GetMarketPricesQuery(
            market_ids=market_ids,
            commodity_codes=commodity_codes,
            start_date=start_date,
            end_date=end_date
        )
        
        # Get results
        results = await handler.handle(query)
        
        # Convert to GraphQL types
        return [
            MarketPrice(
                market_id=r["market_id"],
                commodity_code=r["commodity_code"],
                commodity_name=r["commodity_name"],
                price_amount=r["price_amount"],
                price_currency=r["price_currency"],
                price_unit=r["price_unit"],
                observed_date=datetime.fromisoformat(r["observed_date"]),
                source=r["source"],
                quality=r["quality"],
                observations_count=r["observations_count"]
            )
            for r in results
        ]

schema = strawberry.Schema(query=Query)
