"""API endpoints for policy analysis features."""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from typing import Dict, Any, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field

from ...application.services.policy_orchestrator import PolicyOrchestrator
from ...application.services.policy_data_adapter import PolicyDataAdapter
from ...core.models.policy.welfare_impact_model import PolicyIntervention
from ...core.domain.shared.value_objects import Money
from ...infrastructure.persistence.policy_repository import PolicyResultsRepository
from ...shared.container import get_container

router = APIRouter(prefix="/policy", tags=["Policy Analysis"])


# Request/Response Models
class PolicyInterventionRequest(BaseModel):
    """Request model for policy intervention analysis."""
    type: str = Field(..., description="Type: cash_transfer, price_subsidy, in_kind, infrastructure")
    target_markets: List[str] = Field(..., description="List of market IDs to target")
    target_commodities: List[str] = Field(..., description="List of commodity codes to target")
    magnitude: float = Field(..., description="Amount or percentage of intervention")
    duration_months: int = Field(..., description="Duration of intervention in months")
    targeting_criteria: Dict[str, Any] = Field(default_factory=dict, description="Targeting criteria")
    budget_constraint: Optional[float] = Field(None, description="Budget constraint in USD")
    household_survey_path: Optional[str] = Field(None, description="Path to household survey data")


class EarlyWarningRequest(BaseModel):
    """Request model for early warning analysis."""
    forecast_horizon: int = Field(30, description="Days to forecast ahead")
    include_conflict_data: bool = Field(True, description="Include ACLED conflict data")
    include_climate_data: bool = Field(False, description="Include climate data")
    alert_threshold: str = Field("MEDIUM", description="Minimum alert level to report")


class PolicyComparisonRequest(BaseModel):
    """Request model for comparing multiple interventions."""
    interventions: List[PolicyInterventionRequest] = Field(..., description="List of interventions to compare")
    household_survey_path: Optional[str] = Field(None, description="Path to household survey data")


class PolicyMonitoringRequest(BaseModel):
    """Request model for monitoring active interventions."""
    active_interventions: List[Dict[str, Any]] = Field(..., description="List of active interventions with metadata")


@router.post("/welfare-impact/analyze")
async def analyze_welfare_impact(
    request: PolicyInterventionRequest,
    background_tasks: BackgroundTasks,
    container = Depends(get_container)
) -> Dict[str, Any]:
    """Analyze welfare impact of a policy intervention.
    
    This endpoint analyzes the potential welfare impacts of a policy intervention
    including consumer/producer surplus changes, distributional effects, and cost-effectiveness.
    """
    try:
        # Get services from container
        policy_orchestrator = container.policy_orchestrator
        panel_data = await container.panel_builder.get_latest_panel_data()
        
        if panel_data is None or panel_data.empty:
            raise HTTPException(status_code=400, detail="No panel data available for analysis")
        
        # Create intervention object
        intervention = PolicyIntervention(
            type=request.type,
            target_markets=request.target_markets,
            target_commodities=request.target_commodities,
            magnitude=request.magnitude,
            duration_months=request.duration_months,
            targeting_criteria=request.targeting_criteria,
            budget_constraint=Money(request.budget_constraint, "USD") if request.budget_constraint else None
        )
        
        # Run analysis
        results = await policy_orchestrator.analyze_policy_intervention(
            intervention=intervention,
            panel_data=panel_data,
            household_survey_path=request.household_survey_path
        )
        
        # Save results in background
        analysis_id = results.get('analysis_id', datetime.utcnow().strftime('%Y%m%d_%H%M%S'))
        background_tasks.add_task(
            container.policy_repository.save_welfare_analysis,
            analysis_id,
            results
        )
        
        return {
            "status": "success",
            "analysis_id": analysis_id,
            "results": results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/early-warning/generate")
async def generate_early_warnings(
    request: EarlyWarningRequest,
    background_tasks: BackgroundTasks,
    container = Depends(get_container)
) -> Dict[str, Any]:
    """Generate early warning alerts based on current market conditions.
    
    This endpoint analyzes current market data to detect anomalies and predict
    potential food security crises, generating actionable alerts.
    """
    try:
        # Get services
        policy_orchestrator = container.policy_orchestrator
        panel_data = await container.panel_builder.get_latest_panel_data()
        
        if panel_data is None or panel_data.empty:
            raise HTTPException(status_code=400, detail="No panel data available for analysis")
        
        # Get additional data if requested
        conflict_data = None
        climate_data = None
        
        if request.include_conflict_data:
            conflict_data = await container.acled_processor.get_recent_events(days=90)
        
        if request.include_climate_data:
            # Climate data would come from external source
            pass
        
        # Generate warnings
        results = await policy_orchestrator.generate_early_warnings(
            panel_data=panel_data,
            conflict_data=conflict_data,
            climate_data=climate_data,
            forecast_horizon=request.forecast_horizon
        )
        
        # Filter alerts by threshold
        alert_levels = {"LOW": 1, "MEDIUM": 2, "HIGH": 3, "CRITICAL": 4}
        min_level = alert_levels.get(request.alert_threshold, 2)
        
        filtered_alerts = [
            alert for alert in results['alerts']
            if alert['alert_level'] >= min_level
        ]
        results['alerts'] = filtered_alerts
        
        # Save results
        warning_id = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        background_tasks.add_task(
            container.policy_repository.save_early_warning,
            warning_id,
            results
        )
        
        return {
            "status": "success",
            "warning_id": warning_id,
            "results": results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/compare-interventions")
async def compare_interventions(
    request: PolicyComparisonRequest,
    background_tasks: BackgroundTasks,
    container = Depends(get_container)
) -> Dict[str, Any]:
    """Compare multiple policy interventions.
    
    This endpoint allows comparison of different policy interventions to identify
    the most effective and cost-efficient options.
    """
    try:
        # Get services
        policy_orchestrator = container.policy_orchestrator
        panel_data = await container.panel_builder.get_latest_panel_data()
        
        if panel_data is None or panel_data.empty:
            raise HTTPException(status_code=400, detail="No panel data available for analysis")
        
        # Convert requests to intervention objects
        interventions = []
        for req in request.interventions:
            intervention = PolicyIntervention(
                type=req.type,
                target_markets=req.target_markets,
                target_commodities=req.target_commodities,
                magnitude=req.magnitude,
                duration_months=req.duration_months,
                targeting_criteria=req.targeting_criteria,
                budget_constraint=Money(req.budget_constraint, "USD") if req.budget_constraint else None
            )
            interventions.append(intervention)
        
        # Run comparison
        results = await policy_orchestrator.run_policy_comparison(
            interventions=interventions,
            panel_data=panel_data,
            household_survey_path=request.household_survey_path
        )
        
        # Save results
        comparison_id = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        background_tasks.add_task(
            container.policy_repository.save_policy_comparison,
            comparison_id,
            results
        )
        
        return {
            "status": "success",
            "comparison_id": comparison_id,
            "results": results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/monitor-interventions")
async def monitor_interventions(
    request: PolicyMonitoringRequest,
    background_tasks: BackgroundTasks,
    container = Depends(get_container)
) -> Dict[str, Any]:
    """Monitor active policy interventions.
    
    This endpoint tracks the performance of ongoing interventions against
    expected outcomes and provides recommendations.
    """
    try:
        # Get services
        policy_orchestrator = container.policy_orchestrator
        panel_data = await container.panel_builder.get_latest_panel_data()
        
        if panel_data is None or panel_data.empty:
            raise HTTPException(status_code=400, detail="No panel data available for monitoring")
        
        # Monitor interventions
        results = await policy_orchestrator.monitor_policy_impacts(
            active_interventions=request.active_interventions,
            panel_data=panel_data
        )
        
        # Save results
        monitoring_id = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        background_tasks.add_task(
            container.policy_repository.save_monitoring_results,
            monitoring_id,
            results
        )
        
        return {
            "status": "success",
            "monitoring_id": monitoring_id,
            "results": results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/early-warning/recent")
async def get_recent_warnings(
    days: int = 7,
    min_alert_level: str = "MEDIUM",
    container = Depends(get_container)
) -> Dict[str, Any]:
    """Get recent early warning alerts.
    
    Returns early warning alerts from the past N days that meet the minimum
    alert level threshold.
    """
    try:
        policy_repository = container.policy_repository
        
        alert_levels = {"LOW": 1, "MEDIUM": 2, "HIGH": 3, "CRITICAL": 4}
        min_level = alert_levels.get(min_alert_level, 2)
        
        warnings = await policy_repository.get_recent_early_warnings(
            days=days,
            alert_level_min=min_level
        )
        
        return {
            "status": "success",
            "count": len(warnings),
            "warnings": warnings
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/interventions/history")
async def get_intervention_history(
    intervention_type: Optional[str] = None,
    days: int = 30,
    container = Depends(get_container)
) -> Dict[str, Any]:
    """Get history of policy interventions.
    
    Returns past intervention analyses, optionally filtered by type.
    """
    try:
        policy_repository = container.policy_repository
        
        interventions = await policy_repository.get_intervention_history(
            intervention_type=intervention_type,
            days=days
        )
        
        return {
            "status": "success",
            "count": len(interventions),
            "interventions": interventions
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance-metrics")
async def get_performance_metrics(
    start_date: datetime,
    end_date: datetime,
    container = Depends(get_container)
) -> Dict[str, Any]:
    """Get aggregate performance metrics for policy analyses.
    
    Returns summary statistics for all policy analyses conducted within
    the specified time period.
    """
    try:
        policy_repository = container.policy_repository
        
        metrics = await policy_repository.get_performance_metrics(
            start_date=start_date,
            end_date=end_date
        )
        
        return {
            "status": "success",
            "period": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat()
            },
            "metrics": metrics
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/welfare-impact/{analysis_id}")
async def get_welfare_analysis(
    analysis_id: str,
    container = Depends(get_container)
) -> Dict[str, Any]:
    """Get specific welfare impact analysis results."""
    try:
        policy_repository = container.policy_repository
        
        analysis = await policy_repository.get_welfare_analysis(analysis_id)
        
        if analysis is None:
            raise HTTPException(status_code=404, detail="Analysis not found")
        
        return {
            "status": "success",
            "analysis": analysis
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/early-warning/{warning_id}")
async def get_early_warning(
    warning_id: str,
    container = Depends(get_container)
) -> Dict[str, Any]:
    """Get specific early warning results."""
    try:
        policy_repository = container.policy_repository
        
        warning = await policy_repository.get_early_warning(warning_id)
        
        if warning is None:
            raise HTTPException(status_code=404, detail="Warning not found")
        
        return {
            "status": "success",
            "warning": warning
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/comparison/{comparison_id}")
async def get_policy_comparison(
    comparison_id: str,
    container = Depends(get_container)
) -> Dict[str, Any]:
    """Get specific policy comparison results."""
    try:
        policy_repository = container.policy_repository
        
        comparison = await policy_repository.get_policy_comparison(comparison_id)
        
        if comparison is None:
            raise HTTPException(status_code=404, detail="Comparison not found")
        
        return {
            "status": "success",
            "comparison": comparison
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/export-summary")
async def export_results_summary(
    output_path: str,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    container = Depends(get_container)
) -> Dict[str, Any]:
    """Export summary of all policy analysis results to CSV."""
    try:
        policy_repository = container.policy_repository
        
        await policy_repository.export_results_summary(
            output_path=output_path,
            start_date=start_date,
            end_date=end_date
        )
        
        return {
            "status": "success",
            "output_path": output_path,
            "message": "Results exported successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))