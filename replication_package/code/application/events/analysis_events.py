"""
Domain events for analysis progress tracking.
"""

from datetime import datetime
from typing import Optional, Dict, Any

from ...core.domain.shared.events import DomainEvent


class AnalysisProgressEvent(DomainEvent):
    """Event emitted when analysis makes progress."""
    
    def __init__(
        self,
        analysis_id: str,
        progress: int,
        tier: Optional[str] = None,
        message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """Initialize analysis progress event."""
        super().__init__(
            event_name=f"analysis.progress.{analysis_id}",
            occurred_at=datetime.utcnow()
        )
        self.analysis_id = analysis_id
        self.progress = progress
        self.tier = tier
        self.message = message
        self.details = details or {}


class AnalysisStatusChangedEvent(DomainEvent):
    """Event emitted when analysis status changes."""
    
    def __init__(
        self,
        analysis_id: str,
        old_status: str,
        new_status: str,
        reason: Optional[str] = None
    ):
        """Initialize analysis status change event."""
        super().__init__(
            event_name=f"analysis.status.{analysis_id}",
            occurred_at=datetime.utcnow()
        )
        self.analysis_id = analysis_id
        self.old_status = old_status
        self.status = new_status  # 'status' for SSE compatibility
        self.new_status = new_status
        self.reason = reason


class AnalysisCompletedEvent(DomainEvent):
    """Event emitted when analysis completes successfully."""
    
    def __init__(
        self,
        analysis_id: str,
        results_summary: Dict[str, Any],
        duration_seconds: float
    ):
        """Initialize analysis completed event."""
        super().__init__(
            event_name=f"analysis.completed.{analysis_id}",
            occurred_at=datetime.utcnow()
        )
        self.analysis_id = analysis_id
        self.status = "completed"
        self.results_summary = results_summary
        self.duration_seconds = duration_seconds
        self.message = f"Analysis completed in {duration_seconds:.1f} seconds"


class AnalysisFailedEvent(DomainEvent):
    """Event emitted when analysis fails."""
    
    def __init__(
        self,
        analysis_id: str,
        error: str,
        error_type: str,
        tier: Optional[str] = None,
        traceback: Optional[str] = None
    ):
        """Initialize analysis failed event."""
        super().__init__(
            event_name=f"analysis.failed.{analysis_id}",
            occurred_at=datetime.utcnow()
        )
        self.analysis_id = analysis_id
        self.status = "failed"
        self.error = error
        self.error_type = error_type
        self.tier = tier
        self.traceback = traceback
        self.message = f"Analysis failed: {error}"


class TierStartedEvent(DomainEvent):
    """Event emitted when a specific tier starts processing."""
    
    def __init__(
        self,
        analysis_id: str,
        tier: str,
        config: Dict[str, Any]
    ):
        """Initialize tier started event."""
        super().__init__(
            event_name=f"analysis.tier.started.{analysis_id}",
            occurred_at=datetime.utcnow()
        )
        self.analysis_id = analysis_id
        self.tier = tier
        self.config = config
        self.message = f"Started processing {tier}"


class TierCompletedEvent(DomainEvent):
    """Event emitted when a specific tier completes."""
    
    def __init__(
        self,
        analysis_id: str,
        tier: str,
        results: Dict[str, Any],
        duration_seconds: float
    ):
        """Initialize tier completed event."""
        super().__init__(
            event_name=f"analysis.tier.completed.{analysis_id}",
            occurred_at=datetime.utcnow()
        )
        self.analysis_id = analysis_id
        self.tier = tier
        self.results = results
        self.duration_seconds = duration_seconds
        self.message = f"Completed {tier} in {duration_seconds:.1f} seconds"


class CommodityProcessingEvent(DomainEvent):
    """Event emitted during commodity-specific processing."""
    
    def __init__(
        self,
        analysis_id: str,
        commodity: str,
        action: str,
        progress: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """Initialize commodity processing event."""
        super().__init__(
            event_name=f"analysis.commodity.{analysis_id}",
            occurred_at=datetime.utcnow()
        )
        self.analysis_id = analysis_id
        self.tier = "tier2"  # Commodity analysis is Tier 2
        self.commodity = commodity
        self.action = action
        self.progress = progress
        self.details = details or {}
        self.message = f"{action} for {commodity}"