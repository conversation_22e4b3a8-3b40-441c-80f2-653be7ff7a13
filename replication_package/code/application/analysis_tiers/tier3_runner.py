"""Tier 3 analysis runner."""

from typing import Any, Dict, List, Optional # Added Optional
import pandas as pd
import numpy as np

from ...core.domain.market.repositories import MarketRepository, PriceRepository
from ...core.models.interfaces import ModelSpecification
from ...core.models.validation import (
    ConflictValidationModel,
    FactorModel,
    PCAModel,
    CrossValidationModel,
    DynamicFactorModel
)
from ...infrastructure.diagnostics import PanelDiagnosticTests, TimeSeriesDiagnosticTests
from ...infrastructure.estimators.validation import ConflictValidator, FactorAnalyzer, PCAAnalyzer, DynamicFactorAnalyzer # Added DynamicFactorAnalyzer
from ..services import AnalysisOrchestrator, ModelEstimatorService
from ...core.validation.methodology_validator import MethodologyValidator, MethodologyViolation, AnalysisType
from ...core.utils.logging import get_logger


class Tier3Runner:
    """Encapsulates logic for running Tier 3 econometric analysis."""

    def __init__(
        self,
        market_repo: MarketRepository,
        price_repo: PriceRepository,
        orchestrator: AnalysisOrchestrator,
        estimator_service: ModelEstimatorService
    ):
        self.market_repo = market_repo
        self.price_repo = price_repo
        self.orchestrator = orchestrator
        self.estimator_service = estimator_service
        self._logger = get_logger(__name__)

    async def run(
        self,
        command: Any,  # RunThreeTierAnalysisCommand
        analysis_id: str,
        tier1_result: Dict[str, Any],
        tier2_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Run Tier 3: Cross-validation and robustness checks using v2 models."""
        await self.orchestrator.update_progress(
            analysis_id, "tier3", 0, "Starting validation..."
        )
        
        validation_results = {}
        panel_data = await self._load_panel_data(command)
        
        # Validate panel data before any analysis
        validator = MethodologyValidator()
        hypothesis_tests = getattr(command, 'hypotheses', [])
        
        # Prepare data for validation
        validation_df = panel_data.reset_index() if isinstance(panel_data.index, pd.MultiIndex) else panel_data
        
        is_valid, report = validator.validate_analysis_inputs(
            observations=validation_df,
            analysis_type=AnalysisType.PANEL_ANALYSIS,  # Tier 3 performs validation on panel data
            hypothesis_tests=hypothesis_tests
        )
        
        if not is_valid:
            self._logger.error(f"Methodology validation failed for Tier 3: {report}")
            raise MethodologyViolation(
                f"Critical validation failures in Tier 3: {report.critical_failures}",
                report=report
            )
        
        self._logger.info("Methodology validation passed for Tier 3 analysis")
        
        # 1. Cross-validation using v2 CrossValidationModel
        if "cross_validation" in command.tier3_config["validation_methods"]:
            await self.orchestrator.update_progress(
                analysis_id, "tier3", 20, "Running cross-validation..."
            )
            
            cv_spec = ModelSpecification(
                model_type="cross_validation",
                dependent_variable=tier1_result["result"].model.specification.dependent_variable,
                independent_variables=tier1_result["result"].model.specification.independent_variables,
                parameters={
                    "cv_method": "time_series",
                    "n_splits": 5,
                    "test_size": 0.2
                }
            )
            cv_model = CrossValidationModel(cv_spec)
            
            # Define model function for CV
            # This function needs to be able to estimate the model given training data
            # It should return an EstimationResult compatible object
            async def model_func(train_data):
                # Re-instantiate the Tier 1 model with the same spec
                tier1_model_reinstantiated = type(tier1_result["result"].model)(tier1_result["result"].model.specification)
                return await self.estimator_service.estimate(
                    tier1_model_reinstantiated,
                    train_data
                )
            
            # The cross_validate method in CrossValidationModel expects an awaitable model_func
            validation_results["cross_validation"] = await cv_model.cross_validate(
                panel_data, model_func
            )
        
        # 2. Conflict validation using v2 ConflictValidationModel
        if "conflict_validation" in command.tier3_config["validation_methods"]:
            await self.orchestrator.update_progress(
                analysis_id, "tier3", 40, "Analyzing conflict effects..."
            )
            
            conflict_spec = ModelSpecification(
                model_type="conflict_validation",
                dependent_variable=tier1_result["result"].model.specification.dependent_variable,
                independent_variables=tier1_result["result"].model.specification.independent_variables,
                parameters={
                    "conflict_variable": "conflict_events",
                    "threshold_quantiles": [0.25, 0.5, 0.75]
                }
            )
            conflict_model = ConflictValidationModel(conflict_spec)
            
            conflict_validator = ConflictValidator()
            validation_results["conflict_analysis"] = await conflict_validator.estimate(
                conflict_model, panel_data
            )
        
        # 3. Factor analysis using v2 FactorModel
        if command.tier3_config.get("factor_analysis", True):
            await self.orchestrator.update_progress(
                analysis_id, "tier3", 60, "Conducting factor analysis..."
            )
            
            factor_spec = ModelSpecification(
                model_type="factor_analysis",
                dependent_variable=None,
                independent_variables=[],
                parameters={
                    "n_factors": 3,
                    "rotation": "varimax",
                    "standardize": True
                }
            )
            factor_model = FactorModel(factor_spec)
            
            # Extract residuals from tier1 and tier2
            residuals_data = self._extract_residuals_for_factor_analysis(
                tier1_result, tier2_results
            )
            
            factor_analyzer = FactorAnalyzer() # Instantiate the analyzer
            validation_results["factor_analysis"] = factor_analyzer.estimate(
                factor_model, residuals_data
            )
        
        # 4. PCA analysis using v2 PCAModel
        if command.tier3_config.get("pca_analysis", True):
            await self.orchestrator.update_progress(
                analysis_id, "tier3", 80, "Running PCA..."
            )
            
            pca_spec = ModelSpecification(
                model_type="pca",
                dependent_variable=None,
                independent_variables=[],
                parameters={
                    "n_components": None,  # Auto-select
                    "variance_threshold": 0.95,
                    "standardize": True
                }
            )
            pca_model = PCAModel(pca_spec)
            
            pca_analyzer = PCAAnalyzer() # Instantiate the analyzer
            validation_results["pca_analysis"] = pca_analyzer.estimate(pca_model, panel_data)
        
        # 5. Structural break tests using v2 diagnostics
        if "structural_break" in command.tier3_config["validation_methods"]:
            await self.orchestrator.update_progress(
                analysis_id, "tier3", 90, "Testing structural breaks..."
            )
            
            panel_diagnostics = PanelDiagnosticTests()
            validation_results["structural_breaks"] = panel_diagnostics.test_structural_breaks(
                panel_data,
                f"{tier1_result['result'].model.specification.dependent_variable} ~ " + 
                " + ".join(tier1_result['result'].model.specification.independent_variables)
            )
        
        # 6. Dynamic Factor Model (Addressing Critical Gap)
        if command.tier3_config.get("dynamic_factor_analysis", False): # Add a config option for this
            await self.orchestrator.update_progress(
                analysis_id, "tier3", 95, "Conducting dynamic factor analysis..."
            )
            dynamic_factor_spec = ModelSpecification(
                model_type="dynamic_factor_analysis",
                dependent_variable=None,
                independent_variables=[],
                parameters={
                    "n_factors": 2,
                    "factor_order": 1,
                    "standardize": True
                }
            )
            dynamic_factor_model = DynamicFactorModel(dynamic_factor_spec)
            
            # Use the combined residuals data for dynamic factor analysis
            dynamic_factor_analyzer = DynamicFactorAnalyzer() # Instantiate the analyzer
            validation_results["dynamic_factor_analysis"] = dynamic_factor_analyzer.estimate(
                dynamic_factor_model, residuals_data
            )
            
        # 7. Nowcasting and Early Warning System (NEW)
        if command.tier3_config.get("nowcasting", True):
            await self.orchestrator.update_progress(
                analysis_id, "tier3", 98, "Running nowcasting and early warning analysis..."
            )
            
            # Import nowcasting orchestrator
            from ...infrastructure.estimators.nowcasting_estimators import NowcastingOrchestrator
            
            nowcast_orchestrator = NowcastingOrchestrator()
            
            # Extract markets and commodities from data
            markets = panel_data.index.get_level_values('market_id').unique().tolist()[:10]  # Limit for demo
            commodities = [tier1_result['result'].model.specification.dependent_variable.replace('usd_price_', '')]
            
            # Run comprehensive nowcasting
            nowcast_results = await nowcast_orchestrator.run_comprehensive_nowcasting(
                panel_data=panel_data,
                markets=markets,
                commodities=commodities,
                methods=['dynamic_factor', 'sarimax', 'random_forest'],
                generate_warnings=True,
                forecast_horizon=3,  # 3 months ahead
                n_factors=3
            )
            
            validation_results["nowcasting"] = nowcast_results
            
            # Log warning summary if generated
            if 'warnings' in nowcast_results and nowcast_results['warnings']:
                self._log_early_warnings(nowcast_results['warnings'])

        await self.orchestrator.update_progress(
            analysis_id, "tier3", 100, "Tier 3 complete"
        )
        
        return validation_results

    async def _load_panel_data(
        self,
        command: Any  # RunThreeTierAnalysisCommand
    ) -> pd.DataFrame:
        """Load and prepare panel data for Tier 3 validation."""
        # Reuse the same data loading logic from Tier 1
        from ..analysis_tiers.tier1_runner import Tier1Runner
        
        # Create a temporary Tier1Runner instance to reuse its data loading
        tier1_runner = Tier1Runner(
            market_repo=self.market_repo,
            price_repo=self.price_repo,
            orchestrator=self.orchestrator,
            estimator_service=self.estimator_service
        )
        
        # Load the same panel data used in Tier 1
        panel_data = await tier1_runner._load_panel_data(command)
        
        return panel_data
    
    def _extract_residuals_for_factor_analysis(
        self,
        tier1_result: Dict[str, Any],
        tier2_results: Dict[str, Any]
    ) -> pd.DataFrame:
        """Extract and combine residuals from Tier 1 and Tier 2 for factor analysis."""
        from src.core.utils.logging import get_logger
        logger = get_logger(__name__)
        
        residuals_list = []
        
        # Extract Tier 1 residuals
        if 'result' in tier1_result and hasattr(tier1_result['result'], 'residuals'):
            tier1_residuals = tier1_result['result'].residuals
            
            # Convert to DataFrame if it's a Series
            if isinstance(tier1_residuals, pd.Series):
                tier1_df = tier1_residuals.to_frame(name='residual_tier1')
            else:
                tier1_df = pd.DataFrame(tier1_residuals, columns=['residual_tier1'])
            
            # Ensure it has the proper index
            if not isinstance(tier1_df.index, pd.MultiIndex):
                logger.warning("Tier 1 residuals don't have MultiIndex, attempting to reconstruct")
                # Try to reconstruct from the original data if available
                if 'metadata' in tier1_result:
                    # This is a fallback - ideally the residuals should preserve the index
                    tier1_df['market_id'] = 'unknown'
                    tier1_df['date'] = pd.date_range(
                        start='2023-01-01', 
                        periods=len(tier1_df), 
                        freq='D'
                    )
                    tier1_df = tier1_df.set_index(['market_id', 'date'])
            
            residuals_list.append(tier1_df)
        
        # Extract Tier 2 residuals for each commodity
        for commodity, result in tier2_results.items():
            if 'result' in result and hasattr(result['result'], 'residuals'):
                commodity_residuals = result['result'].residuals
                
                # Handle different result structures
                if isinstance(commodity_residuals, dict):
                    # If residuals are stored per market pair
                    for pair, resid in commodity_residuals.items():
                        if isinstance(resid, pd.Series):
                            resid_df = resid.to_frame(name=f'residual_tier2_{commodity}_{pair}')
                        else:
                            resid_df = pd.DataFrame(resid, columns=[f'residual_tier2_{commodity}_{pair}'])
                        
                        # Add market pair info if not in index
                        if 'market_pair' not in resid_df.columns and '-' in pair:
                            source_market, target_market = pair.split('-')
                            resid_df['market_id'] = source_market  # Use source market as primary
                        
                        residuals_list.append(resid_df)
                else:
                    # Single residual series for the commodity
                    if isinstance(commodity_residuals, pd.Series):
                        resid_df = commodity_residuals.to_frame(name=f'residual_tier2_{commodity}')
                    else:
                        resid_df = pd.DataFrame(commodity_residuals, columns=[f'residual_tier2_{commodity}'])
                    
                    residuals_list.append(resid_df)
        
        if not residuals_list:
            logger.warning("No residuals found in Tier 1 or Tier 2 results")
            # Return empty DataFrame with expected structure
            return pd.DataFrame(columns=['residual_tier1']).set_index(['market_id', 'date'])
        
        # Combine all residuals
        try:
            # Align by index (market_id, date)
            combined_residuals = pd.concat(residuals_list, axis=1, join='outer')
            
            # Fill missing values with 0 (or could use interpolation)
            combined_residuals = combined_residuals.fillna(0)
            
            logger.info(
                f"Extracted residuals: {combined_residuals.shape[0]} observations, "
                f"{combined_residuals.shape[1]} residual series"
            )
            
            return combined_residuals
            
        except Exception as e:
            logger.error(f"Error combining residuals: {e}")
            # Return first available residuals as fallback
            return residuals_list[0] if residuals_list else pd.DataFrame()
    
    def _log_early_warnings(self, warnings: List[Any]) -> None:
        """Log summary of early warning signals."""
        from ...core.utils.logging import get_logger
        logger = get_logger(__name__)
        
        # Count warnings by severity
        severity_counts = {}
        for warning in warnings:
            severity = warning.severity
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
        logger.info(f"Generated {len(warnings)} early warning signals:")
        for severity, count in severity_counts.items():
            logger.info(f"  - {severity}: {count} warnings")
            
        # Log critical warnings
        critical_warnings = [w for w in warnings if w.severity == 'critical']
        if critical_warnings:
            logger.warning(f"CRITICAL WARNINGS: {len(critical_warnings)} markets at risk")
            for warning in critical_warnings[:5]:  # Show first 5
                logger.warning(
                    f"  - {warning.market_id}/{warning.commodity}: "
                    f"{warning.signal_type} predicted for {warning.predicted_date.strftime('%Y-%m-%d')}"
                )
