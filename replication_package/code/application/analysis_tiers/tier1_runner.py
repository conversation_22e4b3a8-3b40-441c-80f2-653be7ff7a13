"""Tier 1 analysis runner."""

from typing import Any, Dict, List, Optional
import pandas as pd
import numpy as np  # Added import for numpy
import statsmodels.api as sm  # Added import for statsmodels

from ...core.domain.market.repositories import MarketRepository, PriceRepository
from ...core.domain.market.value_objects import MarketId
from ...core.models import PooledPanelModel, TwoWayFixedEffectsModel
from ...core.models.panel import InteractiveFixedEffectsModelWrapper
from ...core.models.interfaces import ModelSpecification
from ...infrastructure.diagnostics import PanelDiagnosticTests
from ...infrastructure.estimators.standard_errors import StandardErrorEstimator
from ..services import AnalysisOrchestrator, ModelEstimatorService
from ...core.validation.methodology_validator import (
    MethodologyValidator,
    MethodologyViolation,
    AnalysisType,
)
from ...core.utils.logging import get_logger
from ...core.models.machine_learning.currency_aware_clustering import (
    apply_currency_aware_clustering,
    CurrencyAwareClusteringResults,
)


class Tier1Runner:
    """Encapsulates logic for running Tier 1 econometric analysis."""

    def __init__(
        self,
        market_repo: MarketRepository,
        price_repo: PriceRepository,
        orchestrator: AnalysisOrchestrator,
        estimator_service: ModelEstimatorService,
    ):
        self.market_repo = market_repo
        self.price_repo = price_repo
        self.orchestrator = orchestrator
        self.estimator_service = estimator_service
        self._logger = get_logger(__name__)

    async def run(
        self, command: Any, analysis_id: str  # RunThreeTierAnalysisCommand
    ) -> Dict[str, Any]:
        """Run Tier 1: Pooled panel analysis."""
        await self.orchestrator.update_progress(
            analysis_id, "tier1", 0, "Loading panel data..."
        )

        panel_data = await self._load_panel_data(command)

        # Step 1: Initialize validator
        validator = MethodologyValidator()

        # Step 2: Validate BEFORE any analysis
        hypothesis_tests = getattr(command, "hypotheses", [])
        is_valid, report = validator.validate_analysis_inputs(
            observations=(
                panel_data.reset_index()
                if isinstance(panel_data.index, pd.MultiIndex)
                else panel_data
            ),
            analysis_type=AnalysisType.PANEL_ANALYSIS,
            hypothesis_tests=hypothesis_tests,
        )

        # Step 3: Block invalid analysis
        if not is_valid:
            self._logger.error(f"Methodology validation failed: {report}")
            raise MethodologyViolation(
                f"Critical validation failures: {report.critical_failures}",
                report=report,
            )

        # Step 4: Log successful validation
        self._logger.info("Methodology validation passed, proceeding with analysis")

        # Step 5: Apply currency-aware clustering if enabled
        clustering_results = None
        if command.tier1_config.get("use_clustering", False):
            await self.orchestrator.update_progress(
                analysis_id, "tier1", 15, "Applying currency-aware clustering..."
            )

            self._logger.info("Applying currency-aware market clustering")
            try:
                # Get clustering configuration
                clustering_config = command.tier1_config.get("clustering_config", {})
                n_clusters_per_zone = clustering_config.get("n_clusters_per_zone", None)

                # Apply clustering
                panel_data, clustering_results = await self._apply_clustering(
                    panel_data, n_clusters_per_zone=n_clusters_per_zone, validate=True
                )

                self._logger.info(
                    f"Clustering complete: {clustering_results.total_clusters} clusters "
                    f"across {len(clustering_results.clusters_by_zone)} zones"
                )

            except Exception as e:
                self._logger.warning(
                    f"Clustering failed: {e}. Proceeding without clustering."
                )
                # Continue without clustering if it fails

        # Check if rainfall data is available
        has_rainfall = "rainfall" in panel_data.columns
        config_with_weather = {**command.tier1_config, "include_weather": has_rainfall}

        # Update independent variables to include cluster if clustering was applied
        independent_vars = self._get_tier1_variables(config_with_weather)
        if clustering_results is not None and "cluster" in panel_data.columns:
            # Add cluster as a control variable
            independent_vars.append("cluster")
            # Also add cluster stability if available
            if "cluster_stability" in panel_data.columns:
                independent_vars.append("cluster_stability")

        spec = ModelSpecification(
            model_type="panel_regression",
            dependent_variable=(
                "log_price" if command.tier1_config.get("log_transform") else "price"
            ),
            independent_variables=independent_vars,
            parameters={
                "entity_var": "market_id",
                "time_var": "date",
                **config_with_weather,
            },
        )

        if command.tier1_config["model"] == "two_way_fixed_effects":
            model = TwoWayFixedEffectsModel(spec)
        elif command.tier1_config["model"] == "interactive_fixed_effects":
            # Add IFE-specific parameters to spec
            ife_params = {
                "n_factors": command.tier1_config.get("n_factors", 3),
                "auto_select_factors": command.tier1_config.get(
                    "auto_select_factors", False
                ),
                "max_factors": command.tier1_config.get("max_factors", 10),
                "standardize": command.tier1_config.get("standardize", True),
            }
            spec.parameters.update(ife_params)
            model = InteractiveFixedEffectsModelWrapper(spec)
        elif command.tier1_config["model"] == "bayesian":
            # Add Bayesian-specific parameters to spec
            from ...core.models.panel import BayesianPanelModelWrapper

            bayes_params = {
                "model_type": command.tier1_config.get(
                    "bayes_model_type", "hierarchical"
                ),
                "robust": command.tier1_config.get("robust", True),
                "structural_breaks": command.tier1_config.get(
                    "structural_breaks", True
                ),
                "zone_heterogeneity": command.tier1_config.get(
                    "zone_heterogeneity", True
                ),
                "n_chains": command.tier1_config.get("n_chains", 4),
                "n_samples": command.tier1_config.get("n_samples", 2000),
                "n_tune": command.tier1_config.get("n_tune", 1000),
                "target_accept": command.tier1_config.get("target_accept", 0.9),
            }
            spec.parameters.update(bayes_params)
            model = BayesianPanelModelWrapper(spec)
        else:
            model = PooledPanelModel(spec)

        await self.orchestrator.update_progress(
            analysis_id, "tier1", 25, "Estimating model..."
        )

        result = await self.estimator_service.estimate(model, panel_data)

        diagnostics = None
        if command.run_diagnostics:
            await self.orchestrator.update_progress(
                analysis_id, "tier1", 75, "Running diagnostics..."
            )

            panel_diagnostics = PanelDiagnosticTests()
            diagnostics = panel_diagnostics.run_all_diagnostics(
                residuals=result.residuals,
                X=result.X,  # Assuming result object has X attribute
                panel_info={
                    "entity_id": spec.parameters["entity_var"],
                    "time_id": spec.parameters["time_var"],
                    "N": panel_data[spec.parameters["entity_var"]].nunique(),
                    "nobs": len(panel_data),
                },
            )

            if command.apply_corrections and self._needs_correction(diagnostics):
                result = await self._apply_corrections_v2(
                    model, panel_data, result, diagnostics
                )

        await self.orchestrator.update_progress(
            analysis_id, "tier1", 100, "Tier 1 complete"
        )

        return {
            "model": model.name,
            "result": result,
            "diagnostics": diagnostics,
            "clustering": clustering_results,
            "metadata": {
                "n_markets": panel_data["market_id"].nunique(),
                "n_commodities": panel_data["commodity"].nunique(),
                "n_observations": len(panel_data),
                "time_span": f"{panel_data['date'].min()} to {panel_data['date'].max()}",
                "clustering_applied": clustering_results is not None,
                "n_clusters": (
                    clustering_results.total_clusters if clustering_results else None
                ),
            },
        }

    async def _load_panel_data(
        self, command: Any  # RunThreeTierAnalysisCommand
    ) -> pd.DataFrame:
        """Load and prepare panel data for Tier 1."""
        from src.core.utils.logging import get_logger

        logger = get_logger(__name__)

        try:
            # Get markets based on filters
            if command.market_ids:
                markets = await self.market_repo.find_by_ids(command.market_ids)
            else:
                markets = await self.market_repo.find_all()

            market_ids = [m.market_id for m in markets]

            # Get price data for the specified time range
            price_records = await self.price_repo.find_by_date_range(
                start_date=command.start_date,
                end_date=command.end_date,
                market_ids=market_ids,
                commodity_codes=command.commodity_codes,
            )

            # Convert to DataFrame
            price_data = []
            for record in price_records:
                price_data.append(
                    {
                        "market_id": str(record.market_id.value),
                        "date": record.date,
                        "price": record.price_per_unit,
                        "price_yer": record.price_per_unit,  # Assuming prices are in YER
                        "commodity": record.commodity_code,
                        "source": record.source,
                        "units": record.unit_of_measure,
                        "quality": record.quality_grade,
                    }
                )

            df = pd.DataFrame(price_data)

            if df.empty:
                raise ValueError("No price data found for the specified criteria")

            # Add log prices (using USD prices for analysis)
            df["log_price"] = np.log(df["price_usd"])
            df["log_price_yer"] = np.log(df["price_yer"])

            # Add market attributes
            market_attrs = {}
            for market in markets:
                market_attrs[str(market.market_id.value)] = {
                    "governorate": market.governorate,
                    "district": market.district,
                    "latitude": market.coordinates.latitude,
                    "longitude": market.coordinates.longitude,
                    "market_type": market.market_type.value,
                    "is_accessible": market.is_accessible,
                }

            # Merge market attributes
            for attr in [
                "governorate",
                "district",
                "latitude",
                "longitude",
                "market_type",
            ]:
                df[attr] = df["market_id"].map(
                    lambda x: market_attrs.get(x, {}).get(attr)
                )

            # Add currency zone classification based on governorate
            # Northern governorates (Houthi-controlled) vs Southern (Government-controlled)
            northern_governorates = [
                "Saada",
                "Hajjah",
                "Amran",
                "Al Mahwit",
                "Sana'a",
                "Sana'a City",
                "Raymah",
                "Dhamar",
                "Ibb",
                "Al Bayda",
                "Al Hudaydah",
            ]
            southern_governorates = [
                "Aden",
                "Abyan",
                "Lahj",
                "Al Dhale'e",
                "Shabwah",
                "Hadramaut",
                "Al Mahrah",
                "Socotra",
                "Taiz",
                "Marib",
                "Al Jawf",
            ]

            def classify_currency_zone(governorate):
                if governorate in northern_governorates:
                    return "HOUTHI"
                elif governorate in southern_governorates:
                    return "GOVERNMENT"
                else:
                    return "CONTESTED"

            df["currency_zone"] = df["governorate"].apply(classify_currency_zone)

            # Add exchange rates based on currency zone and date
            # These are approximate rates - in production, should come from exchange_rate_collector_v2
            def get_exchange_rate(row):
                if row["currency_zone"] == "HOUTHI":
                    return 535.0  # Northern rate
                elif row["currency_zone"] == "GOVERNMENT":
                    return 2000.0  # Southern rate
                else:
                    return 1200.0  # Average for contested areas

            df["exchange_rate_used"] = df.apply(get_exchange_rate, axis=1)
            df["exchange_rate_source"] = (
                "default_zone_rates"  # In production, should be actual source
            )

            # Convert prices to USD
            df["price_usd"] = df["price_yer"] / df["exchange_rate_used"]

            # Calculate derived features
            # Distance to major ports (Aden, Hodeidah)
            aden_coords = (12.7855, 45.0187)
            hodeidah_coords = (14.7979, 42.9545)

            df["distance_to_aden"] = df.apply(
                lambda row: self._haversine_distance(
                    row["latitude"], row["longitude"], aden_coords[0], aden_coords[1]
                ),
                axis=1,
            )

            df["distance_to_hodeidah"] = df.apply(
                lambda row: self._haversine_distance(
                    row["latitude"],
                    row["longitude"],
                    hodeidah_coords[0],
                    hodeidah_coords[1],
                ),
                axis=1,
            )

            df["distance_to_port"] = df[
                ["distance_to_aden", "distance_to_hodeidah"]
            ].min(axis=1)

            # Get conflict data if available
            try:
                from ...core.domain.conflict.repositories import ConflictEventRepository

                conflict_repo = getattr(
                    self.orchestrator, "conflict_repository", lambda: None
                )()

                if conflict_repo:
                    # Aggregate conflict intensity by market and date
                    conflict_data = []
                    for market_id in market_ids:
                        market = next(m for m in markets if m.market_id == market_id)
                        conflicts = (
                            await conflict_repo.find_by_governorate_and_date_range(
                                governorate=market.governorate,
                                start_date=command.start_date,
                                end_date=command.end_date,
                            )
                        )

                        # Group by date and calculate daily intensity
                        for date in pd.date_range(command.start_date, command.end_date):
                            daily_conflicts = [
                                c for c in conflicts if c.date.date() == date.date()
                            ]
                            intensity = (
                                sum(c.fatalities for c in daily_conflicts) / 100.0
                            )  # Normalize
                            conflict_data.append(
                                {
                                    "market_id": str(market_id.value),
                                    "date": date,
                                    "conflict_intensity": min(
                                        intensity, 1.0
                                    ),  # Cap at 1.0
                                }
                            )
                else:
                    logger.warning("Conflict repository not available")
                    conflict_data = []

                if conflict_data:
                    conflict_df = pd.DataFrame(conflict_data)
                    df = df.merge(conflict_df, on=["market_id", "date"], how="left")
                    df["conflict_intensity"] = df["conflict_intensity"].fillna(0.0)
                else:
                    df["conflict_intensity"] = 0.0

            except Exception as e:
                logger.warning(f"Could not load conflict data: {e}")
                df["conflict_intensity"] = 0.0

            # Add weather data if available
            # Check if we have a weather data repository or external service
            weather_data = None
            try:
                # Try to get weather data from external service or repository
                # This could be implemented with a WeatherDataRepository or external API
                from ...infrastructure.external_services.weather_client import (
                    WeatherClient,
                )

                weather_client = WeatherClient()

                # Get weather data for each market location
                weather_records = []
                for market in markets:
                    records = await weather_client.get_historical_weather(
                        latitude=market.coordinates.latitude,
                        longitude=market.coordinates.longitude,
                        start_date=command.start_date,
                        end_date=command.end_date,
                        variable="precipitation",
                    )
                    for record in records:
                        weather_records.append(
                            {
                                "market_id": str(market.market_id.value),
                                "date": record["date"],
                                "rainfall": record["precipitation_mm"],
                            }
                        )

                if weather_records:
                    weather_df = pd.DataFrame(weather_records)
                    df = df.merge(weather_df, on=["market_id", "date"], how="left")

            except ImportError:
                # Weather client not implemented yet
                logger.info(
                    "Weather data client not available, excluding rainfall from analysis"
                )
                weather_data = None
            except Exception as e:
                logger.warning(f"Could not load weather data: {e}")
                weather_data = None

            # If no weather data available, exclude rainfall from the model
            if weather_data is None and "rainfall" not in df.columns:
                # Don't add rainfall variable - it will be excluded from the model
                pass

            # Create interaction terms if requested
            if command.tier1_config.get("interactions", False):
                df["conflict_intensity_x_distance"] = (
                    df["conflict_intensity"] * df["distance_to_port"]
                )
                df["conflict_intensity_x_commodity"] = df.groupby("commodity")[
                    "conflict_intensity"
                ].transform(
                    lambda x: x * (x.mean() + 0.1)  # Commodity-specific conflict effect
                )

            # Set multi-index for panel structure
            df = df.set_index(["market_id", "date"])

            logger.info(
                f"Loaded panel data: {len(df)} observations, "
                f"{df.index.get_level_values(0).nunique()} markets, "
                f"{df.index.get_level_values(1).nunique()} time periods"
            )

            return df

        except Exception as e:
            logger.error(f"Error loading panel data: {e}")
            raise

    def _haversine_distance(
        self, lat1: float, lon1: float, lat2: float, lon2: float
    ) -> float:
        """Calculate distance between two points in kilometers."""
        from math import radians, sin, cos, sqrt, atan2

        R = 6371  # Earth's radius in kilometers

        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        dlat = lat2 - lat1
        dlon = lon2 - lon1

        a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
        c = 2 * atan2(sqrt(a), sqrt(1 - a))

        return R * c

    def _get_tier1_variables(self, config: Dict[str, Any]) -> List[str]:
        """Get independent variables for Tier 1."""
        variables = ["conflict_intensity", "distance_to_port"]

        # Only include rainfall if available in config
        if config.get("include_weather", True):
            variables.append("rainfall")

        if config.get("interactions", False):
            variables.extend(
                ["conflict_intensity_x_distance", "conflict_intensity_x_commodity"]
            )

        return variables

    def _needs_correction(self, diagnostics: Dict[str, Any]) -> bool:
        """Check if model needs correction based on diagnostics."""
        if diagnostics.get("serial_correlation", {}).get("reject_null", False):
            return True

        if diagnostics.get("cross_sectional_dependence", {}).get("reject_null", False):
            return True

        if diagnostics.get("heteroskedasticity", {}).get("reject_null", False):
            return True

        return False

    async def _apply_corrections_v2(
        self, model, data: pd.DataFrame, result: Any, diagnostics: Dict[str, Any]
    ) -> Any:
        """Apply corrections using v2 standard error estimators."""
        se_estimator = StandardErrorEstimator()
        tests = diagnostics.get("tests", {})

        # Extract data components from result
        # Assuming result object has residuals and X (exog) attributes
        residuals = result.residuals
        X = (
            result.X
            if hasattr(result, "X")
            else data[model.specification.independent_variables]
        )

        # Ensure X has a constant if the model was fitted with one
        if "const" in result.params.index and "const" not in X.columns:
            X = sm.add_constant(X, prepend=True)

        entity_ids = (
            data.index.get_level_values(0)
            if isinstance(data.index, pd.MultiIndex)
            else None
        )
        time_ids = (
            data.index.get_level_values(1)
            if isinstance(data.index, pd.MultiIndex)
            else None
        )

        # Determine which correction to apply
        serial_corr = tests.get("wooldridge_serial_correlation", {}).get(
            "reject_null", False
        )
        cross_dep_pesaran = tests.get("pesaran_cd_test", {}).get("reject_null", False)
        cross_dep_breusch = tests.get("breusch_pagan_lm_test", {}).get(
            "reject_null", False
        )
        hetero = tests.get("modified_wald_heteroskedasticity", {}).get(
            "reject_null", False
        )

        # Prioritize Driscoll-Kraay for both serial correlation and cross-sectional dependence
        if (serial_corr or cross_dep_pesaran or cross_dep_breusch) and hetero:
            # Driscoll-Kraay handles both heteroskedasticity and serial/cross-sectional correlation
            dk_results = se_estimator.driscoll_kraay(
                residuals=residuals.values,
                X=X.values,
                entity_ids=entity_ids.values,
                time_ids=time_ids.values,
            )
            result.standard_errors = pd.Series(
                dk_results["standard_errors"], index=result.params.index
            )
            result.se_type = "Driscoll-Kraay"

        elif serial_corr:
            # Apply clustered standard errors (time clustering if appropriate, or entity)
            cluster_results = se_estimator.clustered(
                residuals=residuals.values,
                X=X.values,
                cluster_ids=entity_ids.values,  # Default to entity clustering
            )
            result.standard_errors = pd.Series(
                cluster_results["standard_errors"], index=result.params.index
            )
            result.se_type = "Clustered"

        elif hetero:
            # Apply heteroskedasticity-robust standard errors
            hc_results = se_estimator.heteroskedasticity_robust(
                residuals=residuals.values, X=X.values, hc_type="HC3"
            )
            result.standard_errors = pd.Series(
                hc_results["standard_errors"], index=result.params.index
            )
            result.se_type = "HC3"

        return result

    async def _apply_clustering(
        self,
        panel_data: pd.DataFrame,
        n_clusters_per_zone: Optional[Dict[str, int]] = None,
        validate: bool = True,
    ) -> tuple[pd.DataFrame, CurrencyAwareClusteringResults]:
        """Apply currency-aware clustering to panel data.

        Args:
            panel_data: Panel data with required fields
            n_clusters_per_zone: Optional specification of clusters per zone
            validate: Whether to validate methodology compliance

        Returns:
            Tuple of (panel data with cluster assignments, clustering results)
        """
        # Reset index to access market_id as column
        data_for_clustering = panel_data.reset_index()

        # Get conflict data if available
        conflict_cols = [
            "conflict_intensity",
            "conflict_volatility",
            "control_stability",
        ]
        conflict_data = None
        if all(col in data_for_clustering.columns for col in ["conflict_intensity"]):
            # Aggregate conflict data by market
            conflict_data = (
                data_for_clustering.groupby("market_id")
                .agg({"conflict_intensity": ["mean", "std"], "market_id": "first"})
                .reset_index(drop=True)
            )
            conflict_data.columns = ["event_count", "conflict_volatility", "market_id"]
            conflict_data["control_change"] = 0.0  # Placeholder for control changes

        # Get geographic data if available
        geographic_cols = [
            "latitude",
            "longitude",
            "distance_to_port",
            "distance_to_aden",
            "distance_to_hodeidah",
        ]
        geographic_data = None
        if all(col in data_for_clustering.columns for col in ["latitude", "longitude"]):
            # Create geographic features DataFrame
            market_geo = data_for_clustering.groupby("market_id").first()[
                [col for col in geographic_cols if col in data_for_clustering.columns]
            ]

            # Add additional geographic features
            market_geo["distance_to_capital"] = market_geo.get(
                "distance_to_aden", 100.0
            )
            market_geo["distance_to_border"] = 50.0  # Placeholder
            market_geo["urban_rural"] = 0.5  # Placeholder
            market_geo["road_quality"] = 0.5  # Placeholder

            geographic_data = market_geo

        # Apply clustering
        panel_with_clusters, clustering_results = apply_currency_aware_clustering(
            panel_data=data_for_clustering,
            conflict_data=conflict_data,
            geographic_data=geographic_data,
            n_clusters_per_zone=n_clusters_per_zone,
            validate=validate,
        )

        # Merge cluster assignments back to original panel structure
        cluster_cols = ["cluster", "cluster_size", "cluster_stability"]
        existing_cols = [
            col for col in cluster_cols if col in panel_with_clusters.columns
        ]

        # Get unique cluster assignments per market
        cluster_assignments = panel_with_clusters.groupby("market_id")[
            existing_cols
        ].first()

        # Merge back to original panel data
        panel_data_with_clusters = panel_data.copy()
        for col in existing_cols:
            panel_data_with_clusters[col] = (
                panel_data_with_clusters.index.get_level_values(0).map(
                    cluster_assignments[col].to_dict()
                )
            )

        self._logger.info(
            f"Added cluster assignments to panel data: "
            f"{clustering_results.total_clusters} clusters created"
        )

        return panel_data_with_clusters, clustering_results
