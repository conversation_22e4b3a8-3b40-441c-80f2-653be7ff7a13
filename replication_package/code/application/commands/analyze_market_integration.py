"""Command for analyzing market integration."""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4

from ...core.domain.market.repositories import MarketRepository, PriceRepository
from ...core.domain.market.services import MarketIntegrationService
from ...core.domain.market.value_objects import MarketId
from ..interfaces import EventBus, UnitOfWork
from ..services.analysis_orchestrator import AnalysisOrchestrator


@dataclass
class AnalyzeMarketIntegrationCommand:
    """Command to analyze market integration."""
    
    start_date: datetime
    end_date: datetime
    market_ids: List[str]
    commodity_ids: List[str]
    analysis_config: Dict[str, Any]
    user_id: str
    
    def validate(self) -> None:
        """Validate command parameters."""
        if self.start_date >= self.end_date:
            raise ValueError("Start date must be before end date")
        if not self.market_ids:
            raise ValueError("At least one market must be specified")
        if not self.commodity_ids:
            raise ValueError("At least one commodity must be specified")
        if (self.end_date - self.start_date).days < 30:
            raise ValueError("Analysis period must be at least 30 days")


@dataclass
class AnalyzeMarketIntegrationResult:
    """Result of market integration analysis command."""
    
    analysis_id: UUID
    status: str
    message: str
    estimated_completion_time: Optional[datetime] = None


class AnalyzeMarketIntegrationHandler:
    """Handler for market integration analysis command."""
    
    def __init__(
        self,
        uow: UnitOfWork,
        market_repo: MarketRepository,
        price_repo: PriceRepository,
        integration_service: MarketIntegrationService,
        orchestrator: AnalysisOrchestrator,
        event_bus: EventBus
    ):
        """Initialize handler with dependencies."""
        self.uow = uow
        self.market_repo = market_repo
        self.price_repo = price_repo
        self.integration_service = integration_service
        self.orchestrator = orchestrator
        self.event_bus = event_bus
    
    async def handle(self, command: AnalyzeMarketIntegrationCommand) -> AnalyzeMarketIntegrationResult:
        """Execute market integration analysis."""
        # Validate command
        command.validate()
        
        # Generate analysis ID
        analysis_id = uuid4()
        
        async with self.uow:
            # Validate markets exist
            market_ids = [MarketId(mid) for mid in command.market_ids]
            markets = await self.market_repo.find_by_ids(market_ids)
            
            if len(markets) != len(command.market_ids):
                found_ids = {m.market_id.value for m in markets}
                missing_ids = set(command.market_ids) - found_ids
                raise ValueError(f"Markets not found: {missing_ids}")
            
            # Publish analysis started event
            await self.event_bus.publish(
                AnalysisStartedEvent(
                    aggregate_id=analysis_id,
                    user_id=command.user_id,
                    market_count=len(markets),
                    commodity_count=len(command.commodity_ids),
                    start_date=command.start_date,
                    end_date=command.end_date
                )
            )
            
            # Start analysis asynchronously
            await self.orchestrator.start_analysis(
                analysis_id=analysis_id,
                markets=markets,
                commodity_ids=command.commodity_ids,
                start_date=command.start_date,
                end_date=command.end_date,
                config=command.analysis_config
            )
            
            # Estimate completion time (rough estimate: 1 minute per market-commodity pair)
            total_pairs = len(markets) * len(command.commodity_ids)
            estimated_minutes = max(1, total_pairs // 10)  # Parallel processing
            estimated_completion = datetime.utcnow() + timedelta(minutes=estimated_minutes)
            
            await self.uow.commit()
        
        return AnalyzeMarketIntegrationResult(
            analysis_id=analysis_id,
            status="started",
            message=f"Analysis started for {len(markets)} markets and {len(command.commodity_ids)} commodities",
            estimated_completion_time=estimated_completion
        )


# Domain Events
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Optional

from ...core.domain.shared.events import DomainEvent


class AnalysisStartedEvent(DomainEvent):
    """Event raised when analysis is started."""
    
    def __init__(self, user_id: str, market_count: int, commodity_count: int, 
                 start_date: datetime, end_date: datetime, **kwargs):
        """Initialize analysis started event."""
        super().__init__(**kwargs)
        self.user_id = user_id
        self.market_count = market_count
        self.commodity_count = commodity_count
        self.start_date = start_date
        self.end_date = end_date


class AnalysisCompletedEvent(DomainEvent):
    """Event raised when analysis is completed."""
    
    def __init__(self, duration_seconds: float, results_location: str, 
                 success: bool, error_message: Optional[str] = None, **kwargs):
        """Initialize analysis completed event."""
        super().__init__(**kwargs)
        self.duration_seconds = duration_seconds
        self.results_location = results_location
        self.success = success
        self.error_message = error_message