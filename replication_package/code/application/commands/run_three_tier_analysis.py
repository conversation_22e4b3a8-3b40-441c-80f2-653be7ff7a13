"""Command to run the complete three-tier econometric analysis."""

from dataclasses import dataclass, field # Added field
from datetime import datetime
from typing import Any, Dict, List, Optional

from ...core.domain.market.repositories import MarketRepository, PriceRepository
from ...core.models import (
    PooledPanelModel,
    TwoWayFixedEffectsModel,
    ThresholdVECMModel,
)
from ...core.models.interfaces import ModelSpecification
from ...core.models.validation import (
    ConflictValidationModel,
    FactorModel,
    PCAModel,
    CrossValidationModel
)
from ...infrastructure.diagnostics import PanelDiagnosticTests, TimeSeriesDiagnosticTests
from ...infrastructure.estimators.standard_errors import StandardErrorEstimator
from ..interfaces import Command, CommandHandler
from ..services import AnalysisOrchestrator, ModelEstimatorService
from ..analysis_tiers.tier1_runner import Tier1Runner
from ..analysis_tiers.tier2_runner import Tier2Runner
from ..analysis_tiers.tier3_runner import Tier3Runner


@dataclass
class RunThreeTierAnalysisCommand(Command):
    """Command to execute three-tier econometric analysis."""
    
    start_date: datetime
    end_date: datetime
    market_ids: Optional[List[str]] = None  # None means all markets
    commodity_ids: Optional[List[str]] = None  # None means all commodities
    
    # Tier-specific configurations
    tier1_config: Dict[str, Any] = field(default_factory=dict)
    tier2_config: Dict[str, Any] = field(default_factory=dict)
    tier3_config: Dict[str, Any] = field(default_factory=dict)
    
    # Analysis options
    run_diagnostics: bool = True
    apply_corrections: bool = True
    save_intermediate: bool = True
    
    def __post_init__(self):
        """Initialize default configurations."""
        if not self.tier1_config:
            self.tier1_config = {
                "model": "two_way_fixed_effects",
                "se_type": "driscoll_kraay",
                "entity_trends": False,
                "log_transform": True
            }
        
        if not self.tier2_config:
            self.tier2_config = {
                "model": "threshold_vecm",
                "min_obs": 100,
                "n_regimes": 2,
                "threshold_variable": "conflict_intensity"
            }
        
        if not self.tier3_config:
            self.tier3_config = {
                "validation_methods": ["cross_validation", "structural_break"],
                "factor_analysis": True,
                "spatial_analysis": True,
                "dynamic_factor_analysis": True # Enable dynamic factor analysis by default
            }


class RunThreeTierAnalysisHandler(CommandHandler):
    """Handler for three-tier analysis command."""
    
    def __init__(
        self,
        market_repo: MarketRepository,
        price_repo: PriceRepository,
        orchestrator: AnalysisOrchestrator,
        estimator_service: ModelEstimatorService
    ):
        """Initialize handler with dependencies."""
        from ..services.three_tier_analysis_service import ThreeTierAnalysisService
        
        self.market_repo = market_repo
        self.price_repo = price_repo
        self.orchestrator = orchestrator
        self.estimator_service = estimator_service
        
        # Initialize the three-tier analysis service
        self.analysis_service = ThreeTierAnalysisService(
            market_repository=market_repo,
            price_repository=price_repo,
            estimator_service=estimator_service,
            orchestrator=orchestrator
        )
    
    async def handle(self, command: RunThreeTierAnalysisCommand) -> str:
        """Execute three-tier analysis."""
        
        # Create analysis configuration
        config = {
            'tier1': command.tier1_config,
            'tier2': command.tier2_config,
            'tier3': command.tier3_config,
            'run_diagnostics': command.run_diagnostics,
            'apply_corrections': command.apply_corrections,
            'save_intermediate': command.save_intermediate
        }
        
        try:
            # Run the complete three-tier analysis
            results = await self.analysis_service.run_analysis(
                start_date=command.start_date,
                end_date=command.end_date,
                market_ids=command.market_ids,
                commodity_codes=command.commodity_codes,
                config=config
            )
            
            return results.get('analysis_id', 'unknown')
            
        except Exception as e:
            # Log error and re-raise
            from src.core.utils.logging import get_logger
            logger = get_logger(__name__)
            logger.error(f"Three-tier analysis failed: {str(e)}")
            raise
