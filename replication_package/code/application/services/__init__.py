"""Application services for orchestrating domain logic."""

# Import only services that actually exist
try:
    from .analysis_orchestrator import AnalysisOrchestrator
except ImportError:
    AnalysisOrchestrator = None

try:
    from .data_preparation_service import DataPreparationService
except ImportError:
    DataPreparationService = None

try:
    from .model_estimator_service import ModelEstimatorService
except ImportError:
    ModelEstimatorService = None

try:
    from .policy_data_adapter import PolicyDataAdapter
except ImportError:
    PolicyDataAdapter = None

try:
    from .policy_orchestrator import PolicyOrchestrator
except ImportError:
    PolicyOrchestrator = None

try:
    from .three_tier_analysis_service import ThreeTierAnalysisService
except ImportError:
    ThreeTierAnalysisService = None

try:
    from .hypothesis_testing_service import HypothesisTestingService
except ImportError:
    HypothesisTestingService = None

# Only export services that were successfully imported
__all__ = [
    name for name, obj in [
        ("AnalysisOrchestrator", AnalysisOrchestrator),
        ("DataPreparationService", DataPreparationService),
        ("ModelEstimatorService", ModelEstimatorService),
        ("PolicyDataAdapter", PolicyDataAdapter),
        ("PolicyOrchestrator", PolicyOrchestrator),
        ("ThreeTierAnalysisService", ThreeTierAnalysisService),
        ("HypothesisTestingService", HypothesisTestingService),
    ]
    if obj is not None
]