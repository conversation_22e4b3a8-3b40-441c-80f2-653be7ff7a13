"""Service for running hypothesis tests through the API."""

import json
import uuid
from datetime import datetime, date
from typing import Dict, List, Optional, Any
from pathlib import Path

from ...core.models.hypothesis_testing import (
    HypothesisRegistry,
    HypothesisTest,
    TestData,
    TestResults,
    PolicyInterpretation,
    HypothesisOutcome
)
from src.core.utils.logging import get_logger
from ...infrastructure.persistence.repositories import PanelDataRepository
from ...infrastructure.caching import MemoryCache

logger = get_logger(__name__)


class HypothesisTestingService:
    """Service for managing and running hypothesis tests."""
    
    def __init__(
        self,
        panel_repository: Optional[PanelDataRepository] = None,
        cache: Optional[MemoryCache] = None,
        results_path: str = "data/hypothesis_results"
    ):
        """Initialize hypothesis testing service."""
        self.registry = HypothesisRegistry()
        self.panel_repository = panel_repository
        self.cache = cache or MemoryCache(max_size=1000, default_ttl=3600)
        self.results_path = Path(results_path)
        self.results_path.mkdir(parents=True, exist_ok=True)
        
        # In-memory test tracking (in production, use database)
        self._test_status = {}
        self._test_results = {}
    
    async def run_hypothesis_test(
        self,
        hypothesis_id: str,
        start_date: date,
        end_date: date,
        markets: Optional[List[str]] = None,
        commodities: Optional[List[str]] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> TestResults:
        """Run a specific hypothesis test."""
        logger.info(f"Running hypothesis test {hypothesis_id}")
        
        # Get test from registry
        test = self.registry.get(hypothesis_id)
        if not test:
            raise ValueError(f"Hypothesis {hypothesis_id} not found")
        
        # Get panel data
        panel_data = await self._get_panel_data(
            start_date, end_date, markets, commodities
        )
        
        # Prepare test data based on requirements
        test_data = await self._prepare_test_data(
            test, panel_data, start_date, end_date, config
        )
        
        # Run the test
        results = test.run_test(test_data)
        
        # Get policy interpretation
        interpretation = test.interpret_results(results)
        
        # Enhance results with interpretation
        results.policy_interpretation = interpretation
        
        logger.info(f"Hypothesis test {hypothesis_id} completed: {results.outcome.value}")
        
        return results
    
    async def get_test_status(self, test_id: str) -> Optional[Dict[str, Any]]:
        """Get the status of a test."""
        return self._test_status.get(test_id)
    
    async def update_test_status(
        self,
        test_id: str,
        status: str,
        hypothesis_id: Optional[str] = None,
        batch_id: Optional[str] = None,
        outcome: Optional[str] = None,
        progress: Optional[int] = None,
        error: Optional[str] = None,
        message: Optional[str] = None
    ):
        """Update test status."""
        if test_id not in self._test_status:
            self._test_status[test_id] = {
                "test_id": test_id,
                "created_at": datetime.utcnow(),
                "progress": 0
            }
        
        self._test_status[test_id].update({
            "status": status,
            "updated_at": datetime.utcnow()
        })
        
        if hypothesis_id:
            self._test_status[test_id]["hypothesis_id"] = hypothesis_id
        if batch_id:
            self._test_status[test_id]["batch_id"] = batch_id
        if outcome:
            self._test_status[test_id]["outcome"] = outcome
        if progress is not None:
            self._test_status[test_id]["progress"] = progress
        if error:
            self._test_status[test_id]["error"] = error
        if message:
            self._test_status[test_id]["message"] = message
        
        # Cache status
        cache_key = f"test_status:{test_id}"
        await self.cache.set(cache_key, self._test_status[test_id], ttl=3600)
    
    async def save_test_results(self, test_id: str, results: TestResults):
        """Save test results."""
        # Convert results to dictionary
        results_dict = {
            "test_id": test_id,
            "hypothesis_id": results.hypothesis_id,
            "outcome": results.outcome.value,
            "test_statistic": results.test_statistic,
            "p_value": results.p_value,
            "confidence_level": results.confidence_level,
            "effect_size": results.effect_size,
            "confidence_interval": results.confidence_interval,
            "diagnostic_tests": results.diagnostic_tests,
            "robustness_checks": results.robustness_checks,
            "detailed_results": results.detailed_results,
            "completed_at": datetime.utcnow().isoformat()
        }
        
        # Add policy interpretation if available
        if hasattr(results, 'policy_interpretation'):
            interpretation = results.policy_interpretation
            results_dict["policy_interpretation"] = {
                "summary": interpretation.summary,
                "implications": interpretation.implications,
                "recommendations": interpretation.recommendations,
                "confidence_statement": interpretation.confidence_statement,
                "caveats": interpretation.caveats,
                "visualizations": interpretation.visualizations
            }
        
        # Store in memory
        self._test_results[test_id] = results_dict
        
        # Save to file
        results_file = self.results_path / f"{test_id}.json"
        with open(results_file, 'w') as f:
            json.dump(results_dict, f, indent=2, default=str)
        
        # Cache results
        cache_key = f"test_results:{test_id}"
        await self.cache.set(cache_key, results_dict, ttl=86400)  # 24 hours
        
        logger.info(f"Saved results for test {test_id}")
    
    async def get_test_results(self, test_id: str) -> Optional[Dict[str, Any]]:
        """Get test results."""
        # Check cache first
        cache_key = f"test_results:{test_id}"
        cached = await self.cache.get(cache_key)
        if cached:
            return cached
        
        # Check memory
        if test_id in self._test_results:
            return self._test_results[test_id]
        
        # Check file
        results_file = self.results_path / f"{test_id}.json"
        if results_file.exists():
            with open(results_file, 'r') as f:
                results = json.load(f)
                # Cache for future requests
                await self.cache.set(cache_key, results, ttl=86400)
                return results
        
        return None
    
    async def _get_panel_data(
        self,
        start_date: date,
        end_date: date,
        markets: Optional[List[str]] = None,
        commodities: Optional[List[str]] = None
    ) -> Any:
        """Get panel data for the specified period and filters."""
        # In a real implementation, this would query the database
        # For now, create synthetic data
        import pandas as pd
        import numpy as np
        
        # Create date range
        dates = pd.date_range(start_date, end_date, freq='W')
        
        # Default markets and commodities
        if not markets:
            markets = [
                "Sana'a", "Aden", "Taiz", "Al Hudaydah", "Ibb",
                "Dhamar", "Mukalla", "Sa'ada", "Hajjah", "Amran"
            ]
        
        if not commodities:
            commodities = [
                "wheat_flour", "rice", "sugar", "oil", "beans",
                "lentils", "eggs", "tomatoes", "onions", "salt"
            ]
        
        # Create synthetic panel data
        data = []
        for date in dates:
            for market in markets:
                for commodity in commodities:
                    # Determine currency zone
                    if market in ["Sana'a", "Sa'ada", "Hajjah", "Amran", "Dhamar"]:
                        zone = 'houthi'
                        base_price = 1000
                        exchange_rate = 535
                    elif market in ["Aden", "Mukalla"]:
                        zone = 'government'
                        base_price = 1500
                        exchange_rate = 2000
                    else:
                        zone = 'contested'
                        base_price = 1200
                        exchange_rate = 1200
                    
                    # Add variation
                    price_yer = base_price * (1 + np.random.normal(0, 0.1))
                    
                    data.append({
                        'date': date,
                        'market': market,
                        'commodity': commodity,
                        'price': price_yer,
                        'price_yer': price_yer,
                        'currency_zone': zone,
                        'exchange_rate': exchange_rate
                    })
        
        df = pd.DataFrame(data)
        return df
    
    async def _prepare_test_data(
        self,
        test: HypothesisTest,
        panel_data: Any,
        start_date: date,
        end_date: date,
        config: Optional[Dict[str, Any]] = None
    ) -> TestData:
        """Prepare test data based on hypothesis requirements."""
        # For now, just wrap the panel data
        # In a real implementation, this would fetch additional data as needed
        test_data = TestData(
            panel_data=panel_data,
            exchange_rates=None,  # Would fetch if needed
            conflict_events=None,  # Would fetch if needed
            aid_distribution=None,  # Would fetch if needed
            metadata={
                "start_date": start_date,
                "end_date": end_date,
                "config": config or {}
            }
        )
        
        # Prepare data using test's own method
        return test.prepare_data(panel_data)