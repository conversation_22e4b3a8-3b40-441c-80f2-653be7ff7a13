"""Service for estimating econometric models."""

import time
from typing import Any, Dict, Type

import numpy as np
import pandas as pd

from ...core.models.interfaces import (
    Diagnostic<PERSON><PERSON>ult,
    Estimator,
    EstimationResult,
    Model,
)
from ...core.models.panel import (
    PooledPanelModel,
    FixedEffectsModel,
    TwoWayFixedEffectsModel,
    InteractiveFixedEffectsModelWrapper,
    BayesianPanelModelWrapper,
)
from ...core.models.time_series import ThresholdVECMModel, VECMModel
from ...core.models.validation import (
    PCAModel,
    ConflictValidationModel,
    FactorModel,
    CrossValidationModel,
    DynamicFactorModel,
)  # Import new models

from ...infrastructure.estimators.implementations.panel_estimators import (
    PooledPanelEstimator,
    FixedEffectsEstimator,
    TwoWayFixedEffectsEstimator,
    VECMEstimator,
    ThresholdVECMEstimator,
)
from ...infrastructure.estimators.implementations.interactive_fixed_effects_estimator import (
    InteractiveFixedEffectsEstimator,
)
from ...infrastructure.estimators.implementations.bayesian_panel_estimator import (
    BayesianPanelEstimator,
)
from ...infrastructure.estimators.validation.pca_analyzer import PCAAnalyzer
from ...infrastructure.estimators.validation.conflict_validator import ConflictValidator
from ...infrastructure.estimators.validation.factor_analyzer import FactorAnalyzer
from ...infrastructure.estimators.validation.cross_validator import CrossValidator
from ...infrastructure.estimators.validation.dynamic_factor_analyzer import (
    DynamicFactorAnalyzer,
)


class ModelEstimatorService:
    """Service that handles model estimation using appropriate estimators."""

    def __init__(self):
        """Initialize with estimator registry."""
        self._estimators: Dict[Type[Model], Estimator] = {
            PooledPanelModel: PooledPanelEstimator(),
            FixedEffectsModel: FixedEffectsEstimator(),
            TwoWayFixedEffectsModel: TwoWayFixedEffectsEstimator(),
            InteractiveFixedEffectsModelWrapper: InteractiveFixedEffectsEstimator(),
            BayesianPanelModelWrapper: BayesianPanelEstimator(),
            VECMModel: VECMEstimator(),
            ThresholdVECMModel: ThresholdVECMEstimator(),
            PCAModel: PCAAnalyzer(),
            ConflictValidationModel: ConflictValidator(),
            FactorModel: FactorAnalyzer(),
            CrossValidationModel: CrossValidator(),
            DynamicFactorModel: DynamicFactorAnalyzer(),
        }

    async def estimate(self, model: Model, data: pd.DataFrame) -> EstimationResult:
        """Estimate model parameters."""
        # Validate data
        errors = model.validate_data(data)
        if errors:
            raise ValueError(f"Data validation failed: {errors}")

        # Prepare data
        prepared_data = model.prepare_data(data)

        # Get appropriate estimator
        estimator = self._get_estimator(model)

        # Estimate model
        result = estimator.estimate(model, prepared_data)

        # Store results in model
        model._is_fitted = True
        model._results = result

        return result

    async def diagnose(
        self, model: Model, result: EstimationResult
    ) -> Dict[str, DiagnosticResult]:
        """Run diagnostic tests on estimation results."""
        if not model.is_fitted:
            raise ValueError("Model must be fitted before running diagnostics")

        estimator = self._get_estimator(model)
        return estimator.diagnose(model, result)

    async def predict(self, model: Model, new_data: pd.DataFrame) -> pd.Series:
        """Generate predictions using fitted model."""
        if not model.is_fitted:
            raise ValueError("Model must be fitted before prediction")

        # Prepare new data
        prepared_data = model.prepare_data(new_data)

        estimator = self._get_estimator(model)
        return estimator.predict(model, model.results, prepared_data)

    def _get_estimator(self, model: Model) -> Estimator:
        """Get estimator for model type."""
        model_type = type(model)
        if model_type not in self._estimators:
            raise ValueError(f"No estimator registered for {model_type}")
        return self._estimators[model_type]
