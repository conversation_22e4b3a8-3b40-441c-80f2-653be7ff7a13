"""Orchestrates complex analysis workflows."""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4

from ...core.domain.market.repositories import MarketRepository, PriceRepository
from ...infrastructure.caching import MemoryCache, RedisCache
from ...infrastructure.messaging import AsyncEventBus, InMemoryEventBus
from src.core.utils.logging import get_logger
from .model_estimator_service import ModelEstimatorService
from .helpers.analysis_helpers import (
    load_panel_data,
    load_commodity_data,
    get_all_commodities,
    get_tier1_variables,
    needs_correction,
    extract_residuals_for_factor_analysis
)

logger = get_logger(__name__)


class AnalysisOrchestrator:
    """Orchestrates complex analysis workflows, managing state and progress."""
    
    def __init__(
        self,
        market_repo: MarketRepository,
        price_repo: PriceRepository,
        event_bus: Any,  # Can be InMemoryEventBus or AsyncEventBus
        cache: Any,  # Can be MemoryCache or RedisCache
        estimator_service: ModelEstimatorService
    ):
        """Initialize orchestrator with dependencies."""
        self.market_repo = market_repo
        self.price_repo = price_repo
        self.event_bus = event_bus
        self.cache = cache
        self.estimator_service = estimator_service
        # Use cache for job storage instead of in-memory dict
        self._job_prefix = "analysis_job:"
        self._job_list_key = "analysis_jobs:all"
    
    async def start_analysis(self, analysis_type: str, parameters: Dict[str, Any]) -> str:
        """Start a new analysis job."""
        job_id = str(uuid4())
        job_data = {
            "id": job_id,
            "type": analysis_type,
            "status": "running",
            "progress": 0,
            "start_time": datetime.utcnow().isoformat(),
            "parameters": parameters,
            "results": None,
            "error": None,
            "tiers_progress": {
                "tier1": {"progress": 0, "status": "pending", "message": ""},
                "tier2": {"progress": 0, "status": "pending", "message": ""},
                "tier3": {"progress": 0, "status": "pending", "message": ""},
            }
        }
        
        # Store in cache with TTL of 24 hours
        await self.cache.set(
            f"{self._job_prefix}{job_id}", 
            job_data,
            ttl=86400  # 24 hours
        )
        
        # Add to job list
        job_list = await self.cache.get(self._job_list_key) or []
        job_list.append(job_id)
        await self.cache.set(self._job_list_key, job_list, ttl=86400)
        
        logger.info(f"Analysis job {job_id} started: {analysis_type}")
        await self.event_bus.publish(
            {"event_type": "AnalysisStarted", "job_id": job_id, "parameters": parameters}
        )
        return job_id
    
    async def update_progress(self, job_id: str, tier: str, progress: int, message: str) -> None:
        """Update progress for a specific tier of an analysis job."""
        job_key = f"{self._job_prefix}{job_id}"
        job = await self.cache.get(job_key)
        
        if not job:
            logger.warning(f"Attempted to update progress for non-existent job: {job_id}")
            return
        
        job["tiers_progress"][tier]["progress"] = progress
        job["tiers_progress"][tier]["message"] = message
        job["tiers_progress"][tier]["status"] = "running" if progress < 100 else "completed"
        
        # Calculate overall progress (simple average for now)
        total_progress = sum(t["progress"] for t in job["tiers_progress"].values())
        job["progress"] = int(total_progress / len(job["tiers_progress"]))
        
        # Update cache
        await self.cache.set(job_key, job, ttl=86400)
        
        logger.info(f"Job {job_id} - Tier {tier}: {progress}% - {message}")
        await self.event_bus.publish(
            {
                "event_type": "AnalysisProgress",
                "job_id": job_id,
                "tier": tier,
                "progress": progress,
                "message": message,
                "overall_progress": job["progress"],
            }
        )
    
    async def complete_analysis(self, job_id: str, results: Dict[str, Any]) -> None:
        """Mark an analysis job as complete."""
        job_key = f"{self._job_prefix}{job_id}"
        job = await self.cache.get(job_key)
        
        if not job:
            logger.warning(f"Attempted to complete non-existent job: {job_id}")
            return
        
        job["status"] = "completed"
        job["progress"] = 100
        job["end_time"] = datetime.utcnow().isoformat()
        job["results"] = results
        
        # Update cache with extended TTL for completed jobs
        await self.cache.set(job_key, job, ttl=604800)  # 7 days for completed jobs
        
        logger.info(f"Analysis job {job_id} completed.")
        await self.event_bus.publish(
            {"event_type": "AnalysisCompleted", "job_id": job_id, "results": results}
        )
    
    async def fail_analysis(self, job_id: str, error_message: str) -> None:
        """Mark an analysis job as failed."""
        job_key = f"{self._job_prefix}{job_id}"
        job = await self.cache.get(job_key)
        
        if not job:
            logger.warning(f"Attempted to fail non-existent job: {job_id}")
            return
        
        job["status"] = "failed"
        job["error"] = error_message
        job["end_time"] = datetime.utcnow().isoformat()
        
        # Update cache
        await self.cache.set(job_key, job, ttl=86400)  # Keep failed jobs for 24 hours
        
        logger.error(f"Analysis job {job_id} failed: {error_message}")
        await self.event_bus.publish(
            {"event_type": "AnalysisFailed", "job_id": job_id, "error": error_message}
        )
    
    async def get_analysis_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get the current status of an analysis job."""
        job_key = f"{self._job_prefix}{job_id}"
        job = await self.cache.get(job_key)
        
        if job and isinstance(job["start_time"], str):
            # Convert ISO strings back to datetime for compatibility
            job["start_time"] = datetime.fromisoformat(job["start_time"])
            if job.get("end_time") and isinstance(job["end_time"], str):
                job["end_time"] = datetime.fromisoformat(job["end_time"])
        
        return job
    
    async def get_all_analysis_statuses(self) -> List[Dict[str, Any]]:
        """Get statuses for all active and completed analysis jobs."""
        job_list = await self.cache.get(self._job_list_key) or []
        
        statuses = []
        for job_id in job_list:
            status = await self.get_analysis_status(job_id)
            if status:  # Skip if job has expired from cache
                statuses.append(status)
        
        # Sort by start time, most recent first
        statuses.sort(key=lambda x: x["start_time"], reverse=True)
        
        return statuses
