"""Three-Tier Analysis Service.

Orchestrates the complete three-tier econometric analysis using V1 adapters,
ensuring results match the validated 35% conflict effect findings.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple
from uuid import uuid4
import asyncio

from ...core.domain.market.entities import PanelData
from ...core.domain.market.repositories import MarketRepository, PriceRepository
from ...core.domain.shared.events import DomainEvent
from ...infrastructure.messaging import EventBus
from src.core.utils.logging import get_logger
from .analysis_orchestrator import AnalysisOrchestrator
from .model_estimator_service import ModelEstimatorService
from ..analysis_tiers.tier1_runner import Tier1Runner
from ..analysis_tiers.tier2_runner import Tier2Runner
from ..analysis_tiers.tier3_runner import Tier3Runner

logger = get_logger(__name__)


class AnalysisStartedEvent(DomainEvent):
    """Event emitted when analysis starts."""
    
    def __init__(self, analysis_id: str, analysis_type: str, parameters: Dict[str, Any]):
        super().__init__()
        self.analysis_id = analysis_id
        self.analysis_type = analysis_type
        self.parameters = parameters


class AnalysisProgressEvent(DomainEvent):
    """Event emitted for analysis progress updates."""
    
    def __init__(self, analysis_id: str, tier: str, progress: int, message: str):
        super().__init__()
        self.analysis_id = analysis_id
        self.tier = tier
        self.progress = progress
        self.message = message


class AnalysisCompletedEvent(DomainEvent):
    """Event emitted when analysis completes."""
    
    def __init__(self, analysis_id: str, results: Dict[str, Any]):
        super().__init__()
        self.analysis_id = analysis_id
        self.results = results


class AnalysisFailedEvent(DomainEvent):
    """Event emitted when analysis fails."""
    
    def __init__(self, analysis_id: str, error: str):
        super().__init__()
        self.analysis_id = analysis_id
        self.error = error


class ThreeTierAnalysisService:
    """Orchestrate three-tier econometric analysis.
    
    This service coordinates the execution of:
    1. Tier 1: Pooled panel analysis with fixed effects
    2. Tier 2: Commodity-specific threshold models
    3. Tier 3: Validation through factor analysis
    
    It ensures results match V1 findings, particularly the 35% conflict effect.
    """
    
    def __init__(self,
                 market_repository: MarketRepository,
                 price_repository: PriceRepository,
                 estimator_service: ModelEstimatorService,
                 event_bus: Optional[EventBus] = None,
                 orchestrator: Optional[AnalysisOrchestrator] = None):
        """Initialize service with adapters and dependencies.
        
        Parameters
        ----------
        market_repository : MarketRepository
            Repository for market data
        price_repository : PriceRepository
            Repository for price data
        estimator_service : ModelEstimatorService
            Service for model estimation
        event_bus : EventBus, optional
            Event bus for publishing analysis events
        orchestrator : AnalysisOrchestrator, optional
            Orchestrator for managing analysis state
        """
        self.market_repository = market_repository
        self.price_repository = price_repository
        self.estimator_service = estimator_service
        self.event_bus = event_bus
        self.orchestrator = orchestrator
        
        # Initialize tier runners
        self.tier1_runner = Tier1Runner(
            market_repository, price_repository, orchestrator, estimator_service
        )
        self.tier2_runner = Tier2Runner(
            market_repository, price_repository, orchestrator, estimator_service
        )
        self.tier3_runner = Tier3Runner(
            market_repository, price_repository, orchestrator, estimator_service
        )
        
        # Analysis configuration
        self.config = {
            'validate_conflict_effect': True,
            'expected_conflict_effect': 0.35,
            'run_diagnostics': True,
            'apply_corrections': True,
            'parallel_commodity_analysis': False  # Can be enabled for performance
        }
        
        logger.info("Initialized ThreeTierAnalysisService")
    
    async def run_analysis(self,
                          start_date: datetime,
                          end_date: datetime,
                          market_ids: Optional[List[str]] = None,
                          commodity_codes: Optional[List[str]] = None,
                          config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Execute complete three-tier analysis.
        
        Parameters
        ----------
        start_date : datetime
            Analysis start date
        end_date : datetime
            Analysis end date
        market_ids : List[str], optional
            Market IDs to include (None means all markets)
        commodity_codes : List[str], optional
            Commodity codes to include (None means all commodities)
        config : dict, optional
            Analysis configuration overrides
            
        Returns
        -------
        dict
            Complete analysis results including all tiers
        """
        # Generate analysis ID
        analysis_id = str(uuid4())
        
        # Merge configuration
        analysis_config = {**self.config, **(config or {})}
        
        # Create command object for tier runners
        from ..commands.run_three_tier_analysis import RunThreeTierAnalysisCommand
        command = RunThreeTierAnalysisCommand(
            start_date=start_date,
            end_date=end_date,
            market_ids=market_ids,
            commodity_codes=commodity_codes,
            tier1_config=analysis_config.get('tier1', {}),
            tier2_config=analysis_config.get('tier2', {}),
            tier3_config=analysis_config.get('tier3', {})
        )
        
        # Start analysis tracking
        if self.orchestrator:
            job_id = await self.orchestrator.start_analysis(
                'three_tier_analysis',
                {
                    'analysis_id': analysis_id,
                    'config': analysis_config,
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'market_ids': market_ids,
                    'commodity_codes': commodity_codes
                }
            )
        else:
            job_id = analysis_id
        
        # Emit start event
        await self._emit_event(
            AnalysisStartedEvent(
                analysis_id=analysis_id,
                analysis_type='three_tier',
                parameters=analysis_config
            )
        )
        
        try:
            logger.info(f"Starting three-tier analysis {analysis_id}")
            
            # Run Tier 1
            await self._update_progress(job_id, 'tier1', 0, "Starting pooled panel analysis")
            tier1_results = await self.tier1_runner.run(command, job_id)
            await self._update_progress(job_id, 'tier1', 100, "Pooled panel analysis complete")
            
            # Run Tier 2
            await self._update_progress(job_id, 'tier2', 0, "Starting commodity-specific analysis")
            tier2_results = await self.tier2_runner.run(command, job_id, tier1_results)
            await self._update_progress(job_id, 'tier2', 100, "Commodity analysis complete")
            
            # Run Tier 3
            await self._update_progress(job_id, 'tier3', 0, "Starting validation analysis")
            tier3_results = await self.tier3_runner.run(
                command, job_id, tier1_results, tier2_results
            )
            await self._update_progress(job_id, 'tier3', 100, "Validation analysis complete")
            
            # Aggregate and validate results
            final_results = await self._aggregate_results(
                tier1_results,
                tier2_results,
                tier3_results,
                analysis_config
            )
            
            # Validate critical findings
            validation_passed = self._validate_critical_findings(final_results)
            
            if not validation_passed:
                logger.warning("Critical findings validation failed")
                final_results['validation_warning'] = (
                    "Results do not match expected V1 findings. "
                    "Review model configuration and data quality."
                )
            
            # Complete analysis
            if self.orchestrator:
                await self.orchestrator.complete_analysis(job_id, final_results)
            
            # Emit completion event
            await self._emit_event(
                AnalysisCompletedEvent(
                    analysis_id=analysis_id,
                    results=final_results
                )
            )
            
            logger.info(f"Three-tier analysis {analysis_id} completed successfully")
            
            return final_results
            
        except Exception as e:
            error_msg = f"Three-tier analysis failed: {str(e)}"
            logger.error(error_msg)
            
            # Update failure status
            if self.orchestrator:
                await self.orchestrator.fail_analysis(job_id, error_msg)
            
            # Emit failure event
            await self._emit_event(
                AnalysisFailedEvent(
                    analysis_id=analysis_id,
                    error=error_msg
                )
            )
            
            raise
    
    
    
    
    async def _aggregate_results(self,
                               tier1_results: Dict[str, Any],
                               tier2_results: Dict[str, Any],
                               tier3_results: Dict[str, Any],
                               config: Dict[str, Any]) -> Dict[str, Any]:
        """Aggregate results from all tiers."""
        logger.info("Aggregating three-tier results")
        
        # Build aggregated results
        results = {
            'analysis_type': 'three_tier',
            'version': '2.0',
            'timestamp': datetime.utcnow().isoformat(),
            'config': config,
            'tiers': {
                'tier1': tier1_results,
                'tier2': tier2_results,
                'tier3': tier3_results
            },
            'summary': {}
        }
        
        # Generate executive summary
        summary = {
            'key_findings': [],
            'conflict_effect': None,
            'model_fit': None,
            'commodity_integration': {},
            'validation_results': {}
        }
        
        # Extract key findings from Tier 1
        tier1_result = tier1_results.get('result')
        if tier1_result and hasattr(tier1_result, 'coefficients'):
            conflict_coef = tier1_result.coefficients.get('conflict_intensity', 0)
            conflict_pval = tier1_result.p_values.get('conflict_intensity', 1.0)
            
            summary['conflict_effect'] = {
                'coefficient': conflict_coef,
                'p_value': conflict_pval,
                'percentage_effect': conflict_coef * 100,
                'is_significant': conflict_pval < 0.001
            }
            
            if conflict_pval < 0.001:
                summary['key_findings'].append(
                    f"Conflict increases prices by {conflict_coef * 100:.1f}% "
                    f"(p < {conflict_pval:.3f})"
                )
            
            # Model fit
            if hasattr(tier1_result, 'r_squared'):
                summary['model_fit'] = {
                    'r_squared': tier1_result.r_squared,
                    'adjusted_r_squared': getattr(tier1_result, 'adjusted_r_squared', None),
                    'n_observations': tier1_result.n_observations
                }
        
        # From Tier 2
        if isinstance(tier2_results, dict):
            successful_commodities = [
                commodity for commodity, result in tier2_results.items()
                if isinstance(result, dict) and 'error' not in result
            ]
            
            summary['commodity_integration'] = {
                'analyzed_commodities': list(tier2_results.keys()),
                'successful_analyses': successful_commodities,
                'success_rate': len(successful_commodities) / len(tier2_results) if tier2_results else 0
            }
            
            if successful_commodities:
                summary['key_findings'].append(
                    f"Successfully analyzed {len(successful_commodities)} out of {len(tier2_results)} commodities"
                )
        
        # From Tier 3
        if isinstance(tier3_results, dict):
            validation_methods = list(tier3_results.keys())
            successful_validations = [
                method for method, result in tier3_results.items()
                if isinstance(result, dict) and 'error' not in result
            ]
            
            summary['validation_results'] = {
                'validation_methods': validation_methods,
                'successful_validations': successful_validations,
                'validation_success_rate': len(successful_validations) / len(validation_methods) if validation_methods else 0
            }
            
            if successful_validations:
                summary['key_findings'].append(
                    f"Completed {len(successful_validations)} validation methods: {', '.join(successful_validations)}"
                )
        
        results['summary'] = summary
        
        # Calculate confidence scores
        results['confidence_scores'] = self._calculate_confidence_scores(
            tier1_results,
            tier2_results,
            tier3_results
        )
        
        return results
    
    def _validate_critical_findings(self, results: Dict[str, Any]) -> bool:
        """Validate that results match expected V1 findings."""
        validations = []
        
        try:
            # 1. Validate conflict effect exists in tier1
            tier1_result = results['tiers']['tier1'].get('result')
            if tier1_result and hasattr(tier1_result, 'coefficients'):
                conflict_coef = tier1_result.coefficients.get('conflict_intensity', 0)
                conflict_pval = tier1_result.p_values.get('conflict_intensity', 1.0)
                
                # Check magnitude (35% ± 10% tolerance)
                validations.append(0.25 <= conflict_coef <= 0.45)
                # Check significance
                validations.append(conflict_pval < 0.001)
                
                # Check model fit
                if hasattr(tier1_result, 'r_squared') and tier1_result.r_squared:
                    validations.append(tier1_result.r_squared > 0.6)
                else:
                    validations.append(False)
            else:
                validations.extend([False, False, False])
            
            # 2. Validate tier2 results exist
            tier2_results = results['tiers']['tier2']
            validations.append(isinstance(tier2_results, dict) and len(tier2_results) > 0)
            
            # 3. Validate tier3 results exist
            tier3_results = results['tiers']['tier3']
            validations.append(isinstance(tier3_results, dict) and len(tier3_results) > 0)
            
        except Exception as e:
            logger.warning(f"Error validating critical findings: {e}")
            return False
        
        # At least 4 out of 5 validations must pass for overall success
        return sum(validations) >= 4
    
    def _calculate_confidence_scores(self,
                                   tier1: Dict[str, Any],
                                   tier2: Dict[str, Any],
                                   tier3: Dict[str, Any]) -> Dict[str, float]:
        """Calculate confidence scores for results."""
        scores = {}
        
        # Tier 1 confidence
        tier1_conf = 1.0
        if tier1.get('diagnostics', {}).get('summary', {}).get('has_critical_failures'):
            tier1_conf *= 0.8
        
        # Check for conflict effect validity
        tier1_result = tier1.get('result')
        if tier1_result and hasattr(tier1_result, 'coefficients'):
            conflict_coef = tier1_result.coefficients.get('conflict_intensity', 0)
            conflict_pval = tier1_result.p_values.get('conflict_intensity', 1.0)
            if 0.25 <= conflict_coef <= 0.45 and conflict_pval < 0.001:
                tier1_conf *= 1.0
            else:
                tier1_conf *= 0.5
        else:
            tier1_conf *= 0.3
        scores['tier1'] = tier1_conf
        
        # Tier 2 confidence
        if isinstance(tier2, dict) and len(tier2) > 0:
            # Count successful commodity analyses
            successful = sum(1 for result in tier2.values() 
                           if isinstance(result, dict) and 'error' not in result)
            total = len(tier2)
            scores['tier2'] = successful / total if total > 0 else 0.0
        else:
            scores['tier2'] = 0.0
        
        # Tier 3 confidence
        if isinstance(tier3, dict) and len(tier3) > 0:
            # Count successful validation models
            successful = sum(1 for result in tier3.values() 
                           if isinstance(result, dict) and 'error' not in result)
            total = len(tier3)
            scores['tier3'] = successful / total if total > 0 else 0.0
        else:
            scores['tier3'] = 0.0
        
        # Overall confidence
        scores['overall'] = sum(scores.values()) / len(scores)
        
        return scores
    
    async def _emit_event(self, event: DomainEvent) -> None:
        """Emit event if event bus is available."""
        if self.event_bus:
            await self.event_bus.publish(event)
    
    async def _update_progress(self, job_id: str, tier: str, 
                             progress: int, message: str) -> None:
        """Update analysis progress."""
        if self.orchestrator:
            await self.orchestrator.update_progress(job_id, tier, progress, message)
        
        # Also emit progress event
        await self._emit_event(
            AnalysisProgressEvent(
                analysis_id=job_id,
                tier=tier,
                progress=progress,
                message=message
            )
        )