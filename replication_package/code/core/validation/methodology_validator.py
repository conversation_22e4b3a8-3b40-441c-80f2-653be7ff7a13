"""
Methodology Validator for Yemen Market Integration Analysis

This module enforces strict research methodology requirements on all data and analyses
to ensure the integrity of econometric research in multi-exchange rate environments.

CRITICAL: The initial analysis failed because it compared prices without proper currency
conversion. This validator ensures that mistake is never repeated.

Key Requirements:
1. Currency Conversion: ALL prices MUST be converted to USD before ANY analysis
2. Exchange Rate Validation: Multi-source validation required (CBY-Aden, CBY-Sana'a, parallel)
3. Statistical Testing: Bonferroni correction for H1-H5, <PERSON><PERSON><PERSON><PERSON><PERSON> for H6-H10
4. Data Coverage: 88.4% coverage target with conflict-aware imputation
5. Statistical Power: n≥300 markets, t≥36 months, obs≥10,000
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any, Set
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import logging

# Set up logging if not already configured
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ValidationStatus(Enum):
    """Status of validation checks."""
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"
    NOT_APPLICABLE = "not_applicable"


class AnalysisType(Enum):
    """Types of analyses that require validation."""
    PANEL_ANALYSIS = "panel_analysis"
    HYPOTHESIS_TESTING = "hypothesis_testing"
    WELFARE_ANALYSIS = "welfare_analysis"
    TIME_SERIES = "time_series"
    CROSS_SECTIONAL = "cross_sectional"
    EVENT_STUDY = "event_study"
    REGIME_SWITCHING = "regime_switching"


@dataclass
class ValidationIssue:
    """Represents a single validation issue."""
    check_name: str
    status: ValidationStatus
    message: str
    severity: str  # "critical", "error", "warning", "info"
    details: Dict[str, Any] = field(default_factory=dict)
    recommendation: Optional[str] = None


@dataclass
class ValidationReport:
    """Comprehensive validation report."""
    timestamp: datetime
    analysis_type: AnalysisType
    total_observations: int
    
    # Validation results
    issues: List[ValidationIssue]
    critical_failures: List[str]
    errors: List[str]
    warnings: List[str]
    
    # Compliance metrics
    currency_conversion_compliance: float  # % of prices properly converted
    exchange_rate_coverage: float  # % of observations with valid rates
    zone_classification_coverage: float  # % of markets properly classified
    statistical_power_adequate: bool
    data_coverage_percentage: float
    
    # Summary
    is_valid: bool
    can_proceed_with_warnings: bool
    blocked_analyses: List[str]  # Analyses that cannot proceed
    
    def __post_init__(self):
        """Calculate summary statistics."""
        self.critical_failures = [i.message for i in self.issues if i.severity == "critical"]
        self.errors = [i.message for i in self.issues if i.severity == "error"]
        self.warnings = [i.message for i in self.issues if i.severity == "warning"]
        
        # Analysis is valid only if no critical failures
        self.is_valid = len(self.critical_failures) == 0
        
        # Can proceed with warnings if no critical failures or errors
        self.can_proceed_with_warnings = len(self.critical_failures) == 0 and len(self.errors) == 0


@dataclass
class DataRequirements:
    """Minimum data requirements for valid analysis."""
    min_markets: int = 300
    min_time_periods: int = 36
    min_observations: int = 10000
    target_coverage: float = 0.884  # 88.4%
    
    # Currency zone requirements
    min_zones_represented: int = 2
    min_observations_per_zone: int = 1000
    
    # Exchange rate requirements
    max_missing_exchange_rate_pct: float = 0.05  # 5% max missing
    required_rate_sources: List[str] = field(default_factory=lambda: [
        "CBY_Aden", "CBY_Sanaa", "parallel_market"
    ])
    
    # Multiple testing requirements
    primary_hypotheses: List[str] = field(default_factory=lambda: ["H1", "H2", "H3", "H4", "H5"])
    secondary_hypotheses: List[str] = field(default_factory=lambda: ["H6", "H7", "H8", "H9", "H10"])
    bonferroni_alpha: float = 0.01  # 0.05 / 5 primary hypotheses
    fdr_alpha: float = 0.10  # For secondary hypotheses


class MethodologyValidator:
    """
    Enforces research methodology requirements on data and analyses.
    
    This is the gatekeeper that prevents invalid analyses from proceeding.
    Every analysis must pass validation before results can be generated.
    """
    
    def __init__(self, requirements: Optional[DataRequirements] = None):
        """Initialize validator with data requirements."""
        self.requirements = requirements or DataRequirements()
        self.validation_history: List[ValidationReport] = []
        
    def validate_analysis_inputs(
        self,
        observations: pd.DataFrame,
        analysis_type: AnalysisType,
        hypothesis_tests: Optional[List[str]] = None,
        additional_checks: Optional[List[str]] = None
    ) -> Tuple[bool, ValidationReport]:
        """
        Validate that analysis inputs meet all methodology requirements.
        
        Args:
            observations: DataFrame with price/market data
            analysis_type: Type of analysis to perform
            hypothesis_tests: List of hypothesis IDs (e.g., ["H1", "H2"])
            additional_checks: Additional validation checks to perform
            
        Returns:
            Tuple of (is_valid, validation_report)
        """
        logger.info(f"Validating inputs for {analysis_type.value} analysis")
        
        issues = []
        
        # 1. CRITICAL: Currency Conversion Check
        currency_issue = self._validate_currency_conversion(observations)
        if currency_issue:
            issues.append(currency_issue)
            
        # 2. Exchange Rate Validation
        exchange_rate_issues = self._validate_exchange_rates(observations)
        issues.extend(exchange_rate_issues)
        
        # 3. Zone Classification Check
        zone_issue = self._validate_zone_classification(observations)
        if zone_issue:
            issues.append(zone_issue)
            
        # 4. Statistical Power Check
        power_issues = self._validate_statistical_power(observations)
        issues.extend(power_issues)
        
        # 5. Data Coverage Check
        coverage_issue = self._validate_data_coverage(observations)
        if coverage_issue:
            issues.append(coverage_issue)
            
        # 6. Multiple Testing Correction Check (if hypothesis testing)
        if hypothesis_tests:
            testing_issues = self._validate_multiple_testing_setup(hypothesis_tests)
            issues.extend(testing_issues)
            
        # 7. Analysis-specific checks
        specific_issues = self._validate_analysis_specific(observations, analysis_type)
        issues.extend(specific_issues)
        
        # 8. Additional custom checks
        if additional_checks:
            custom_issues = self._run_additional_checks(observations, additional_checks)
            issues.extend(custom_issues)
            
        # Calculate compliance metrics
        compliance_metrics = self._calculate_compliance_metrics(observations, issues)
        
        # Create validation report
        report = ValidationReport(
            timestamp=datetime.now(),
            analysis_type=analysis_type,
            total_observations=len(observations),
            issues=issues,
            critical_failures=[],  # Will be set in __post_init__
            errors=[],  # Will be set in __post_init__
            warnings=[],  # Will be set in __post_init__
            is_valid=False,  # Will be recalculated in __post_init__
            can_proceed_with_warnings=False,  # Will be recalculated in __post_init__
            **compliance_metrics
        )
        
        # Store in history
        self.validation_history.append(report)
        
        # Log summary
        if report.is_valid:
            logger.info("✅ Validation PASSED - analysis can proceed")
        else:
            logger.error(f"❌ Validation FAILED - {len(report.critical_failures)} critical issues found")
            for failure in report.critical_failures:
                logger.error(f"  - {failure}")
                
        return report.is_valid, report
        
    def _validate_currency_conversion(self, df: pd.DataFrame) -> Optional[ValidationIssue]:
        """
        CRITICAL CHECK: Ensure all prices are converted to USD.
        
        This is THE most important check. The entire project initially failed
        because prices were compared without currency conversion.
        """
        required_columns = ['price_usd', 'price_yer', 'exchange_rate_used', 'currency_zone']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            return ValidationIssue(
                check_name="currency_conversion",
                status=ValidationStatus.FAILED,
                message=f"CRITICAL: Missing required currency columns: {missing_columns}",
                severity="critical",
                details={"missing_columns": missing_columns},
                recommendation="All price data MUST include both YER and USD prices with exchange rate used"
            )
            
        # Check for missing USD prices
        missing_usd = df['price_usd'].isna().sum()
        missing_pct = missing_usd / len(df) * 100
        
        if missing_pct > 0:
            return ValidationIssue(
                check_name="currency_conversion",
                status=ValidationStatus.FAILED,
                message=f"CRITICAL: {missing_pct:.1f}% of observations missing USD prices",
                severity="critical",
                details={
                    "missing_count": int(missing_usd),
                    "total_count": len(df),
                    "missing_percentage": missing_pct
                },
                recommendation="Convert ALL prices to USD using zone-specific exchange rates before analysis"
            )
            
        # Check for reasonable exchange rates
        if 'exchange_rate_used' in df.columns:
            invalid_rates = df[
                (df['exchange_rate_used'] < 100) | 
                (df['exchange_rate_used'] > 3000)
            ]
            if len(invalid_rates) > 0:
                return ValidationIssue(
                    check_name="currency_conversion",
                    status=ValidationStatus.WARNING,
                    message=f"Found {len(invalid_rates)} observations with suspicious exchange rates",
                    severity="warning",
                    details={
                        "min_rate": float(df['exchange_rate_used'].min()),
                        "max_rate": float(df['exchange_rate_used'].max()),
                        "suspicious_count": len(invalid_rates)
                    },
                    recommendation="Review exchange rates outside 100-3000 YER/USD range"
                )
                
        logger.info("✅ Currency conversion check PASSED")
        return None
        
    def _validate_exchange_rates(self, df: pd.DataFrame) -> List[ValidationIssue]:
        """Validate exchange rate data completeness and multi-source validation."""
        issues = []
        
        if 'exchange_rate_source' not in df.columns:
            issues.append(ValidationIssue(
                check_name="exchange_rate_sources",
                status=ValidationStatus.WARNING,
                message="No exchange rate source information available",
                severity="warning",
                recommendation="Track exchange rate sources for validation"
            ))
            return issues
            
        # Check for required sources
        available_sources = df['exchange_rate_source'].unique()
        missing_sources = [
            source for source in self.requirements.required_rate_sources 
            if source not in available_sources
        ]
        
        if missing_sources:
            issues.append(ValidationIssue(
                check_name="exchange_rate_sources",
                status=ValidationStatus.FAILED,
                message=f"Missing required exchange rate sources: {missing_sources}",
                severity="error",
                details={
                    "required": self.requirements.required_rate_sources,
                    "available": list(available_sources),
                    "missing": missing_sources
                },
                recommendation="Collect rates from CBY-Aden, CBY-Sana'a, and parallel markets"
            ))
            
        # Check temporal consistency
        if 'date' in df.columns and 'currency_zone' in df.columns:
            for zone in df['currency_zone'].unique():
                zone_data = df[df['currency_zone'] == zone].copy()
                zone_data['rate_change'] = zone_data.groupby('date')['exchange_rate_used'].transform(
                    lambda x: x.pct_change()
                )
                
                # Flag large daily changes
                large_changes = zone_data[zone_data['rate_change'].abs() > 0.10]
                if len(large_changes) > 0:
                    issues.append(ValidationIssue(
                        check_name="exchange_rate_stability",
                        status=ValidationStatus.WARNING,
                        message=f"Zone {zone}: {len(large_changes)} days with >10% rate changes",
                        severity="warning",
                        details={
                            "zone": zone,
                            "volatile_days": len(large_changes),
                            "max_change": float(zone_data['rate_change'].abs().max())
                        },
                        recommendation="Verify exchange rate spikes with multiple sources"
                    ))
                    
        return issues
        
    def _validate_zone_classification(self, df: pd.DataFrame) -> Optional[ValidationIssue]:
        """Validate that all markets are properly classified into currency zones."""
        if 'currency_zone' not in df.columns:
            return ValidationIssue(
                check_name="zone_classification",
                status=ValidationStatus.FAILED,
                message="CRITICAL: No currency zone classification found",
                severity="critical",
                recommendation="Classify all markets into currency zones (HOUTHI, GOVERNMENT, CONTESTED)"
            )
            
        # Check for missing classifications
        missing_zones = df['currency_zone'].isna().sum()
        if missing_zones > 0:
            return ValidationIssue(
                check_name="zone_classification",
                status=ValidationStatus.FAILED,
                message=f"{missing_zones} observations missing currency zone classification",
                severity="error",
                details={"missing_count": int(missing_zones)},
                recommendation="Ensure all markets are mapped to currency zones"
            )
            
        # Check zone distribution
        zone_counts = df['currency_zone'].value_counts()
        if len(zone_counts) < self.requirements.min_zones_represented:
            return ValidationIssue(
                check_name="zone_representation",
                status=ValidationStatus.WARNING,
                message=f"Only {len(zone_counts)} currency zones represented (min: {self.requirements.min_zones_represented})",
                severity="warning",
                details={"zone_distribution": zone_counts.to_dict()},
                recommendation="Ensure data covers multiple currency zones for comparison"
            )
            
        # Check minimum observations per zone
        for zone, count in zone_counts.items():
            if count < self.requirements.min_observations_per_zone:
                return ValidationIssue(
                    check_name="zone_observations",
                    status=ValidationStatus.WARNING,
                    message=f"Zone {zone} has only {count} observations (min: {self.requirements.min_observations_per_zone})",
                    severity="warning",
                    details={"zone": zone, "count": int(count)},
                    recommendation="Collect more data for underrepresented zones"
                )
                
        logger.info("✅ Zone classification check PASSED")
        return None
        
    def _validate_statistical_power(self, df: pd.DataFrame) -> List[ValidationIssue]:
        """Validate that data meets minimum statistical power requirements."""
        issues = []
        
        # Check number of markets
        if 'market_id' in df.columns:
            n_markets = df['market_id'].nunique()
            if n_markets < self.requirements.min_markets:
                issues.append(ValidationIssue(
                    check_name="market_coverage",
                    status=ValidationStatus.FAILED,
                    message=f"Only {n_markets} markets (min: {self.requirements.min_markets})",
                    severity="error",
                    details={"market_count": n_markets},
                    recommendation="Expand market coverage to ensure statistical power"
                ))
                
        # Check time periods
        if 'date' in df.columns:
            df['year_month'] = pd.to_datetime(df['date']).dt.to_period('M')
            n_periods = df['year_month'].nunique()
            if n_periods < self.requirements.min_time_periods:
                issues.append(ValidationIssue(
                    check_name="time_coverage",
                    status=ValidationStatus.FAILED,
                    message=f"Only {n_periods} time periods (min: {self.requirements.min_time_periods})",
                    severity="error",
                    details={"period_count": n_periods},
                    recommendation="Extend time series to at least 36 months"
                ))
                
        # Check total observations
        n_obs = len(df)
        if n_obs < self.requirements.min_observations:
            issues.append(ValidationIssue(
                check_name="sample_size",
                status=ValidationStatus.FAILED,
                message=f"Only {n_obs:,} observations (min: {self.requirements.min_observations:,})",
                severity="error",
                details={"observation_count": n_obs},
                recommendation="Increase sample size for adequate statistical power"
            ))
            
        return issues
        
    def _validate_data_coverage(self, df: pd.DataFrame) -> Optional[ValidationIssue]:
        """Check data coverage against 88.4% target."""
        # Calculate theoretical maximum observations
        if all(col in df.columns for col in ['market_id', 'date', 'commodity']):
            markets = df['market_id'].nunique()
            dates = df['date'].nunique()
            commodities = df['commodity'].nunique()
            
            theoretical_max = markets * dates * commodities
            actual_coverage = len(df) / theoretical_max
            
            if actual_coverage < self.requirements.target_coverage:
                return ValidationIssue(
                    check_name="data_coverage",
                    status=ValidationStatus.WARNING,
                    message=f"Data coverage is {actual_coverage:.1%} (target: {self.requirements.target_coverage:.1%})",
                    severity="warning",
                    details={
                        "coverage": actual_coverage,
                        "target": self.requirements.target_coverage,
                        "theoretical_max": theoretical_max,
                        "actual_obs": len(df)
                    },
                    recommendation="Use conflict-aware imputation to reach 88.4% coverage target"
                )
                
        return None
        
    def _validate_multiple_testing_setup(self, hypothesis_tests: List[str]) -> List[ValidationIssue]:
        """Validate multiple testing correction setup."""
        issues = []
        
        # Separate primary and secondary hypotheses
        primary_tests = [h for h in hypothesis_tests if h in self.requirements.primary_hypotheses]
        secondary_tests = [h for h in hypothesis_tests if h in self.requirements.secondary_hypotheses]
        
        # Check if testing primary hypotheses
        if primary_tests:
            issues.append(ValidationIssue(
                check_name="multiple_testing_primary",
                status=ValidationStatus.WARNING,
                message=f"Testing {len(primary_tests)} primary hypotheses - Bonferroni correction required (α={self.requirements.bonferroni_alpha})",
                severity="info",
                details={
                    "hypotheses": primary_tests,
                    "corrected_alpha": self.requirements.bonferroni_alpha
                },
                recommendation="Apply Bonferroni correction to control family-wise error rate"
            ))
            
        # Check if testing secondary hypotheses
        if secondary_tests:
            issues.append(ValidationIssue(
                check_name="multiple_testing_secondary",
                status=ValidationStatus.WARNING,
                message=f"Testing {len(secondary_tests)} secondary hypotheses - FDR control required",
                severity="info",
                details={
                    "hypotheses": secondary_tests,
                    "fdr_alpha": self.requirements.fdr_alpha
                },
                recommendation="Apply Benjamini-Hochberg procedure for false discovery rate control"
            ))
            
        return issues
        
    def _validate_analysis_specific(self, df: pd.DataFrame, analysis_type: AnalysisType) -> List[ValidationIssue]:
        """Run analysis-specific validation checks."""
        issues = []
        
        if analysis_type == AnalysisType.PANEL_ANALYSIS:
            # Check panel structure
            if 'market_id' in df.columns and 'date' in df.columns:
                # Check for balanced panel
                panel_balance = df.groupby('market_id')['date'].count()
                if panel_balance.std() / panel_balance.mean() > 0.2:
                    issues.append(ValidationIssue(
                        check_name="panel_balance",
                        status=ValidationStatus.WARNING,
                        message="Panel is unbalanced - consider using balanced panel methods",
                        severity="warning",
                        details={
                            "mean_obs_per_market": float(panel_balance.mean()),
                            "std_obs_per_market": float(panel_balance.std())
                        }
                    ))
                    
        elif analysis_type == AnalysisType.WELFARE_ANALYSIS:
            # Check for required welfare variables
            welfare_vars = ['household_size', 'income_decile', 'consumption_basket']
            missing_welfare = [v for v in welfare_vars if v not in df.columns]
            if missing_welfare:
                issues.append(ValidationIssue(
                    check_name="welfare_variables",
                    status=ValidationStatus.WARNING,
                    message=f"Missing welfare analysis variables: {missing_welfare}",
                    severity="warning",
                    recommendation="Include household characteristics for welfare analysis"
                ))
                
        elif analysis_type == AnalysisType.REGIME_SWITCHING:
            # Check time series length
            if 'date' in df.columns:
                n_periods = df['date'].nunique()
                if n_periods < 60:  # 5 years monthly
                    issues.append(ValidationIssue(
                        check_name="regime_switching_length",
                        status=ValidationStatus.WARNING,
                        message=f"Time series may be too short for regime switching ({n_periods} periods)",
                        severity="warning",
                        recommendation="Regime switching models typically need 60+ time periods"
                    ))
                    
        return issues
        
    def _run_additional_checks(self, df: pd.DataFrame, checks: List[str]) -> List[ValidationIssue]:
        """Run any additional custom validation checks."""
        issues = []
        
        for check in checks:
            if check == "outlier_detection":
                # Check for price outliers
                if 'price_usd' in df.columns:
                    z_scores = np.abs((df['price_usd'] - df['price_usd'].mean()) / df['price_usd'].std())
                    outliers = z_scores > 3
                    if outliers.sum() > 0:
                        issues.append(ValidationIssue(
                            check_name="price_outliers",
                            status=ValidationStatus.WARNING,
                            message=f"Found {outliers.sum()} potential price outliers (|z| > 3)",
                            severity="warning",
                            recommendation="Review and validate extreme price values"
                        ))
                        
            elif check == "temporal_gaps":
                # Check for temporal gaps
                if 'date' in df.columns:
                    dates = pd.to_datetime(df['date']).sort_values()
                    gaps = dates.diff()
                    large_gaps = gaps[gaps > pd.Timedelta(days=7)]
                    if len(large_gaps) > 0:
                        issues.append(ValidationIssue(
                            check_name="temporal_gaps",
                            status=ValidationStatus.WARNING,
                            message=f"Found {len(large_gaps)} temporal gaps > 7 days",
                            severity="warning",
                            recommendation="Consider imputation for temporal gaps"
                        ))
                        
        return issues
        
    def _calculate_compliance_metrics(self, df: pd.DataFrame, issues: List[ValidationIssue]) -> Dict[str, Any]:
        """Calculate overall compliance metrics."""
        metrics = {}
        
        # Currency conversion compliance
        if 'price_usd' in df.columns:
            metrics['currency_conversion_compliance'] = 1.0 - (df['price_usd'].isna().sum() / len(df))
        else:
            metrics['currency_conversion_compliance'] = 0.0
            
        # Exchange rate coverage
        if 'exchange_rate_used' in df.columns:
            metrics['exchange_rate_coverage'] = 1.0 - (df['exchange_rate_used'].isna().sum() / len(df))
        else:
            metrics['exchange_rate_coverage'] = 0.0
            
        # Zone classification coverage
        if 'currency_zone' in df.columns:
            metrics['zone_classification_coverage'] = 1.0 - (df['currency_zone'].isna().sum() / len(df))
        else:
            metrics['zone_classification_coverage'] = 0.0
            
        # Statistical power
        power_issues = [i for i in issues if i.check_name in ['market_coverage', 'time_coverage', 'sample_size']]
        metrics['statistical_power_adequate'] = all(i.status != ValidationStatus.FAILED for i in power_issues)
        
        # Data coverage
        if all(col in df.columns for col in ['market_id', 'date', 'commodity']):
            theoretical_max = df['market_id'].nunique() * df['date'].nunique() * df['commodity'].nunique()
            metrics['data_coverage_percentage'] = len(df) / theoretical_max if theoretical_max > 0 else 0.0
        else:
            metrics['data_coverage_percentage'] = 0.0
            
        # Blocked analyses based on critical failures
        blocked = []
        critical_checks = [i.check_name for i in issues if i.severity == "critical"]
        
        if "currency_conversion" in critical_checks:
            blocked.extend(["panel_analysis", "hypothesis_testing", "welfare_analysis"])
        if "zone_classification" in critical_checks:
            blocked.extend(["regime_switching", "cross_zone_comparison"])
            
        metrics['blocked_analyses'] = list(set(blocked))
        
        return metrics
        
    def generate_validation_summary(self, report: ValidationReport) -> str:
        """Generate human-readable validation summary."""
        lines = ["# Methodology Validation Report", ""]
        
        # Header
        lines.append(f"**Analysis Type**: {report.analysis_type.value}")
        lines.append(f"**Timestamp**: {report.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"**Total Observations**: {report.total_observations:,}")
        lines.append("")
        
        # Overall Status
        if report.is_valid:
            lines.append("## ✅ VALIDATION PASSED")
        else:
            lines.append("## ❌ VALIDATION FAILED")
            
        lines.append("")
        
        # Compliance Metrics
        lines.append("## Compliance Metrics")
        lines.append(f"- Currency Conversion: {report.currency_conversion_compliance:.1%}")
        lines.append(f"- Exchange Rate Coverage: {report.exchange_rate_coverage:.1%}")
        lines.append(f"- Zone Classification: {report.zone_classification_coverage:.1%}")
        lines.append(f"- Statistical Power: {'✅ Adequate' if report.statistical_power_adequate else '❌ Insufficient'}")
        lines.append(f"- Data Coverage: {report.data_coverage_percentage:.1%} (target: 88.4%)")
        lines.append("")
        
        # Critical Failures
        if report.critical_failures:
            lines.append("## 🚨 Critical Failures")
            for failure in report.critical_failures:
                lines.append(f"- {failure}")
            lines.append("")
            
        # Errors
        if report.errors:
            lines.append("## ❌ Errors")
            for error in report.errors:
                lines.append(f"- {error}")
            lines.append("")
            
        # Warnings
        if report.warnings:
            lines.append("## ⚠️ Warnings")
            for warning in report.warnings:
                lines.append(f"- {warning}")
            lines.append("")
            
        # Blocked Analyses
        if report.blocked_analyses:
            lines.append("## 🚫 Blocked Analyses")
            lines.append("The following analyses cannot proceed due to validation failures:")
            for analysis in report.blocked_analyses:
                lines.append(f"- {analysis}")
            lines.append("")
            
        # Recommendations
        lines.append("## 📋 Recommendations")
        recommendations = set()
        for issue in report.issues:
            if issue.recommendation:
                recommendations.add(issue.recommendation)
                
        for rec in sorted(recommendations):
            lines.append(f"- {rec}")
            
        return "\n".join(lines)
        
    def enforce_pre_analysis_requirements(
        self,
        df: pd.DataFrame,
        analysis_type: AnalysisType
    ) -> pd.DataFrame:
        """
        Enforce requirements and return cleaned data ready for analysis.
        
        This method will:
        1. Run validation
        2. Apply automatic fixes where possible
        3. Return analysis-ready data or raise exception
        """
        # First validate
        is_valid, report = self.validate_analysis_inputs(df, analysis_type)
        
        if not is_valid and not report.can_proceed_with_warnings:
            raise MethodologyViolation(
                f"Cannot proceed with {analysis_type.value}: {len(report.critical_failures)} critical failures",
                report=report
            )
            
        # Apply automatic fixes
        cleaned_df = df.copy()
        
        # Remove observations without USD prices
        if 'price_usd' in cleaned_df.columns:
            before_count = len(cleaned_df)
            cleaned_df = cleaned_df.dropna(subset=['price_usd'])
            after_count = len(cleaned_df)
            
            if before_count > after_count:
                logger.warning(f"Removed {before_count - after_count} observations without USD prices")
                
        # Remove observations without zone classification
        if 'currency_zone' in cleaned_df.columns:
            before_count = len(cleaned_df)
            cleaned_df = cleaned_df.dropna(subset=['currency_zone'])
            after_count = len(cleaned_df)
            
            if before_count > after_count:
                logger.warning(f"Removed {before_count - after_count} observations without zone classification")
                
        # Re-validate cleaned data
        is_valid_cleaned, report_cleaned = self.validate_analysis_inputs(cleaned_df, analysis_type)
        
        if not is_valid_cleaned:
            logger.warning("Data still has issues after cleaning - proceed with caution")
            
        return cleaned_df


class MethodologyViolation(Exception):
    """Exception raised when methodology requirements are violated."""
    
    def __init__(self, message: str, report: Optional[ValidationReport] = None):
        super().__init__(message)
        self.report = report
        
    def __str__(self):
        base_msg = super().__str__()
        if self.report:
            # Generate a simple summary from the report
            summary = f"\nValidation Report:\n"
            summary += f"- Analysis Type: {self.report.analysis_type.value}\n"
            summary += f"- Is Valid: {self.report.is_valid}\n"
            summary += f"- Critical Failures: {len(self.report.critical_failures)}\n"
            for failure in self.report.critical_failures:
                summary += f"  * {failure}\n"
            return f"{base_msg}\n{summary}"
        return base_msg


# Convenience functions
def validate_before_analysis(analysis_func):
    """Decorator to ensure validation before analysis."""
    def wrapper(self, *args, **kwargs):
        # Extract DataFrame from args/kwargs
        df = args[0] if args else kwargs.get('data', kwargs.get('df'))
        
        if df is None:
            raise ValueError("No data provided for analysis")
            
        # Determine analysis type from function name
        analysis_type = AnalysisType.PANEL_ANALYSIS  # Default
        if 'welfare' in analysis_func.__name__:
            analysis_type = AnalysisType.WELFARE_ANALYSIS
        elif 'hypothesis' in analysis_func.__name__:
            analysis_type = AnalysisType.HYPOTHESIS_TESTING
            
        # Validate
        validator = MethodologyValidator()
        is_valid, report = validator.validate_analysis_inputs(df, analysis_type)
        
        if not is_valid:
            raise MethodologyViolation(
                f"Validation failed for {analysis_func.__name__}",
                report=report
            )
            
        # Proceed with analysis
        return analysis_func(self, *args, **kwargs)
        
    return wrapper