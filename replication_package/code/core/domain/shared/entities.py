"""Base entities and aggregate roots for domain modeling."""

from abc import ABC
from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional
from uuid import UUID, uuid4

from .events import DomainEvent


@dataclass
class Entity(ABC):
    """Base entity with identity."""
    
    id: UUID = field(default_factory=uuid4)
    
    def __eq__(self, other: object) -> bool:
        """Entities are equal if they have the same ID."""
        if not isinstance(other, Entity):
            return False
        return self.id == other.id
    
    def __hash__(self) -> int:
        """Hash based on ID."""
        return hash(self.id)


@dataclass
class AggregateRoot(Entity):
    """Base aggregate root that can emit domain events."""
    
    _events: List[DomainEvent] = field(default_factory=list, init=False)
    version: int = field(default=0, init=False)
    
    @property
    def events(self) -> List[DomainEvent]:
        """Get uncommitted domain events."""
        return self._events.copy()
    
    def add_event(self, event: DomainEvent) -> None:
        """Add a domain event."""
        self._events.append(event)
    
    def clear_events(self) -> None:
        """Clear uncommitted events after they've been dispatched."""
        self._events.clear()
    
    def increment_version(self) -> None:
        """Increment aggregate version for optimistic locking."""
        self.version += 1


@dataclass(frozen=True)
class ValueObject(ABC):
    """Base value object - immutable and compared by value."""
    pass


@dataclass(frozen=True)
class EntityId(ValueObject):
    """Base entity identifier."""
    
    value: UUID
    
    def __init__(self, value: UUID):
        """Initialize with UUID value."""
        object.__setattr__(self, 'value', value)
    
    def __str__(self) -> str:
        """String representation."""
        return str(self.value)