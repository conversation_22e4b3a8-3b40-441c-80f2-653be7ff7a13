"""Domain validators for data quality and business rules."""

from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Optional, Dict, Any
import numpy as np

from .exceptions import ValidationException, BusinessRuleViolation


class PriceValidator:
    """Domain validation for price observations."""
    
    @staticmethod
    def validate_outlier(
        price_amount: Decimal,
        historical_prices: List[Decimal],
        threshold: float = 3.0
    ) -> bool:
        """
        Detect price outliers using IQR method.
        
        Args:
            price_amount: Price to validate
            historical_prices: Historical price data
            threshold: Z-score threshold for outlier detection
            
        Returns:
            True if price is valid, False if outlier
        """
        if len(historical_prices) < 10:
            return True  # Insufficient data for validation
        
        values = [float(p) for p in historical_prices]
        
        # Use IQR method for outlier detection
        q1, q3 = np.percentile(values, [25, 75])
        iqr = q3 - q1
        
        if iqr == 0:
            # No variation - check for exact match
            return float(price_amount) == np.mean(values)
        
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        return lower_bound <= float(price_amount) <= upper_bound
    
    @staticmethod
    def validate_price_consistency(
        price_amount: Decimal,
        currency: str,
        commodity_category: str
    ) -> bool:
        """
        Validate price consistency based on commodity category and currency.
        
        Args:
            price_amount: Price amount to validate
            currency: Currency code
            commodity_category: Category of commodity
            
        Returns:
            True if price is consistent with expectations
        """
        # Basic sanity checks based on Yemen market context
        price_float = float(price_amount)
        
        if currency == "USD":
            # USD prices should be reasonable
            if commodity_category == "fuel":
                return 0.1 <= price_float <= 5.0  # $0.10 - $5.00 per liter
            elif commodity_category == "imported":
                return 0.05 <= price_float <= 10.0  # $0.05 - $10.00 per kg
            elif commodity_category in ["local", "agricultural"]:
                return 0.01 <= price_float <= 5.0  # $0.01 - $5.00 per kg
        
        elif currency == "YER":
            # YER prices should account for inflation
            if commodity_category == "fuel":
                return 50 <= price_float <= 5000  # 50 - 5000 YER per liter
            elif commodity_category == "imported":
                return 25 <= price_float <= 50000  # 25 - 50,000 YER per kg
            elif commodity_category in ["local", "agricultural"]:
                return 10 <= price_float <= 25000  # 10 - 25,000 YER per kg
        
        return True  # Default to valid for unknown currencies
    
    @staticmethod
    def validate_temporal_consistency(
        price_amount: Decimal,
        observation_date: datetime,
        recent_prices: List[tuple]  # [(date, price), ...]
    ) -> bool:
        """
        Validate temporal consistency of price observations.
        
        Args:
            price_amount: Price to validate
            observation_date: Date of observation
            recent_prices: Recent price observations as (date, price) tuples
            
        Returns:
            True if price is temporally consistent
        """
        if not recent_prices:
            return True
        
        # Filter prices within 30 days
        cutoff_date = observation_date - timedelta(days=30)
        relevant_prices = [
            (date, price) for date, price in recent_prices
            if date >= cutoff_date and date < observation_date
        ]
        
        if len(relevant_prices) < 3:
            return True  # Insufficient data
        
        # Calculate maximum expected daily change (20%)
        max_daily_change = 0.20
        
        # Check against most recent price
        recent_prices_sorted = sorted(relevant_prices, key=lambda x: x[0], reverse=True)
        most_recent_date, most_recent_price = recent_prices_sorted[0]
        
        days_diff = (observation_date - most_recent_date).days
        if days_diff == 0:
            days_diff = 1
        
        # Calculate maximum allowed change
        max_change = max_daily_change * days_diff
        expected_min = float(most_recent_price) * (1 - max_change)
        expected_max = float(most_recent_price) * (1 + max_change)
        
        return expected_min <= float(price_amount) <= expected_max


class ConflictValidator:
    """Domain validation for conflict events."""
    
    @staticmethod
    def validate_conflict_intensity(
        fatalities: int,
        declared_intensity: str
    ) -> bool:
        """
        Validate that declared intensity matches fatality count.
        
        Args:
            fatalities: Number of fatalities
            declared_intensity: Declared intensity level
            
        Returns:
            True if intensity matches fatalities
        """
        if fatalities <= 5:
            expected = "low"
        elif fatalities <= 25:
            expected = "medium"
        elif fatalities <= 100:
            expected = "high"
        else:
            expected = "severe"
        
        return declared_intensity.lower() == expected
    
    @staticmethod
    def validate_spatial_consistency(
        event_location: tuple,  # (lat, lon)
        market_locations: List[tuple],  # [(lat, lon), ...]
        max_distance_km: float = 1000.0
    ) -> bool:
        """
        Validate that conflict event is within reasonable distance of markets.
        
        Args:
            event_location: Event coordinates
            market_locations: List of market coordinates
            max_distance_km: Maximum reasonable distance
            
        Returns:
            True if event is spatially consistent
        """
        if not market_locations:
            return True
        
        lat1, lon1 = event_location
        
        # Check if event is within reasonable distance of any market
        for lat2, lon2 in market_locations:
            distance = ConflictValidator._haversine_distance(lat1, lon1, lat2, lon2)
            if distance <= max_distance_km:
                return True
        
        return False
    
    @staticmethod
    def _haversine_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate Haversine distance between two points."""
        from math import radians, sin, cos, sqrt, atan2
        
        R = 6371  # Earth's radius in kilometers
        
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * atan2(sqrt(a), sqrt(1-a))
        
        return R * c


class MarketValidator:
    """Domain validation for market data."""
    
    @staticmethod
    def validate_market_coordinates(
        latitude: float,
        longitude: float,
        governorate: str
    ) -> bool:
        """
        Validate that market coordinates are within Yemen and consistent with governorate.
        
        Args:
            latitude: Market latitude
            longitude: Market longitude
            governorate: Governorate name
            
        Returns:
            True if coordinates are valid
        """
        # Yemen bounding box (approximate)
        yemen_bounds = {
            'lat_min': 12.0,
            'lat_max': 19.0,
            'lon_min': 42.0,
            'lon_max': 54.0
        }
        
        # Check if coordinates are within Yemen
        if not (yemen_bounds['lat_min'] <= latitude <= yemen_bounds['lat_max']):
            return False
        if not (yemen_bounds['lon_min'] <= longitude <= yemen_bounds['lon_max']):
            return False
        
        # Rough governorate validation (simplified)
        governorate_regions = {
            'Sana\'a': {'lat': (15.0, 16.0), 'lon': (43.5, 44.5)},
            'Aden': {'lat': (12.5, 13.5), 'lon': (44.5, 45.5)},
            'Taiz': {'lat': (13.0, 14.0), 'lon': (43.5, 44.5)},
            'Hodeidah': {'lat': (14.0, 15.5), 'lon': (42.5, 43.5)},
            'Ibb': {'lat': (13.5, 14.5), 'lon': (43.5, 44.5)},
        }
        
        if governorate in governorate_regions:
            region = governorate_regions[governorate]
            lat_range = region['lat']
            lon_range = region['lon']
            
            return (lat_range[0] <= latitude <= lat_range[1] and
                    lon_range[0] <= longitude <= lon_range[1])
        
        return True  # Accept unknown governorates
    
    @staticmethod
    def validate_market_type_consistency(
        market_type: str,
        commodities_traded: List[str]
    ) -> bool:
        """
        Validate that market type is consistent with commodities traded.
        
        Args:
            market_type: Type of market (wholesale, retail, etc.)
            commodities_traded: List of commodity categories
            
        Returns:
            True if market type is consistent
        """
        if market_type == "port":
            # Port markets should primarily trade imported goods
            return "imported" in commodities_traded
        
        elif market_type == "rural":
            # Rural markets should trade local/agricultural goods
            return any(cat in commodities_traded for cat in ["local", "agricultural"])
        
        elif market_type == "wholesale":
            # Wholesale markets should have multiple commodity types
            return len(commodities_traded) >= 2
        
        return True  # Default to valid


class IntegrationValidator:
    """Domain validation for market integration metrics."""
    
    @staticmethod
    def validate_integration_score(
        score: float,
        market_distance_km: float,
        correlation: float
    ) -> bool:
        """
        Validate that integration score is consistent with distance and correlation.
        
        Args:
            score: Integration score (0-1)
            market_distance_km: Distance between markets
            correlation: Price correlation coefficient
            
        Returns:
            True if score is consistent
        """
        # Basic consistency checks
        if not 0 <= score <= 1:
            return False
        
        # Integration should decrease with distance
        if market_distance_km > 500 and score > 0.8:
            return False  # Unlikely to have high integration over long distances
        
        # Score should be related to correlation
        if abs(correlation) < 0.3 and score > 0.6:
            return False  # Low correlation shouldn't yield high integration
        
        if abs(correlation) > 0.8 and score < 0.4:
            return False  # High correlation should yield reasonable integration
        
        return True
    
    @staticmethod
    def validate_transmission_metrics(
        correlation: float,
        beta_coefficient: float,
        adjustment_speed: float,
        half_life_days: float
    ) -> bool:
        """
        Validate price transmission metrics for consistency.
        
        Args:
            correlation: Correlation coefficient
            beta_coefficient: Price transmission elasticity
            adjustment_speed: Speed of adjustment parameter
            half_life_days: Half-life of price shocks
            
        Returns:
            True if metrics are consistent
        """
        # Correlation should be between -1 and 1
        if not -1 <= correlation <= 1:
            return False
        
        # Beta coefficient should be positive for normal transmission
        if beta_coefficient < 0:
            return False
        
        # Adjustment speed should be between 0 and 1
        if not 0 <= adjustment_speed <= 1:
            return False
        
        # Half-life should be positive
        if half_life_days <= 0:
            return False
        
        # Consistency between adjustment speed and half-life
        # half_life = -ln(2) / ln(1 - speed)
        expected_half_life = -np.log(2) / np.log(1 - adjustment_speed) if adjustment_speed < 1 else float('inf')
        
        # Allow some tolerance (±50%)
        if adjustment_speed < 0.99:
            lower_bound = expected_half_life * 0.5
            upper_bound = expected_half_life * 1.5
            return lower_bound <= half_life_days <= upper_bound
        
        return True


class DataQualityValidator:
    """Comprehensive data quality validation."""
    
    @staticmethod
    def validate_completeness(
        data: Dict[str, Any],
        required_fields: List[str]
    ) -> List[str]:
        """
        Validate data completeness.
        
        Args:
            data: Data dictionary to validate
            required_fields: List of required field names
            
        Returns:
            List of missing fields
        """
        missing_fields = []
        
        for field in required_fields:
            if field not in data or data[field] is None:
                missing_fields.append(field)
            elif isinstance(data[field], str) and not data[field].strip():
                missing_fields.append(field)
        
        return missing_fields
    
    @staticmethod
    def validate_data_types(
        data: Dict[str, Any],
        type_mapping: Dict[str, type]
    ) -> List[str]:
        """
        Validate data types.
        
        Args:
            data: Data dictionary to validate
            type_mapping: Expected types for each field
            
        Returns:
            List of fields with incorrect types
        """
        type_errors = []
        
        for field, expected_type in type_mapping.items():
            if field in data and data[field] is not None:
                if not isinstance(data[field], expected_type):
                    type_errors.append(f"{field}: expected {expected_type.__name__}, got {type(data[field]).__name__}")
        
        return type_errors
    
    @staticmethod
    def validate_ranges(
        data: Dict[str, Any],
        range_mapping: Dict[str, tuple]
    ) -> List[str]:
        """
        Validate numeric ranges.
        
        Args:
            data: Data dictionary to validate
            range_mapping: (min, max) ranges for numeric fields
            
        Returns:
            List of fields outside valid ranges
        """
        range_errors = []
        
        for field, (min_val, max_val) in range_mapping.items():
            if field in data and data[field] is not None:
                try:
                    value = float(data[field])
                    if not min_val <= value <= max_val:
                        range_errors.append(f"{field}: {value} not in range [{min_val}, {max_val}]")
                except (ValueError, TypeError):
                    range_errors.append(f"{field}: cannot convert to numeric value")
        
        return range_errors