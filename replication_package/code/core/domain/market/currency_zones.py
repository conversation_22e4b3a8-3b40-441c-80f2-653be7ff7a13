"""Currency Zone domain model for multi-exchange rate analysis."""

from dataclasses import dataclass, field
from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Dict, Optional, List

from ..shared.entities import ValueObject, Entity
from ..shared.exceptions import ValidationException, BusinessRuleViolation
from .value_objects import Currency, ExchangeRate, ControlStatus, MarketId


class CurrencyZone(Enum):
    """Territorial control zones with different exchange rate regimes.
    
    Different zones have significantly different exchange rates,
    requiring careful currency conversion for valid price comparisons.
    """
    HOUTHI = "houthi"  # De facto Sana'a authority, ~535 YER/USD
    GOVERNMENT = "government"  # IRG Aden, 2000+ YER/USD  
    CONTESTED = "contested"  # Variable rates, often switching
    UNKNOWN = "unknown"  # No control data available


@dataclass(frozen=True)
class ZoneExchangeRate(ValueObject):
    """Exchange rate specific to a currency zone and date."""
    
    zone: CurrencyZone
    from_currency: Currency
    to_currency: Currency  
    rate: Decimal
    date: datetime
    rate_type: str  # "official_cby_aden", "official_cby_sanaa", "parallel"
    source: str
    confidence: float = 0.9  # Confidence in the rate accuracy
    
    def __post_init__(self):
        """Validate zone exchange rate."""
        if self.rate <= 0:
            raise ValidationException("Exchange rate must be positive")
        if not 0 <= self.confidence <= 1:
            raise ValidationException("Confidence must be between 0 and 1")
        
        # Validate rate type matches zone
        if self.zone == CurrencyZone.HOUTHI and "sanaa" not in self.rate_type:
            raise ValidationException("Houthi zone should use CBY Sanaa rates")
        if self.zone == CurrencyZone.GOVERNMENT and "aden" not in self.rate_type:
            raise ValidationException("Government zone should use CBY Aden rates")


@dataclass(frozen=True)
class CurrencyZoneMapping(ValueObject):
    """Maps markets to currency zones based on territorial control."""
    
    market_id: MarketId
    zone: CurrencyZone
    start_date: datetime
    end_date: Optional[datetime]
    confidence: float  # 0-1 confidence in mapping
    source: str  # e.g., "ACAPS_control_data"
    
    def __post_init__(self):
        """Validate zone mapping."""
        if not 0 <= self.confidence <= 1:
            raise ValidationException("Confidence must be between 0 and 1")
        if self.end_date and self.start_date >= self.end_date:
            raise ValidationException("Start date must be before end date")
    
    def is_active_at(self, date: datetime) -> bool:
        """Check if mapping is active at given date."""
        if date < self.start_date:
            return False
        if self.end_date and date > self.end_date:
            return False
        return True


@dataclass
class CurrencyFragmentationIndex(Entity):
    """Measures the degree of currency fragmentation between zones.
    
    This is a key metric for the Yemen Paradox - higher fragmentation
    means greater price distortions and aid inefficiency.
    """
    
    date: datetime = field(default_factory=datetime.now)
    houthi_rate: Decimal = field(default=Decimal("535"))  # YER/USD in Houthi areas
    government_rate: Decimal = field(default=Decimal("2000"))  # YER/USD in Government areas
    
    @property
    def fragmentation_ratio(self) -> float:
        """Calculate fragmentation as ratio of exchange rates."""
        return float(self.government_rate / self.houthi_rate)
    
    @property
    def fragmentation_percentage(self) -> float:
        """Calculate fragmentation as percentage difference."""
        return (self.fragmentation_ratio - 1) * 100
    
    @property
    def purchasing_power_gap(self) -> float:
        """Calculate relative purchasing power gap between zones.
        
        This directly translates to potential aid effectiveness gains.
        """
        # If aid is distributed in USD value terms, the purchasing power
        # in Houthi areas is artificially inflated by fragmentation_ratio
        return self.fragmentation_ratio
    
    def get_severity_level(self) -> str:
        """Categorize fragmentation severity."""
        ratio = self.fragmentation_ratio
        if ratio >= 4.0:
            return "extreme"  # 300%+ difference
        elif ratio >= 3.0:
            return "severe"   # 200-300% difference
        elif ratio >= 2.0:
            return "high"     # 100-200% difference
        elif ratio >= 1.5:
            return "moderate" # 50-100% difference
        elif ratio >= 1.2:
            return "low"      # 20-50% difference
        else:
            return "minimal"  # <20% difference


class CurrencyZoneService:
    """Service for currency zone operations and conversions."""
    
    def __init__(self):
        """Initialize with default zone rates."""
        # Default contemporary rates (can be overridden with actual data)
        self._default_rates = {
            CurrencyZone.HOUTHI: Decimal("535"),      # Stable CBY Sanaa rate
            CurrencyZone.GOVERNMENT: Decimal("2000"),  # Depreciated CBY Aden rate
            CurrencyZone.CONTESTED: Decimal("1200"),   # Average of both
            CurrencyZone.UNKNOWN: Decimal("1000")      # Conservative estimate
        }
    
    def get_zone_from_control_status(self, control_status: ControlStatus) -> CurrencyZone:
        """Map control status to currency zone."""
        mapping = {
            ControlStatus.HOUTHI: CurrencyZone.HOUTHI,
            ControlStatus.GOVERNMENT: CurrencyZone.GOVERNMENT,
            ControlStatus.CONTESTED: CurrencyZone.CONTESTED,
            ControlStatus.UNKNOWN: CurrencyZone.UNKNOWN
        }
        return mapping.get(control_status, CurrencyZone.UNKNOWN)
    
    def convert_price_to_usd(
        self,
        amount_yer: Decimal,
        zone: CurrencyZone,
        zone_rates: Optional[Dict[CurrencyZone, Decimal]] = None
    ) -> Decimal:
        """Convert YER price to USD using zone-specific rate.
        
        This is the KEY operation that reveals the Yemen Paradox.
        """
        rates = zone_rates or self._default_rates
        
        if zone not in rates:
            raise BusinessRuleViolation(f"No exchange rate available for zone {zone}")
        
        rate = rates[zone]
        return amount_yer / rate
    
    def calculate_zone_price_ratio(
        self,
        price_houthi_yer: Decimal,
        price_government_yer: Decimal,
        zone_rates: Optional[Dict[CurrencyZone, Decimal]] = None
    ) -> Dict[str, float]:
        """Calculate true price ratios after currency adjustment.
        
        This reveals whether conflict zones truly have lower prices.
        """
        # Convert both prices to USD
        price_houthi_usd = self.convert_price_to_usd(
            price_houthi_yer, CurrencyZone.HOUTHI, zone_rates
        )
        price_government_usd = self.convert_price_to_usd(
            price_government_yer, CurrencyZone.GOVERNMENT, zone_rates
        )
        
        return {
            "yer_ratio": float(price_houthi_yer / price_government_yer),
            "usd_ratio": float(price_houthi_usd / price_government_usd),
            "northern_zone_more_expensive_usd": float(price_houthi_usd / price_government_usd) > 1.0
        }
    
    def estimate_aid_effectiveness_gain(
        self,
        fragmentation_index: CurrencyFragmentationIndex,
        current_allocation: Dict[CurrencyZone, float]  # % of aid to each zone
    ) -> float:
        """Estimate potential aid effectiveness gains from proper currency awareness.
        
        Returns percentage improvement possible (typically 25-40%).
        """
        # Calculate current purchasing power weighted by allocation
        current_pp = (
            current_allocation.get(CurrencyZone.HOUTHI, 0) * fragmentation_index.fragmentation_ratio +
            current_allocation.get(CurrencyZone.GOVERNMENT, 0) * 1.0
        )
        
        # Optimal allocation would equalize real purchasing power
        # This is a simplified model - real optimization would consider
        # population, needs, and logistics
        optimal_pp = 1.0  # Baseline purchasing power
        
        # Potential gain from reallocation
        potential_gain = (current_pp - optimal_pp) / optimal_pp * 100
        
        # Typical gains are 25-40% based on research
        return min(max(potential_gain, 25.0), 40.0)