"""Repository interfaces for the Market bounded context."""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional

from .entities import Market, PriceObservation
from .value_objects import MarketId, Commodity


class MarketRepository(ABC):
    """Repository interface for Market aggregate."""
    
    @abstractmethod
    async def find_by_id(self, market_id: MarketId) -> Optional[Market]:
        """Find market by ID."""
        pass
    
    @abstractmethod
    async def find_by_ids(self, market_ids: List[MarketId]) -> List[Market]:
        """Find multiple markets by IDs."""
        pass
    
    @abstractmethod
    async def find_by_governorate(self, governorate: str) -> List[Market]:
        """Find all markets in a governorate."""
        pass
    
    @abstractmethod
    async def find_active_at(self, date: datetime) -> List[Market]:
        """Find all markets active at a given date."""
        pass
    
    @abstractmethod
    async def save(self, market: Market) -> None:
        """Save market aggregate."""
        pass
    
    @abstractmethod
    async def delete(self, market_id: MarketId) -> None:
        """Delete market by ID."""
        pass
    
    @abstractmethod
    async def find_all(self) -> List[Market]:
        """Find all markets."""
        pass


class PriceRepository(ABC):
    """Repository interface for price observations."""
    
    @abstractmethod
    async def find_by_market_and_commodity(
        self,
        market_id: MarketId,
        commodity: Commodity,
        start_date: datetime,
        end_date: datetime
    ) -> List[PriceObservation]:
        """Find price observations for a market and commodity within date range."""
        pass
    
    @abstractmethod
    async def find_by_markets_and_commodity(
        self,
        market_ids: List[MarketId],
        commodity: Commodity,
        start_date: datetime,
        end_date: datetime
    ) -> List[PriceObservation]:
        """Find price observations for multiple markets and a commodity."""
        pass
    
    @abstractmethod
    async def save(self, observation: PriceObservation) -> None:
        """Save price observation."""
        pass
    
    @abstractmethod
    async def save_batch(self, observations: List[PriceObservation]) -> None:
        """Save multiple price observations."""
        pass
    
    @abstractmethod
    async def delete_by_date_range(
        self,
        market_id: MarketId,
        start_date: datetime,
        end_date: datetime
    ) -> int:
        """Delete observations within date range. Returns count of deleted records."""
        pass
    
    @abstractmethod
    async def find_by_date_range(
        self,
        start_date: datetime,
        end_date: datetime,
        market_ids: Optional[List[MarketId]] = None,
        commodity_codes: Optional[List[str]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[PriceObservation]:
        """Find price observations within date range with optional filters."""
        pass
    
    @abstractmethod
    async def find_by_markets(
        self,
        market_ids: List[MarketId],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[PriceObservation]:
        """Find price observations for multiple markets."""
        pass
    
    @abstractmethod
    async def find_by_commodities(
        self,
        commodities: List[Commodity],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[PriceObservation]:
        """Find price observations for multiple commodities."""
        pass
    
    @abstractmethod
    async def find_commodity_by_code(self, code: str) -> Optional[Commodity]:
        """Find commodity by code."""
        pass
    
    @abstractmethod
    async def get_distinct_commodities(self) -> List[Commodity]:
        """Get all distinct commodities in the system."""
        pass


class CommodityRepository(ABC):
    """Repository interface for Commodity reference data."""
    
    @abstractmethod
    async def find_by_code(self, code: str) -> Optional[Commodity]:
        """Find commodity by code."""
        pass
    
    @abstractmethod
    async def find_all(self) -> List[Commodity]:
        """Find all commodities."""
        pass
    
    @abstractmethod
    async def save(self, commodity: Commodity) -> None:
        """Save commodity."""
        pass