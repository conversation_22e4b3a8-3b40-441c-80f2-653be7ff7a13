"""Market entities - core business objects."""

from dataclasses import dataclass, field
from datetime import datetime
from decimal import Decimal
from typing import List, Optional, TYPE_CHECKING
from uuid import UUID

if TYPE_CHECKING:
    import pandas as pd

from ..shared.entities import AggregateRoot, Entity
from ..shared.events import DomainEvent
from ..shared.exceptions import BusinessRuleViolation, ValidationException
from .value_objects import (
    MarketId, Coordinates, MarketType, Price, Commodity,
    ControlStatus, Currency, ExchangeRate
)


@dataclass
class MarketCreatedEvent(DomainEvent):
    """Event raised when a market is created."""
    
    market_id: MarketId = None
    name: str = ""
    market_type: MarketType = MarketType.RETAIL


@dataclass
class MarketDeactivatedEvent(DomainEvent):
    """Event raised when a market is deactivated."""
    
    market_id: MarketId = None
    reason: str = ""
    deactivated_at: datetime = None


@dataclass
class ControlStatusChangedEvent(DomainEvent):
    """Event raised when market control status changes."""
    
    market_id: MarketId = None
    previous_status: ControlStatus = ControlStatus.UNKNOWN
    new_status: ControlStatus = ControlStatus.UNKNOWN
    changed_at: datetime = None


@dataclass
class Market(AggregateRoot):
    """Market aggregate root - represents a physical market location."""
    
    market_id: MarketId = None
    name: str = ""
    coordinates: Coordinates = None
    market_type: MarketType = MarketType.RETAIL
    governorate: str = ""
    district: str = ""
    active_since: datetime = None
    active_until: Optional[datetime] = None
    control_status: ControlStatus = ControlStatus.UNKNOWN
    control_status_date: Optional[datetime] = None
    
    def __post_init__(self) -> None:
        """Initialize market and raise creation event."""
        self._validate()
        self.add_event(MarketCreatedEvent(
            aggregate_id=self.id,
            market_id=self.market_id,
            name=self.name,
            market_type=self.market_type
        ))
    
    def _validate(self) -> None:
        """Validate market state."""
        if not self.name:
            raise ValidationException("Market name is required")
        if not self.governorate:
            raise ValidationException("Governorate is required")
        if not self.district:
            raise ValidationException("District is required")
        if self.active_since > datetime.utcnow():
            raise ValidationException("Active since date cannot be in the future")
        if self.active_until and self.active_until <= self.active_since:
            raise ValidationException("Active until must be after active since")
    
    def deactivate(self, reason: str) -> None:
        """Deactivate market with reason."""
        if self.active_until is not None:
            raise BusinessRuleViolation("Market is already deactivated")
        
        self.active_until = datetime.utcnow()
        self.add_event(MarketDeactivatedEvent(
            aggregate_id=self.id,
            market_id=self.market_id,
            reason=reason,
            deactivated_at=self.active_until
        ))
    
    def is_active_at(self, date: datetime) -> bool:
        """Check if market was active at given date."""
        if date < self.active_since:
            return False
        if self.active_until and date > self.active_until:
            return False
        return True
    
    def can_trade_commodity(self, commodity: Commodity) -> bool:
        """Check if market can trade given commodity based on market type."""
        # Business rule: Port markets can only trade imported commodities
        if self.market_type == MarketType.PORT:
            return commodity.category == "imported"
        
        # Business rule: Rural markets have limited commodity types
        if self.market_type == MarketType.RURAL:
            return commodity.category in ["local", "agricultural"]
        
        # Other markets can trade all commodities
        return True
    
    def update_control_status(self, new_status: ControlStatus, changed_at: Optional[datetime] = None) -> None:
        """Update market control status."""
        if changed_at is None:
            changed_at = datetime.utcnow()
        
        if changed_at < self.active_since:
            raise BusinessRuleViolation("Control status change date cannot be before market activation")
        
        if self.control_status != new_status:
            previous_status = self.control_status
            self.control_status = new_status
            self.control_status_date = changed_at
            
            self.add_event(ControlStatusChangedEvent(
                aggregate_id=self.id,
                market_id=self.market_id,
                previous_status=previous_status,
                new_status=new_status,
                changed_at=changed_at
            ))


@dataclass
class PriceObservation(Entity):
    """Price observation entity - represents a single price point."""
    
    market_id: MarketId = None
    commodity: Commodity = None
    price: Price = None
    observed_date: datetime = None
    source: str = ""
    quality: str = "standard"
    observations_count: int = 1
    
    def __post_init__(self) -> None:
        """Validate price observation."""
        if self.observed_date > datetime.utcnow():
            raise ValidationException("Observation date cannot be in the future")
        if self.observations_count < 1:
            raise ValidationException("Observations count must be at least 1")
        if self.quality not in ["high", "standard", "low"]:
            raise ValidationException(f"Invalid quality: {self.quality}")
    
    def is_outlier(self, mean_price: Decimal, std_dev: Decimal, threshold: float = 3.0) -> bool:
        """Check if price is an outlier based on z-score."""
        if std_dev == 0:
            return False
        
        z_score = abs(float(self.price.amount - mean_price) / float(std_dev))
        return z_score > threshold
    
    def merge_with(self, other: 'PriceObservation') -> 'PriceObservation':
        """Merge with another observation of the same commodity and date."""
        if self.commodity != other.commodity:
            raise BusinessRuleViolation("Cannot merge observations of different commodities")
        if self.observed_date != other.observed_date:
            raise BusinessRuleViolation("Cannot merge observations from different dates")
        if self.market_id != other.market_id:
            raise BusinessRuleViolation("Cannot merge observations from different markets")
        
        # Calculate weighted average price
        total_count = self.observations_count + other.observations_count
        weighted_price = (
            (self.price.amount * self.observations_count +
             other.price.amount * other.observations_count) /
            total_count
        )
        
        return PriceObservation(
            market_id=self.market_id,
            commodity=self.commodity,
            price=Price(
                amount=weighted_price,
                currency=self.price.currency,
                unit=self.price.unit
            ),
            observed_date=self.observed_date,
            source=f"{self.source},{other.source}",
            quality=min(self.quality, other.quality),  # Conservative quality estimate
            observations_count=total_count
        )


@dataclass
class ExchangeRateObservation(Entity):
    """Exchange rate observation entity - tracks currency exchange rates."""
    
    market_id: MarketId = None
    exchange_rate: ExchangeRate = None
    observed_date: datetime = None
    source: str = ""
    black_market_premium: Optional[Decimal] = None
    
    def __post_init__(self) -> None:
        """Validate exchange rate observation."""
        if self.observed_date > datetime.utcnow():
            raise ValidationException("Observation date cannot be in the future")
        if not self.source:
            raise ValidationException("Source is required")
        if self.black_market_premium is not None and self.black_market_premium < 0:
            raise ValidationException("Black market premium cannot be negative")
    
    def get_effective_rate(self) -> Decimal:
        """Get effective exchange rate including black market premium."""
        if self.black_market_premium is None:
            return self.exchange_rate.rate
        return self.exchange_rate.rate * (Decimal("1") + self.black_market_premium)


@dataclass
class PanelData(Entity):
    """Panel data entity - represents a collection of observations for econometric analysis."""
    
    observations: List[PriceObservation] = field(default_factory=list)
    markets: List[Market] = field(default_factory=list)
    start_date: datetime = None
    end_date: datetime = None
    commodity_filter: Optional[List[str]] = None
    
    def __post_init__(self) -> None:
        """Validate panel data structure."""
        if not self.observations:
            raise ValidationException("Panel data must contain observations")
        if not self.markets:
            raise ValidationException("Panel data must contain markets")
        if self.start_date and self.end_date and self.start_date >= self.end_date:
            raise ValidationException("Start date must be before end date")
    
    def get_unique_markets(self) -> List[MarketId]:
        """Get unique market IDs from observations."""
        return list(set(obs.market_id for obs in self.observations))
    
    def get_unique_commodities(self) -> List[Commodity]:
        """Get unique commodities from observations."""
        return list(set(obs.commodity for obs in self.observations))
    
    def get_observations_for_market(self, market_id: MarketId) -> List[PriceObservation]:
        """Get all observations for a specific market."""
        return [obs for obs in self.observations if obs.market_id == market_id]
    
    def get_observations_for_commodity(self, commodity: Commodity) -> List[PriceObservation]:
        """Get all observations for a specific commodity."""
        return [obs for obs in self.observations if obs.commodity == commodity]
    
    def filter_by_date_range(self, start_date: datetime, end_date: datetime) -> 'PanelData':
        """Create new PanelData filtered by date range."""
        filtered_obs = [
            obs for obs in self.observations
            if start_date <= obs.observed_date <= end_date
        ]
        
        return PanelData(
            observations=filtered_obs,
            markets=self.markets,
            start_date=start_date,
            end_date=end_date,
            commodity_filter=self.commodity_filter
        )
    
    def to_dataframe(self) -> 'pd.DataFrame':
        """Convert panel data to DataFrame for analysis."""
        import pandas as pd
        
        data = []
        for obs in self.observations:
            # Find market details
            market = next((m for m in self.markets if m.market_id == obs.market_id), None)
            
            row = {
                'market_id': str(obs.market_id.value),
                'date': obs.observed_date,
                'commodity_code': obs.commodity.code,
                'commodity_name': obs.commodity.name,
                'price': float(obs.price.amount),
                'currency': obs.price.currency.value,
                'unit': obs.price.unit,
                'source': obs.source,
                'quality': obs.quality,
                'observations_count': obs.observations_count
            }
            
            # Add market information if available
            if market:
                row.update({
                    'market_name': market.name,
                    'governorate': market.governorate,
                    'district': market.district,
                    'latitude': float(market.coordinates.latitude),
                    'longitude': float(market.coordinates.longitude),
                    'market_type': market.market_type.value,
                    'control_status': market.control_status.value if market.control_status else None,
                    'control_status_date': market.control_status_date
                })
            
            data.append(row)
        
        return pd.DataFrame(data)
    
    @classmethod
    def from_observations(
        cls,
        observations: List[PriceObservation],
        markets: List[Market],
        commodity_filter: Optional[List[str]] = None
    ) -> 'PanelData':
        """Create PanelData from observations and markets."""
        if not observations:
            raise ValidationException("Cannot create panel data without observations")
        
        # Filter observations by commodity if specified
        if commodity_filter:
            filtered_obs = [
                obs for obs in observations
                if obs.commodity.code in commodity_filter
            ]
        else:
            filtered_obs = observations
        
        # Calculate date range
        start_date = min(obs.observed_date for obs in filtered_obs)
        end_date = max(obs.observed_date for obs in filtered_obs)
        
        return cls(
            observations=filtered_obs,
            markets=markets,
            start_date=start_date,
            end_date=end_date,
            commodity_filter=commodity_filter
        )