"""Authentication domain entities."""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID, uuid4

from pydantic import Field

from ..shared.entities import Entity, AggregateRoot
from .value_objects import User<PERSON>ole, UserStatus, Email, HashedPassword


class User(AggregateRoot):
    """User aggregate root."""
    
    username: str
    email: Email
    password_hash: HashedPassword
    full_name: Optional[str] = None
    roles: List[UserRole] = Field(default_factory=list)
    status: UserStatus = UserStatus.ACTIVE
    last_login: Optional[datetime] = None
    failed_login_attempts: int = 0
    locked_until: Optional[datetime] = None
    email_verified: bool = False
    email_verification_token: Optional[str] = None
    password_reset_token: Optional[str] = None
    password_reset_expires: Optional[datetime] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    def __init__(self, **data):
        """Initialize user with default ID if not provided."""
        if 'id' not in data:
            data['id'] = uuid4()
        super().__init__(**data)
    
    def can_login(self) -> tuple[bool, Optional[str]]:
        """Check if user can login."""
        if self.status == UserStatus.INACTIVE:
            return False, "User account is inactive"
        
        if self.status == UserStatus.SUSPENDED:
            return False, "User account is suspended"
        
        if self.locked_until and self.locked_until > datetime.utcnow():
            return False, f"Account locked until {self.locked_until}"
        
        return True, None
    
    def record_successful_login(self) -> None:
        """Record a successful login."""
        self.last_login = datetime.utcnow()
        self.failed_login_attempts = 0
        self.locked_until = None
    
    def record_failed_login(self, max_attempts: int = 5, lockout_minutes: int = 30) -> None:
        """Record a failed login attempt."""
        self.failed_login_attempts += 1
        
        if self.failed_login_attempts >= max_attempts:
            from datetime import timedelta
            self.locked_until = datetime.utcnow() + timedelta(minutes=lockout_minutes)
    
    def has_role(self, role: UserRole) -> bool:
        """Check if user has a specific role."""
        return role in self.roles
    
    def has_any_role(self, roles: List[UserRole]) -> bool:
        """Check if user has any of the specified roles."""
        return any(role in self.roles for role in roles)
    
    def add_role(self, role: UserRole) -> None:
        """Add a role to the user."""
        if role not in self.roles:
            self.roles.append(role)
    
    def remove_role(self, role: UserRole) -> None:
        """Remove a role from the user."""
        if role in self.roles:
            self.roles.remove(role)
    
    def initiate_password_reset(self) -> str:
        """Initiate password reset process."""
        import secrets
        from datetime import timedelta
        
        self.password_reset_token = secrets.token_urlsafe(32)
        self.password_reset_expires = datetime.utcnow() + timedelta(hours=24)
        
        return self.password_reset_token
    
    def complete_password_reset(self, new_password_hash: HashedPassword) -> bool:
        """Complete password reset."""
        if not self.password_reset_token:
            return False
        
        if self.password_reset_expires and self.password_reset_expires < datetime.utcnow():
            return False
        
        self.password_hash = new_password_hash
        self.password_reset_token = None
        self.password_reset_expires = None
        
        return True
    
    def initiate_email_verification(self) -> str:
        """Initiate email verification."""
        import secrets
        
        self.email_verification_token = secrets.token_urlsafe(32)
        return self.email_verification_token
    
    def verify_email(self, token: str) -> bool:
        """Verify email address."""
        if self.email_verification_token == token:
            self.email_verified = True
            self.email_verification_token = None
            return True
        return False


class APIKey(Entity):
    """API Key entity."""
    
    name: str
    key_hash: str
    key_prefix: str
    user_id: Optional[UUID] = None
    permissions: List[str] = Field(default_factory=list)
    expires_at: Optional[datetime] = None
    last_used_at: Optional[datetime] = None
    is_active: bool = True
    rate_limit: Optional[int] = None  # Requests per minute
    allowed_ips: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    usage_count: int = 0
    
    def is_valid(self) -> bool:
        """Check if API key is valid."""
        if not self.is_active:
            return False
        
        if self.expires_at and self.expires_at < datetime.utcnow():
            return False
        
        return True
    
    def record_usage(self) -> None:
        """Record API key usage."""
        self.last_used_at = datetime.utcnow()
        self.usage_count += 1
    
    def is_ip_allowed(self, ip_address: str) -> bool:
        """Check if IP address is allowed."""
        if not self.allowed_ips:
            return True  # No restrictions
        
        return ip_address in self.allowed_ips


class RefreshToken(Entity):
    """Refresh token entity."""
    
    token_hash: str
    user_id: UUID
    expires_at: datetime
    is_revoked: bool = False
    device_info: Optional[str] = None
    ip_address: Optional[str] = None
    
    def is_valid(self) -> bool:
        """Check if refresh token is valid."""
        if self.is_revoked:
            return False
        
        if self.expires_at < datetime.utcnow():
            return False
        
        return True
    
    def revoke(self) -> None:
        """Revoke the refresh token."""
        self.is_revoked = True