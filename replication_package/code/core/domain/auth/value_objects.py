"""Authentication value objects."""

import re
from enum import Enum
from typing import Any

from pydantic import BaseModel, validator


class UserRole(str, Enum):
    """User roles matching RBAC system."""
    ADMIN = "admin"
    ANALYST = "analyst"
    VIEWER = "viewer"
    API_USER = "api_user"
    POLICY_MAKER = "policy_maker"


class UserStatus(str, Enum):
    """User account status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending"


class Email(BaseModel):
    """Email value object with validation."""
    value: str
    
    @validator('value')
    def validate_email(cls, v: str) -> str:
        """Validate email format."""
        email_regex = re.compile(
            r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        )
        if not email_regex.match(v):
            raise ValueError("Invalid email format")
        return v.lower()
    
    def __str__(self) -> str:
        return self.value
    
    def __eq__(self, other: Any) -> bool:
        if isinstance(other, Email):
            return self.value == other.value
        return False
    
    def __hash__(self) -> int:
        return hash(self.value)
    
    @property
    def domain(self) -> str:
        """Get email domain."""
        return self.value.split('@')[1]
    
    @property
    def local_part(self) -> str:
        """Get local part of email."""
        return self.value.split('@')[0]


class HashedPassword(BaseModel):
    """Hashed password value object."""
    value: str
    
    @validator('value')
    def validate_hash(cls, v: str) -> str:
        """Validate that this looks like a bcrypt hash."""
        if not v.startswith('$2b$') or len(v) < 60:
            raise ValueError("Invalid password hash format")
        return v
    
    def __str__(self) -> str:
        return "***REDACTED***"
    
    def __eq__(self, other: Any) -> bool:
        if isinstance(other, HashedPassword):
            return self.value == other.value
        return False