"""Domain services for the Conflict bounded context."""

from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

from ..market.entities import Market
from ..market.value_objects import Coordinates
from ..shared.exceptions import BusinessRuleViolation
from .entities import ConflictEvent
from .value_objects import ConflictType, ConflictIntensity


@dataclass
class ConflictImpactMetrics:
    """Metrics for conflict impact on a market."""
    
    market_id: str
    total_events: int
    immediate_impacts: int
    moderate_impacts: int
    marginal_impacts: int
    average_intensity: float
    conflict_days: int
    max_intensity: ConflictIntensity
    dominant_type: ConflictType


@dataclass
class ConflictCluster:
    """Represents a cluster of related conflict events."""
    
    center: Coordinates
    events: List[ConflictEvent]
    radius_km: float
    start_date: datetime
    end_date: datetime
    total_fatalities: int
    
    @property
    def duration_days(self) -> int:
        """Get cluster duration in days."""
        return (self.end_date - self.start_date).days + 1
    
    @property
    def intensity(self) -> ConflictIntensity:
        """Get overall cluster intensity."""
        return ConflictIntensity.from_fatalities(self.total_fatalities)


class ConflictAnalysisService:
    """Domain service for analyzing conflict impacts on markets."""
    
    def calculate_market_exposure(
        self,
        market: Market,
        conflicts: List[ConflictEvent],
        analysis_period: Tuple[datetime, datetime]
    ) -> ConflictImpactMetrics:
        """Calculate conflict exposure metrics for a market."""
        start_date, end_date = analysis_period
        
        # Filter conflicts within analysis period
        period_conflicts = [
            c for c in conflicts
            if start_date <= c.event_date <= end_date
        ]
        
        if not period_conflicts:
            return ConflictImpactMetrics(
                market_id=str(market.market_id.value),
                total_events=0,
                immediate_impacts=0,
                moderate_impacts=0,
                marginal_impacts=0,
                average_intensity=0.0,
                conflict_days=0,
                max_intensity=ConflictIntensity.LOW,
                dominant_type=ConflictType.BATTLE
            )
        
        # Count impacts by level
        immediate = 0
        moderate = 0
        marginal = 0
        intensities = []
        conflict_types = []
        conflict_dates = set()
        
        for conflict in period_conflicts:
            impact_level = conflict.get_impact_level(market.coordinates)
            
            if impact_level == "immediate":
                immediate += 1
            elif impact_level == "moderate":
                moderate += 1
            elif impact_level == "marginal":
                marginal += 1
            
            if impact_level != "none":
                intensities.append(list(ConflictIntensity).index(conflict.intensity))
                conflict_types.append(conflict.conflict_type)
                conflict_dates.add(conflict.event_date.date())
        
        # Calculate metrics
        avg_intensity = sum(intensities) / len(intensities) if intensities else 0
        max_intensity_idx = max(intensities) if intensities else 0
        max_intensity = list(ConflictIntensity)[max_intensity_idx]
        
        # Find dominant conflict type
        type_counts = {}
        for ct in conflict_types:
            type_counts[ct] = type_counts.get(ct, 0) + 1
        dominant_type = max(type_counts.items(), key=lambda x: x[1])[0] if type_counts else ConflictType.BATTLE
        
        return ConflictImpactMetrics(
            market_id=str(market.market_id.value),
            total_events=len(period_conflicts),
            immediate_impacts=immediate,
            moderate_impacts=moderate,
            marginal_impacts=marginal,
            average_intensity=avg_intensity,
            conflict_days=len(conflict_dates),
            max_intensity=max_intensity,
            dominant_type=dominant_type
        )
    
    def identify_conflict_clusters(
        self,
        conflicts: List[ConflictEvent],
        spatial_threshold_km: float = 50.0,
        temporal_threshold_days: int = 7
    ) -> List[ConflictCluster]:
        """Identify clusters of related conflict events."""
        if not conflicts:
            return []
        
        # Sort conflicts by date
        sorted_conflicts = sorted(conflicts, key=lambda c: c.event_date)
        clusters = []
        used_conflicts = set()
        
        for i, conflict in enumerate(sorted_conflicts):
            if i in used_conflicts:
                continue
            
            # Start new cluster
            cluster_events = [conflict]
            used_conflicts.add(i)
            
            # Find related conflicts
            for j, other_conflict in enumerate(sorted_conflicts[i+1:], i+1):
                if j in used_conflicts:
                    continue
                
                # Check temporal proximity
                time_diff = (other_conflict.event_date - conflict.event_date).days
                if time_diff > temporal_threshold_days:
                    break  # No more temporally related conflicts
                
                # Check spatial proximity to any event in cluster
                spatially_related = any(
                    event.distance_to_point(other_conflict.location) <= spatial_threshold_km
                    for event in cluster_events
                )
                
                if spatially_related:
                    cluster_events.append(other_conflict)
                    used_conflicts.add(j)
            
            # Create cluster if multiple events
            if len(cluster_events) > 1:
                clusters.append(self._create_cluster(cluster_events))
        
        return clusters
    
    def assess_market_vulnerability(
        self,
        market: Market,
        historical_conflicts: List[ConflictEvent],
        lookback_days: int = 365
    ) -> Dict[str, float]:
        """Assess market vulnerability based on historical conflict exposure."""
        cutoff_date = datetime.utcnow() - timedelta(days=lookback_days)
        recent_conflicts = [
            c for c in historical_conflicts
            if c.event_date >= cutoff_date
        ]
        
        if not recent_conflicts:
            return {
                "vulnerability_score": 0.0,
                "exposure_frequency": 0.0,
                "average_proximity": float('inf'),
                "intensity_score": 0.0
            }
        
        # Calculate exposure metrics
        exposures = []
        distances = []
        intensities = []
        
        for conflict in recent_conflicts:
            distance = conflict.distance_to_point(market.coordinates)
            impact_level = conflict.get_impact_level(market.coordinates)
            
            if impact_level != "none":
                exposures.append(1)
                distances.append(distance)
                intensities.append(list(ConflictIntensity).index(conflict.intensity))
            else:
                exposures.append(0)
        
        # Calculate vulnerability components
        exposure_frequency = sum(exposures) / lookback_days  # Events per day
        avg_proximity = sum(distances) / len(distances) if distances else float('inf')
        intensity_score = sum(intensities) / len(intensities) if intensities else 0.0
        
        # Normalize intensity score (0-1)
        max_intensity = len(ConflictIntensity) - 1
        intensity_score = intensity_score / max_intensity
        
        # Calculate composite vulnerability score
        # Weight: 40% frequency, 30% proximity, 30% intensity
        proximity_score = 1.0 - min(avg_proximity / 100.0, 1.0)  # Normalize to 0-1
        vulnerability_score = (
            0.4 * min(exposure_frequency * 365, 1.0) +  # Normalize frequency
            0.3 * proximity_score +
            0.3 * intensity_score
        )
        
        return {
            "vulnerability_score": vulnerability_score,
            "exposure_frequency": exposure_frequency,
            "average_proximity": avg_proximity,
            "intensity_score": intensity_score
        }
    
    def _create_cluster(self, events: List[ConflictEvent]) -> ConflictCluster:
        """Create a conflict cluster from events."""
        # Calculate cluster center (centroid)
        avg_lat = sum(e.location.latitude for e in events) / len(events)
        avg_lon = sum(e.location.longitude for e in events) / len(events)
        center = Coordinates(latitude=avg_lat, longitude=avg_lon)
        
        # Calculate cluster radius
        max_distance = max(
            center.distance_to(e.location)
            for e in events
        )
        
        # Get date range
        start_date = min(e.event_date for e in events)
        end_date = max(e.event_date for e in events)
        
        # Sum fatalities
        total_fatalities = sum(e.fatalities for e in events)
        
        return ConflictCluster(
            center=center,
            events=events,
            radius_km=max_distance,
            start_date=start_date,
            end_date=end_date,
            total_fatalities=total_fatalities
        )