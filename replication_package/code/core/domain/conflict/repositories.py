"""Repository interfaces for the Conflict bounded context."""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional

from .entities import ConflictEvent


class ConflictEventRepository(ABC):
    """Repository interface for ConflictEvent aggregate."""
    
    @abstractmethod
    async def find_by_id(self, event_id: str) -> Optional[ConflictEvent]:
        """Find conflict event by ID."""
        pass
    
    @abstractmethod
    async def find_by_governorate_and_date_range(
        self,
        governorate: str,
        start_date: datetime,
        end_date: datetime
    ) -> List[ConflictEvent]:
        """Find conflict events in a governorate within date range."""
        pass
    
    @abstractmethod
    async def find_by_location_radius(
        self,
        latitude: float,
        longitude: float,
        radius_km: float,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[ConflictEvent]:
        """Find conflict events within radius of a location."""
        pass
    
    @abstractmethod
    async def save(self, event: ConflictEvent) -> None:
        """Save conflict event."""
        pass
    
    @abstractmethod
    async def save_batch(self, events: List[ConflictEvent]) -> None:
        """Save multiple conflict events."""
        pass
    
    @abstractmethod
    async def delete(self, event_id: str) -> None:
        """Delete conflict event by ID."""
        pass