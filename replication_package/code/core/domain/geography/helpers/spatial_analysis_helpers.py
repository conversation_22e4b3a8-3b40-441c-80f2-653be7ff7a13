"""Helper functions for spatial analysis."""

from typing import Dict, Any, List, Optional, Set, Tuple
import numpy as np
import pandas as pd
from scipy.spatial import distance_matrix

from ..entities import District, Governorate
from ...market.entities import Market
from ...market.value_objects import Coordinates, MarketId
from ...conflict.entities import ConflictEvent


def calculate_distance_matrix(coords: np.ndarray) -> np.ndarray:
    """Calculate pairwise distance matrix between coordinates."""
    # Convert to radians
    coords_rad = np.radians(coords)
    
    # Calculate pairwise distances using Haversine formula
    n_points = len(coords)
    distances = np.zeros((n_points, n_points))
    
    for i in range(n_points):
        for j in range(i + 1, n_points):
            dlat = coords_rad[j, 0] - coords_rad[i, 0]
            dlon = coords_rad[j, 1] - coords_rad[i, 1]
            
            a = (np.sin(dlat/2)**2 + 
                 np.cos(coords_rad[i, 0]) * np.cos(coords_rad[j, 0]) * 
                 np.sin(dlon/2)**2)
            c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1-a))
            
            dist_km = 6371 * c  # Earth radius in km
            distances[i, j] = distances[j, i] = dist_km
    
    return distances


def create_spatial_cluster(markets: List[Market]) -> Dict[str, Any]:
    """Create a spatial cluster from markets."""
    # Calculate cluster center (centroid)
    avg_lat = np.mean([m.coordinates.latitude for m in markets])
    avg_lon = np.mean([m.coordinates.longitude for m in markets])
    center = Coordinates(latitude=avg_lat, longitude=avg_lon)
    
    # Calculate cluster radius
    max_distance = max(
        center.distance_to(m.coordinates)
        for m in markets
    )
    
    # Calculate density (markets per 100 km²)
    area = np.pi * max_distance**2
    density = len(markets) / (area / 100) if area > 0 else 0
    
    return {
        'center': center,
        'markets': markets,
        'radius_km': max_distance,
        'density': density
    }


def calculate_control_zone_access(
    market: Market,
    other_markets: List[Market],
    governorates: List[Governorate]
) -> Dict[str, float]:
    """Calculate accessibility to different control zones."""
    market_gov = next(
        (g for g in governorates if g.name == market.governorate),
        None
    )
    
    if not market_gov:
        return {}
    
    zone_markets = {"IRG": [], "DFA": [], "Contested": []}
    
    for other in other_markets:
        other_gov = next(
            (g for g in governorates if g.name == other.governorate),
            None
        )
        if other_gov:
            zone_markets[other_gov.control_authority].append(other)
    
    zone_access = {}
    for zone, markets_in_zone in zone_markets.items():
        if markets_in_zone:
            distances = [
                market.coordinates.distance_to(m.coordinates)
                for m in markets_in_zone
            ]
            avg_distance = np.mean(distances)
            zone_access[zone] = 1.0 / (1.0 + avg_distance / 100.0)
        else:
            zone_access[zone] = 0.0
    
    return zone_access
