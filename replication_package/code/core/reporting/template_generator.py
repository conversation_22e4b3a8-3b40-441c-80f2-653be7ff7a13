"""
Results Template Generator

Generates results sections based on actual findings, not predetermined text.
Prevents predetermined outcomes and ensures scientific integrity.
"""

import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum


class TemplateType(Enum):
    """Types of result templates."""
    EXCHANGE_RATE = "exchange_rate"
    COMMODITY = "commodity"
    INTEGRATION = "integration"
    CONFLICT = "conflict"


class EffectSize(Enum):
    """Effect size classifications."""
    NEGLIGIBLE = "negligible"
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"


@dataclass
class TestResult:
    """Container for statistical test results."""
    test_name: str
    test_statistic: float
    p_value: float
    effect_size: float
    confidence_interval: tuple
    interpretation: str
    robustness_checks: List[Dict[str, Any]]


@dataclass
class IntegrationMetrics:
    """Container for market integration metrics."""
    speed_of_adjustment: Optional[float]
    half_life: Optional[float]
    r_squared: Optional[float]
    pass_through: Optional[float]
    error_correction_coef: Optional[float]


class ResultsTemplateGenerator:
    """Generate results sections based on actual findings, not predetermined text."""
    
    # Forbidden phrases that suggest predetermined outcomes
    FORBIDDEN_PHRASES = [
        "revolutionary discovery",
        "game-changing",
        "paradigm shift", 
        "as expected",
        "confirms our hypothesis",
        "groundbreaking",
        "validates our theory",
        "proves conclusively",
        "demonstrates unequivocally"
    ]
    
    # Effect size thresholds
    EFFECT_SIZE_THRESHOLDS = {
        EffectSize.NEGLIGIBLE: 0.05,
        EffectSize.SMALL: 0.20,
        EffectSize.MEDIUM: 0.50,
        EffectSize.LARGE: 0.80
    }
    
    # Integration strength thresholds
    INTEGRATION_THRESHOLDS = {
        'strong': {'speed_min': 0.5, 'half_life_max': 3},
        'moderate': {'speed_min': 0.2, 'half_life_max': 12},
        'weak': {'speed_min': 0.0, 'half_life_max': float('inf')}
    }

    def __init__(self):
        self.templates = {}
        self._load_templates()

    def _load_templates(self):
        """Load template configurations."""
        self.templates = {
            'null_result': self._get_null_result_template(),
            'significant_result': self._get_significant_result_template(),
            'mixed_result': self._get_mixed_result_template(),
            'integration_strong': self._get_strong_integration_template(),
            'integration_weak': self._get_weak_integration_template()
        }

    def generate_results_section(
        self, 
        test_results: List[TestResult],
        hypothesis_id: str,
        template_type: TemplateType,
        integration_metrics: Optional[IntegrationMetrics] = None
    ) -> str:
        """
        Generate results section based on actual findings.
        
        Args:
            test_results: List of statistical test results
            hypothesis_id: Hypothesis being tested (e.g., "H1", "H2")
            template_type: Type of analysis template
            integration_metrics: Market integration metrics if applicable
            
        Returns:
            Formatted results section with placeholders filled
        """
        # Validate inputs
        self._validate_inputs(test_results, hypothesis_id)
        
        # Determine result type
        result_type = self._classify_results(test_results, integration_metrics)
        
        # Generate appropriate section
        if result_type == 'null':
            return self._generate_null_results_section(test_results, hypothesis_id)
        elif result_type == 'significant':
            return self._generate_significant_results_section(
                test_results, hypothesis_id, template_type, integration_metrics
            )
        elif result_type == 'mixed':
            return self._generate_mixed_results_section(
                test_results, hypothesis_id, template_type
            )
        else:
            raise ValueError(f"Unknown result type: {result_type}")

    def _validate_inputs(self, test_results: List[TestResult], hypothesis_id: str):
        """Validate input parameters."""
        if not test_results:
            raise ValueError("At least one test result required")
        
        if not hypothesis_id:
            raise ValueError("Hypothesis ID required")
        
        # Check for forbidden predetermined language in interpretations
        for result in test_results:
            self._ensure_no_predetermined_text(result.interpretation)

    def _ensure_no_predetermined_text(self, text: str):
        """Check for predetermined language that suggests bias."""
        text_lower = text.lower()
        for phrase in self.FORBIDDEN_PHRASES:
            if phrase in text_lower:
                raise ValueError(
                    f"Predetermined language detected: '{phrase}'. "
                    f"Results must be based on data, not expectations."
                )

    def _classify_results(
        self, 
        test_results: List[TestResult],
        integration_metrics: Optional[IntegrationMetrics]
    ) -> str:
        """Classify overall result pattern."""
        significant_count = sum(1 for r in test_results if r.p_value < 0.05)
        total_tests = len(test_results)
        
        if significant_count == 0:
            return 'null'
        elif significant_count == total_tests:
            return 'significant'
        else:
            return 'mixed'

    def _generate_null_results_section(
        self, 
        test_results: List[TestResult],
        hypothesis_id: str
    ) -> str:
        """Generate section for null results."""
        template = self.templates['null_result']
        
        # Get primary test result
        primary_result = test_results[0]
        
        # Fill template
        section = template.format(
            hypothesis_id=hypothesis_id,
            test_statistic=primary_result.test_statistic,
            p_value=primary_result.p_value,
            ci_lower=primary_result.confidence_interval[0],
            ci_upper=primary_result.confidence_interval[1],
            interpretation=self._generate_null_interpretation(primary_result)
        )
        
        return section

    def _generate_significant_results_section(
        self,
        test_results: List[TestResult],
        hypothesis_id: str,
        template_type: TemplateType,
        integration_metrics: Optional[IntegrationMetrics]
    ) -> str:
        """Generate section for significant results."""
        template = self.templates['significant_result']
        
        # Get primary result
        primary_result = test_results[0]
        
        # Classify effect size
        effect_classification = self._classify_effect_size(primary_result.effect_size)
        
        # Generate integration assessment if applicable
        integration_assessment = ""
        if integration_metrics:
            integration_assessment = self._assess_integration_strength(integration_metrics)
        
        section = template.format(
            hypothesis_id=hypothesis_id,
            test_statistic=primary_result.test_statistic,
            p_value=primary_result.p_value,
            effect_size=primary_result.effect_size,
            effect_classification=effect_classification.value,
            ci_lower=primary_result.confidence_interval[0],
            ci_upper=primary_result.confidence_interval[1],
            integration_assessment=integration_assessment
        )
        
        return section

    def _generate_mixed_results_section(
        self,
        test_results: List[TestResult],
        hypothesis_id: str,
        template_type: TemplateType
    ) -> str:
        """Generate section for mixed results."""
        significant_results = [r for r in test_results if r.p_value < 0.05]
        null_results = [r for r in test_results if r.p_value >= 0.05]
        
        template = self.templates['mixed_result']
        
        section = template.format(
            hypothesis_id=hypothesis_id,
            significant_count=len(significant_results),
            total_tests=len(test_results),
            significant_tests=", ".join([r.test_name for r in significant_results]),
            null_tests=", ".join([r.test_name for r in null_results])
        )
        
        return section

    def _classify_effect_size(self, effect_size: float) -> EffectSize:
        """Classify effect size magnitude."""
        abs_effect = abs(effect_size)
        
        if abs_effect < self.EFFECT_SIZE_THRESHOLDS[EffectSize.NEGLIGIBLE]:
            return EffectSize.NEGLIGIBLE
        elif abs_effect < self.EFFECT_SIZE_THRESHOLDS[EffectSize.SMALL]:
            return EffectSize.SMALL
        elif abs_effect < self.EFFECT_SIZE_THRESHOLDS[EffectSize.MEDIUM]:
            return EffectSize.MEDIUM
        else:
            return EffectSize.LARGE

    def _assess_integration_strength(self, metrics: IntegrationMetrics) -> str:
        """Assess market integration strength."""
        if metrics.speed_of_adjustment is None:
            return "Integration metrics insufficient for assessment"
        
        speed = metrics.speed_of_adjustment
        half_life = metrics.half_life or float('inf')
        
        if (speed >= self.INTEGRATION_THRESHOLDS['strong']['speed_min'] and 
            half_life <= self.INTEGRATION_THRESHOLDS['strong']['half_life_max']):
            return "Strong market integration detected"
        elif (speed >= self.INTEGRATION_THRESHOLDS['moderate']['speed_min'] and 
              half_life <= self.INTEGRATION_THRESHOLDS['moderate']['half_life_max']):
            return "Moderate market integration detected"
        else:
            return "Weak market integration detected"

    def _generate_null_interpretation(self, result: TestResult) -> str:
        """Generate interpretation for null results."""
        if result.p_value > 0.10:
            strength = "no evidence"
        elif result.p_value > 0.05:
            strength = "weak evidence"
        else:
            strength = "marginal evidence"
        
        return f"We find {strength} for the hypothesized relationship"

    def _get_null_result_template(self) -> str:
        """Template for null results."""
        return """
## Result: {hypothesis_id} - No Significant Effect Found

**Statistical Test**: {test_statistic:.3f}, p = {p_value:.3f}
**95% Confidence Interval**: [{ci_lower:.3f}, {ci_upper:.3f}]
**Interpretation**: {interpretation}

**Scientific Value**: This null result contributes valuable information by:
- Establishing absence of detectable effect under current conditions
- Preventing Type I errors in policy recommendations  
- Guiding future research toward alternative mechanisms
- Building realistic expectations for intervention impacts

**Possible Explanations**:
1. True absence of effect in this context
2. Insufficient statistical power (check sample size)
3. Measurement error obscuring true relationship
4. Effect occurs outside current sample period
5. Non-linear relationship not captured by linear model

**Next Steps**:
- [ ] Calculate post-hoc statistical power
- [ ] Test non-linear specifications
- [ ] Investigate measurement quality
- [ ] Consider alternative time periods
"""

    def _get_significant_result_template(self) -> str:
        """Template for significant results."""
        return """
## Result: {hypothesis_id} - Significant Effect Detected

**Statistical Test**: {test_statistic:.3f}, p = {p_value:.3f}
**Effect Size**: {effect_size:.3f} ({effect_classification} effect)
**95% Confidence Interval**: [{ci_lower:.3f}, {ci_upper:.3f}]

{integration_assessment}

**Robustness Assessment**: [TO BE COMPLETED AFTER ROBUSTNESS CHECKS]
- [ ] Alternative specifications tested
- [ ] Outlier sensitivity checked  
- [ ] Different estimation methods applied
- [ ] Subsample stability verified

**Policy Implications**: [TO BE DETERMINED BASED ON SPECIFIC CONTEXT]
- Effect magnitude suggests [PRACTICAL SIGNIFICANCE ASSESSMENT]
- Confidence interval indicates [UNCERTAINTY BOUNDS]
- Policy relevance depends on [CONTEXT-SPECIFIC FACTORS]
"""

    def _get_mixed_result_template(self) -> str:
        """Template for mixed results."""
        return """
## Result: {hypothesis_id} - Mixed Evidence

**Test Summary**: {significant_count} of {total_tests} tests significant
**Significant Tests**: {significant_tests}
**Non-significant Tests**: {null_tests}

**Pattern Analysis**: 
The mixed pattern suggests that [TO BE INTERPRETED BASED ON SPECIFIC TESTS]:
- Relationship may be conditional on [INVESTIGATE CONDITIONS]
- Effect may vary across [INVESTIGATE HETEROGENEITY]  
- Measurement issues may affect [INVESTIGATE SPECIFIC TESTS]

**Next Steps**:
- [ ] Investigate sources of heterogeneity
- [ ] Test conditional relationships
- [ ] Verify measurement consistency
- [ ] Consider meta-analytic approach
"""

    def _get_strong_integration_template(self) -> str:
        """Template for strong market integration."""
        return """
**Market Integration Assessment**: Strong integration detected
- Speed of adjustment: {speed:.3f} (>0.5 threshold)
- Half-life: {half_life:.1f} periods (<3 threshold)
- Markets adjust quickly to price shocks
"""

    def _get_weak_integration_template(self) -> str:
        """Template for weak market integration."""
        return """
**Market Integration Assessment**: Weak integration detected  
- Speed of adjustment: {speed:.3f} (<0.2 threshold)
- Half-life: {half_life:.1f} periods (>12 threshold)
- Markets show limited price transmission
"""

    def generate_policy_implications(
        self, 
        test_results: List[TestResult],
        template_type: TemplateType
    ) -> str:
        """Generate conditional policy implications based on results."""
        
        # Policy implications must be conditional on actual findings
        if all(r.p_value >= 0.05 for r in test_results):
            return self._null_result_policy_implications(template_type)
        else:
            return self._significant_result_policy_implications(test_results, template_type)

    def _null_result_policy_implications(self, template_type: TemplateType) -> str:
        """Policy implications for null results."""
        base_text = """
**Policy Implications of Null Results**:

Since no significant effect was detected, current policy should:
1. **Avoid unnecessary interventions** in this area
2. **Redirect resources** to areas with demonstrated effects  
3. **Maintain status quo** while monitoring for changes
4. **Investigate alternative mechanisms** that might drive outcomes

**Caution**: Absence of evidence is not evidence of absence. Consider:
- Whether sample was adequate to detect meaningful effects
- Whether timing was appropriate for effect detection
- Whether measurement captured relevant variation
"""
        return base_text

    def _significant_result_policy_implications(
        self, 
        test_results: List[TestResult],
        template_type: TemplateType
    ) -> str:
        """Policy implications for significant results."""
        primary_result = test_results[0]
        effect_size = self._classify_effect_size(primary_result.effect_size)
        
        if effect_size == EffectSize.LARGE:
            priority = "High priority"
            action = "immediate policy attention"
        elif effect_size == EffectSize.MEDIUM:
            priority = "Moderate priority"  
            action = "planned policy response"
        else:
            priority = "Low priority"
            action = "monitoring and evaluation"
            
        return f"""
**Policy Implications of Significant Results**:

**Priority Level**: {priority} based on {effect_size.value} effect size
**Recommended Action**: Effect magnitude suggests {action}

**Implementation Considerations**:
1. **Effect size** ({primary_result.effect_size:.3f}) indicates {effect_size.value} practical impact
2. **Confidence interval** suggests uncertainty range for planning
3. **Robustness** across specifications affects implementation confidence
4. **Context specificity** may limit generalizability

**Risk Assessment**:
- **Type I Error Risk**: {primary_result.p_value:.3f} probability of false positive
- **Practical Significance**: Based on effect size and confidence interval
- **Implementation Complexity**: [TO BE ASSESSED BASED ON SPECIFIC CONTEXT]
"""

    def validate_template_integrity(self, template_text: str) -> bool:
        """Validate that template maintains scientific integrity."""
        
        # Check for predetermined language
        try:
            self._ensure_no_predetermined_text(template_text)
        except ValueError:
            return False
        
        # Check for proper placeholders
        placeholders = re.findall(r'\[([^\]]+)\]', template_text)
        required_placeholders = [
            'TO BE DETERMINED', 'TO BE CALCULATED', 'TO BE TESTED',
            'TO BE COMPLETED', 'TO BE ASSESSED'
        ]
        
        has_placeholders = any(
            any(req in placeholder for req in required_placeholders)
            for placeholder in placeholders
        )
        
        return has_placeholders

    def generate_executive_summary(
        self, 
        all_results: Dict[str, List[TestResult]],
        study_context: Dict[str, Any]
    ) -> str:
        """Generate executive summary based on all results."""
        
        # Count significant vs null results
        all_tests = []
        for hypothesis_results in all_results.values():
            all_tests.extend(hypothesis_results)
        
        significant_count = sum(1 for r in all_tests if r.p_value < 0.05)
        total_count = len(all_tests)
        
        return f"""
# Executive Summary

**Study Period**: {study_context.get('start_date', '[START_DATE]')} to {study_context.get('end_date', '[END_DATE]')}
**Sample Size**: {study_context.get('sample_size', '[N]')} observations
**Hypotheses Tested**: {len(all_results)}
**Significant Results**: {significant_count} of {total_count} tests

**Key Findings**:
[TO BE WRITTEN BASED ON ACTUAL RESULTS]

**Policy Relevance**:
[TO BE ASSESSED AFTER ANALYSIS COMPLETION]

**Limitations**:
[TO BE DOCUMENTED BASED ON ANALYSIS EXPERIENCE]

**Next Steps**:
[TO BE DETERMINED BASED ON FINDINGS]

**Note**: This summary reflects actual analytical findings, not predetermined expectations.
All results reported with appropriate statistical uncertainty and effect size considerations.
"""


# Example usage and testing
if __name__ == "__main__":
    # Example test result
    test_result = TestResult(
        test_name="Integration Test",
        test_statistic=2.45,
        p_value=0.034,
        effect_size=0.23,
        confidence_interval=(0.02, 0.44),
        interpretation="Moderate positive effect detected",
        robustness_checks=[]
    )
    
    # Example integration metrics
    integration_metrics = IntegrationMetrics(
        speed_of_adjustment=0.35,
        half_life=2.8,
        r_squared=0.67,
        pass_through=0.84,
        error_correction_coef=-0.35
    )
    
    # Generate template
    generator = ResultsTemplateGenerator()
    
    try:
        results_section = generator.generate_results_section(
            test_results=[test_result],
            hypothesis_id="H1",
            template_type=TemplateType.INTEGRATION,
            integration_metrics=integration_metrics
        )
        print(results_section)
        
    except ValueError as e:
        print(f"Template generation failed: {e}")