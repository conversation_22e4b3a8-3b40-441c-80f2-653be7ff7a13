"""
Sensitivity Analysis Framework

Tests sensitivity to unobserved confounders, measurement error,
and model assumptions for the Yemen market integration analysis.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class SensitivityResult:
    """Results from sensitivity analysis."""
    test_name: str
    original_estimate: float
    adjusted_estimates: Dict[str, float]
    robustness_assessment: str
    bounds: Optional[Tuple[float, float]] = None
    critical_value: Optional[float] = None
    visualization: Optional[plt.Figure] = None


class SensitivityAnalysis:
    """
    Comprehensive sensitivity analysis for causal inference.
    
    Implements various methods to assess robustness to:
    - Unobserved confounding
    - Selection bias
    - Measurement error
    - Model misspecification
    """
    
    def __init__(self):
        """Initialize sensitivity analysis framework."""
        self.results = []
        
    def omitted_variable_bias(self,
                            model_results: Dict[str, Any],
                            r_squared_current: float,
                            r_squared_max: float = 1.0,
                            delta_range: Optional[Tuple[float, float]] = None) -> SensitivityResult:
        """
        Assess sensitivity to omitted variable bias using Oster (2019) method.
        
        Args:
            model_results: Results from current model including coefficient and R²
            r_squared_current: R-squared from current model
            r_squared_max: Maximum R-squared with unobservables (default 1.0)
            delta_range: Range of selection ratios to test
            
        Returns:
            SensitivityResult with bounds on treatment effect
        """
        beta_controlled = model_results.get('coefficient')
        beta_uncontrolled = model_results.get('uncontrolled_coefficient', beta_controlled * 1.5)
        
        if delta_range is None:
            delta_range = (-2, 2)
            
        # Calculate identified set for different delta values
        deltas = np.linspace(delta_range[0], delta_range[1], 100)
        identified_set = []
        
        for delta in deltas:
            # Oster's formula
            beta_star = beta_controlled - delta * (beta_uncontrolled - beta_controlled) * \
                       (r_squared_max - r_squared_current) / (r_squared_current - model_results.get('r_squared_uncontrolled', 0))
            identified_set.append(beta_star)
            
        identified_set = np.array(identified_set)
        
        # Find bounds
        bounds = (np.min(identified_set), np.max(identified_set))
        
        # Critical delta where effect goes to zero
        if beta_controlled != 0:
            critical_delta = beta_controlled / ((beta_uncontrolled - beta_controlled) * 
                           (r_squared_max - r_squared_current) / 
                           (r_squared_current - model_results.get('r_squared_uncontrolled', 0)))
        else:
            critical_delta = np.inf
            
        # Create visualization
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.plot(deltas, identified_set, 'b-', linewidth=2)
        ax.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        ax.axhline(y=beta_controlled, color='green', linestyle='--', 
                  alpha=0.7, label=f'Current estimate: {beta_controlled:.3f}')
        ax.axvline(x=1, color='gray', linestyle=':', alpha=0.5,
                  label='Equal selection')
        ax.fill_between(deltas, identified_set, 0, 
                       where=(identified_set > 0), alpha=0.2, color='blue')
        ax.fill_between(deltas, identified_set, 0,
                       where=(identified_set < 0), alpha=0.2, color='red')
        
        ax.set_xlabel('Selection Ratio (δ)', fontsize=12)
        ax.set_ylabel('Treatment Effect', fontsize=12)
        ax.set_title('Sensitivity to Omitted Variable Bias (Oster 2019)', fontsize=14)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Assess robustness
        if abs(critical_delta) > 1:
            robustness = "ROBUST: Effect survives equal selection assumption"
        elif abs(critical_delta) > 0.5:
            robustness = "MODERATELY ROBUST: Some sensitivity to omitted variables"
        else:
            robustness = "FRAGILE: High sensitivity to omitted variables"
            
        return SensitivityResult(
            test_name="Omitted Variable Bias (Oster)",
            original_estimate=beta_controlled,
            adjusted_estimates={'delta_1': identified_set[deltas == 1][0] if 1 in deltas else np.nan},
            robustness_assessment=robustness,
            bounds=bounds,
            critical_value=critical_delta,
            visualization=fig
        )
        
    def rosenbaum_bounds(self,
                        treated_outcomes: np.ndarray,
                        control_outcomes: np.ndarray,
                        gamma_range: Tuple[float, float] = (1, 3)) -> SensitivityResult:
        """
        Calculate Rosenbaum bounds for hidden bias in observational studies.
        
        Args:
            treated_outcomes: Outcomes for treated units
            control_outcomes: Outcomes for control units  
            gamma_range: Range of hidden bias parameter Γ
            
        Returns:
            SensitivityResult with bounds under different levels of hidden bias
        """
        # Calculate test statistic (Wilcoxon signed-rank test)
        statistic, p_value_no_bias = stats.wilcoxon(treated_outcomes, control_outcomes)
        
        gammas = np.linspace(gamma_range[0], gamma_range[1], 50)
        p_values_upper = []
        p_values_lower = []
        
        for gamma in gammas:
            # Calculate bounds on p-values
            # Upper bound: most favorable assignment
            p_upper = self._rosenbaum_p_value(statistic, len(treated_outcomes), gamma, upper=True)
            p_values_upper.append(p_upper)
            
            # Lower bound: least favorable assignment
            p_lower = self._rosenbaum_p_value(statistic, len(treated_outcomes), gamma, upper=False)
            p_values_lower.append(p_lower)
            
        # Find critical gamma where p > 0.05
        critical_gamma = None
        for i, (gamma, p_upper) in enumerate(zip(gammas, p_values_upper)):
            if p_upper > 0.05:
                critical_gamma = gamma
                break
                
        # Create visualization
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.fill_between(gammas, p_values_lower, p_values_upper, 
                       alpha=0.3, color='blue', label='P-value bounds')
        ax.plot(gammas, p_values_upper, 'b-', linewidth=2, label='Upper bound')
        ax.plot(gammas, p_values_lower, 'b--', linewidth=2, label='Lower bound')
        ax.axhline(y=0.05, color='red', linestyle='--', alpha=0.7, label='α = 0.05')
        
        if critical_gamma:
            ax.axvline(x=critical_gamma, color='green', linestyle=':', 
                      alpha=0.7, label=f'Critical Γ = {critical_gamma:.2f}')
            
        ax.set_xlabel('Hidden Bias Parameter (Γ)', fontsize=12)
        ax.set_ylabel('P-value', fontsize=12)
        ax.set_title('Rosenbaum Bounds for Hidden Bias', fontsize=14)
        ax.set_ylim(0, 1)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Assess robustness
        if critical_gamma is None or critical_gamma > 2:
            robustness = "ROBUST: Survives substantial hidden bias"
        elif critical_gamma > 1.5:
            robustness = "MODERATELY ROBUST: Survives moderate hidden bias"
        else:
            robustness = "FRAGILE: Sensitive to small hidden bias"
            
        return SensitivityResult(
            test_name="Rosenbaum Bounds",
            original_estimate=p_value_no_bias,
            adjusted_estimates={'gamma_2': p_values_upper[gammas == 2][0] if 2 in gammas else np.nan},
            robustness_assessment=robustness,
            critical_value=critical_gamma,
            visualization=fig
        )
        
    def _rosenbaum_p_value(self, statistic: float, n: int, 
                          gamma: float, upper: bool = True) -> float:
        """Calculate Rosenbaum bound p-value."""
        # Simplified calculation - full implementation would use exact distribution
        if upper:
            # Most favorable case
            z_score = (statistic - n * (n + 1) / 4) / np.sqrt(n * (n + 1) * (2 * n + 1) / 24)
            z_adjusted = z_score / gamma
        else:
            # Least favorable case
            z_score = (statistic - n * (n + 1) / 4) / np.sqrt(n * (n + 1) * (2 * n + 1) / 24)
            z_adjusted = z_score * gamma
            
        return 2 * (1 - stats.norm.cdf(abs(z_adjusted)))
        
    def bounds_analysis(self,
                       data: pd.DataFrame,
                       outcome_var: str,
                       treatment_var: str,
                       assumptions: List[str] = ['monotone_treatment']) -> SensitivityResult:
        """
        Calculate bounds on treatment effects under different assumptions.
        
        Args:
            data: Dataset
            outcome_var: Outcome variable name
            treatment_var: Treatment variable name
            assumptions: List of assumptions to maintain
            
        Returns:
            SensitivityResult with various bounds
        """
        bounds_dict = {}
        
        # Manski bounds (no assumptions)
        y_min, y_max = data[outcome_var].min(), data[outcome_var].max()
        p_treated = (data[treatment_var] == 1).mean()
        
        # Worst case bounds
        manski_lower = (data[data[treatment_var] == 1][outcome_var].mean() - y_max) * p_treated + \
                       (y_min - data[data[treatment_var] == 0][outcome_var].mean()) * (1 - p_treated)
        manski_upper = (data[data[treatment_var] == 1][outcome_var].mean() - y_min) * p_treated + \
                       (y_max - data[data[treatment_var] == 0][outcome_var].mean()) * (1 - p_treated)
        
        bounds_dict['manski'] = (manski_lower, manski_upper)
        
        if 'monotone_treatment' in assumptions:
            # Lee bounds for sample selection
            bounds_dict['lee'] = self._calculate_lee_bounds(data, outcome_var, treatment_var)
            
        if 'mean_independence' in assumptions:
            # Bounds under mean independence of potential outcomes
            ate = data[data[treatment_var] == 1][outcome_var].mean() - \
                  data[data[treatment_var] == 0][outcome_var].mean()
            bounds_dict['mean_independence'] = (ate, ate)
            
        # Create visualization
        fig, ax = plt.subplots(figsize=(10, 6))
        
        methods = list(bounds_dict.keys())
        lower_bounds = [bounds[0] for bounds in bounds_dict.values()]
        upper_bounds = [bounds[1] for bounds in bounds_dict.values()]
        
        y_pos = np.arange(len(methods))
        
        # Plot bounds
        for i, (method, (lower, upper)) in enumerate(bounds_dict.items()):
            ax.plot([lower, upper], [i, i], 'o-', linewidth=3, markersize=8)
            ax.text(lower - 0.01, i, f'{lower:.3f}', ha='right', va='center')
            ax.text(upper + 0.01, i, f'{upper:.3f}', ha='left', va='center')
            
        ax.axvline(x=0, color='red', linestyle='--', alpha=0.5)
        ax.set_yticks(y_pos)
        ax.set_yticklabels([m.replace('_', ' ').title() for m in methods])
        ax.set_xlabel('Treatment Effect Bounds', fontsize=12)
        ax.set_title('Bounds Analysis Under Different Assumptions', fontsize=14)
        ax.grid(True, alpha=0.3, axis='x')
        
        # Assess robustness
        all_positive = all(lower > 0 for lower in lower_bounds)
        all_negative = all(upper < 0 for upper in upper_bounds)
        
        if all_positive or all_negative:
            robustness = "ROBUST: Sign is identified under all assumptions"
        elif bounds_dict.get('lee', (0, 0))[0] * bounds_dict.get('lee', (0, 0))[1] > 0:
            robustness = "MODERATELY ROBUST: Sign identified under reasonable assumptions"
        else:
            robustness = "FRAGILE: Sign not identified even under strong assumptions"
            
        return SensitivityResult(
            test_name="Bounds Analysis",
            original_estimate=ate if 'mean_independence' in assumptions else np.nan,
            adjusted_estimates=bounds_dict,
            robustness_assessment=robustness,
            bounds=(min(lower_bounds), max(upper_bounds)),
            visualization=fig
        )
        
    def _calculate_lee_bounds(self, data: pd.DataFrame, 
                            outcome_var: str, treatment_var: str) -> Tuple[float, float]:
        """Calculate Lee (2009) bounds for sample selection."""
        # Simplified implementation
        treated = data[data[treatment_var] == 1]
        control = data[data[treatment_var] == 0]
        
        n_treated = len(treated)
        n_control = len(control)
        
        if n_treated > n_control:
            # Trim from treated group
            trim_prop = (n_treated - n_control) / n_treated
            threshold = treated[outcome_var].quantile(1 - trim_prop)
            trimmed_treated = treated[treated[outcome_var] <= threshold]
            
            lower_bound = trimmed_treated[outcome_var].mean() - control[outcome_var].mean()
            
            threshold = treated[outcome_var].quantile(trim_prop)
            trimmed_treated = treated[treated[outcome_var] >= threshold]
            
            upper_bound = trimmed_treated[outcome_var].mean() - control[outcome_var].mean()
        else:
            # Trim from control group
            trim_prop = (n_control - n_treated) / n_control
            threshold = control[outcome_var].quantile(trim_prop)
            trimmed_control = control[control[outcome_var] >= threshold]
            
            lower_bound = treated[outcome_var].mean() - trimmed_control[outcome_var].mean()
            
            threshold = control[outcome_var].quantile(1 - trim_prop)
            trimmed_control = control[control[outcome_var] <= threshold]
            
            upper_bound = treated[outcome_var].mean() - trimmed_control[outcome_var].mean()
            
        return (min(lower_bound, upper_bound), max(lower_bound, upper_bound))
        
    def measurement_error_sensitivity(self,
                                    model_results: Dict[str, Any],
                                    reliability_range: Tuple[float, float] = (0.7, 1.0),
                                    variable_type: str = 'independent') -> SensitivityResult:
        """
        Assess sensitivity to measurement error in variables.
        
        Args:
            model_results: Original model results
            reliability_range: Range of reliability ratios to test
            variable_type: 'independent' or 'dependent' variable
            
        Returns:
            SensitivityResult
        """
        original_coef = model_results.get('coefficient')
        
        reliabilities = np.linspace(reliability_range[0], reliability_range[1], 50)
        adjusted_coefs = []
        
        for reliability in reliabilities:
            if variable_type == 'independent':
                # Classical measurement error in X attenuates coefficient
                adjusted_coef = original_coef / reliability
            else:
                # Measurement error in Y doesn't bias coefficient but inflates SE
                adjusted_coef = original_coef
                
            adjusted_coefs.append(adjusted_coef)
            
        # Create visualization
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.plot(reliabilities, adjusted_coefs, 'b-', linewidth=2)
        ax.axhline(y=original_coef, color='green', linestyle='--',
                  alpha=0.7, label=f'Observed estimate: {original_coef:.3f}')
        ax.axhline(y=0, color='red', linestyle='--', alpha=0.5)
        
        # Add confidence bands if measurement error in Y
        if variable_type == 'dependent':
            se = model_results.get('se', 0.1)
            adjusted_ses = se / np.sqrt(reliabilities)
            ax.fill_between(reliabilities, 
                          adjusted_coefs - 1.96 * adjusted_ses,
                          adjusted_coefs + 1.96 * adjusted_ses,
                          alpha=0.2, color='blue')
                          
        ax.set_xlabel('Reliability Ratio', fontsize=12)
        ax.set_ylabel('Adjusted Coefficient', fontsize=12)
        ax.set_title(f'Sensitivity to Measurement Error in {variable_type.title()} Variable', 
                    fontsize=14)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Critical reliability where effect becomes insignificant
        if variable_type == 'independent':
            critical_reliability = None
            se = model_results.get('se', 0.1)
            for rel, coef in zip(reliabilities, adjusted_coefs):
                if abs(coef) < 1.96 * se:
                    critical_reliability = rel
                    break
        else:
            critical_reliability = None
            
        # Assess robustness
        if critical_reliability is None or critical_reliability < 0.7:
            robustness = "ROBUST: Effect survives substantial measurement error"
        elif critical_reliability < 0.85:
            robustness = "MODERATELY ROBUST: Some sensitivity to measurement error"
        else:
            robustness = "FRAGILE: High sensitivity to measurement error"
            
        return SensitivityResult(
            test_name=f"Measurement Error in {variable_type.title()}",
            original_estimate=original_coef,
            adjusted_estimates={'reliability_0.9': adjusted_coefs[reliabilities == 0.9][0] 
                              if 0.9 in reliabilities else np.nan},
            robustness_assessment=robustness,
            critical_value=critical_reliability,
            visualization=fig
        )
        
    def placebo_test_suite(self,
                          data: pd.DataFrame,
                          model: Callable,
                          placebo_specs: List[Dict[str, Any]]) -> SensitivityResult:
        """
        Run suite of placebo tests to validate identification strategy.
        
        Args:
            data: Dataset
            model: Model estimation function
            placebo_specs: List of placebo test specifications
            
        Returns:
            SensitivityResult summarizing all placebo tests
        """
        placebo_results = {}
        
        for spec in placebo_specs:
            test_name = spec['name']
            test_type = spec['type']
            
            if test_type == 'fake_treatment_time':
                # Assign treatment at fake time
                placebo_data = data.copy()
                placebo_data['treatment'] = (placebo_data['date'] >= spec['fake_date']).astype(int)
                
            elif test_type == 'fake_treatment_location':
                # Randomly assign treatment to different units
                placebo_data = data.copy()
                treated_units = placebo_data['unit_id'].unique()
                np.random.shuffle(treated_units)
                n_treated = int(len(treated_units) * spec.get('treatment_proportion', 0.5))
                fake_treated = treated_units[:n_treated]
                placebo_data['treatment'] = placebo_data['unit_id'].isin(fake_treated).astype(int)
                
            elif test_type == 'unaffected_outcome':
                # Use outcome that shouldn't be affected
                placebo_data = data.copy()
                placebo_data['outcome'] = placebo_data[spec['placebo_outcome']]
                
            else:
                continue
                
            # Run placebo regression
            try:
                result = model(placebo_data)
                placebo_results[test_name] = {
                    'coefficient': result.get('coefficient'),
                    'p_value': result.get('p_value'),
                    'passed': result.get('p_value', 0) > 0.10  # Should be insignificant
                }
            except Exception as e:
                logger.warning(f"Placebo test {test_name} failed: {e}")
                placebo_results[test_name] = {
                    'coefficient': np.nan,
                    'p_value': np.nan,
                    'passed': False
                }
                
        # Create visualization
        fig, ax = plt.subplots(figsize=(12, 6))
        
        test_names = list(placebo_results.keys())
        coefficients = [r['coefficient'] for r in placebo_results.values()]
        p_values = [r['p_value'] for r in placebo_results.values()]
        passed = [r['passed'] for r in placebo_results.values()]
        
        y_pos = np.arange(len(test_names))
        colors = ['green' if p else 'red' for p in passed]
        
        # Plot coefficients with CI
        ax.barh(y_pos, coefficients, color=colors, alpha=0.7)
        
        # Add p-values as text
        for i, (coef, p_val) in enumerate(zip(coefficients, p_values)):
            if not np.isnan(p_val):
                ax.text(coef + 0.01 if coef > 0 else coef - 0.01, i,
                       f'p={p_val:.3f}', va='center',
                       ha='left' if coef > 0 else 'right')
                       
        ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)
        ax.set_yticks(y_pos)
        ax.set_yticklabels(test_names)
        ax.set_xlabel('Placebo Effect Estimate', fontsize=12)
        ax.set_title('Placebo Test Suite Results', fontsize=14)
        ax.grid(True, alpha=0.3, axis='x')
        
        # Overall assessment
        pass_rate = sum(passed) / len(passed) if passed else 0
        
        if pass_rate >= 0.9:
            robustness = "ROBUST: Passes all/most placebo tests"
        elif pass_rate >= 0.7:
            robustness = "MODERATELY ROBUST: Passes majority of placebo tests"
        else:
            robustness = "FRAGILE: Fails multiple placebo tests"
            
        return SensitivityResult(
            test_name="Placebo Test Suite",
            original_estimate=np.nan,
            adjusted_estimates=placebo_results,
            robustness_assessment=robustness,
            visualization=fig
        )
        
    def create_comprehensive_report(self, 
                                  save_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Create comprehensive sensitivity analysis report.
        
        Args:
            save_path: Path to save report
            
        Returns:
            Dictionary with all sensitivity results
        """
        report = {
            'summary': self._generate_summary(),
            'individual_tests': self.results,
            'overall_assessment': self._overall_robustness_assessment()
        }
        
        if save_path:
            # Save detailed report
            import json
            with open(save_path, 'w') as f:
                # Convert to serializable format
                serializable_report = {
                    'summary': report['summary'],
                    'overall_assessment': report['overall_assessment'],
                    'test_results': [
                        {
                            'test_name': r.test_name,
                            'original_estimate': r.original_estimate,
                            'robustness_assessment': r.robustness_assessment,
                            'critical_value': r.critical_value
                        }
                        for r in self.results
                    ]
                }
                json.dump(serializable_report, f, indent=2)
                
        return report
        
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate summary of all sensitivity tests."""
        if not self.results:
            return {}
            
        robust_count = sum(1 for r in self.results if 'ROBUST' in r.robustness_assessment)
        fragile_count = sum(1 for r in self.results if 'FRAGILE' in r.robustness_assessment)
        
        return {
            'total_tests': len(self.results),
            'robust_results': robust_count,
            'fragile_results': fragile_count,
            'robustness_rate': robust_count / len(self.results) if self.results else 0
        }
        
    def _overall_robustness_assessment(self) -> str:
        """Provide overall robustness assessment."""
        if not self.results:
            return "No sensitivity tests conducted"
            
        summary = self._generate_summary()
        
        if summary['robustness_rate'] >= 0.8:
            return "HIGHLY ROBUST: Results are insensitive to most threats"
        elif summary['robustness_rate'] >= 0.6:
            return "MODERATELY ROBUST: Some sensitivity but generally stable"
        elif summary['robustness_rate'] >= 0.4:
            return "MIXED ROBUSTNESS: Substantial variation across tests"
        else:
            return "FRAGILE: Results are sensitive to multiple threats"