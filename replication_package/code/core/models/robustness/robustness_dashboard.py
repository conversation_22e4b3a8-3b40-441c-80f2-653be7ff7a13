"""
Robustness Visualization Dashboard

Creates comprehensive visualizations for robustness testing results
in the Yemen market integration analysis.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.gridspec import GridSpec
from typing import Dict, List, Optional, Any, Tuple
import matplotlib.patches as patches
from matplotlib.patches import Rectangle

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


class RobustnessDashboard:
    """
    Create comprehensive robustness visualization dashboards.
    
    Provides multi-panel visualizations that summarize:
    - Specification curve analysis
    - Bootstrap distributions
    - Sensitivity analysis
    - Subsample stability
    - Overall robustness assessment
    """
    
    def __init__(self, style: str = 'academic'):
        """
        Initialize dashboard with style settings.
        
        Args:
            style: 'academic' or 'presentation' style
        """
        self.style = style
        self._set_style()
        
    def _set_style(self):
        """Set matplotlib style for consistent visualizations."""
        if self.style == 'academic':
            plt.style.use('seaborn-v0_8-whitegrid')
            sns.set_palette("husl")
            self.figsize = (16, 12)
            self.dpi = 300
        else:  # presentation
            plt.style.use('seaborn-v0_8-darkgrid')
            sns.set_palette("bright")
            self.figsize = (20, 15)
            self.dpi = 150
            
    def create_dashboard(self,
                        main_results: Dict[str, Any],
                        robustness_tests: Dict[str, Any],
                        save_path: Optional[str] = None,
                        title: Optional[str] = None) -> plt.Figure:
        """
        Create comprehensive robustness dashboard.
        
        Args:
            main_results: Results from main specification
            robustness_tests: Dictionary of robustness test results
            save_path: Path to save figure
            title: Overall title for dashboard
            
        Returns:
            matplotlib Figure
        """
        # Create figure with custom layout
        fig = plt.figure(figsize=self.figsize, constrained_layout=True)
        
        if title is None:
            title = "Comprehensive Robustness Analysis Dashboard"
            
        fig.suptitle(title, fontsize=20, y=0.98)
        
        # Create grid specification
        gs = GridSpec(4, 3, figure=fig, height_ratios=[1.5, 1, 1, 1],
                     width_ratios=[1, 1, 1])
        
        # Panel 1: Main result with confidence intervals
        ax1 = fig.add_subplot(gs[0, 0])
        self._plot_main_result(ax1, main_results)
        
        # Panel 2: Specification curve
        ax2 = fig.add_subplot(gs[0, 1:])
        self._plot_specification_curve(ax2, robustness_tests.get('spec_curve', {}))
        
        # Panel 3: Bootstrap distribution
        ax3 = fig.add_subplot(gs[1, 0])
        self._plot_bootstrap_distribution(ax3, robustness_tests.get('bootstrap', {}))
        
        # Panel 4: Sensitivity to controls
        ax4 = fig.add_subplot(gs[1, 1])
        self._plot_control_sensitivity(ax4, robustness_tests.get('controls', {}))
        
        # Panel 5: Subsample analysis
        ax5 = fig.add_subplot(gs[1, 2])
        self._plot_subsample_results(ax5, robustness_tests.get('subsamples', {}))
        
        # Panel 6: Outlier sensitivity
        ax6 = fig.add_subplot(gs[2, 0])
        self._plot_outlier_sensitivity(ax6, robustness_tests.get('outliers', {}))
        
        # Panel 7: Time period stability
        ax7 = fig.add_subplot(gs[2, 1])
        self._plot_time_stability(ax7, robustness_tests.get('time_periods', {}))
        
        # Panel 8: Geographic variation
        ax8 = fig.add_subplot(gs[2, 2])
        self._plot_geographic_variation(ax8, robustness_tests.get('geographic', {}))
        
        # Panel 9: Summary statistics table
        ax9 = fig.add_subplot(gs[3, :2])
        self._plot_summary_table(ax9, main_results, robustness_tests)
        
        # Panel 10: Overall robustness gauge
        ax10 = fig.add_subplot(gs[3, 2])
        self._plot_robustness_gauge(ax10, robustness_tests)
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            logger.info(f"Dashboard saved to {save_path}")
            
        return fig
        
    def _plot_main_result(self, ax: plt.Axes, main_results: Dict[str, Any]):
        """Plot main result with multiple confidence intervals."""
        coefficient = main_results.get('coefficient', 0)
        se = main_results.get('se', 0.1)
        
        # Different confidence levels
        confidence_levels = [0.90, 0.95, 0.99]
        z_scores = [1.645, 1.96, 2.576]
        colors = ['#3498db', '#2980b9', '#21618c']
        
        # Plot confidence intervals
        for i, (conf_level, z_score, color) in enumerate(zip(confidence_levels, z_scores, colors)):
            ci_lower = coefficient - z_score * se
            ci_upper = coefficient + z_score * se
            
            ax.barh(i, ci_upper - ci_lower, left=ci_lower, height=0.6,
                   alpha=0.6, color=color, label=f'{int(conf_level*100)}% CI')
                   
        # Point estimate
        ax.axvline(x=coefficient, color='#e74c3c', linewidth=3,
                  label=f'Estimate: {coefficient:.3f}')
        
        # Zero line
        ax.axvline(x=0, color='black', linestyle='--', alpha=0.5)
        
        ax.set_yticks(range(len(confidence_levels)))
        ax.set_yticklabels([f'{int(cl*100)}%' for cl in confidence_levels])
        ax.set_xlabel('Effect Size')
        ax.set_title('Main Specification Result', fontsize=14, pad=10)
        ax.legend(loc='upper right')
        ax.grid(True, alpha=0.3)
        
    def _plot_specification_curve(self, ax: plt.Axes, spec_curve_data: Dict[str, Any]):
        """Plot specification curve results."""
        if not spec_curve_data:
            ax.text(0.5, 0.5, 'No specification curve data available',
                   ha='center', va='center', transform=ax.transAxes)
            return
            
        coefficients = spec_curve_data.get('coefficients', [])
        significant = spec_curve_data.get('significant', [])
        
        if not coefficients:
            return
            
        # Sort by coefficient value
        sorted_indices = np.argsort(coefficients)
        sorted_coefs = np.array(coefficients)[sorted_indices]
        sorted_sig = np.array(significant)[sorted_indices]
        
        # Color by significance
        colors = ['#2ecc71' if sig else '#95a5a6' for sig in sorted_sig]
        
        # Plot
        x = range(len(sorted_coefs))
        ax.scatter(x, sorted_coefs, c=colors, alpha=0.6, s=20)
        
        # Add median line
        median_coef = np.median(sorted_coefs)
        ax.axhline(y=median_coef, color='#e74c3c', linestyle='-',
                  linewidth=2, label=f'Median: {median_coef:.3f}')
        
        # Zero line
        ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        
        # Shaded region for IQR
        q1, q3 = np.percentile(sorted_coefs, [25, 75])
        ax.fill_between(x, q1, q3, alpha=0.1, color='blue')
        
        ax.set_xlabel('Specification Number')
        ax.set_ylabel('Effect Size')
        ax.set_title('Specification Curve Analysis', fontsize=14, pad=10)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Add text summary
        pct_sig = np.mean(significant) * 100
        ax.text(0.02, 0.98, f'{len(coefficients)} specs\n{pct_sig:.0f}% significant',
               transform=ax.transAxes, va='top', ha='left',
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
               
    def _plot_bootstrap_distribution(self, ax: plt.Axes, bootstrap_data: Dict[str, Any]):
        """Plot bootstrap distribution with confidence intervals."""
        distribution = bootstrap_data.get('distribution', [])
        point_estimate = bootstrap_data.get('point_estimate', 0)
        
        if not distribution:
            ax.text(0.5, 0.5, 'No bootstrap data available',
                   ha='center', va='center', transform=ax.transAxes)
            return
            
        # Histogram
        n, bins, patches = ax.hist(distribution, bins=30, density=True,
                                  alpha=0.7, color='#3498db', edgecolor='black')
        
        # Kernel density estimate
        from scipy.stats import gaussian_kde
        kde = gaussian_kde(distribution)
        x_range = np.linspace(min(distribution), max(distribution), 200)
        ax.plot(x_range, kde(x_range), 'r-', linewidth=2, label='KDE')
        
        # Confidence intervals
        ci_lower, ci_upper = np.percentile(distribution, [2.5, 97.5])
        ax.axvline(x=ci_lower, color='green', linestyle='--', alpha=0.7)
        ax.axvline(x=ci_upper, color='green', linestyle='--', alpha=0.7)
        ax.axvspan(ci_lower, ci_upper, alpha=0.1, color='green')
        
        # Point estimate
        ax.axvline(x=point_estimate, color='red', linewidth=2,
                  label=f'Estimate: {point_estimate:.3f}')
        
        ax.set_xlabel('Bootstrap Estimates')
        ax.set_ylabel('Density')
        ax.set_title('Bootstrap Distribution', fontsize=14, pad=10)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
    def _plot_control_sensitivity(self, ax: plt.Axes, control_data: Dict[str, Any]):
        """Plot sensitivity to control variable inclusion."""
        if not control_data:
            ax.text(0.5, 0.5, 'No control sensitivity data',
                   ha='center', va='center', transform=ax.transAxes)
            return
            
        control_sets = control_data.get('control_sets', [])
        coefficients = control_data.get('coefficients', [])
        r_squared = control_data.get('r_squared', [])
        
        if not control_sets:
            return
            
        # Create coefficient plot
        y_pos = np.arange(len(control_sets))
        
        # Coefficient dots with error bars
        ax.errorbar(coefficients, y_pos, xerr=[c*0.1 for c in coefficients],
                   fmt='o', markersize=8, capsize=5, color='#e74c3c')
        
        # R-squared on secondary axis
        ax2 = ax.twiny()
        ax2.plot(r_squared, y_pos, 's-', markersize=8, color='#3498db',
                label='R²', alpha=0.7)
        
        # Labels
        ax.set_yticks(y_pos)
        ax.set_yticklabels(control_sets)
        ax.set_xlabel('Coefficient Estimate', color='#e74c3c')
        ax2.set_xlabel('R-squared', color='#3498db')
        
        # Zero line
        ax.axvline(x=0, color='black', linestyle='--', alpha=0.5)
        
        ax.set_title('Control Variable Sensitivity', fontsize=14, pad=20)
        ax.grid(True, alpha=0.3, axis='x')
        
    def _plot_subsample_results(self, ax: plt.Axes, subsample_data: Dict[str, Any]):
        """Plot results across different subsamples."""
        if not subsample_data:
            ax.text(0.5, 0.5, 'No subsample data available',
                   ha='center', va='center', transform=ax.transAxes)
            return
            
        subsamples = subsample_data.get('subsamples', [])
        estimates = subsample_data.get('estimates', [])
        ci_lower = subsample_data.get('ci_lower', [])
        ci_upper = subsample_data.get('ci_upper', [])
        
        if not subsamples:
            return
            
        # Forest plot
        y_pos = np.arange(len(subsamples))
        
        # Confidence intervals
        for i, (lower, upper, est) in enumerate(zip(ci_lower, ci_upper, estimates)):
            ax.plot([lower, upper], [i, i], 'b-', linewidth=2, alpha=0.7)
            ax.plot(est, i, 'o', markersize=8, color='#e74c3c')
            
        # Zero line
        ax.axvline(x=0, color='black', linestyle='--', alpha=0.5)
        
        # Main estimate line
        if 'main_estimate' in subsample_data:
            ax.axvline(x=subsample_data['main_estimate'], color='green',
                      linestyle='-', alpha=0.5, linewidth=2)
                      
        ax.set_yticks(y_pos)
        ax.set_yticklabels(subsamples)
        ax.set_xlabel('Effect Estimate')
        ax.set_title('Subsample Analysis', fontsize=14, pad=10)
        ax.grid(True, alpha=0.3, axis='x')
        
    def _plot_outlier_sensitivity(self, ax: plt.Axes, outlier_data: Dict[str, Any]):
        """Plot sensitivity to outlier treatment."""
        if not outlier_data:
            ax.text(0.5, 0.5, 'No outlier sensitivity data',
                   ha='center', va='center', transform=ax.transAxes)
            return
            
        methods = outlier_data.get('methods', [])
        coefficients = outlier_data.get('coefficients', [])
        n_outliers = outlier_data.get('n_outliers', [])
        
        if not methods:
            return
            
        # Bar plot with outlier counts
        x = np.arange(len(methods))
        bars = ax.bar(x, coefficients, alpha=0.7, color='#3498db')
        
        # Add outlier counts on top
        for i, (bar, n) in enumerate(zip(bars, n_outliers)):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'n={n}', ha='center', va='bottom', fontsize=9)
                   
        # Baseline
        if 'baseline' in outlier_data:
            ax.axhline(y=outlier_data['baseline'], color='red',
                      linestyle='--', label='No treatment')
                      
        ax.set_xticks(x)
        ax.set_xticklabels(methods, rotation=45, ha='right')
        ax.set_ylabel('Coefficient Estimate')
        ax.set_title('Outlier Treatment Sensitivity', fontsize=14, pad=10)
        ax.legend()
        ax.grid(True, alpha=0.3, axis='y')
        
    def _plot_time_stability(self, ax: plt.Axes, time_data: Dict[str, Any]):
        """Plot coefficient stability over time periods."""
        if not time_data:
            ax.text(0.5, 0.5, 'No time stability data',
                   ha='center', va='center', transform=ax.transAxes)
            return
            
        periods = time_data.get('periods', [])
        coefficients = time_data.get('coefficients', [])
        ci_lower = time_data.get('ci_lower', [])
        ci_upper = time_data.get('ci_upper', [])
        
        if not periods:
            return
            
        # Time series plot
        x = range(len(periods))
        ax.plot(x, coefficients, 'o-', markersize=8, linewidth=2,
               color='#e74c3c', label='Point estimate')
               
        # Confidence bands
        ax.fill_between(x, ci_lower, ci_upper, alpha=0.2, color='#e74c3c')
        
        # Zero line
        ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        
        # Trend line
        if len(periods) > 3:
            z = np.polyfit(x, coefficients, 1)
            p = np.poly1d(z)
            ax.plot(x, p(x), '--', color='blue', alpha=0.7,
                   label=f'Trend: {z[0]:.3f}')
                   
        ax.set_xticks(x)
        ax.set_xticklabels(periods, rotation=45, ha='right')
        ax.set_ylabel('Coefficient Estimate')
        ax.set_title('Temporal Stability', fontsize=14, pad=10)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
    def _plot_geographic_variation(self, ax: plt.Axes, geo_data: Dict[str, Any]):
        """Plot geographic variation in effects."""
        if not geo_data:
            ax.text(0.5, 0.5, 'No geographic data available',
                   ha='center', va='center', transform=ax.transAxes)
            return
            
        regions = geo_data.get('regions', [])
        effects = geo_data.get('effects', [])
        
        if not regions:
            return
            
        # Horizontal bar chart
        y_pos = np.arange(len(regions))
        colors = ['#e74c3c' if e > 0 else '#3498db' for e in effects]
        
        bars = ax.barh(y_pos, effects, color=colors, alpha=0.7)
        
        # Add value labels
        for bar, effect in zip(bars, effects):
            width = bar.get_width()
            ax.text(width + 0.01 if width > 0 else width - 0.01,
                   bar.get_y() + bar.get_height()/2,
                   f'{effect:.3f}', ha='left' if width > 0 else 'right',
                   va='center')
                   
        ax.set_yticks(y_pos)
        ax.set_yticklabels(regions)
        ax.set_xlabel('Effect Size')
        ax.set_title('Geographic Heterogeneity', fontsize=14, pad=10)
        ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)
        ax.grid(True, alpha=0.3, axis='x')
        
    def _plot_summary_table(self, ax: plt.Axes, main_results: Dict[str, Any],
                           robustness_tests: Dict[str, Any]):
        """Create summary statistics table."""
        ax.axis('tight')
        ax.axis('off')
        
        # Prepare table data
        table_data = []
        
        # Main specification
        table_data.append(['Main Specification', '', ''])
        table_data.append(['  Coefficient', f"{main_results.get('coefficient', 0):.4f}", ''])
        table_data.append(['  Std. Error', f"{main_results.get('se', 0):.4f}", ''])
        table_data.append(['  P-value', f"{main_results.get('p_value', 0):.4f}", ''])
        table_data.append(['  N', f"{main_results.get('n_obs', 0):,}", ''])
        table_data.append(['', '', ''])
        
        # Robustness summary
        table_data.append(['Robustness Tests', 'Result', 'Assessment'])
        
        if 'spec_curve' in robustness_tests:
            sc = robustness_tests['spec_curve']
            table_data.append([
                '  Specification Curve',
                f"{sc.get('n_specs', 0)} specs, {sc.get('pct_significant', 0):.0f}% sig",
                sc.get('assessment', 'N/A')
            ])
            
        if 'bootstrap' in robustness_tests:
            bs = robustness_tests['bootstrap']
            table_data.append([
                '  Bootstrap CI',
                f"[{bs.get('ci_lower', 0):.3f}, {bs.get('ci_upper', 0):.3f}]",
                'Robust' if bs.get('converged', False) else 'Check'
            ])
            
        if 'outliers' in robustness_tests:
            out = robustness_tests['outliers']
            table_data.append([
                '  Outlier Sensitivity',
                f"Range: {out.get('range', 0):.3f}",
                out.get('assessment', 'N/A')
            ])
            
        # Create table
        table = ax.table(cellText=table_data,
                        colWidths=[0.5, 0.3, 0.2],
                        cellLoc='left',
                        loc='center')
                        
        # Style table
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1, 1.5)
        
        # Header styling
        for i in [0, 6]:
            for j in range(3):
                table[(i, j)].set_facecolor('#3498db')
                table[(i, j)].set_text_props(weight='bold', color='white')
                
        ax.set_title('Summary Statistics', fontsize=14, pad=20)
        
    def _plot_robustness_gauge(self, ax: plt.Axes, robustness_tests: Dict[str, Any]):
        """Create overall robustness gauge visualization."""
        # Calculate overall robustness score
        score = self._calculate_robustness_score(robustness_tests)
        
        # Create gauge chart
        # Background
        ax.add_patch(plt.Circle((0.5, 0.3), 0.4, color='lightgray', 
                               transform=ax.transAxes))
        ax.add_patch(plt.Circle((0.5, 0.3), 0.35, color='white',
                               transform=ax.transAxes))
        
        # Color zones
        colors = ['#e74c3c', '#f39c12', '#f1c40f', '#2ecc71']
        labels = ['Fragile', 'Weak', 'Moderate', 'Robust']
        
        # Draw colored arcs
        theta1 = 180
        for i, (color, label) in enumerate(zip(colors, labels)):
            theta2 = theta1 - 45
            arc = patches.Wedge((0.5, 0.3), 0.4, theta2, theta1,
                              width=0.05, color=color, 
                              transform=ax.transAxes)
            ax.add_patch(arc)
            
            # Add labels
            mid_theta = np.radians((theta1 + theta2) / 2)
            label_x = 0.5 + 0.45 * np.cos(mid_theta)
            label_y = 0.3 + 0.45 * np.sin(mid_theta)
            ax.text(label_x, label_y, label, ha='center', va='center',
                   fontsize=9, transform=ax.transAxes)
            theta1 = theta2
            
        # Needle
        needle_angle = 180 - score * 180
        needle_x = 0.5 + 0.3 * np.cos(np.radians(needle_angle))
        needle_y = 0.3 + 0.3 * np.sin(np.radians(needle_angle))
        ax.plot([0.5, needle_x], [0.3, needle_y], 'k-', linewidth=3,
               transform=ax.transAxes)
        ax.add_patch(plt.Circle((0.5, 0.3), 0.03, color='black',
                               transform=ax.transAxes))
        
        # Score text
        ax.text(0.5, 0.1, f'Robustness Score: {score:.0%}',
               ha='center', fontsize=14, weight='bold',
               transform=ax.transAxes)
               
        # Assessment text
        assessment = self._get_robustness_assessment(score)
        ax.text(0.5, 0.05, assessment, ha='center', fontsize=11,
               style='italic', transform=ax.transAxes)
               
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        ax.set_title('Overall Robustness', fontsize=14, pad=10)
        
    def _calculate_robustness_score(self, robustness_tests: Dict[str, Any]) -> float:
        """Calculate overall robustness score from 0 to 1."""
        scores = []
        
        # Specification curve
        if 'spec_curve' in robustness_tests:
            pct_sig = robustness_tests['spec_curve'].get('pct_significant', 0) / 100
            scores.append(pct_sig)
            
        # Bootstrap convergence
        if 'bootstrap' in robustness_tests:
            conv_rate = robustness_tests['bootstrap'].get('convergence_rate', 0)
            scores.append(conv_rate)
            
        # Outlier stability
        if 'outliers' in robustness_tests:
            stability = 1 - robustness_tests['outliers'].get('variation', 0.5)
            scores.append(stability)
            
        # Time stability
        if 'time_periods' in robustness_tests:
            stability = robustness_tests['time_periods'].get('stability_score', 0.5)
            scores.append(stability)
            
        # Geographic consistency
        if 'geographic' in robustness_tests:
            consistency = robustness_tests['geographic'].get('consistency_score', 0.5)
            scores.append(consistency)
            
        return np.mean(scores) if scores else 0.5
        
    def _get_robustness_assessment(self, score: float) -> str:
        """Get text assessment based on robustness score."""
        if score >= 0.8:
            return "Highly robust results suitable for policy decisions"
        elif score >= 0.6:
            return "Moderately robust with some caveats"
        elif score >= 0.4:
            return "Mixed robustness - interpret with caution"
        else:
            return "Fragile results - additional investigation needed"
            
    def create_specification_importance_plot(self,
                                           importance_data: Dict[str, float],
                                           save_path: Optional[str] = None) -> plt.Figure:
        """
        Create visualization showing which specification choices matter most.
        
        Args:
            importance_data: Dictionary of choice importance scores
            save_path: Path to save figure
            
        Returns:
            matplotlib Figure
        """
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Sort by importance
        sorted_items = sorted(importance_data.items(), 
                            key=lambda x: x[1], reverse=True)
        choices = [item[0].replace('_', ' ').title() for item in sorted_items]
        values = [item[1] * 100 for item in sorted_items]
        
        # Create horizontal bar plot
        y_pos = np.arange(len(choices))
        bars = ax.barh(y_pos, values, color='#3498db', alpha=0.8)
        
        # Add value labels
        for bar, value in zip(bars, values):
            width = bar.get_width()
            ax.text(width + 1, bar.get_y() + bar.get_height()/2,
                   f'{value:.1f}%', va='center', fontsize=10)
                   
        # Styling
        ax.set_yticks(y_pos)
        ax.set_yticklabels(choices)
        ax.set_xlabel('Relative Importance (%)', fontsize=12)
        ax.set_title('Specification Choice Importance Analysis', fontsize=14)
        ax.grid(True, alpha=0.3, axis='x')
        ax.set_xlim(0, max(values) * 1.1)
        
        # Add explanation
        ax.text(0.98, 0.02, 
               'Higher values indicate choices that affect results more',
               transform=ax.transAxes, ha='right', va='bottom',
               fontsize=9, style='italic', alpha=0.7)
               
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            
        return fig
        
    def create_publication_figure(self,
                                main_results: Dict[str, Any],
                                spec_curve_results: Dict[str, Any],
                                bootstrap_results: Dict[str, Any],
                                save_path: Optional[str] = None) -> plt.Figure:
        """
        Create publication-quality figure with key robustness results.
        
        Args:
            main_results: Main specification results
            spec_curve_results: Specification curve analysis results
            bootstrap_results: Bootstrap results
            save_path: Path to save figure
            
        Returns:
            matplotlib Figure suitable for journal publication
        """
        # Use minimalist style for publication
        plt.style.use('seaborn-v0_8-white')
        
        fig, axes = plt.subplots(1, 3, figsize=(12, 4))
        
        # Panel A: Main result forest plot
        ax = axes[0]
        self._create_forest_plot(ax, main_results)
        ax.set_title('(a) Main Results', fontsize=12)
        
        # Panel B: Specification curve
        ax = axes[1]
        self._create_minimal_spec_curve(ax, spec_curve_results)
        ax.set_title('(b) Specification Curve', fontsize=12)
        
        # Panel C: Bootstrap distribution
        ax = axes[2]
        self._create_minimal_bootstrap_plot(ax, bootstrap_results)
        ax.set_title('(c) Bootstrap Distribution', fontsize=12)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=600, bbox_inches='tight')
            
        return fig
        
    def _create_forest_plot(self, ax: plt.Axes, results: Dict[str, Any]):
        """Create minimal forest plot for publication."""
        estimate = results.get('coefficient', 0)
        ci_lower = results.get('ci_lower', estimate - 0.1)
        ci_upper = results.get('ci_upper', estimate + 0.1)
        
        # Main estimate
        ax.plot([ci_lower, ci_upper], [0, 0], 'k-', linewidth=2)
        ax.plot(estimate, 0, 'ko', markersize=8)
        
        # Zero line
        ax.axvline(x=0, color='gray', linestyle='--', alpha=0.5)
        
        # Clean up
        ax.set_ylim(-0.5, 0.5)
        ax.set_yticks([])
        ax.set_xlabel('Effect Size')
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_visible(False)
        
    def _create_minimal_spec_curve(self, ax: plt.Axes, spec_data: Dict[str, Any]):
        """Create minimal specification curve for publication."""
        coefficients = spec_data.get('coefficients', [])
        
        if coefficients:
            sorted_coefs = sorted(coefficients)
            x = range(len(sorted_coefs))
            
            ax.scatter(x, sorted_coefs, s=5, alpha=0.5, color='gray')
            ax.axhline(y=np.median(sorted_coefs), color='red', 
                      linewidth=1.5, linestyle='-')
            ax.axhline(y=0, color='black', linewidth=0.5, linestyle='--')
            
        ax.set_xlabel('Specification')
        ax.set_ylabel('Effect Size')
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        
    def _create_minimal_bootstrap_plot(self, ax: plt.Axes, bootstrap_data: Dict[str, Any]):
        """Create minimal bootstrap plot for publication."""
        distribution = bootstrap_data.get('distribution', [])
        
        if distribution:
            ax.hist(distribution, bins=30, density=True, alpha=0.7,
                   color='gray', edgecolor='black', linewidth=0.5)
            
            # Add confidence interval
            ci_lower, ci_upper = np.percentile(distribution, [2.5, 97.5])
            ax.axvline(x=ci_lower, color='red', linestyle='--', linewidth=1)
            ax.axvline(x=ci_upper, color='red', linestyle='--', linewidth=1)
            
        ax.set_xlabel('Bootstrap Estimate')
        ax.set_ylabel('Density')
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)