"""Estimator interface for model estimation."""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional

import numpy as np
import pandas as pd

from .model import Model


@dataclass
class EstimationResult:
    """Container for estimation results."""
    
    model_name: str
    estimation_method: str
    coefficients: Dict[str, float]
    standard_errors: Dict[str, float]
    t_statistics: Dict[str, float] = field(default_factory=dict)
    p_values: Dict[str, float] = field(default_factory=dict)
    
    # Model fit statistics
    r_squared: Optional[float] = None
    adjusted_r_squared: Optional[float] = None
    log_likelihood: Optional[float] = None
    aic: Optional[float] = None
    bic: Optional[float] = None
    
    # Sample information
    n_observations: int = 0
    n_parameters: int = 0
    degrees_of_freedom: int = 0
    
    # Time information
    estimation_time: datetime = field(default_factory=datetime.utcnow)
    computation_time_seconds: float = 0.0
    
    # Additional results
    residuals: Optional[pd.Series] = None
    fitted_values: Optional[pd.Series] = None
    covariance_matrix: Optional[np.ndarray] = None
    
    # Metadata
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_significant_coefficients(self, alpha: float = 0.05) -> Dict[str, float]:
        """Get statistically significant coefficients."""
        return {
            var: coef for var, coef in self.coefficients.items()
            if self.p_values.get(var, 1.0) < alpha
        }
    
    def summary_table(self) -> pd.DataFrame:
        """Create summary table of results."""
        data = []
        for var in self.coefficients:
            data.append({
                "Variable": var,
                "Coefficient": self.coefficients[var],
                "Std. Error": self.standard_errors.get(var, np.nan),
                "t-statistic": self.t_statistics.get(var, np.nan),
                "P>|t|": self.p_values.get(var, np.nan),
            })
        return pd.DataFrame(data)


@dataclass
class DiagnosticResult:
    """Container for diagnostic test results."""
    
    test_name: str
    test_statistic: float
    p_value: float
    reject_null: bool
    interpretation: str
    corrective_action: Optional[str] = None
    critical_values: Optional[Dict[str, float]] = None
    
    def __str__(self) -> str:
        """String representation."""
        result = f"{self.test_name}: {self.test_statistic:.4f} (p={self.p_value:.4f})"
        if self.reject_null:
            result += " ***"
        return result


class Estimator(ABC):
    """Abstract base class for model estimators."""
    
    @abstractmethod
    def estimate(self, model: Model, data: pd.DataFrame, **kwargs) -> EstimationResult:
        """
        Estimate model parameters.
        
        Args:
            model: Model to estimate
            data: Data for estimation
            **kwargs: Additional estimation options
            
        Returns:
            EstimationResult containing fitted parameters and statistics
        """
        pass
    
    @abstractmethod
    def diagnose(self, model: Model, result: EstimationResult) -> Dict[str, DiagnosticResult]:
        """
        Run diagnostic tests on estimation results.
        
        Args:
            model: Estimated model
            result: Estimation results
            
        Returns:
            Dictionary of diagnostic test results
        """
        pass
    
    @abstractmethod
    def predict(
        self,
        model: Model,
        result: EstimationResult,
        new_data: pd.DataFrame
    ) -> pd.Series:
        """
        Generate predictions using fitted model.
        
        Args:
            model: Fitted model
            result: Estimation results
            new_data: Data for prediction
            
        Returns:
            Series of predictions
        """
        pass
    
    @abstractmethod
    def get_standard_errors(
        self,
        model: Model,
        result: EstimationResult,
        se_type: str = "standard"
    ) -> Dict[str, float]:
        """
        Calculate standard errors with specified method.
        
        Args:
            model: Fitted model
            result: Estimation results
            se_type: Type of standard errors (e.g., 'robust', 'clustered')
            
        Returns:
            Dictionary of standard errors
        """
        pass