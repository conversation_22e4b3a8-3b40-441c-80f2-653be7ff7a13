"""Model specification interfaces."""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, Any, List, Optional


@dataclass
class ModelSpecification(ABC):
    """Base model specification."""
    
    model_type: str
    parameters: Dict[str, Any] = None
    
    def __post_init__(self):
        """Initialize default parameters."""
        if self.parameters is None:
            self.parameters = {}
    
    @abstractmethod
    def validate(self) -> List[str]:
        """Validate specification and return list of errors."""
        pass
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        pass
    
    @classmethod
    @abstractmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelSpecification':
        """Create from dictionary representation."""
        pass


@dataclass
class PanelModelSpecification(ModelSpecification):
    """Panel model specification."""
    
    fixed_effects: Optional[List[str]] = None
    time_effects: bool = False
    entity_effects: bool = True
    model_type: str = "panel"
    
    def validate(self) -> List[str]:
        """Validate panel model specification."""
        errors = []
        
        if not isinstance(self.parameters, dict):
            errors.append("Parameters must be a dictionary")
        
        if self.fixed_effects is not None and not isinstance(self.fixed_effects, list):
            errors.append("Fixed effects must be a list of strings")
        
        return errors
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "model_type": self.model_type,
            "parameters": self.parameters,
            "fixed_effects": self.fixed_effects,
            "time_effects": self.time_effects,
            "entity_effects": self.entity_effects
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PanelModelSpecification':
        """Create from dictionary representation."""
        return cls(
            model_type=data.get("model_type", "panel"),
            parameters=data.get("parameters", {}),
            fixed_effects=data.get("fixed_effects"),
            time_effects=data.get("time_effects", False),
            entity_effects=data.get("entity_effects", True)
        )