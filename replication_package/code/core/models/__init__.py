"""Core econometric models as first-class citizens."""

from .interfaces import Model, Estimator, EstimationResult, DiagnosticResult, ModelSpecification
from .panel import PooledPanelModel, FixedEffectsModel, TwoWayFixedEffectsModel, InteractiveFixedEffectsModel, IFEResults
from .time_series import VECMModel, ThresholdVECMModel
from .bayesian import BayesianPanelModel
from .machine_learning import MarketClusterAnalyzer as MarketSegmentationModel
from .regime_switching import MarkovSwitchingCurrencyModel, SmoothTransitionModel, PanelThresholdModel
from .welfare import ZoneSpecificWelfareCalculator, FragmentationCostEstimator, AidOptimizationEngine
from .early_warning import IntegratedEarlyWarningSystem

__all__ = [
    # Interfaces
    "Model",
    "Estimator",
    "EstimationResult",
    "DiagnosticResult",
    "ModelSpecification",
    # Panel models
    "PooledPanelModel",
    "FixedEffectsModel",
    "TwoWayFixedEffectsModel",
    "InteractiveFixedEffectsModel",
    "IFEResults",
    # Time series models
    "VECMModel",
    "ThresholdVECMModel",
    # Bayesian models
    "BayesianPanelModel",
    # Machine learning
    "MarketSegmentationModel",
    # Regime switching
    "MarkovSwitchingCurrencyModel",
    "SmoothTransitionModel", 
    "PanelThresholdModel",
    # Welfare analysis
    "ZoneSpecificWelfareCalculator",
    "FragmentationCostEstimator",
    "AidOptimizationEngine",
    # Early warning
    "IntegratedEarlyWarningSystem",
]