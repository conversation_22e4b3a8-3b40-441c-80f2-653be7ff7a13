"""
Bayesian Panel Data Models

This module implements Bayesian panel data models for market integration analysis,
providing full uncertainty quantification crucial for policy decisions in conflict settings.

Key Features:
1. Hierarchical Bayesian models with zone-specific parameters
2. Missing data imputation through MCMC
3. Informative priors for exchange rate regimes
4. Model comparison using WAIC/LOO
5. Posterior predictive checks

The implementation uses PyMC for MCMC sampling, with fallback to custom samplers.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass
import logging
from scipy import stats

# Try to import PyMC, fall back to custom implementation if not available
try:
    import pymc as pm
    import arviz as az
    PYMC_AVAILABLE = True
except ImportError:
    PYMC_AVAILABLE = False
    logger.warning("PyMC not available - using simplified Bayesian implementation")

logger = logging.getLogger(__name__)


@dataclass
class BayesianPanelResults:
    """Results from Bayesian panel estimation."""
    # Posterior samples
    posterior_samples: Dict[str, np.ndarray]  # Parameter name -> samples
    
    # Summary statistics
    posterior_means: Dict[str, float]
    posterior_medians: Dict[str, float]
    posterior_std: Dict[str, float]
    credible_intervals: Dict[str, Tuple[float, float]]  # 95% CIs
    
    # Model diagnostics
    effective_sample_size: Dict[str, float]  # ESS for each parameter
    rhat: Dict[str, float]  # Gelman-Rubin statistic
    
    # Model comparison
    waic: Optional[float] = None  # Widely Applicable Information Criterion
    loo: Optional[float] = None  # Leave-one-out cross-validation
    
    # Posterior predictive
    posterior_predictive: Optional[np.ndarray] = None
    
    # Zone-specific results
    zone_parameters: Optional[Dict[str, Dict[str, float]]] = None
    zone_effects: Optional[Dict[str, np.ndarray]] = None
    
    # Convergence diagnostics
    n_divergent: int = 0
    energy_plot: Optional[Any] = None
    trace_plot: Optional[Any] = None


@dataclass
class PriorSpecification:
    """Specification of prior distributions."""
    # Regression coefficients
    beta_mean: np.ndarray  # Prior mean for coefficients
    beta_precision: np.ndarray  # Prior precision (1/variance)
    
    # Variance parameters
    sigma_shape: float = 2.0  # Shape for inverse gamma
    sigma_scale: float = 1.0  # Scale for inverse gamma
    
    # Random effects variance
    tau_shape: float = 2.0
    tau_scale: float = 1.0
    
    # Zone-specific priors
    zone_priors: Optional[Dict[str, Dict[str, Any]]] = None
    
    # Exchange rate regime priors
    regime_transition_alpha: float = 1.0  # Dirichlet concentration


class BayesianPanelModel:
    """
    Bayesian panel data model with hierarchical structure.
    
    Model specification:
    y_it = α_i + β*X_it + γ_z[i]*Z_it + ε_it
    
    Where:
    - α_i ~ Normal(μ_α, τ²) - Random effects
    - β ~ Normal(β_0, Σ_β) - Fixed effects
    - γ_z ~ Normal(γ_0, Σ_γ) - Zone-specific effects
    - ε_it ~ Normal(0, σ²) - Error term
    """
    
    def __init__(self,
                 model_type: str = 'random_effects',
                 hierarchical: bool = True,
                 zone_specific: bool = True,
                 missing_data_method: str = 'impute'):
        """
        Initialize Bayesian panel model.
        
        Args:
            model_type: 'random_effects', 'fixed_effects', or 'dynamic'
            hierarchical: Use hierarchical priors
            zone_specific: Allow zone-specific parameters
            missing_data_method: 'impute' or 'drop'
        """
        self.model_type = model_type
        self.hierarchical = hierarchical
        self.zone_specific = zone_specific
        self.missing_data_method = missing_data_method
        self.fitted = False
        
    def set_priors(self, prior_spec: Optional[PriorSpecification] = None):
        """
        Set prior distributions.
        
        Args:
            prior_spec: Prior specification (uses defaults if None)
        """
        if prior_spec is None:
            # Default weakly informative priors
            self.priors = PriorSpecification(
                beta_mean=np.zeros(1),  # Will be resized based on data
                beta_precision=np.ones(1) * 0.001,  # Weak precision
                sigma_shape=2.0,
                sigma_scale=1.0,
                tau_shape=2.0,
                tau_scale=1.0
            )
        else:
            self.priors = prior_spec
            
    def fit(self,
            panel_data: pd.DataFrame,
            dependent_var: str,
            independent_vars: List[str],
            entity_var: str = 'market_id',
            time_var: str = 'date',
            zone_var: Optional[str] = 'currency_zone',
            n_samples: int = 2000,
            n_chains: int = 4,
            random_seed: int = 42) -> 'BayesianPanelModel':
        """
        Fit Bayesian panel model using MCMC.
        
        Args:
            panel_data: Panel DataFrame
            dependent_var: Dependent variable name
            independent_vars: List of independent variables
            entity_var: Entity identifier
            time_var: Time identifier
            zone_var: Zone identifier (optional)
            n_samples: Number of MCMC samples per chain
            n_chains: Number of MCMC chains
            random_seed: Random seed for reproducibility
            
        Returns:
            Self for chaining
        """
        logger.info(f"Fitting Bayesian {self.model_type} panel model")
        
        # Prepare data
        self._prepare_data(panel_data, dependent_var, independent_vars, 
                          entity_var, time_var, zone_var)
        
        # Set priors if not already set
        if not hasattr(self, 'priors'):
            self.set_priors()
            
        # Update prior dimensions
        self.priors.beta_mean = np.zeros(self.k_vars)
        self.priors.beta_precision = np.ones(self.k_vars) * 0.001
        
        # Fit model
        if PYMC_AVAILABLE:
            self._fit_pymc(n_samples, n_chains, random_seed)
        else:
            self._fit_custom(n_samples, n_chains, random_seed)
            
        self.fitted = True
        
        return self
        
    def _prepare_data(self, panel_data, dependent_var, independent_vars,
                     entity_var, time_var, zone_var):
        """Prepare data for Bayesian estimation."""
        # Store variable names
        self.dependent_var = dependent_var
        self.independent_vars = independent_vars
        self.entity_var = entity_var
        self.time_var = time_var
        self.zone_var = zone_var
        
        # Extract arrays
        self.y = panel_data[dependent_var].values
        self.X = panel_data[independent_vars].values
        self.entities = panel_data[entity_var].values
        self.time_ids = panel_data[time_var].values
        
        if zone_var and zone_var in panel_data.columns:
            self.zones = panel_data[zone_var].values
        else:
            self.zones = None
            
        # Get dimensions
        self.n_obs = len(self.y)
        self.k_vars = self.X.shape[1]
        self.unique_entities = np.unique(self.entities)
        self.n_entities = len(self.unique_entities)
        self.unique_times = np.unique(self.time_ids)
        self.n_times = len(self.unique_times)
        
        if self.zones is not None:
            self.unique_zones = np.unique(self.zones[~pd.isna(self.zones)])
            self.n_zones = len(self.unique_zones)
        else:
            self.unique_zones = None
            self.n_zones = 0
            
        # Handle missing data
        self._handle_missing_data()
        
        # Create entity and zone indices
        self.entity_idx = np.array([np.where(self.unique_entities == e)[0][0] 
                                   for e in self.entities])
        
        if self.zones is not None:
            self.zone_idx = np.array([np.where(self.unique_zones == z)[0][0] 
                                     if not pd.isna(z) else -1
                                     for z in self.zones])
            
    def _handle_missing_data(self):
        """Handle missing data according to specified method."""
        if self.missing_data_method == 'drop':
            # Drop missing observations
            valid_mask = ~(np.isnan(self.y) | np.any(np.isnan(self.X), axis=1))
            
            self.y = self.y[valid_mask]
            self.X = self.X[valid_mask]
            self.entities = self.entities[valid_mask]
            self.time_ids = self.time_ids[valid_mask]
            
            if self.zones is not None:
                self.zones = self.zones[valid_mask]
                
            self.n_obs = len(self.y)
            logger.info(f"Dropped {np.sum(~valid_mask)} observations with missing data")
            
        elif self.missing_data_method == 'impute':
            # Mark missing for imputation during MCMC
            self.y_missing = np.isnan(self.y)
            self.X_missing = np.isnan(self.X)
            
            # Initialize with mean imputation
            if np.any(self.y_missing):
                self.y[self.y_missing] = np.nanmean(self.y)
                
            if np.any(self.X_missing):
                col_means = np.nanmean(self.X, axis=0)
                for j in range(self.k_vars):
                    self.X[self.X_missing[:, j], j] = col_means[j]
                    
            logger.info(f"Will impute {np.sum(self.y_missing)} missing y values")
            
    def _fit_pymc(self, n_samples, n_chains, random_seed):
        """Fit model using PyMC."""
        logger.info("Using PyMC for MCMC sampling")
        
        with pm.Model() as model:
            # Data containers
            X_data = pm.ConstantData('X', self.X)
            y_data = pm.MutableData('y', self.y)
            
            # Priors for fixed effects
            beta = pm.Normal('beta', 
                           mu=self.priors.beta_mean,
                           tau=self.priors.beta_precision,
                           shape=self.k_vars)
            
            # Random effects (if applicable)
            if self.model_type == 'random_effects':
                # Hyperpriors
                mu_alpha = pm.Normal('mu_alpha', mu=0, sigma=10)
                tau_alpha = pm.InverseGamma('tau_alpha', 
                                           alpha=self.priors.tau_shape,
                                           beta=self.priors.tau_scale)
                
                # Random intercepts
                alpha = pm.Normal('alpha', 
                                mu=mu_alpha, 
                                sigma=1/pm.math.sqrt(tau_alpha),
                                shape=self.n_entities)
                
                # Map to observations
                alpha_i = alpha[self.entity_idx]
            else:
                alpha_i = 0
                
            # Zone-specific effects (if applicable)
            if self.zone_specific and self.zones is not None:
                # Zone-specific slopes
                gamma_mu = pm.Normal('gamma_mu', mu=0, sigma=10, shape=self.k_vars)
                gamma_tau = pm.InverseGamma('gamma_tau', alpha=2, beta=1)
                
                gamma = pm.Normal('gamma',
                                mu=gamma_mu,
                                sigma=1/pm.math.sqrt(gamma_tau),
                                shape=(self.n_zones, self.k_vars))
                
                # Zone interaction terms
                zone_effects = []
                for i in range(self.n_obs):
                    if self.zone_idx[i] >= 0:
                        zone_effect = pm.math.sum(gamma[self.zone_idx[i]] * X_data[i])
                    else:
                        zone_effect = 0
                    zone_effects.append(zone_effect)
                    
                zone_contribution = pm.math.stack(zone_effects)
            else:
                zone_contribution = 0
                
            # Linear predictor
            mu = alpha_i + pm.math.dot(X_data, beta) + zone_contribution
            
            # Error variance
            sigma = pm.InverseGamma('sigma', 
                                  alpha=self.priors.sigma_shape,
                                  beta=self.priors.sigma_scale)
            
            # Likelihood
            y_obs = pm.Normal('y_obs', mu=mu, sigma=sigma, observed=y_data)
            
            # Sample
            trace = pm.sample(n_samples, 
                            chains=n_chains,
                            random_seed=random_seed,
                            progressbar=True)
            
            # Posterior predictive
            posterior_predictive = pm.sample_posterior_predictive(trace)
            
        # Store results
        self._extract_pymc_results(trace, posterior_predictive)
        
    def _fit_custom(self, n_samples, n_chains, random_seed):
        """Fit model using custom MCMC implementation."""
        logger.info("Using custom MCMC implementation")
        
        # Initialize samplers for each chain
        np.random.seed(random_seed)
        all_samples = []
        
        for chain in range(n_chains):
            logger.info(f"Running chain {chain + 1}/{n_chains}")
            
            # Initialize parameters
            params = self._initialize_parameters()
            
            # Run Gibbs sampler
            chain_samples = self._gibbs_sampler(params, n_samples)
            all_samples.append(chain_samples)
            
        # Combine chains
        self._combine_chains(all_samples)
        
    def _initialize_parameters(self) -> Dict[str, np.ndarray]:
        """Initialize parameters for MCMC."""
        params = {}
        
        # Fixed effects - OLS initialization
        XtX = self.X.T @ self.X
        Xty = self.X.T @ self.y
        params['beta'] = np.linalg.solve(XtX + np.eye(self.k_vars) * 0.01, Xty)
        
        # Random effects
        if self.model_type == 'random_effects':
            params['alpha'] = np.zeros(self.n_entities)
            params['mu_alpha'] = 0.0
            params['tau_alpha'] = 1.0
            
        # Error variance
        residuals = self.y - self.X @ params['beta']
        params['sigma2'] = np.var(residuals)
        
        # Zone effects
        if self.zone_specific and self.zones is not None:
            params['gamma'] = np.zeros((self.n_zones, self.k_vars))
            params['gamma_mu'] = np.zeros(self.k_vars)
            params['gamma_tau'] = 1.0
            
        return params
        
    def _gibbs_sampler(self, params: Dict[str, np.ndarray], 
                      n_samples: int) -> Dict[str, List[np.ndarray]]:
        """Run Gibbs sampler for posterior sampling."""
        samples = {key: [] for key in params.keys()}
        
        for iteration in range(n_samples):
            # Update beta (fixed effects)
            params['beta'] = self._update_beta(params)
            
            # Update random effects
            if self.model_type == 'random_effects':
                params['alpha'] = self._update_alpha(params)
                params['mu_alpha'] = self._update_mu_alpha(params)
                params['tau_alpha'] = self._update_tau_alpha(params)
                
            # Update error variance
            params['sigma2'] = self._update_sigma2(params)
            
            # Update zone effects
            if self.zone_specific and self.zones is not None:
                params['gamma'] = self._update_gamma(params)
                params['gamma_mu'] = self._update_gamma_mu(params)
                params['gamma_tau'] = self._update_gamma_tau(params)
                
            # Store samples
            for key, value in params.items():
                samples[key].append(value.copy() if isinstance(value, np.ndarray) else value)
                
        return samples
        
    def _update_beta(self, params: Dict) -> np.ndarray:
        """Update fixed effects coefficients."""
        # Calculate residuals after removing other effects
        residuals = self.y.copy()
        
        if self.model_type == 'random_effects':
            residuals -= params['alpha'][self.entity_idx]
            
        if self.zone_specific and self.zones is not None:
            for i in range(self.n_obs):
                if self.zone_idx[i] >= 0:
                    residuals[i] -= self.X[i] @ params['gamma'][self.zone_idx[i]]
                    
        # Posterior precision and mean
        prior_precision = np.diag(self.priors.beta_precision)
        likelihood_precision = self.X.T @ self.X / params['sigma2']
        
        post_precision = prior_precision + likelihood_precision
        post_mean_num = (prior_precision @ self.priors.beta_mean + 
                        self.X.T @ residuals / params['sigma2'])
        post_mean = np.linalg.solve(post_precision, post_mean_num)
        
        # Sample from posterior
        post_cov = np.linalg.inv(post_precision)
        beta_new = np.random.multivariate_normal(post_mean, post_cov)
        
        return beta_new
        
    def _update_alpha(self, params: Dict) -> np.ndarray:
        """Update random effects."""
        alpha_new = np.zeros(self.n_entities)
        
        for i in range(self.n_entities):
            # Observations for entity i
            entity_mask = self.entity_idx == i
            n_i = np.sum(entity_mask)
            
            if n_i > 0:
                y_i = self.y[entity_mask]
                X_i = self.X[entity_mask]
                
                # Residuals after fixed effects
                residuals_i = y_i - X_i @ params['beta']
                
                # Zone effects
                if self.zone_specific and self.zones is not None:
                    zone_idx_i = self.zone_idx[entity_mask]
                    for j in range(n_i):
                        if zone_idx_i[j] >= 0:
                            residuals_i[j] -= X_i[j] @ params['gamma'][zone_idx_i[j]]
                            
                # Posterior parameters
                post_precision = n_i / params['sigma2'] + params['tau_alpha']
                post_mean = (np.sum(residuals_i) / params['sigma2'] + 
                           params['mu_alpha'] * params['tau_alpha']) / post_precision
                post_var = 1 / post_precision
                
                # Sample
                alpha_new[i] = np.random.normal(post_mean, np.sqrt(post_var))
                
        return alpha_new
        
    def _update_mu_alpha(self, params: Dict) -> float:
        """Update random effects mean."""
        # Posterior parameters
        prior_precision = 0.01  # Weakly informative
        likelihood_precision = self.n_entities * params['tau_alpha']
        
        post_precision = prior_precision + likelihood_precision
        post_mean = (np.sum(params['alpha']) * params['tau_alpha']) / post_precision
        post_var = 1 / post_precision
        
        return np.random.normal(post_mean, np.sqrt(post_var))
        
    def _update_tau_alpha(self, params: Dict) -> float:
        """Update random effects precision."""
        # Posterior parameters (inverse gamma)
        shape = self.priors.tau_shape + self.n_entities / 2
        scale = (self.priors.tau_scale + 
                np.sum((params['alpha'] - params['mu_alpha'])**2) / 2)
        
        # Sample from inverse gamma
        return np.random.gamma(shape, 1/scale)
        
    def _update_sigma2(self, params: Dict) -> float:
        """Update error variance."""
        # Calculate full residuals
        residuals = self.y - self.X @ params['beta']
        
        if self.model_type == 'random_effects':
            residuals -= params['alpha'][self.entity_idx]
            
        if self.zone_specific and self.zones is not None:
            for i in range(self.n_obs):
                if self.zone_idx[i] >= 0:
                    residuals[i] -= self.X[i] @ params['gamma'][self.zone_idx[i]]
                    
        # Posterior parameters (inverse gamma)
        shape = self.priors.sigma_shape + self.n_obs / 2
        scale = self.priors.sigma_scale + np.sum(residuals**2) / 2
        
        # Sample from inverse gamma
        return 1 / np.random.gamma(shape, 1/scale)
        
    def _update_gamma(self, params: Dict) -> np.ndarray:
        """Update zone-specific effects."""
        gamma_new = np.zeros((self.n_zones, self.k_vars))
        
        for z in range(self.n_zones):
            # Observations in zone z
            zone_mask = self.zone_idx == z
            n_z = np.sum(zone_mask)
            
            if n_z > 0:
                y_z = self.y[zone_mask]
                X_z = self.X[zone_mask]
                entity_idx_z = self.entity_idx[zone_mask]
                
                # Residuals after fixed and random effects
                residuals_z = y_z - X_z @ params['beta']
                
                if self.model_type == 'random_effects':
                    residuals_z -= params['alpha'][entity_idx_z]
                    
                # Posterior parameters
                prior_precision = np.eye(self.k_vars) * params['gamma_tau']
                likelihood_precision = X_z.T @ X_z / params['sigma2']
                
                post_precision = prior_precision + likelihood_precision
                post_mean_num = (prior_precision @ params['gamma_mu'] + 
                               X_z.T @ residuals_z / params['sigma2'])
                post_mean = np.linalg.solve(post_precision, post_mean_num)
                
                # Sample
                post_cov = np.linalg.inv(post_precision)
                gamma_new[z] = np.random.multivariate_normal(post_mean, post_cov)
                
        return gamma_new
        
    def _update_gamma_mu(self, params: Dict) -> np.ndarray:
        """Update zone effects mean."""
        # Posterior parameters
        prior_precision = np.eye(self.k_vars) * 0.01
        likelihood_precision = np.eye(self.k_vars) * self.n_zones * params['gamma_tau']
        
        post_precision = prior_precision + likelihood_precision
        post_mean_num = (np.sum(params['gamma'], axis=0) * params['gamma_tau'])
        post_mean = np.linalg.solve(post_precision, post_mean_num)
        
        # Sample
        post_cov = np.linalg.inv(post_precision)
        return np.random.multivariate_normal(post_mean, post_cov)
        
    def _update_gamma_tau(self, params: Dict) -> float:
        """Update zone effects precision."""
        # Posterior parameters
        shape = 2.0 + self.n_zones * self.k_vars / 2
        
        diff = params['gamma'] - params['gamma_mu']
        scale = 1.0 + np.sum(diff**2) / 2
        
        return np.random.gamma(shape, 1/scale)
        
    def _combine_chains(self, all_samples: List[Dict]):
        """Combine samples from multiple chains."""
        # Flatten samples across chains
        combined_samples = {}
        
        for key in all_samples[0].keys():
            combined = []
            for chain_samples in all_samples:
                combined.extend(chain_samples[key])
            combined_samples[key] = np.array(combined)
            
        # Calculate diagnostics
        self._calculate_diagnostics(all_samples, combined_samples)
        
        # Store results
        self.posterior_samples = combined_samples
        
    def _calculate_diagnostics(self, chains, combined):
        """Calculate convergence diagnostics."""
        self.diagnostics = {}
        
        # Effective sample size
        self.diagnostics['ess'] = {}
        for param in combined.keys():
            if param in ['beta', 'alpha', 'gamma']:
                # Vector parameters
                if len(combined[param].shape) > 1:
                    ess = []
                    for i in range(combined[param].shape[1]):
                        param_samples = combined[param][:, i]
                        ess.append(self._calculate_ess(param_samples))
                    self.diagnostics['ess'][param] = np.array(ess)
                else:
                    self.diagnostics['ess'][param] = self._calculate_ess(combined[param])
            else:
                # Scalar parameters
                self.diagnostics['ess'][param] = self._calculate_ess(combined[param])
                
        # Gelman-Rubin statistic (R-hat)
        self.diagnostics['rhat'] = self._calculate_rhat(chains)
        
    def _calculate_ess(self, samples: np.ndarray) -> float:
        """Calculate effective sample size."""
        n = len(samples)
        
        # Autocorrelation
        acf = []
        for lag in range(min(n//4, 100)):
            if lag == 0:
                acf.append(1.0)
            else:
                acf.append(np.corrcoef(samples[:-lag], samples[lag:])[0, 1])
                
        # Sum until first negative autocorrelation
        sum_acf = 1.0
        for i in range(1, len(acf)):
            if acf[i] < 0:
                break
            sum_acf += 2 * acf[i]
            
        ess = n / sum_acf
        return ess
        
    def _calculate_rhat(self, chains: List[Dict]) -> Dict[str, float]:
        """Calculate Gelman-Rubin statistic."""
        rhat = {}
        
        for param in chains[0].keys():
            # Get parameter samples from each chain
            chain_samples = []
            for chain in chains:
                chain_samples.append(np.array(chain[param]))
                
            # Calculate between and within chain variance
            n_chains = len(chain_samples)
            n_samples = len(chain_samples[0])
            
            # Chain means
            chain_means = [np.mean(samples, axis=0) for samples in chain_samples]
            
            # Between chain variance
            B = n_samples * np.var(chain_means, axis=0, ddof=1)
            
            # Within chain variance
            W = np.mean([np.var(samples, axis=0, ddof=1) for samples in chain_samples], axis=0)
            
            # Pooled variance estimate
            var_plus = ((n_samples - 1) * W + B) / n_samples
            
            # R-hat
            rhat[param] = np.sqrt(var_plus / W) if W > 0 else 1.0
            
        return rhat
        
    def _extract_pymc_results(self, trace, posterior_predictive):
        """Extract results from PyMC trace."""
        # Posterior samples
        self.posterior_samples = {}
        var_names = ['beta', 'sigma']
        
        if self.model_type == 'random_effects':
            var_names.extend(['alpha', 'mu_alpha', 'tau_alpha'])
            
        if self.zone_specific and self.zones is not None:
            var_names.extend(['gamma', 'gamma_mu', 'gamma_tau'])
            
        for var in var_names:
            if var in trace.posterior:
                self.posterior_samples[var] = trace.posterior[var].values.reshape(-1, -1)
                
        # Diagnostics
        self.diagnostics = {
            'ess': az.ess(trace),
            'rhat': az.rhat(trace),
            'n_divergent': trace.sample_stats.diverging.sum().item()
        }
        
        # Model comparison metrics
        self.waic = az.waic(trace).waic
        self.loo = az.loo(trace).loo
        
        # Posterior predictive
        self.posterior_predictive = posterior_predictive.posterior_predictive['y_obs'].values
        
    def predict(self, X_new: np.ndarray,
                entity_ids: Optional[np.ndarray] = None,
                zone_ids: Optional[np.ndarray] = None,
                return_uncertainty: bool = True) -> Union[np.ndarray, Tuple[np.ndarray, np.ndarray]]:
        """
        Make predictions with uncertainty quantification.
        
        Args:
            X_new: New predictor values
            entity_ids: Entity IDs for random effects
            zone_ids: Zone IDs for zone effects
            return_uncertainty: Return prediction intervals
            
        Returns:
            Predictions and optionally 95% prediction intervals
        """
        if not self.fitted:
            raise ValueError("Model must be fitted before prediction")
            
        n_new = X_new.shape[0]
        n_samples = len(self.posterior_samples['beta'])
        
        # Generate predictions for each posterior sample
        predictions = np.zeros((n_samples, n_new))
        
        for s in range(n_samples):
            # Fixed effects
            pred = X_new @ self.posterior_samples['beta'][s]
            
            # Random effects
            if self.model_type == 'random_effects' and entity_ids is not None:
                for i, entity_id in enumerate(entity_ids):
                    if entity_id in self.unique_entities:
                        entity_idx = np.where(self.unique_entities == entity_id)[0][0]
                        pred[i] += self.posterior_samples['alpha'][s][entity_idx]
                    else:
                        # New entity - use population mean
                        pred[i] += self.posterior_samples['mu_alpha'][s]
                        
            # Zone effects
            if self.zone_specific and zone_ids is not None and self.zones is not None:
                for i, zone_id in enumerate(zone_ids):
                    if zone_id in self.unique_zones:
                        zone_idx = np.where(self.unique_zones == zone_id)[0][0]
                        pred[i] += X_new[i] @ self.posterior_samples['gamma'][s][zone_idx]
                        
            # Add observation noise
            if 'sigma2' in self.posterior_samples:
                sigma = np.sqrt(self.posterior_samples['sigma2'][s])
            else:
                sigma = np.sqrt(1 / self.posterior_samples['sigma'][s])
                
            pred += np.random.normal(0, sigma, n_new)
            
            predictions[s] = pred
            
        # Summarize predictions
        pred_mean = np.mean(predictions, axis=0)
        
        if return_uncertainty:
            pred_lower = np.percentile(predictions, 2.5, axis=0)
            pred_upper = np.percentile(predictions, 97.5, axis=0)
            return pred_mean, (pred_lower, pred_upper)
        else:
            return pred_mean
            
    def get_results(self) -> BayesianPanelResults:
        """
        Get comprehensive results summary.
        
        Returns:
            BayesianPanelResults object
        """
        if not self.fitted:
            raise ValueError("Model must be fitted before getting results")
            
        # Calculate summary statistics
        posterior_means = {}
        posterior_medians = {}
        posterior_std = {}
        credible_intervals = {}
        
        for param, samples in self.posterior_samples.items():
            if len(samples.shape) == 1:
                # Scalar parameter
                posterior_means[param] = np.mean(samples)
                posterior_medians[param] = np.median(samples)
                posterior_std[param] = np.std(samples)
                credible_intervals[param] = (
                    np.percentile(samples, 2.5),
                    np.percentile(samples, 97.5)
                )
            else:
                # Vector parameter
                posterior_means[param] = np.mean(samples, axis=0)
                posterior_medians[param] = np.median(samples, axis=0)
                posterior_std[param] = np.std(samples, axis=0)
                credible_intervals[param] = (
                    np.percentile(samples, 2.5, axis=0),
                    np.percentile(samples, 97.5, axis=0)
                )
                
        # Zone-specific results
        zone_parameters = None
        zone_effects = None
        
        if self.zone_specific and self.zones is not None and 'gamma' in self.posterior_samples:
            zone_parameters = {}
            zone_effects = {}
            
            for i, zone in enumerate(self.unique_zones):
                zone_params = {}
                for j, var in enumerate(self.independent_vars):
                    zone_params[var] = {
                        'mean': posterior_means['gamma'][i, j],
                        'std': posterior_std['gamma'][i, j],
                        'ci_lower': credible_intervals['gamma'][0][i, j],
                        'ci_upper': credible_intervals['gamma'][1][i, j]
                    }
                zone_parameters[zone] = zone_params
                zone_effects[zone] = self.posterior_samples['gamma'][:, i, :]
                
        return BayesianPanelResults(
            posterior_samples=self.posterior_samples,
            posterior_means=posterior_means,
            posterior_medians=posterior_medians,
            posterior_std=posterior_std,
            credible_intervals=credible_intervals,
            effective_sample_size=self.diagnostics.get('ess', {}),
            rhat=self.diagnostics.get('rhat', {}),
            waic=getattr(self, 'waic', None),
            loo=getattr(self, 'loo', None),
            posterior_predictive=getattr(self, 'posterior_predictive', None),
            zone_parameters=zone_parameters,
            zone_effects=zone_effects,
            n_divergent=self.diagnostics.get('n_divergent', 0)
        )


# Integration with Three-Tier framework
def apply_bayesian_panel(panel_data: pd.DataFrame,
                        dependent_var: str,
                        independent_vars: List[str],
                        model_type: str = 'random_effects',
                        zone_specific: bool = True,
                        n_samples: int = 2000) -> BayesianPanelResults:
    """
    Apply Bayesian panel model in Three-Tier framework.
    
    Args:
        panel_data: Panel DataFrame
        dependent_var: Dependent variable name
        independent_vars: Independent variable names
        model_type: 'random_effects' or 'fixed_effects'
        zone_specific: Include zone-specific effects
        n_samples: Number of MCMC samples
        
    Returns:
        BayesianPanelResults object
    """
    logger.info(f"Applying Bayesian {model_type} model")
    
    # Initialize model
    model = BayesianPanelModel(
        model_type=model_type,
        hierarchical=True,
        zone_specific=zone_specific
    )
    
    # Set informative priors for Yemen context
    if 'exchange_rate' in independent_vars:
        # Informative prior on exchange rate effect
        beta_mean = np.zeros(len(independent_vars))
        beta_precision = np.ones(len(independent_vars)) * 0.001
        
        # Stronger prior on exchange rate
        er_idx = independent_vars.index('exchange_rate')
        beta_mean[er_idx] = -0.5  # Negative effect expected
        beta_precision[er_idx] = 0.1  # More precise
        
        priors = PriorSpecification(
            beta_mean=beta_mean,
            beta_precision=beta_precision
        )
        model.set_priors(priors)
        
    # Fit model
    model.fit(
        panel_data,
        dependent_var,
        independent_vars,
        n_samples=n_samples
    )
    
    # Get results
    results = model.get_results()
    
    # Log summary
    logger.info("Bayesian estimation complete:")
    logger.info(f"  - Effective sample size: {np.mean(list(results.effective_sample_size.values())):.0f}")
    logger.info(f"  - Max R-hat: {max(results.rhat.values()):.3f}")
    if results.waic:
        logger.info(f"  - WAIC: {results.waic:.1f}")
        
    return results