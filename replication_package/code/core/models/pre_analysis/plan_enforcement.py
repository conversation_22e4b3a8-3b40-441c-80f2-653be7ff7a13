"""
Pre-Analysis Plan Enforcement Module

This module enforces adherence to the pre-analysis plan by:
1. Validating all analysis steps against locked specifications
2. Preventing unauthorized deviations from the plan
3. Logging all analysis decisions for transparency
4. Ensuring proper multiple testing corrections

Version: 1.0
Status: LOCKED - NO MODIFICATIONS PERMITTED
"""

import logging
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import json
import pandas as pd
from dataclasses import dataclass

from .locked_specifications import LOCKED_SPECS, HypothesisType, TestType


@dataclass
class AnalysisStep:
    """Record of an analysis step for audit trail"""
    step_id: str
    timestamp: datetime
    hypothesis_id: str
    specification_used: str
    sample_size: int
    results: Dict[str, Any]
    deviations: List[str]
    analyst: str


class PreAnalysisPlanEnforcer:
    """
    Enforces strict adherence to the pre-analysis plan.
    
    This class acts as a gatekeeper that ensures:
    - Only pre-specified analyses are conducted
    - Correct multiple testing corrections are applied
    - All deviations are documented and justified
    - Analysis workflow follows the locked sequence
    """
    
    def __init__(self, analysis_log_path: Optional[str] = None):
        """Initialize the plan enforcer"""
        self.specs = LOCKED_SPECS
        self.analysis_log_path = analysis_log_path or "results/analysis_log.json"
        self.analysis_steps: List[AnalysisStep] = []
        self.current_phase = "initialization"
        self.permitted_phases = [
            "data_preparation",
            "descriptive_analysis", 
            "primary_hypothesis_testing",
            "secondary_hypothesis_testing",
            "robustness_testing",
            "results_integration"
        ]
        self.phase_index = 0
        
        # Set up logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('analysis_audit.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Log initialization
        self.logger.info(f"Pre-Analysis Plan Enforcer initialized")
        self.logger.info(f"Plan version: {self.specs._plan_version}")
        self.logger.info(f"Plan hash: {self.specs.plan_hash}")
        self.logger.info(f"Lock date: {self.specs.lock_date}")
        
    def validate_plan_integrity(self) -> bool:
        """Validate that the plan hasn't been tampered with"""
        if not self.specs.validate_specification_integrity():
            self.logger.error("CRITICAL: Pre-analysis plan integrity compromised!")
            raise RuntimeError("Plan integrity validation failed")
        return True
    
    def advance_to_phase(self, phase_name: str, justification: str) -> bool:
        """Advance to the next analysis phase"""
        if phase_name not in self.permitted_phases:
            raise ValueError(f"Invalid phase: {phase_name}")
        
        expected_phase_index = self.permitted_phases.index(phase_name)
        
        # Check if advancing in correct order
        if expected_phase_index != self.phase_index + 1:
            if expected_phase_index <= self.phase_index:
                self.logger.warning(f"Attempting to go backwards in analysis: {phase_name}")
                return False
            else:
                self.logger.error(f"Skipping phases: from {self.current_phase} to {phase_name}")
                raise RuntimeError("Cannot skip analysis phases")
        
        # Advance phase
        self.current_phase = phase_name
        self.phase_index = expected_phase_index
        
        self.logger.info(f"Advanced to phase: {phase_name}")
        self.logger.info(f"Justification: {justification}")
        
        return True
    
    def check_hypothesis_permission(self, hypothesis_id: str) -> bool:
        """Check if hypothesis testing is permitted in current phase"""
        hyp_config = self.specs.get_hypothesis_config(hypothesis_id)
        
        if hyp_config is None:
            self.logger.error(f"Unknown hypothesis: {hypothesis_id}")
            return False
        
        # Check phase permissions
        if hyp_config.hypothesis_type == HypothesisType.PRIMARY:
            if self.current_phase != "primary_hypothesis_testing":
                self.logger.error(f"Primary hypothesis {hypothesis_id} not permitted in phase {self.current_phase}")
                return False
        elif hyp_config.hypothesis_type == HypothesisType.SECONDARY:
            if self.current_phase != "secondary_hypothesis_testing":
                self.logger.error(f"Secondary hypothesis {hypothesis_id} not permitted in phase {self.current_phase}")
                return False
        
        return True
    
    def validate_specification(self, hypothesis_id: str, specification: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate that specification matches locked plan"""
        hyp_config = self.specs.get_hypothesis_config(hypothesis_id)
        if hyp_config is None:
            return False, [f"Unknown hypothesis: {hypothesis_id}"]
        
        deviations = []
        
        # Check model formula
        if specification.get("model_formula") != hyp_config.primary_specification.model_formula:
            deviations.append(f"Model formula deviation for {hypothesis_id}")
        
        # Check estimation method
        if specification.get("estimation_method") != hyp_config.primary_specification.estimation_method:
            deviations.append(f"Estimation method deviation for {hypothesis_id}")
        
        # Check fixed effects
        spec_fe = specification.get("fixed_effects", [])
        expected_fe = hyp_config.primary_specification.fixed_effects
        if set(spec_fe) != set(expected_fe):
            deviations.append(f"Fixed effects deviation for {hypothesis_id}")
        
        # Check clustering
        if specification.get("clustering_variable") != hyp_config.primary_specification.clustering_variable:
            deviations.append(f"Clustering variable deviation for {hypothesis_id}")
        
        # Log deviations
        if deviations:
            self.logger.warning(f"Specification deviations for {hypothesis_id}: {deviations}")
        
        return len(deviations) == 0, deviations
    
    def approve_analysis_step(
        self, 
        hypothesis_id: str,
        specification: Dict[str, Any],
        sample_description: Dict[str, Any],
        analyst: str
    ) -> bool:
        """Approve an analysis step if it conforms to the plan"""
        
        # Validate plan integrity first
        self.validate_plan_integrity()
        
        # Check if hypothesis is permitted
        if not self.check_hypothesis_permission(hypothesis_id):
            return False
        
        # Validate specification
        is_valid, deviations = self.validate_specification(hypothesis_id, specification)
        
        # Check sample criteria
        sample_deviations = self._validate_sample(sample_description)
        deviations.extend(sample_deviations)
        
        # Approve if no critical deviations
        if len(deviations) == 0:
            self.logger.info(f"Analysis step approved for {hypothesis_id}")
            return True
        else:
            self.logger.error(f"Analysis step rejected for {hypothesis_id}: {deviations}")
            return False
    
    def _validate_sample(self, sample_description: Dict[str, Any]) -> List[str]:
        """Validate sample against locked criteria"""
        deviations = []
        criteria = self.specs.sample_criteria
        
        # Check minimum sample size
        if sample_description.get("n_markets", 0) < criteria["inclusion_criteria"]["min_months_per_market"]:
            deviations.append("Sample size below minimum threshold")
        
        # Check time period
        if sample_description.get("time_period") != criteria["inclusion_criteria"]["temporal_scope"]:
            deviations.append("Time period deviates from plan")
        
        # Check exclusions applied
        expected_exclusions = criteria["exclusion_criteria"]
        applied_exclusions = sample_description.get("exclusions_applied", [])
        
        for exclusion, expected in expected_exclusions.items():
            if expected and exclusion not in applied_exclusions:
                deviations.append(f"Required exclusion not applied: {exclusion}")
        
        return deviations
    
    def record_analysis_step(
        self,
        hypothesis_id: str,
        specification: Dict[str, Any],
        results: Dict[str, Any],
        sample_size: int,
        analyst: str,
        deviations: List[str] = None
    ) -> None:
        """Record an analysis step in the audit trail"""
        
        step = AnalysisStep(
            step_id=f"{hypothesis_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            timestamp=datetime.now(),
            hypothesis_id=hypothesis_id,
            specification_used=json.dumps(specification, sort_keys=True),
            sample_size=sample_size,
            results=results,
            deviations=deviations or [],
            analyst=analyst
        )
        
        self.analysis_steps.append(step)
        
        self.logger.info(f"Recorded analysis step: {step.step_id}")
        
        # Save to file
        self._save_analysis_log()
    
    def apply_multiple_testing_correction(
        self, 
        hypothesis_results: Dict[str, Dict[str, float]]
    ) -> Dict[str, Dict[str, float]]:
        """Apply appropriate multiple testing corrections"""
        
        corrected_results = {}
        
        # Separate primary and secondary hypotheses
        primary_results = {}
        secondary_results = {}
        
        for hyp_id, results in hypothesis_results.items():
            hyp_config = self.specs.get_hypothesis_config(hyp_id)
            if hyp_config is None:
                continue
                
            if hyp_config.hypothesis_type == HypothesisType.PRIMARY:
                primary_results[hyp_id] = results
            elif hyp_config.hypothesis_type == HypothesisType.SECONDARY:
                secondary_results[hyp_id] = results
        
        # Apply Bonferroni correction to primary hypotheses
        if primary_results:
            corrected_primary = self._apply_bonferroni_correction(primary_results, n_tests=3)
            corrected_results.update(corrected_primary)
        
        # Apply Bonferroni correction to secondary hypotheses  
        if secondary_results:
            corrected_secondary = self._apply_bonferroni_correction(secondary_results, n_tests=10)
            corrected_results.update(corrected_secondary)
        
        self.logger.info(f"Applied multiple testing corrections to {len(corrected_results)} hypotheses")
        
        return corrected_results
    
    def _apply_fdr_correction(self, results: Dict[str, Dict[str, float]]) -> Dict[str, Dict[str, float]]:
        """Apply Benjamini-Hochberg FDR correction"""
        # Extract p-values
        p_values = [(hyp_id, res["p_value"]) for hyp_id, res in results.items()]
        p_values.sort(key=lambda x: x[1])  # Sort by p-value
        
        n = len(p_values)
        alpha = self.specs.multiple_testing_config["primary_alpha"]
        
        corrected_results = {}
        for i, (hyp_id, p_val) in enumerate(p_values):
            # BH critical value
            critical_value = alpha * (i + 1) / n
            is_significant = p_val <= critical_value
            
            corrected_results[hyp_id] = results[hyp_id].copy()
            corrected_results[hyp_id]["corrected_p_value"] = min(p_val * n / (i + 1), 1.0)
            corrected_results[hyp_id]["is_significant_corrected"] = is_significant
            corrected_results[hyp_id]["correction_method"] = "benjamini_hochberg"
        
        return corrected_results
    
    def _apply_bonferroni_correction(self, results: Dict[str, Dict[str, float]], n_tests: int = None) -> Dict[str, Dict[str, float]]:
        """Apply Bonferroni correction"""
        n = n_tests or len(results)
        
        # Determine alpha based on hypothesis type
        if n_tests == 3:  # Primary hypotheses
            alpha = self.specs.multiple_testing_config["primary_alpha"]
        else:  # Secondary hypotheses
            alpha = self.specs.multiple_testing_config["secondary_alpha"]
        
        corrected_alpha = alpha / n
        
        corrected_results = {}
        for hyp_id, res in results.items():
            corrected_results[hyp_id] = res.copy()
            corrected_results[hyp_id]["corrected_p_value"] = min(res["p_value"] * n, 1.0)
            corrected_results[hyp_id]["is_significant_corrected"] = res["p_value"] <= corrected_alpha
            corrected_results[hyp_id]["correction_method"] = "bonferroni"
        
        return corrected_results
    
    def check_stopping_rules(self) -> Tuple[bool, str]:
        """Check if analysis should stop according to plan"""
        
        # Check if all phases completed
        if self.phase_index >= len(self.permitted_phases) - 1:
            return True, "All planned phases completed"
        
        # Check if primary hypotheses tested
        primary_tested = [step for step in self.analysis_steps 
                         if self.specs.get_hypothesis_config(step.hypothesis_id).hypothesis_type == HypothesisType.PRIMARY]
        
        if len(primary_tested) >= 3 and self.current_phase == "primary_hypothesis_testing":
            return False, "Ready to advance to secondary testing"
        
        return False, "Continue with current phase"
    
    def generate_compliance_report(self) -> Dict[str, Any]:
        """Generate a compliance report for the analysis"""
        
        report = {
            "plan_version": self.specs._plan_version,
            "plan_hash": self.specs.plan_hash,
            "analysis_date": datetime.now().isoformat(),
            "current_phase": self.current_phase,
            "total_steps": len(self.analysis_steps),
            "deviations_count": sum(len(step.deviations) for step in self.analysis_steps),
            "phases_completed": self.phase_index + 1,
            "compliance_score": self._calculate_compliance_score(),
            "critical_violations": self._identify_critical_violations(),
            "recommendations": self._generate_recommendations()
        }
        
        return report
    
    def _calculate_compliance_score(self) -> float:
        """Calculate overall compliance score (0-1)"""
        if not self.analysis_steps:
            return 1.0
        
        total_steps = len(self.analysis_steps)
        steps_with_deviations = sum(1 for step in self.analysis_steps if step.deviations)
        
        return 1.0 - (steps_with_deviations / total_steps)
    
    def _identify_critical_violations(self) -> List[str]:
        """Identify critical violations of the plan"""
        violations = []
        
        # Check for major specification deviations
        for step in self.analysis_steps:
            for deviation in step.deviations:
                if "formula deviation" in deviation or "method deviation" in deviation:
                    violations.append(f"Critical specification deviation in {step.step_id}")
        
        # Check for phase violations
        if self.phase_index != len(self.permitted_phases) - 1:
            violations.append("Analysis incomplete - not all phases executed")
        
        return violations
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations for improving compliance"""
        recommendations = []
        
        compliance_score = self._calculate_compliance_score()
        
        if compliance_score < 0.9:
            recommendations.append("High deviation rate - review analysis procedures")
        
        if not self.analysis_steps:
            recommendations.append("No analysis steps recorded - ensure proper logging")
        
        if self.current_phase != "results_integration":
            recommendations.append("Complete all analysis phases before finalizing results")
        
        return recommendations
    
    def _save_analysis_log(self) -> None:
        """Save analysis log to file"""
        log_data = {
            "plan_hash": self.specs.plan_hash,
            "current_phase": self.current_phase,
            "analysis_steps": [
                {
                    "step_id": step.step_id,
                    "timestamp": step.timestamp.isoformat(),
                    "hypothesis_id": step.hypothesis_id,
                    "specification_used": step.specification_used,
                    "sample_size": step.sample_size,
                    "results": step.results,
                    "deviations": step.deviations,
                    "analyst": step.analyst
                }
                for step in self.analysis_steps
            ]
        }
        
        # Ensure directory exists
        Path(self.analysis_log_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(self.analysis_log_path, 'w') as f:
            json.dump(log_data, f, indent=2)
    
    def __enter__(self):
        """Context manager entry"""
        self.logger.info("Starting enforced analysis session")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.logger.info("Ending enforced analysis session")
        self._save_analysis_log()
        
        if exc_type is not None:
            self.logger.error(f"Analysis session ended with error: {exc_type.__name__}: {exc_val}")
        
        # Generate final compliance report
        compliance_report = self.generate_compliance_report()
        self.logger.info(f"Final compliance score: {compliance_report['compliance_score']:.2f}")
        
        return False  # Don't suppress exceptions