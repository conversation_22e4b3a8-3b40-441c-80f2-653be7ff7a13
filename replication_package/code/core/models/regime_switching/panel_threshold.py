"""
Panel threshold regression models for market integration analysis.

Implements <PERSON> (1999) panel threshold models to identify
critical exchange rate differentials that trigger market fragmentation.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from scipy import stats
import warnings
from joblib import Parallel, delayed

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class ThresholdTestResult:
    """Results from threshold effect testing."""
    threshold_value: float
    f_statistic: float
    p_value: float
    bootstrap_p_value: Optional[float]
    confidence_interval: Tuple[float, float]
    reject_null: bool


@dataclass
class PanelThresholdResults:
    """Results from panel threshold model estimation."""
    thresholds: List[float]
    n_regimes: int
    regime_coefficients: Dict[int, np.ndarray]
    regime_assignment: pd.DataFrame
    threshold_variable: str
    ssr: float
    r_squared: float
    regime_statistics: Dict[int, Dict]
    threshold_tests: List[ThresholdTestResult]
    fitted_values: pd.Series
    residuals: pd.Series


class PanelThresholdModel:
    """
    Panel threshold regression for identifying fragmentation thresholds.
    
    Based on <PERSON> (1999), this model identifies threshold values of
    exchange rate differentials that trigger changes in market integration.
    """
    
    def __init__(self,
                 max_thresholds: int = 2,
                 trim_pct: float = 0.15,
                 n_bootstrap: int = 300):
        """
        Initialize panel threshold model.
        
        Args:
            max_thresholds: Maximum number of thresholds to test
            trim_pct: Trimming percentage for threshold search
            n_bootstrap: Number of bootstrap replications for testing
        """
        self.max_thresholds = max_thresholds
        self.trim_pct = trim_pct
        self.n_bootstrap = n_bootstrap
        self.results = None
    
    def prepare_panel_data(self,
                          panel_data: pd.DataFrame,
                          dependent_var: str,
                          independent_vars: List[str],
                          threshold_var: str,
                          entity_var: str,
                          time_var: str) -> Tuple[pd.DataFrame, Dict]:
        """
        Prepare panel data for threshold regression.
        
        Args:
            panel_data: Panel dataset
            dependent_var: Name of dependent variable (price integration measure)
            independent_vars: List of independent variables
            threshold_var: Variable to use for thresholds (e.g., exchange_rate_diff)
            entity_var: Entity identifier (e.g., market_pair)
            time_var: Time identifier
            
        Returns:
            Tuple of (prepared data, metadata dict)
        """
        # Create copy and ensure proper indexing
        data = panel_data.copy()
        data = data.set_index([entity_var, time_var])
        
        # Extract variables
        y = data[dependent_var]
        X = data[independent_vars]
        q = data[threshold_var]  # Threshold variable
        
        # Remove missing values
        valid_idx = y.notna() & X.notna().all(axis=1) & q.notna()
        y_clean = y[valid_idx]
        X_clean = X[valid_idx]
        q_clean = q[valid_idx]
        
        # Combine for easier handling
        clean_data = pd.DataFrame({
            'y': y_clean,
            'q': q_clean
        })
        
        for col in independent_vars:
            clean_data[col] = X_clean[col]
        
        metadata = {
            'n_entities': len(clean_data.index.get_level_values(0).unique()),
            'n_periods': len(clean_data.index.get_level_values(1).unique()),
            'n_obs': len(clean_data),
            'entity_var': entity_var,
            'time_var': time_var,
            'dependent_var': dependent_var,
            'independent_vars': independent_vars,
            'threshold_var': threshold_var
        }
        
        return clean_data, metadata
    
    def fit(self,
            panel_data: pd.DataFrame,
            dependent_var: str,
            independent_vars: List[str],
            threshold_var: str,
            entity_var: str,
            time_var: str) -> PanelThresholdResults:
        """
        Fit panel threshold model.
        
        Args:
            panel_data: Panel dataset
            dependent_var: Dependent variable name
            independent_vars: List of independent variables
            threshold_var: Threshold variable name
            entity_var: Entity identifier
            time_var: Time identifier
            
        Returns:
            Model estimation results
        """
        logger.info(f"Fitting panel threshold model with max {self.max_thresholds} thresholds")
        
        # Prepare data
        data, metadata = self.prepare_panel_data(
            panel_data, dependent_var, independent_vars,
            threshold_var, entity_var, time_var
        )
        
        # Sequential threshold estimation
        thresholds = []
        threshold_tests = []
        
        for n_thresh in range(1, self.max_thresholds + 1):
            logger.info(f"Testing for threshold {n_thresh}")
            
            # Estimate threshold
            if n_thresh == 1:
                # Single threshold
                thresh, test_result = self._estimate_single_threshold(
                    data, independent_vars, threshold_var
                )
            else:
                # Multiple thresholds
                thresh, test_result = self._estimate_additional_threshold(
                    data, independent_vars, threshold_var, thresholds
                )
            
            # Test significance
            if test_result.reject_null:
                thresholds.append(thresh)
                threshold_tests.append(test_result)
                logger.info(f"Threshold {n_thresh} = {thresh:.4f} is significant (p={test_result.p_value:.4f})")
            else:
                logger.info(f"Threshold {n_thresh} is not significant. Stopping search.")
                break
        
        # Estimate final model with identified thresholds
        if thresholds:
            results = self._estimate_final_model(
                data, independent_vars, threshold_var, thresholds, threshold_tests
            )
        else:
            # No thresholds - linear model
            results = self._estimate_linear_model(
                data, independent_vars, threshold_var
            )
        
        self.results = results
        logger.info(f"Panel threshold model complete. Found {len(thresholds)} threshold(s)")
        
        return results
    
    def _estimate_single_threshold(self,
                                 data: pd.DataFrame,
                                 indep_vars: List[str],
                                 threshold_var: str) -> Tuple[float, ThresholdTestResult]:
        """Estimate single threshold model."""
        # Get threshold variable values
        q_values = data[threshold_var].values
        q_unique = np.unique(q_values)
        
        # Determine search grid (with trimming)
        n_obs = len(q_values)
        trim_n = int(n_obs * self.trim_pct)
        q_sorted = np.sort(q_unique)
        q_grid = q_sorted[trim_n:-trim_n]
        
        # Grid search for optimal threshold
        best_ssr = np.inf
        best_threshold = None
        
        for gamma in q_grid:
            # Split sample
            regime1 = data[data[threshold_var] <= gamma]
            regime2 = data[data[threshold_var] > gamma]
            
            # Check minimum observations
            if len(regime1) < trim_n or len(regime2) < trim_n:
                continue
            
            # Estimate regime-specific models
            ssr1 = self._estimate_regime_ssr(regime1, indep_vars)
            ssr2 = self._estimate_regime_ssr(regime2, indep_vars)
            
            total_ssr = ssr1 + ssr2
            
            if total_ssr < best_ssr:
                best_ssr = total_ssr
                best_threshold = gamma
        
        # Test threshold significance
        test_result = self._test_threshold_significance(
            data, indep_vars, threshold_var, best_threshold, n_thresholds=1
        )
        
        return best_threshold, test_result
    
    def _estimate_additional_threshold(self,
                                     data: pd.DataFrame,
                                     indep_vars: List[str],
                                     threshold_var: str,
                                     existing_thresholds: List[float]) -> Tuple[float, ThresholdTestResult]:
        """Estimate additional threshold given existing thresholds."""
        # Sort existing thresholds
        thresholds_sorted = sorted(existing_thresholds)
        
        # Define regimes based on existing thresholds
        regimes = self._assign_regimes(data[threshold_var], thresholds_sorted)
        
        # Search for new threshold within each regime
        best_ssr = np.inf
        best_threshold = None
        best_regime = None
        
        for regime_id in range(len(thresholds_sorted) + 1):
            # Get data for this regime
            regime_data = data[regimes == regime_id]
            
            if len(regime_data) < 2 * int(len(data) * self.trim_pct):
                continue
            
            # Search for threshold within this regime
            q_regime = regime_data[threshold_var].values
            q_unique = np.unique(q_regime)
            
            # Determine search bounds
            if regime_id == 0:
                q_min = q_unique.min()
                q_max = thresholds_sorted[0]
            elif regime_id == len(thresholds_sorted):
                q_min = thresholds_sorted[-1]
                q_max = q_unique.max()
            else:
                q_min = thresholds_sorted[regime_id - 1]
                q_max = thresholds_sorted[regime_id]
            
            # Grid within bounds
            q_grid = q_unique[(q_unique > q_min) & (q_unique < q_max)]
            trim_n = int(len(regime_data) * self.trim_pct)
            
            if len(q_grid) > 2 * trim_n:
                q_grid = q_grid[trim_n:-trim_n]
            
            # Search within this regime
            for gamma in q_grid:
                # Create new threshold list
                new_thresholds = thresholds_sorted + [gamma]
                new_thresholds.sort()
                
                # Calculate SSR with new threshold set
                ssr = self._calculate_multi_threshold_ssr(data, indep_vars, threshold_var, new_thresholds)
                
                if ssr < best_ssr:
                    best_ssr = ssr
                    best_threshold = gamma
                    best_regime = regime_id
        
        # Test significance of additional threshold
        test_result = self._test_threshold_significance(
            data, indep_vars, threshold_var, best_threshold, 
            n_thresholds=len(existing_thresholds) + 1
        )
        
        return best_threshold, test_result
    
    def _estimate_regime_ssr(self, regime_data: pd.DataFrame, indep_vars: List[str]) -> float:
        """Estimate sum of squared residuals for a regime."""
        if len(regime_data) == 0:
            return np.inf
        
        # Fixed effects regression within regime
        y = regime_data['y'].values
        X = regime_data[indep_vars].values
        
        # Entity dummies
        entities = regime_data.index.get_level_values(0)
        entity_dummies = pd.get_dummies(entities, drop_first=True).values
        
        # Combine regressors
        if entity_dummies.shape[1] > 0:
            X_full = np.column_stack([X, entity_dummies])
        else:
            X_full = X
        
        # OLS estimation
        try:
            beta = np.linalg.lstsq(X_full, y, rcond=None)[0]
            residuals = y - X_full @ beta
            return np.sum(residuals**2)
        except:
            return np.inf
    
    def _calculate_multi_threshold_ssr(self,
                                     data: pd.DataFrame,
                                     indep_vars: List[str],
                                     threshold_var: str,
                                     thresholds: List[float]) -> float:
        """Calculate SSR for model with multiple thresholds."""
        # Assign regimes
        regimes = self._assign_regimes(data[threshold_var], thresholds)
        
        # Calculate SSR for each regime
        total_ssr = 0
        for regime_id in range(len(thresholds) + 1):
            regime_data = data[regimes == regime_id]
            if len(regime_data) > 0:
                ssr = self._estimate_regime_ssr(regime_data, indep_vars)
                total_ssr += ssr
        
        return total_ssr
    
    def _assign_regimes(self, threshold_values: pd.Series, thresholds: List[float]) -> pd.Series:
        """Assign observations to regimes based on thresholds."""
        regimes = pd.Series(0, index=threshold_values.index)
        
        for i, thresh in enumerate(sorted(thresholds)):
            regimes[threshold_values > thresh] = i + 1
        
        return regimes
    
    def _test_threshold_significance(self,
                                   data: pd.DataFrame,
                                   indep_vars: List[str],
                                   threshold_var: str,
                                   threshold: float,
                                   n_thresholds: int) -> ThresholdTestResult:
        """Test significance of threshold using bootstrap."""
        # Calculate F-statistic
        if n_thresholds == 1:
            # Test of no threshold vs one threshold
            ssr_null = self._estimate_regime_ssr(data, indep_vars)
            ssr_alt = self._calculate_multi_threshold_ssr(
                data, indep_vars, threshold_var, [threshold]
            )
        else:
            # Test of (n-1) vs n thresholds
            # This would require storing previous thresholds
            # Simplified implementation
            ssr_null = self._estimate_regime_ssr(data, indep_vars)
            ssr_alt = self._calculate_multi_threshold_ssr(
                data, indep_vars, threshold_var, [threshold]
            )
        
        n = len(data)
        k = len(indep_vars) + data.index.get_level_values(0).nunique() - 1
        
        f_stat = (ssr_null - ssr_alt) / (ssr_alt / (n - k))
        
        # Bootstrap p-value (simplified)
        if self.n_bootstrap > 0:
            bootstrap_p = self._bootstrap_threshold_test(
                data, indep_vars, threshold_var, f_stat
            )
        else:
            bootstrap_p = None
        
        # Asymptotic p-value (Hansen distribution)
        # Simplified - use chi-square approximation
        p_value = 1 - stats.chi2.cdf(f_stat, df=1)
        
        # Confidence interval for threshold
        ci = self._threshold_confidence_interval(
            data, indep_vars, threshold_var, threshold
        )
        
        return ThresholdTestResult(
            threshold_value=threshold,
            f_statistic=f_stat,
            p_value=p_value,
            bootstrap_p_value=bootstrap_p,
            confidence_interval=ci,
            reject_null=p_value < 0.05
        )
    
    def _bootstrap_threshold_test(self,
                                data: pd.DataFrame,
                                indep_vars: List[str],
                                threshold_var: str,
                                observed_f: float) -> float:
        """Bootstrap p-value for threshold test."""
        # Simplified bootstrap - in practice would use Hansen's fixed regressor bootstrap
        bootstrap_f_stats = []
        
        for _ in range(min(self.n_bootstrap, 100)):  # Limit for speed
            # Resample residuals
            boot_data = data.sample(n=len(data), replace=True)
            
            # Estimate threshold on bootstrap sample
            thresh_boot, _ = self._estimate_single_threshold(
                boot_data, indep_vars, threshold_var
            )
            
            # Calculate F-statistic
            ssr_null = self._estimate_regime_ssr(boot_data, indep_vars)
            ssr_alt = self._calculate_multi_threshold_ssr(
                boot_data, indep_vars, threshold_var, [thresh_boot]
            )
            
            n = len(boot_data)
            k = len(indep_vars) + boot_data.index.get_level_values(0).nunique() - 1
            
            f_boot = (ssr_null - ssr_alt) / (ssr_alt / (n - k))
            bootstrap_f_stats.append(f_boot)
        
        # Calculate p-value
        p_value = np.mean(np.array(bootstrap_f_stats) > observed_f)
        
        return p_value
    
    def _threshold_confidence_interval(self,
                                     data: pd.DataFrame,
                                     indep_vars: List[str], 
                                     threshold_var: str,
                                     threshold: float) -> Tuple[float, float]:
        """Compute confidence interval for threshold."""
        # Likelihood ratio based CI
        # Get SSR at estimated threshold
        ssr_min = self._calculate_multi_threshold_ssr(
            data, indep_vars, threshold_var, [threshold]
        )
        
        # Critical value (5% level)
        crit_val = stats.chi2.ppf(0.95, df=1)
        
        # Search for CI bounds
        q_values = np.unique(data[threshold_var])
        ci_lower = threshold
        ci_upper = threshold
        
        # Search downward
        for q in reversed(q_values[q_values < threshold]):
            ssr_q = self._calculate_multi_threshold_ssr(
                data, indep_vars, threshold_var, [q]
            )
            lr_stat = len(data) * np.log(ssr_q / ssr_min)
            
            if lr_stat <= crit_val:
                ci_lower = q
            else:
                break
        
        # Search upward
        for q in q_values[q_values > threshold]:
            ssr_q = self._calculate_multi_threshold_ssr(
                data, indep_vars, threshold_var, [q]
            )
            lr_stat = len(data) * np.log(ssr_q / ssr_min)
            
            if lr_stat <= crit_val:
                ci_upper = q
            else:
                break
        
        return (ci_lower, ci_upper)
    
    def _estimate_final_model(self,
                            data: pd.DataFrame,
                            indep_vars: List[str],
                            threshold_var: str,
                            thresholds: List[float],
                            threshold_tests: List[ThresholdTestResult]) -> PanelThresholdResults:
        """Estimate final model with identified thresholds."""
        # Assign regimes
        regimes = self._assign_regimes(data[threshold_var], thresholds)
        n_regimes = len(thresholds) + 1
        
        # Estimate regime-specific coefficients
        regime_coefficients = {}
        regime_statistics = {}
        fitted_values = pd.Series(index=data.index, dtype=float)
        
        for regime_id in range(n_regimes):
            regime_data = data[regimes == regime_id]
            
            if len(regime_data) > 0:
                # Estimate coefficients
                y = regime_data['y'].values
                X = regime_data[indep_vars].values
                
                # Add entity fixed effects
                entities = regime_data.index.get_level_values(0)
                entity_dummies = pd.get_dummies(entities, drop_first=True).values
                
                if entity_dummies.shape[1] > 0:
                    X_full = np.column_stack([X, entity_dummies])
                else:
                    X_full = X
                
                # OLS
                beta = np.linalg.lstsq(X_full, y, rcond=None)[0]
                y_fit = X_full @ beta
                
                # Store results
                regime_coefficients[regime_id] = beta[:len(indep_vars)]
                fitted_values.loc[regime_data.index] = y_fit
                
                # Regime statistics
                regime_statistics[regime_id] = {
                    'n_obs': len(regime_data),
                    'mean_threshold_var': regime_data[threshold_var].mean(),
                    'std_threshold_var': regime_data[threshold_var].std()
                }
        
        # Calculate residuals and R-squared
        residuals = data['y'] - fitted_values
        ssr = np.sum(residuals**2)
        sst = np.sum((data['y'] - data['y'].mean())**2)
        r_squared = 1 - ssr / sst
        
        # Create regime assignment DataFrame
        regime_assignment = pd.DataFrame({
            'regime': regimes,
            threshold_var: data[threshold_var]
        })
        
        return PanelThresholdResults(
            thresholds=thresholds,
            n_regimes=n_regimes,
            regime_coefficients=regime_coefficients,
            regime_assignment=regime_assignment,
            threshold_variable=threshold_var,
            ssr=ssr,
            r_squared=r_squared,
            regime_statistics=regime_statistics,
            threshold_tests=threshold_tests,
            fitted_values=fitted_values,
            residuals=residuals
        )
    
    def _estimate_linear_model(self,
                             data: pd.DataFrame,
                             indep_vars: List[str],
                             threshold_var: str) -> PanelThresholdResults:
        """Estimate linear model when no thresholds are found."""
        # Standard panel regression
        y = data['y'].values
        X = data[indep_vars].values
        
        # Entity fixed effects
        entities = data.index.get_level_values(0)
        entity_dummies = pd.get_dummies(entities, drop_first=True).values
        
        if entity_dummies.shape[1] > 0:
            X_full = np.column_stack([X, entity_dummies])
        else:
            X_full = X
        
        # OLS
        beta = np.linalg.lstsq(X_full, y, rcond=None)[0]
        fitted = X_full @ beta
        residuals = y - fitted
        
        # R-squared
        ssr = np.sum(residuals**2)
        sst = np.sum((y - y.mean())**2)
        r_squared = 1 - ssr / sst
        
        # Package as single-regime result
        return PanelThresholdResults(
            thresholds=[],
            n_regimes=1,
            regime_coefficients={0: beta[:len(indep_vars)]},
            regime_assignment=pd.DataFrame({
                'regime': 0,
                threshold_var: data[threshold_var]
            }),
            threshold_variable=threshold_var,
            ssr=ssr,
            r_squared=r_squared,
            regime_statistics={
                0: {
                    'n_obs': len(data),
                    'mean_threshold_var': data[threshold_var].mean(),
                    'std_threshold_var': data[threshold_var].std()
                }
            },
            threshold_tests=[],
            fitted_values=pd.Series(fitted, index=data.index),
            residuals=pd.Series(residuals, index=data.index)
        )