"""
Markov-switching models for currency regime detection.

Implements regime-switching models that can detect transitions between:
- Stable currency periods
- Transition/adjustment periods  
- Crisis/fragmentation periods
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import statsmodels.api as sm
from scipy import stats
from sklearn.preprocessing import StandardScaler

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class RegimeState:
    """Represents a single regime state."""
    id: int
    name: str
    mean_spread: float
    volatility: float
    transition_prob: Dict[int, float]


@dataclass
class MSResults:
    """Results from Markov-switching model estimation."""
    regimes: List[RegimeState]
    filtered_probs: pd.DataFrame
    smoothed_probs: pd.DataFrame
    regime_sequence: pd.Series
    log_likelihood: float
    aic: float
    bic: float
    transition_matrix: np.ndarray
    regime_durations: Dict[int, float]
    model_params: Dict


@dataclass
class RegimePrediction:
    """Prediction of regime change probability."""
    current_regime: int
    regime_probs: Dict[int, float]
    change_probability: float
    expected_duration: float
    warning_level: str  # 'low', 'medium', 'high', 'critical'


class MarkovSwitchingCurrencyModel:
    """
    Detects currency regime changes using Markov-switching models.
    
    This model identifies different states of currency market integration:
    - Regime 0: Stable/Integrated (low spread, low volatility)
    - Regime 1: Transition (moderate spread, rising volatility)
    - Regime 2: Crisis/Fragmented (high spread, high volatility)
    """
    
    def __init__(self, n_regimes: int = 3, switching_variance: bool = True):
        """
        Initialize Markov-switching model.
        
        Args:
            n_regimes: Number of regimes (default 3: stable, transition, crisis)
            switching_variance: Whether variance switches across regimes
        """
        self.n_regimes = n_regimes
        self.switching_variance = switching_variance
        self.model = None
        self.results = None
        self.scaler = StandardScaler()
        
        # Define regime interpretations
        self.regime_names = {
            0: "Stable/Integrated",
            1: "Transition/Adjustment", 
            2: "Crisis/Fragmented"
        }
        
        # Warning thresholds
        self.warning_thresholds = {
            'low': 0.1,
            'medium': 0.3,
            'high': 0.5,
            'critical': 0.7
        }
    
    def prepare_data(self, exchange_spreads: pd.Series) -> pd.Series:
        """
        Prepare exchange rate spread data for regime-switching analysis.
        
        Args:
            exchange_spreads: Series of exchange rate spreads between zones
            
        Returns:
            Prepared data series
        """
        # Handle missing values
        spreads_clean = exchange_spreads.dropna()
        
        # Log transform to handle skewness
        spreads_log = np.log1p(spreads_clean)
        
        # Standardize
        spreads_scaled = self.scaler.fit_transform(spreads_log.values.reshape(-1, 1)).flatten()
        
        return pd.Series(spreads_scaled, index=spreads_clean.index)
    
    def fit(self, exchange_spreads: pd.Series) -> MSResults:
        """
        Fit Markov-switching model to exchange rate spreads.
        
        Args:
            exchange_spreads: Time series of exchange rate spreads
            
        Returns:
            Model results with regime identification
        """
        logger.info(f"Fitting Markov-switching model with {self.n_regimes} regimes")
        
        # Prepare data
        data = self.prepare_data(exchange_spreads)
        
        # Create and fit model
        if self.switching_variance:
            # Both mean and variance switch
            self.model = sm.tsa.MarkovRegression(
                data,
                k_regimes=self.n_regimes,
                trend='c',
                switching_variance=True
            )
        else:
            # Only mean switches
            self.model = sm.tsa.MarkovRegression(
                data,
                k_regimes=self.n_regimes,
                trend='c',
                switching_variance=False
            )
        
        # Fit model
        self.results = self.model.fit(
            search_reps=20,
            disp=False
        )
        
        # Extract results
        ms_results = self._extract_results(data)
        
        logger.info(f"Model fit complete. Log-likelihood: {ms_results.log_likelihood:.2f}")
        
        return ms_results
    
    def _extract_results(self, data: pd.Series) -> MSResults:
        """Extract and organize results from fitted model."""
        # Get regime properties
        regimes = []
        for i in range(self.n_regimes):
            # Extract parameters for each regime
            regime_mean = self.results.params[f'const[{i}]']
            
            if self.switching_variance:
                regime_var = self.results.params[f'sigma2[{i}]']
                regime_vol = np.sqrt(regime_var)
            else:
                regime_vol = np.sqrt(self.results.params['sigma2'])
            
            # Get transition probabilities
            trans_probs = {}
            for j in range(self.n_regimes):
                if i == j:
                    trans_probs[j] = self.results.transition_probs[i, j]
                else:
                    trans_probs[j] = self.results.transition_probs[i, j]
            
            regimes.append(RegimeState(
                id=i,
                name=self.regime_names.get(i, f"Regime {i}"),
                mean_spread=float(regime_mean),
                volatility=float(regime_vol),
                transition_prob=trans_probs
            ))
        
        # Get filtered and smoothed probabilities
        filtered_probs = pd.DataFrame(
            self.results.filtered_marginal_probabilities,
            index=data.index,
            columns=[f"Regime_{i}" for i in range(self.n_regimes)]
        )
        
        smoothed_probs = pd.DataFrame(
            self.results.smoothed_marginal_probabilities,
            index=data.index,
            columns=[f"Regime_{i}" for i in range(self.n_regimes)]
        )
        
        # Get most likely regime sequence
        regime_sequence = smoothed_probs.idxmax(axis=1).str.extract(r'(\d+)')[0].astype(int)
        regime_sequence = pd.Series(regime_sequence.values, index=data.index, name='regime')
        
        # Calculate average regime durations
        regime_durations = self._calculate_regime_durations()
        
        # Model parameters
        model_params = {
            'n_params': len(self.results.params),
            'convergence': self.results.mle_retvals['converged'],
            'iterations': self.results.mle_retvals['iterations']
        }
        
        return MSResults(
            regimes=regimes,
            filtered_probs=filtered_probs,
            smoothed_probs=smoothed_probs,
            regime_sequence=regime_sequence,
            log_likelihood=float(self.results.llf),
            aic=float(self.results.aic),
            bic=float(self.results.bic),
            transition_matrix=self.results.transition_probs,
            regime_durations=regime_durations,
            model_params=model_params
        )
    
    def _calculate_regime_durations(self) -> Dict[int, float]:
        """Calculate expected duration in each regime."""
        durations = {}
        
        for i in range(self.n_regimes):
            # Expected duration = 1 / (1 - staying probability)
            stay_prob = self.results.transition_probs[i, i]
            if stay_prob < 0.999:  # Avoid division by very small numbers
                durations[i] = 1 / (1 - stay_prob)
            else:
                durations[i] = np.inf
                
        return durations
    
    def get_regime_probabilities(self) -> pd.DataFrame:
        """
        Get smoothed probabilities of being in each regime.
        
        Returns:
            DataFrame with probability of each regime over time
        """
        if self.results is None:
            raise ValueError("Model must be fitted first")
            
        return pd.DataFrame(
            self.results.smoothed_marginal_probabilities,
            columns=[self.regime_names.get(i, f"Regime_{i}") for i in range(self.n_regimes)]
        )
    
    def predict_regime_change(self, 
                            current_spread: float,
                            recent_spreads: pd.Series,
                            horizon: int = 4) -> RegimePrediction:
        """
        Predict probability of regime change in next periods.
        
        Args:
            current_spread: Current exchange rate spread
            recent_spreads: Recent history of spreads (for filtering)
            horizon: Forecast horizon in weeks
            
        Returns:
            Regime prediction with change probabilities
        """
        if self.results is None:
            raise ValueError("Model must be fitted first")
        
        # Prepare current data
        current_scaled = self.scaler.transform([[np.log1p(current_spread)]])[0, 0]
        
        # Get current regime probabilities using recent data
        recent_data = self.prepare_data(recent_spreads)
        
        # Run filter to get current state probabilities
        current_probs = self._filter_current_state(recent_data, current_scaled)
        
        # Most likely current regime
        current_regime = np.argmax(current_probs)
        
        # Calculate change probability over horizon
        change_prob = self._calculate_change_probability(current_regime, horizon)
        
        # Determine warning level
        warning_level = self._get_warning_level(change_prob, current_regime)
        
        # Expected duration in current regime
        expected_duration = self.regime_durations.get(current_regime, np.inf)
        
        return RegimePrediction(
            current_regime=int(current_regime),
            regime_probs={i: float(current_probs[i]) for i in range(self.n_regimes)},
            change_probability=float(change_prob),
            expected_duration=float(expected_duration),
            warning_level=warning_level
        )
    
    def _filter_current_state(self, recent_data: pd.Series, current_value: float) -> np.ndarray:
        """Filter to get current regime probabilities."""
        # Simplified filtering - in practice would use Kalman filter
        # Here we use the model's filtered probabilities for the most recent observation
        
        if len(recent_data) > 0:
            # Append current value
            full_data = pd.concat([recent_data, pd.Series([current_value])])
            
            # Rerun filter (simplified - in production would use online filtering)
            temp_model = sm.tsa.MarkovRegression(
                full_data,
                k_regimes=self.n_regimes,
                trend='c',
                switching_variance=self.switching_variance
            )
            
            # Use previous parameters as starting values for speed
            temp_results = temp_model.filter(self.results.params)
            
            # Get last filtered probability
            return temp_results.filtered_marginal_probabilities[-1]
        else:
            # Use steady-state probabilities
            return self.results.steady_state_marginal_probabilities
    
    def _calculate_change_probability(self, current_regime: int, horizon: int) -> float:
        """Calculate probability of regime change within horizon."""
        # Get transition matrix
        P = self.results.transition_probs
        
        # Calculate probability of staying in current regime for horizon periods
        stay_prob = P[current_regime, current_regime] ** horizon
        
        # Probability of change is complement
        return 1 - stay_prob
    
    def _get_warning_level(self, change_prob: float, current_regime: int) -> str:
        """Determine warning level based on change probability and current regime."""
        # Higher warning if currently stable and might transition
        if current_regime == 0:  # Currently stable
            # Lower thresholds for warnings when leaving stability
            if change_prob >= 0.5:
                return 'critical'
            elif change_prob >= 0.3:
                return 'high'
            elif change_prob >= 0.15:
                return 'medium'
            else:
                return 'low'
        else:
            # Standard thresholds for other regimes
            for level, threshold in sorted(self.warning_thresholds.items(), 
                                         key=lambda x: x[1], reverse=True):
                if change_prob >= threshold:
                    return level
            return 'low'
    
    def plot_regime_probabilities(self, results: MSResults, save_path: Optional[str] = None):
        """Plot regime probabilities over time."""
        import matplotlib.pyplot as plt
        
        fig, axes = plt.subplots(2, 1, figsize=(12, 8), sharex=True)
        
        # Plot regime probabilities
        ax1 = axes[0]
        for i in range(self.n_regimes):
            ax1.fill_between(
                results.smoothed_probs.index,
                0,
                results.smoothed_probs[f'Regime_{i}'],
                alpha=0.6,
                label=self.regime_names.get(i, f'Regime {i}')
            )
        
        ax1.set_ylabel('Regime Probability')
        ax1.set_title('Currency Regime Probabilities Over Time')
        ax1.legend(loc='upper right')
        ax1.set_ylim(0, 1)
        
        # Plot regime sequence
        ax2 = axes[1]
        regime_colors = {0: 'green', 1: 'orange', 2: 'red'}
        
        for regime in range(self.n_regimes):
            mask = results.regime_sequence == regime
            ax2.scatter(
                results.regime_sequence.index[mask],
                results.regime_sequence[mask],
                c=regime_colors.get(regime, 'blue'),
                label=self.regime_names.get(regime, f'Regime {regime}'),
                alpha=0.7,
                s=20
            )
        
        ax2.set_ylabel('Regime')
        ax2.set_xlabel('Date')
        ax2.set_title('Most Likely Regime Sequence')
        ax2.set_yticks(range(self.n_regimes))
        ax2.set_yticklabels([self.regime_names.get(i, f'Regime {i}') for i in range(self.n_regimes)])
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Regime probability plot saved to {save_path}")
        
        return fig