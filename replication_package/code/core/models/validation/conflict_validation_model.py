"""Conflict validation model for analyzing conflict effects on market integration.

This model validates how conflict intensity affects price transmission
and market integration patterns.
"""

from typing import List, Dict, Any, Optional
import pandas as pd
import numpy as np

from core.models.interfaces import Model, ModelSpecification
from infrastructure.logging import Logger

logger = get_logger(__name__)


class ConflictValidationModel(Model):
    """Model for validating conflict effects on market integration.
    
    This model tests whether conflict intensity significantly affects:
    - Price transmission between markets
    - Speed of adjustment to equilibrium
    - Market integration patterns
    """
    
    def __init__(self, specification: ModelSpecification):
        """Initialize conflict validation model.
        
        Args:
            specification: Model specification with conflict variables
        """
        self.specification = specification
        self.conflict_var = specification.features.get('conflict_variable', 'conflict_events')
        self.threshold_quantiles = specification.features.get('threshold_quantiles', [0.25, 0.5, 0.75])
        
    @property
    def name(self) -> str:
        """Model name for identification."""
        return f"ConflictValidation_{self.conflict_var}"
    
    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate that data meets conflict validation requirements.
        
        Args:
            data: Input data to validate
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        # Check for required columns
        required = [self.specification.dependent_var] + self.specification.independent_vars
        if self.conflict_var not in data.columns:
            errors.append(f"Conflict variable '{self.conflict_var}' not found in data")
        
        for col in required:
            if col not in data.columns:
                errors.append(f"Required column '{col}' not found in data")
        
        # Check for sufficient variation in conflict variable
        if self.conflict_var in data.columns:
            unique_values = data[self.conflict_var].nunique()
            if unique_values < 2:
                errors.append(f"Insufficient variation in conflict variable (only {unique_values} unique values)")
        
        # Check for minimum observations
        if len(data) < 100:
            errors.append(f"Insufficient observations for conflict validation: {len(data)} < 100")
        
        return errors
    
    def prepare_conflict_regimes(self, data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Split data into conflict intensity regimes.
        
        Args:
            data: Input data with conflict variable
            
        Returns:
            Dictionary mapping regime names to data subsets
        """
        logger.info(f"Splitting data by conflict intensity using quantiles: {self.threshold_quantiles}")
        
        conflict_values = data[self.conflict_var]
        
        # Calculate quantile thresholds
        thresholds = conflict_values.quantile(self.threshold_quantiles).values
        
        # Create regimes
        regimes = {}
        
        # Low conflict regime
        low_mask = conflict_values <= thresholds[0]
        regimes['low_conflict'] = data[low_mask].copy()
        
        # Medium conflict regime(s)
        for i in range(len(thresholds) - 1):
            regime_name = f'medium_conflict_{i+1}'
            mask = (conflict_values > thresholds[i]) & (conflict_values <= thresholds[i+1])
            regimes[regime_name] = data[mask].copy()
        
        # High conflict regime
        high_mask = conflict_values > thresholds[-1]
        regimes['high_conflict'] = data[high_mask].copy()
        
        # Log regime sizes
        for regime_name, regime_data in regimes.items():
            logger.info(f"{regime_name}: {len(regime_data)} observations "
                       f"({len(regime_data)/len(data)*100:.1f}%)")
        
        return regimes
    
    def test_regime_differences(self, regime_results: Dict[str, Any]) -> Dict[str, Any]:
        """Test for significant differences between conflict regimes.
        
        Args:
            regime_results: Estimation results for each regime
            
        Returns:
            Dictionary with test results
        """
        logger.info("Testing for significant differences between conflict regimes")
        
        test_results = {
            'chow_tests': {},
            'parameter_differences': {},
            'adjustment_speed_comparison': {},
            'overall_conclusion': ''
        }
        
        # Extract regime names
        regime_names = list(regime_results.keys())
        
        # Compare each pair of regimes
        for i in range(len(regime_names)):
            for j in range(i + 1, len(regime_names)):
                regime1, regime2 = regime_names[i], regime_names[j]
                
                # Chow test for structural break between regimes
                chow_result = self._chow_test(
                    regime_results[regime1], 
                    regime_results[regime2]
                )
                test_results['chow_tests'][f"{regime1}_vs_{regime2}"] = chow_result
                
                # Parameter differences
                param_diff = self._compare_parameters(
                    regime_results[regime1], 
                    regime_results[regime2]
                )
                test_results['parameter_differences'][f"{regime1}_vs_{regime2}"] = param_diff
        
        # Compare adjustment speeds if available
        if all('adjustment_speed' in res for res in regime_results.values()):
            for regime_name, results in regime_results.items():
                test_results['adjustment_speed_comparison'][regime_name] = {
                    'speed': results.get('adjustment_speed'),
                    'half_life': self._calculate_half_life(results.get('adjustment_speed'))
                }
        
        # Overall conclusion
        significant_differences = any(
            test['reject_equality'] 
            for test in test_results['chow_tests'].values()
        )
        
        if significant_differences:
            test_results['overall_conclusion'] = (
                "Significant differences found between conflict regimes. "
                "Conflict intensity affects market integration patterns."
            )
        else:
            test_results['overall_conclusion'] = (
                "No significant differences between conflict regimes. "
                "Market integration appears robust to conflict variations."
            )
        
        return test_results
    
    def _chow_test(self, results1: Dict[str, Any], 
                   results2: Dict[str, Any]) -> Dict[str, Any]:
        """Perform Chow test for parameter equality between regimes.
        
        Args:
            results1: Results from first regime
            results2: Results from second regime
            
        Returns:
            Dictionary with Chow test results
        """
        # Extract needed statistics
        rss1 = results1.get('rss', 0)
        rss2 = results2.get('rss', 0)
        rss_pooled = results1.get('rss_pooled', rss1 + rss2)
        
        n1 = results1.get('n_obs', 0)
        n2 = results2.get('n_obs', 0)
        k = len(results1.get('params', {}))
        
        if n1 == 0 or n2 == 0 or k == 0:
            return {
                'statistic': np.nan,
                'p_value': np.nan,
                'reject_equality': False,
                'error': 'Insufficient data for test'
            }
        
        # Calculate F-statistic
        numerator = (rss_pooled - (rss1 + rss2)) / k
        denominator = (rss1 + rss2) / (n1 + n2 - 2*k)
        
        f_stat = numerator / denominator if denominator > 0 else np.nan
        
        # Calculate p-value
        from scipy import stats
        p_value = 1 - stats.f.cdf(f_stat, k, n1 + n2 - 2*k) if not np.isnan(f_stat) else np.nan
        
        return {
            'statistic': f_stat,
            'p_value': p_value,
            'df1': k,
            'df2': n1 + n2 - 2*k,
            'reject_equality': p_value < 0.05 if not np.isnan(p_value) else False
        }
    
    def _compare_parameters(self, results1: Dict[str, Any],
                           results2: Dict[str, Any]) -> Dict[str, Any]:
        """Compare parameters between two regimes.
        
        Args:
            results1: Results from first regime
            results2: Results from second regime
            
        Returns:
            Dictionary with parameter comparisons
        """
        params1 = results1.get('params', {})
        params2 = results2.get('params', {})
        se1 = results1.get('standard_errors', {})
        se2 = results2.get('standard_errors', {})
        
        comparisons = {}
        
        for param in set(params1.keys()) | set(params2.keys()):
            if param in params1 and param in params2:
                # Calculate difference and test statistic
                diff = params1[param] - params2[param]
                se_diff = np.sqrt(se1.get(param, 0)**2 + se2.get(param, 0)**2)
                
                z_stat = diff / se_diff if se_diff > 0 else np.nan
                p_value = 2 * (1 - stats.norm.cdf(abs(z_stat))) if not np.isnan(z_stat) else np.nan
                
                comparisons[param] = {
                    'value_regime1': params1[param],
                    'value_regime2': params2[param],
                    'difference': diff,
                    'z_statistic': z_stat,
                    'p_value': p_value,
                    'significant': p_value < 0.05 if not np.isnan(p_value) else False
                }
        
        return comparisons
    
    def _calculate_half_life(self, adjustment_speed: Optional[float]) -> Optional[float]:
        """Calculate half-life of shock from adjustment speed.
        
        Args:
            adjustment_speed: Speed of adjustment parameter
            
        Returns:
            Half-life in periods, or None if not calculable
        """
        if adjustment_speed is None or adjustment_speed >= 0 or adjustment_speed <= -2:
            return None
        
        # Half-life = ln(0.5) / ln(1 + adjustment_speed)
        return np.log(0.5) / np.log(1 + adjustment_speed)