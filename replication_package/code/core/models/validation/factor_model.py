"""Factor model for identifying common drivers of price movements.

This model extracts latent factors that drive price co-movements
across markets and commodities.
"""

from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
import numpy as np
from sklearn.decomposition import FactorAnalysis
from sklearn.preprocessing import StandardScaler

from ..interfaces import Model, ModelSpecification
from ....infrastructure.logging import Logger
from .helpers.factor_analysis_helpers import (
    determine_n_factors,
    calculate_variance_explained,
    interpret_factors,
    test_factor_adequacy
)

logger = get_logger(__name__)


class FactorModel(Model):
    """Factor model for analyzing common price drivers.
    
    This model uses factor analysis to identify latent factors that
    explain co-movements in prices across markets, helping to understand
    the underlying structure of market integration.
    """
    
    def __init__(self, specification: ModelSpecification):
        """Initialize factor model.
        
        Args:
            specification: Model specification with factor analysis parameters
        """
        self.specification = specification
        self.n_factors = specification.features.get('n_factors', 'auto')
        self.rotation = specification.features.get('rotation', 'varimax')
        self.standardize = specification.features.get('standardize', True)
        self.min_variance_explained = specification.features.get('min_variance_explained', 0.7)
        self.max_factors = specification.features.get('max_factors', 10) # For auto-selection
        
        self.scaler: Optional[StandardScaler] = None
        self.factor_analysis_object: Optional[FactorAnalysis] = None
        
    @property
    def name(self) -> str:
        """Model name for identification."""
        n_factors_str = self.n_factors if isinstance(self.n_factors, int) else 'auto'
        return f"FactorModel_{n_factors_str}factors"
    
    @property
    def model_type(self) -> str:
        """Get model type."""
        return "validation"
    
    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate that data meets factor analysis requirements.
        
        Args:
            data: Input data to validate
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        # Check for numeric columns
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) < 3:
            errors.append(f"Insufficient numeric variables for factor analysis: {len(numeric_cols)} < 3")
        
        # Check for missing values
        if data[numeric_cols].isnull().any().any():
            missing_pct = data[numeric_cols].isnull().sum().sum() / (len(data) * len(numeric_cols)) * 100
            errors.append(f"Missing values detected ({missing_pct:.1f}% of data)")
        
        # Check sample size
        n_obs = len(data)
        n_vars = len(numeric_cols)
        if n_obs < 5 * n_vars:
            errors.append(f"Sample size ({n_obs}) may be insufficient for {n_vars} variables "
                         f"(recommended: {5 * n_vars})")
        
        # Check for constant columns
        constant_cols = [col for col in numeric_cols if data[col].nunique() == 1]
        if constant_cols:
            errors.append(f"Constant columns found: {constant_cols}")
        
        return errors
    
    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for factor analysis estimation.
        
        Args:
            data: DataFrame with variables for factor analysis
            
        Returns:
            Prepared DataFrame
        """
        logger.info("Preparing data for Factor Model")
        
        numeric_data = data.select_dtypes(include=[np.number])
        clean_data = numeric_data.dropna()
        
        if self.standardize:
            self.scaler = StandardScaler()
            scaled_data = self.scaler.fit_transform(clean_data)
            prepared_data = pd.DataFrame(
                scaled_data, 
                index=clean_data.index,
                columns=clean_data.columns
            )
            logger.info("Data standardized.")
        else:
            prepared_data = clean_data
        
        return prepared_data
        
    def fit(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Fit factor model to the data.
        
        Args:
            data: Prepared DataFrame with variables for factor analysis
            
        Returns:
            Dictionary with factor analysis results
        """
        validation_errors = self.validate_data(data)
        if validation_errors:
            raise ValueError(f"Data validation failed: {'; '.join(validation_errors)}")
            
        prepared_data = self.prepare_data(data)
        
        logger.info(f"Fitting Factor Model with n_factors={self.n_factors}")
        
        # Determine optimal number of factors if not specified
        n_factors_to_fit = self.n_factors
        if n_factors_to_fit == 'auto':
            n_factors_to_fit = determine_n_factors(
                prepared_data, 
                max_factors=self.max_factors,
                min_variance_explained=self.min_variance_explained,
                standardize=self.standardize
            )
            self.n_factors = n_factors_to_fit # Update model's n_factors
            logger.info(f"Automatically selected {self.n_factors} factors")
        
        # Fit factor model
        self.factor_analysis_object = FactorAnalysis(
            n_components=n_factors_to_fit,
            rotation=self.rotation if self.rotation != 'none' else None,
            random_state=42
        )
        
        factor_scores = self.factor_analysis_object.fit_transform(prepared_data)
        
        # Store the original data for diagnostics
        self._original_data = prepared_data
        
        # Create results dictionary
        results = {
            'n_factors': self.n_factors,
            'factor_loadings': pd.DataFrame(
                self.factor_analysis_object.components_.T,
                index=prepared_data.columns,
                columns=[f'Factor_{i+1}' for i in range(self.n_factors)]
            ),
            'factor_scores': pd.DataFrame(
                factor_scores,
                index=prepared_data.index,
                columns=[f'Factor_{i+1}' for i in range(self.n_factors)]
            ),
            'communalities': pd.Series(
                1 - self.factor_analysis_object.noise_variance_,
                index=prepared_data.columns
            ),
            'variance_explained': calculate_variance_explained(self.factor_analysis_object, prepared_data),
            'rotation': self.rotation,
            'standardized': self.standardize,
            'factor_analysis_object': self.factor_analysis_object # Store for later use
        }
        
        # Interpret factors using helper
        results['factor_interpretation'] = interpret_factors(results['factor_loadings'])
        
        # Test factor adequacy using helper
        results['adequacy_tests'] = test_factor_adequacy(
            prepared_data, results, self.min_variance_explained
        )
        
        return results
    
    def predict(self, data: pd.DataFrame) -> pd.DataFrame:
        """Transform new data using fitted factors.
        
        Args:
            data: New data to transform
            
        Returns:
            Factor scores for new data
        """
        if self.factor_analysis_object is None:
            raise RuntimeError("Model must be fitted before prediction.")
            
        logger.info("Generating factor score predictions.")
        
        numeric_data = data.select_dtypes(include=[np.number])
        clean_data = numeric_data.dropna()
        
        if self.scaler:
            scaled_data = self.scaler.transform(clean_data)
            data_for_transform = pd.DataFrame(
                scaled_data,
                index=clean_data.index,
                columns=clean_data.columns
            )
        else:
            data_for_transform = clean_data
            
        factor_scores = self.factor_analysis_object.transform(data_for_transform)
        
        return pd.DataFrame(
            factor_scores,
            index=data_for_transform.index,
            columns=[f'Factor_{i+1}' for i in range(self.n_factors)]
        )
        
    def get_diagnostics(self) -> Dict[str, Any]:
        """Get diagnostic tests for the Factor model."""
        # Diagnostics are typically part of the fit results for FactorAnalysis
        # This method would return relevant parts of the results dictionary
        if self.factor_analysis_object is None:
            return {"error": "Model not fitted."}
        
        # Use original data stored during fit
        if hasattr(self, '_original_data'):
            data = self._original_data
        else:
            # If original data not available, return limited diagnostics
            logger.warning("Original data not available for full diagnostics")
            return {
                "error": "Model was not fitted with fit() method or data was not preserved",
                "n_factors": self.n_factors,
                "rotation": self.rotation,
                "method": self.method
            }
        
        # Calculate variance explained using reconstructed or original data
        variance_explained = calculate_variance_explained(self.factor_analysis_object, data)
        
        # Perform adequacy tests
        adequacy_results = test_factor_adequacy(
            data,
            {'variance_explained': variance_explained},
            self.min_variance_explained
        )
        
        return {
            "n_factors": self.n_factors,
            "variance_explained": variance_explained,
            "adequacy_tests": adequacy_results,
            "rotation": self.rotation,
            "method": self.method
        }
    
    def get_factor_contributions(self, entity: str) -> pd.Series:
        """Get factor contributions for a specific market-commodity pair."""
        if self.factor_analysis_object is None:
            raise ValueError("Model must be fitted first")
            
        loadings = pd.DataFrame(
            self.factor_analysis_object.components_.T,
            index=self.factor_analysis_object.feature_names_in_,
            columns=[f'Factor_{i+1}' for i in range(self.n_factors)]
        )
            
        if entity not in loadings.index:
            raise ValueError(f"Entity {entity} not found in model")
            
        return loadings.loc[entity]
