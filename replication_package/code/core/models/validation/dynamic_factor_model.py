"""Dynamic factor model for capturing time-varying factors.

This model extends static factor analysis by allowing factors to evolve
over time, capturing structural changes in market integration.
"""

from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
import statsmodels.api as sm
from statsmodels.tsa.statespace.dynamic_factor import DynamicFactor

from ..interfaces import Model, ModelSpecification
from ....infrastructure.logging import Logger # Corrected import path

logger = get_logger(__name__)


class DynamicFactorModel(Model):
    """Dynamic factor model capturing time-varying factors.
    
    This model extends static factor analysis by allowing factors to evolve
    over time, capturing structural changes in market integration.
    """
    
    def __init__(self, specification: ModelSpecification):
        """Initialize dynamic factor model.
        
        Args:
            specification: Model specification with dynamic factor parameters
        """
        self.specification = specification
        self.n_factors = specification.features.get('n_factors', 3)
        self.factor_order = specification.features.get('factor_order', 1) # AR order for factors
        self.standardize = specification.features.get('standardize', True)
        
        self.scaler: Optional[StandardScaler] = None
        self.model: Optional[DynamicFactor] = None
        self.results: Optional[Any] = None # statsmodels results object
        
    @property
    def name(self) -> str:
        """Model name for identification."""
        return f"DynamicFactorModel_{self.n_factors}factors_AR{self.factor_order}"
    
    @property
    def model_type(self) -> str:
        """Get model type."""
        return "validation"
        
    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate that data meets dynamic factor analysis requirements.
        
        Args:
            data: Input data to validate
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        # Check for numeric columns
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) < 2:
            errors.append(f"Insufficient numeric variables for dynamic factor analysis: {len(numeric_cols)} < 2")
        
        # Check for missing values
        if data[numeric_cols].isnull().any().any():
            missing_pct = data[numeric_cols].isnull().sum().sum() / (len(data) * len(numeric_cols)) * 100
            errors.append(f"Missing values detected ({missing_pct:.1f}% of data). Consider imputation before DFM.")
        
        # Check sample size
        n_obs = len(data)
        n_vars = len(numeric_cols)
        if n_obs < 50: # Heuristic for time series models
            errors.append(f"Sample size ({n_obs}) may be insufficient for dynamic factor analysis (recommended: >50)")
        
        # Check for zero variance columns
        zero_var_cols = [col for col in numeric_cols if data[col].var() == 0]
        if zero_var_cols:
            errors.append(f"Zero variance columns found: {zero_var_cols}")
            
        # Check for datetime index
        if not isinstance(data.index, pd.DatetimeIndex):
            errors.append("Data must have a DatetimeIndex for dynamic factor model.")
            
        return errors
    
    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for dynamic factor model estimation.
        
        Args:
            data: DataFrame with variables for DFM
            
        Returns:
            Prepared DataFrame
        """
        logger.info("Preparing data for Dynamic Factor Model")
        
        numeric_data = data.select_dtypes(include=[np.number])
        
        # Handle missing values by interpolation and then dropping remaining NaNs
        prepared_data = numeric_data.ffill().bfill()
        prepared_data = prepared_data.interpolate(method='time')
        prepared_data = prepared_data.dropna() # Drop any remaining NaNs
        
        if self.standardize:
            self.scaler = StandardScaler()
            scaled_data = self.scaler.fit_transform(prepared_data)
            prepared_data = pd.DataFrame(
                scaled_data,
                index=prepared_data.index,
                columns=prepared_data.columns
            )
            logger.info("Data standardized.")
        
        return prepared_data
        
    def fit(self, data: pd.DataFrame) -> Any:
        """Fit the dynamic factor model.
        
        Args:
            data: Prepared DataFrame with variables for DFM
            
        Returns:
            Fitted model results object (statsmodels.tsa.statespace.dynamic_factor.DFMResults)
        """
        validation_errors = self.validate_data(data)
        if validation_errors:
            raise ValueError(f"Data validation failed: {'; '.join(validation_errors)}")
            
        prepared_data = self.prepare_data(data)
        
        logger.info(f"Fitting Dynamic Factor Model with {self.n_factors} factors and AR({self.factor_order})")
        
        # Initialize DynamicFactor model
        self.model = DynamicFactor(
            prepared_data,
            k_factors=self.n_factors,
            factor_order=self.factor_order,
            error_order=0 # For simplicity, no MA component in errors
        )
        
        # Fit the model
        self.results = self.model.fit(disp=False) # disp=False to suppress convergence output
        logger.info("Dynamic Factor Model fitted successfully.")
        
        return self.results
        
    def predict(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate predictions (factor scores) from the fitted model.
        
        Args:
            data: New data to transform into factor scores. Must have same columns as training data.
            
        Returns:
            DataFrame with predicted factor scores.
        """
        if self.results is None:
            raise RuntimeError("Model must be fitted before prediction.")
            
        logger.info("Generating factor score predictions.")
        
        # Ensure data has the same columns as the training data
        # This is crucial for consistent transformation
        original_cols = self.model.data.orig_endog.columns
        data_for_prediction = data[original_cols].copy()
        
        # Apply same preprocessing as training data
        if self.scaler:
            scaled_data = self.scaler.transform(data_for_prediction)
            data_for_prediction = pd.DataFrame(
                scaled_data,
                index=data_for_prediction.index,
                columns=data_for_prediction.columns
            )
            
        # Get filtered states (factor scores) for the new data
        # This is equivalent to `transform` for DFM
        # Note: statsmodels DFM does not have a direct `predict` method for new data
        # Instead, you would typically use `filter` or `forecast`
        # For simplicity, we'll return the smoothed states from the fitted model
        # or re-filter if new data is provided.
        
        # If new data is provided, we need to re-filter or extend the model
        # For now, we'll assume `predict` is used to get the in-sample smoothed factors
        # or for a simple out-of-sample forecast if the model supports it.
        # A more robust implementation would use `model.filter(new_data)` or `model.forecast()`.
        
        # For simplicity, return smoothed states from the fitted model
        # If actual out-of-sample prediction is needed, this method needs refinement
        smoothed_factors = self.results.smoothed_state.iloc[self.model.ssm.k_states - self.n_factors:, :].T
        smoothed_factors.columns = [f'DynamicFactor_{i+1}' for i in range(self.n_factors)]
        smoothed_factors.index = data.index # Align index with input data
        
        return smoothed_factors
        
    def get_diagnostics(self) -> Dict[str, Any]:
        """Get diagnostic tests for the dynamic factor model."""
        if self.results is None:
            return {"error": "Model not fitted."}
            
        diagnostics = {}
        
        # Residual diagnostics
        residuals = self.results.resid
        
        # Ljung-Box test for autocorrelation in residuals
        from statsmodels.stats.diagnostic import acorr_ljungbox
        for i, col in enumerate(residuals.columns):
            lb_test = acorr_ljungbox(residuals[col], lags=[10], return_df=True)
            diagnostics[f'ljung_box_{col}'] = {
                'statistic': lb_test['lb_stat'].iloc[0],
                'p_value': lb_test['lb_pvalue'].iloc[0]
            }
            
        # Normality test (Jarque-Bera)
        from statsmodels.stats.stattools import jarque_bera
        for i, col in enumerate(residuals.columns):
            jb_test = jarque_bera(residuals[col].dropna())
            diagnostics[f'jarque_bera_{col}'] = {
                'statistic': jb_test[0],
                'p_value': jb_test[1]
            }
            
        # Information criteria
        diagnostics['aic'] = self.results.aic
        diagnostics['bic'] = self.results.bic
        diagnostics['hqic'] = self.results.hqic
        
        # Factor interpretation (loadings)
        if hasattr(self.results, 'factor_loadings'):
            diagnostics['factor_loadings'] = self.results.factor_loadings.to_dict()
            
        return diagnostics
