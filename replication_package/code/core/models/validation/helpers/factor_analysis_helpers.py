"""Helper functions for factor analysis models."""

from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
from sklearn.decomposition import FactorAnalysis
from scipy import stats

from .....infrastructure.logging import Logger # Corrected import path

logger = get_logger(__name__)


def determine_n_factors(data: pd.DataFrame, max_factors: int = 10,
                        min_variance_explained: float = 0.7, standardize: bool = True) -> int:
    """Determine optimal number of factors using eigenvalue criterion and scree test.
    
    Args:
        data: Prepared data for factor analysis
        max_factors: Maximum number of factors to consider
        min_variance_explained: Minimum cumulative variance to explain
        standardize: Whether data was standardized (affects Kaiser criterion)
            
    Returns:
        Optimal number of factors
    """
    # Calculate correlation matrix eigenvalues
    corr_matrix = data.corr()
    eigenvalues, _ = np.linalg.eig(corr_matrix)
    eigenvalues = np.sort(eigenvalues)[::-1]
    
    # Kaiser criterion: eigenvalues > 1 (for standardized data)
    n_factors_kaiser = np.sum(eigenvalues > 1) if standardize else len(eigenvalues) # If not standardized, <PERSON> is not directly applicable as 1
    
    # Scree test: find elbow
    # Look for the point where the slope of the eigenvalues plot changes most dramatically
    if len(eigenvalues) > 1:
        diffs = np.diff(eigenvalues)
        # Find the largest drop in eigenvalues
        n_factors_scree = np.argmax(np.abs(diffs)) + 1
    else:
        n_factors_scree = 1
    
    # Use minimum of the two, capped by max_factors and number of variables
    n_factors = min(n_factors_kaiser, n_factors_scree, len(data.columns) - 1, max_factors)
    
    logger.info(f"Kaiser criterion suggests {n_factors_kaiser} factors, "
               f"scree test suggests {n_factors_scree} factors")
    
    # Also consider minimum variance explained
    if len(eigenvalues) > 0:
        cumulative_variance = np.cumsum(eigenvalues / np.sum(eigenvalues))
        n_factors_variance = np.argmax(cumulative_variance >= min_variance_explained) + 1
        n_factors = min(n_factors, n_factors_variance)
        logger.info(f"Variance explained criterion suggests {n_factors_variance} factors for {min_variance_explained*100}% variance")
    
    return max(1, n_factors)


def calculate_variance_explained(fa: FactorAnalysis, 
                                 data: pd.DataFrame) -> Dict[str, Any]:
    """Calculate variance explained by factors.
    
    Args:
        fa: Fitted FactorAnalysis object
        data: Original data
            
    Returns:
        Dictionary with variance explained metrics
    """
    # Total variance (sum of variances of standardized variables)
    total_variance = len(data.columns)  # For standardized data
    
    # Variance explained by each factor
    factor_variances = np.sum(fa.components_**2, axis=1)
    
    # Proportion of variance explained
    prop_variance = factor_variances / total_variance
    cumulative_variance = np.cumsum(prop_variance)
    
    return {
        'by_factor': pd.Series(
            prop_variance,
            index=[f'Factor_{i+1}' for i in range(len(prop_variance))]
        ),
        'cumulative': pd.Series(
            cumulative_variance,
            index=[f'Factor_{i+1}' for i in range(len(cumulative_variance))]
        ),
        'total': float(cumulative_variance[-1])
    }


def interpret_factors(loadings: pd.DataFrame) -> Dict[str, str]:
    """Interpret factors based on loadings.
    
    Args:
        loadings: Factor loadings matrix
            
    Returns:
        Dictionary with factor interpretations
    """
    interpretations = {}
    
    for factor in loadings.columns:
        # Find variables with high loadings
        high_loadings = loadings[factor][abs(loadings[factor]) > 0.4].sort_values(
            ascending=False, key=abs
        )
        
        if len(high_loadings) > 0:
            # Create interpretation based on top loadings
            top_vars = high_loadings.head(3).index.tolist()
            interpretation = f"Driven by {', '.join(top_vars)}"
            
            # Add direction information
            if all(high_loadings.head(3) > 0):
                interpretation += " (positive relationship)"
            elif all(high_loadings.head(3) < 0):
                interpretation += " (negative relationship)"
            else:
                interpretation += " (mixed relationship)"
            
            interpretations[factor] = interpretation
        else:
            interpretations[factor] = "No strong loadings"
    
    return interpretations


def test_factor_adequacy(data: pd.DataFrame, 
                         results: Dict[str, Any],
                         min_variance_explained: float = 0.7) -> Dict[str, Any]:
    """Test adequacy of factor model.
    
    Args:
        data: Original data
        results: Factor analysis results
        min_variance_explained: Minimum cumulative variance for adequacy
            
    Returns:
        Dictionary with test results
    """
    tests = {}
    
    # Kaiser-Meyer-Olkin (KMO) test
    tests['kmo'] = calculate_kmo(data)
    
    # Bartlett's test of sphericity
    tests['bartlett'] = bartlett_sphericity_test(data)
    
    # Check variance explained
    total_var_explained = results['variance_explained']['total']
    tests['variance_adequate'] = total_var_explained >= min_variance_explained
    
    # Overall adequacy
    tests['overall_adequate'] = (
        tests['kmo']['overall'] > 0.6 and
        tests['bartlett']['reject_null'] and
        tests['variance_adequate']
    )
    
    return tests


def calculate_kmo(data: pd.DataFrame) -> Dict[str, Any]:
    """Calculate Kaiser-Meyer-Olkin measure of sampling adequacy.
    
    Args:
        data: Data matrix
            
    Returns:
        Dictionary with KMO results
    """
    # Correlation matrix
    corr = data.corr().values
    
    # Partial correlation matrix
    try:
        inv_corr = np.linalg.inv(corr)
        partial_corr = -inv_corr / np.sqrt(np.outer(np.diag(inv_corr), np.diag(inv_corr)))
        np.fill_diagonal(partial_corr, 0)
    except np.linalg.LinAlgError:
        logger.warning("Singular correlation matrix, KMO cannot be calculated")
        return {'overall': np.nan, 'by_variable': {}}
    
    # KMO calculation
    corr_sq = corr**2
    partial_corr_sq = partial_corr**2
    
    # Overall KMO
    overall_kmo = np.sum(corr_sq) / (np.sum(corr_sq) + np.sum(partial_corr_sq))
    
    # Per-variable KMO
    var_kmo = {}
    for i, var in enumerate(data.columns):
        var_kmo[var] = np.sum(corr_sq[i, :]) / (
            np.sum(corr_sq[i, :]) + np.sum(partial_corr_sq[i, :])
        )
    
    return {
        'overall': overall_kmo,
        'by_variable': var_kmo,
        'adequate': overall_kmo > 0.6,
        'interpretation': interpret_kmo(overall_kmo)
    }


def interpret_kmo(kmo: float) -> str:
    """Interpret KMO value."""
    if kmo >= 0.9:
        return "Marvelous"
    elif kmo >= 0.8:
        return "Meritorious"
    elif kmo >= 0.7:
        return "Middling"
    elif kmo >= 0.6:
        return "Mediocre"
    elif kmo >= 0.5:
        return "Miserable"
    else:
        return "Unacceptable"


def bartlett_sphericity_test(data: pd.DataFrame) -> Dict[str, Any]:
    """Bartlett's test of sphericity.
    
    Tests whether correlation matrix is identity matrix.
    
    Args:
        data: Data matrix
            
    Returns:
        Dictionary with test results
    """
    n_obs, n_vars = data.shape
    corr_matrix = data.corr()
    
    # Calculate test statistic
    det_corr = np.linalg.det(corr_matrix)
    if det_corr <= 0:
        logger.warning("Non-positive definite correlation matrix")
        return {
            'statistic': np.nan,
            'p_value': np.nan,
            'reject_null': False
        }
    
    chi_square = -(n_obs - 1 - (2 * n_vars + 5) / 6) * np.log(det_corr)
    
    # Degrees of freedom
    df = n_vars * (n_vars - 1) / 2
    
    # P-value
    p_value = 1 - stats.chi2.cdf(chi_square, df)
    
    return {
        'statistic': float(chi_square),
        'df': float(df),
        'p_value': float(p_value),
        'reject_null': p_value < 0.05,
        'interpretation': "Variables are intercorrelated" if p_value < 0.05 
                        else "Variables may be independent"
    }
