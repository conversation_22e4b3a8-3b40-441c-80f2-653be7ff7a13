"""Cross-validation model for robustness testing.

This model performs various cross-validation techniques to assess
the robustness and stability of econometric results.
"""

from typing import List, Dict, Any, Optional, Tuple, Callable
import pandas as pd
import numpy as np
from sklearn.model_selection import TimeSeriesSplit, KFold
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings

from core.models.interfaces import Model, ModelSpecification
from infrastructure.logging import Logger

logger = get_logger(__name__)


class CrossValidationModel(Model):
    """Cross-validation model for testing robustness of econometric results.
    
    This model implements various cross-validation strategies including:
    - Time series cross-validation
    - K-fold cross-validation for panel data
    - Leave-one-out validation
    - Rolling window validation
    """
    
    def __init__(self, specification: ModelSpecification):
        """Initialize cross-validation model.
        
        Args:
            specification: Model specification with CV parameters
        """
        self.specification = specification
        self.cv_method = specification.features.get('cv_method', 'time_series')
        self.n_splits = specification.features.get('n_splits', 5)
        self.test_size = specification.features.get('test_size', 0.2)
        self.gap = specification.features.get('gap', 0)  # Gap between train and test
        self.parallel = specification.features.get('parallel', True)
        self.max_workers = specification.features.get('max_workers', 4)
        
    @property
    def name(self) -> str:
        """Model name for identification."""
        return f"CrossValidation_{self.cv_method}_{self.n_splits}splits"
    
    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate that data meets cross-validation requirements.
        
        Args:
            data: Input data to validate
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        # Check minimum size
        min_size = self.n_splits * 20  # At least 20 obs per fold
        if len(data) < min_size:
            errors.append(f"Insufficient data for {self.n_splits}-fold CV: "
                         f"{len(data)} < {min_size}")
        
        # Check for time index if using time series CV
        if self.cv_method == 'time_series':
            if 'date' not in data.columns and not isinstance(data.index, pd.DatetimeIndex):
                errors.append("Time series CV requires a date column or datetime index")
        
        # Check for panel structure if using panel CV
        if self.cv_method == 'panel':
            if not isinstance(data.index, pd.MultiIndex):
                errors.append("Panel CV requires MultiIndex (entity, time)")
        
        return errors
    
    def cross_validate(self, data: pd.DataFrame,
                      model_func: Callable,
                      metric_func: Optional[Callable] = None) -> Dict[str, Any]:
        """Perform cross-validation on the model.
        
        Args:
            data: Input data
            model_func: Function that takes train data and returns fitted model
            metric_func: Function that takes (y_true, y_pred) and returns metric
            
        Returns:
            Dictionary with cross-validation results
        """
        logger.info(f"Starting {self.cv_method} cross-validation with {self.n_splits} splits")
        
        # Get appropriate splitter
        splitter = self._get_splitter(data)
        
        # Initialize results storage
        results = {
            'method': self.cv_method,
            'n_splits': self.n_splits,
            'fold_results': [],
            'metrics': {},
            'parameter_stability': {},
            'predictions': {}
        }
        
        # Perform cross-validation
        if self.parallel and self.n_splits > 2:
            results['fold_results'] = self._parallel_cv(
                data, model_func, metric_func, splitter
            )
        else:
            results['fold_results'] = self._sequential_cv(
                data, model_func, metric_func, splitter
            )
        
        # Aggregate results
        results['summary'] = self._aggregate_results(results['fold_results'])
        
        # Test parameter stability
        results['parameter_stability'] = self._test_parameter_stability(
            results['fold_results']
        )
        
        # Calculate out-of-sample predictions
        results['oos_performance'] = self._calculate_oos_performance(
            results['fold_results']
        )
        
        return results
    
    def _get_splitter(self, data: pd.DataFrame):
        """Get appropriate cross-validation splitter.
        
        Args:
            data: Input data
            
        Returns:
            Cross-validation splitter object
        """
        if self.cv_method == 'time_series':
            return TimeSeriesSplit(
                n_splits=self.n_splits,
                test_size=int(len(data) * self.test_size) if self.test_size < 1 else int(self.test_size),
                gap=self.gap
            )
        
        elif self.cv_method == 'k_fold':
            return KFold(
                n_splits=self.n_splits,
                shuffle=True,
                random_state=42
            )
        
        elif self.cv_method == 'panel':
            # Custom panel splitter
            return self._create_panel_splitter(data)
        
        elif self.cv_method == 'rolling_window':
            return self._create_rolling_window_splitter(data)
        
        else:
            raise ValueError(f"Unknown CV method: {self.cv_method}")
    
    def _create_panel_splitter(self, data: pd.DataFrame):
        """Create custom panel data splitter.
        
        Args:
            data: Panel data with MultiIndex
            
        Yields:
            Train and test indices
        """
        # Get unique entities
        entities = data.index.get_level_values(0).unique()
        n_entities = len(entities)
        
        # K-fold split on entities
        entity_kfold = KFold(n_splits=self.n_splits, shuffle=True, random_state=42)
        
        for train_entities, test_entities in entity_kfold.split(entities):
            # Get indices for train and test entities
            train_entity_list = entities[train_entities]
            test_entity_list = entities[test_entities]
            
            train_idx = data.index.get_level_values(0).isin(train_entity_list)
            test_idx = data.index.get_level_values(0).isin(test_entity_list)
            
            yield np.where(train_idx)[0], np.where(test_idx)[0]
    
    def _create_rolling_window_splitter(self, data: pd.DataFrame):
        """Create rolling window splitter.
        
        Args:
            data: Time series data
            
        Yields:
            Train and test indices
        """
        window_size = int(len(data) * (1 - self.test_size))
        test_size = int(len(data) * self.test_size)
        
        for i in range(self.n_splits):
            start_idx = i * (len(data) - window_size - test_size) // (self.n_splits - 1)
            train_end = start_idx + window_size
            test_end = min(train_end + test_size, len(data))
            
            train_idx = np.arange(start_idx, train_end)
            test_idx = np.arange(train_end, test_end)
            
            if len(test_idx) > 0:
                yield train_idx, test_idx
    
    def _sequential_cv(self, data: pd.DataFrame,
                      model_func: Callable,
                      metric_func: Optional[Callable],
                      splitter) -> List[Dict[str, Any]]:
        """Perform sequential cross-validation.
        
        Args:
            data: Input data
            model_func: Model fitting function
            metric_func: Metric calculation function
            splitter: CV splitter
            
        Returns:
            List of fold results
        """
        fold_results = []
        
        for fold_idx, (train_idx, test_idx) in enumerate(splitter.split(data)):
            logger.info(f"Processing fold {fold_idx + 1}/{self.n_splits}")
            
            # Split data
            if isinstance(data.index, pd.MultiIndex):
                train_data = data.iloc[train_idx]
                test_data = data.iloc[test_idx]
            else:
                train_data = data.iloc[train_idx]
                test_data = data.iloc[test_idx]
            
            # Fit model
            try:
                fold_result = self._fit_fold(
                    fold_idx, train_data, test_data, model_func, metric_func
                )
                fold_results.append(fold_result)
            except Exception as e:
                logger.error(f"Error in fold {fold_idx}: {e}")
                fold_results.append({
                    'fold': fold_idx,
                    'error': str(e),
                    'success': False
                })
        
        return fold_results
    
    def _parallel_cv(self, data: pd.DataFrame,
                    model_func: Callable,
                    metric_func: Optional[Callable],
                    splitter) -> List[Dict[str, Any]]:
        """Perform parallel cross-validation.
        
        Args:
            data: Input data
            model_func: Model fitting function
            metric_func: Metric calculation function
            splitter: CV splitter
            
        Returns:
            List of fold results
        """
        fold_results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit jobs
            futures = {}
            for fold_idx, (train_idx, test_idx) in enumerate(splitter.split(data)):
                if isinstance(data.index, pd.MultiIndex):
                    train_data = data.iloc[train_idx]
                    test_data = data.iloc[test_idx]
                else:
                    train_data = data.iloc[train_idx]
                    test_data = data.iloc[test_idx]
                
                future = executor.submit(
                    self._fit_fold,
                    fold_idx, train_data, test_data, model_func, metric_func
                )
                futures[future] = fold_idx
            
            # Collect results
            for future in as_completed(futures):
                fold_idx = futures[future]
                try:
                    result = future.result()
                    fold_results.append(result)
                except Exception as e:
                    logger.error(f"Error in fold {fold_idx}: {e}")
                    fold_results.append({
                        'fold': fold_idx,
                        'error': str(e),
                        'success': False
                    })
        
        # Sort by fold index
        fold_results.sort(key=lambda x: x['fold'])
        
        return fold_results
    
    def _fit_fold(self, fold_idx: int,
                 train_data: pd.DataFrame,
                 test_data: pd.DataFrame,
                 model_func: Callable,
                 metric_func: Optional[Callable]) -> Dict[str, Any]:
        """Fit model on single fold.
        
        Args:
            fold_idx: Fold index
            train_data: Training data
            test_data: Test data
            model_func: Model fitting function
            metric_func: Metric calculation function
            
        Returns:
            Dictionary with fold results
        """
        # Suppress warnings for individual folds
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            
            # Fit model on training data
            model_result = model_func(train_data)
            
            # Make predictions on test data
            if hasattr(model_result, 'predict'):
                predictions = model_result.predict(test_data)
            else:
                # Fallback for models without predict method
                predictions = None
            
            # Calculate metrics
            metrics = {}
            if metric_func and predictions is not None:
                if hasattr(model_result, 'dependent_var'):
                    y_true = test_data[model_result.dependent_var]
                    metrics['custom_metric'] = metric_func(y_true, predictions)
                
                # Standard metrics
                if predictions is not None:
                    metrics['rmse'] = np.sqrt(np.mean((y_true - predictions)**2))
                    metrics['mae'] = np.mean(np.abs(y_true - predictions))
                    metrics['mape'] = np.mean(np.abs((y_true - predictions) / y_true)) * 100
            
            # Extract parameters
            params = {}
            if hasattr(model_result, 'params'):
                params = model_result.params.to_dict() if hasattr(model_result.params, 'to_dict') else dict(model_result.params)
            
            return {
                'fold': fold_idx,
                'success': True,
                'train_size': len(train_data),
                'test_size': len(test_data),
                'parameters': params,
                'metrics': metrics,
                'predictions': predictions,
                'model_result': model_result
            }
    
    def _aggregate_results(self, fold_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Aggregate results across folds.
        
        Args:
            fold_results: List of fold results
            
        Returns:
            Dictionary with aggregated statistics
        """
        successful_folds = [f for f in fold_results if f.get('success', False)]
        
        if not successful_folds:
            return {'error': 'No successful folds'}
        
        # Aggregate parameters
        param_names = set()
        for fold in successful_folds:
            param_names.update(fold.get('parameters', {}).keys())
        
        param_stats = {}
        for param in param_names:
            values = [fold['parameters'].get(param) for fold in successful_folds 
                     if param in fold.get('parameters', {})]
            if values:
                param_stats[param] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'cv': np.std(values) / np.mean(values) if np.mean(values) != 0 else np.inf
                }
        
        # Aggregate metrics
        metric_names = set()
        for fold in successful_folds:
            metric_names.update(fold.get('metrics', {}).keys())
        
        metric_stats = {}
        for metric in metric_names:
            values = [fold['metrics'].get(metric) for fold in successful_folds
                     if metric in fold.get('metrics', {})]
            if values:
                metric_stats[metric] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values)
                }
        
        return {
            'n_successful': len(successful_folds),
            'n_failed': len(fold_results) - len(successful_folds),
            'parameter_stats': param_stats,
            'metric_stats': metric_stats
        }
    
    def _test_parameter_stability(self, fold_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Test stability of parameters across folds.
        
        Args:
            fold_results: List of fold results
            
        Returns:
            Dictionary with stability test results
        """
        successful_folds = [f for f in fold_results if f.get('success', False)]
        
        if len(successful_folds) < 2:
            return {'error': 'Need at least 2 successful folds for stability testing'}
        
        # Get parameter names
        param_names = set()
        for fold in successful_folds:
            param_names.update(fold.get('parameters', {}).keys())
        
        stability_tests = {}
        
        for param in param_names:
            values = [fold['parameters'].get(param) for fold in successful_folds
                     if param in fold.get('parameters', {})]
            
            if len(values) >= 2:
                # Coefficient of variation
                cv = np.std(values) / np.mean(values) if np.mean(values) != 0 else np.inf
                
                # Test if significantly different from zero
                from scipy import stats
                t_stat, p_value = stats.ttest_1samp(values, 0)
                
                # Test for trend across folds
                fold_indices = [f['fold'] for f in successful_folds 
                              if param in f.get('parameters', {})]
                if len(fold_indices) >= 3:
                    slope, intercept, r_value, p_trend, std_err = stats.linregress(
                        fold_indices, values
                    )
                else:
                    p_trend = np.nan
                
                stability_tests[param] = {
                    'coefficient_of_variation': cv,
                    'is_stable': cv < 0.5,  # Arbitrary threshold
                    'significantly_different_from_zero': p_value < 0.05,
                    'has_trend': p_trend < 0.05 if not np.isnan(p_trend) else False,
                    'mean': np.mean(values),
                    'std': np.std(values)
                }
        
        # Overall stability assessment
        stable_params = sum(1 for p in stability_tests.values() if p['is_stable'])
        total_params = len(stability_tests)
        
        stability_tests['overall'] = {
            'stable_parameters': stable_params,
            'total_parameters': total_params,
            'stability_ratio': stable_params / total_params if total_params > 0 else 0,
            'model_is_stable': stable_params / total_params > 0.7 if total_params > 0 else False
        }
        
        return stability_tests
    
    def _calculate_oos_performance(self, fold_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate out-of-sample performance metrics.
        
        Args:
            fold_results: List of fold results
            
        Returns:
            Dictionary with OOS performance metrics
        """
        successful_folds = [f for f in fold_results if f.get('success', False) 
                          and 'metrics' in f and f['metrics']]
        
        if not successful_folds:
            return {'error': 'No folds with valid metrics'}
        
        # Aggregate all metrics
        all_metrics = {}
        for metric in successful_folds[0]['metrics'].keys():
            values = [f['metrics'][metric] for f in successful_folds 
                     if metric in f['metrics']]
            
            if values:
                all_metrics[metric] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'best': np.min(values) if 'error' in metric.lower() else np.max(values),
                    'worst': np.max(values) if 'error' in metric.lower() else np.min(values)
                }
        
        # Calculate improvement over baseline
        if 'rmse' in all_metrics:
            # Baseline: predict mean
            baseline_rmse = np.inf  # Would need actual calculation
            improvement = (baseline_rmse - all_metrics['rmse']['mean']) / baseline_rmse * 100
            all_metrics['improvement_over_baseline'] = improvement
        
        return all_metrics