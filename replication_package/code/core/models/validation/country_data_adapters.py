"""
Data adapters for cross-country validation framework.

Provides standardized data transformation protocols to adapt Yemen's spatial
econometric models for validation across Syria, Lebanon, Somalia, and Afghanistan.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
import warnings

from ...domain.shared.value_objects import Country
from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class CountryDataSchema:
    """Schema definition for country-specific data structure."""
    price_columns: Dict[str, str]  # Column name mappings
    exchange_rate_columns: Dict[str, str]
    conflict_columns: Dict[str, str]
    geography_columns: Dict[str, str]
    date_format: str
    currency_primary: str
    currency_secondary: Optional[str] = None
    territorial_control_variable: Optional[str] = None
    required_transformations: List[str] = None


@dataclass
class ValidationDataPackage:
    """Standardized data package for cross-country validation."""
    country: Country
    price_data: pd.DataFrame
    exchange_rates: pd.DataFrame
    conflict_data: pd.DataFrame
    geography_data: pd.DataFrame
    currency_zones: Optional[pd.DataFrame] = None
    metadata: Dict[str, Any] = None
    data_quality_score: float = 0.0
    coverage_period: Tuple[datetime, datetime] = None


class CountryDataAdapterBase(ABC):
    """Base class for country-specific data adapters."""
    
    def __init__(self, country: Country):
        """Initialize adapter for specific country."""
        self.country = country
        self.schema = self._get_country_schema()
        self.transformations = self._get_transformation_pipeline()
    
    @abstractmethod
    def _get_country_schema(self) -> CountryDataSchema:
        """Get country-specific data schema."""
        pass
    
    @abstractmethod
    def _get_transformation_pipeline(self) -> List[str]:
        """Get ordered list of transformations to apply."""
        pass
    
    @abstractmethod
    def load_raw_data(self, data_sources: Dict[str, str]) -> Dict[str, pd.DataFrame]:
        """Load raw data from country-specific sources."""
        pass
    
    def standardize_data(self, raw_data: Dict[str, pd.DataFrame]) -> ValidationDataPackage:
        """Transform raw data into standardized format for validation."""
        logger.info(f"Standardizing data for {self.country.value}")
        
        # Apply transformation pipeline
        transformed_data = raw_data.copy()
        for transformation in self.transformations:
            transformed_data = self._apply_transformation(transformation, transformed_data)
        
        # Create standardized data package
        package = ValidationDataPackage(
            country=self.country,
            price_data=self._standardize_price_data(transformed_data.get('prices', pd.DataFrame())),
            exchange_rates=self._standardize_exchange_data(transformed_data.get('exchange_rates', pd.DataFrame())),
            conflict_data=self._standardize_conflict_data(transformed_data.get('conflict', pd.DataFrame())),
            geography_data=self._standardize_geography_data(transformed_data.get('geography', pd.DataFrame())),
            currency_zones=self._standardize_currency_zones(transformed_data.get('currency_zones')),
            metadata=self._extract_metadata(transformed_data),
            data_quality_score=self._calculate_data_quality(transformed_data),
            coverage_period=self._get_coverage_period(transformed_data)
        )
        
        logger.info(f"Data standardization complete for {self.country.value}. Quality score: {package.data_quality_score:.2f}")
        return package
    
    def _apply_transformation(self, transformation: str, data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Apply specific transformation to data."""
        transformation_method = getattr(self, f'_transform_{transformation}', None)
        if transformation_method:
            return transformation_method(data)
        else:
            logger.warning(f"Transformation {transformation} not implemented for {self.country.value}")
            return data
    
    def _standardize_price_data(self, price_df: pd.DataFrame) -> pd.DataFrame:
        """Standardize price data to common format."""
        if price_df.empty:
            return pd.DataFrame(columns=['date', 'market_id', 'commodity', 'price', 'currency', 'currency_zone'])
        
        # Map columns according to schema
        column_mapping = self.schema.price_columns
        standardized = pd.DataFrame()
        
        for std_col, source_col in column_mapping.items():
            if source_col in price_df.columns:
                standardized[std_col] = price_df[source_col]
        
        # Ensure required columns exist
        required_cols = ['date', 'market_id', 'commodity', 'price', 'currency']
        for col in required_cols:
            if col not in standardized.columns:
                logger.warning(f"Missing required price column: {col}")
                standardized[col] = np.nan
        
        # Add currency zone if territorial control data available
        if self.schema.territorial_control_variable and 'currency_zone' not in standardized.columns:
            standardized['currency_zone'] = self._infer_currency_zones(standardized)
        
        # Convert data types
        standardized['date'] = pd.to_datetime(standardized['date'], format=self.schema.date_format, errors='coerce')
        standardized['price'] = pd.to_numeric(standardized['price'], errors='coerce')
        
        return standardized.dropna(subset=['date', 'price'])
    
    def _standardize_exchange_data(self, exchange_df: pd.DataFrame) -> pd.DataFrame:
        """Standardize exchange rate data to common format."""
        if exchange_df.empty:
            return pd.DataFrame(columns=['date', 'currency_pair', 'rate', 'source', 'zone'])
        
        column_mapping = self.schema.exchange_rate_columns
        standardized = pd.DataFrame()
        
        for std_col, source_col in column_mapping.items():
            if source_col in exchange_df.columns:
                standardized[std_col] = exchange_df[source_col]
        
        # Convert data types
        standardized['date'] = pd.to_datetime(standardized['date'], format=self.schema.date_format, errors='coerce')
        standardized['rate'] = pd.to_numeric(standardized['rate'], errors='coerce')
        
        return standardized.dropna(subset=['date', 'rate'])
    
    def _standardize_conflict_data(self, conflict_df: pd.DataFrame) -> pd.DataFrame:
        """Standardize conflict data to common format."""
        if conflict_df.empty:
            return pd.DataFrame(columns=['date', 'location', 'event_type', 'fatalities', 'intensity'])
        
        column_mapping = self.schema.conflict_columns
        standardized = pd.DataFrame()
        
        for std_col, source_col in column_mapping.items():
            if source_col in conflict_df.columns:
                standardized[std_col] = conflict_df[source_col]
        
        # Convert data types
        standardized['date'] = pd.to_datetime(standardized['date'], format=self.schema.date_format, errors='coerce')
        standardized['fatalities'] = pd.to_numeric(standardized['fatalities'], errors='coerce').fillna(0)
        
        # Calculate intensity score
        if 'intensity' not in standardized.columns:
            standardized['intensity'] = self._calculate_conflict_intensity(standardized)
        
        return standardized.dropna(subset=['date'])
    
    def _standardize_geography_data(self, geo_df: pd.DataFrame) -> pd.DataFrame:
        """Standardize geographic data to common format."""
        if geo_df.empty:
            return pd.DataFrame(columns=['location_id', 'latitude', 'longitude', 'admin_level', 'control_zone'])
        
        column_mapping = self.schema.geography_columns
        standardized = pd.DataFrame()
        
        for std_col, source_col in column_mapping.items():
            if source_col in geo_df.columns:
                standardized[std_col] = geo_df[source_col]
        
        # Convert coordinates to numeric
        standardized['latitude'] = pd.to_numeric(standardized['latitude'], errors='coerce')
        standardized['longitude'] = pd.to_numeric(standardized['longitude'], errors='coerce')
        
        return standardized.dropna(subset=['latitude', 'longitude'])
    
    def _standardize_currency_zones(self, zones_df: Optional[pd.DataFrame]) -> Optional[pd.DataFrame]:
        """Standardize currency zone data if available."""
        if zones_df is None or zones_df.empty:
            return None
        
        # Implement country-specific currency zone standardization
        return zones_df
    
    def _infer_currency_zones(self, price_data: pd.DataFrame) -> pd.Series:
        """Infer currency zones from price and location data."""
        # Default implementation - override in country-specific adapters
        return pd.Series(['unknown'] * len(price_data), index=price_data.index)
    
    def _calculate_conflict_intensity(self, conflict_data: pd.DataFrame) -> pd.Series:
        """Calculate conflict intensity score."""
        # Simple intensity based on fatalities and event frequency
        intensity = np.log1p(conflict_data['fatalities'].fillna(0))
        return (intensity - intensity.min()) / (intensity.max() - intensity.min()) if intensity.max() > intensity.min() else intensity * 0
    
    def _extract_metadata(self, data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Extract metadata about the dataset."""
        metadata = {
            'country': self.country.value,
            'data_sources': list(data.keys()),
            'extraction_date': datetime.now().isoformat(),
            'primary_currency': self.schema.currency_primary,
            'secondary_currency': self.schema.currency_secondary,
        }
        
        # Add data coverage statistics
        for source, df in data.items():
            if not df.empty and 'date' in df.columns:
                metadata[f'{source}_start_date'] = df['date'].min().isoformat() if pd.notnull(df['date'].min()) else None
                metadata[f'{source}_end_date'] = df['date'].max().isoformat() if pd.notnull(df['date'].max()) else None
                metadata[f'{source}_record_count'] = len(df)
        
        return metadata
    
    def _calculate_data_quality(self, data: Dict[str, pd.DataFrame]) -> float:
        """Calculate overall data quality score."""
        scores = []
        
        for source, df in data.items():
            if df.empty:
                scores.append(0.0)
                continue
            
            # Completeness score
            completeness = 1 - df.isnull().sum().sum() / (df.shape[0] * df.shape[1])
            
            # Consistency score (simplified)
            consistency = 0.8  # Default reasonable score
            
            # Timeliness score
            if 'date' in df.columns:
                latest_date = df['date'].max()
                if pd.notnull(latest_date):
                    days_old = (datetime.now() - latest_date).days
                    timeliness = max(0, 1 - days_old / 365)  # Degrade over 1 year
                else:
                    timeliness = 0
            else:
                timeliness = 0.5
            
            source_score = np.mean([completeness, consistency, timeliness])
            scores.append(source_score)
        
        return np.mean(scores) if scores else 0.0
    
    def _get_coverage_period(self, data: Dict[str, pd.DataFrame]) -> Tuple[datetime, datetime]:
        """Get overall data coverage period."""
        all_dates = []
        
        for df in data.values():
            if not df.empty and 'date' in df.columns:
                valid_dates = df['date'].dropna()
                if not valid_dates.empty:
                    all_dates.extend(valid_dates.tolist())
        
        if all_dates:
            return min(all_dates), max(all_dates)
        else:
            return None, None


class SyriaDataAdapter(CountryDataAdapterBase):
    """Data adapter for Syria validation."""
    
    def __init__(self):
        super().__init__(Country.SYRIA)
    
    def _get_country_schema(self) -> CountryDataSchema:
        return CountryDataSchema(
            price_columns={
                'date': 'date',
                'market_id': 'market_name',
                'commodity': 'commodity',
                'price': 'price_syp',
                'currency': 'currency',
                'currency_zone': 'control_zone'
            },
            exchange_rate_columns={
                'date': 'date',
                'currency_pair': 'pair',
                'rate': 'rate',
                'source': 'source',
                'zone': 'control_area'
            },
            conflict_columns={
                'date': 'event_date',
                'location': 'location',
                'event_type': 'event_type',
                'fatalities': 'fatalities',
                'intensity': 'conflict_intensity'
            },
            geography_columns={
                'location_id': 'admin_name',
                'latitude': 'latitude',
                'longitude': 'longitude',
                'admin_level': 'admin_level',
                'control_zone': 'control_2023'
            },
            date_format='%Y-%m-%d',
            currency_primary='SYP',
            currency_secondary='USD',
            territorial_control_variable='control_zone',
            required_transformations=['currency_zone_mapping', 'conflict_aggregation']
        )
    
    def _get_transformation_pipeline(self) -> List[str]:
        return ['currency_zone_mapping', 'conflict_aggregation', 'usd_conversion']
    
    def load_raw_data(self, data_sources: Dict[str, str]) -> Dict[str, pd.DataFrame]:
        """Load Syria-specific data sources."""
        raw_data = {}
        
        # Load from various Syria data sources
        # This would integrate with WFP, ACLED, and Syria-specific databases
        try:
            if 'wfp_prices' in data_sources:
                raw_data['prices'] = pd.read_csv(data_sources['wfp_prices'])
            
            if 'syrian_pound_rates' in data_sources:
                raw_data['exchange_rates'] = pd.read_csv(data_sources['syrian_pound_rates'])
            
            if 'acled_syria' in data_sources:
                raw_data['conflict'] = pd.read_csv(data_sources['acled_syria'])
            
            if 'syria_admin' in data_sources:
                raw_data['geography'] = pd.read_csv(data_sources['syria_admin'])
            
            if 'control_zones' in data_sources:
                raw_data['currency_zones'] = pd.read_csv(data_sources['control_zones'])
        
        except Exception as e:
            logger.error(f"Failed to load Syria data: {e}")
            raw_data = {}
        
        return raw_data
    
    def _transform_currency_zone_mapping(self, data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Map Syrian territorial control to currency zones."""
        if 'prices' not in data or data['prices'].empty:
            return data
        
        # Map Syrian control zones to currency regimes
        # Government = Syrian Pound (official rate)
        # Opposition/Kurdish = USD or Turkish Lira
        # ISIS (historical) = USD
        
        control_mapping = {
            'government': 'syp_official',
            'opposition': 'usd_parallel',
            'sdf': 'usd_parallel',
            'kurdish': 'usd_parallel',
            'turkish': 'try_zone',
            'isis': 'usd_black',  # Historical
            'unknown': 'mixed'
        }
        
        if 'control_zone' in data['prices'].columns:
            data['prices']['currency_zone'] = data['prices']['control_zone'].map(control_mapping).fillna('mixed')
        
        return data
    
    def _transform_conflict_aggregation(self, data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Aggregate conflict data to monthly market-level observations."""
        if 'conflict' not in data or data['conflict'].empty:
            return data
        
        # Group by location and month, sum fatalities
        data['conflict']['date'] = pd.to_datetime(data['conflict']['event_date'])
        data['conflict']['year_month'] = data['conflict']['date'].dt.to_period('M')
        
        monthly_conflict = data['conflict'].groupby(['location', 'year_month']).agg({
            'fatalities': 'sum',
            'event_type': 'count'
        }).rename(columns={'event_type': 'event_count'}).reset_index()
        
        monthly_conflict['date'] = monthly_conflict['year_month'].dt.to_timestamp()
        data['conflict'] = monthly_conflict
        
        return data
    
    def _transform_usd_conversion(self, data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Convert Syrian pound prices to USD using appropriate rates."""
        if 'prices' not in data or 'exchange_rates' not in data:
            return data
        
        # Merge exchange rates with price data based on currency zone
        prices = data['prices'].copy()
        
        # Different exchange rates for different zones
        zone_rate_mapping = {
            'syp_official': 'official_rate',
            'usd_parallel': 1.0,  # Already in USD
            'try_zone': 'try_usd_rate',
            'usd_black': 'black_market_rate',
            'mixed': 'parallel_rate'
        }
        
        for zone, rate_type in zone_rate_mapping.items():
            if rate_type == 1.0:  # Already USD
                mask = prices['currency_zone'] == zone
                prices.loc[mask, 'price_usd'] = prices.loc[mask, 'price']
            else:
                # Apply appropriate exchange rate
                # This would require more sophisticated rate matching
                mask = prices['currency_zone'] == zone
                prices.loc[mask, 'price_usd'] = prices.loc[mask, 'price'] / 2500  # Simplified
        
        data['prices'] = prices
        return data


class LebanonDataAdapter(CountryDataAdapterBase):
    """Data adapter for Lebanon validation."""
    
    def __init__(self):
        super().__init__(Country.LEBANON)
    
    def _get_country_schema(self) -> CountryDataSchema:
        return CountryDataSchema(
            price_columns={
                'date': 'date',
                'market_id': 'market',
                'commodity': 'commodity',
                'price': 'price_lbp',
                'currency': 'currency',
                'currency_zone': 'banking_zone'
            },
            exchange_rate_columns={
                'date': 'date',
                'currency_pair': 'pair',
                'rate': 'lbp_usd_rate',
                'source': 'rate_source',
                'zone': 'market_type'
            },
            conflict_columns={
                'date': 'date',
                'location': 'location',
                'event_type': 'type',
                'fatalities': 'fatalities',
                'intensity': 'intensity'
            },
            geography_columns={
                'location_id': 'admin_name',
                'latitude': 'lat',
                'longitude': 'lon',
                'admin_level': 'level',
                'control_zone': 'banking_access'
            },
            date_format='%Y-%m-%d',
            currency_primary='LBP',
            currency_secondary='USD',
            territorial_control_variable='banking_zone',
            required_transformations=['banking_zone_mapping', 'exchange_rate_regime']
        )
    
    def _get_transformation_pipeline(self) -> List[str]:
        return ['banking_zone_mapping', 'exchange_rate_regime', 'lira_usd_conversion']
    
    def load_raw_data(self, data_sources: Dict[str, str]) -> Dict[str, pd.DataFrame]:
        """Load Lebanon-specific data sources."""
        raw_data = {}
        
        try:
            if 'lebanon_prices' in data_sources:
                raw_data['prices'] = pd.read_csv(data_sources['lebanon_prices'])
            
            if 'lbp_rates' in data_sources:
                raw_data['exchange_rates'] = pd.read_csv(data_sources['lbp_rates'])
            
            if 'lebanon_conflict' in data_sources:
                raw_data['conflict'] = pd.read_csv(data_sources['lebanon_conflict'])
            
            if 'lebanon_admin' in data_sources:
                raw_data['geography'] = pd.read_csv(data_sources['lebanon_admin'])
        
        except Exception as e:
            logger.error(f"Failed to load Lebanon data: {e}")
            raw_data = {}
        
        return raw_data
    
    def _transform_banking_zone_mapping(self, data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Map Lebanese banking access to currency zones."""
        if 'prices' not in data:
            return data
        
        # Lebanon's multiple exchange rate system:
        # 1. Official rate (1,507.5 LBP/USD) - for specific transactions
        # 2. Sayrafa rate (varying) - for money changers
        # 3. Parallel market rate (varying) - black market
        
        banking_mapping = {
            'formal_banking': 'official_rate',
            'money_changers': 'sayrafa_rate',
            'informal': 'parallel_rate',
            'no_access': 'parallel_rate'
        }
        
        if 'banking_zone' in data['prices'].columns:
            data['prices']['currency_zone'] = data['prices']['banking_zone'].map(banking_mapping).fillna('parallel_rate')
        
        return data


class SomaliaDataAdapter(CountryDataAdapterBase):
    """Data adapter for Somalia validation."""
    
    def __init__(self):
        super().__init__(Country.SOMALIA)
    
    def _get_country_schema(self) -> CountryDataSchema:
        return CountryDataSchema(
            price_columns={
                'date': 'date',
                'market_id': 'market_name',
                'commodity': 'commodity',
                'price': 'price_sos',
                'currency': 'currency',
                'currency_zone': 'region_control'
            },
            exchange_rate_columns={
                'date': 'date',
                'currency_pair': 'pair',
                'rate': 'sos_usd_rate',
                'source': 'source',
                'zone': 'region'
            },
            conflict_columns={
                'date': 'event_date',
                'location': 'location',
                'event_type': 'event_type',
                'fatalities': 'fatalities',
                'intensity': 'conflict_level'
            },
            geography_columns={
                'location_id': 'admin_name',
                'latitude': 'latitude',
                'longitude': 'longitude',
                'admin_level': 'admin_level',
                'control_zone': 'territorial_control'
            },
            date_format='%Y-%m-%d',
            currency_primary='SOS',
            currency_secondary='USD',
            territorial_control_variable='region_control',
            required_transformations=['regional_currency_mapping', 'sos_usd_conversion']
        )
    
    def _get_transformation_pipeline(self) -> List[str]:
        return ['regional_currency_mapping', 'sos_usd_conversion']
    
    def load_raw_data(self, data_sources: Dict[str, str]) -> Dict[str, pd.DataFrame]:
        """Load Somalia-specific data sources."""
        raw_data = {}
        
        try:
            if 'somalia_prices' in data_sources:
                raw_data['prices'] = pd.read_csv(data_sources['somalia_prices'])
            
            if 'sos_rates' in data_sources:
                raw_data['exchange_rates'] = pd.read_csv(data_sources['sos_rates'])
            
            if 'somalia_conflict' in data_sources:
                raw_data['conflict'] = pd.read_csv(data_sources['somalia_conflict'])
            
            if 'somalia_admin' in data_sources:
                raw_data['geography'] = pd.read_csv(data_sources['somalia_admin'])
        
        except Exception as e:
            logger.error(f"Failed to load Somalia data: {e}")
            raw_data = {}
        
        return raw_data
    
    def _transform_regional_currency_mapping(self, data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Map Somali regional control to currency zones."""
        if 'prices' not in data:
            return data
        
        # Somalia's regional currency system:
        # Somaliland = Somaliland Shilling (SLS)
        # Puntland = Somali Shilling (SOS)
        # South/Central = Mixed (SOS/USD)
        
        regional_mapping = {
            'somaliland': 'sls_zone',
            'puntland': 'sos_zone',
            'south_central': 'mixed_zone',
            'federal': 'sos_zone',
            'al_shabaab': 'usd_zone'
        }
        
        if 'region_control' in data['prices'].columns:
            data['prices']['currency_zone'] = data['prices']['region_control'].map(regional_mapping).fillna('mixed_zone')
        
        return data


class AfghanistanDataAdapter(CountryDataAdapterBase):
    """Data adapter for Afghanistan validation (post-2021 focus)."""
    
    def __init__(self):
        super().__init__(Country.AFGHANISTAN)
    
    def _get_country_schema(self) -> CountryDataSchema:
        return CountryDataSchema(
            price_columns={
                'date': 'date',
                'market_id': 'market_name',
                'commodity': 'commodity',
                'price': 'price_afn',
                'currency': 'currency',
                'currency_zone': 'control_zone'
            },
            exchange_rate_columns={
                'date': 'date',
                'currency_pair': 'pair',
                'rate': 'afn_usd_rate',
                'source': 'source',
                'zone': 'market_type'
            },
            conflict_columns={
                'date': 'event_date',
                'location': 'location',
                'event_type': 'event_type',
                'fatalities': 'fatalities',
                'intensity': 'intensity'
            },
            geography_columns={
                'location_id': 'admin_name',
                'latitude': 'latitude',
                'longitude': 'longitude',
                'admin_level': 'admin_level',
                'control_zone': 'taliban_control'
            },
            date_format='%Y-%m-%d',
            currency_primary='AFN',
            currency_secondary='USD',
            territorial_control_variable='control_zone',
            required_transformations=['taliban_control_mapping', 'post_2021_focus', 'banking_collapse_adjustment']
        )
    
    def _get_transformation_pipeline(self) -> List[str]:
        return ['taliban_control_mapping', 'post_2021_focus', 'banking_collapse_adjustment', 'afn_usd_conversion']
    
    def load_raw_data(self, data_sources: Dict[str, str]) -> Dict[str, pd.DataFrame]:
        """Load Afghanistan-specific data sources."""
        raw_data = {}
        
        try:
            if 'afghanistan_prices' in data_sources:
                raw_data['prices'] = pd.read_csv(data_sources['afghanistan_prices'])
            
            if 'afn_rates' in data_sources:
                raw_data['exchange_rates'] = pd.read_csv(data_sources['afn_rates'])
            
            if 'afghanistan_conflict' in data_sources:
                raw_data['conflict'] = pd.read_csv(data_sources['afghanistan_conflict'])
            
            if 'afghanistan_admin' in data_sources:
                raw_data['geography'] = pd.read_csv(data_sources['afghanistan_admin'])
        
        except Exception as e:
            logger.error(f"Failed to load Afghanistan data: {e}")
            raw_data = {}
        
        return raw_data
    
    def _transform_taliban_control_mapping(self, data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Map Afghan territorial control to currency zones."""
        if 'prices' not in data:
            return data
        
        # Afghanistan's post-2021 currency situation:
        # Taliban areas = Limited banking, cash-based AFN
        # Urban areas = Mixed AFN/USD, banking restrictions
        # Border areas = Pakistani Rupee, Iranian Rial influence
        
        control_mapping = {
            'taliban_rural': 'afn_cash',
            'taliban_urban': 'afn_restricted_banking',
            'border_pakistan': 'pkr_influence',
            'border_iran': 'irr_influence',
            'international_zone': 'usd_preferred'
        }
        
        if 'control_zone' in data['prices'].columns:
            data['prices']['currency_zone'] = data['prices']['control_zone'].map(control_mapping).fillna('afn_cash')
        
        return data
    
    def _transform_post_2021_focus(self, data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Focus analysis on post-Taliban takeover period."""
        cutoff_date = pd.to_datetime('2021-08-15')  # Taliban takeover
        
        for source, df in data.items():
            if not df.empty and 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
                data[source] = df[df['date'] >= cutoff_date].copy()
        
        return data
    
    def _transform_banking_collapse_adjustment(self, data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Account for banking system collapse effects."""
        if 'exchange_rates' not in data:
            return data
        
        # Add volatility adjustment for post-banking collapse period
        exchange_data = data['exchange_rates'].copy()
        exchange_data['date'] = pd.to_datetime(exchange_data['date'])
        
        # Higher volatility and spreads after banking collapse
        post_collapse = exchange_data['date'] >= pd.to_datetime('2021-08-15')
        if 'rate' in exchange_data.columns:
            exchange_data.loc[post_collapse, 'volatility_adjustment'] = 1.5
            exchange_data.loc[~post_collapse, 'volatility_adjustment'] = 1.0
        
        data['exchange_rates'] = exchange_data
        return data


class CountryDataAdapterFactory:
    """Factory for creating country-specific data adapters."""
    
    _adapters = {
        Country.SYRIA: SyriaDataAdapter,
        Country.LEBANON: LebanonDataAdapter,
        Country.SOMALIA: SomaliaDataAdapter,
        Country.AFGHANISTAN: AfghanistanDataAdapter
    }
    
    @classmethod
    def create_adapter(cls, country: Country) -> CountryDataAdapterBase:
        """Create adapter for specified country."""
        if country not in cls._adapters:
            raise ValueError(f"No adapter available for {country.value}")
        
        return cls._adapters[country]()
    
    @classmethod
    def get_supported_countries(cls) -> List[Country]:
        """Get list of supported countries."""
        return list(cls._adapters.keys())
    
    @classmethod
    def validate_all_countries(cls, data_sources: Dict[Country, Dict[str, str]]) -> Dict[Country, ValidationDataPackage]:
        """Create validation data packages for all countries."""
        packages = {}
        
        for country in cls.get_supported_countries():
            if country in data_sources:
                try:
                    adapter = cls.create_adapter(country)
                    raw_data = adapter.load_raw_data(data_sources[country])
                    packages[country] = adapter.standardize_data(raw_data)
                    logger.info(f"Successfully created validation package for {country.value}")
                except Exception as e:
                    logger.error(f"Failed to create validation package for {country.value}: {e}")
        
        return packages