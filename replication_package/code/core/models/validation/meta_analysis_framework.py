"""
Meta-analysis framework for cross-country validation of currency fragmentation effects.

Provides statistical synthesis of findings across Yemen, Syria, Lebanon, Somalia, 
and Afghanistan to assess external validity and generalizability of the methodology.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass
from datetime import datetime
import warnings
from scipy import stats
from scipy.stats import chi2
import matplotlib.pyplot as plt
import seaborn as sns

from .cross_country_validation import CrossCountryResults, CountryValidationResult
from ...domain.shared.value_objects import Country
from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class EffectSize:
    """Effect size measurement for meta-analysis."""
    country: Country
    hypothesis: str
    effect: float
    standard_error: float
    confidence_interval: Tuple[float, float]
    sample_size: int
    p_value: float
    methodology: str


@dataclass
class MetaAnalysisResult:
    """Results from meta-analysis across countries."""
    hypothesis: str
    pooled_effect: float
    pooled_se: float
    confidence_interval: Tuple[float, float]
    heterogeneity_i2: float
    heterogeneity_p_value: float
    publication_bias_p: Optional[float]
    moderator_effects: Dict[str, float]
    country_effects: Dict[Country, float]
    forest_plot_data: pd.DataFrame


@dataclass
class CrossCountryMetaAnalysis:
    """Complete meta-analysis results."""
    effect_sizes: List[EffectSize]
    meta_results: Dict[str, MetaAnalysisResult]
    overall_validity: float
    generalizability_score: float
    policy_implications: List[str]
    publication_readiness: bool
    quality_assessment: Dict[str, float]


class MetaAnalysisFramework:
    """
    Framework for conducting meta-analysis of cross-country validation results.
    
    Synthesizes evidence across conflict-affected countries to assess:
    1. External validity of Yemen findings
    2. Heterogeneity in effects across contexts
    3. Publication bias assessment
    4. Policy-relevant effect sizes
    """
    
    def __init__(self):
        """Initialize meta-analysis framework."""
        self.quality_weights = {
            'data_completeness': 0.3,
            'methodology_rigor': 0.4,
            'sample_size': 0.2,
            'temporal_coverage': 0.1
        }
        
        # Core hypotheses for meta-analysis
        self.core_hypotheses = [
            'H1_currency_mechanism',
            'H5_arbitrage_fails', 
            'H9_threshold_effects',
            'conflict_correlation',
            'fragmentation_detection'
        ]
    
    def conduct_meta_analysis(self, 
                             cross_country_results: CrossCountryResults,
                             effect_size_data: Optional[Dict[Country, Dict[str, float]]] = None) -> CrossCountryMetaAnalysis:
        """
        Conduct comprehensive meta-analysis of cross-country validation.
        
        Args:
            cross_country_results: Results from cross-country validation
            effect_size_data: Optional detailed effect size data
            
        Returns:
            Complete meta-analysis results
        """
        logger.info("Starting cross-country meta-analysis")
        
        # Extract effect sizes from validation results
        effect_sizes = self._extract_effect_sizes(cross_country_results, effect_size_data)
        
        # Conduct meta-analysis for each hypothesis
        meta_results = {}
        for hypothesis in self.core_hypotheses:
            hypothesis_effects = [es for es in effect_sizes if es.hypothesis == hypothesis]
            if len(hypothesis_effects) >= 2:  # Need at least 2 studies
                meta_results[hypothesis] = self._analyze_hypothesis(hypothesis_effects)
        
        # Assess overall validity and generalizability
        overall_validity = self._calculate_overall_validity(meta_results)
        generalizability_score = self._calculate_generalizability(meta_results, cross_country_results)
        
        # Generate policy implications
        policy_implications = self._generate_policy_implications(meta_results, cross_country_results)
        
        # Assess publication readiness
        publication_readiness = self._assess_publication_readiness(meta_results, overall_validity)
        
        # Quality assessment
        quality_assessment = self._assess_study_quality(cross_country_results)
        
        logger.info(f"Meta-analysis complete. Overall validity: {overall_validity:.2%}")
        
        return CrossCountryMetaAnalysis(
            effect_sizes=effect_sizes,
            meta_results=meta_results,
            overall_validity=overall_validity,
            generalizability_score=generalizability_score,
            policy_implications=policy_implications,
            publication_readiness=publication_readiness,
            quality_assessment=quality_assessment
        )
    
    def _extract_effect_sizes(self, 
                             cross_country_results: CrossCountryResults,
                             effect_size_data: Optional[Dict[Country, Dict[str, float]]]) -> List[EffectSize]:
        """Extract effect sizes from country validation results."""
        effect_sizes = []
        
        for country, result in cross_country_results.country_results.items():
            # Use provided effect size data if available
            if effect_size_data and country in effect_size_data:
                country_effects = effect_size_data[country]
            else:
                # Estimate effect sizes from validation results
                country_effects = self._estimate_effect_sizes(result)
            
            # Create effect size objects
            for hypothesis, effect_data in country_effects.items():
                if isinstance(effect_data, dict):
                    effect = effect_data.get('effect', 0.0)
                    se = effect_data.get('standard_error', 0.1)
                    p_value = effect_data.get('p_value', 0.5)
                    sample_size = effect_data.get('sample_size', 100)
                else:
                    # Simple effect estimate
                    effect = float(effect_data)
                    se = 0.1  # Default SE
                    p_value = 0.05 if abs(effect) > 0.2 else 0.5
                    sample_size = 100  # Default sample size
                
                # Calculate confidence interval
                ci_lower = effect - 1.96 * se
                ci_upper = effect + 1.96 * se
                
                effect_sizes.append(EffectSize(
                    country=country,
                    hypothesis=hypothesis,
                    effect=effect,
                    standard_error=se,
                    confidence_interval=(ci_lower, ci_upper),
                    sample_size=sample_size,
                    p_value=p_value,
                    methodology='spatial_econometric'
                ))
        
        return effect_sizes
    
    def _estimate_effect_sizes(self, result: CountryValidationResult) -> Dict[str, float]:
        """Estimate effect sizes from validation results."""
        effects = {}
        
        # Convert binary hypothesis results to effect sizes
        for hypothesis, confirmed in result.hypothesis_results.items():
            if confirmed:
                # Positive effect size based on confidence
                effects[hypothesis] = result.confidence_score * 0.8
            else:
                # Small or negative effect
                effects[hypothesis] = -0.1
        
        # Fragmentation detection effect
        effects['fragmentation_detection'] = result.fragmentation_index
        
        # Model performance as effect size
        if result.model_performance:
            avg_performance = np.mean(list(result.model_performance.values()))
            effects['model_performance'] = avg_performance
        
        return effects
    
    def _analyze_hypothesis(self, hypothesis_effects: List[EffectSize]) -> MetaAnalysisResult:
        """Conduct meta-analysis for a specific hypothesis."""
        if len(hypothesis_effects) < 2:
            logger.warning(f"Insufficient studies for hypothesis: {hypothesis_effects[0].hypothesis}")
            return None
        
        hypothesis = hypothesis_effects[0].hypothesis
        logger.info(f"Meta-analyzing hypothesis: {hypothesis}")
        
        # Extract effect sizes and weights
        effects = np.array([es.effect for es in hypothesis_effects])
        variances = np.array([es.standard_error ** 2 for es in hypothesis_effects])
        weights = 1 / variances
        
        # Fixed effects meta-analysis
        pooled_effect = np.sum(weights * effects) / np.sum(weights)
        pooled_variance = 1 / np.sum(weights)
        pooled_se = np.sqrt(pooled_variance)
        
        # Confidence interval
        ci_lower = pooled_effect - 1.96 * pooled_se
        ci_upper = pooled_effect + 1.96 * pooled_se
        
        # Test for heterogeneity (I²)
        Q = np.sum(weights * (effects - pooled_effect) ** 2)
        df = len(effects) - 1
        heterogeneity_p_value = 1 - chi2.cdf(Q, df) if df > 0 else 1.0
        
        # I² statistic
        I2 = max(0, (Q - df) / Q) * 100 if Q > 0 else 0
        
        # Publication bias test (Egger's test approximation)
        publication_bias_p = self._test_publication_bias(hypothesis_effects)
        
        # Moderator effects (simplified)
        moderator_effects = self._analyze_moderators(hypothesis_effects)
        
        # Country-specific effects
        country_effects = {es.country: es.effect for es in hypothesis_effects}
        
        # Prepare forest plot data
        forest_plot_data = pd.DataFrame({
            'country': [es.country.value for es in hypothesis_effects],
            'effect': effects,
            'se': [es.standard_error for es in hypothesis_effects],
            'ci_lower': [es.confidence_interval[0] for es in hypothesis_effects],
            'ci_upper': [es.confidence_interval[1] for es in hypothesis_effects],
            'weight': weights / np.sum(weights) * 100
        })
        
        return MetaAnalysisResult(
            hypothesis=hypothesis,
            pooled_effect=pooled_effect,
            pooled_se=pooled_se,
            confidence_interval=(ci_lower, ci_upper),
            heterogeneity_i2=I2,
            heterogeneity_p_value=heterogeneity_p_value,
            publication_bias_p=publication_bias_p,
            moderator_effects=moderator_effects,
            country_effects=country_effects,
            forest_plot_data=forest_plot_data
        )
    
    def _test_publication_bias(self, effect_sizes: List[EffectSize]) -> Optional[float]:
        """Test for publication bias using Egger's test approximation."""
        if len(effect_sizes) < 3:
            return None
        
        try:
            # Egger's test: regress effect size on precision
            effects = np.array([es.effect for es in effect_sizes])
            precisions = np.array([1/es.standard_error for es in effect_sizes])
            
            # Linear regression
            slope, intercept, r_value, p_value, std_err = stats.linregress(precisions, effects)
            
            # Test if intercept significantly different from 0
            t_stat = intercept / std_err
            p_bias = 2 * (1 - stats.t.cdf(abs(t_stat), len(effects) - 2))
            
            return p_bias
        except Exception as e:
            logger.warning(f"Publication bias test failed: {e}")
            return None
    
    def _analyze_moderators(self, effect_sizes: List[EffectSize]) -> Dict[str, float]:
        """Analyze moderator effects (simplified)."""
        moderators = {}
        
        # Sample size moderator
        sample_sizes = [es.sample_size for es in effect_sizes]
        effects = [es.effect for es in effect_sizes]
        
        if len(set(sample_sizes)) > 1:  # Variation in sample sizes
            corr, p_value = stats.pearsonr(sample_sizes, effects)
            moderators['sample_size_correlation'] = corr
        
        # Methodology variation (if any)
        methodologies = [es.methodology for es in effect_sizes]
        if len(set(methodologies)) > 1:
            # Compare effect sizes by methodology
            method_effects = {}
            for method in set(methodologies):
                method_effects[method] = np.mean([es.effect for es in effect_sizes if es.methodology == method])
            moderators.update(method_effects)
        
        return moderators
    
    def _calculate_overall_validity(self, meta_results: Dict[str, MetaAnalysisResult]) -> float:
        """Calculate overall methodology validity score."""
        if not meta_results:
            return 0.0
        
        validity_scores = []
        
        for hypothesis, result in meta_results.items():
            # Weight by inverse of standard error (precision)
            precision = 1 / result.pooled_se if result.pooled_se > 0 else 1
            
            # Significant positive effect increases validity
            if result.confidence_interval[0] > 0:  # Significant positive
                score = min(1.0, abs(result.pooled_effect))
            elif result.confidence_interval[1] < 0:  # Significant negative
                score = 0.2  # Some validity but contrary to expectation
            else:  # Non-significant
                score = 0.5
            
            # Adjust for heterogeneity
            if result.heterogeneity_i2 > 75:  # High heterogeneity
                score *= 0.7
            elif result.heterogeneity_i2 > 50:  # Moderate heterogeneity
                score *= 0.85
            
            validity_scores.append(score * precision)
        
        # Weighted average
        weights = [1/meta_results[h].pooled_se for h in meta_results.keys()]
        return np.average([s/w for s, w in zip(validity_scores, weights)], weights=weights)
    
    def _calculate_generalizability(self, 
                                   meta_results: Dict[str, MetaAnalysisResult],
                                   cross_country_results: CrossCountryResults) -> float:
        """Calculate generalizability score across countries."""
        if not meta_results:
            return 0.0
        
        generalizability_scores = []
        
        for result in meta_results.values():
            # Low heterogeneity = high generalizability
            het_score = max(0, 1 - result.heterogeneity_i2 / 100)
            
            # Consistent direction across countries
            country_effects = list(result.country_effects.values())
            if len(country_effects) > 1:
                signs = [1 if e > 0 else -1 for e in country_effects]
                consistency = len([s for s in signs if s == signs[0]]) / len(signs)
            else:
                consistency = 1.0
            
            # Significant pooled effect
            significance = 1.0 if result.confidence_interval[0] > 0 or result.confidence_interval[1] < 0 else 0.5
            
            gen_score = np.mean([het_score, consistency, significance])
            generalizability_scores.append(gen_score)
        
        return np.mean(generalizability_scores)
    
    def _generate_policy_implications(self,
                                    meta_results: Dict[str, MetaAnalysisResult],
                                    cross_country_results: CrossCountryResults) -> List[str]:
        """Generate policy implications from meta-analysis."""
        implications = []
        
        # Check for consistent currency fragmentation effects
        if 'fragmentation_detection' in meta_results:
            result = meta_results['fragmentation_detection']
            if result.pooled_effect > 0.5 and result.confidence_interval[0] > 0.3:
                implications.append(
                    f"Currency fragmentation consistently reduces market integration "
                    f"(pooled effect: {result.pooled_effect:.2f}, 95% CI: "
                    f"{result.confidence_interval[0]:.2f}-{result.confidence_interval[1]:.2f})"
                )
        
        # Threshold effects for early warning
        if 'H9_threshold_effects' in meta_results:
            result = meta_results['H9_threshold_effects'] 
            if result.pooled_effect > 0.3:
                # Calculate average threshold from country results
                thresholds = [r.threshold_value for r in cross_country_results.country_results.values() 
                            if r.threshold_value is not None]
                if thresholds:
                    avg_threshold = np.mean(thresholds)
                    implications.append(
                        f"Early warning systems should trigger at {avg_threshold:.0f}% "
                        f"exchange rate differential across countries"
                    )
        
        # Aid effectiveness implications
        if 'H7_aid_effectiveness' in meta_results:
            result = meta_results['H7_aid_effectiveness']
            if result.confidence_interval[0] > 0:
                implications.append(
                    "Aid effectiveness significantly varies by currency zone - "
                    "targeting and modality adjustments needed"
                )
        
        # Cross-border arbitrage failure
        if 'H5_arbitrage_fails' in meta_results:
            result = meta_results['H5_arbitrage_fails']
            if result.pooled_effect > 0.4:
                implications.append(
                    "Cross-border arbitrage fails in conflict settings - "
                    "private sector hedging strategies essential"
                )
        
        # Heterogeneity implications
        high_heterogeneity = [h for h, r in meta_results.items() if r.heterogeneity_i2 > 75]
        if high_heterogeneity:
            implications.append(
                f"High heterogeneity in {', '.join(high_heterogeneity)} suggests "
                f"context-specific factors require tailored approaches"
            )
        
        return implications
    
    def _assess_publication_readiness(self, 
                                    meta_results: Dict[str, MetaAnalysisResult],
                                    overall_validity: float) -> bool:
        """Assess whether results are ready for publication."""
        criteria = {
            'sufficient_studies': len(meta_results) >= 3,
            'overall_validity': overall_validity > 0.7,
            'significant_effects': any(r.confidence_interval[0] > 0 for r in meta_results.values()),
            'low_publication_bias': all(
                r.publication_bias_p is None or r.publication_bias_p > 0.05 
                for r in meta_results.values()
            )
        }
        
        return sum(criteria.values()) >= 3
    
    def _assess_study_quality(self, cross_country_results: CrossCountryResults) -> Dict[str, float]:
        """Assess quality of individual country studies."""
        quality_scores = {}
        
        for country, result in cross_country_results.country_results.items():
            # Data completeness
            data_completeness = result.model_performance.get('data_coverage', 0.5)
            
            # Methodology rigor (based on validation score)
            methodology_rigor = result.confidence_score
            
            # Sample size adequacy (simplified)
            sample_size_score = 0.8  # Default adequate
            
            # Temporal coverage (simplified)
            temporal_coverage = 0.7  # Default reasonable
            
            # Weighted quality score
            quality_score = (
                self.quality_weights['data_completeness'] * data_completeness +
                self.quality_weights['methodology_rigor'] * methodology_rigor +
                self.quality_weights['sample_size'] * sample_size_score +
                self.quality_weights['temporal_coverage'] * temporal_coverage
            )
            
            quality_scores[country.value] = quality_score
        
        return quality_scores
    
    def generate_forest_plots(self, meta_analysis: CrossCountryMetaAnalysis, save_path: Optional[str] = None) -> Dict[str, plt.Figure]:
        """Generate forest plots for meta-analysis results."""
        figures = {}
        
        for hypothesis, result in meta_analysis.meta_results.items():
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # Plot country effects
            y_positions = range(len(result.forest_plot_data))
            countries = result.forest_plot_data['country']
            effects = result.forest_plot_data['effect']
            ci_lower = result.forest_plot_data['ci_lower']
            ci_upper = result.forest_plot_data['ci_upper']
            weights = result.forest_plot_data['weight']
            
            # Error bars for confidence intervals
            ax.errorbar(effects, y_positions, xerr=[effects - ci_lower, ci_upper - effects], 
                       fmt='o', capsize=5, capthick=2)
            
            # Point sizes proportional to weights
            sizes = weights * 10
            ax.scatter(effects, y_positions, s=sizes, alpha=0.7)
            
            # Pooled effect line
            ax.axvline(result.pooled_effect, color='red', linestyle='--', linewidth=2, 
                      label=f'Pooled Effect: {result.pooled_effect:.3f}')
            
            # Pooled effect confidence interval
            ax.axvspan(result.confidence_interval[0], result.confidence_interval[1], 
                      alpha=0.2, color='red')
            
            # Null effect line
            ax.axvline(0, color='black', linestyle='-', alpha=0.3)
            
            # Labels
            ax.set_yticks(y_positions)
            ax.set_yticklabels(countries)
            ax.set_xlabel('Effect Size')
            ax.set_title(f'Forest Plot: {hypothesis.replace("_", " ").title()}')
            ax.legend()
            
            # Add heterogeneity info
            ax.text(0.02, 0.98, f'I² = {result.heterogeneity_i2:.1f}%', 
                   transform=ax.transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
            
            plt.tight_layout()
            figures[hypothesis] = fig
            
            if save_path:
                fig.savefig(f"{save_path}/forest_plot_{hypothesis}.png", dpi=300, bbox_inches='tight')
        
        return figures
    
    def generate_meta_analysis_report(self, meta_analysis: CrossCountryMetaAnalysis) -> str:
        """Generate comprehensive meta-analysis report."""
        report = f"""
CROSS-COUNTRY META-ANALYSIS REPORT
==================================
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M')}

EXECUTIVE SUMMARY
-----------------
Overall Methodology Validity: {meta_analysis.overall_validity:.1%}
Generalizability Score: {meta_analysis.generalizability_score:.1%}
Publication Ready: {'YES' if meta_analysis.publication_readiness else 'NO'}
Countries Analyzed: {len(set(es.country for es in meta_analysis.effect_sizes))}

HYPOTHESIS-BY-HYPOTHESIS RESULTS
--------------------------------
"""
        
        for hypothesis, result in meta_analysis.meta_results.items():
            report += f"""
{hypothesis.replace('_', ' ').upper()}
Pooled Effect: {result.pooled_effect:.3f} (95% CI: {result.confidence_interval[0]:.3f} to {result.confidence_interval[1]:.3f})
Standard Error: {result.pooled_se:.3f}
Heterogeneity I²: {result.heterogeneity_i2:.1f}%
Heterogeneity p-value: {result.heterogeneity_p_value:.3f}
Publication Bias p-value: {result.publication_bias_p:.3f if result.publication_bias_p else 'N/A'}

Country Effects:
"""
            for country, effect in result.country_effects.items():
                report += f"  • {country.value}: {effect:.3f}\n"
        
        report += """
QUALITY ASSESSMENT
------------------
"""
        for country, quality in meta_analysis.quality_assessment.items():
            report += f"• {country}: {quality:.1%}\n"
        
        report += """
POLICY IMPLICATIONS
-------------------
"""
        for i, implication in enumerate(meta_analysis.policy_implications, 1):
            report += f"{i}. {implication}\n"
        
        report += f"""
GENERALIZABILITY ASSESSMENT
---------------------------
The currency fragmentation methodology shows {meta_analysis.generalizability_score:.0%} 
generalizability across conflict-affected countries. 

{'Results support broader application of the methodology across similar contexts.' if meta_analysis.generalizability_score > 0.7 else 'Results suggest context-specific modifications may be needed.'}

PUBLICATION READINESS
--------------------
{'The meta-analysis meets criteria for publication in peer-reviewed journals.' if meta_analysis.publication_readiness else 'Additional studies or methodological refinements needed before publication.'}

END OF REPORT
"""
        return report