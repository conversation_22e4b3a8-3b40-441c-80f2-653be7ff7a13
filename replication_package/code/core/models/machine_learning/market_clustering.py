"""
Market Clustering using Machine Learning

This module implements ML-based clustering to identify market typologies
and integration patterns in Yemen's fragmented market system.

Key Features:
1. Feature engineering for market characteristics
2. Multiple clustering algorithms (K-means, DBSCAN, Hierarchical)
3. Currency zone-aware clustering
4. Temporal stability analysis
5. Integration with Three-Tier framework
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from datetime import datetime
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.ensemble import RandomForestRegressor
import logging

logger = logging.getLogger(__name__)


@dataclass
class MarketFeatures:
    """Features characterizing a market for clustering."""
    market_id: str
    
    # Price characteristics
    avg_price_level: float  # Average price in USD
    price_volatility: float  # Standard deviation of prices
    price_trend: float  # Trend coefficient
    
    # Currency zone features
    currency_zone: str
    exchange_rate_volatility: float
    zone_stability_score: float  # 0-1 score
    
    # Integration metrics
    cointegration_count: int  # Number of cointegrated markets
    avg_price_correlation: float  # Average correlation with other markets
    price_transmission_speed: float  # From VECM analysis
    
    # Conflict features
    conflict_intensity: float  # Average conflict events
    conflict_variability: float  # Std dev of conflict
    control_changes: int  # Number of control changes
    
    # Geographic features
    distance_to_capital: float  # km
    distance_to_port: float  # km
    distance_to_border: float  # km
    
    # Trade features
    market_size_score: float  # Relative market size
    trade_flow_intensity: float  # Volume of trade
    infrastructure_quality: float  # Road quality index
    
    # Additional metadata
    observation_period: str
    data_quality_score: float


@dataclass
class ClusteringResults:
    """Results from market clustering analysis."""
    algorithm: str
    n_clusters: int
    cluster_labels: np.ndarray
    cluster_centers: Optional[np.ndarray]
    
    # Quality metrics
    silhouette_score: float
    calinski_harabasz_score: float
    within_cluster_variance: float
    between_cluster_variance: float
    
    # Cluster characteristics
    cluster_profiles: Dict[int, Dict[str, float]]
    cluster_stability: Dict[int, float]  # Temporal stability
    
    # Feature importance
    feature_importance: Dict[str, float]
    
    # Validation
    cross_validation_score: Optional[float] = None
    bootstrap_stability: Optional[float] = None


class MarketClusterAnalyzer:
    """
    Identifies market typologies using machine learning clustering.
    
    This analyzer helps understand:
    1. Natural market groupings beyond administrative boundaries
    2. Integration patterns within and across currency zones
    3. Impact of conflict on market clustering
    4. Optimal market aggregation for analysis
    """
    
    def __init__(self, 
                 n_clusters: Optional[int] = None,
                 clustering_algorithm: str = 'kmeans',
                 currency_zone_weight: float = 2.0):
        """
        Initialize market cluster analyzer.
        
        Args:
            n_clusters: Number of clusters (None for automatic selection)
            clustering_algorithm: Algorithm to use ('kmeans', 'dbscan', 'hierarchical')
            currency_zone_weight: Weight for currency zone features
        """
        self.n_clusters = n_clusters
        self.clustering_algorithm = clustering_algorithm
        self.currency_zone_weight = currency_zone_weight
        self.scaler = StandardScaler()
        self.feature_columns = []
        self.fitted = False
        
    def create_market_features(self, 
                             price_data: pd.DataFrame,
                             conflict_data: Optional[pd.DataFrame] = None,
                             geographic_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        Create feature matrix for clustering.
        
        Args:
            price_data: Panel data with prices, exchange rates, zones
            conflict_data: Conflict event data
            geographic_data: Geographic and infrastructure data
            
        Returns:
            DataFrame with market features
        """
        logger.info("Creating market features for clustering")
        
        features_list = []
        
        # Group by market
        for market_id, market_data in price_data.groupby('market_id'):
            features = self._extract_market_features(
                market_id, 
                market_data,
                price_data,
                conflict_data,
                geographic_data
            )
            features_list.append(features)
        
        # Convert to DataFrame
        features_df = pd.DataFrame([
            {
                'market_id': f.market_id,
                'avg_price_level': f.avg_price_level,
                'price_volatility': f.price_volatility,
                'price_trend': f.price_trend,
                'currency_zone': f.currency_zone,
                'exchange_rate_volatility': f.exchange_rate_volatility,
                'zone_stability_score': f.zone_stability_score,
                'cointegration_count': f.cointegration_count,
                'avg_price_correlation': f.avg_price_correlation,
                'price_transmission_speed': f.price_transmission_speed,
                'conflict_intensity': f.conflict_intensity,
                'conflict_variability': f.conflict_variability,
                'control_changes': f.control_changes,
                'distance_to_capital': f.distance_to_capital,
                'distance_to_port': f.distance_to_port,
                'distance_to_border': f.distance_to_border,
                'market_size_score': f.market_size_score,
                'trade_flow_intensity': f.trade_flow_intensity,
                'infrastructure_quality': f.infrastructure_quality,
                'data_quality_score': f.data_quality_score
            }
            for f in features_list
        ])
        
        logger.info(f"Created features for {len(features_df)} markets")
        
        return features_df
    
    def _extract_market_features(self,
                               market_id: str,
                               market_data: pd.DataFrame,
                               all_price_data: pd.DataFrame,
                               conflict_data: Optional[pd.DataFrame],
                               geographic_data: Optional[pd.DataFrame]) -> MarketFeatures:
        """Extract features for a single market."""
        
        # Price characteristics (in USD)
        avg_price = market_data['price_usd'].mean()
        price_volatility = market_data['price_usd'].std()
        
        # Price trend
        if len(market_data) > 2:
            time_index = np.arange(len(market_data))
            trend_coef = np.polyfit(time_index, market_data['price_usd'].values, 1)[0]
        else:
            trend_coef = 0.0
        
        # Currency zone (most common)
        currency_zone = market_data['currency_zone'].mode().iloc[0]
        
        # Exchange rate volatility
        if 'exchange_rate_used' in market_data.columns:
            er_volatility = market_data['exchange_rate_used'].std()
        else:
            er_volatility = 0.0
        
        # Zone stability (based on exchange rate consistency)
        zone_stability = 1.0 - min(er_volatility / 1000, 1.0)  # Normalize
        
        # Integration metrics (simplified - would come from VECM analysis)
        cointegration_count = self._estimate_cointegration_count(
            market_id, all_price_data
        )
        
        avg_correlation = self._calculate_avg_correlation(
            market_id, all_price_data
        )
        
        # Price transmission speed (placeholder - would come from VECM)
        transmission_speed = avg_correlation * 0.5  # Simplified
        
        # Conflict features
        if conflict_data is not None and market_id in conflict_data['market_id'].values:
            market_conflict = conflict_data[conflict_data['market_id'] == market_id]
            conflict_intensity = market_conflict['event_count'].mean()
            conflict_variability = market_conflict['event_count'].std()
            control_changes = market_conflict['control_change'].sum()
        else:
            conflict_intensity = 0.0
            conflict_variability = 0.0
            control_changes = 0
        
        # Geographic features
        if geographic_data is not None and market_id in geographic_data.index:
            geo = geographic_data.loc[market_id]
            distance_capital = geo.get('distance_to_capital', 100.0)
            distance_port = geo.get('distance_to_port', 100.0)
            distance_border = geo.get('distance_to_border', 50.0)
            market_size = geo.get('market_size_score', 0.5)
            trade_flow = geo.get('trade_flow_intensity', 0.5)
            infrastructure = geo.get('infrastructure_quality', 0.5)
        else:
            # Default values
            distance_capital = 100.0
            distance_port = 100.0
            distance_border = 50.0
            market_size = 0.5
            trade_flow = 0.5
            infrastructure = 0.5
        
        # Data quality
        data_quality = 1.0 - (market_data['price_usd'].isna().sum() / len(market_data))
        
        return MarketFeatures(
            market_id=market_id,
            avg_price_level=avg_price,
            price_volatility=price_volatility,
            price_trend=trend_coef,
            currency_zone=currency_zone,
            exchange_rate_volatility=er_volatility,
            zone_stability_score=zone_stability,
            cointegration_count=cointegration_count,
            avg_price_correlation=avg_correlation,
            price_transmission_speed=transmission_speed,
            conflict_intensity=conflict_intensity,
            conflict_variability=conflict_variability,
            control_changes=control_changes,
            distance_to_capital=distance_capital,
            distance_to_port=distance_port,
            distance_to_border=distance_border,
            market_size_score=market_size,
            trade_flow_intensity=trade_flow,
            infrastructure_quality=infrastructure,
            observation_period=f"{market_data['date'].min()} to {market_data['date'].max()}",
            data_quality_score=data_quality
        )
    
    def _estimate_cointegration_count(self, 
                                    market_id: str,
                                    all_price_data: pd.DataFrame) -> int:
        """Estimate number of cointegrated markets (simplified)."""
        # In practice, this would use proper cointegration tests
        # Here we use correlation as a proxy
        
        market_prices = all_price_data[all_price_data['market_id'] == market_id]['price_usd']
        if len(market_prices) < 10:
            return 0
        
        cointegrated = 0
        for other_market in all_price_data['market_id'].unique():
            if other_market != market_id:
                other_prices = all_price_data[all_price_data['market_id'] == other_market]['price_usd']
                if len(other_prices) >= 10:
                    # Simplified: high correlation as proxy for cointegration
                    corr = market_prices.corr(other_prices)
                    if corr > 0.8:
                        cointegrated += 1
        
        return cointegrated
    
    def _calculate_avg_correlation(self,
                                 market_id: str,
                                 all_price_data: pd.DataFrame) -> float:
        """Calculate average price correlation with other markets."""
        market_prices = all_price_data[all_price_data['market_id'] == market_id]['price_usd']
        if len(market_prices) < 5:
            return 0.0
        
        correlations = []
        for other_market in all_price_data['market_id'].unique():
            if other_market != market_id:
                other_prices = all_price_data[all_price_data['market_id'] == other_market]['price_usd']
                if len(other_prices) >= 5:
                    corr = market_prices.corr(other_prices)
                    if not np.isnan(corr):
                        correlations.append(corr)
        
        return np.mean(correlations) if correlations else 0.0
    
    def fit(self, features_df: pd.DataFrame) -> 'MarketClusterAnalyzer':
        """
        Fit clustering model on market features.
        
        Args:
            features_df: DataFrame with market features
            
        Returns:
            Self for chaining
        """
        logger.info(f"Fitting {self.clustering_algorithm} clustering model")
        
        # Prepare features
        X, feature_names = self._prepare_features(features_df)
        self.feature_columns = feature_names
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Determine optimal number of clusters if not specified
        if self.n_clusters is None:
            self.n_clusters = self._find_optimal_clusters(X_scaled)
            logger.info(f"Optimal number of clusters: {self.n_clusters}")
        
        # Fit clustering model
        if self.clustering_algorithm == 'kmeans':
            self.model = KMeans(n_clusters=self.n_clusters, random_state=42)
        elif self.clustering_algorithm == 'dbscan':
            self.model = DBSCAN(eps=0.5, min_samples=5)
        elif self.clustering_algorithm == 'hierarchical':
            self.model = AgglomerativeClustering(n_clusters=self.n_clusters)
        else:
            raise ValueError(f"Unknown algorithm: {self.clustering_algorithm}")
        
        self.cluster_labels_ = self.model.fit_predict(X_scaled)
        self.fitted = True
        
        # Store results
        self._features_df = features_df
        self._X_scaled = X_scaled
        
        logger.info(f"Clustering complete: {len(np.unique(self.cluster_labels_))} clusters found")
        
        return self
    
    def _prepare_features(self, features_df: pd.DataFrame) -> Tuple[np.ndarray, List[str]]:
        """Prepare feature matrix for clustering."""
        # Select numeric features
        numeric_cols = [
            'avg_price_level', 'price_volatility', 'price_trend',
            'exchange_rate_volatility', 'zone_stability_score',
            'cointegration_count', 'avg_price_correlation', 'price_transmission_speed',
            'conflict_intensity', 'conflict_variability', 'control_changes',
            'distance_to_capital', 'distance_to_port', 'distance_to_border',
            'market_size_score', 'trade_flow_intensity', 'infrastructure_quality'
        ]
        
        X = features_df[numeric_cols].values
        
        # Add one-hot encoded currency zone with higher weight
        zone_encoder = LabelEncoder()
        zone_encoded = zone_encoder.fit_transform(features_df['currency_zone'])
        zone_one_hot = np.eye(len(np.unique(zone_encoded)))[zone_encoded]
        
        # Apply currency zone weight
        zone_one_hot *= self.currency_zone_weight
        
        # Combine features
        X = np.hstack([X, zone_one_hot])
        
        # Feature names
        zone_names = [f'zone_{z}' for z in zone_encoder.classes_]
        feature_names = numeric_cols + zone_names
        
        return X, feature_names
    
    def _find_optimal_clusters(self, X: np.ndarray) -> int:
        """Find optimal number of clusters using elbow method."""
        max_clusters = min(10, len(X) // 5)
        
        scores = []
        for k in range(2, max_clusters + 1):
            kmeans = KMeans(n_clusters=k, random_state=42)
            labels = kmeans.fit_predict(X)
            
            if len(np.unique(labels)) > 1:
                score = silhouette_score(X, labels)
                scores.append(score)
            else:
                scores.append(-1)
        
        # Find elbow point (simplified)
        if scores:
            optimal_k = np.argmax(scores) + 2
        else:
            optimal_k = 3  # Default
        
        return optimal_k
    
    def predict(self, features_df: pd.DataFrame) -> np.ndarray:
        """
        Predict cluster labels for new markets.
        
        Args:
            features_df: DataFrame with market features
            
        Returns:
            Array of cluster labels
        """
        if not self.fitted:
            raise ValueError("Model must be fitted before prediction")
        
        X, _ = self._prepare_features(features_df)
        X_scaled = self.scaler.transform(X)
        
        if hasattr(self.model, 'predict'):
            return self.model.predict(X_scaled)
        else:
            # For algorithms without predict (like hierarchical)
            raise NotImplementedError(f"Prediction not available for {self.clustering_algorithm}")
    
    def analyze_clusters(self) -> ClusteringResults:
        """
        Analyze clustering results and create profiles.
        
        Returns:
            ClusteringResults with detailed analysis
        """
        if not self.fitted:
            raise ValueError("Model must be fitted before analysis")
        
        # Calculate quality metrics
        silhouette = silhouette_score(self._X_scaled, self.cluster_labels_)
        calinski = calinski_harabasz_score(self._X_scaled, self.cluster_labels_)
        
        # Within and between cluster variance
        within_var, between_var = self._calculate_cluster_variance()
        
        # Create cluster profiles
        cluster_profiles = self._create_cluster_profiles()
        
        # Assess temporal stability
        cluster_stability = self._assess_cluster_stability()
        
        # Feature importance (using Random Forest)
        feature_importance = self._calculate_feature_importance()
        
        # Get cluster centers if available
        if hasattr(self.model, 'cluster_centers_'):
            centers = self.model.cluster_centers_
        else:
            centers = None
        
        return ClusteringResults(
            algorithm=self.clustering_algorithm,
            n_clusters=len(np.unique(self.cluster_labels_)),
            cluster_labels=self.cluster_labels_,
            cluster_centers=centers,
            silhouette_score=silhouette,
            calinski_harabasz_score=calinski,
            within_cluster_variance=within_var,
            between_cluster_variance=between_var,
            cluster_profiles=cluster_profiles,
            cluster_stability=cluster_stability,
            feature_importance=feature_importance
        )
    
    def _calculate_cluster_variance(self) -> Tuple[float, float]:
        """Calculate within and between cluster variance."""
        within_var = 0.0
        cluster_means = {}
        
        for cluster in np.unique(self.cluster_labels_):
            cluster_mask = self.cluster_labels_ == cluster
            cluster_data = self._X_scaled[cluster_mask]
            
            if len(cluster_data) > 0:
                cluster_mean = cluster_data.mean(axis=0)
                cluster_means[cluster] = cluster_mean
                
                # Within cluster variance
                within_var += np.sum((cluster_data - cluster_mean) ** 2)
        
        # Between cluster variance
        overall_mean = self._X_scaled.mean(axis=0)
        between_var = sum(
            len(self._X_scaled[self.cluster_labels_ == c]) * 
            np.sum((cluster_means[c] - overall_mean) ** 2)
            for c in cluster_means
        )
        
        return within_var, between_var
    
    def _create_cluster_profiles(self) -> Dict[int, Dict[str, float]]:
        """Create interpretable profiles for each cluster."""
        profiles = {}
        
        for cluster in np.unique(self.cluster_labels_):
            cluster_mask = self.cluster_labels_ == cluster
            cluster_features = self._features_df[cluster_mask]
            
            profile = {
                'size': int(cluster_mask.sum()),
                'avg_price_level': cluster_features['avg_price_level'].mean(),
                'avg_volatility': cluster_features['price_volatility'].mean(),
                'dominant_zone': cluster_features['currency_zone'].mode().iloc[0],
                'zone_diversity': len(cluster_features['currency_zone'].unique()),
                'avg_conflict': cluster_features['conflict_intensity'].mean(),
                'avg_integration': cluster_features['avg_price_correlation'].mean(),
                'geographic_spread': cluster_features['distance_to_capital'].std()
            }
            
            profiles[int(cluster)] = profile
        
        return profiles
    
    def _assess_cluster_stability(self) -> Dict[int, float]:
        """Assess temporal stability of clusters (simplified)."""
        # In practice, this would use temporal data
        # Here we use a simple heuristic based on feature variance
        
        stability = {}
        
        for cluster in np.unique(self.cluster_labels_):
            cluster_mask = self.cluster_labels_ == cluster
            cluster_features = self._features_df[cluster_mask]
            
            # Stability based on price volatility and conflict variability
            avg_volatility = cluster_features['price_volatility'].mean()
            avg_conflict_var = cluster_features['conflict_variability'].mean()
            
            # Higher volatility = lower stability
            stability_score = 1.0 / (1.0 + avg_volatility / 100 + avg_conflict_var / 10)
            stability[int(cluster)] = float(stability_score)
        
        return stability
    
    def _calculate_feature_importance(self) -> Dict[str, float]:
        """Calculate feature importance using Random Forest."""
        # Use RF to predict cluster membership
        rf = RandomForestRegressor(n_estimators=100, random_state=42)
        rf.fit(self._X_scaled, self.cluster_labels_)
        
        # Get feature importance
        importance = dict(zip(self.feature_columns, rf.feature_importances_))
        
        # Normalize
        total_importance = sum(importance.values())
        importance = {k: v/total_importance for k, v in importance.items()}
        
        return importance
    
    def visualize_clusters(self, save_path: Optional[str] = None) -> Dict[str, any]:
        """
        Create visualizations of clustering results.
        
        Args:
            save_path: Path to save visualizations
            
        Returns:
            Dictionary of plot objects
        """
        import matplotlib.pyplot as plt
        
        plots = {}
        
        # PCA visualization
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(self._X_scaled)
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Cluster scatter plot
        scatter = ax1.scatter(X_pca[:, 0], X_pca[:, 1], 
                            c=self.cluster_labels_, 
                            cmap='viridis', 
                            alpha=0.6)
        ax1.set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%} variance)')
        ax1.set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%} variance)')
        ax1.set_title('Market Clusters (PCA projection)')
        plt.colorbar(scatter, ax=ax1, label='Cluster')
        
        # Feature importance
        results = self.analyze_clusters()
        importance = results.feature_importance
        top_features = sorted(importance.items(), key=lambda x: x[1], reverse=True)[:10]
        
        features, scores = zip(*top_features)
        ax2.barh(features, scores)
        ax2.set_xlabel('Importance')
        ax2.set_title('Top 10 Feature Importance')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plots['cluster_visualization'] = fig
        
        return plots
    
    def export_cluster_assignments(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """
        Export cluster assignments with market IDs.
        
        Args:
            features_df: Original features DataFrame
            
        Returns:
            DataFrame with market IDs and cluster assignments
        """
        if not self.fitted:
            raise ValueError("Model must be fitted before export")
        
        assignments = pd.DataFrame({
            'market_id': features_df['market_id'],
            'cluster': self.cluster_labels_,
            'currency_zone': features_df['currency_zone']
        })
        
        # Add cluster characteristics
        results = self.analyze_clusters()
        for cluster, profile in results.cluster_profiles.items():
            mask = assignments['cluster'] == cluster
            assignments.loc[mask, 'cluster_size'] = profile['size']
            assignments.loc[mask, 'cluster_stability'] = results.cluster_stability[cluster]
        
        return assignments


# Integration function for Three-Tier framework
def apply_clustering_to_tier1(panel_data: pd.DataFrame,
                            conflict_data: Optional[pd.DataFrame] = None,
                            n_clusters: Optional[int] = None) -> Tuple[pd.DataFrame, ClusteringResults]:
    """
    Apply clustering to Tier 1 pooled analysis.
    
    Args:
        panel_data: Price panel data
        conflict_data: Conflict event data
        n_clusters: Number of clusters (None for automatic)
        
    Returns:
        Tuple of (panel data with cluster assignments, clustering results)
    """
    logger.info("Applying ML clustering to Tier 1 analysis")
    
    # Initialize analyzer
    analyzer = MarketClusterAnalyzer(
        n_clusters=n_clusters,
        clustering_algorithm='kmeans',
        currency_zone_weight=2.0  # High weight on currency zones
    )
    
    # Create features
    features_df = analyzer.create_market_features(
        panel_data,
        conflict_data
    )
    
    # Fit clustering model
    analyzer.fit(features_df)
    
    # Get results
    results = analyzer.analyze_clusters()
    
    # Add cluster assignments to panel data
    assignments = analyzer.export_cluster_assignments(features_df)
    panel_data_clustered = panel_data.merge(
        assignments[['market_id', 'cluster']],
        on='market_id',
        how='left'
    )
    
    logger.info(f"Added cluster assignments for {results.n_clusters} clusters")
    
    return panel_data_clustered, results