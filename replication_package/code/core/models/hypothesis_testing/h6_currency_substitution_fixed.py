"""
H6: Currency Substitution Dynamics

Tests the relationship between exchange rate volatility and the
probability of pricing in USD versus YER.
""" 

import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
import statsmodels.api as sm
from scipy import stats
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler

from .hypothesis_framework import (
    HypothesisTest, TestData, TestResults, PolicyInterpretation
)
from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class CurrencySubstitutionData(TestData):
    """Data structure for currency substitution analysis."""
    price_data: Optional[pd.DataFrame] = None
    exchange_rates: Optional[pd.DataFrame] = None
    volatility_metrics: Optional[pd.DataFrame] = None
    market_characteristics: Optional[pd.DataFrame] = None
    commodity_attributes: Optional[pd.DataFrame] = None


@dataclass
class CurrencySubstitutionResults:
    """Results from currency substitution analysis.
    
    Uses composition with TestResults for core fields while adding
    hypothesis-specific fields.
    """ 
    # Core test results (composition, not inheritance)
    base_results: TestResults
    
    # Hypothesis-specific fields
    usd_pricing_probability: Optional[float] = None
    volatility_threshold: Optional[float] = None
    commodity_dollarization: Optional[Dict[str, float]] = None
    zone_dollarization: Optional[Dict[str, float]] = None
    switching_costs: Optional[float] = None
    network_effects: Optional[float] = None
    persistence_coefficient: Optional[float] = None

    
    def to_dict(self) -> Dict[str, Any]:
        # Convert to dictionary, combining base and specific results.
        result = self.base_results.to_dict()
        result.update({
            'usd_pricing_probability': self.usd_pricing_probability,
            'volatility_threshold': self.volatility_threshold,
            'commodity_dollarization': self.commodity_dollarization,
            'zone_dollarization': self.zone_dollarization,
            'switching_costs': self.switching_costs,
            'network_effects': self.network_effects,
            'persistence_coefficient': self.persistence_coefficient
        })
        return result

class H6CurrencySubstitutionTest(HypothesisTest):
    """
    Tests currency substitution dynamics under fragmentation.
    
    H6: Exchange volatility -> USD pricing probability
        - High volatility increases USD pricing
        - Network externalities create persistence
        - Switching costs determine threshold
    """ 
    
    def __init__(self):
        super().__init__(
            hypothesis_id="H6",
            name="Currency Substitution Dynamics",
            description="Tests USD pricing probability under exchange rate volatility"
        )
        self.volatility_window = 30  # days for volatility calculation
        self.min_observations = 100
    
    def prepare_data(self, panel_data: pd.DataFrame) -> CurrencySubstitutionData:
        # Prepare data for currency substitution analysis.
        logger.info("Preparing data for H6 currency substitution test")
        
        # Calculate exchange rate volatility
        volatility_metrics = self._calculate_volatility_metrics(panel_data)
        
        # Identify USD vs YER pricing
        price_data = self._identify_currency_usage(panel_data)
        
        # Extract market characteristics
        market_chars = self._extract_market_characteristics(panel_data)
        
        # Get commodity attributes
        commodity_attrs = self._get_commodity_attributes(panel_data)
        
        # Get exchange rates
        exchange_rates = self._prepare_exchange_rates(panel_data)
        
        return CurrencySubstitutionData(
            price_data=price_data,
            exchange_rates=exchange_rates,
            volatility_metrics=volatility_metrics,
            market_characteristics=market_chars,
            commodity_attributes=commodity_attrs
        )
    
    def _calculate_volatility_metrics(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        # Calculate various volatility metrics.
        if 'exchange_rate' not in panel_data.columns:
            # Simulate exchange rates if not available
            panel_data = self._simulate_exchange_rates(panel_data)
        
        # Sort by market and date
        df = panel_data.sort_values(['market', 'date'])
        
        # Calculate rolling volatility
        volatility_data = []
        
        for market in df['market'].unique():
            market_data = df[df['market'] == market].copy()
            
            # Log returns
            market_data['log_rate'] = np.log(market_data['exchange_rate'])
            market_data['returns'] = market_data['log_rate'].diff()
            
            # Rolling standard deviation
            market_data['volatility_30d'] = (
                market_data['returns'].rolling(window=self.volatility_window).std()
            )
            
            # GARCH-type conditional volatility (simplified)
            market_data['volatility_garch'] = (
                market_data['returns'].ewm(span=10).std()
            )
            
            # Volatility of volatility
            market_data['vol_of_vol'] = (
                market_data['volatility_30d'].rolling(window=30).std()
            )
            
            # Add to results
            volatility_data.append(market_data[['date', 'market', 'volatility_30d', 
                                               'volatility_garch', 'vol_of_vol']])
        
        if volatility_data:
            return pd.concat(volatility_data, ignore_index=True)
        else:
            return pd.DataFrame()
    
    def _simulate_exchange_rates(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        # Simulate exchange rates if not in data.
        # Create realistic exchange rates with fragmentation
        panel_data = panel_data.copy()
        
        # Base rates by zone
        zone_base_rates = {
            'houthi': 535,
            'government': 1800,
            'disputed': 1200
        }
        
        # Add time trend and volatility
        panel_data['days_since_start'] = (
            panel_data['date'] - panel_data['date'].min()
        ).dt.days
        
        # Simulate exchange rates
        for zone, base_rate in zone_base_rates.items():
            zone_mask = panel_data['currency_zone'] == zone
            
            # Time trend (depreciation)
            trend = panel_data.loc[zone_mask, 'days_since_start'] * 0.5
            
            # Random walk component
            noise = np.random.normal(0, base_rate * 0.02, sum(zone_mask))
            
            # Set exchange rate
            panel_data.loc[zone_mask, 'exchange_rate'] = base_rate + trend + noise
        
        return panel_data
    
    def _identify_currency_usage(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        # Identify whether prices are in USD or YER.
        df = panel_data.copy()
        
        # If currency info not available, infer from price patterns
        if 'price_currency' not in df.columns:
            # Use heuristics to identify currency
            # USD prices typically have decimal places and are smaller
            df['likely_usd'] = (
                (df['price_usd'] < 100) & 
                (df['price_usd'] % 1 != 0)
            ).astype(int)
        else:
            df['likely_usd'] = (df['price_currency'] == 'USD').astype(int)
        
        # Add lagged USD usage for persistence analysis
        df = df.sort_values(['market', 'commodity', 'date'])
        df['usd_usage_lag1'] = (
            df.groupby(['market', 'commodity'])['likely_usd'].shift(1)
        )
        
        return df[['date', 'market', 'commodity', 'price_usd', 'likely_usd', 
                   'usd_usage_lag1']]
    
    def _extract_market_characteristics(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        # Extract market-level characteristics.
        market_data = []
        
        for market in panel_data['market'].unique():
            market_df = panel_data[panel_data['market'] == market]
            
            # Market size proxy (number of observations)
            market_size = len(market_df)
            
            # Trade connectivity (simplified - based on market name)
            is_major_city = market.lower() in ['sanaa', 'aden', 'taiz', 'hodeidah']
            
            # Border market
            is_border = 'border' in market.lower() or market.lower() in ['haradh', 'albuq']
            
            market_data.append({
                'market': market,
                'market_size': market_size,
                'is_major_city': int(is_major_city),
                'is_border_market': int(is_border),
                'connectivity_score': market_size / 1000 + int(is_major_city) * 2
            })
        
        return pd.DataFrame(market_data)
    
    def _get_commodity_attributes(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        # Get commodity-specific attributes.
        commodities = panel_data['commodity'].unique()
        
        # Define commodity characteristics
        tradeable = ['wheat', 'rice', 'sugar', 'oil', 'fuel']
        perishable = ['tomatoes', 'onions', 'meat', 'eggs']
        imported = ['wheat', 'rice', 'sugar', 'fuel']
        
        commodity_data = []
        for commodity in commodities:
            commodity_lower = commodity.lower()
            
            is_tradeable = any(t in commodity_lower for t in tradeable)
            is_perishable = any(p in commodity_lower for p in perishable)
            is_imported = any(i in commodity_lower for i in imported)
            
            commodity_data.append({
                'commodity': commodity,
                'is_tradeable': int(is_tradeable),
                'is_perishable': int(is_perishable),
                'is_imported': int(is_imported),
                'storage_cost': 0.1 if is_perishable else 0.02
            })
        
        return pd.DataFrame(commodity_data)
    
    def _prepare_exchange_rates(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        # Prepare exchange rate data.
        if 'exchange_rate' in panel_data.columns:
            rates = panel_data.groupby(['date', 'currency_zone'])['exchange_rate'].mean()
            return rates.reset_index()
        else:
            # Return empty DataFrame if no exchange rate data
            return pd.DataFrame(columns=['date', 'currency_zone', 'exchange_rate'])
    
    def run_test(self, data: CurrencySubstitutionData) -> CurrencySubstitutionResults:
        # Run currency substitution test.
        logger.info("Running H6 currency substitution test")
        
        # Merge all data sources
        analysis_df = self._merge_analysis_data(data)
        
        if len(analysis_df) < self.min_observations:
            return self._insufficient_data_result()
        
        # Test 1: Basic dollarization model
        basic_results = self._test_basic_dollarization(analysis_df)
        
        # Test 2: Volatility threshold identification
        threshold_results = self._test_volatility_threshold(analysis_df)
        
        # Test 3: Network effects and persistence
        network_results = self._test_network_effects(analysis_df)
        
        # Test 4: Commodity-specific dollarization
        commodity_results = self._test_commodity_dollarization(analysis_df, data)
        
        # Test 5: Zone-specific patterns
        zone_results = self._test_zone_patterns(analysis_df)
        
        # Test 6: Switching costs estimation
        switching_costs = self._estimate_switching_costs(analysis_df)
        
        # Aggregate results
        overall_dollarization = basic_results['avg_dollarization']
        
        return CurrencySubstitutionResults(
            test_passed=basic_results['significant'],
            confidence=self._calculate_confidence(
                basic_results, threshold_results, network_results
            ),
            test_statistic=basic_results['z_stat'],
            p_value=basic_results['p_value'],
            effect_size=basic_results['marginal_effect'],
            summary={
                'overall_dollarization': overall_dollarization,
                'volatility_threshold': threshold_results['threshold'],
                'network_strength': network_results['network_coefficient'],
                'n_observations': len(analysis_df)
            },
            usd_pricing_probability=overall_dollarization,
            volatility_threshold=threshold_results['threshold'],
            commodity_dollarization=commodity_results,
            zone_dollarization=zone_results,
            switching_costs=switching_costs,
            network_effects=network_results['network_coefficient'],
            persistence_coefficient=network_results['persistence']
        )
    
    def _merge_analysis_data(self, data: CurrencySubstitutionData) -> pd.DataFrame:
        # Merge all data sources for analysis.
        # Start with price data
        df = data.price_data.copy()
        
        # Merge volatility metrics
        if not data.volatility_metrics.empty:
            df = pd.merge(df, data.volatility_metrics, on=['date', 'market'], how='left')
        
        # Merge market characteristics
        if not data.market_characteristics.empty:
            df = pd.merge(df, data.market_characteristics, on='market', how='left')
        
        # Merge commodity attributes
        if not data.commodity_attributes.empty:
            df = pd.merge(df, data.commodity_attributes, on='commodity', how='left')
        
        # Fill missing values
        df['volatility_30d'] = df['volatility_30d'].fillna(df['volatility_30d'].mean())
        df['connectivity_score'] = df['connectivity_score'].fillna(1)
        
        return df
    
    def _test_basic_dollarization(self, df: pd.DataFrame) -> Dict:
        # Test basic relationship between volatility and USD pricing.
        # Prepare data for logistic regression
        analysis_df = df.dropna(subset=['likely_usd', 'volatility_30d'])
        
        if len(analysis_df) < 50:
            return {
                'avg_dollarization': 0.5,
                'marginal_effect': 0,
                'z_stat': 0,
                'p_value': 1,
                'significant': False
            }
        
        # Features for model
        X = analysis_df[['volatility_30d', 'connectivity_score', 'is_tradeable']]
        y = analysis_df['likely_usd']
        
        # Standardize features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Logistic regression
        try:
            model = LogisticRegression(random_state=42)
            model.fit(X_scaled, y)
            
            # Get predictions and probabilities
            predictions = model.predict(X_scaled)
            probabilities = model.predict_proba(X_scaled)[:, 1]
            
            # Calculate marginal effect at mean
            mean_prob = probabilities.mean()
            volatility_coef = model.coef_[0][0]
            marginal_effect = volatility_coef * mean_prob * (1 - mean_prob)
            
            # Statistical significance (simplified z-test)
            n = len(y)
            se = np.sqrt(mean_prob * (1 - mean_prob) / n)
            z_stat = marginal_effect / se if se > 0 else 0
            p_value = 2 * (1 - stats.norm.cdf(abs(z_stat)))
            
            return {
                'avg_dollarization': mean_prob,
                'marginal_effect': marginal_effect,
                'z_stat': z_stat,
                'p_value': p_value,
                'significant': p_value < 0.05
            }
            
        except Exception as e:
            logger.error(f"Basic dollarization model failed: {e}")
            return {
                'avg_dollarization': 0.5,
                'marginal_effect': 0,
                'z_stat': 0,
                'p_value': 1,
                'significant': False
            }
    
    def _test_volatility_threshold(self, df: pd.DataFrame) -> Dict:
        # Identify volatility threshold for currency switching.
        # Group by volatility bins and calculate USD usage
        df_clean = df.dropna(subset=['volatility_30d', 'likely_usd'])
        
        if len(df_clean) < 100:
            return {'threshold': 0.05, 'confidence': 0}
        
        # Create volatility percentile bins
        df_clean['vol_percentile'] = pd.qcut(
            df_clean['volatility_30d'], 
            q=20, 
            labels=False,
            duplicates='drop'
        )
        
        # Calculate USD usage by bin
        bin_stats = df_clean.groupby('vol_percentile').agg({
            'likely_usd': 'mean',
            'volatility_30d': 'mean'
        }).reset_index()
        
        # Find structural break point
        threshold = 0.05  # Default
        max_diff = 0
        
        for i in range(1, len(bin_stats) - 1):
            # USD usage before and after this point
            before = bin_stats.iloc[:i]['likely_usd'].mean()
            after = bin_stats.iloc[i:]['likely_usd'].mean()
            
            diff = after - before
            if diff > max_diff:
                max_diff = diff
                threshold = bin_stats.iloc[i]['volatility_30d']
        
        # Bootstrap confidence
        n_bootstrap = 100
        bootstrap_thresholds = []
        
        for _ in range(n_bootstrap):
            sample = df_clean.sample(frac=1, replace=True)
            # Simplified - just use median of high USD usage observations
            high_usd = sample[sample['likely_usd'] > 0.5]
            if len(high_usd) > 10:
                bootstrap_thresholds.append(high_usd['volatility_30d'].median())
        
        if bootstrap_thresholds:
            confidence = 1 - np.std(bootstrap_thresholds) / np.mean(bootstrap_thresholds)
        else:
            confidence = 0
        
        return {
            'threshold': threshold,
            'confidence': max(0, min(1, confidence))
        }
    
    def _test_network_effects(self, df: pd.DataFrame) -> Dict:
        # Test network effects and persistence in currency choice.
        # Prepare panel for network analysis
        df_panel = df.dropna(subset=['likely_usd', 'usd_usage_lag1'])
        
        if len(df_panel) < 100:
            return {
                'network_coefficient': 0,
                'persistence': 0,
                'spatial_correlation': 0
            }
        
        # Test 1: Persistence (state dependence)
        # Simple AR(1) model for USD usage
        X = sm.add_constant(df_panel['usd_usage_lag1'])
        y = df_panel['likely_usd']
        
        try:
            persistence_model = sm.Logit(y, X).fit(disp=0)
            persistence = persistence_model.params['usd_usage_lag1']
        except:
            persistence = 0
        
        # Test 2: Market-level network effects
        # Calculate market-level USD usage
        market_usd_usage = df_panel.groupby('market')['likely_usd'].mean()
        
        # Merge back to get "others' usage"
        df_panel['market_usd_rate'] = df_panel['market'].map(market_usd_usage)
        
        # Exclude own observation from market average
        df_panel['others_usd_usage'] = (
            (df_panel['market_usd_rate'] * df_panel.groupby('market').transform('count')['likely_usd'] - 
             df_panel['likely_usd']) /
            (df_panel.groupby('market').transform('count')['likely_usd'] - 1)
        )
        
        # Network effects model
        X_network = df_panel[['volatility_30d', 'others_usd_usage', 'connectivity_score']]
        X_network = sm.add_constant(X_network.fillna(0))
        
        try:
            network_model = sm.Logit(y, X_network).fit(disp=0)
            network_coefficient = network_model.params.get('others_usd_usage', 0)
        except:
            network_coefficient = 0
        
        # Test 3: Spatial correlation (simplified)
        # Markets in same zone should have similar USD usage
        zone_usd_usage = df_panel.groupby('market')['likely_usd'].mean()
        spatial_correlation = zone_usd_usage.corr(zone_usd_usage.shift(1))
        
        return {
            'network_coefficient': network_coefficient,
            'persistence': persistence,
            'spatial_correlation': spatial_correlation if not np.isnan(spatial_correlation) else 0
        }
    
    def _test_commodity_dollarization(self, 
                                    df: pd.DataFrame,
                                    data: CurrencySubstitutionData) -> Dict[str, float]:
        # Test dollarization patterns by commodity type.
        commodity_results = {}
        
        for commodity in df['commodity'].unique():
            commodity_df = df[df['commodity'] == commodity]
            
            if len(commodity_df) > 30:
                dollarization_rate = commodity_df['likely_usd'].mean()
                commodity_results[commodity] = dollarization_rate
        
        # Add category aggregates
        if not data.commodity_attributes.empty:
            # Tradeables vs non-tradeables
            tradeable_commodities = data.commodity_attributes[
                data.commodity_attributes['is_tradeable'] == 1
            ]['commodity'].tolist()
            
            tradeable_dollar = df[
                df['commodity'].isin(tradeable_commodities)
            ]['likely_usd'].mean()
            
            non_tradeable_dollar = df[
                ~df['commodity'].isin(tradeable_commodities)
            ]['likely_usd'].mean()
            
            commodity_results['_tradeable_avg'] = tradeable_dollar
            commodity_results['_non_tradeable_avg'] = non_tradeable_dollar
        
        return commodity_results
    
    def _test_zone_patterns(self, df: pd.DataFrame) -> Dict[str, float]:
        # Test dollarization patterns by currency zone.
        zone_results = {}
        
        # Add currency zone if not present
        if 'currency_zone' not in df.columns:
            df['currency_zone'] = df['market'].apply(self._infer_currency_zone)
        
        for zone in df['currency_zone'].unique():
            zone_df = df[df['currency_zone'] == zone]
            
            if len(zone_df) > 30:
                dollarization_rate = zone_df['likely_usd'].mean()
                zone_results[zone] = dollarization_rate
        
        return zone_results
    
    def _infer_currency_zone(self, market: str) -> str:
        # Infer currency zone from market name.
        houthi_markets = ['sanaa', 'saada', 'amran', 'hajjah', 'hodeidah']
        
        market_lower = market.lower()
        for houthi in houthi_markets:
            if houthi in market_lower:
                return 'houthi'
        
        return 'government'
    
    def _estimate_switching_costs(self, df: pd.DataFrame) -> float:
        # Estimate implied switching costs from persistence.
        # Look for markets that switched currency usage
        df_sorted = df.sort_values(['market', 'commodity', 'date'])
        
        # Identify switches
        df_sorted['currency_switch'] = (
            (df_sorted['likely_usd'] != df_sorted['usd_usage_lag1']) & 
            df_sorted['usd_usage_lag1'].notna()
        ).astype(int)
        
        # Calculate switch rate
        switch_rate = df_sorted['currency_switch'].mean()
        
        # Low switch rate implies high switching costs
        # Convert to cost metric (simplified)
        if switch_rate > 0:
            switching_cost = -np.log(switch_rate)  # Higher cost = lower switching
        else:
            switching_cost = 5  # Maximum cost if no switching observed
        
        return min(switching_cost, 5)  # Cap at 5
    
    def _insufficient_data_result(self) -> CurrencySubstitutionResults:
        # Return result when insufficient data.
        return CurrencySubstitutionResults(
            test_passed=False,
            confidence=0,
            test_statistic=0,
            p_value=1,
            effect_size=0,
            summary={'error': 'Insufficient data'},
            usd_pricing_probability=0.5,
            volatility_threshold=0.05,
            commodity_dollarization={},
            zone_dollarization={},
            switching_costs=0,
            network_effects=0,
            persistence_coefficient=0
        )
    
    def _calculate_confidence(self,
                            basic_results: Dict,
                            threshold_results: Dict,
                            network_results: Dict) -> float:
        # Calculate overall confidence in results.
        confidence = 0.5
        
        # Statistical significance
        if basic_results['p_value'] < 0.05:
            confidence += 0.2
        elif basic_results['p_value'] < 0.1:
            confidence += 0.1
        
        # Threshold identification quality
        confidence += threshold_results['confidence'] * 0.2
        
        # Network effects strength
        if abs(network_results['network_coefficient']) > 0.5:
            confidence += 0.1
        
        # Persistence strength
        if network_results['persistence'] > 0.3:
            confidence += 0.1
        
        return min(confidence, 0.95)
    
    def interpret_results(self, results: CurrencySubstitutionResults) -> PolicyInterpretation:
        # Interpret results for policy makers.
        key_insights = []
        
        # Main finding on dollarization level
        if results.usd_pricing_probability > 0.7:
            key_insights.append(
                f"High dollarization: {results.usd_pricing_probability*100:.0f}% of transactions use USD pricing"
            )
        elif results.usd_pricing_probability > 0.3:
            key_insights.append(
                f"Partial dollarization: {results.usd_pricing_probability*100:.0f}% USD pricing indicates dual currency system"
            )
        else:
            key_insights.append(
                "Low dollarization suggests YER remains dominant despite volatility"
            )
        
        # Volatility threshold insight
        key_insights.append(
            f"Currency substitution accelerates when volatility exceeds {results.volatility_threshold*100:.1f}% daily"
        )
        
        # Network effects
        if results.network_effects > 0.5:
            key_insights.append(
                "Strong network effects create self-reinforcing dollarization"
            )
        
        # Persistence
        if results.persistence_coefficient > 0.7:
            key_insights.append(
                "High persistence means currency choices are sticky - reversing dollarization is difficult"
            )
        
        # Zone patterns
        if results.zone_dollarization:
            max_zone = max(results.zone_dollarization, key=results.zone_dollarization.get)
            key_insights.append(
                f"{max_zone} zone shows highest dollarization at {results.zone_dollarization[max_zone]*100:.0f}%"
            )
        
        # Commodity patterns
        if '_tradeable_avg' in results.commodity_dollarization:
            tradeable_rate = results.commodity_dollarization['_tradeable_avg']
            non_tradeable_rate = results.commodity_dollarization['_non_tradeable_avg']
            
            if tradeable_rate > non_tradeable_rate * 1.5:
                key_insights.append(
                    "Tradeable goods show significantly higher USD pricing, facilitating cross-zone trade"
                )
        
        # Policy recommendations
        recommendations = []
        
        if results.usd_pricing_probability > 0.5:
            recommendations.extend([
                "Consider official dual currency system to reduce transaction costs",
                "Stabilize exchange rate to slow further dollarization",
                f"Target volatility below {results.volatility_threshold*100:.1f}% to prevent acceleration"
            ])
        
        if results.switching_costs > 3:
            recommendations.append(
                "High switching costs lock in currency choices - act before full dollarization"
            )
        
        if results.network_effects > 0.5:
            recommendations.append(
                "Coordinate currency policies across markets to leverage network effects"
            )
        
        # Evidence strength
        if results.confidence > 0.8:
            evidence_strength = "strong"
        elif results.confidence > 0.6:
            evidence_strength = "moderate"
        else:
            evidence_strength = "weak"
        
        return PolicyInterpretation(
            summary=f"H6 test shows {evidence_strength} evidence of currency substitution dynamics",
            key_insights=key_insights,
            recommendations=recommendations,
            confidence_level=results.confidence,
            evidence_strength=evidence_strength,
            policy_actions={
                'immediate': "Monitor and publish daily volatility metrics",
                'short_term': "Develop framework for managed dual currency system",
                'long_term': "Plan for eventual re-dollarization or currency unification"
            },
            caveats=[
                "USD pricing identification may have measurement error",
                "Network effects difficult to separate from common shocks",
                "Switching costs are implied, not directly observed"
            ],
            further_research=[
                "Survey traders on actual currency preferences and constraints",
                "Study micro-level determinants of currency choice",
                "Analyze welfare implications of partial dollarization"
            ]
        )
