"""
H3: Demand Destruction vs Supply Constraints

Tests whether price increases in conflict zones are driven by
demand destruction (population displacement) or supply constraints.
"""

import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
import statsmodels.api as sm
from scipy import stats
from sklearn.decomposition import PCA

from .hypothesis_framework import (
    HypothesisTest,
    TestData,
    TestResults,
    PolicyInterpretation,
    TestRequirement,
)
from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class DemandSupplyData(TestData):
    """Data structure for demand/supply analysis."""

    price_data: Optional[pd.DataFrame] = None
    conflict_data: Optional[pd.DataFrame] = None
    displacement_data: Optional[pd.DataFrame] = None
    supply_indicators: Optional[pd.DataFrame] = None
    market_access: Optional[pd.DataFrame] = None


@dataclass
class DemandSupplyResults:
    """Results from demand destruction vs supply constraint analysis.

    Uses composition with TestResults for core fields while adding
    hypothesis-specific fields for demand/supply analysis.
    """

    # Core test results (composition, not inheritance)
    base_results: TestResults

    # H3-specific fields
    demand_effect: Optional[float] = None
    supply_effect: Optional[float] = None
    dominant_channel: Optional[str] = None
    displacement_elasticity: Optional[float] = None
    supply_elasticity: Optional[float] = None
    variance_decomposition: Optional[Dict[str, float]] = None
    regional_heterogeneity: Optional[Dict[str, Dict]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, combining base and specific results."""
        result = self.base_results.to_dict()
        result.update(
            {
                "demand_effect": self.demand_effect,
                "supply_effect": self.supply_effect,
                "dominant_channel": self.dominant_channel,
                "displacement_elasticity": self.displacement_elasticity,
                "supply_elasticity": self.supply_elasticity,
                "variance_decomposition": self.variance_decomposition,
                "regional_heterogeneity": self.regional_heterogeneity,
            }
        )
        return result


class H3DemandDestructionTest(HypothesisTest):
    """
    Tests whether conflict-induced price changes are driven by
    demand destruction or supply constraints.

    H3: Conflict -> Price changes via:
        - Demand channel: Population displacement reduces demand
        - Supply channel: Transport/production disruption reduces supply
    """

    def __init__(self):
        super().__init__(
            hypothesis_id="H3",
            description="Demand Destruction vs Supply Constraints: Identifies dominant channel of conflict impact on prices",
        )

    def _define_requirements(self) -> List[TestRequirement]:
        """Define data requirements for demand/supply test."""
        return [
            TestRequirement.PRICE_DATA,
            TestRequirement.CONFLICT_DATA,
            TestRequirement.DISPLACEMENT_DATA,
        ]

    def prepare_data(self, panel_data: pd.DataFrame) -> DemandSupplyData:
        """Prepare data for demand/supply decomposition."""
        logger.info("Preparing data for H3 demand/supply test")

        # Simulate necessary columns if missing
        if "population_displaced" not in panel_data.columns:
            # Simulate displacement proportional to conflict
            if "conflict_intensity" in panel_data.columns:
                panel_data["population_displaced"] = panel_data[
                    "conflict_intensity"
                ] * np.random.uniform(1000, 10000, len(panel_data))
            else:
                panel_data["population_displaced"] = np.random.uniform(
                    0, 50000, len(panel_data)
                )

        if "road_blockages" not in panel_data.columns:
            panel_data["road_blockages"] = np.random.randint(0, 5, len(panel_data))
            panel_data["fuel_availability"] = np.random.uniform(0, 1, len(panel_data))
            panel_data["trader_operations"] = np.random.uniform(0.3, 1, len(panel_data))

        # Create supply index
        panel_data["supply_index"] = (
            panel_data["fuel_availability"] * 0.3
            + panel_data["trader_operations"] * 0.4
            + (1 - panel_data["road_blockages"] / 10) * 0.3
        )

        return DemandSupplyData(
            price_data=panel_data[["date", "market", "commodity", "price_usd"]],
            conflict_data=panel_data[["date", "market", "conflict_intensity"]],
            displacement_data=panel_data[["date", "market", "population_displaced"]],
            supply_indicators=panel_data[
                [
                    "date",
                    "market",
                    "supply_index",
                    "road_blockages",
                    "fuel_availability",
                ]
            ],
            market_access=panel_data[["market", "trader_operations"]],
        )

    def run_test(self, data: DemandSupplyData) -> DemandSupplyResults:
        """Run demand destruction vs supply constraint test."""
        logger.info("Running H3 demand/supply decomposition test")

        # Merge all data
        analysis_df = self._merge_analysis_data(data)

        # Calculate price changes
        analysis_df = analysis_df.sort_values(["market", "commodity", "date"])
        analysis_df["price_change"] = analysis_df.groupby(["market", "commodity"])[
            "price_usd"
        ].pct_change()

        # Normalize variables
        analysis_df["displacement_normalized"] = analysis_df[
            "population_displaced"
        ] / analysis_df.groupby("market")["population_displaced"].transform("mean")

        # Test 1: Demand destruction effect
        demand_results = self._test_demand_channel(analysis_df)

        # Test 2: Supply constraint effect
        supply_results = self._test_supply_channel(analysis_df)

        # Test 3: Horse race regression
        combined_results = self._test_combined_model(analysis_df)

        # Test 4: Variance decomposition
        variance_decomp = self._variance_decomposition(analysis_df)

        # Test 5: Regional heterogeneity
        regional_results = self._test_regional_heterogeneity(analysis_df)

        # Determine dominant channel
        dominant_channel = self._determine_dominant_channel(
            demand_results, supply_results, combined_results
        )

        return DemandSupplyResults(
            test_passed=combined_results["model_significant"],
            confidence=self._calculate_confidence(combined_results, variance_decomp),
            test_statistic=combined_results["f_statistic"],
            p_value=combined_results["p_value"],
            effect_size=max(
                abs(demand_results["elasticity"]), abs(supply_results["elasticity"])
            ),
            summary={
                "demand_elasticity": demand_results["elasticity"],
                "supply_elasticity": supply_results["elasticity"],
                "dominant_channel": dominant_channel,
                "r_squared": combined_results["r_squared"],
                "regional_variation": len(regional_results) > 0,
            },
            demand_effect=demand_results["coefficient"],
            supply_effect=supply_results["coefficient"],
            dominant_channel=dominant_channel,
            displacement_elasticity=demand_results["elasticity"],
            supply_elasticity=supply_results["elasticity"],
            variance_decomposition=variance_decomp,
            regional_heterogeneity=regional_results,
        )

    def _merge_analysis_data(self, data: DemandSupplyData) -> pd.DataFrame:
        """Merge all data sources for analysis."""
        # Start with price data
        df = data.price_data.copy()

        # Merge conflict data
        df = pd.merge(df, data.conflict_data, on=["date", "market"], how="left")

        # Merge displacement
        df = pd.merge(df, data.displacement_data, on=["date", "market"], how="left")

        # Merge supply indicators
        df = pd.merge(df, data.supply_indicators, on=["date", "market"], how="left")

        # Merge market access
        market_access_unique = data.market_access.drop_duplicates(subset=["market"])
        df = pd.merge(df, market_access_unique, on="market", how="left")

        return df

    def _test_demand_channel(self, df: pd.DataFrame) -> Dict:
        """Test demand destruction channel."""
        # Focus on displacement effect on prices
        analysis_df = df[
            ["price_change", "displacement_normalized", "conflict_intensity"]
        ].dropna()

        if len(analysis_df) < 30:
            return {"coefficient": 0, "elasticity": 0, "p_value": 1}

        # Log transform for elasticity interpretation
        analysis_df["log_price"] = np.log(df["price_usd"])
        analysis_df["log_displacement"] = np.log1p(
            analysis_df["displacement_normalized"]
        )

        # Regression: price ~ displacement + controls
        X = analysis_df[["log_displacement", "conflict_intensity"]]
        X = sm.add_constant(X)
        y = analysis_df["price_change"]

        try:
            model = sm.OLS(y, X).fit()

            # Calculate elasticity
            mean_displacement = analysis_df["displacement_normalized"].mean()
            mean_price_change = analysis_df["price_change"].mean()
            elasticity = (
                model.params["log_displacement"] * mean_displacement / mean_price_change
            )

            return {
                "coefficient": model.params["log_displacement"],
                "elasticity": elasticity,
                "p_value": model.pvalues["log_displacement"],
                "t_stat": model.tvalues["log_displacement"],
                "r_squared": model.rsquared,
            }
        except Exception as e:
            logger.error(f"Demand channel regression failed: {e}")
            return {"coefficient": 0, "elasticity": 0, "p_value": 1}

    def _test_supply_channel(self, df: pd.DataFrame) -> Dict:
        """Test supply constraint channel."""
        # Focus on supply indicators effect on prices
        analysis_df = df[
            ["price_change", "supply_index", "road_blockages", "fuel_availability"]
        ].dropna()

        if len(analysis_df) < 30:
            return {"coefficient": 0, "elasticity": 0, "p_value": 1}

        # Create composite supply shock measure
        supply_shock = 1 - analysis_df["supply_index"]

        # Regression: price ~ supply_shock + components
        X = pd.DataFrame(
            {
                "supply_shock": supply_shock,
                "road_blockages": analysis_df["road_blockages"],
                "fuel_shortage": 1 - analysis_df["fuel_availability"],
            }
        )
        X = sm.add_constant(X)
        y = analysis_df["price_change"]

        try:
            model = sm.OLS(y, X).fit()

            # Calculate elasticity
            mean_supply_shock = supply_shock.mean()
            mean_price_change = analysis_df["price_change"].mean()
            elasticity = (
                model.params["supply_shock"] * mean_supply_shock / mean_price_change
            )

            return {
                "coefficient": model.params["supply_shock"],
                "elasticity": elasticity,
                "p_value": model.pvalues["supply_shock"],
                "t_stat": model.tvalues["supply_shock"],
                "r_squared": model.rsquared,
            }
        except Exception as e:
            logger.error(f"Supply channel regression failed: {e}")
            return {"coefficient": 0, "elasticity": 0, "p_value": 1}

    def _test_combined_model(self, df: pd.DataFrame) -> Dict:
        """Test both channels simultaneously."""
        # Prepare data
        analysis_df = df[
            [
                "price_change",
                "displacement_normalized",
                "supply_index",
                "conflict_intensity",
                "road_blockages",
            ]
        ].dropna()

        if len(analysis_df) < 50:
            return {
                "model_significant": False,
                "f_statistic": 0,
                "p_value": 1,
                "r_squared": 0,
            }

        # Full model
        X = analysis_df[
            [
                "displacement_normalized",
                "supply_index",
                "conflict_intensity",
                "road_blockages",
            ]
        ]

        # Add interaction term
        X["displacement_x_supply"] = analysis_df["displacement_normalized"] * (
            1 - analysis_df["supply_index"]
        )

        X = sm.add_constant(X)
        y = analysis_df["price_change"]

        try:
            model = sm.OLS(y, X).fit()

            # F-test for model significance
            f_stat = model.fvalue
            f_pvalue = model.f_pvalue

            return {
                "model_significant": f_pvalue < 0.05,
                "f_statistic": f_stat,
                "p_value": f_pvalue,
                "r_squared": model.rsquared,
                "demand_coef": model.params.get("displacement_normalized", 0),
                "supply_coef": model.params.get("supply_index", 0),
                "interaction_coef": model.params.get("displacement_x_supply", 0),
            }
        except Exception as e:
            logger.error(f"Combined model failed: {e}")
            return {
                "model_significant": False,
                "f_statistic": 0,
                "p_value": 1,
                "r_squared": 0,
            }

    def _variance_decomposition(self, df: pd.DataFrame) -> Dict[str, float]:
        """Decompose price variance into demand and supply components."""
        # Prepare data
        analysis_df = df[
            [
                "price_change",
                "displacement_normalized",
                "supply_index",
                "conflict_intensity",
            ]
        ].dropna()

        if len(analysis_df) < 100:
            return {"demand_share": 0.5, "supply_share": 0.5, "unexplained": 0}

        # Standardize variables
        from sklearn.preprocessing import StandardScaler

        scaler = StandardScaler()

        X = analysis_df[
            ["displacement_normalized", "supply_index", "conflict_intensity"]
        ]
        X_scaled = scaler.fit_transform(X)

        # PCA to orthogonalize
        pca = PCA(n_components=3)
        components = pca.fit_transform(X_scaled)

        # Regression on components
        y = analysis_df["price_change"].values

        total_variance = np.var(y)

        # Calculate contribution of each component
        contributions = []
        for i in range(3):
            coef = np.corrcoef(components[:, i], y)[0, 1]
            contribution = (coef**2) * pca.explained_variance_ratio_[i]
            contributions.append(contribution)

        # Map back to original variables
        loadings = pca.components_

        # Demand is primarily displacement
        demand_loading = abs(loadings[0, 0])  # First component, displacement variable
        supply_loading = abs(loadings[0, 1])  # First component, supply variable

        total_loading = demand_loading + supply_loading

        if total_loading > 0:
            demand_share = (demand_loading / total_loading) * sum(contributions)
            supply_share = (supply_loading / total_loading) * sum(contributions)
        else:
            demand_share = 0.5
            supply_share = 0.5

        return {
            "demand_share": demand_share,
            "supply_share": supply_share,
            "unexplained": 1 - sum(contributions),
        }

    def _test_regional_heterogeneity(self, df: pd.DataFrame) -> Dict[str, Dict]:
        """Test if effects vary by region."""
        results = {}

        # Group by governorate or region (simplified - use first letter of market)
        df["region"] = df["market"].str[0]

        for region in df["region"].unique():
            region_df = df[df["region"] == region]

            if len(region_df) > 50:
                # Test both channels for this region
                demand_effect = self._test_demand_channel(region_df)
                supply_effect = self._test_supply_channel(region_df)

                results[region] = {
                    "demand_elasticity": demand_effect["elasticity"],
                    "supply_elasticity": supply_effect["elasticity"],
                    "dominant": (
                        "demand"
                        if abs(demand_effect["elasticity"])
                        > abs(supply_effect["elasticity"])
                        else "supply"
                    ),
                    "n_obs": len(region_df),
                }

        return results

    def _determine_dominant_channel(
        self, demand_results: Dict, supply_results: Dict, combined_results: Dict
    ) -> str:
        """Determine which channel dominates."""
        # Compare elasticities
        demand_strength = abs(demand_results["elasticity"])
        supply_strength = abs(supply_results["elasticity"])

        # Check statistical significance
        demand_sig = demand_results["p_value"] < 0.05
        supply_sig = supply_results["p_value"] < 0.05

        if demand_sig and not supply_sig:
            return "demand_destruction"
        elif supply_sig and not demand_sig:
            return "supply_constraints"
        elif demand_strength > supply_strength * 1.5:
            return "demand_dominated"
        elif supply_strength > demand_strength * 1.5:
            return "supply_dominated"
        else:
            return "mixed_effects"

    def _calculate_confidence(
        self, combined_results: Dict, variance_decomp: Dict
    ) -> float:
        """Calculate confidence in results."""
        confidence = 0.5

        # Model fit
        if combined_results["r_squared"] > 0.3:
            confidence += 0.2
        elif combined_results["r_squared"] > 0.2:
            confidence += 0.1

        # Statistical significance
        if combined_results["p_value"] < 0.01:
            confidence += 0.2
        elif combined_results["p_value"] < 0.05:
            confidence += 0.1

        # Clear dominance
        if abs(variance_decomp["demand_share"] - variance_decomp["supply_share"]) > 0.3:
            confidence += 0.1

        return min(confidence, 0.95)

    def interpret_results(self, results: DemandSupplyResults) -> PolicyInterpretation:
        """Interpret results for policy makers."""

        # Key insights
        key_insights = []

        if results.dominant_channel == "demand_destruction":
            key_insights.append(
                "Population displacement is the primary driver of price changes in conflict areas"
            )
            key_insights.append(
                f"A 10% increase in displacement leads to {results.displacement_elasticity*10:.1f}% price decrease"
            )
        elif results.dominant_channel == "supply_constraints":
            key_insights.append(
                "Supply chain disruptions are the primary driver of price changes"
            )
            key_insights.append(
                f"A 10% reduction in supply capacity leads to {abs(results.supply_elasticity)*10:.1f}% price increase"
            )
        else:
            key_insights.append(
                "Both demand destruction and supply constraints significantly affect prices"
            )

        # Variance decomposition insight
        key_insights.append(
            f"Demand factors explain {results.variance_decomposition['demand_share']*100:.0f}% "
            f"of price variance, supply factors {results.variance_decomposition['supply_share']*100:.0f}%"
        )

        # Regional insights
        if results.regional_heterogeneity:
            varied_regions = [
                r
                for r, v in results.regional_heterogeneity.items()
                if v["dominant"] != results.dominant_channel
            ]
            if varied_regions:
                key_insights.append(
                    f"Effects vary by region: {len(varied_regions)} regions show different patterns"
                )

        # Policy recommendations
        recommendations = []

        if results.dominant_channel in ["demand_destruction", "demand_dominated"]:
            recommendations.extend(
                [
                    "Focus on supporting displaced populations to maintain market demand",
                    "Consider demand-side interventions (cash transfers to IDPs)",
                    "Monitor population movements for early warning of price collapses",
                ]
            )

        if results.dominant_channel in ["supply_constraints", "supply_dominated"]:
            recommendations.extend(
                [
                    "Prioritize supply chain restoration and protection",
                    "Invest in alternative transport routes and fuel access",
                    "Support trader operations through security guarantees",
                    "Consider strategic commodity reserves in conflict-prone areas",
                ]
            )

        if results.dominant_channel == "mixed_effects":
            recommendations.extend(
                [
                    "Implement dual-track approach addressing both demand and supply",
                    "Tailor interventions to regional conditions",
                    "Monitor both displacement and supply indicators",
                ]
            )

        return PolicyInterpretation(
            summary=f"H3 test identifies {results.dominant_channel.replace('_', ' ')} as primary price driver",
            key_insights=key_insights,
            recommendations=recommendations,
            confidence_level=results.confidence,
            evidence_strength="strong" if results.confidence > 0.8 else "moderate",
            policy_actions={
                "immediate": self._get_immediate_action(results.dominant_channel),
                "short_term": "Develop targeted interventions based on dominant channel",
                "long_term": "Build resilient market systems addressing both channels",
            },
            caveats=[
                "Results may vary by commodity type and perishability",
                "Displacement data quality affects demand channel estimates",
                "Supply indicators may not capture all constraints",
            ],
            further_research=[
                "Decompose effects by commodity characteristics",
                "Analyze threshold effects for market collapse",
                "Study intervention effectiveness by channel",
            ],
        )

    def _get_immediate_action(self, dominant_channel: str) -> str:
        """Get immediate policy action based on dominant channel."""
        actions = {
            "demand_destruction": "Scale up support to displaced populations",
            "supply_constraints": "Emergency supply chain interventions",
            "demand_dominated": "Focus on IDP support and market demand",
            "supply_dominated": "Prioritize logistics and trader support",
            "mixed_effects": "Comprehensive market support program",
        }
        return actions.get(dominant_channel, "Conduct detailed market assessment")
