"""
H1: Exchange Rate Mechanism Hypothesis Implementation

This test examines the relationship between price differentials and currency
conversion, specifically testing whether price patterns change when properly
converting from local currency (YER) to USD using appropriate exchange rates.

IMPORTANT: This test was revised after recognizing initial methodological errors
in currency conversion. Previous versions may have contained flawed assumptions.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from decimal import Decimal
import statsmodels.api as sm
from linearmodels.panel import PanelOLS
from scipy import stats

from .hypothesis_framework import (
    HypothesisTest, TestData, TestResults, PolicyInterpretation,
    HypothesisOutcome, TestRequirement, HypothesisRegistry
)
from .pre_registration import requires_preregistration, track_degrees_of_freedom
from src.core.domain.market.currency_zones import CurrencyZone, CurrencyZoneService
from src.core.utils.logging import get_logger


logger = get_logger(__name__)


class H1ExchangeRateMechanism(HypothesisTest):
    """
    Test relationship between price differentials and currency conversion.
    
    Null hypothesis: Price differentials between zones persist after 
    controlling for exchange rate differences.
    
    Alternative hypothesis: Price differentials change significantly
    when converting from local to common currency.
    """
    
    def __init__(self):
        super().__init__(
            hypothesis_id="H1",
            description="Test price differentials with currency conversion"
        )
        self.zone_service = CurrencyZoneService()
        self.dof_tracker = track_degrees_of_freedom("H1")
        
    def _define_requirements(self) -> List[TestRequirement]:
        """H1 requires price and exchange rate data."""
        return [
            TestRequirement.PRICE_DATA,
            TestRequirement.EXCHANGE_RATES
        ]
    
    @requires_preregistration
    def prepare_data(self, raw_data: Dict[str, pd.DataFrame]) -> TestData:
        """Prepare data for H1 testing."""
        logger.info("Preparing data for H1 Exchange Rate Mechanism test")
        
        # Extract price data
        price_df = raw_data.get('prices', pd.DataFrame())
        exchange_df = raw_data.get('exchange_rates', pd.DataFrame())
        
        # Ensure we have currency zone information
        if 'currency_zone' not in price_df.columns:
            # Map governorates to zones
            price_df['currency_zone'] = price_df['governorate'].map(
                self._get_governorate_zone_mapping()
            )
        
        # Create panel data structure
        panel_data = self._create_panel_data(price_df)
        
        # Prepare exchange rate data
        exchange_rates = self._prepare_exchange_rates(exchange_df)
        
        return TestData(
            panel_data=panel_data,
            exchange_rates=exchange_rates,
            metadata={
                'n_markets': price_df['market_id'].nunique(),
                'n_observations': len(price_df),
                'date_range': (price_df['date'].min(), price_df['date'].max())
            }
        )
    
    def run_test(self, data: TestData) -> TestResults:
        """Execute the H1 hypothesis test."""
        logger.info("Running H1 Exchange Rate Mechanism test")
        
        # Calculate statistical power first
        power = self._calculate_statistical_power(data)
        if power < 0.80:
            logger.warning(f"Low statistical power: {power:.2f}. Results may be unreliable.")
        
        # Extract dataframe from panel data
        df = data.panel_data.to_dataframe()
        
        # Step 1: Test price differentials in YER
        yer_differential_test = self._test_price_differential_yer(df)
        
        # Step 2: Convert to USD and test price differentials
        df_with_usd = self._add_usd_prices(df, data.exchange_rates)
        usd_differential_test = self._test_price_differential_usd(df_with_usd)
        
        # Step 3: Test whether exchange rate is associated with differentials
        exchange_association_test = self._test_exchange_rate_association(df_with_usd)
        
        # Run specification curve analysis
        spec_curve_results = self._run_specification_curve_analysis(df_with_usd)
        
        # Determine outcome based on pre-registered decision rule
        p_value = exchange_association_test['p_value']
        if power < 0.80:
            outcome = HypothesisOutcome.INSUFFICIENT_POWER
        elif p_value < 0.05:
            outcome = HypothesisOutcome.NULL_REJECTED
        else:
            outcome = HypothesisOutcome.FAIL_TO_REJECT_NULL
        
        # Calculate effect size and confidence interval
        effect_size = exchange_association_test.get('effect_size', 0.0)
        ci_lower, ci_upper = self._calculate_confidence_interval(
            effect_size, 
            exchange_association_test.get('se', 0.1)
        )
        
        # Calculate minimum detectable effect
        mde = self._calculate_minimum_detectable_effect(data)
        
        # Log this analysis attempt
        self.bias_detector.log_analysis(
            hypothesis_id="H1",
            specification={'primary_analysis': True},
            p_value=p_value,
            effect_size=effect_size,
            reported=True  # This is the primary analysis
        )
        
        # Check for bias patterns
        bias_check = self.bias_detector.check_p_hacking("H1")
        
        return TestResults(
            hypothesis_id="H1",
            outcome=outcome,
            test_statistic=exchange_association_test['test_statistic'],
            p_value=p_value,
            alpha=0.05,  # Significance level used
            effect_size=effect_size,
            confidence_interval=(ci_lower, ci_upper),
            ci_level=0.95,
            statistical_power=power,
            minimum_detectable_effect=mde,
            n_observations=len(df),
            degrees_of_freedom=exchange_association_test.get('df', None),
            diagnostic_tests={
                'yer_differential_t': yer_differential_test['t_statistic'],
                'usd_differential_t': usd_differential_test['t_statistic'],
                'model_r2': exchange_association_test['r_squared'],
                'durbin_watson': exchange_association_test.get('durbin_watson', None)
            },
            detailed_results={
                'yer_differential': yer_differential_test,
                'usd_differential': usd_differential_test,
                'exchange_association': exchange_association_test
            },
            specification_curve_results=spec_curve_results,
            bias_detection_results=bias_check
        )
    
    def interpret_results(self, results: TestResults) -> PolicyInterpretation:
        """Interpret H1 results for policy makers."""
        
        if results.outcome == HypothesisOutcome.NULL_REJECTED:
            summary = (
                "Exchange rate differences significantly affect price comparisons. "
                "Currency conversion is essential for accurate market analysis. "
                "Price patterns change when properly accounting for exchange rates."
            )
            
            implications = [
                "Currency fragmentation complicates price comparisons",
                "Aid calculations must account for appropriate exchange rates",
                "Market integration analysis requires currency standardization"
            ]
            
            recommendations = [
                "Implement consistent currency conversion protocols",
                "Use appropriate exchange rates for each territorial zone",
                "Monitor exchange rate assumptions in ongoing analysis",
                "Cross-validate findings with alternative rate sources"
            ]
            
        elif results.outcome == HypothesisOutcome.FAIL_TO_REJECT_NULL:
            summary = (
                "Exchange rate differences do not significantly explain price patterns. "
                "Other factors may be more important for understanding price differentials."
            )
            
            implications = [
                "Currency fragmentation may not be the primary driver",
                "Alternative explanations should be investigated",
                "Current price patterns require different interpretation"
            ]
            
            recommendations = [
                "Investigate alternative hypotheses (H2-H10)",
                "Examine non-currency factors affecting prices",
                "Maintain cautious approach to currency-based conclusions"
            ]
            
        else:
            summary = (
                "Analysis insufficient to determine exchange rate effects. "
                "Results inconclusive due to data limitations or low statistical power."
            )
            
            implications = [
                "Current data insufficient for reliable conclusions",
                "Additional data collection may be needed",
                "Uncertainty remains about currency effects"
            ]
            
            recommendations = [
                "Collect additional exchange rate data",
                "Improve data quality before drawing conclusions", 
                "Report findings as inconclusive pending better data"
            ]
        
        confidence_statement = f"These findings have {results.confidence_level:.0%} confidence based on the analysis."
        
        caveats = [
            "Results assume exchange rate data accurately reflects market rates",
            "Black market premiums may not be fully captured",
            "Temporal dynamics of currency fragmentation need monitoring"
        ]
        
        # Add data quality notes
        data_quality_notes = []
        if results.n_observations < 1000:
            data_quality_notes.append(f"Limited sample size: {results.n_observations} observations")
        if results.statistical_power < 0.80:
            data_quality_notes.append(f"Low statistical power: {results.statistical_power:.2f}")
        if results.bias_detection_results and results.bias_detection_results.get('p_hacking_risk'):
            data_quality_notes.append("Potential bias detected in analysis history")
            
        return PolicyInterpretation(
            statistical_summary=summary,
            findings=implications,
            considerations=recommendations,
            uncertainty_statement=confidence_statement,
            limitations=caveats,
            data_quality_notes=data_quality_notes,
            visualizations={
                'price_comparison': self._create_price_comparison_viz(results),
                'specification_curve': self._create_specification_curve_viz(results)
            }
        )
    
    def _test_price_differential_yer(self, df: pd.DataFrame) -> Dict:
        """Test price differentials between zones in YER."""
        # Filter to cross-zone comparisons
        houthi_prices = df[df['currency_zone'] == 'houthi']['price_yer'].values
        gov_prices = df[df['currency_zone'] == 'government']['price_yer'].values
        
        # Log transform for percentage interpretation
        log_houthi = np.log(houthi_prices)
        log_gov = np.log(gov_prices)
        
        # T-test
        t_stat, p_value = stats.ttest_ind(log_houthi, log_gov)
        
        # Calculate means
        mean_diff_pct = (np.exp(log_houthi.mean()) - np.exp(log_gov.mean())) / np.exp(log_gov.mean()) * 100
        
        return {
            'significant_difference': p_value < 0.05,
            'direction': 'houthi_lower' if t_stat < 0 else 'houthi_higher',
            't_statistic': t_stat,
            'p_value': p_value,
            'mean_diff_pct': mean_diff_pct,
            'houthi_mean_yer': np.exp(log_houthi.mean()),
            'gov_mean_yer': np.exp(log_gov.mean()),
            'n_houthi': len(houthi_prices),
            'n_gov': len(gov_prices)
        }
    
    def _test_price_differential_usd(self, df: pd.DataFrame) -> Dict:
        """Test price differentials between zones in USD."""
        # Filter to cross-zone comparisons
        houthi_prices = df[df['currency_zone'] == 'houthi']['price_usd'].values
        gov_prices = df[df['currency_zone'] == 'government']['price_usd'].values
        
        # Log transform
        log_houthi = np.log(houthi_prices)
        log_gov = np.log(gov_prices)
        
        # T-test
        t_stat, p_value = stats.ttest_ind(log_houthi, log_gov)
        
        # Calculate conflict premium
        conflict_premium_pct = (np.exp(log_houthi.mean()) - np.exp(log_gov.mean())) / np.exp(log_gov.mean()) * 100
        
        return {
            'significant_difference': p_value < 0.05,
            'direction': 'houthi_lower' if t_stat < 0 else 'houthi_higher',
            't_statistic': t_stat,
            'p_value': p_value,
            'mean_diff_pct': conflict_premium_pct,
            'houthi_mean_usd': np.exp(log_houthi.mean()),
            'gov_mean_usd': np.exp(log_gov.mean()),
            'n_houthi': len(houthi_prices),
            'n_gov': len(gov_prices)
        }
    
    def _test_exchange_rate_association(self, df: pd.DataFrame) -> Dict:
        """Test association between exchange rates and price differentials."""
        # Prepare panel data for regression
        df['log_price_yer'] = np.log(df['price_yer'])
        df['log_exchange_rate'] = np.log(df['exchange_rate'])
        df['is_houthi'] = (df['currency_zone'] == 'houthi').astype(int)
        df['is_contested'] = (df['currency_zone'] == 'contested').astype(int)
        
        # Set up panel structure
        df = df.set_index(['market_id', 'date'])
        
        # Model 1: Zone effects only
        exog_zone = sm.add_constant(df[['is_houthi', 'is_contested']])
        model_zone = PanelOLS(df['log_price_yer'], exog_zone, entity_effects=True, time_effects=True)
        results_zone = model_zone.fit(cov_type='clustered', cluster_entity=True)
        
        # Model 2: Zone effects + exchange rate
        exog_full = sm.add_constant(df[['is_houthi', 'is_contested', 'log_exchange_rate']])
        model_full = PanelOLS(df['log_price_yer'], exog_full, entity_effects=True, time_effects=True)
        results_full = model_full.fit(cov_type='clustered', cluster_entity=True)
        
        # Test significance of exchange rate coefficient
        exchange_rate_significant = results_full.pvalues['log_exchange_rate'] < 0.05
        zone_effect_remains = results_full.pvalues['is_houthi'] < 0.05
        
        # F-test for nested models
        rss_restricted = results_zone.resid_ss
        rss_unrestricted = results_full.resid_ss
        df_restricted = results_zone.df_resid
        df_unrestricted = results_full.df_resid
        
        f_stat = ((rss_restricted - rss_unrestricted) / (df_restricted - df_unrestricted)) / (rss_unrestricted / df_unrestricted)
        f_pvalue = 1 - stats.f.cdf(f_stat, df_restricted - df_unrestricted, df_unrestricted)
        
        return {
            'exchange_rate_significant': exchange_rate_significant,
            'zone_effect_remains': zone_effect_remains,
            'zone_coef_before': results_zone.params['is_houthi'],
            'zone_pvalue_before': results_zone.pvalues['is_houthi'],
            'zone_coef_after': results_full.params['is_houthi'],
            'zone_pvalue_after': results_full.pvalues['is_houthi'],
            'exchange_coef': results_full.params['log_exchange_rate'],
            'exchange_pvalue': results_full.pvalues['log_exchange_rate'],
            'effect_size': results_full.params['log_exchange_rate'],  # Exchange rate coefficient as effect size
            'se': results_full.std_errors['log_exchange_rate'],
            'r_squared': results_full.rsquared,
            'test_statistic': f_stat,
            'p_value': f_pvalue,
            'durbin_watson': None  # Would need to calculate if time series properties matter
        }
    
    def _add_usd_prices(self, df: pd.DataFrame, exchange_rates: pd.DataFrame) -> pd.DataFrame:
        """Add USD prices using zone-specific exchange rates."""
        # Merge exchange rates
        df = df.merge(
            exchange_rates[['date', 'currency_zone', 'rate']],
            on=['date', 'currency_zone'],
            how='left'
        )
        
        # Rename for clarity
        df.rename(columns={'rate': 'exchange_rate'}, inplace=True)
        
        # Calculate USD prices
        df['price_usd'] = df['price_yer'] / df['exchange_rate']
        
        return df
    
    def _calculate_effect_size(self, df: pd.DataFrame) -> float:
        """Calculate effect size as the ratio of price differentials."""
        # YER differential (Houthi/Government)
        yer_ratio = (
            df[df['currency_zone'] == 'houthi']['price_yer'].mean() /
            df[df['currency_zone'] == 'government']['price_yer'].mean()
        )
        
        # USD differential (Houthi/Government)
        usd_ratio = (
            df[df['currency_zone'] == 'houthi']['price_usd'].mean() /
            df[df['currency_zone'] == 'government']['price_usd'].mean()
        )
        
        # Effect size is the change in ratio between YER and USD comparisons
        effect_size = abs(usd_ratio - yer_ratio)
        
        return effect_size
    
    def _get_governorate_zone_mapping(self) -> Dict[str, str]:
        """Map governorates to currency zones."""
        return {
            # Houthi-controlled
            "Sana'a": "houthi",
            "Sa'ada": "houthi",
            "Amran": "houthi",
            "Hajjah": "houthi",
            "Al Mahwit": "houthi",
            "Dhamar": "houthi",
            "Raymah": "houthi",
            "Ibb": "houthi",
            
            # Government-controlled
            "Aden": "government",
            "Lahj": "government",
            "Abyan": "government",
            "Al Dhale": "government",
            "Shabwah": "government",
            "Hadramaut": "government",
            "Al Mahrah": "government",
            "Socotra": "government",
            
            # Contested
            "Taiz": "contested",
            "Al Bayda": "contested",
            "Marib": "contested",
            "Al Jawf": "contested",
            "Al Hudaydah": "contested"
        }
    
    def _create_panel_data(self, price_df: pd.DataFrame) -> 'PanelData':
        """Create PanelData object from price dataframe."""
        from src.core.domain.market.entities import PanelData, PriceObservation, Market
        from src.core.domain.market.value_objects import (
            MarketId, Price, Commodity, Currency
        )
        
        # Convert dataframe to PanelData structure
        observations = []
        markets = []
        market_ids = set()
        
        for _, row in price_df.iterrows():
            # Create market if not seen
            market_id = MarketId(value=row.get('market_id'))
            if market_id not in market_ids:
                market = Market(
                    id=market_id,
                    name=row.get('market_name', f"Market_{market_id.value}"),
                    governorate=row.get('governorate'),
                    district=row.get('district')
                )
                markets.append(market)
                market_ids.add(market_id)
            
            # Create price observation
            obs = PriceObservation(
                market_id=market_id,
                commodity=Commodity(name=row.get('commodity', 'wheat')),
                price=Price(
                    amount=Decimal(str(row.get('price_yer', 0))),
                    currency=Currency.YER,
                    unit=row.get('unit', 'kg')
                ),
                observed_date=pd.to_datetime(row.get('date'))
            )
            observations.append(obs)
        
        return PanelData(
            observations=observations,
            markets=markets,
            start_date=price_df['date'].min(),
            end_date=price_df['date'].max()
        )
    
    def _prepare_exchange_rates(self, exchange_df: pd.DataFrame) -> pd.DataFrame:
        """Prepare exchange rate data with zone information."""
        if exchange_df.empty:
            raise ValueError(
                "Exchange rate data is required for H1 hypothesis test. "
                "Cannot proceed without actual exchange rate data."
            )
        
        # Validate required columns
        required_cols = ['date', 'currency_zone', 'rate']
        missing_cols = set(required_cols) - set(exchange_df.columns)
        if missing_cols:
            raise ValueError(
                f"Exchange rate data missing required columns: {missing_cols}"
            )
        
        return exchange_df
    
    def _calculate_statistical_power(self, data: TestData) -> float:
        """Calculate post-hoc statistical power for the test."""
        # Extract sample sizes
        df = data.panel_data.to_dataframe()
        n_houthi = len(df[df['currency_zone'] == 'houthi'])
        n_gov = len(df[df['currency_zone'] == 'government'])
        
        # Use harmonic mean for unequal groups
        n_harmonic = 2 * n_houthi * n_gov / (n_houthi + n_gov)
        
        # Simplified power calculation (would use statsmodels power analysis in practice)
        # Assumes medium effect size (d=0.5) and alpha=0.05
        from statsmodels.stats.power import ttest_power
        power = ttest_power(0.5, n_harmonic, 0.05, alternative='two-sided')
        
        return power
    
    def _calculate_minimum_detectable_effect(self, data: TestData, power: float = 0.80) -> float:
        """Calculate minimum detectable effect size at given power."""
        df = data.panel_data.to_dataframe()
        n_houthi = len(df[df['currency_zone'] == 'houthi'])
        n_gov = len(df[df['currency_zone'] == 'government'])
        n_harmonic = 2 * n_houthi * n_gov / (n_houthi + n_gov)
        
        # Calculate MDE using power analysis
        from statsmodels.stats.power import tt_solve_power
        mde = tt_solve_power(effect_size=None, nobs=n_harmonic, alpha=0.05, 
                            power=power, alternative='two-sided')
        
        return mde
    
    def _calculate_confidence_interval(self, effect_size: float, se: float) -> Tuple[float, float]:
        """Calculate confidence interval for effect size."""
        z_critical = 1.96  # 95% CI
        ci_lower = effect_size - z_critical * se
        ci_upper = effect_size + z_critical * se
        return ci_lower, ci_upper
    
    def _run_specification_curve_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Run specification curve analysis to test robustness."""
        specifications = []
        
        # Generate specifications
        for fixed_effects in ['none', 'market', 'time', 'both']:
            for clustering in ['none', 'market', 'governorate']:
                for outlier_handling in ['none', 'winsorize_1pct', 'winsorize_5pct']:
                    spec = {
                        'fixed_effects': fixed_effects,
                        'clustering': clustering,
                        'outlier_handling': outlier_handling
                    }
                    
                    # Run specification and store results
                    try:
                        result = self._run_single_specification(df, spec)
                        specifications.append({
                            **spec,
                            'coefficient': result['exchange_coef'],
                            'p_value': result['p_value'],
                            'significant': result['p_value'] < 0.05
                        })
                    except Exception as e:
                        logger.warning(f"Specification failed: {spec}, error: {e}")
        
        # Analyze consistency
        if specifications:
            significant_specs = [s for s in specifications if s['significant']]
            consistency = len(significant_specs) / len(specifications)
            median_coef = np.median([s['coefficient'] for s in specifications])
            coef_range = (
                min(s['coefficient'] for s in specifications),
                max(s['coefficient'] for s in specifications)
            )
        else:
            consistency = 0
            median_coef = 0
            coef_range = (0, 0)
        
        return {
            'n_specifications': len(specifications),
            'consistency': consistency,
            'median_coefficient': median_coef,
            'coefficient_range': coef_range,
            'specifications': specifications[:10]  # Return top 10 for brevity
        }
    
    def _run_single_specification(self, df: pd.DataFrame, spec: Dict[str, Any]) -> Dict[str, Any]:
        """Run a single specification for the specification curve."""
        # Apply outlier handling
        df_spec = df.copy()
        if spec['outlier_handling'] == 'winsorize_1pct':
            from scipy.stats.mstats import winsorize
            df_spec['log_price_yer'] = winsorize(df['log_price_yer'], limits=(0.01, 0.01))
        elif spec['outlier_handling'] == 'winsorize_5pct':
            from scipy.stats.mstats import winsorize
            df_spec['log_price_yer'] = winsorize(df['log_price_yer'], limits=(0.05, 0.05))
        
        # Set up model based on specification
        exog = sm.add_constant(df_spec[['is_houthi', 'is_contested', 'log_exchange_rate']])
        
        # Fixed effects
        entity_effects = spec['fixed_effects'] in ['market', 'both']
        time_effects = spec['fixed_effects'] in ['time', 'both']
        
        # Run model
        model = PanelOLS(df_spec['log_price_yer'], exog, 
                        entity_effects=entity_effects, 
                        time_effects=time_effects)
        
        # Clustering
        if spec['clustering'] == 'market':
            results = model.fit(cov_type='clustered', cluster_entity=True)
        elif spec['clustering'] == 'governorate':
            # Would need governorate clustering implementation
            results = model.fit(cov_type='robust')
        else:
            results = model.fit()
        
        return {
            'exchange_coef': results.params['log_exchange_rate'],
            'p_value': results.pvalues['log_exchange_rate']
        }
    
    def _create_price_comparison_viz(self, results: TestResults) -> Dict:
        """Create visualization data for price comparisons."""
        yer_data = results.detailed_results['yer_differential']
        usd_data = results.detailed_results['usd_differential']
        
        return {
            'type': 'bar_comparison',
            'data': {
                'categories': ['YER Prices', 'USD Prices'],
                'houthi': [yer_data['houthi_mean_yer'], usd_data['houthi_mean_usd']],
                'government': [yer_data['gov_mean_yer'], usd_data['gov_mean_usd']],
                'differential': [yer_data['mean_diff_pct'], usd_data['mean_diff_pct']]
            },
            'title': 'Price Comparison: YER vs USD',
            'interpretation': f"YER: {yer_data['direction']}, USD: {usd_data['direction']}"
        }
    
    def _create_specification_curve_viz(self, results: TestResults) -> Dict:
        """Create specification curve visualization."""
        spec_data = results.specification_curve_results
        
        return {
            'type': 'specification_curve',
            'data': {
                'n_specifications': spec_data.get('n_specifications', 0),
                'consistency': spec_data.get('consistency', 0),
                'median_coefficient': spec_data.get('median_coefficient', 0),
                'coefficient_range': spec_data.get('coefficient_range', (0, 0)),
                'specifications': spec_data.get('specifications', [])
            },
            'title': 'Specification Curve Analysis',
            'interpretation': f"{spec_data.get('consistency', 0):.0%} of specifications show significant results"
        }


# Register the hypothesis test
HypothesisRegistry.register(H1ExchangeRateMechanism())