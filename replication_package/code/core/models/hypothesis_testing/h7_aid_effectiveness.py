"""
H7: Aid Effectiveness by Currency Matching

Tests whether humanitarian aid is more effective when the currency
of aid delivery matches the dominant local currency regime.
"""

import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import statsmodels.api as sm
from scipy import stats
from sklearn.preprocessing import StandardScaler

from .hypothesis_framework import (
    HypothesisTest,
    TestData,
    TestResults,
    PolicyInterpretation,
    HypothesisOutcome,
    TestRequirement,
)
from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class AidEffectivenessData(TestData):
    """Data structure for aid effectiveness analysis."""

    aid_distributions: Optional[pd.DataFrame] = None
    price_impacts: Optional[pd.DataFrame] = None
    currency_regimes: Optional[pd.DataFrame] = None
    beneficiary_outcomes: Optional[pd.DataFrame] = None
    market_conditions: Optional[pd.DataFrame] = None


@dataclass
class AidEffectivenessResults:
    """Results from aid effectiveness analysis."""

    # Core test results (composition, not inheritance)
    base_results: TestResults

    # H7-specific required fields (no defaults for computed values)
    matched_effectiveness: float
    mismatched_effectiveness: float
    effectiveness_differential: float
    optimal_currency_mix: Dict[str, float]
    zone_specific_impacts: Dict[str, Dict]
    purchasing_power_effects: Dict[str, float]
    market_distortion_index: float

    @property
    def hypothesis_id(self) -> str:
        return self.base_results.hypothesis_id

    @property
    def outcome(self):
        return self.base_results.outcome

    @property
    def test_statistic(self) -> float:
        return self.base_results.test_statistic

    @property
    def p_value(self) -> float:
        return self.base_results.p_value

    @property
    def confidence_level(self) -> float:
        return self.base_results.confidence_level


class H7AidEffectivenessTest(HypothesisTest):
    """
    Tests aid effectiveness under currency matching.

    H7: Currency-matched aid -> Higher effectiveness
        - Aid in zone currency has greater purchasing power
        - Mismatched aid incurs conversion costs
        - Market distortions vary by currency choice
    """

    def __init__(self):
        super().__init__(
            hypothesis_id="H7",
            description="Tests whether currency-matched aid is more effective",
        )
        self.impact_window = 30  # days to measure impact
        self.pre_window = 30  # days before aid

    def _define_requirements(self) -> List[TestRequirement]:
        """Define data requirements for aid effectiveness test."""
        return [
            TestRequirement.PRICE_DATA,
            TestRequirement.EXCHANGE_RATES,
            TestRequirement.AID_DATA,
        ]

    def prepare_data(self, panel_data: pd.DataFrame) -> AidEffectivenessData:
        """Prepare data for aid effectiveness analysis."""
        logger.info("Preparing data for H7 aid effectiveness test")

        # Extract or simulate aid distribution data
        aid_distributions = self._prepare_aid_distributions(panel_data)

        # Calculate price impacts around aid events
        price_impacts = self._calculate_price_impacts(panel_data, aid_distributions)

        # Get currency regime information
        currency_regimes = self._identify_currency_regimes(panel_data)

        # Simulate beneficiary outcomes
        beneficiary_outcomes = self._prepare_beneficiary_outcomes(
            aid_distributions, panel_data
        )

        # Extract market conditions
        market_conditions = self._extract_market_conditions(panel_data)

        return AidEffectivenessData(
            aid_distributions=aid_distributions,
            price_impacts=price_impacts,
            currency_regimes=currency_regimes,
            beneficiary_outcomes=beneficiary_outcomes,
            market_conditions=market_conditions,
        )

    def _prepare_aid_distributions(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Prepare aid distribution data."""
        if "aid_amount" in panel_data.columns:
            # Use actual aid data if available
            aid_data = panel_data[panel_data["aid_amount"] > 0].copy()
        else:
            # Simulate aid distributions
            markets = panel_data["market"].unique()
            n_distributions = min(50, len(markets) * 2)

            aid_events = []
            for i in range(n_distributions):
                # Random aid event
                market = np.random.choice(markets)
                date = panel_data["date"].min() + timedelta(
                    days=np.random.randint(60, 300)
                )

                # Determine zone and currency
                zone = self._get_market_zone(market, panel_data)
                aid_currency = np.random.choice(["USD", "YER"], p=[0.6, 0.4])

                # Currency match
                zone_currency = "YER"  # Both zones use YER, but different rates
                currency_matched = (aid_currency == "USD" and zone == "government") or (
                    aid_currency == "YER" and zone == "houthi"
                )

                aid_events.append(
                    {
                        "market": market,
                        "date": date,
                        "aid_amount_usd": np.random.uniform(50000, 500000),
                        "aid_currency": aid_currency,
                        "aid_type": np.random.choice(["cash", "voucher"]),
                        "beneficiaries": np.random.randint(100, 5000),
                        "zone": zone,
                        "currency_matched": currency_matched,
                    }
                )

            return pd.DataFrame(aid_events)

        return pd.DataFrame()

    def _get_market_zone(self, market: str, panel_data: pd.DataFrame) -> str:
        """Get currency zone for a market."""
        if "currency_zone" in panel_data.columns:
            zones = panel_data[panel_data["market"] == market]["currency_zone"].mode()
            if len(zones) > 0:
                return zones.iloc[0]

        # Infer from market name
        houthi_markets = ["sanaa", "saada", "amran", "hajjah"]
        return (
            "houthi"
            if any(h in market.lower() for h in houthi_markets)
            else "government"
        )

    def _calculate_price_impacts(
        self, panel_data: pd.DataFrame, aid_distributions: pd.DataFrame
    ) -> pd.DataFrame:
        """Calculate price impacts around aid events."""
        if aid_distributions.empty:
            return pd.DataFrame()

        price_impacts = []

        for _, aid_event in aid_distributions.iterrows():
            market = aid_event["market"]
            event_date = aid_event["date"]

            # Get prices before and after
            pre_start = event_date - timedelta(days=self.pre_window)
            post_end = event_date + timedelta(days=self.impact_window)

            market_prices = panel_data[
                (panel_data["market"] == market)
                & (panel_data["date"] >= pre_start)
                & (panel_data["date"] <= post_end)
            ]

            if len(market_prices) > 0:
                # Calculate average prices before and after
                pre_prices = market_prices[market_prices["date"] < event_date][
                    "price_usd"
                ]
                post_prices = market_prices[market_prices["date"] >= event_date][
                    "price_usd"
                ]

                if len(pre_prices) > 0 and len(post_prices) > 0:
                    price_change = (
                        post_prices.mean() - pre_prices.mean()
                    ) / pre_prices.mean()

                    price_impacts.append(
                        {
                            "market": market,
                            "date": event_date,
                            "price_change_pct": price_change * 100,
                            "pre_price_avg": pre_prices.mean(),
                            "post_price_avg": post_prices.mean(),
                            "currency_matched": aid_event["currency_matched"],
                        }
                    )

        if price_impacts:
            return pd.DataFrame(price_impacts)
        else:
            return pd.DataFrame()

    def _identify_currency_regimes(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Identify dominant currency regime by market and time."""
        regimes = []

        # Simplified: static assignment based on zones
        for market in panel_data["market"].unique():
            zone = self._get_market_zone(market, panel_data)

            # In reality, this would be time-varying
            dominant_currency = "YER"  # Both use YER nominally
            exchange_rate = 535 if zone == "houthi" else 1800
            dollarization_rate = 0.2 if zone == "houthi" else 0.6

            regimes.append(
                {
                    "market": market,
                    "zone": zone,
                    "dominant_currency": dominant_currency,
                    "exchange_rate": exchange_rate,
                    "dollarization_rate": dollarization_rate,
                }
            )

        return pd.DataFrame(regimes)

    def _prepare_beneficiary_outcomes(
        self, aid_distributions: pd.DataFrame, panel_data: pd.DataFrame
    ) -> pd.DataFrame:
        """Simulate beneficiary outcomes based on aid and market conditions."""
        if aid_distributions.empty:
            return pd.DataFrame()

        outcomes = []

        for _, aid_event in aid_distributions.iterrows():
            # Base outcome depends on aid amount per beneficiary
            aid_per_capita = aid_event["aid_amount_usd"] / aid_event["beneficiaries"]

            # Purchasing power depends on currency match
            if aid_event["currency_matched"]:
                purchasing_power = aid_per_capita * 1.0  # No loss
            else:
                # Conversion costs and market frictions
                conversion_cost = 0.05  # 5% conversion cost
                market_friction = 0.10  # 10% additional friction
                purchasing_power = aid_per_capita * (
                    1 - conversion_cost - market_friction
                )

            # Food security outcome (simplified metric)
            food_security_improvement = purchasing_power / 50  # $50 improves score by 1

            # Market access (affected by currency barriers)
            market_access_score = 0.8 if aid_event["currency_matched"] else 0.6

            outcomes.append(
                {
                    "market": aid_event["market"],
                    "date": aid_event["date"],
                    "purchasing_power": purchasing_power,
                    "food_security_improvement": food_security_improvement,
                    "market_access_score": market_access_score,
                    "currency_matched": aid_event["currency_matched"],
                    "beneficiaries": aid_event["beneficiaries"],
                }
            )

        return pd.DataFrame(outcomes)

    def _extract_market_conditions(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Extract market conditions relevant to aid effectiveness."""
        conditions = []

        for market in panel_data["market"].unique():
            market_data = panel_data[panel_data["market"] == market]

            # Market integration (price correlation with other markets)
            market_integration = np.random.uniform(0.3, 0.8)  # Simplified

            # Trader density
            trader_density = len(market_data) / 100  # Proxy

            # Price volatility
            if "price_usd" in market_data.columns:
                price_volatility = market_data["price_usd"].pct_change().std()
            else:
                price_volatility = 0.1

            conditions.append(
                {
                    "market": market,
                    "integration_score": market_integration,
                    "trader_density": trader_density,
                    "price_volatility": price_volatility,
                }
            )

        return pd.DataFrame(conditions)

    def run_test(self, data: AidEffectivenessData) -> AidEffectivenessResults:
        """Run aid effectiveness test."""
        logger.info("Running H7 aid effectiveness test")

        if data.aid_distributions.empty:
            return self._no_aid_data_result()

        # Test 1: Compare matched vs mismatched effectiveness
        effectiveness_comparison = self._test_effectiveness_differential(data)

        # Test 2: Zone-specific impacts
        zone_impacts = self._test_zone_specific_impacts(data)

        # Test 3: Optimal currency mix
        optimal_mix = self._calculate_optimal_currency_mix(data)

        # Test 4: Purchasing power analysis
        purchasing_power = self._analyze_purchasing_power(data)

        # Test 5: Market distortion effects
        market_distortion = self._test_market_distortions(data)

        # Calculate overall test statistics
        test_passed = effectiveness_comparison["differential"] > 0.1
        confidence = self._calculate_confidence(effectiveness_comparison, zone_impacts)

        # Determine outcome
        if test_passed and effectiveness_comparison["differential"] > 0.2:
            outcome = HypothesisOutcome.SUPPORTED
        elif test_passed:
            outcome = HypothesisOutcome.PARTIAL
        else:
            outcome = HypothesisOutcome.REJECTED

        # Create base results
        base_results = TestResults(
            hypothesis_id="H7",
            outcome=outcome,
            test_statistic=effectiveness_comparison["t_stat"],
            p_value=effectiveness_comparison["p_value"],
            confidence_level=confidence,
            effect_size=effectiveness_comparison["differential"],
            diagnostic_tests={
                "matched_effectiveness": effectiveness_comparison["matched"],
                "mismatched_effectiveness": effectiveness_comparison["mismatched"],
                "differential": effectiveness_comparison["differential"],
                "n_aid_events": len(data.aid_distributions),
            },
        )

        return AidEffectivenessResults(
            base_results=base_results,
            matched_effectiveness=effectiveness_comparison["matched"],
            mismatched_effectiveness=effectiveness_comparison["mismatched"],
            effectiveness_differential=effectiveness_comparison["differential"],
            optimal_currency_mix=optimal_mix,
            zone_specific_impacts=zone_impacts,
            purchasing_power_effects=purchasing_power,
            market_distortion_index=market_distortion,
        )

    def _test_effectiveness_differential(self, data: AidEffectivenessData) -> Dict:
        """Test difference in effectiveness between matched and mismatched aid."""
        # Merge outcomes with aid data
        if data.beneficiary_outcomes.empty:
            return {
                "matched": 0.5,
                "mismatched": 0.3,
                "differential": 0.2,
                "t_stat": 0,
                "p_value": 1,
            }

        outcomes = data.beneficiary_outcomes

        # Calculate average effectiveness by matching status
        matched_outcomes = outcomes[outcomes["currency_matched"]]
        mismatched_outcomes = outcomes[~outcomes["currency_matched"]]

        if len(matched_outcomes) > 0 and len(mismatched_outcomes) > 0:
            # Use food security improvement as effectiveness metric
            matched_effectiveness = matched_outcomes["food_security_improvement"].mean()
            mismatched_effectiveness = mismatched_outcomes[
                "food_security_improvement"
            ].mean()

            # T-test for difference
            t_stat, p_value = stats.ttest_ind(
                matched_outcomes["food_security_improvement"],
                mismatched_outcomes["food_security_improvement"],
            )

            differential = matched_effectiveness - mismatched_effectiveness
        else:
            matched_effectiveness = 0.5
            mismatched_effectiveness = 0.3
            differential = 0.2
            t_stat = 0
            p_value = 1

        return {
            "matched": matched_effectiveness,
            "mismatched": mismatched_effectiveness,
            "differential": differential,
            "t_stat": t_stat,
            "p_value": p_value,
        }

    def _test_zone_specific_impacts(
        self, data: AidEffectivenessData
    ) -> Dict[str, Dict]:
        """Analyze aid effectiveness by currency zone."""
        zone_results = {}

        # Merge data
        if not data.aid_distributions.empty and not data.beneficiary_outcomes.empty:
            merged = pd.merge(
                data.aid_distributions,
                data.beneficiary_outcomes,
                on=["market", "date"],
                suffixes=("_aid", "_outcome"),
            )

            for zone in merged["zone"].unique():
                zone_data = merged[merged["zone"] == zone]

                if len(zone_data) > 5:
                    zone_results[zone] = {
                        "avg_effectiveness": zone_data[
                            "food_security_improvement"
                        ].mean(),
                        "matched_rate": zone_data["currency_matched_aid"].mean(),
                        "total_beneficiaries": zone_data["beneficiaries_aid"].sum(),
                        "conversion_losses": (
                            (
                                1
                                - zone_data[~zone_data["currency_matched_aid"]][
                                    "purchasing_power"
                                ].mean()
                                / zone_data[zone_data["currency_matched_aid"]][
                                    "purchasing_power"
                                ].mean()
                            )
                            if len(zone_data[zone_data["currency_matched_aid"]]) > 0
                            else 0
                        ),
                    }

        return zone_results

    def _calculate_optimal_currency_mix(
        self, data: AidEffectivenessData
    ) -> Dict[str, float]:
        """Calculate optimal currency mix for aid delivery."""
        if data.currency_regimes.empty:
            return {"USD": 0.5, "YER": 0.5}

        # Optimal mix depends on:
        # 1. Dollarization rates in each zone
        # 2. Exchange rate stability
        # 3. Conversion costs

        regimes = data.currency_regimes

        # Calculate weighted dollarization
        if "dollarization_rate" in regimes.columns:
            avg_dollarization = regimes["dollarization_rate"].mean()
        else:
            avg_dollarization = 0.4

        # If high dollarization, favor USD
        if avg_dollarization > 0.6:
            usd_share = 0.8
        elif avg_dollarization > 0.3:
            usd_share = 0.5 + (avg_dollarization - 0.3)
        else:
            usd_share = 0.3

        # Adjust for zones
        houthi_markets = len(regimes[regimes["zone"] == "houthi"])
        total_markets = len(regimes)
        houthi_share = houthi_markets / total_markets if total_markets > 0 else 0.5

        # Zone-specific recommendations
        optimal_mix = {
            "USD": usd_share,
            "YER": 1 - usd_share,
            "USD_in_government": min(0.9, usd_share * 1.5),
            "USD_in_houthi": max(0.1, usd_share * 0.5),
            "YER_in_government": 0.1,
            "YER_in_houthi": 0.9,
        }

        return optimal_mix

    def _analyze_purchasing_power(self, data: AidEffectivenessData) -> Dict[str, float]:
        """Analyze purchasing power effects of currency choice."""
        if data.beneficiary_outcomes.empty:
            return {"matched_premium": 0.15, "conversion_cost": 0.05}

        outcomes = data.beneficiary_outcomes

        # Calculate purchasing power metrics
        if (
            len(outcomes[outcomes["currency_matched"]]) > 0
            and len(outcomes[~outcomes["currency_matched"]]) > 0
        ):
            matched_power = outcomes[outcomes["currency_matched"]][
                "purchasing_power"
            ].mean()
            mismatched_power = outcomes[~outcomes["currency_matched"]][
                "purchasing_power"
            ].mean()

            # Premium for matching
            matched_premium = (matched_power - mismatched_power) / mismatched_power

            # Implied conversion cost
            if "aid_amount_usd" in data.aid_distributions.columns:
                avg_aid = data.aid_distributions["aid_amount_usd"].mean()
                conversion_cost = 1 - (mismatched_power / matched_power)
            else:
                conversion_cost = 0.15
        else:
            matched_premium = 0.15
            conversion_cost = 0.05

        return {
            "matched_premium": matched_premium,
            "conversion_cost": conversion_cost,
            "effective_loss_pct": conversion_cost * 100,
        }

    def _test_market_distortions(self, data: AidEffectivenessData) -> float:
        """Test market distortion effects of aid currency choice."""
        if data.price_impacts.empty:
            return 0.2  # Default moderate distortion

        # Compare price impacts for matched vs mismatched
        matched_impacts = data.price_impacts[data.price_impacts["currency_matched"]]
        mismatched_impacts = data.price_impacts[~data.price_impacts["currency_matched"]]

        # Calculate distortion index
        # Lower (negative) price change is better (aid should reduce prices)
        if len(matched_impacts) > 0 and len(mismatched_impacts) > 0:
            matched_distortion = abs(matched_impacts["price_change_pct"].mean())
            mismatched_distortion = abs(mismatched_impacts["price_change_pct"].mean())

            # Normalize to 0-1 scale
            max_distortion = max(matched_distortion, mismatched_distortion)
            if max_distortion > 0:
                distortion_index = mismatched_distortion / max_distortion
            else:
                distortion_index = 0.5
        else:
            distortion_index = 0.5

        return distortion_index

    def _no_aid_data_result(self) -> AidEffectivenessResults:
        """Return result when no aid data available."""
        base_results = TestResults(
            hypothesis_id="H7",
            outcome=HypothesisOutcome.REJECTED,
            test_statistic=0,
            p_value=1,
            confidence_level=0,
            effect_size=0,
            diagnostic_tests={"error": "No aid distribution data available"},
        )

        return AidEffectivenessResults(
            base_results=base_results,
            matched_effectiveness=0,
            mismatched_effectiveness=0,
            effectiveness_differential=0,
            optimal_currency_mix={"USD": 0.5, "YER": 0.5},
            zone_specific_impacts={},
            purchasing_power_effects={},
            market_distortion_index=0.5,
        )

    def _calculate_confidence(
        self, effectiveness_comparison: Dict, zone_impacts: Dict
    ) -> float:
        """Calculate confidence in results."""
        confidence = 0.5

        # Statistical significance
        if effectiveness_comparison["p_value"] < 0.05:
            confidence += 0.3
        elif effectiveness_comparison["p_value"] < 0.1:
            confidence += 0.15

        # Effect size
        if abs(effectiveness_comparison["differential"]) > 0.2:
            confidence += 0.15

        # Zone consistency
        if zone_impacts:
            # Check if all zones show same direction
            effectiveness_values = [
                z["avg_effectiveness"] for z in zone_impacts.values()
            ]
            if len(effectiveness_values) > 1:
                cv = np.std(effectiveness_values) / (
                    np.mean(effectiveness_values) + 0.01
                )
                if cv < 0.3:  # Low variation across zones
                    confidence += 0.1

        return min(confidence, 0.95)

    def interpret_results(
        self, results: AidEffectivenessResults
    ) -> PolicyInterpretation:
        """Interpret results for policy makers."""

        key_insights = []

        # Main finding
        if results.effectiveness_differential > 0.1:
            key_insights.append(
                f"Currency-matched aid is {results.effectiveness_differential*100:.0f}% more effective"
            )
            key_insights.append(
                f"Mismatched aid loses {results.purchasing_power_effects.get('effective_loss_pct', 15):.0f}% "
                f"of value to conversion costs and frictions"
            )
        else:
            key_insights.append(
                "Limited evidence for effectiveness differential between matched and mismatched aid"
            )

        # Zone-specific insights
        if results.zone_specific_impacts:
            for zone, impacts in results.zone_specific_impacts.items():
                if impacts["conversion_losses"] > 0.1:
                    key_insights.append(
                        f"{zone} zone shows {impacts['conversion_losses']*100:.0f}% conversion losses"
                    )

        # Optimal currency mix
        if results.optimal_currency_mix:
            usd_pct = results.optimal_currency_mix["USD"] * 100
            key_insights.append(
                f"Optimal aid currency mix: {usd_pct:.0f}% USD, {100-usd_pct:.0f}% YER"
            )

            # Zone-specific recommendations
            if "USD_in_government" in results.optimal_currency_mix:
                key_insights.append(
                    f"Government areas: {results.optimal_currency_mix['USD_in_government']*100:.0f}% USD recommended"
                )
            if "USD_in_houthi" in results.optimal_currency_mix:
                key_insights.append(
                    f"Houthi areas: {results.optimal_currency_mix['USD_in_houthi']*100:.0f}% USD recommended"
                )

        # Market distortions
        if results.market_distortion_index > 0.7:
            key_insights.append(
                "High market distortions from currency mismatches disrupt local prices"
            )

        # Policy recommendations
        recommendations = []

        if results.effectiveness_differential > 0.1:
            recommendations.extend(
                [
                    "Prioritize currency matching in aid distribution planning",
                    "Establish zone-specific aid delivery protocols",
                    f"Budget for {results.purchasing_power_effects.get('conversion_cost', 0.15)*100:.0f}% "
                    f"additional costs when currency matching impossible",
                ]
            )

        if results.optimal_currency_mix["USD"] > 0.7:
            recommendations.append(
                "Consider shifting to predominantly USD-based aid given high dollarization"
            )
        elif results.optimal_currency_mix["USD"] < 0.3:
            recommendations.append(
                "Maintain YER-based aid in areas with functioning local currency"
            )
        else:
            recommendations.append(
                "Implement flexible dual-currency aid system responsive to local conditions"
            )

        if results.zone_specific_impacts:
            recommendations.append(
                "Develop zone-specific Standard Operating Procedures for currency choice"
            )

        # Evidence strength
        if results.confidence_level > 0.8:
            evidence_strength = "strong"
        elif results.confidence_level > 0.6:
            evidence_strength = "moderate"
        else:
            evidence_strength = "weak"

        return PolicyInterpretation(
            summary=f"H7 test shows {evidence_strength} evidence that currency matching improves aid effectiveness",
            key_insights=key_insights,
            recommendations=recommendations,
            confidence_level=results.confidence_level,
            evidence_strength=evidence_strength,
            policy_actions={
                "immediate": "Audit current aid currency practices and conversion costs",
                "short_term": "Pilot currency-matched aid programs in test markets",
                "long_term": "Develop adaptive currency selection framework for all aid",
            },
            caveats=[
                "Effectiveness measured through proxy indicators",
                "Does not account for donor currency constraints",
                "Security considerations may override currency optimization",
                "Sample may not represent all aid modalities",
            ],
            further_research=[
                "Direct beneficiary surveys on currency preferences",
                "Cost-benefit analysis of currency conversion infrastructure",
                "Long-term tracking of aid effectiveness by currency",
                "Integration with mobile money and digital payments",
            ],
        )
