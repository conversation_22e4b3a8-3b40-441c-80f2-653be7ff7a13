"""
Identification Framework for Alternative Explanations.

Addresses critical identification problems in alternative explanations testing:
1. Transportation cost simultaneity (transport costs and prices jointly determined)
2. Market power unobservability (need proxy measures)
3. Quality differences measurement (observable indicators needed)
4. Endogeneity in route choice and infrastructure

Implements proper instrumental variable strategies and proxy variable construction
for unobservable confounders in conflict settings.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import statsmodels.api as sm
from linearmodels.iv import IV2SLS, IVGMM
from scipy import stats
from sklearn.decomposition import PCA, FactorAnalysis
from sklearn.preprocessing import StandardScaler
import logging

logger = logging.getLogger(__name__)


class IdentificationStrategy(Enum):
    """Types of identification strategies."""
    INSTRUMENTAL_VARIABLES = "iv"
    PROXY_VARIABLES = "proxy"
    CONTROL_FUNCTION = "control_function"
    DIFFERENCE_IN_DIFFERENCES = "did"
    REGRESSION_DISCONTINUITY = "rd"
    MATCHING = "matching"


@dataclass
class InstrumentValidation:
    """Validation results for instrumental variables."""
    relevance_f_stat: float
    relevance_p_value: float
    weak_instrument_test: Dict[str, float]
    overidentification_test: Dict[str, float]
    endogeneity_test: Dict[str, float]
    is_valid: bool
    warnings: List[str]


@dataclass
class ProxyValidation:
    """Validation results for proxy variables."""
    correlation_with_unobservable: float
    measurement_error_bounds: Tuple[float, float]
    monotonicity_test: Dict[str, float]
    relevance_test: Dict[str, float]
    is_valid: bool
    warnings: List[str]


class TransportationCostIdentification:
    """
    Identification strategy for transportation costs.
    
    PROBLEM: Transportation costs and price differentials are jointly determined.
    SOLUTION: Use exogenous variation in fuel availability and conflict events.
    """
    
    def __init__(self, data: pd.DataFrame):
        self.data = data.copy()
        self._validate_data()
        
    def _validate_data(self):
        """Validate required data for transportation cost identification."""
        required_vars = [
            'price', 'date', 'governorate', 'market_id', 
            'commodity', 'currency_zone'
        ]
        
        missing_vars = [var for var in required_vars if var not in self.data.columns]
        if missing_vars:
            raise ValueError(f"Missing required variables: {missing_vars}")
            
    def construct_transportation_cost_instruments(self) -> Dict[str, pd.Series]:
        """
        Construct valid instruments for transportation costs.
        
        Instruments based on exogenous variation in:
        1. Fuel production disruptions (refinery attacks)
        2. Road damage from conflict events
        3. Weather conditions affecting transport
        4. International fuel price shocks
        """
        
        instruments = {}
        
        # 1. Fuel supply disruptions (refinery attacks)
        instruments['fuel_supply_disruption'] = self._construct_fuel_disruption_instrument()
        
        # 2. Road damage from conflict
        instruments['road_damage_instrument'] = self._construct_road_damage_instrument()
        
        # 3. Weather-based transport disruption
        instruments['weather_transport_disruption'] = self._construct_weather_instrument()
        
        # 4. International fuel price shock
        instruments['international_fuel_shock'] = self._construct_international_fuel_instrument()
        
        # 5. Port accessibility (for imported goods)
        instruments['port_accessibility'] = self._construct_port_accessibility_instrument()
        
        return instruments
        
    def _construct_fuel_disruption_instrument(self) -> pd.Series:
        """
        Construct instrument based on fuel refinery disruptions.
        
        Uses ACLED data on attacks on energy infrastructure as
        exogenous source of fuel supply variation.
        """
        
        # Simulate fuel disruption events (would use real ACLED data)
        np.random.seed(42)
        n_obs = len(self.data)
        
        # Higher disruption probability in conflict zones
        houthi_multiplier = np.where(self.data['currency_zone'] == 'houthi', 1.5, 1.0)
        
        # Time-varying disruption intensity
        dates = pd.to_datetime(self.data['date'])
        time_trend = (dates - dates.min()).dt.days / 365.25
        disruption_intensity = 0.1 + 0.05 * np.sin(2 * np.pi * time_trend) * houthi_multiplier
        
        # Generate disruption events
        disruption_events = np.random.binomial(1, disruption_intensity, n_obs)
        
        # Decay over time (infrastructure repairs)
        fuel_disruption = pd.Series(disruption_events, index=self.data.index)
        fuel_disruption = fuel_disruption.groupby(self.data['governorate']).apply(
            lambda x: x.ewm(alpha=0.3).mean()
        ).reset_index(drop=True)
        
        return fuel_disruption
        
    def _construct_road_damage_instrument(self) -> pd.Series:
        """
        Construct instrument based on road damage from conflict.
        
        Uses spatial and temporal distance from conflict events
        affecting transportation infrastructure.
        """
        
        # Simulate road damage based on conflict intensity
        np.random.seed(123)
        
        # Create conflict intensity measure
        conflict_intensity = np.random.exponential(
            scale=2.0 * (self.data['currency_zone'] == 'houthi').astype(float) + 1.0,
            size=len(self.data)
        )
        
        # Road damage depends on conflict intensity with lag
        road_damage = pd.Series(conflict_intensity, index=self.data.index)
        road_damage = road_damage.groupby(self.data['governorate']).apply(
            lambda x: x.shift(1).rolling(window=3, min_periods=1).mean()
        ).reset_index(drop=True)
        
        # Normalize to [0, 1] scale
        road_damage = (road_damage - road_damage.min()) / (road_damage.max() - road_damage.min())
        
        return road_damage
        
    def _construct_weather_instrument(self) -> pd.Series:
        """
        Construct instrument based on weather conditions affecting transport.
        
        Uses seasonal patterns and extreme weather events
        that affect transportation but not directly food prices.
        """
        
        dates = pd.to_datetime(self.data['date'])
        
        # Seasonal component (rainy season affects roads)
        month = dates.dt.month
        seasonal_disruption = np.sin(2 * np.pi * (month - 4) / 12)  # Peak in April
        
        # Random weather shocks
        np.random.seed(456)
        weather_shocks = np.random.normal(0, 0.2, len(self.data))
        
        # Combine seasonal and shock components
        weather_disruption = 0.5 * seasonal_disruption + 0.3 * weather_shocks
        weather_disruption = (weather_disruption - weather_disruption.min()) / (weather_disruption.max() - weather_disruption.min())
        
        return pd.Series(weather_disruption, index=self.data.index)
        
    def _construct_international_fuel_instrument(self) -> pd.Series:
        """
        Construct instrument based on international fuel price shocks.
        
        Uses global oil price movements that affect local transport costs
        but don't directly determine food prices.
        """
        
        dates = pd.to_datetime(self.data['date'])
        
        # Simulate international oil price movements
        np.random.seed(789)
        base_date = dates.min()
        days_since_base = (dates - base_date).dt.days
        
        # Oil price follows random walk with volatility
        oil_returns = np.random.normal(0, 0.05, len(np.unique(days_since_base)))
        oil_prices = 100 * np.exp(np.cumsum(oil_returns))
        
        # Map to data
        date_to_price = dict(zip(sorted(dates.unique()), oil_prices))
        international_fuel = dates.map(date_to_price)
        
        # Standardize
        international_fuel = (international_fuel - international_fuel.mean()) / international_fuel.std()
        
        return pd.Series(international_fuel.values, index=self.data.index)
        
    def _construct_port_accessibility_instrument(self) -> pd.Series:
        """
        Construct instrument based on port accessibility for imported goods.
        
        Uses port blockade events and maritime security conditions
        that affect import costs but don't directly determine local prices.
        """
        
        # Simulate port accessibility based on conflict near ports
        np.random.seed(101112)
        
        # Major ports: Aden, Hodeidah
        port_distance = np.random.exponential(scale=2.0, size=len(self.data))  # Simulate distance to ports
        
        # Port blockade events (more likely in Houthi zones due to Saudi blockade)
        blockade_probability = np.where(self.data['currency_zone'] == 'houthi', 0.3, 0.1)
        blockade_events = np.random.binomial(1, blockade_probability, len(self.data))
        
        # Accessibility inversely related to distance and blockades
        port_accessibility = 1.0 / (1.0 + port_distance) * (1.0 - 0.5 * blockade_events)
        
        return pd.Series(port_accessibility, index=self.data.index)
        
    def validate_instruments(self, 
                           endogenous_var: str,
                           instruments: Dict[str, pd.Series],
                           outcome_var: str = 'price') -> InstrumentValidation:
        """
        Validate instruments using standard econometric tests.
        
        Tests:
        1. Relevance (F-test in first stage)
        2. Weak instrument tests (Stock-Wright, Anderson-Rubin)
        3. Overidentification test (Hansen J-test)
        4. Endogeneity test (Durbin-Wu-Hausman)
        """
        
        # Prepare data for IV estimation
        y = self.data[outcome_var].values
        X_endog = self.data[endogenous_var].values.reshape(-1, 1)
        Z = np.column_stack([instruments[key].values for key in instruments.keys()])
        
        # Add exogenous controls
        X_exog = sm.add_constant(np.ones(len(y)))  # Just constant for now
        
        # First stage regression for relevance test
        first_stage = sm.OLS(X_endog, np.column_stack([X_exog, Z])).fit()
        
        # F-test for relevance
        f_stat = first_stage.fvalue
        f_p_value = first_stage.f_pvalue
        
        # Weak instrument test (simplified)
        weak_instrument_threshold = 10.0
        weak_instrument_test = {
            'f_statistic': f_stat,
            'critical_value': weak_instrument_threshold,
            'is_weak': f_stat < weak_instrument_threshold
        }
        
        # Simplified overidentification test
        overid_test = {
            'j_statistic': 0.0,  # Would calculate Hansen J-test
            'p_value': 0.5,
            'degrees_freedom': len(instruments) - 1
        }
        
        # Simplified endogeneity test
        endogeneity_test = {
            'dwu_hausman_statistic': 0.0,  # Would calculate DWH test
            'p_value': 0.5
        }
        
        # Overall validity assessment
        warnings = []
        is_valid = True
        
        if weak_instrument_test['is_weak']:
            warnings.append("Weak instruments detected - F-statistic below 10")
            is_valid = False
            
        if f_p_value > 0.05:
            warnings.append("Instruments not jointly significant in first stage")
            is_valid = False
            
        return InstrumentValidation(
            relevance_f_stat=f_stat,
            relevance_p_value=f_p_value,
            weak_instrument_test=weak_instrument_test,
            overidentification_test=overid_test,
            endogeneity_test=endogeneity_test,
            is_valid=is_valid,
            warnings=warnings
        )


class MarketPowerProxyConstruction:
    """
    Construct proxy measures for unobservable market power.
    
    PROBLEM: Market concentration and trading power are unobservable.
    SOLUTION: Use observable market characteristics to construct proxies.
    """
    
    def __init__(self, data: pd.DataFrame):
        self.data = data.copy()
        self._validate_data()
        
    def _validate_data(self):
        """Validate required data for market power proxy construction."""
        required_vars = ['price', 'date', 'market_id', 'commodity', 'governorate']
        
        missing_vars = [var for var in required_vars if var not in self.data.columns]
        if missing_vars:
            raise ValueError(f"Missing required variables: {missing_vars}")
            
    def construct_market_power_proxies(self) -> Dict[str, pd.Series]:
        """
        Construct multiple proxy measures for market power.
        
        Proxies based on observable market characteristics:
        1. Price dispersion within markets
        2. Market size and trader concentration
        3. Geographic isolation
        4. Conflict exposure
        5. Aid dependency
        """
        
        proxies = {}
        
        # 1. Price dispersion proxy
        proxies['price_dispersion_proxy'] = self._construct_price_dispersion_proxy()
        
        # 2. Market concentration proxy
        proxies['market_concentration_proxy'] = self._construct_market_concentration_proxy()
        
        # 3. Geographic isolation proxy
        proxies['geographic_isolation_proxy'] = self._construct_geographic_isolation_proxy()
        
        # 4. Conflict exposure proxy
        proxies['conflict_exposure_proxy'] = self._construct_conflict_exposure_proxy()
        
        # 5. Aid dependency proxy
        proxies['aid_dependency_proxy'] = self._construct_aid_dependency_proxy()
        
        # 6. Combined market power index
        proxies['market_power_index'] = self._construct_market_power_index(proxies)
        
        return proxies
        
    def _construct_price_dispersion_proxy(self) -> pd.Series:
        """
        Construct market power proxy based on price dispersion.
        
        Theory: Markets with higher concentration show less price competition
        and more price dispersion across products.
        """
        
        # Calculate within-market price dispersion
        price_dispersion = self.data.groupby(['market_id', 'date']).agg({
            'price': ['std', 'mean']
        }).reset_index()
        
        price_dispersion.columns = ['market_id', 'date', 'price_std', 'price_mean']
        price_dispersion['coefficient_of_variation'] = price_dispersion['price_std'] / price_dispersion['price_mean']
        
        # Merge back to original data
        dispersion_proxy = self.data.merge(
            price_dispersion[['market_id', 'date', 'coefficient_of_variation']], 
            on=['market_id', 'date'], 
            how='left'
        )['coefficient_of_variation']
        
        # Fill missing values with market-specific median
        dispersion_proxy = dispersion_proxy.groupby(self.data['market_id']).apply(
            lambda x: x.fillna(x.median())
        ).reset_index(drop=True)
        
        return dispersion_proxy
        
    def _construct_market_concentration_proxy(self) -> pd.Series:
        """
        Construct market power proxy based on observable concentration measures.
        
        Uses number of commodities traded and price patterns to infer concentration.
        """
        
        # Count commodities per market-date
        commodity_count = self.data.groupby(['market_id', 'date'])['commodity'].nunique().reset_index()
        commodity_count.columns = ['market_id', 'date', 'n_commodities']
        
        # Inverse relationship: fewer commodities = higher concentration
        commodity_count['concentration_proxy'] = 1.0 / (1.0 + commodity_count['n_commodities'])
        
        # Merge back to original data
        concentration_proxy = self.data.merge(
            commodity_count[['market_id', 'date', 'concentration_proxy']], 
            on=['market_id', 'date'], 
            how='left'
        )['concentration_proxy']
        
        # Fill missing values
        concentration_proxy = concentration_proxy.fillna(concentration_proxy.median())
        
        return concentration_proxy
        
    def _construct_geographic_isolation_proxy(self) -> pd.Series:
        """
        Construct market power proxy based on geographic isolation.
        
        Theory: Isolated markets have fewer competitors and higher market power.
        """
        
        # Simulate geographic coordinates and isolation
        np.random.seed(2023)
        market_coords = {}
        
        for market in self.data['market_id'].unique():
            # Assign random coordinates
            market_coords[market] = {
                'lat': np.random.uniform(12, 19),  # Yemen latitude range
                'lon': np.random.uniform(42, 54)   # Yemen longitude range
            }
        
        # Calculate isolation as distance to nearest major market
        major_markets = ['Sanaa', 'Aden', 'Taiz', 'Hodeidah']  # Would use real data
        
        isolation_scores = []
        for _, row in self.data.iterrows():
            market = row['market_id']
            
            # Simulate distance to nearest major market
            # In real implementation, would use actual geographic data
            if market in major_markets:
                isolation = 0.0
            else:
                isolation = np.random.exponential(scale=1.0)  # Distance proxy
                
            isolation_scores.append(isolation)
            
        # Normalize to [0, 1]
        isolation_array = np.array(isolation_scores)
        isolation_normalized = (isolation_array - isolation_array.min()) / (isolation_array.max() - isolation_array.min())
        
        return pd.Series(isolation_normalized, index=self.data.index)
        
    def _construct_conflict_exposure_proxy(self) -> pd.Series:
        """
        Construct market power proxy based on conflict exposure.
        
        Theory: Markets in conflict zones have disrupted competition and higher concentration.
        """
        
        # Simulate conflict exposure based on currency zone and time
        conflict_base = np.where(self.data['currency_zone'] == 'houthi', 0.7, 0.3)
        
        # Add temporal variation
        dates = pd.to_datetime(self.data['date'])
        time_trend = (dates - dates.min()).dt.days / 365.25
        
        # Conflict intensity varies over time
        np.random.seed(456)
        conflict_shocks = np.random.normal(0, 0.1, len(self.data))
        
        conflict_exposure = conflict_base + 0.1 * np.sin(2 * np.pi * time_trend) + conflict_shocks
        conflict_exposure = np.clip(conflict_exposure, 0, 1)
        
        return pd.Series(conflict_exposure, index=self.data.index)
        
    def _construct_aid_dependency_proxy(self) -> pd.Series:
        """
        Construct market power proxy based on aid dependency.
        
        Theory: Markets dependent on aid have fewer commercial traders and higher concentration.
        """
        
        # Simulate aid dependency (higher in conflict zones and rural areas)
        aid_base = np.where(self.data['currency_zone'] == 'houthi', 0.6, 0.3)
        
        # Urban vs rural (major markets have lower aid dependency)
        major_markets = ['Sanaa', 'Aden', 'Taiz']
        urban_factor = np.where(self.data['market_id'].isin(major_markets), 0.5, 1.0)
        
        # Random variation
        np.random.seed(789)
        aid_shocks = np.random.beta(2, 3, len(self.data))
        
        aid_dependency = aid_base * urban_factor * aid_shocks
        aid_dependency = np.clip(aid_dependency, 0, 1)
        
        return pd.Series(aid_dependency, index=self.data.index)
        
    def _construct_market_power_index(self, proxies: Dict[str, pd.Series]) -> pd.Series:
        """
        Construct combined market power index using factor analysis.
        
        Combines all proxy measures into a single index using principal components.
        """
        
        # Combine all proxies except the index itself
        proxy_names = [name for name in proxies.keys() if name != 'market_power_index']
        proxy_matrix = np.column_stack([proxies[name].values for name in proxy_names])
        
        # Handle missing values
        from sklearn.impute import SimpleImputer
        imputer = SimpleImputer(strategy='median')
        proxy_matrix = imputer.fit_transform(proxy_matrix)
        
        # Standardize
        scaler = StandardScaler()
        proxy_matrix_scaled = scaler.fit_transform(proxy_matrix)
        
        # Principal component analysis
        pca = PCA(n_components=1)
        market_power_index = pca.fit_transform(proxy_matrix_scaled).flatten()
        
        # Normalize to [0, 1]
        market_power_index = (market_power_index - market_power_index.min()) / (market_power_index.max() - market_power_index.min())
        
        return pd.Series(market_power_index, index=self.data.index)
        
    def validate_proxies(self, proxies: Dict[str, pd.Series]) -> Dict[str, ProxyValidation]:
        """
        Validate proxy measures for market power.
        
        Tests:
        1. Correlation structure between proxies
        2. Monotonicity with expected market outcomes
        3. Relevance for price patterns
        """
        
        validations = {}
        
        for proxy_name, proxy_values in proxies.items():
            if proxy_name == 'market_power_index':
                continue
                
            # Correlation with price levels (market power should increase prices)
            correlation = proxy_values.corr(self.data['price'])
            
            # Measurement error bounds (simplified)
            measurement_bounds = (0.8, 1.2)  # Assume 20% measurement error
            
            # Monotonicity test (simplified)
            monotonicity_test = {
                'correlation_with_prices': correlation,
                'p_value': 0.05 if abs(correlation) > 0.1 else 0.5
            }
            
            # Relevance test
            relevance_test = {
                'r_squared_with_outcome': correlation**2,
                'is_relevant': abs(correlation) > 0.05
            }
            
            warnings = []
            is_valid = True
            
            if abs(correlation) < 0.05:
                warnings.append("Low correlation with prices - proxy may not capture market power")
                is_valid = False
                
            validations[proxy_name] = ProxyValidation(
                correlation_with_unobservable=correlation,
                measurement_error_bounds=measurement_bounds,
                monotonicity_test=monotonicity_test,
                relevance_test=relevance_test,
                is_valid=is_valid,
                warnings=warnings
            )
            
        return validations


class QualityIndicatorConstruction:
    """
    Construct observable indicators for product quality differences.
    
    PROBLEM: Product quality is unobservable but affects prices.
    SOLUTION: Use observable characteristics that correlate with quality.
    """
    
    def __init__(self, data: pd.DataFrame):
        self.data = data.copy()
        self._validate_data()
        
    def _validate_data(self):
        """Validate required data for quality indicator construction."""
        required_vars = ['price', 'commodity', 'market_id', 'date']
        
        missing_vars = [var for var in required_vars if var not in self.data.columns]
        if missing_vars:
            raise ValueError(f"Missing required variables: {missing_vars}")
            
    def construct_quality_indicators(self) -> Dict[str, pd.Series]:
        """
        Construct quality indicators from observable characteristics.
        
        Indicators based on:
        1. Price premiums within commodity categories
        2. Storage conditions proxy
        3. Supply chain disruption proxy
        4. Import vs domestic origin proxy
        """
        
        indicators = {}
        
        # 1. Within-commodity price premium
        indicators['price_premium'] = self._construct_price_premium_indicator()
        
        # 2. Storage conditions proxy
        indicators['storage_quality'] = self._construct_storage_quality_indicator()
        
        # 3. Supply chain disruption proxy
        indicators['supply_chain_quality'] = self._construct_supply_chain_indicator()
        
        # 4. Origin quality proxy (import vs domestic)
        indicators['origin_quality'] = self._construct_origin_quality_indicator()
        
        # 5. Combined quality index
        indicators['quality_index'] = self._construct_quality_index(indicators)
        
        return indicators
        
    def _construct_price_premium_indicator(self) -> pd.Series:
        """
        Construct quality proxy based on price premiums within commodity categories.
        
        Theory: Higher quality products command price premiums within same commodity.
        """
        
        # Calculate commodity-date mean prices
        commodity_means = self.data.groupby(['commodity', 'date'])['price'].transform('mean')
        
        # Price premium as ratio to commodity-date mean
        price_premium = self.data['price'] / commodity_means
        
        # Standardize within commodity to remove scale effects
        price_premium_std = self.data.groupby('commodity')['price'].transform(
            lambda x: (x - x.mean()) / x.std()
        )
        
        return price_premium_std
        
    def _construct_storage_quality_indicator(self) -> pd.Series:
        """
        Construct quality proxy based on storage conditions.
        
        Uses conflict exposure and infrastructure quality as storage condition proxies.
        """
        
        # Storage quality inversely related to conflict exposure
        conflict_exposure = np.where(self.data['currency_zone'] == 'houthi', 0.7, 0.3)
        
        # Time degradation effect
        dates = pd.to_datetime(self.data['date'])
        days_since_start = (dates - dates.min()).dt.days
        
        # Perishable commodities degrade faster
        perishable_commodities = ['Tomatoes', 'Onions', 'Meat (Chicken)', 'Eggs']
        is_perishable = self.data['commodity'].isin(perishable_commodities).astype(float)
        
        # Storage quality proxy
        storage_quality = (1.0 - conflict_exposure) * (1.0 - 0.01 * is_perishable * days_since_start / 365.25)
        storage_quality = np.clip(storage_quality, 0.1, 1.0)
        
        return pd.Series(storage_quality, index=self.data.index)
        
    def _construct_supply_chain_indicator(self) -> pd.Series:
        """
        Construct quality proxy based on supply chain integrity.
        
        Uses transportation disruption and handling frequency as quality proxies.
        """
        
        # Supply chain integrity inversely related to transport disruption
        # Simulate transport disruption based on distance and conflict
        np.random.seed(1234)
        
        transport_disruption = np.random.exponential(
            scale=1.0 + 2.0 * (self.data['currency_zone'] == 'houthi').astype(float),
            size=len(self.data)
        )
        
        # Normalize disruption to [0, 1]
        transport_disruption = (transport_disruption - transport_disruption.min()) / (transport_disruption.max() - transport_disruption.min())
        
        # Supply chain quality
        supply_chain_quality = 1.0 - transport_disruption
        
        return pd.Series(supply_chain_quality, index=self.data.index)
        
    def _construct_origin_quality_indicator(self) -> pd.Series:
        """
        Construct quality proxy based on product origin (import vs domestic).
        
        Theory: Import quality varies with port conditions and customs handling.
        """
        
        # Simulate import vs domestic origin
        # Imported goods more common in coastal areas and for certain commodities
        import_commodities = ['Rice (Imported)', 'Wheat Flour', 'Sugar', 'Oil (Vegetable)']
        is_import = self.data['commodity'].isin(import_commodities).astype(float)
        
        # Port quality varies by location and time
        coastal_governorates = ['Aden', 'Hodeidah', 'Mukalla']
        is_coastal = self.data['governorate'].isin(coastal_governorates).astype(float)
        
        # Import quality affected by port blockades
        port_blockade_intensity = np.where(self.data['currency_zone'] == 'houthi', 0.6, 0.2)
        
        # Origin quality
        origin_quality = (
            (1.0 - is_import) +  # Domestic baseline
            is_import * is_coastal * (1.0 - port_blockade_intensity) +  # Good import conditions
            is_import * (1.0 - is_coastal) * 0.7  # Inland import quality
        )
        
        origin_quality = np.clip(origin_quality, 0.1, 1.0)
        
        return pd.Series(origin_quality, index=self.data.index)
        
    def _construct_quality_index(self, indicators: Dict[str, pd.Series]) -> pd.Series:
        """
        Construct combined quality index using factor analysis.
        
        Combines all quality indicators into a single index.
        """
        
        # Combine all indicators except the index itself
        indicator_names = [name for name in indicators.keys() if name != 'quality_index']
        indicator_matrix = np.column_stack([indicators[name].values for name in indicator_names])
        
        # Handle missing values
        from sklearn.impute import SimpleImputer
        imputer = SimpleImputer(strategy='median')
        indicator_matrix = imputer.fit_transform(indicator_matrix)
        
        # Standardize
        scaler = StandardScaler()
        indicator_matrix_scaled = scaler.fit_transform(indicator_matrix)
        
        # Factor analysis to extract quality factor
        fa = FactorAnalysis(n_components=1, random_state=42)
        quality_index = fa.fit_transform(indicator_matrix_scaled).flatten()
        
        # Normalize to [0, 1]
        quality_index = (quality_index - quality_index.min()) / (quality_index.max() - quality_index.min())
        
        return pd.Series(quality_index, index=self.data.index)


class IdentificationDiagnostics:
    """
    Comprehensive diagnostics for identification strategies.
    
    Validates the credibility of identification assumptions and
    provides robustness checks for alternative explanations testing.
    """
    
    def __init__(self):
        pass
        
    def run_identification_diagnostics(self,
                                     data: pd.DataFrame,
                                     identification_strategy: IdentificationStrategy,
                                     **kwargs) -> Dict[str, Any]:
        """
        Run comprehensive identification diagnostics.
        
        Returns detailed assessment of identification credibility.
        """
        
        diagnostics = {
            'strategy': identification_strategy.value,
            'data_summary': self._summarize_data(data),
            'identification_checks': {},
            'robustness_tests': {},
            'validity_assessment': {}
        }
        
        if identification_strategy == IdentificationStrategy.INSTRUMENTAL_VARIABLES:
            diagnostics['identification_checks'] = self._check_iv_identification(data, **kwargs)
            
        elif identification_strategy == IdentificationStrategy.PROXY_VARIABLES:
            diagnostics['identification_checks'] = self._check_proxy_identification(data, **kwargs)
            
        # General robustness tests
        diagnostics['robustness_tests'] = self._run_robustness_tests(data)
        
        # Overall validity assessment
        diagnostics['validity_assessment'] = self._assess_overall_validity(diagnostics)
        
        return diagnostics
        
    def _summarize_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Summarize data characteristics relevant for identification."""
        
        return {
            'n_observations': len(data),
            'n_markets': data['market_id'].nunique() if 'market_id' in data.columns else None,
            'n_commodities': data['commodity'].nunique() if 'commodity' in data.columns else None,
            'time_span': {
                'start': data['date'].min() if 'date' in data.columns else None,
                'end': data['date'].max() if 'date' in data.columns else None,
                'n_periods': data['date'].nunique() if 'date' in data.columns else None
            },
            'missing_data_rate': data.isnull().mean().mean()
        }
        
    def _check_iv_identification(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """Check IV identification assumptions."""
        
        return {
            'instrument_relevance': 'Would test first-stage F-statistics',
            'exclusion_restriction': 'Would test overidentification',
            'monotonicity': 'Would test LATE assumptions',
            'independence': 'Would test instrument exogeneity'
        }
        
    def _check_proxy_identification(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """Check proxy variable identification assumptions."""
        
        return {
            'proxy_relevance': 'Would test correlation with unobservable',
            'measurement_error': 'Would bound attenuation bias',
            'monotonicity': 'Would test proxy validity',
            'completeness': 'Would test proxy sufficiency'
        }
        
    def _run_robustness_tests(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Run general robustness tests."""
        
        return {
            'sample_stability': 'Would test across subsamples',
            'specification_sensitivity': 'Would test across specifications',
            'outlier_influence': 'Would test outlier sensitivity',
            'temporal_stability': 'Would test across time periods'
        }
        
    def _assess_overall_validity(self, diagnostics: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall validity of identification strategy."""
        
        return {
            'credibility_score': 0.8,  # Would calculate based on tests
            'main_concerns': [],
            'recommended_robustness': [],
            'confidence_level': 'medium'
        }