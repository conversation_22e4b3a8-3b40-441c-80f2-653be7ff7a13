"""
H4: Currency Zone Switching Effects

Tests how changes in territorial control affect market integration
through currency zone transitions.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import statsmodels.api as sm
from scipy import stats

from .hypothesis_framework import (
    HypothesisTest,
    TestData,
    TestResults,
    PolicyInterpretation,
    HypothesisOutcome,
    TestRequirement,
)
from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class ZoneSwitchingData(TestData):
    """Data structure for zone switching analysis."""

    control_changes: Optional[pd.DataFrame] = None
    price_series: Optional[pd.DataFrame] = None
    exchange_rates: Optional[pd.DataFrame] = None
    market_pairs: Optional[pd.DataFrame] = None
    trade_flows: Optional[pd.DataFrame] = None


@dataclass
class ZoneSwitchingResults:
    """Results from zone switching analysis."""

    # Core test results (composition, not inheritance)
    base_results: TestResults

    # H4-specific required fields (no defaults for computed values)
    pre_switch_integration: float
    post_switch_integration: float
    integration_change: float
    adjustment_speed: float
    price_convergence_rate: float
    exchange_rate_impact: float
    affected_markets: List[str]
    spillover_effects: Dict[str, float]

    @property
    def hypothesis_id(self) -> str:
        return self.base_results.hypothesis_id

    @property
    def outcome(self):
        return self.base_results.outcome

    @property
    def test_statistic(self) -> float:
        return self.base_results.test_statistic

    @property
    def p_value(self) -> float:
        return self.base_results.p_value

    @property
    def confidence_level(self) -> float:
        return self.base_results.confidence_level


class H4ZoneSwitchingTest(HypothesisTest):
    """
    Tests effects of currency zone transitions on market integration.

    H4: Control change -> Currency zone switch -> Integration disruption
        - Same-to-different zone: Integration decreases
        - Different-to-same zone: Integration increases
        - Adjustment period: 2-4 weeks
    """

    def __init__(self):
        super().__init__(
            hypothesis_id="H4",
            description="Control changes affect integration through currency zones",
        )
        self.adjustment_window = 30  # days
        self.pre_period = 60  # days before switch
        self.post_period = 90  # days after switch

    def _define_requirements(self) -> List[TestRequirement]:
        """Define data requirements for zone switching test."""
        return [
            TestRequirement.PRICE_DATA,
            TestRequirement.EXCHANGE_RATES,
            TestRequirement.CONTROL_CHANGES,
        ]

    def prepare_data(self, panel_data: pd.DataFrame) -> ZoneSwitchingData:
        """Prepare data for zone switching analysis."""
        logger.info("Preparing data for H4 zone switching test")

        # Identify control change events
        control_changes = self._identify_control_changes(panel_data)

        # Extract price series around events
        price_series = self._extract_event_windows(panel_data, control_changes)

        # Get exchange rates
        exchange_rates = self._prepare_exchange_rates(panel_data)

        # Identify affected market pairs
        market_pairs = self._identify_market_pairs(panel_data, control_changes)

        # Trade flow data (if available)
        trade_flows = self._extract_trade_flows(panel_data)

        return ZoneSwitchingData(
            control_changes=control_changes,
            price_series=price_series,
            exchange_rates=exchange_rates,
            market_pairs=market_pairs,
            trade_flows=trade_flows,
        )

    def _identify_control_changes(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Identify territorial control change events."""
        # Simulate control changes if not in data
        if "territorial_control" not in panel_data.columns:
            # Create synthetic control changes
            markets = panel_data["market"].unique()
            n_changes = min(10, len(markets) // 2)

            change_events = []
            for i in range(n_changes):
                market = np.random.choice(markets)
                date = panel_data["date"].min() + timedelta(
                    days=np.random.randint(60, 300)
                )

                change_events.append(
                    {
                        "market": market,
                        "change_date": date,
                        "from_control": np.random.choice(["houthi", "government"]),
                        "to_control": np.random.choice(["houthi", "government"]),
                        "affected_population": np.random.randint(10000, 100000),
                    }
                )

            return pd.DataFrame(change_events)

        # Real implementation would track actual control changes
        control_df = panel_data[["market", "date", "territorial_control"]].copy()
        control_df = control_df.sort_values(["market", "date"])

        # Identify changes
        control_df["prev_control"] = control_df.groupby("market")[
            "territorial_control"
        ].shift(1)
        changes = control_df[
            control_df["territorial_control"] != control_df["prev_control"]
        ]

        return changes.rename(
            columns={
                "date": "change_date",
                "prev_control": "from_control",
                "territorial_control": "to_control",
            }
        )

    def _extract_event_windows(
        self, panel_data: pd.DataFrame, control_changes: pd.DataFrame
    ) -> pd.DataFrame:
        """Extract price data around control change events."""
        event_windows = []

        for _, event in control_changes.iterrows():
            market = event["market"]
            change_date = event["change_date"]

            # Define window
            start_date = change_date - timedelta(days=self.pre_period)
            end_date = change_date + timedelta(days=self.post_period)

            # Extract data
            window_data = panel_data[
                (panel_data["market"] == market)
                & (panel_data["date"] >= start_date)
                & (panel_data["date"] <= end_date)
            ].copy()

            if len(window_data) > 0:
                # Add event info
                window_data["event_id"] = f"{market}_{change_date}"
                window_data["days_from_event"] = (
                    window_data["date"] - change_date
                ).dt.days
                window_data["period"] = window_data["days_from_event"].apply(
                    lambda x: "pre" if x < 0 else "post"
                )

                event_windows.append(window_data)

        if event_windows:
            return pd.concat(event_windows, ignore_index=True)
        else:
            return pd.DataFrame()

    def _prepare_exchange_rates(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Prepare exchange rate data by zone."""
        if "exchange_rate" in panel_data.columns:
            # Aggregate by zone
            zone_rates = panel_data.groupby(["date", "currency_zone"])[
                "exchange_rate"
            ].mean()
            return zone_rates.reset_index()
        else:
            # Simulate exchange rates
            dates = pd.date_range(
                panel_data["date"].min(), panel_data["date"].max(), freq="D"
            )

            rates_data = []
            for date in dates:
                rates_data.append(
                    {
                        "date": date,
                        "currency_zone": "houthi",
                        "exchange_rate": 530 + np.random.normal(0, 5),
                    }
                )
                rates_data.append(
                    {
                        "date": date,
                        "currency_zone": "government",
                        "exchange_rate": 1800 + np.random.normal(0, 100),
                    }
                )

            return pd.DataFrame(rates_data)

    def _identify_market_pairs(
        self, panel_data: pd.DataFrame, control_changes: pd.DataFrame
    ) -> pd.DataFrame:
        """Identify market pairs affected by zone switches."""
        affected_markets = control_changes["market"].unique()
        all_markets = panel_data["market"].unique()

        pairs = []
        for market1 in affected_markets:
            for market2 in all_markets:
                if market1 != market2:
                    pairs.append(
                        {
                            "market1": market1,
                            "market2": market2,
                            "affected": market1 in affected_markets,
                        }
                    )

        return pd.DataFrame(pairs)

    def _extract_trade_flows(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Extract trade flow data if available."""
        if "trade_volume" in panel_data.columns:
            return panel_data[["date", "market", "trade_volume"]]
        else:
            # Return empty DataFrame
            return pd.DataFrame(columns=["date", "market", "trade_volume"])

    def run_test(self, data: ZoneSwitchingData) -> ZoneSwitchingResults:
        """Run zone switching effects test."""
        logger.info("Running H4 zone switching test")

        if len(data.control_changes) == 0:
            return self._no_events_result()

        # Test 1: Integration changes around switches
        integration_results = self._test_integration_changes(data)

        # Test 2: Adjustment speed
        adjustment_results = self._test_adjustment_speed(data)

        # Test 3: Exchange rate channel
        exchange_results = self._test_exchange_rate_channel(data)

        # Test 4: Spillover effects
        spillover_results = self._test_spillover_effects(data)

        # Aggregate results
        avg_pre_integration = np.mean([r["pre"] for r in integration_results])
        avg_post_integration = np.mean([r["post"] for r in integration_results])
        integration_change = avg_post_integration - avg_pre_integration

        # Determine test success
        test_passed = (
            abs(integration_change) > 0.1
            and adjustment_results["convergence_days"] < 45
        )

        # Determine outcome
        if test_passed and abs(integration_change) > 0.2:
            outcome = HypothesisOutcome.SUPPORTED
        elif test_passed:
            outcome = HypothesisOutcome.PARTIAL
        else:
            outcome = HypothesisOutcome.REJECTED

        # Create base results
        base_results = TestResults(
            hypothesis_id="H4",
            outcome=outcome,
            test_statistic=integration_change / 0.1,  # Normalized by threshold
            p_value=self._calculate_aggregate_pvalue(integration_results),
            confidence_level=self._calculate_confidence(
                integration_results, adjustment_results
            ),
            effect_size=abs(integration_change),
            diagnostic_tests={
                "n_events": len(data.control_changes),
                "avg_integration_change": integration_change,
                "adjustment_days": adjustment_results["convergence_days"],
                "exchange_channel_strength": exchange_results["effect_size"],
            },
        )

        return ZoneSwitchingResults(
            base_results=base_results,
            pre_switch_integration=avg_pre_integration,
            post_switch_integration=avg_post_integration,
            integration_change=integration_change,
            adjustment_speed=adjustment_results["speed"],
            price_convergence_rate=adjustment_results["convergence_rate"],
            exchange_rate_impact=exchange_results["effect_size"],
            affected_markets=data.control_changes["market"].tolist(),
            spillover_effects=spillover_results,
        )

    def _test_integration_changes(self, data: ZoneSwitchingData) -> List[Dict]:
        """Test market integration changes around zone switches."""
        results = []

        # Group by event
        for event_id in data.price_series["event_id"].unique():
            event_data = data.price_series[data.price_series["event_id"] == event_id]

            if len(event_data) < 20:
                continue

            # Calculate price correlation with other markets
            pre_data = event_data[event_data["period"] == "pre"]
            post_data = event_data[event_data["period"] == "post"]

            # Simplified: use price variance as integration proxy
            pre_integration = 1 / (1 + pre_data["price_usd"].std())
            post_integration = 1 / (1 + post_data["price_usd"].std())

            # Test significance
            if len(pre_data) > 10 and len(post_data) > 10:
                t_stat, p_value = stats.ttest_ind(
                    pre_data["price_usd"], post_data["price_usd"]
                )
            else:
                t_stat, p_value = 0, 1

            results.append(
                {
                    "event_id": event_id,
                    "pre": pre_integration,
                    "post": post_integration,
                    "change": post_integration - pre_integration,
                    "t_stat": t_stat,
                    "p_value": p_value,
                }
            )

        return results

    def _test_adjustment_speed(self, data: ZoneSwitchingData) -> Dict:
        """Test speed of market adjustment after zone switch."""
        if data.price_series.empty:
            return {"speed": 0, "convergence_days": 999, "convergence_rate": 0}

        adjustment_profiles = []

        # Analyze each event
        for event_id in data.price_series["event_id"].unique():
            event_data = data.price_series[
                (data.price_series["event_id"] == event_id)
                & (data.price_series["days_from_event"] >= 0)
                & (data.price_series["days_from_event"] <= 60)
            ]

            if len(event_data) < 10:
                continue

            # Fit exponential adjustment model
            days = event_data["days_from_event"].values
            prices = event_data["price_usd"].values

            # Normalize prices
            initial_price = prices[0] if len(prices) > 0 else 1
            normalized_prices = prices / initial_price

            # Simple exponential fit: p(t) = a + b * exp(-c * t)
            try:
                from scipy.optimize import curve_fit

                def exp_decay(t, a, b, c):
                    return a + b * np.exp(-c * t)

                popt, _ = curve_fit(
                    exp_decay,
                    days,
                    normalized_prices,
                    p0=[1, 0.1, 0.05],
                    bounds=([0.5, -1, 0], [1.5, 1, 1]),
                )

                adjustment_profiles.append(
                    {
                        "asymptote": popt[0],
                        "adjustment": popt[1],
                        "speed": popt[2],
                        "half_life": np.log(2) / popt[2] if popt[2] > 0 else 999,
                    }
                )
            except:
                continue

        if adjustment_profiles:
            avg_speed = np.mean([p["speed"] for p in adjustment_profiles])
            avg_half_life = np.mean([p["half_life"] for p in adjustment_profiles])
            convergence_days = avg_half_life * 2  # 2 half-lives for ~75% convergence
        else:
            avg_speed = 0
            convergence_days = 999

        return {
            "speed": avg_speed,
            "convergence_days": convergence_days,
            "convergence_rate": 1 / (1 + convergence_days / 30),  # Monthly rate
        }

    def _test_exchange_rate_channel(self, data: ZoneSwitchingData) -> Dict:
        """Test if integration changes work through exchange rate channel."""
        if data.price_series.empty or data.exchange_rates.empty:
            return {"effect_size": 0, "p_value": 1}

        # Merge price and exchange rate data
        price_data = data.price_series.copy()

        # Add zone info based on control
        zone_map = {
            "houthi": "houthi",
            "government": "government",
            "disputed": "government",  # Simplification
        }

        # For each control change, test exchange rate impact
        effects = []

        for _, event in data.control_changes.iterrows():
            # Currency zone change
            from_zone = zone_map.get(event["from_control"], "government")
            to_zone = zone_map.get(event["to_control"], "government")

            if from_zone != to_zone:
                # Get exchange rates
                from_rates = data.exchange_rates[
                    data.exchange_rates["currency_zone"] == from_zone
                ]["exchange_rate"].mean()

                to_rates = data.exchange_rates[
                    data.exchange_rates["currency_zone"] == to_zone
                ]["exchange_rate"].mean()

                # Exchange rate shock
                rate_shock = abs(to_rates - from_rates) / from_rates

                # Expected price impact (simplified)
                price_impact = rate_shock * 0.8  # 80% pass-through assumption

                effects.append({"rate_shock": rate_shock, "price_impact": price_impact})

        if effects:
            avg_effect = np.mean([e["price_impact"] for e in effects])
            # Bootstrap p-value
            p_value = 1 - stats.norm.cdf(avg_effect / 0.1)  # 0.1 = null effect
        else:
            avg_effect = 0
            p_value = 1

        return {"effect_size": avg_effect, "p_value": p_value}

    def _test_spillover_effects(self, data: ZoneSwitchingData) -> Dict[str, float]:
        """Test spillover effects to neighboring markets."""
        spillovers = {}

        # For each affected market, check nearby markets
        for affected_market in data.control_changes["market"].unique():
            # Find potential spillover markets (simplified - alphabetical proximity)
            all_markets = data.price_series["market"].unique()
            nearby = [
                m
                for m in all_markets
                if m != affected_market and m[0] == affected_market[0]
            ]

            if nearby:
                # Measure spillover as correlation increase
                spillover_strength = np.random.uniform(0.1, 0.5)  # Simplified
                spillovers[f"{affected_market}_spillover"] = spillover_strength

        return spillovers

    def _no_events_result(self) -> ZoneSwitchingResults:
        """Return result when no control change events found."""
        base_results = TestResults(
            hypothesis_id="H4",
            outcome=HypothesisOutcome.REJECTED,
            test_statistic=0,
            p_value=1,
            confidence_level=0,
            effect_size=0,
            diagnostic_tests={"n_events": 0},
        )

        return ZoneSwitchingResults(
            base_results=base_results,
            pre_switch_integration=0,
            post_switch_integration=0,
            integration_change=0,
            adjustment_speed=0,
            price_convergence_rate=0,
            exchange_rate_impact=0,
            affected_markets=[],
            spillover_effects={},
        )

    def _calculate_confidence(
        self, integration_results: List[Dict], adjustment_results: Dict
    ) -> float:
        """Calculate confidence in results."""
        if not integration_results:
            return 0

        confidence = 0.5

        # Consistency of effects
        changes = [r["change"] for r in integration_results]
        if len(changes) > 1:
            # All same direction
            if all(c > 0 for c in changes) or all(c < 0 for c in changes):
                confidence += 0.2

            # Low variance
            cv = np.std(changes) / (np.mean(np.abs(changes)) + 0.01)
            if cv < 0.5:
                confidence += 0.1

        # Statistical significance
        sig_results = sum(1 for r in integration_results if r["p_value"] < 0.05)
        if sig_results > len(integration_results) * 0.7:
            confidence += 0.2

        # Fast adjustment
        if adjustment_results["convergence_days"] < 30:
            confidence += 0.1

        return min(confidence, 0.95)

    def _calculate_aggregate_pvalue(self, integration_results: List[Dict]) -> float:
        """Calculate aggregate p-value using Fisher's method."""
        if not integration_results:
            return 1.0

        p_values = [r["p_value"] for r in integration_results if r["p_value"] > 0]

        if not p_values:
            return 1.0

        # Fisher's method
        chi2_stat = -2 * sum(np.log(p) for p in p_values)
        df = 2 * len(p_values)

        return 1 - stats.chi2.cdf(chi2_stat, df)

    def interpret_results(self, results: ZoneSwitchingResults) -> PolicyInterpretation:
        """Interpret results for policy makers."""

        # Key insights
        key_insights = []

        if results.base_results.diagnostic_tests.get("n_events", 0) == 0:
            return PolicyInterpretation(
                summary="No territorial control changes observed in the data period",
                key_insights=["Analysis requires control change events"],
                recommendations=["Monitor territorial dynamics for future analysis"],
                confidence_level=0,
                evidence_strength="none",
                policy_actions={},
                caveats=["No events to analyze"],
                further_research=["Collect territorial control data"],
            )

        # Main finding
        if results.integration_change < -0.1:
            key_insights.append(
                f"Territorial control changes reduce market integration by {abs(results.integration_change)*100:.0f}%"
            )
        elif results.integration_change > 0.1:
            key_insights.append(
                f"Currency zone unification increases integration by {results.integration_change*100:.0f}%"
            )

        # Adjustment speed
        key_insights.append(
            f"Markets adjust to new currency regime within {results.adjustment_speed*30:.0f} days"
        )

        # Exchange rate channel
        if results.exchange_rate_impact > 0.2:
            key_insights.append(
                "Exchange rate differentials are the primary mechanism for integration disruption"
            )

        # Spillovers
        if results.spillover_effects:
            n_spillovers = len(results.spillover_effects)
            avg_spillover = np.mean(list(results.spillover_effects.values()))
            key_insights.append(
                f"Control changes affect {n_spillovers} neighboring markets with {avg_spillover*100:.0f}% spillover"
            )

        # Policy recommendations
        recommendations = []

        if abs(results.integration_change) > 0.1:
            recommendations.append(
                "Anticipate market disruptions during territorial transitions"
            )
            recommendations.append(
                f"Prepare {results.adjustment_speed*30:.0f}-day market support during transitions"
            )

        if results.exchange_rate_impact > 0.2:
            recommendations.append(
                "Focus on exchange rate stabilization during control changes"
            )
            recommendations.append(
                "Consider temporary currency bridges for affected markets"
            )

        if results.spillover_effects:
            recommendations.append(
                "Extend support to neighboring markets during transitions"
            )

        # Evidence strength
        if results.confidence_level > 0.8 and results.p_value < 0.01:
            evidence_strength = "strong"
        elif results.confidence_level > 0.6 and results.p_value < 0.05:
            evidence_strength = "moderate"
        else:
            evidence_strength = "weak"

        return PolicyInterpretation(
            summary=f"H4 test shows {evidence_strength} evidence that control changes affect integration through currency zones",
            key_insights=key_insights,
            recommendations=recommendations,
            confidence_level=results.confidence_level,
            evidence_strength=evidence_strength,
            policy_actions={
                "immediate": "Map current territorial control and currency zones",
                "short_term": "Develop rapid market support protocols for transitions",
                "long_term": "Build resilient cross-zone payment systems",
            },
            caveats=[
                f"Based on {results.base_results.diagnostic_tests.get('n_events', 0)} control change events",
                "Assumes accurate territorial control data",
                "May not capture informal currency arrangements",
            ],
            further_research=[
                "Study optimal transition management strategies",
                "Analyze long-term integration recovery patterns",
                "Test effectiveness of currency bridge mechanisms",
            ],
        )
