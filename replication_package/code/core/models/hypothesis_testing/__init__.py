"""
Hypothesis testing framework for Yemen market integration.

This module implements tests for all research hypotheses (H1-H10, S1, N1, P1).
"""

from .hypothesis_framework import (
    HypothesisTest,
    TestData,
    TestResults, 
    PolicyInterpretation,
    HypothesisRegistry,
    HypothesisOutcome,
    TestRequirement
)
from .specification_curve import (
    SpecificationCurve,
    SpecificationChoice,
    SpecificationResult,
    create_standard_specifications
)
from .bias_detection import (
    BiasDetector,
    AnalysisRecord,
    PreAnalysisPlan,
    create_pre_analysis_plan
)
from .pre_registration import (
    requires_preregistration,
    register_analysis_plan,
    get_registered_plan,
    DegreesOfFreedomTracker,
    track_degrees_of_freedom,
    PreRegistrationError
)
from .h1_exchange_rate import H1ExchangeRateMechanism
from .h2_aid_distribution import H2AidDistributionTest
from .h3_demand_destruction import H3DemandDestructionTest
from .h4_zone_switching import H4ZoneSwitchingTest
from .h5_cross_border import H5CrossBorderArbitrageTest
from .h6_currency_substitution import H6CurrencySubstitutionTest
from .h7_aid_effectiveness import H7AidEffectivenessTest
from .h8_information_spillover import H8InformationSpilloverTest
from .h9_threshold_effects import H9ThresholdEffectsTest
from .h10_convergence import H10ConvergenceTest
from .s1_spatial_boundaries import S1SpatialBoundariesTest
from .n1_network_density import N1NetworkDensityTest
from .p1_political_economy import P1PoliticalEconomyTest

__all__ = [
    # Framework classes
    'HypothesisTest',
    'TestData',
    'TestResults',
    'PolicyInterpretation', 
    'HypothesisRegistry',
    'HypothesisOutcome',
    'TestRequirement',
    # Bias prevention
    'SpecificationCurve',
    'SpecificationChoice',
    'SpecificationResult',
    'create_standard_specifications',
    'BiasDetector',
    'AnalysisRecord',
    'PreAnalysisPlan',
    'create_pre_analysis_plan',
    # Pre-registration
    'requires_preregistration',
    'register_analysis_plan',
    'get_registered_plan',
    'DegreesOfFreedomTracker',
    'track_degrees_of_freedom',
    'PreRegistrationError',
    # Hypothesis tests
    'H1ExchangeRateMechanism',
    'H2AidDistributionTest',
    'H3DemandDestructionTest',
    'H4ZoneSwitchingTest',
    'H5CrossBorderArbitrageTest',
    'H6CurrencySubstitutionTest',
    'H7AidEffectivenessTest',
    'H8InformationSpilloverTest',
    'H9ThresholdEffectsTest',
    'H10ConvergenceTest',
    'S1SpatialBoundariesTest',
    'N1NetworkDensityTest',
    'P1PoliticalEconomyTest'
]

# Register all hypothesis tests
registry = HypothesisRegistry()
registry.register(H1ExchangeRateMechanism())
registry.register(H2AidDistributionTest())
registry.register(H3DemandDestructionTest())
registry.register(H4ZoneSwitchingTest())
registry.register(H5CrossBorderArbitrageTest())
registry.register(H6CurrencySubstitutionTest())
registry.register(H7AidEffectivenessTest())
registry.register(H8InformationSpilloverTest())
registry.register(H9ThresholdEffectsTest())
registry.register(H10ConvergenceTest())
registry.register(S1SpatialBoundariesTest())
registry.register(N1NetworkDensityTest())
registry.register(P1PoliticalEconomyTest())