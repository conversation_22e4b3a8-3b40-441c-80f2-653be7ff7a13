"""Fixed effects panel data models."""

from typing import Dict, List, Optional

import numpy as np
import pandas as pd

from ..interfaces import Model, ModelSpecification


class FixedEffectsModel(Model):
    """
    Entity fixed effects panel data model.
    
    Includes entity-specific intercepts to control for time-invariant
    unobserved heterogeneity.
    """
    
    @property
    def name(self) -> str:
        """Model name."""
        return "Fixed Effects Model"
    
    @property
    def required_data_structure(self) -> str:
        """Required data structure."""
        return "panel"
    
    def __init__(self, specification: ModelSpecification):
        """Initialize fixed effects model."""
        super().__init__(specification)
        
        # Extract panel-specific parameters
        self.entity_var = specification.parameters.get("entity_var", "market")
        self.time_var = specification.parameters.get("time_var", "date")
        
        # Standard error options
        self.se_type = specification.parameters.get("se_type", "clustered")
        self.cluster_var = specification.parameters.get("cluster_var", self.entity_var)
        
        # Driscoll-<PERSON>raay for cross-sectional dependence
        self.use_driscoll_kraay = specification.parameters.get("driscoll_kraay", False)
        self.dk_bandwidth = specification.parameters.get("dk_bandwidth", 3)
    
    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate panel data for fixed effects."""
        errors = []
        
        # Basic panel validation
        required_cols = [
            self.entity_var,
            self.time_var,
            self.specification.dependent_variable
        ] + self.specification.independent_variables
        
        missing_cols = set(required_cols) - set(data.columns)
        if missing_cols:
            errors.append(f"Missing columns: {missing_cols}")
        
        # Check panel structure
        if self.entity_var in data.columns:
            n_entities = data[self.entity_var].nunique()
            if n_entities < 2:
                errors.append("Fixed effects requires at least 2 entities")
            
            # Check within variation
            for var in [self.specification.dependent_variable] + self.specification.independent_variables:
                if var in data.columns:
                    # Calculate within-entity variation
                    entity_means = data.groupby(self.entity_var)[var].transform('mean')
                    within_var = (data[var] - entity_means).var()
                    if within_var < 1e-10:
                        errors.append(f"No within-entity variation in {var}")
        
        # Minimum observations per entity
        if not errors:
            obs_per_entity = data.groupby(self.entity_var).size()
            if obs_per_entity.min() < 2:
                errors.append("Some entities have less than 2 observations")
        
        return errors
    
    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for fixed effects estimation."""
        prepared = data.copy()
        
        # Sort by entity and time
        prepared = prepared.sort_values([self.entity_var, self.time_var])
        
        # Set panel index
        prepared = prepared.set_index([self.entity_var, self.time_var])
        
        # Add time trends if specified
        if self.specification.parameters.get("entity_trends", False):
            # Entity-specific time trends
            entity_ids = prepared.index.get_level_values(0).unique()
            for i, entity in enumerate(entity_ids):
                mask = prepared.index.get_level_values(0) == entity
                prepared.loc[mask, f"trend_{entity}"] = np.arange(mask.sum())
        
        # Create interaction terms if specified
        interactions = self.specification.parameters.get("interactions", [])
        for var1, var2 in interactions:
            if var1 in prepared.columns and var2 in prepared.columns:
                prepared[f"{var1}_x_{var2}"] = prepared[var1] * prepared[var2]
        
        # Handle unbalanced panel
        if self.specification.parameters.get("balance_panel", False):
            # Keep only entities present in all periods
            entity_counts = prepared.groupby(level=0).size()
            max_periods = entity_counts.max()
            balanced_entities = entity_counts[entity_counts == max_periods].index
            prepared = prepared.loc[prepared.index.get_level_values(0).isin(balanced_entities)]
        
        return prepared
    
    def get_diagnostics(self) -> List[str]:
        """Get applicable diagnostic tests for fixed effects."""
        return [
            "heteroskedasticity",
            "serial_correlation",
            "cross_sectional_dependence",
            "hausman_test",  # FE vs RE
            "time_fixed_effects_test"
        ]


class TwoWayFixedEffectsModel(FixedEffectsModel):
    """
    Two-way fixed effects model with both entity and time fixed effects.
    
    Controls for both entity-specific and time-specific unobserved factors.
    """
    
    @property
    def name(self) -> str:
        """Model name."""
        return "Two-Way Fixed Effects Model"
    
    def __init__(self, specification: ModelSpecification):
        """Initialize two-way fixed effects model."""
        super().__init__(specification)
        
        # Force Driscoll-Kraay standard errors for two-way FE
        # due to likely cross-sectional dependence
        if not self.use_driscoll_kraay:
            self.use_driscoll_kraay = True
    
    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate data for two-way fixed effects."""
        errors = super().validate_data(data)
        
        # Additional checks for time dimension
        if self.time_var in data.columns:
            n_periods = data[self.time_var].nunique()
            if n_periods < 2:
                errors.append("Two-way FE requires at least 2 time periods")
        
        return errors
    
    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for two-way fixed effects."""
        prepared = super().prepare_data(data)
        
        # Add global time trends if specified
        if self.specification.parameters.get("global_trend", False):
            # Map time periods to integers
            time_mapping = {
                time: i for i, time in enumerate(
                    sorted(prepared.index.get_level_values(1).unique())
                )
            }
            prepared["trend"] = prepared.index.get_level_values(1).map(time_mapping)
        
        # Add pre/post indicators if treatment date specified
        treatment_date = self.specification.parameters.get("treatment_date")
        if treatment_date:
            prepared["post"] = (
                prepared.index.get_level_values(1) >= pd.to_datetime(treatment_date)
            ).astype(int)
        
        return prepared
    
    def get_diagnostics(self) -> List[str]:
        """Get diagnostic tests for two-way FE."""
        diagnostics = super().get_diagnostics()
        
        # Add specific tests for two-way FE
        diagnostics.extend([
            "joint_significance_time_effects",
            "common_trends_test"  # For DiD-type specifications
        ])
        
        return diagnostics