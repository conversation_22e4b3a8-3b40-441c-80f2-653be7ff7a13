"""
Wrapper for Bayesian Panel Model to integrate with three-tier framework.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional

from .bayesian_panel import BayesianPanelModel, BayesianPanelResults
from ..interfaces import Model
from ...core.utils.logging import get_logger

logger = get_logger(__name__)


class BayesianPanelModelWrapper(Model):
    """
    Wrapper class that adapts BayesianPanelModel to the Model interface.

    This enables Bayesian models to be used in the three-tier analysis framework
    alongside frequentist methods like OLS and IFE.
    """

    def __init__(self, specification):
        """Initialize with model specification."""
        self.specification = specification

        # Extract Bayesian-specific parameters
        self.model_type = specification.parameters.get("model_type", "hierarchical")
        self.robust = specification.parameters.get("robust", True)
        self.structural_breaks = specification.parameters.get("structural_breaks", True)
        self.zone_heterogeneity = specification.parameters.get(
            "zone_heterogeneity", True
        )
        self.n_chains = specification.parameters.get("n_chains", 4)
        self.n_samples = specification.parameters.get("n_samples", 2000)
        self.n_tune = specification.parameters.get("n_tune", 1000)
        self.target_accept = specification.parameters.get("target_accept", 0.9)

        # Panel structure parameters
        self.entity_var = specification.parameters.get("entity_var", "market_id")
        self.time_var = specification.parameters.get("time_var", "date")

        # Create underlying Bayesian model
        self._bayes_model = BayesianPanelModel(
            model_type=self.model_type,
            robust=self.robust,
            structural_breaks=self.structural_breaks,
            zone_heterogeneity=self.zone_heterogeneity,
            n_chains=self.n_chains,
            n_samples=self.n_samples,
            n_tune=self.n_tune,
            target_accept=self.target_accept,
        )

        self._is_fitted = False
        self._results = None

    @property
    def name(self) -> str:
        return f"Bayesian {self.model_type.title()} Panel Model"

    @property
    def required_data_structure(self) -> str:
        return "panel"

    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate data for Bayesian panel model."""
        errors = []

        # Check for required columns
        required_columns = [
            self.entity_var,
            self.time_var,
        ] + self.specification.independent_variables
        required_columns.append(self.specification.dependent_variable)

        missing_cols = [col for col in required_columns if col not in data.columns]
        if missing_cols:
            errors.append(f"Missing required columns: {missing_cols}")

        # Currency zone required for zone heterogeneity
        if self.zone_heterogeneity and "currency_zone" not in data.columns:
            errors.append("Zone heterogeneity requires 'currency_zone' column")

        # Minimum data requirements
        if len(data) < 100:
            errors.append(f"Insufficient observations: {len(data)} (minimum 100)")

        if self.entity_var in data.columns:
            n_entities = data[self.entity_var].nunique()
            if n_entities < 5:
                errors.append(f"Insufficient entities: {n_entities} (minimum 5)")

        if self.time_var in data.columns:
            n_periods = data[self.time_var].nunique()
            if n_periods < 12:
                errors.append(f"Insufficient time periods: {n_periods} (minimum 12)")

        # Additional Bayesian model validation
        bayes_errors = self._bayes_model.validate_data(data)
        errors.extend(bayes_errors)

        return errors

    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for Bayesian estimation."""
        # Ensure we have the dependent and independent variables
        required_cols = [
            self.specification.dependent_variable
        ] + self.specification.independent_variables
        required_cols.extend([self.entity_var, self.time_var])

        if "currency_zone" in data.columns:
            required_cols.append("currency_zone")

        if "exchange_rate_used" in data.columns:
            required_cols.append("exchange_rate_used")

        # Add conflict intensity if available
        if "conflict_intensity" in data.columns:
            required_cols.append("conflict_intensity")

        # Select and prepare data
        df = data[required_cols].copy()

        # Rename dependent variable for consistency
        if self.specification.dependent_variable != "price_usd":
            df["price_usd"] = df[self.specification.dependent_variable]

        # Ensure exchange rate is available
        if (
            "log_exchange_rate" in self.specification.independent_variables
            and "exchange_rate_used" not in df.columns
        ):
            # Try to infer from log
            df["exchange_rate_used"] = np.exp(df["log_exchange_rate"])

        return df

    def fit(self, data: pd.DataFrame) -> None:
        """Fit the Bayesian panel model."""
        logger.info(f"Fitting {self.name}")

        # Prepare data
        prepared_data = self.prepare_data(data)

        # Fit Bayesian model
        self._results = self._bayes_model.fit(prepared_data)
        self._is_fitted = True

        logger.info(
            f"Bayesian model fitted: converged={self._results.converged}, "
            f"LOO={self._results.loo:.2f}"
        )

    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """Generate predictions using the fitted model."""
        if not self._is_fitted:
            raise ValueError("Model must be fitted before prediction")

        prepared_data = self.prepare_data(data)
        return self._bayes_model.predict(prepared_data)

    def get_results_dict(self) -> Dict[str, Any]:
        """Get results in dictionary format for compatibility."""
        if not self._is_fitted or self._results is None:
            raise ValueError("Model must be fitted first")

        results = self._results

        # Extract key coefficients
        coefficients = {}
        standard_errors = {}
        t_statistics = {}
        p_values = {}

        # Map Bayesian parameters to standard names
        param_mapping = {
            "beta_exchange": (
                self.specification.independent_variables[0]
                if len(self.specification.independent_variables) > 0
                else "exchange_rate"
            ),
            "beta_conflict": "conflict_intensity",
            "beta_exchange_base": (
                self.specification.independent_variables[0]
                if len(self.specification.independent_variables) > 0
                else "exchange_rate"
            ),
        }

        for bayes_param, std_param in param_mapping.items():
            if bayes_param in results.posterior_means:
                coefficients[std_param] = results.posterior_means[bayes_param]
                standard_errors[std_param] = results.posterior_sds[bayes_param]

                # Calculate t-statistic equivalent (posterior mean / sd)
                t_statistics[std_param] = (
                    coefficients[std_param] / standard_errors[std_param]
                )

                # Calculate "p-value" as probability that parameter has wrong sign
                # This is a Bayesian equivalent, not a true p-value
                if coefficients[std_param] > 0:
                    p_values[std_param] = 2 * min(
                        0.5,
                        len(
                            [
                                x
                                for x in results.trace.posterior[
                                    bayes_param
                                ].values.flatten()
                                if x < 0
                            ]
                        )
                        / len(results.trace.posterior[bayes_param].values.flatten()),
                    )
                else:
                    p_values[std_param] = 2 * min(
                        0.5,
                        len(
                            [
                                x
                                for x in results.trace.posterior[
                                    bayes_param
                                ].values.flatten()
                                if x > 0
                            ]
                        )
                        / len(results.trace.posterior[bayes_param].values.flatten()),
                    )

        # Model fit metrics (Bayesian equivalents)
        # Pseudo R-squared based on posterior predictive performance
        r_squared = 1 - (1 / (1 + np.exp(-results.loo)))  # Rough approximation
        adjusted_r_squared = r_squared  # Bayesian models handle complexity differently

        return {
            "coefficients": coefficients,
            "standard_errors": standard_errors,
            "t_statistics": t_statistics,
            "p_values": p_values,
            "r_squared": r_squared,
            "adjusted_r_squared": adjusted_r_squared,
            "n_observations": results.n_observations,
            "converged": results.converged,
            "loo": results.loo,
            "waic": results.waic,
            "r_hat": results.r_hat,
            "zone_effects": results.zone_effects,
            "heterogeneity_measures": results.heterogeneity_measures,
            "posterior_intervals": results.posterior_intervals,
            "model_type": self.model_type,
            "robust": self.robust,
            "structural_breaks": self.structural_breaks,
            "zone_heterogeneity": self.zone_heterogeneity,
        }

    def get_diagnostics(self) -> List[str]:
        """Get list of available diagnostics."""
        return [
            "convergence_check",
            "effective_sample_size",
            "posterior_predictive_check",
            "leave_one_out_cv",
            "widely_applicable_ic",
            "zone_heterogeneity_test",
            "structural_break_detection",
        ]

    @property
    def is_fitted(self) -> bool:
        return self._is_fitted

    @property
    def results(self) -> Optional[BayesianPanelResults]:
        return self._results
