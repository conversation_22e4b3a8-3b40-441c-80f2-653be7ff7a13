"""Interactive Fixed Effects wrapper for Tier 1 integration."""

from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np

from ..interfaces import Model, ModelSpecification
from .interactive_fixed_effects import (
    InteractiveFixedEffectsModel,
    IFEResults,
    estimate_optimal_factors,
)
from ...validation.methodology_validator import MethodologyValidator, AnalysisType
from ...utils.logging import get_logger


logger = get_logger(__name__)


class InteractiveFixedEffectsModelWrapper(Model):
    """
    Wrapper to integrate Interactive Fixed Effects with the three-tier framework.

    This model captures time-varying unobserved heterogeneity through factor structures,
    critical for Yemen's fragmented market where common shocks (e.g., Ramadan, conflicts)
    affect currency zones differently.
    """

    @property
    def name(self) -> str:
        """Model name."""
        return "Interactive Fixed Effects Model"

    @property
    def required_data_structure(self) -> str:
        """Required data structure."""
        return "panel"

    def __init__(self, specification: ModelSpecification):
        """Initialize IFE wrapper."""
        super().__init__(specification)

        # Extract IFE-specific parameters
        self.entity_var = specification.parameters.get("entity_var", "market_id")
        self.time_var = specification.parameters.get("time_var", "date")
        self.n_factors = specification.parameters.get("n_factors", 3)
        self.auto_select_factors = specification.parameters.get(
            "auto_select_factors", False
        )
        self.max_factors = specification.parameters.get("max_factors", 10)
        self.standardize = specification.parameters.get("standardize", True)
        self.weights_var = specification.parameters.get("weights_var", None)

        # Initialize the underlying IFE model
        self._ife_model = InteractiveFixedEffectsModel(
            n_factors=self.n_factors, standardize=self.standardize
        )

        # Store results
        self._ife_results: Optional[IFEResults] = None
        self._prepared_data: Optional[pd.DataFrame] = None

    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate panel data structure for IFE."""
        errors = []

        # Check required columns
        required_cols = [
            self.entity_var,
            self.time_var,
            self.specification.dependent_variable,
        ] + self.specification.independent_variables

        missing_cols = set(required_cols) - set(data.columns)
        if missing_cols:
            errors.append(f"Missing columns: {missing_cols}")

        # Check panel structure
        if self.entity_var not in data.columns or self.time_var not in data.columns:
            errors.append("Panel structure variables not found")

        # Check for missing values (IFE requires balanced panel)
        panel_vars = [
            self.entity_var,
            self.time_var,
            self.specification.dependent_variable,
        ] + self.specification.independent_variables

        if data[panel_vars].isnull().any().any():
            errors.append("IFE requires balanced panel without missing values")

        # Check minimum panel dimensions
        if self.entity_var in data.columns and self.time_var in data.columns:
            n_entities = data[self.entity_var].nunique()
            n_times = data[self.time_var].nunique()

            if n_entities < 10:
                errors.append(f"IFE requires at least 10 entities, found {n_entities}")

            if n_times < 20:
                errors.append(f"IFE requires at least 20 time periods, found {n_times}")

            # Check factor feasibility
            min_dim = min(n_entities, n_times)
            if self.n_factors > min_dim:
                errors.append(
                    f"Number of factors ({self.n_factors}) exceeds minimum dimension ({min_dim})"
                )

        # Validate currency conversion (critical for Yemen analysis)
        if "price_usd" in data.columns and "currency_zone" in data.columns:
            validator = MethodologyValidator()
            is_valid, report = validator.validate_analysis_inputs(
                observations=data,
                analysis_type=AnalysisType.PANEL_ANALYSIS,
                hypothesis_tests=[],
            )
            if not is_valid:
                errors.extend(report.critical_failures)

        return errors

    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for IFE estimation."""
        # Create a copy to avoid modifying original
        df = data.copy()

        # Ensure panel is sorted
        df = df.sort_values([self.entity_var, self.time_var])

        # Create panel index
        df = df.set_index([self.entity_var, self.time_var])

        # Check if panel is balanced
        entities = df.index.get_level_values(0).unique()
        times = df.index.get_level_values(1).unique()
        expected_obs = len(entities) * len(times)

        if len(df) != expected_obs:
            logger.warning(
                f"Unbalanced panel detected: {len(df)} obs vs {expected_obs} expected"
            )
            # Balance the panel by forward-filling missing observations
            full_index = pd.MultiIndex.from_product(
                [entities, times], names=[self.entity_var, self.time_var]
            )
            df = df.reindex(full_index)

            # For numeric columns, forward fill then backward fill
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            df[numeric_cols] = (
                df[numeric_cols]
                .groupby(level=0)
                .fillna(method="ffill")
                .fillna(method="bfill")
            )

            # Drop any remaining rows with missing values
            df = df.dropna()

        # Store prepared data for later use
        self._prepared_data = df

        return df

    def get_diagnostics(self) -> List[str]:
        """Get list of diagnostic tests for IFE."""
        return [
            "factor_significance",
            "factor_stability",
            "residual_serial_correlation",
            "residual_cross_sectional_dependence",
            "model_specification_test",
        ]

    def fit(self, data: pd.DataFrame) -> "InteractiveFixedEffectsModelWrapper":
        """
        Fit the IFE model.

        Args:
            data: Prepared panel data

        Returns:
            Self for method chaining
        """
        # Auto-select optimal number of factors if requested
        if self.auto_select_factors:
            logger.info("Auto-selecting optimal number of factors")

            # Extract X and y
            X = data[self.specification.independent_variables]
            y = data[self.specification.dependent_variable]

            optimal_factors = estimate_optimal_factors(
                X,
                y,
                max_factors=min(
                    self.max_factors,
                    min(
                        len(X.index.get_level_values(0).unique()),
                        len(X.index.get_level_values(1).unique()),
                    )
                    - 1,
                ),
            )

            logger.info(f"Optimal number of factors: {optimal_factors}")
            self._ife_model.n_factors = optimal_factors
            self.n_factors = optimal_factors

        # Extract features and target
        X = data[self.specification.independent_variables]
        y = data[self.specification.dependent_variable]

        # Get weights if specified
        weights = None
        if self.weights_var and self.weights_var in data.columns:
            weights = data[self.weights_var]

        # Fit IFE model
        logger.info(f"Fitting IFE with {self.n_factors} factors")
        self._ife_results = self._ife_model.fit(
            X=X,
            y=y,
            entity_var=self.entity_var,
            time_var=self.time_var,
            weights=weights,
        )

        self._is_fitted = True

        # Log results
        logger.info(
            f"IFE estimation complete: R² = {self._ife_results.r_squared:.4f}, "
            f"Factor contribution = {self._ife_results.factor_contribution:.4f}"
        )

        return self

    def get_results_dict(self) -> Dict[str, Any]:
        """Convert IFE results to dictionary format compatible with ModelResult."""
        if not self._is_fitted or self._ife_results is None:
            raise ValueError("Model must be fitted first")

        results = self._ife_results

        # Convert to expected format
        return {
            "coefficients": results.coefficients.to_dict(),
            "standard_errors": results.standard_errors.to_dict(),
            "t_statistics": results.t_statistics.to_dict(),
            "p_values": results.p_values.to_dict(),
            "r_squared": results.r_squared,
            "adjusted_r_squared": results.adjusted_r_squared,
            "n_observations": (
                len(self._prepared_data) if self._prepared_data is not None else 0
            ),
            "n_factors": results.n_factors,
            "converged": results.converged,
            "iterations": results.iterations,
            "factor_contribution": results.factor_contribution,
            "factors": results.factors.tolist(),  # Convert numpy array for serialization
            "loadings_shape": results.loadings.shape,  # Just store shape for diagnostics
            "residuals": results.residuals,
        }

    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """Generate predictions using fitted IFE model."""
        if not self._is_fitted:
            raise ValueError("Model must be fitted before prediction")

        # Prepare new data
        X_new = data[self.specification.independent_variables]

        # Use IFE prediction (without factor contribution for new data)
        predictions = self._ife_model.predict_with_factors(X_new)

        return predictions

    def get_factor_analysis(self) -> Dict[str, Any]:
        """Extract factor analysis results for interpretation."""
        if not self._is_fitted or self._ife_results is None:
            raise ValueError("Model must be fitted first")

        results = self._ife_results

        # Get time index from prepared data
        time_index = self._prepared_data.index.get_level_values(1).unique()

        # Extract seasonal patterns if time index is datetime
        seasonal_effects = {}
        if pd.api.types.is_datetime64_any_dtype(time_index):
            seasonal_effects = self._ife_model.extract_seasonal_effects(
                pd.DatetimeIndex(time_index), results.factors
            )

        return {
            "n_factors": results.n_factors,
            "factor_r_squared": results.factor_contribution,
            "factors": results.factors,
            "loadings": results.loadings,
            "seasonal_effects": seasonal_effects,
            "converged": results.converged,
        }
