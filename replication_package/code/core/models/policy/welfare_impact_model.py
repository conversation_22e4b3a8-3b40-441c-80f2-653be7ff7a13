"""Welfare impact assessment model for policy interventions."""

from typing import Dict, Any, List, Optional, Tuple
import numpy as np
import pandas as pd
from dataclasses import dataclass
from scipy.optimize import minimize

from ..interfaces import Model, ModelSpecification
from ...domain.shared.value_objects import Money


@dataclass
class WelfareImpact:
    """Results of welfare impact analysis."""
    consumer_surplus_change: Money
    producer_surplus_change: Money
    government_cost: Money
    deadweight_loss: Money
    net_welfare_change: Money
    distributional_effects: Dict[str, float]  # By income quintile
    beneficiary_count: int
    cost_per_beneficiary: Money


@dataclass
class PolicyIntervention:
    """Policy intervention specification."""
    type: str  # 'cash_transfer', 'price_subsidy', 'in_kind', 'infrastructure'
    target_markets: List[str]
    target_commodities: List[str]
    magnitude: float  # Amount or percentage
    duration_months: int
    targeting_criteria: Dict[str, Any]
    budget_constraint: Optional[Money] = None


class WelfareImpactModel(Model):
    """Model for assessing welfare impacts of policy interventions.
    
    Based on partial equilibrium framework with:
    - Marshallian demand curves
    - Supply elasticities
    - Market integration effects
    - Distributional analysis
    """
    
    def __init__(self, specification: ModelSpecification,
                 demand_elasticities: Dict[str, float],
                 supply_elasticities: Dict[str, float],
                 household_data: pd.DataFrame):
        """Initialize welfare impact model.
        
        Args:
            specification: Model specification
            demand_elasticities: Price elasticities of demand by commodity
            supply_elasticities: Price elasticities of supply by commodity
            household_data: Household consumption and income data
        """
        super().__init__(specification)
        self.demand_elasticities = demand_elasticities
        self.supply_elasticities = supply_elasticities
        self.household_data = household_data
        self._estimated = False
    
    def estimate_intervention_impact(self, 
                                   intervention: PolicyIntervention,
                                   baseline_prices: pd.DataFrame,
                                   market_integration: pd.DataFrame) -> WelfareImpact:
        """Estimate welfare impact of a policy intervention.
        
        Args:
            intervention: Policy intervention specification
            baseline_prices: Baseline price data
            market_integration: Market integration parameters from VECM
            
        Returns:
            WelfareImpact with detailed results
        """
        # Simulate price changes from intervention
        price_effects = self._simulate_price_changes(
            intervention, baseline_prices, market_integration
        )
        
        # Calculate welfare components
        cs_change = self._calculate_consumer_surplus_change(
            price_effects, intervention
        )
        
        ps_change = self._calculate_producer_surplus_change(
            price_effects, intervention
        )
        
        govt_cost = self._calculate_government_cost(
            intervention, price_effects
        )
        
        dwl = self._calculate_deadweight_loss(
            price_effects, intervention
        )
        
        # Distributional analysis
        dist_effects = self._analyze_distributional_effects(
            price_effects, intervention
        )
        
        # Net welfare
        net_welfare = cs_change + ps_change - govt_cost - dwl
        
        # Beneficiary analysis
        beneficiaries = self._count_beneficiaries(intervention)
        cost_per_ben = govt_cost / beneficiaries if beneficiaries > 0 else Money(0, "USD")
        
        self._estimated = True
        
        return WelfareImpact(
            consumer_surplus_change=cs_change,
            producer_surplus_change=ps_change,
            government_cost=govt_cost,
            deadweight_loss=dwl,
            net_welfare_change=net_welfare,
            distributional_effects=dist_effects,
            beneficiary_count=beneficiaries,
            cost_per_beneficiary=cost_per_ben
        )
    
    def optimize_intervention(self,
                            intervention_type: str,
                            objective: str,
                            constraints: Dict[str, Any],
                            baseline_prices: pd.DataFrame) -> PolicyIntervention:
        """Optimize intervention parameters given constraints.
        
        Args:
            intervention_type: Type of intervention
            objective: 'max_welfare', 'max_coverage', 'min_cost'
            constraints: Budget, political, logistical constraints
            baseline_prices: Baseline price data
            
        Returns:
            Optimal intervention specification
        """
        # Define optimization problem
        if objective == 'max_welfare':
            def objective_func(params):
                intervention = self._params_to_intervention(params, intervention_type)
                impact = self.estimate_intervention_impact(
                    intervention, baseline_prices, pd.DataFrame()
                )
                return -impact.net_welfare_change.amount  # Negative for minimization
        
        # Set up constraints
        constraint_funcs = self._setup_constraints(constraints)
        
        # Initial guess
        x0 = self._get_initial_params(intervention_type, constraints)
        
        # Optimize
        result = minimize(
            objective_func,
            x0,
            method='SLSQP',
            constraints=constraint_funcs
        )
        
        # Convert back to intervention
        return self._params_to_intervention(result.x, intervention_type)
    
    def _simulate_price_changes(self,
                              intervention: PolicyIntervention,
                              baseline_prices: pd.DataFrame,
                              market_integration: pd.DataFrame) -> pd.DataFrame:
        """Simulate equilibrium price changes from intervention."""
        price_changes = baseline_prices.copy()
        
        if intervention.type == 'cash_transfer':
            # Cash transfers increase demand
            for commodity in intervention.target_commodities:
                # Income effect on demand
                income_elasticity = self._get_income_elasticity(commodity)
                transfer_pct = intervention.magnitude / self._get_avg_income()
                demand_shift = income_elasticity * transfer_pct
                
                # New equilibrium price
                price_change = self._calculate_equilibrium_change(
                    demand_shift, 
                    self.demand_elasticities[commodity],
                    self.supply_elasticities[commodity]
                )
                
                price_changes[commodity] *= (1 + price_change)
        
        elif intervention.type == 'price_subsidy':
            # Direct price reduction
            for commodity in intervention.target_commodities:
                subsidy_rate = intervention.magnitude
                price_changes[commodity] *= (1 - subsidy_rate)
        
        # Apply market integration effects (spillovers)
        integrated_changes = self._apply_integration_effects(
            price_changes, market_integration
        )
        
        return integrated_changes
    
    def _calculate_consumer_surplus_change(self,
                                         price_changes: pd.DataFrame,
                                         intervention: PolicyIntervention) -> Money:
        """Calculate change in consumer surplus using linear approximation."""
        total_cs_change = 0
        
        for commodity in intervention.target_commodities:
            # Get consumption quantities from household data
            avg_consumption = self.household_data[f'{commodity}_consumption'].mean()
            
            # Price change
            p0 = price_changes[commodity].iloc[0]  # Baseline
            p1 = price_changes[commodity].iloc[-1]  # After intervention
            price_change_pct = (p1 - p0) / p0
            
            # Quantity change using demand elasticity
            elasticity = self.demand_elasticities[commodity]
            q0 = avg_consumption
            q1 = q0 * (1 + elasticity * price_change_pct)
            
            # Consumer surplus change (trapezoid rule)
            cs_change = -0.5 * (p1 - p0) * (q0 + q1)
            total_cs_change += cs_change
        
        return Money(total_cs_change, "YER")
    
    def _calculate_producer_surplus_change(self,
                                         price_changes: pd.DataFrame,
                                         intervention: PolicyIntervention) -> Money:
        """Calculate change in producer surplus."""
        total_ps_change = 0
        
        for commodity in intervention.target_commodities:
            # Get production quantities
            avg_production = self._estimate_local_production(commodity)
            
            # Price change
            p0 = price_changes[commodity].iloc[0]
            p1 = price_changes[commodity].iloc[-1]
            price_change_pct = (p1 - p0) / p0
            
            # Quantity change using supply elasticity
            elasticity = self.supply_elasticities[commodity]
            q0 = avg_production
            q1 = q0 * (1 + elasticity * price_change_pct)
            
            # Producer surplus change
            ps_change = 0.5 * (p1 - p0) * (q0 + q1)
            total_ps_change += ps_change
        
        return Money(total_ps_change, "YER")
    
    def _calculate_government_cost(self,
                                 intervention: PolicyIntervention,
                                 price_effects: pd.DataFrame) -> Money:
        """Calculate total government cost of intervention."""
        total_cost = 0
        
        if intervention.type == 'cash_transfer':
            # Direct transfer cost
            beneficiaries = self._count_beneficiaries(intervention)
            total_cost = intervention.magnitude * beneficiaries * intervention.duration_months
        
        elif intervention.type == 'price_subsidy':
            # Subsidy cost = subsidy rate × quantity consumed
            for commodity in intervention.target_commodities:
                subsidized_quantity = self._estimate_subsidized_consumption(
                    commodity, intervention
                )
                subsidy_per_unit = price_effects[commodity].iloc[0] * intervention.magnitude
                total_cost += subsidy_per_unit * subsidized_quantity * intervention.duration_months
        
        elif intervention.type == 'infrastructure':
            # Capital cost + maintenance
            total_cost = intervention.magnitude  # Lump sum investment
        
        return Money(total_cost, "YER")
    
    def _calculate_deadweight_loss(self,
                                  price_effects: pd.DataFrame,
                                  intervention: PolicyIntervention) -> Money:
        """Calculate deadweight loss from market distortions."""
        total_dwl = 0
        
        if intervention.type == 'price_subsidy':
            for commodity in intervention.target_commodities:
                # DWL from subsidy = 0.5 × subsidy × quantity change
                p0 = price_effects[commodity].iloc[0]
                subsidy = p0 * intervention.magnitude
                
                # Quantity distortion
                elasticity = self.demand_elasticities[commodity]
                q_change = self._estimate_quantity_change(commodity, -intervention.magnitude, elasticity)
                
                dwl = 0.5 * subsidy * abs(q_change)
                total_dwl += dwl
        
        return Money(total_dwl, "YER")
    
    def _analyze_distributional_effects(self,
                                      price_effects: pd.DataFrame,
                                      intervention: PolicyIntervention) -> Dict[str, float]:
        """Analyze impact by income quintile."""
        effects = {}
        
        # Group households by income quintile
        self.household_data['quintile'] = pd.qcut(
            self.household_data['income'], 
            q=5, 
            labels=['Q1', 'Q2', 'Q3', 'Q4', 'Q5']
        )
        
        for quintile in ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']:
            quintile_data = self.household_data[
                self.household_data['quintile'] == quintile
            ]
            
            # Calculate welfare change for this quintile
            if intervention.type == 'cash_transfer':
                # Benefit from transfer
                if self._is_targeted(quintile, intervention):
                    benefit = intervention.magnitude * intervention.duration_months
                else:
                    benefit = 0
                
                # Cost from any price increases
                cost = self._calculate_price_burden(quintile_data, price_effects)
                
                net_effect = benefit - cost
            
            else:
                # Just price effects
                net_effect = -self._calculate_price_burden(quintile_data, price_effects)
            
            # As percentage of income
            avg_income = quintile_data['income'].mean()
            effects[quintile] = (net_effect / avg_income) * 100
        
        return effects
    
    def _count_beneficiaries(self, intervention: PolicyIntervention) -> int:
        """Count number of beneficiary households."""
        eligible = self.household_data.copy()
        
        # Apply targeting criteria
        for criterion, value in intervention.targeting_criteria.items():
            if criterion == 'income_below':
                eligible = eligible[eligible['income'] < value]
            elif criterion == 'location':
                eligible = eligible[eligible['market_id'].isin(value)]
            elif criterion == 'household_size_above':
                eligible = eligible[eligible['household_size'] > value]
        
        return len(eligible)
    
    def _calculate_equilibrium_change(self,
                                    demand_shift: float,
                                    demand_elasticity: float,
                                    supply_elasticity: float) -> float:
        """Calculate equilibrium price change from demand shift."""
        # Using partial equilibrium framework
        # Price change = demand_shift / (supply_elasticity - demand_elasticity)
        return demand_shift / (abs(supply_elasticity) + abs(demand_elasticity))
    
    def _apply_integration_effects(self,
                                 direct_changes: pd.DataFrame,
                                 integration_params: pd.DataFrame) -> pd.DataFrame:
        """Apply market integration spillover effects."""
        # Simplified: Use integration parameters to propagate price changes
        integrated = direct_changes.copy()
        
        # This would use the VECM parameters to model spatial price transmission
        # For now, simplified implementation
        
        return integrated
    
    def _get_income_elasticity(self, commodity: str) -> float:
        """Get income elasticity of demand."""
        # These would come from demand system estimation
        elasticities = {
            'WHEAT': 0.7,
            'RICE': 0.8,
            'SUGAR': 0.5,
            'OIL': 0.6
        }
        return elasticities.get(commodity, 0.6)
    
    def _get_avg_income(self) -> float:
        """Get average household income."""
        return self.household_data['income'].mean()
    
    def _estimate_local_production(self, commodity: str) -> float:
        """Estimate local production quantity."""
        # Would use agricultural statistics
        # Simplified for now
        return self.household_data[f'{commodity}_consumption'].sum() * 0.3
    
    def _estimate_subsidized_consumption(self,
                                       commodity: str,
                                       intervention: PolicyIntervention) -> float:
        """Estimate quantity consumed under subsidy."""
        base_consumption = self.household_data[f'{commodity}_consumption'].sum()
        
        # Adjust for targeting
        if intervention.targeting_criteria:
            coverage = self._count_beneficiaries(intervention) / len(self.household_data)
            return base_consumption * coverage
        
        return base_consumption
    
    def _estimate_quantity_change(self,
                                commodity: str,
                                price_change_pct: float,
                                elasticity: float) -> float:
        """Estimate quantity change from price change."""
        base_quantity = self.household_data[f'{commodity}_consumption'].sum()
        return base_quantity * elasticity * price_change_pct
    
    def _is_targeted(self, quintile: str, intervention: PolicyIntervention) -> bool:
        """Check if income quintile is targeted."""
        if 'quintiles' in intervention.targeting_criteria:
            return quintile in intervention.targeting_criteria['quintiles']
        return True  # Universal if not specified
    
    def _calculate_price_burden(self,
                              household_data: pd.DataFrame,
                              price_effects: pd.DataFrame) -> float:
        """Calculate cost burden from price changes."""
        total_burden = 0
        
        for commodity in price_effects.columns:
            if f'{commodity}_consumption' in household_data.columns:
                consumption = household_data[f'{commodity}_consumption'].mean()
                price_change = price_effects[commodity].iloc[-1] - price_effects[commodity].iloc[0]
                burden = consumption * price_change
                total_burden += burden
        
        return total_burden
    
    def validate_data(self, data: pd.DataFrame) -> None:
        """Validate input data."""
        required_cols = ['income', 'household_size', 'market_id']
        missing = [col for col in required_cols if col not in data.columns]
        if missing:
            raise ValueError(f"Missing required columns: {missing}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information."""
        return {
            'name': 'Welfare Impact Model',
            'type': 'policy_analysis',
            'description': 'Assesses welfare impacts of policy interventions',
            'capabilities': [
                'consumer_surplus_calculation',
                'producer_surplus_calculation',
                'distributional_analysis',
                'intervention_optimization'
            ]
        }
    
    def is_estimated(self) -> bool:
        """Check if model has been estimated."""
        return self._estimated
    
    def _setup_constraints(self, constraints: Dict[str, Any]) -> List[Dict]:
        """Set up optimization constraints."""
        constraint_list = []
        
        if 'budget' in constraints:
            constraint_list.append({
                'type': 'ineq',
                'fun': lambda x: constraints['budget'] - x[0] * x[1]  # magnitude × duration
            })
        
        return constraint_list
    
    def _get_initial_params(self, 
                          intervention_type: str,
                          constraints: Dict[str, Any]) -> np.ndarray:
        """Get initial parameter guess for optimization."""
        # Simplified: [magnitude, duration]
        if 'budget' in constraints:
            # Start with 10% of budget for 6 months
            magnitude = constraints['budget'] * 0.1 / 6
            return np.array([magnitude, 6])
        
        return np.array([1000, 6])  # Default
    
    def _params_to_intervention(self,
                              params: np.ndarray,
                              intervention_type: str) -> PolicyIntervention:
        """Convert optimization parameters to intervention."""
        return PolicyIntervention(
            type=intervention_type,
            target_markets=['all'],
            target_commodities=['WHEAT', 'RICE'],
            magnitude=params[0],
            duration_months=int(params[1]),
            targeting_criteria={'quintiles': ['Q1', 'Q2']}
        )