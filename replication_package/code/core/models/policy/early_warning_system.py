"""Early warning system for food security crises and market disruptions."""

from typing import Dict, Any, List, Optional, Tuple
import numpy as np
import pandas as pd
from dataclasses import dataclass
from datetime import datetime, timedelta
from sklearn.ensemble import IsolationForest, RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import warnings

from ..interfaces import Model, ModelSpecification
from ...domain.shared.value_objects import AlertLevel


@dataclass
class CrisisIndicators:
    """Comprehensive crisis indicators."""
    price_spike_probability: float
    supply_disruption_risk: float
    food_security_phase: int  # IPC phase 1-5
    market_functionality_index: float  # 0-1 scale
    humanitarian_needs_projection: int  # Affected population
    confidence_interval: Tuple[float, float]


@dataclass
class EarlyWarningAlert:
    """Early warning alert specification."""
    alert_level: AlertLevel  # CRITICAL, HIGH, MEDIUM, LOW
    alert_type: str  # 'price_spike', 'supply_shock', 'conflict_escalation'
    affected_markets: List[str]
    affected_commodities: List[str]
    time_horizon: int  # Days until event
    probability: float
    recommended_actions: List[str]
    supporting_evidence: Dict[str, Any]


class EarlyWarningSystem(Model):
    """Early warning system for humanitarian crises.
    
    Combines multiple data sources and methods:
    - Anomaly detection for price spikes
    - Time series forecasting for price trends
    - ML classification for crisis prediction
    - Composite indicators for food security
    """
    
    def __init__(self, specification: ModelSpecification,
                 historical_crises: Optional[pd.DataFrame] = None):
        """Initialize early warning system.
        
        Args:
            specification: Model specification
            historical_crises: Historical crisis events for training
        """
        super().__init__(specification)
        self.historical_crises = historical_crises
        self.anomaly_detector = None
        self.price_forecaster = None
        self.crisis_classifier = None
        self.scaler = StandardScaler()
        self._is_trained = False
    
    def train(self, historical_data: pd.DataFrame) -> None:
        """Train early warning models on historical data.
        
        Args:
            historical_data: Historical price, conflict, and crisis data
        """
        # Prepare features
        features = self._engineer_features(historical_data)
        
        # Train anomaly detector
        self.anomaly_detector = IsolationForest(
            contamination=0.1,  # Expected 10% anomalies
            random_state=42
        )
        self.anomaly_detector.fit(features)
        
        # Train price forecaster
        self.price_forecaster = self._train_price_forecaster(historical_data)
        
        # Train crisis classifier if historical crises available
        if self.historical_crises is not None:
            self.crisis_classifier = self._train_crisis_classifier(
                features, self.historical_crises
            )
        
        self._is_trained = True
    
    def generate_alerts(self, current_data: pd.DataFrame,
                       forecast_horizon: int = 30) -> List[EarlyWarningAlert]:
        """Generate early warning alerts based on current conditions.
        
        Args:
            current_data: Current market and context data
            forecast_horizon: Days to forecast ahead
            
        Returns:
            List of early warning alerts
        """
        if not self._is_trained:
            raise ValueError("System must be trained before generating alerts")
        
        alerts = []
        
        # Check for price anomalies
        price_alerts = self._detect_price_anomalies(current_data)
        alerts.extend(price_alerts)
        
        # Forecast price trends
        forecast_alerts = self._forecast_price_spikes(current_data, forecast_horizon)
        alerts.extend(forecast_alerts)
        
        # Assess supply disruption risk
        supply_alerts = self._assess_supply_risks(current_data)
        alerts.extend(supply_alerts)
        
        # Calculate composite food security indicators
        fs_alerts = self._assess_food_security(current_data)
        alerts.extend(fs_alerts)
        
        # Deduplicate and prioritize alerts
        alerts = self._prioritize_alerts(alerts)
        
        return alerts
    
    def calculate_crisis_indicators(self, data: pd.DataFrame) -> CrisisIndicators:
        """Calculate comprehensive crisis indicators.
        
        Args:
            data: Current market and context data
            
        Returns:
            CrisisIndicators with all metrics
        """
        # Price spike probability
        spike_prob = self._calculate_spike_probability(data)
        
        # Supply disruption risk
        supply_risk = self._calculate_supply_risk(data)
        
        # IPC phase estimation
        ipc_phase = self._estimate_ipc_phase(data)
        
        # Market functionality
        mfi = self._calculate_market_functionality_index(data)
        
        # Humanitarian needs
        needs = self._project_humanitarian_needs(data, ipc_phase)
        
        # Confidence interval
        ci = self._calculate_confidence_interval(data)
        
        return CrisisIndicators(
            price_spike_probability=spike_prob,
            supply_disruption_risk=supply_risk,
            food_security_phase=ipc_phase,
            market_functionality_index=mfi,
            humanitarian_needs_projection=needs,
            confidence_interval=ci
        )
    
    def _engineer_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Engineer features for ML models."""
        features = pd.DataFrame()
        
        # Price features
        for commodity in data.select_dtypes(include=[np.number]).columns:
            if 'price' in commodity.lower():
                # Price level
                features[f'{commodity}_level'] = data[commodity]
                
                # Price volatility (rolling std)
                features[f'{commodity}_volatility'] = data[commodity].rolling(30).std()
                
                # Price momentum (rate of change)
                features[f'{commodity}_momentum'] = data[commodity].pct_change(7)
                
                # Price acceleration
                features[f'{commodity}_acceleration'] = features[f'{commodity}_momentum'].diff()
        
        # Seasonal indicators
        if 'date' in data.columns:
            dates = pd.to_datetime(data['date'])
            features['month'] = dates.dt.month
            features['is_ramadan'] = self._is_ramadan_period(dates)
            features['is_harvest'] = dates.dt.month.isin([3, 4, 9, 10])
        
        # Conflict features
        if 'conflict_events' in data.columns:
            features['conflict_intensity'] = data['conflict_events'].rolling(30).sum()
            features['conflict_trend'] = data['conflict_events'].rolling(30).mean().diff()
        
        # Market integration features (from VECM results)
        if 'integration_score' in data.columns:
            features['market_integration'] = data['integration_score']
            features['integration_change'] = data['integration_score'].diff()
        
        # Climate features
        if 'rainfall' in data.columns:
            features['rainfall_anomaly'] = (data['rainfall'] - data['rainfall'].rolling(365).mean()) / data['rainfall'].rolling(365).std()
            features['drought_indicator'] = (features['rainfall_anomaly'] < -1).astype(int)
        
        return features.fillna(method='ffill').fillna(0)
    
    def _detect_price_anomalies(self, data: pd.DataFrame) -> List[EarlyWarningAlert]:
        """Detect current price anomalies."""
        alerts = []
        features = self._engineer_features(data)
        
        # Standardize features
        features_scaled = self.scaler.fit_transform(features)
        
        # Detect anomalies
        anomaly_scores = self.anomaly_detector.decision_function(features_scaled[-1:])
        is_anomaly = self.anomaly_detector.predict(features_scaled[-1:])
        
        if is_anomaly[0] == -1:  # Anomaly detected
            # Identify which commodities are anomalous
            anomalous_commodities = []
            for col in features.columns:
                if 'price' in col and features[col].iloc[-1] > features[col].quantile(0.95):
                    commodity = col.split('_')[0]
                    anomalous_commodities.append(commodity)
            
            if anomalous_commodities:
                alert = EarlyWarningAlert(
                    alert_level=AlertLevel.HIGH,
                    alert_type='price_spike',
                    affected_markets=data['market_id'].unique().tolist() if 'market_id' in data else ['all'],
                    affected_commodities=anomalous_commodities,
                    time_horizon=0,  # Current
                    probability=abs(anomaly_scores[0]),
                    recommended_actions=[
                        'Investigate supply chain disruptions',
                        'Monitor hoarding behavior',
                        'Consider market stabilization measures',
                        'Prepare humanitarian response'
                    ],
                    supporting_evidence={
                        'anomaly_score': float(anomaly_scores[0]),
                        'affected_prices': {c: float(features[f'{c}_price_level'].iloc[-1]) 
                                          for c in anomalous_commodities if f'{c}_price_level' in features}
                    }
                )
                alerts.append(alert)
        
        return alerts
    
    def _forecast_price_spikes(self, data: pd.DataFrame, 
                             horizon: int) -> List[EarlyWarningAlert]:
        """Forecast potential price spikes."""
        alerts = []
        
        for commodity in ['WHEAT', 'RICE', 'SUGAR', 'OIL']:
            if f'{commodity}_price' in data.columns:
                # Simple forecast using recent trend
                recent_prices = data[f'{commodity}_price'].tail(30)
                trend = np.polyfit(range(len(recent_prices)), recent_prices, 1)[0]
                
                # Project forward
                current_price = recent_prices.iloc[-1]
                forecast_price = current_price + trend * horizon
                
                # Check if spike expected (>20% increase)
                price_increase = (forecast_price - current_price) / current_price
                
                if price_increase > 0.20:
                    spike_probability = min(0.9, price_increase * 2)  # Simple probability
                    
                    alert = EarlyWarningAlert(
                        alert_level=AlertLevel.MEDIUM if price_increase < 0.30 else AlertLevel.HIGH,
                        alert_type='price_spike',
                        affected_markets=['all'],
                        affected_commodities=[commodity],
                        time_horizon=horizon,
                        probability=spike_probability,
                        recommended_actions=[
                            f'Secure {commodity} supplies within {horizon//2} days',
                            'Negotiate forward contracts',
                            'Prepare price stabilization fund',
                            'Alert vulnerable households'
                        ],
                        supporting_evidence={
                            'current_price': float(current_price),
                            'forecast_price': float(forecast_price),
                            'expected_increase': float(price_increase),
                            'trend_strength': float(trend)
                        }
                    )
                    alerts.append(alert)
        
        return alerts
    
    def _assess_supply_risks(self, data: pd.DataFrame) -> List[EarlyWarningAlert]:
        """Assess supply chain disruption risks."""
        alerts = []
        
        # Check conflict escalation
        if 'conflict_events' in data.columns:
            recent_conflict = data['conflict_events'].tail(7).mean()
            historical_avg = data['conflict_events'].mean()
            
            if recent_conflict > historical_avg * 2:
                alert = EarlyWarningAlert(
                    alert_level=AlertLevel.HIGH,
                    alert_type='supply_shock',
                    affected_markets=self._identify_conflict_markets(data),
                    affected_commodities=['all'],
                    time_horizon=7,
                    probability=0.75,
                    recommended_actions=[
                        'Pre-position emergency supplies',
                        'Identify alternative supply routes',
                        'Coordinate with security actors',
                        'Prepare for market interventions'
                    ],
                    supporting_evidence={
                        'recent_conflict_rate': float(recent_conflict),
                        'historical_rate': float(historical_avg),
                        'escalation_factor': float(recent_conflict / historical_avg)
                    }
                )
                alerts.append(alert)
        
        # Check climate risks
        if 'rainfall_anomaly' in data.columns:
            if data['rainfall_anomaly'].iloc[-1] < -2:  # Severe drought
                alert = EarlyWarningAlert(
                    alert_level=AlertLevel.MEDIUM,
                    alert_type='supply_shock',
                    affected_markets=['rural'],
                    affected_commodities=['WHEAT', 'local_produce'],
                    time_horizon=60,
                    probability=0.80,
                    recommended_actions=[
                        'Import planning for affected commodities',
                        'Support local farmers',
                        'Prepare drought response',
                        'Monitor local production'
                    ],
                    supporting_evidence={
                        'rainfall_anomaly': float(data['rainfall_anomaly'].iloc[-1]),
                        'drought_duration': int((data['rainfall_anomaly'] < -1).sum())
                    }
                )
                alerts.append(alert)
        
        return alerts
    
    def _assess_food_security(self, data: pd.DataFrame) -> List[EarlyWarningAlert]:
        """Assess overall food security situation."""
        alerts = []
        
        # Calculate composite food security score
        fs_score = 0
        factors = 0
        
        # Price factor
        if 'food_basket_cost' in data.columns:
            basket_increase = data['food_basket_cost'].pct_change(30).iloc[-1]
            if basket_increase > 0.15:
                fs_score += 1
            if basket_increase > 0.30:
                fs_score += 1
            factors += 1
        
        # Access factor
        if 'market_functionality_index' in data.columns:
            if data['market_functionality_index'].iloc[-1] < 0.5:
                fs_score += 1
            if data['market_functionality_index'].iloc[-1] < 0.3:
                fs_score += 1
            factors += 1
        
        # Availability factor
        if 'stock_levels' in data.columns:
            if data['stock_levels'].iloc[-1] < data['stock_levels'].quantile(0.25):
                fs_score += 1
            factors += 1
        
        # Calculate IPC-like phase
        ipc_phase = min(5, int(1 + (fs_score / max(1, factors)) * 4))
        
        if ipc_phase >= 3:  # Crisis or worse
            alert = EarlyWarningAlert(
                alert_level=AlertLevel.CRITICAL if ipc_phase >= 4 else AlertLevel.HIGH,
                alert_type='food_security_crisis',
                affected_markets=['all'],
                affected_commodities=['food_basket'],
                time_horizon=0,
                probability=0.90,
                recommended_actions=[
                    'Activate humanitarian response',
                    'Scale up food assistance',
                    'Implement market support programs',
                    'Coordinate with UN agencies',
                    'Monitor malnutrition indicators'
                ],
                supporting_evidence={
                    'ipc_phase': ipc_phase,
                    'food_security_score': float(fs_score),
                    'contributing_factors': self._identify_fs_factors(data)
                }
            )
            alerts.append(alert)
        
        return alerts
    
    def _calculate_spike_probability(self, data: pd.DataFrame) -> float:
        """Calculate probability of price spike in next 30 days."""
        if self.crisis_classifier is not None:
            features = self._engineer_features(data)
            prob = self.crisis_classifier.predict_proba(features.tail(1))[0, 1]
            return float(prob)
        
        # Fallback to simple heuristic
        recent_volatility = data.select_dtypes(include=[np.number]).std().mean()
        historical_volatility = data.select_dtypes(include=[np.number]).std().mean()
        return min(0.9, recent_volatility / historical_volatility)
    
    def _calculate_supply_risk(self, data: pd.DataFrame) -> float:
        """Calculate supply disruption risk score."""
        risk_score = 0
        factors = 0
        
        # Conflict risk
        if 'conflict_events' in data.columns:
            conflict_trend = data['conflict_events'].rolling(7).mean().pct_change().iloc[-1]
            if conflict_trend > 0:
                risk_score += min(0.5, conflict_trend)
            factors += 1
        
        # Climate risk
        if 'rainfall_anomaly' in data.columns:
            if data['rainfall_anomaly'].iloc[-1] < -1:
                risk_score += 0.3
            factors += 1
        
        # Import dependency risk
        if 'import_share' in data.columns:
            risk_score += data['import_share'].iloc[-1] * 0.3
            factors += 1
        
        return risk_score / max(1, factors)
    
    def _estimate_ipc_phase(self, data: pd.DataFrame) -> int:
        """Estimate IPC food security phase (1-5)."""
        # Simplified IPC phase estimation
        indicators = []
        
        # Food consumption
        if 'food_consumption_score' in data.columns:
            fcs = data['food_consumption_score'].iloc[-1]
            if fcs < 21:
                indicators.append(5)  # Catastrophe
            elif fcs < 28:
                indicators.append(4)  # Emergency
            elif fcs < 42:
                indicators.append(3)  # Crisis
            elif fcs < 53:
                indicators.append(2)  # Stressed
            else:
                indicators.append(1)  # Minimal
        
        # Coping strategies
        if 'coping_strategy_index' in data.columns:
            csi = data['coping_strategy_index'].iloc[-1]
            if csi > 40:
                indicators.append(4)
            elif csi > 20:
                indicators.append(3)
            elif csi > 10:
                indicators.append(2)
            else:
                indicators.append(1)
        
        # Market prices
        if 'food_basket_cost' in data.columns:
            price_increase = data['food_basket_cost'].pct_change(90).iloc[-1]
            if price_increase > 0.50:
                indicators.append(4)
            elif price_increase > 0.30:
                indicators.append(3)
            elif price_increase > 0.15:
                indicators.append(2)
            else:
                indicators.append(1)
        
        return int(np.mean(indicators)) if indicators else 2
    
    def _calculate_market_functionality_index(self, data: pd.DataFrame) -> float:
        """Calculate market functionality index (0-1)."""
        score = 1.0
        
        # Price volatility impact
        if any('volatility' in col for col in data.columns):
            volatility_cols = [col for col in data.columns if 'volatility' in col]
            avg_volatility = data[volatility_cols].iloc[-1].mean()
            score *= max(0, 1 - avg_volatility / 100)
        
        # Supply availability
        if 'stock_levels' in data.columns:
            stock_ratio = data['stock_levels'].iloc[-1] / data['stock_levels'].mean()
            score *= min(1, stock_ratio)
        
        # Market access
        if 'markets_accessible' in data.columns:
            access_ratio = data['markets_accessible'].iloc[-1] / data['markets_accessible'].max()
            score *= access_ratio
        
        return float(score)
    
    def _project_humanitarian_needs(self, data: pd.DataFrame, 
                                  ipc_phase: int) -> int:
        """Project population in need of humanitarian assistance."""
        # Base population (would come from demographic data)
        base_population = 1000000  # Example for a region
        
        # IPC phase to needs mapping
        needs_percentage = {
            1: 0.00,   # Minimal
            2: 0.10,   # Stressed
            3: 0.30,   # Crisis
            4: 0.50,   # Emergency
            5: 0.80    # Catastrophe
        }
        
        affected_percentage = needs_percentage.get(ipc_phase, 0.20)
        
        # Adjust for urban/rural if available
        if 'urban_rural_ratio' in data.columns:
            # Rural areas typically more vulnerable
            rural_weight = 1 - data['urban_rural_ratio'].iloc[-1]
            affected_percentage *= (1 + rural_weight * 0.5)
        
        return int(base_population * affected_percentage)
    
    def _calculate_confidence_interval(self, data: pd.DataFrame) -> Tuple[float, float]:
        """Calculate confidence interval for predictions."""
        # Based on data quality and model uncertainty
        base_confidence = 0.80
        
        # Adjust for data completeness
        missing_ratio = data.isnull().sum().sum() / data.size
        confidence = base_confidence * (1 - missing_ratio)
        
        # Simple 95% CI
        margin = 1.96 * (1 - confidence)
        return (max(0, confidence - margin), min(1, confidence + margin))
    
    def _train_price_forecaster(self, data: pd.DataFrame) -> RandomForestRegressor:
        """Train price forecasting model."""
        # Simplified - would use more sophisticated time series models
        features = self._engineer_features(data)
        
        # Create lagged target
        target_col = 'WHEAT_price_level'  # Example
        if target_col in features.columns:
            y = features[target_col].shift(-30)  # 30-day ahead prediction
            X = features[:-30]
            y = y[:-30]
            
            # Remove NaN
            mask = ~(X.isnull().any(axis=1) | y.isnull())
            X = X[mask]
            y = y[mask]
            
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            model.fit(X, y)
            return model
        
        return None
    
    def _train_crisis_classifier(self, features: pd.DataFrame,
                               crisis_labels: pd.DataFrame) -> Any:
        """Train crisis classification model."""
        # Would implement proper crisis classification
        # Using historical crisis labels
        from sklearn.ensemble import RandomForestClassifier
        
        # Align features with crisis labels
        # ... implementation ...
        
        return RandomForestClassifier(n_estimators=100, random_state=42)
    
    def _is_ramadan_period(self, dates: pd.Series) -> pd.Series:
        """Check if dates fall in Ramadan period."""
        # Simplified - would use proper Islamic calendar
        ramadan_months = [3, 4]  # Approximate
        return dates.dt.month.isin(ramadan_months).astype(int)
    
    def _identify_conflict_markets(self, data: pd.DataFrame) -> List[str]:
        """Identify markets affected by conflict."""
        if 'market_id' in data.columns and 'conflict_events' in data.columns:
            # Group by market and find high conflict areas
            market_conflict = data.groupby('market_id')['conflict_events'].sum()
            threshold = market_conflict.quantile(0.75)
            return market_conflict[market_conflict > threshold].index.tolist()
        return ['conflict_affected']
    
    def _identify_fs_factors(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Identify contributing factors to food security situation."""
        factors = {}
        
        if 'food_basket_cost' in data.columns:
            factors['price_increase'] = float(data['food_basket_cost'].pct_change(30).iloc[-1])
        
        if 'conflict_events' in data.columns:
            factors['conflict_intensity'] = float(data['conflict_events'].tail(30).mean())
        
        if 'rainfall_anomaly' in data.columns:
            factors['climate_stress'] = float(data['rainfall_anomaly'].iloc[-1])
        
        return factors
    
    def _prioritize_alerts(self, alerts: List[EarlyWarningAlert]) -> List[EarlyWarningAlert]:
        """Prioritize and deduplicate alerts."""
        # Sort by alert level and probability
        priority_map = {
            AlertLevel.CRITICAL: 4,
            AlertLevel.HIGH: 3,
            AlertLevel.MEDIUM: 2,
            AlertLevel.LOW: 1
        }
        
        alerts.sort(key=lambda x: (priority_map.get(x.alert_level, 0), x.probability), 
                   reverse=True)
        
        # Remove duplicates (same type, markets, commodities)
        seen = set()
        unique_alerts = []
        for alert in alerts:
            key = (alert.alert_type, 
                   tuple(alert.affected_markets), 
                   tuple(alert.affected_commodities))
            if key not in seen:
                seen.add(key)
                unique_alerts.append(alert)
        
        return unique_alerts[:10]  # Top 10 alerts
    
    def validate_data(self, data: pd.DataFrame) -> None:
        """Validate input data for early warning system."""
        if len(data) < 30:
            raise ValueError("Need at least 30 days of data for early warning")
        
        # Check for price columns
        price_cols = [col for col in data.columns if 'price' in col.lower()]
        if not price_cols:
            raise ValueError("No price columns found in data")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information."""
        return {
            'name': 'Early Warning System',
            'type': 'predictive_monitoring',
            'description': 'ML-based early warning for food security crises',
            'capabilities': [
                'anomaly_detection',
                'price_forecasting', 
                'crisis_prediction',
                'risk_assessment'
            ],
            'alert_types': [
                'price_spike',
                'supply_shock',
                'food_security_crisis'
            ]
        }
    
    def is_estimated(self) -> bool:
        """Check if model is trained."""
        return self._is_trained