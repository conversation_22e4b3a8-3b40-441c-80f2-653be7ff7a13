"""
Humanitarian policy simulation models connecting cross-country validation findings
to operational decision-making for aid organizations.

Translates econometric results into actionable humanitarian programming guidance
across Yemen, Syria, Lebanon, Somalia, and Afghanistan contexts.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import warnings

from ...domain.shared.value_objects import Country, Money
from ..validation.cross_country_validation import CrossCountryResults
from ..validation.meta_analysis_framework import CrossCountryMetaAnalysis
from src.core.utils.logging import get_logger

logger = get_logger(__name__)


class AidModality(Enum):
    """Types of humanitarian aid modalities."""
    CASH_ASSISTANCE = "cash_assistance"
    VOUCHERS = "vouchers"
    IN_KIND = "in_kind"
    FOOD_ASSISTANCE = "food_assistance"
    LIVELIHOOD_SUPPORT = "livelihood_support"
    MARKET_SUPPORT = "market_support"


class InterventionType(Enum):
    """Types of humanitarian interventions."""
    EMERGENCY_RESPONSE = "emergency_response"
    RESILIENCE_BUILDING = "resilience_building"
    MARKET_STABILIZATION = "market_stabilization"
    EARLY_WARNING = "early_warning"
    CAPACITY_BUILDING = "capacity_building"


@dataclass
class PolicyScenario:
    """Definition of a policy scenario for simulation."""
    scenario_name: str
    country: Country
    intervention_type: InterventionType
    aid_modality: AidModality
    target_population: int
    budget_usd: float
    duration_months: int
    currency_zone: str
    implementation_assumptions: Dict[str, float]


@dataclass
class SimulationResult:
    """Results from policy scenario simulation."""
    scenario: PolicyScenario
    expected_outcomes: Dict[str, float]
    cost_effectiveness: float
    risk_factors: Dict[str, float]
    currency_adjustments: Dict[str, float]
    implementation_guidance: List[str]
    monitoring_indicators: List[str]
    confidence_interval: Tuple[float, float]


@dataclass
class HumanitarianPolicyRecommendations:
    """Comprehensive policy recommendations."""
    country: Country
    priority_interventions: List[Tuple[InterventionType, float]]  # (intervention, priority_score)
    modality_recommendations: Dict[str, str]  # zone -> recommended modality
    budget_allocations: Dict[str, float]  # zone -> optimal allocation
    timing_recommendations: Dict[str, str]
    risk_mitigation_strategies: List[str]
    coordination_requirements: List[str]
    monitoring_framework: Dict[str, List[str]]


class HumanitarianPolicySimulator:
    """
    Humanitarian policy simulator connecting econometric findings to operational decisions.
    
    Uses cross-country validation results to:
    1. Optimize aid modality selection by currency zone
    2. Calculate zone-specific transfer value adjustments
    3. Predict intervention effectiveness
    4. Generate early warning triggers
    5. Provide operational guidance
    """
    
    def __init__(self, meta_analysis: CrossCountryMetaAnalysis):
        """Initialize policy simulator with meta-analysis results."""
        self.meta_analysis = meta_analysis
        self.country_parameters = self._initialize_country_parameters()
        self.intervention_effectiveness = self._calculate_intervention_effectiveness()
        
        logger.info("Initialized humanitarian policy simulator")
    
    def _initialize_country_parameters(self) -> Dict[Country, Dict[str, float]]:
        """Initialize country-specific parameters for simulation."""
        return {
            Country.YEMEN: {
                'baseline_transfer_value': 100.0,  # USD per month per household
                'market_integration_baseline': 0.6,
                'currency_volatility': 0.8,
                'operational_capacity': 0.7,
                'access_constraints': 0.5,
                'inflation_adjustment': 1.2
            },
            Country.SYRIA: {
                'baseline_transfer_value': 120.0,
                'market_integration_baseline': 0.5,
                'currency_volatility': 0.9,
                'operational_capacity': 0.6,
                'access_constraints': 0.7,
                'inflation_adjustment': 1.4
            },
            Country.LEBANON: {
                'baseline_transfer_value': 150.0,
                'market_integration_baseline': 0.4,
                'currency_volatility': 1.2,
                'operational_capacity': 0.8,
                'access_constraints': 0.3,
                'inflation_adjustment': 2.5
            },
            Country.SOMALIA: {
                'baseline_transfer_value': 80.0,
                'market_integration_baseline': 0.3,
                'currency_volatility': 0.6,
                'operational_capacity': 0.5,
                'access_constraints': 0.8,
                'inflation_adjustment': 1.1
            },
            Country.AFGHANISTAN: {
                'baseline_transfer_value': 90.0,
                'market_integration_baseline': 0.4,
                'currency_volatility': 1.0,
                'operational_capacity': 0.4,
                'access_constraints': 0.9,
                'inflation_adjustment': 1.3
            }
        }
    
    def _calculate_intervention_effectiveness(self) -> Dict[Country, Dict[InterventionType, float]]:
        """Calculate intervention effectiveness scores by country."""
        effectiveness = {}
        
        for country in Country:
            if country in self.country_parameters:
                params = self.country_parameters[country]
                
                # Base effectiveness on market integration and operational capacity
                base_effectiveness = (params['market_integration_baseline'] + 
                                    params['operational_capacity']) / 2
                
                # Adjust by intervention type
                effectiveness[country] = {
                    InterventionType.EMERGENCY_RESPONSE: base_effectiveness * 0.9,
                    InterventionType.RESILIENCE_BUILDING: base_effectiveness * 0.7,
                    InterventionType.MARKET_STABILIZATION: base_effectiveness * 0.8,
                    InterventionType.EARLY_WARNING: base_effectiveness * 1.1,
                    InterventionType.CAPACITY_BUILDING: base_effectiveness * 0.6
                }
        
        return effectiveness
    
    def simulate_policy_scenario(self, scenario: PolicyScenario) -> SimulationResult:
        """
        Simulate outcomes for a specific policy scenario.
        
        Args:
            scenario: Policy scenario definition
            
        Returns:
            Simulation results with expected outcomes and guidance
        """
        logger.info(f"Simulating policy scenario: {scenario.scenario_name}")
        
        # Get country parameters
        country_params = self.country_parameters.get(scenario.country, {})
        if not country_params:
            raise ValueError(f"No parameters available for {scenario.country.value}")
        
        # Calculate currency adjustments based on fragmentation findings
        currency_adjustments = self._calculate_currency_adjustments(scenario, country_params)
        
        # Estimate intervention effectiveness
        base_effectiveness = self.intervention_effectiveness[scenario.country][scenario.intervention_type]
        
        # Adjust effectiveness based on modality and context
        modality_adjustment = self._get_modality_effectiveness(scenario.aid_modality, scenario.country)
        adjusted_effectiveness = base_effectiveness * modality_adjustment
        
        # Calculate expected outcomes
        expected_outcomes = self._calculate_expected_outcomes(
            scenario, country_params, adjusted_effectiveness, currency_adjustments
        )
        
        # Calculate cost-effectiveness
        cost_effectiveness = self._calculate_cost_effectiveness(scenario, expected_outcomes)
        
        # Assess risk factors
        risk_factors = self._assess_risk_factors(scenario, country_params)
        
        # Generate implementation guidance
        implementation_guidance = self._generate_implementation_guidance(
            scenario, currency_adjustments, risk_factors
        )
        
        # Define monitoring indicators
        monitoring_indicators = self._define_monitoring_indicators(scenario)
        
        # Calculate confidence intervals
        confidence_interval = self._calculate_confidence_interval(
            expected_outcomes, risk_factors, adjusted_effectiveness
        )
        
        return SimulationResult(
            scenario=scenario,
            expected_outcomes=expected_outcomes,
            cost_effectiveness=cost_effectiveness,
            risk_factors=risk_factors,
            currency_adjustments=currency_adjustments,
            implementation_guidance=implementation_guidance,
            monitoring_indicators=monitoring_indicators,
            confidence_interval=confidence_interval
        )
    
    def _calculate_currency_adjustments(self, 
                                      scenario: PolicyScenario, 
                                      country_params: Dict[str, float]) -> Dict[str, float]:
        """Calculate currency zone-specific adjustments."""
        adjustments = {}
        
        # Base adjustment from meta-analysis findings
        if 'fragmentation_detection' in self.meta_analysis.meta_results:
            fragmentation_effect = self.meta_analysis.meta_results['fragmentation_detection'].pooled_effect
        else:
            fragmentation_effect = 0.5  # Default moderate fragmentation
        
        # Zone-specific adjustments
        if scenario.currency_zone == 'depreciated_zone':
            # Higher transfer values needed in depreciated currency zones
            adjustments['transfer_value_multiplier'] = 1 + (fragmentation_effect * 0.5)
            adjustments['price_volatility_buffer'] = 0.2
            adjustments['exchange_rate_buffer'] = 0.15
        elif scenario.currency_zone == 'stable_zone':
            adjustments['transfer_value_multiplier'] = 1.0
            adjustments['price_volatility_buffer'] = 0.1
            adjustments['exchange_rate_buffer'] = 0.05
        else:  # mixed or unknown zone
            adjustments['transfer_value_multiplier'] = 1 + (fragmentation_effect * 0.3)
            adjustments['price_volatility_buffer'] = 0.15
            adjustments['exchange_rate_buffer'] = 0.1
        
        # Country-specific volatility adjustment
        volatility = country_params.get('currency_volatility', 0.8)
        adjustments['volatility_adjustment'] = 1 + (volatility * 0.1)
        
        return adjustments
    
    def _get_modality_effectiveness(self, modality: AidModality, country: Country) -> float:
        """Get modality-specific effectiveness adjustment."""
        # Effectiveness based on market integration levels and operational context
        country_params = self.country_parameters.get(country, {})
        market_integration = country_params.get('market_integration_baseline', 0.5)
        access_constraints = country_params.get('access_constraints', 0.5)
        
        modality_scores = {
            AidModality.CASH_ASSISTANCE: market_integration * (1 - access_constraints * 0.5),
            AidModality.VOUCHERS: market_integration * 0.8 * (1 - access_constraints * 0.3),
            AidModality.IN_KIND: 0.7 * (1 - access_constraints * 0.8),
            AidModality.FOOD_ASSISTANCE: 0.8 * (1 - access_constraints * 0.6),
            AidModality.LIVELIHOOD_SUPPORT: market_integration * 0.6,
            AidModality.MARKET_SUPPORT: market_integration * 1.2
        }
        
        return modality_scores.get(modality, 0.7)
    
    def _calculate_expected_outcomes(self, 
                                   scenario: PolicyScenario,
                                   country_params: Dict[str, float],
                                   effectiveness: float,
                                   currency_adjustments: Dict[str, float]) -> Dict[str, float]:
        """Calculate expected outcomes from intervention."""
        
        # Base outcomes calculation
        monthly_transfer = country_params['baseline_transfer_value'] * currency_adjustments['transfer_value_multiplier']
        total_transfers = monthly_transfer * scenario.target_population * scenario.duration_months
        
        outcomes = {
            'people_reached': scenario.target_population * effectiveness,
            'total_transfer_value_usd': total_transfers,
            'market_integration_improvement': effectiveness * 0.2,  # 20% max improvement
            'price_volatility_reduction': effectiveness * 0.15,     # 15% max reduction
            'welfare_improvement_index': effectiveness * 0.8,       # 0-1 scale
            'local_market_stimulus_usd': total_transfers * 0.7,     # 70% local multiplier
        }
        
        # Modality-specific outcomes
        if scenario.aid_modality == AidModality.CASH_ASSISTANCE:
            outcomes['cash_utilization_rate'] = 0.85 * effectiveness
            outcomes['market_access_improvement'] = 0.3 * effectiveness
        elif scenario.aid_modality == AidModality.MARKET_SUPPORT:
            outcomes['trader_participation_increase'] = 0.4 * effectiveness
            outcomes['market_infrastructure_improvement'] = 0.25 * effectiveness
        
        # Intervention-specific outcomes
        if scenario.intervention_type == InterventionType.EARLY_WARNING:
            outcomes['early_warning_accuracy'] = 0.7 + (effectiveness * 0.2)
            outcomes['response_time_improvement_days'] = 5 * effectiveness
        elif scenario.intervention_type == InterventionType.MARKET_STABILIZATION:
            outcomes['price_stability_improvement'] = 0.2 * effectiveness
            outcomes['cross_border_trade_increase'] = 0.15 * effectiveness
        
        return outcomes
    
    def _calculate_cost_effectiveness(self, 
                                    scenario: PolicyScenario, 
                                    outcomes: Dict[str, float]) -> float:
        """Calculate cost-effectiveness ratio."""
        people_reached = outcomes.get('people_reached', 1)
        welfare_improvement = outcomes.get('welfare_improvement_index', 0.1)
        
        # Cost per person reached with welfare improvement weighting
        cost_per_person = scenario.budget_usd / people_reached if people_reached > 0 else float('inf')
        effectiveness_per_dollar = (people_reached * welfare_improvement) / scenario.budget_usd
        
        return effectiveness_per_dollar
    
    def _assess_risk_factors(self, 
                           scenario: PolicyScenario, 
                           country_params: Dict[str, float]) -> Dict[str, float]:
        """Assess implementation risk factors."""
        
        base_risks = {
            'access_risk': country_params.get('access_constraints', 0.5),
            'currency_risk': country_params.get('currency_volatility', 0.5),
            'operational_risk': 1 - country_params.get('operational_capacity', 0.5),
            'market_disruption_risk': 0.3,  # Base market disruption risk
            'political_economy_risk': 0.4   # Base political economy risk
        }
        
        # Adjust risks based on intervention type
        if scenario.intervention_type == InterventionType.EMERGENCY_RESPONSE:
            base_risks['access_risk'] *= 1.2
            base_risks['operational_risk'] *= 0.8
        elif scenario.intervention_type == InterventionType.MARKET_STABILIZATION:
            base_risks['market_disruption_risk'] *= 1.3
            base_risks['political_economy_risk'] *= 1.1
        
        # Adjust risks based on modality
        if scenario.aid_modality == AidModality.CASH_ASSISTANCE:
            base_risks['currency_risk'] *= 1.2
        elif scenario.aid_modality == AidModality.IN_KIND:
            base_risks['access_risk'] *= 1.1
            base_risks['operational_risk'] *= 1.2
        
        return base_risks
    
    def _generate_implementation_guidance(self, 
                                        scenario: PolicyScenario,
                                        currency_adjustments: Dict[str, float],
                                        risk_factors: Dict[str, float]) -> List[str]:
        """Generate implementation guidance."""
        guidance = []
        
        # Currency guidance
        if currency_adjustments['transfer_value_multiplier'] > 1.1:
            guidance.append(
                f"Increase transfer values by {(currency_adjustments['transfer_value_multiplier'] - 1) * 100:.0f}% "
                f"in {scenario.currency_zone} to account for currency depreciation"
            )
        
        # Risk mitigation guidance
        if risk_factors['currency_risk'] > 0.7:
            guidance.append(
                "Implement frequent exchange rate monitoring and consider USD-denominated transfers"
            )
        
        if risk_factors['access_risk'] > 0.6:
            guidance.append(
                "Develop alternative delivery mechanisms and strengthen partner capacity"
            )
        
        # Modality-specific guidance
        if scenario.aid_modality == AidModality.CASH_ASSISTANCE:
            guidance.append(
                "Ensure financial service providers can handle volume and have adequate liquidity"
            )
            guidance.append(
                "Monitor market prices weekly to adjust transfer values as needed"
            )
        
        # Country-specific guidance
        if scenario.country == Country.LEBANON:
            guidance.append(
                "Consider multiple exchange rate exposure and banking sector limitations"
            )
        elif scenario.country == Country.AFGHANISTAN:
            guidance.append(
                "Account for banking system restrictions and cash liquidity constraints"
            )
        elif scenario.country == Country.SYRIA:
            guidance.append(
                "Coordinate across territorial control lines for currency zone consistency"
            )
        
        return guidance
    
    def _define_monitoring_indicators(self, scenario: PolicyScenario) -> List[str]:
        """Define key monitoring indicators."""
        indicators = [
            "Number of beneficiaries reached",
            "Total transfer value distributed",
            "Market price monitoring (weekly)",
            "Exchange rate tracking (daily)",
            "Beneficiary expenditure patterns",
            "Local market availability scores"
        ]
        
        # Intervention-specific indicators
        if scenario.intervention_type == InterventionType.MARKET_STABILIZATION:
            indicators.extend([
                "Cross-market price correlation coefficients",
                "Transport cost indices",
                "Trader participation rates"
            ])
        elif scenario.intervention_type == InterventionType.EARLY_WARNING:
            indicators.extend([
                "Alert accuracy rates",
                "Response time to warnings",
                "Predictive model performance"
            ])
        
        # Modality-specific indicators
        if scenario.aid_modality == AidModality.CASH_ASSISTANCE:
            indicators.extend([
                "Cash utilization rates",
                "ATM/payment point functionality",
                "Fee structures and charges"
            ])
        
        return indicators
    
    def _calculate_confidence_interval(self, 
                                     outcomes: Dict[str, float],
                                     risk_factors: Dict[str, float],
                                     effectiveness: float) -> Tuple[float, float]:
        """Calculate confidence interval for key outcome."""
        # Use welfare improvement as primary outcome
        point_estimate = outcomes.get('welfare_improvement_index', 0.5)
        
        # Uncertainty based on risk factors and effectiveness
        risk_adjustment = np.mean(list(risk_factors.values()))
        uncertainty = 0.2 + (risk_adjustment * 0.3)  # 20-50% uncertainty range
        
        lower_bound = max(0, point_estimate * (1 - uncertainty))
        upper_bound = min(1, point_estimate * (1 + uncertainty))
        
        return (lower_bound, upper_bound)
    
    def generate_country_recommendations(self, country: Country) -> HumanitarianPolicyRecommendations:
        """Generate comprehensive policy recommendations for a country."""
        logger.info(f"Generating policy recommendations for {country.value}")
        
        if country not in self.country_parameters:
            raise ValueError(f"No parameters available for {country.value}")
        
        country_params = self.country_parameters[country]
        
        # Priority interventions based on context
        priority_interventions = self._prioritize_interventions(country, country_params)
        
        # Modality recommendations by zone
        modality_recommendations = self._recommend_modalities_by_zone(country, country_params)
        
        # Budget allocation optimization
        budget_allocations = self._optimize_budget_allocation(country, country_params)
        
        # Timing recommendations
        timing_recommendations = self._generate_timing_recommendations(country)
        
        # Risk mitigation strategies
        risk_mitigation = self._generate_risk_mitigation_strategies(country, country_params)
        
        # Coordination requirements
        coordination_requirements = self._identify_coordination_requirements(country)
        
        # Monitoring framework
        monitoring_framework = self._design_monitoring_framework(country)
        
        return HumanitarianPolicyRecommendations(
            country=country,
            priority_interventions=priority_interventions,
            modality_recommendations=modality_recommendations,
            budget_allocations=budget_allocations,
            timing_recommendations=timing_recommendations,
            risk_mitigation_strategies=risk_mitigation,
            coordination_requirements=coordination_requirements,
            monitoring_framework=monitoring_framework
        )
    
    def _prioritize_interventions(self, 
                                country: Country, 
                                country_params: Dict[str, float]) -> List[Tuple[InterventionType, float]]:
        """Prioritize interventions based on country context."""
        # Get effectiveness scores
        effectiveness_scores = self.intervention_effectiveness.get(country, {})
        
        # Adjust priorities based on country-specific needs
        priorities = []
        
        for intervention, base_score in effectiveness_scores.items():
            # Context adjustments
            if country == Country.AFGHANISTAN and intervention == InterventionType.CAPACITY_BUILDING:
                adjusted_score = base_score * 1.3  # Higher priority post-2021
            elif country == Country.LEBANON and intervention == InterventionType.MARKET_STABILIZATION:
                adjusted_score = base_score * 1.2  # Banking crisis context
            elif country == Country.SYRIA and intervention == InterventionType.EARLY_WARNING:
                adjusted_score = base_score * 1.1  # Ongoing conflict
            else:
                adjusted_score = base_score
            
            priorities.append((intervention, adjusted_score))
        
        # Sort by priority score
        priorities.sort(key=lambda x: x[1], reverse=True)
        
        return priorities
    
    def _recommend_modalities_by_zone(self, 
                                    country: Country, 
                                    country_params: Dict[str, float]) -> Dict[str, str]:
        """Recommend aid modalities by currency zone."""
        market_integration = country_params['market_integration_baseline']
        access_constraints = country_params['access_constraints']
        
        recommendations = {}
        
        # Stable currency zones
        if market_integration > 0.6 and access_constraints < 0.4:
            recommendations['stable_zone'] = "Cash assistance - markets functioning well"
        elif market_integration > 0.4:
            recommendations['stable_zone'] = "Vouchers - moderate market functionality"
        else:
            recommendations['stable_zone'] = "In-kind assistance - limited market access"
        
        # Depreciated currency zones
        if market_integration > 0.5:
            recommendations['depreciated_zone'] = "USD cash or vouchers - protect against depreciation"
        else:
            recommendations['depreciated_zone'] = "In-kind assistance - market disruption likely"
        
        # Border/mixed zones
        recommendations['border_zone'] = "Flexible modalities - adapt to changing conditions"
        
        return recommendations
    
    def _optimize_budget_allocation(self, 
                                  country: Country, 
                                  country_params: Dict[str, float]) -> Dict[str, float]:
        """Optimize budget allocation across zones."""
        # Simplified allocation based on need and effectiveness
        fragmentation_level = 1 - country_params['market_integration_baseline']
        
        allocations = {
            'depreciated_zone': 0.4 + (fragmentation_level * 0.2),  # Higher allocation to affected zones
            'stable_zone': 0.3 - (fragmentation_level * 0.1),
            'border_zone': 0.2,
            'emergency_reserve': 0.1
        }
        
        # Ensure allocations sum to 1
        total = sum(allocations.values())
        allocations = {k: v/total for k, v in allocations.items()}
        
        return allocations
    
    def _generate_timing_recommendations(self, country: Country) -> Dict[str, str]:
        """Generate timing recommendations for interventions."""
        return {
            'emergency_response': 'Immediate - within 72 hours of trigger',
            'market_support': 'Medium-term - 2-3 months planning horizon',
            'early_warning': 'Continuous - real-time monitoring',
            'capacity_building': 'Long-term - 6-12 month programs'
        }
    
    def _generate_risk_mitigation_strategies(self, 
                                           country: Country, 
                                           country_params: Dict[str, float]) -> List[str]:
        """Generate risk mitigation strategies."""
        strategies = []
        
        if country_params['currency_volatility'] > 0.8:
            strategies.append("Implement real-time exchange rate monitoring and adjustment mechanisms")
            strategies.append("Consider USD-denominated programming where feasible")
        
        if country_params['access_constraints'] > 0.6:
            strategies.append("Develop multiple delivery channel partnerships")
            strategies.append("Invest in remote monitoring technologies")
        
        if country_params['operational_capacity'] < 0.6:
            strategies.append("Strengthen local partner capacity before scale-up")
            strategies.append("Implement phased expansion approach")
        
        # Country-specific strategies
        if country == Country.LEBANON:
            strategies.append("Monitor banking sector developments and maintain FSP diversity")
        elif country == Country.AFGHANISTAN:
            strategies.append("Prepare alternative mechanisms for potential banking restrictions")
        
        return strategies
    
    def _identify_coordination_requirements(self, country: Country) -> List[str]:
        """Identify coordination requirements."""
        requirements = [
            "Coordinate exchange rate assumptions across agencies",
            "Harmonize transfer values within currency zones",
            "Share market monitoring data and analysis",
            "Align on early warning trigger thresholds"
        ]
        
        # Country-specific coordination needs
        if country == Country.SYRIA:
            requirements.append("Coordinate across territorial control lines")
        elif country == Country.SOMALIA:
            requirements.append("Align with federal and regional authorities")
        
        return requirements
    
    def _design_monitoring_framework(self, country: Country) -> Dict[str, List[str]]:
        """Design monitoring framework."""
        framework = {
            'outcome_indicators': [
                'Market integration index',
                'Price volatility measures',
                'Welfare improvement scores',
                'Local market stimulus effects'
            ],
            'process_indicators': [
                'Transfer value adequacy',
                'Delivery mechanism performance',
                'Beneficiary satisfaction scores',
                'Partner capacity assessments'
            ],
            'risk_indicators': [
                'Exchange rate volatility',
                'Market disruption events',
                'Access constraint incidents',
                'Political economy developments'
            ],
            'early_warning_indicators': [
                'Price spike alerts',
                'Exchange rate threshold breaches',
                'Conflict intensity changes',
                'Market access deterioration'
            ]
        }
        
        return framework
    
    def generate_comprehensive_report(self, 
                                    scenarios: List[PolicyScenario]) -> str:
        """Generate comprehensive policy simulation report."""
        
        # Run simulations for all scenarios
        simulation_results = []
        for scenario in scenarios:
            try:
                result = self.simulate_policy_scenario(scenario)
                simulation_results.append(result)
            except Exception as e:
                logger.error(f"Simulation failed for {scenario.scenario_name}: {e}")
        
        # Generate report
        report = f"""
HUMANITARIAN POLICY SIMULATION REPORT
====================================
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M')}
Based on Cross-Country Validation Meta-Analysis

EXECUTIVE SUMMARY
-----------------
Scenarios Analyzed: {len(simulation_results)}
Countries Covered: {len(set(s.scenario.country for s in simulation_results))}
Total Budget Simulated: ${sum(s.scenario.budget_usd for s in simulation_results):,.0f}

SCENARIO RESULTS
----------------
"""
        
        for result in simulation_results:
            scenario = result.scenario
            report += f"""
{scenario.scenario_name.upper()}
Country: {scenario.country.value.title()}
Intervention: {scenario.intervention_type.value.replace('_', ' ').title()}
Modality: {scenario.aid_modality.value.replace('_', ' ').title()}
Budget: ${scenario.budget_usd:,.0f}

Expected Outcomes:
- People Reached: {result.expected_outcomes.get('people_reached', 0):,.0f}
- Welfare Improvement: {result.expected_outcomes.get('welfare_improvement_index', 0):.1%}
- Cost per Person: ${scenario.budget_usd / max(1, result.expected_outcomes.get('people_reached', 1)):,.0f}

Currency Adjustments:
- Transfer Value Multiplier: {result.currency_adjustments.get('transfer_value_multiplier', 1.0):.2f}
- Volatility Buffer: {result.currency_adjustments.get('price_volatility_buffer', 0) * 100:.0f}%

Top Implementation Guidance:
"""
            for guidance in result.implementation_guidance[:3]:
                report += f"  • {guidance}\n"
        
        report += """
CROSS-CUTTING RECOMMENDATIONS
-----------------------------
1. Implement harmonized exchange rate monitoring across all operations
2. Develop currency zone-specific programming approaches
3. Strengthen early warning systems with econometric triggers
4. Build adaptive programming capacity for volatile contexts
5. Invest in real-time market monitoring infrastructure

POLICY IMPLICATIONS FOR HUMANITARIAN PROGRAMMING
-----------------------------------------------
- Currency fragmentation significantly affects aid effectiveness
- Transfer values must be adjusted by currency zone (10-50% increases needed)
- Market-based modalities require higher market integration thresholds
- Early warning systems should trigger at 50-80% exchange rate differentials
- Cross-border programming coordination essential for effectiveness

END OF REPORT
"""
        
        return report