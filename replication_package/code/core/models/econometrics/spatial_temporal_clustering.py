"""
Spatial-Temporal Clustering Standard Errors.

Implements proper clustering corrections for standard errors in spatial panel models,
addressing the critical issue of invalid statistical inference identified in the
Yemen Market Integration methodology audit.

CRITICAL FIX: Addresses inadequate standard error corrections that only handled
temporal correlation (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) but ignored spatial correlation patterns.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Any, Callable
from dataclasses import dataclass
from enum import Enum
from scipy import stats
from scipy.spatial.distance import pdist, squareform
import logging

logger = logging.getLogger(__name__)


class ClusteringMethod(Enum):
    """Methods for clustering standard errors."""
    MARKET_LEVEL = "market"
    TIME_LEVEL = "time" 
    TWO_WAY = "two_way"
    SPATIAL_HAC = "spatial_hac"
    CONLEY = "conley"
    DRISCOLL_KRAAY = "driscoll_kraay"
    SPATIAL_DRISCOLL_KRAAY = "spatial_driscoll_kraay"


class KernelType(Enum):
    """Kernel types for HAC estimation."""
    BARTLETT = "bartlett"
    PARZEN = "parzen"
    QUADRATIC_SPECTRAL = "quadratic_spectral"
    TRUNCATED = "truncated"


@dataclass
class ClusteringOptions:
    """Configuration options for spatial-temporal clustering."""
    method: ClusteringMethod = ClusteringMethod.TWO_WAY
    spatial_cutoff_km: float = 500.0
    temporal_lags: int = 4
    kernel_type: KernelType = KernelType.BARTLETT
    bandwidth_selection: str = "auto"  # 'auto', 'fixed', or numeric value
    degrees_of_freedom_correction: bool = True
    small_sample_correction: bool = True


@dataclass
class ClusteringResults:
    """Results from spatial-temporal clustering correction."""
    
    # Corrected standard errors
    standard_errors: np.ndarray
    t_statistics: np.ndarray
    p_values: np.ndarray
    
    # Confidence intervals
    conf_int_lower: np.ndarray
    conf_int_upper: np.ndarray
    
    # Clustering details
    clustering_method: ClusteringMethod
    n_clusters_spatial: Optional[int]
    n_clusters_temporal: Optional[int]
    effective_degrees_freedom: float
    
    # Covariance matrix
    variance_covariance_matrix: np.ndarray
    
    # Diagnostic information
    spatial_correlation_detected: bool
    temporal_correlation_detected: bool
    bandwidth_used: Optional[float]
    kernel_type_used: Optional[KernelType]
    
    # Comparison with uncorrected SEs
    uncorrected_se: np.ndarray
    se_inflation_factor: np.ndarray


class SpatialTemporalClustering:
    """
    Comprehensive framework for spatial-temporal clustering standard errors.
    
    Implements multiple methods for correcting standard errors in panel models
    with both spatial and temporal dependence.
    """
    
    def __init__(self, options: Optional[ClusteringOptions] = None):
        """
        Initialize spatial-temporal clustering framework.
        
        Args:
            options: Configuration options for clustering
        """
        self.options = options or ClusteringOptions()
        
    def cluster_standard_errors(self,
                              residuals: np.ndarray,
                              X: np.ndarray,
                              panel_structure: Dict[str, np.ndarray],
                              coordinates: Optional[np.ndarray] = None) -> ClusteringResults:
        """
        Compute spatially and temporally clustered standard errors.
        
        Args:
            residuals: Model residuals
            X: Design matrix
            panel_structure: Dictionary with 'market_id' and 'time_id' arrays
            coordinates: Spatial coordinates for each market (optional)
            
        Returns:
            ClusteringResults with corrected standard errors
        """
        logger.info(f"Computing clustered standard errors using {self.options.method.value} method")
        
        # Compute uncorrected standard errors for comparison
        meat = X.T @ X
        try:
            bread = np.linalg.inv(meat)
        except np.linalg.LinAlgError:
            bread = np.linalg.pinv(meat)
            
        uncorrected_se = np.sqrt(np.diag(bread) * np.var(residuals))
        
        # Compute variance-covariance matrix with clustering correction
        if self.options.method == ClusteringMethod.MARKET_LEVEL:
            vcov_matrix = self._market_cluster(residuals, X, panel_structure)
        elif self.options.method == ClusteringMethod.TIME_LEVEL:
            vcov_matrix = self._time_cluster(residuals, X, panel_structure)
        elif self.options.method == ClusteringMethod.TWO_WAY:
            vcov_matrix = self._two_way_cluster(residuals, X, panel_structure)
        elif self.options.method == ClusteringMethod.SPATIAL_HAC:
            vcov_matrix = self._spatial_hac(residuals, X, panel_structure, coordinates)
        elif self.options.method == ClusteringMethod.CONLEY:
            vcov_matrix = self._conley_spatial_hac(residuals, X, panel_structure, coordinates)
        elif self.options.method == ClusteringMethod.DRISCOLL_KRAAY:
            vcov_matrix = self._driscoll_kraay(residuals, X, panel_structure)
        elif self.options.method == ClusteringMethod.SPATIAL_DRISCOLL_KRAAY:
            vcov_matrix = self._spatial_driscoll_kraay(residuals, X, panel_structure, coordinates)
        else:
            raise ValueError(f"Unknown clustering method: {self.options.method}")
            
        # Extract standard errors
        clustered_se = np.sqrt(np.diag(vcov_matrix))
        
        # Compute test statistics
        coefficients = bread @ X.T @ residuals  # OLS coefficients
        t_stats = coefficients / clustered_se
        
        # Degrees of freedom calculation
        if self.options.degrees_of_freedom_correction:
            if self.options.method in [ClusteringMethod.MARKET_LEVEL, ClusteringMethod.TIME_LEVEL]:
                dof = self._get_cluster_count(panel_structure, self.options.method) - X.shape[1]
            else:
                dof = len(residuals) - X.shape[1]
        else:
            dof = len(residuals) - X.shape[1]
            
        effective_dof = max(dof, 1)  # Ensure positive dof
        p_values = 2 * (1 - stats.t.cdf(np.abs(t_stats), df=effective_dof))
        
        # Calculate confidence intervals
        t_critical = stats.t.ppf(0.975, df=effective_dof)
        conf_int_lower = coefficients - t_critical * clustered_se
        conf_int_upper = coefficients + t_critical * clustered_se
        
        # Calculate inflation factors
        se_inflation = clustered_se / uncorrected_se
        
        # Diagnostic tests
        spatial_corr_detected = self._test_spatial_correlation(residuals, panel_structure, coordinates)
        temporal_corr_detected = self._test_temporal_correlation(residuals, panel_structure)
        
        # Assemble results
        results = ClusteringResults(
            standard_errors=clustered_se,
            t_statistics=t_stats,
            p_values=p_values,
            conf_int_lower=conf_int_lower,
            conf_int_upper=conf_int_upper,
            clustering_method=self.options.method,
            n_clusters_spatial=self._get_spatial_cluster_count(panel_structure, coordinates),
            n_clusters_temporal=self._get_temporal_cluster_count(panel_structure),
            effective_degrees_freedom=effective_dof,
            variance_covariance_matrix=vcov_matrix,
            spatial_correlation_detected=spatial_corr_detected,
            temporal_correlation_detected=temporal_corr_detected,
            bandwidth_used=self._get_bandwidth_used(),
            kernel_type_used=self.options.kernel_type,
            uncorrected_se=uncorrected_se,
            se_inflation_factor=se_inflation
        )
        
        return results
        
    def _market_cluster(self, residuals: np.ndarray, X: np.ndarray, 
                       panel_structure: Dict[str, np.ndarray]) -> np.ndarray:
        """Cluster standard errors at market level."""
        
        market_ids = panel_structure['market_id']
        unique_markets = np.unique(market_ids)
        
        # Compute bread (X'X)^-1
        meat = X.T @ X
        try:
            bread = np.linalg.inv(meat)
        except np.linalg.LinAlgError:
            bread = np.linalg.pinv(meat)
        
        # Compute clustered meat
        clustered_meat = np.zeros_like(meat)
        
        for market in unique_markets:
            market_mask = market_ids == market
            X_market = X[market_mask]
            resid_market = residuals[market_mask]
            
            # Market-specific score
            score_market = X_market.T @ resid_market
            clustered_meat += np.outer(score_market, score_market)
            
        # Small sample correction
        if self.options.small_sample_correction:
            n_clusters = len(unique_markets)
            n_obs = len(residuals)
            correction = (n_clusters / (n_clusters - 1)) * ((n_obs - 1) / (n_obs - X.shape[1]))
            clustered_meat *= correction
            
        vcov_matrix = bread @ clustered_meat @ bread
        
        return vcov_matrix
        
    def _time_cluster(self, residuals: np.ndarray, X: np.ndarray,
                     panel_structure: Dict[str, np.ndarray]) -> np.ndarray:
        """Cluster standard errors at time level."""
        
        time_ids = panel_structure['time_id']
        unique_times = np.unique(time_ids)
        
        # Compute bread
        meat = X.T @ X
        try:
            bread = np.linalg.inv(meat)
        except np.linalg.LinAlgError:
            bread = np.linalg.pinv(meat)
            
        # Compute clustered meat
        clustered_meat = np.zeros_like(meat)
        
        for time_period in unique_times:
            time_mask = time_ids == time_period
            X_time = X[time_mask]
            resid_time = residuals[time_mask]
            
            # Time-specific score
            score_time = X_time.T @ resid_time
            clustered_meat += np.outer(score_time, score_time)
            
        # Small sample correction
        if self.options.small_sample_correction:
            n_clusters = len(unique_times)
            n_obs = len(residuals)
            correction = (n_clusters / (n_clusters - 1)) * ((n_obs - 1) / (n_obs - X.shape[1]))
            clustered_meat *= correction
            
        vcov_matrix = bread @ clustered_meat @ bread
        
        return vcov_matrix
        
    def _two_way_cluster(self, residuals: np.ndarray, X: np.ndarray,
                        panel_structure: Dict[str, np.ndarray]) -> np.ndarray:
        """Two-way clustering (market and time)."""
        
        # Compute individual clustering corrections
        market_vcov = self._market_cluster(residuals, X, panel_structure)
        time_vcov = self._time_cluster(residuals, X, panel_structure)
        
        # For two-way clustering: V_market + V_time - V_intersection
        # Simplified approach: use Cameron, Gelbach, Miller (2011) formula
        intersection_vcov = self._intersection_cluster(residuals, X, panel_structure)
        
        two_way_vcov = market_vcov + time_vcov - intersection_vcov
        
        return two_way_vcov
        
    def _intersection_cluster(self, residuals: np.ndarray, X: np.ndarray,
                            panel_structure: Dict[str, np.ndarray]) -> np.ndarray:
        """Cluster by intersection of market and time (market-time pairs)."""
        
        market_ids = panel_structure['market_id']
        time_ids = panel_structure['time_id']
        
        # Create intersection clusters
        intersection_ids = [f"{m}_{t}" for m, t in zip(market_ids, time_ids)]
        unique_intersections = list(set(intersection_ids))
        
        # Compute bread
        meat = X.T @ X
        try:
            bread = np.linalg.inv(meat)
        except np.linalg.LinAlgError:
            bread = np.linalg.pinv(meat)
            
        # Compute clustered meat
        clustered_meat = np.zeros_like(meat)
        
        for intersection in unique_intersections:
            intersection_mask = np.array(intersection_ids) == intersection
            X_intersection = X[intersection_mask]
            resid_intersection = residuals[intersection_mask]
            
            if len(resid_intersection) > 0:
                score_intersection = X_intersection.T @ resid_intersection
                clustered_meat += np.outer(score_intersection, score_intersection)
                
        vcov_matrix = bread @ clustered_meat @ bread
        
        return vcov_matrix
        
    def _spatial_hac(self, residuals: np.ndarray, X: np.ndarray,
                    panel_structure: Dict[str, np.ndarray],
                    coordinates: Optional[np.ndarray]) -> np.ndarray:
        """Spatial HAC standard errors."""
        
        if coordinates is None:
            logger.warning("No coordinates provided for spatial HAC, falling back to market clustering")
            return self._market_cluster(residuals, X, panel_structure)
            
        # Compute spatial distance matrix
        distances = squareform(pdist(coordinates, metric='euclidean'))
        
        # Convert to kilometers if needed (assuming coordinates are in degrees)
        distances_km = distances * 111.32  # Rough conversion from degrees to km
        
        # Compute spatial weights using specified kernel
        spatial_weights = self._compute_spatial_weights(distances_km)
        
        # Compute HAC variance-covariance matrix
        vcov_matrix = self._compute_hac_vcov(residuals, X, spatial_weights)
        
        return vcov_matrix
        
    def _conley_spatial_hac(self, residuals: np.ndarray, X: np.ndarray,
                          panel_structure: Dict[str, np.ndarray],
                          coordinates: Optional[np.ndarray]) -> np.ndarray:
        """Conley (1999) spatial HAC standard errors."""
        
        if coordinates is None:
            logger.warning("No coordinates provided for Conley HAC, falling back to market clustering")
            return self._market_cluster(residuals, X, panel_structure)
            
        # Compute spatial distance matrix
        distances = squareform(pdist(coordinates, metric='euclidean'))
        distances_km = distances * 111.32
        
        # Conley cutoff
        cutoff = self.options.spatial_cutoff_km
        
        # Compute bread
        meat = X.T @ X
        try:
            bread = np.linalg.inv(meat)
        except np.linalg.LinAlgError:
            bread = np.linalg.pinv(meat)
            
        # Compute Conley HAC meat
        n_obs = len(residuals)
        clustered_meat = np.zeros_like(meat)
        
        for i in range(n_obs):
            for j in range(n_obs):
                if distances_km[i, j] <= cutoff:
                    # Apply kernel weight
                    if distances_km[i, j] == 0:
                        weight = 1.0
                    else:
                        weight = 1 - distances_km[i, j] / cutoff  # Bartlett kernel
                        
                    score_i = X[i:i+1].T @ residuals[i:i+1]
                    score_j = X[j:j+1].T @ residuals[j:j+1]
                    
                    clustered_meat += weight * (score_i @ score_j.T)
                    
        vcov_matrix = bread @ clustered_meat @ bread
        
        return vcov_matrix
        
    def _driscoll_kraay(self, residuals: np.ndarray, X: np.ndarray,
                       panel_structure: Dict[str, np.ndarray]) -> np.ndarray:
        """Driscoll-Kraay (1998) standard errors."""
        
        time_ids = panel_structure['time_id']
        market_ids = panel_structure['market_id']
        
        unique_times = np.unique(time_ids)
        T = len(unique_times)
        
        # Compute bread
        meat = X.T @ X
        try:
            bread = np.linalg.inv(meat)
        except np.linalg.LinAlgError:
            bread = np.linalg.pinv(meat)
            
        # Compute DK meat with temporal HAC
        clustered_meat = np.zeros_like(meat)
        
        for lag in range(self.options.temporal_lags + 1):
            weight = self._bartlett_kernel(lag, self.options.temporal_lags)
            
            cross_products = np.zeros_like(meat)
            
            for t_idx, t in enumerate(unique_times):
                if t_idx + lag < len(unique_times):
                    t_plus_lag = unique_times[t_idx + lag]
                    
                    # Current period
                    mask_t = time_ids == t
                    X_t = X[mask_t]
                    resid_t = residuals[mask_t]
                    score_t = X_t.T @ resid_t
                    
                    # Lagged period
                    mask_t_lag = time_ids == t_plus_lag
                    X_t_lag = X[mask_t_lag]
                    resid_t_lag = residuals[mask_t_lag]
                    score_t_lag = X_t_lag.T @ resid_t_lag
                    
                    if lag == 0:
                        cross_products += score_t @ score_t.T
                    else:
                        cross_products += score_t @ score_t_lag.T + score_t_lag @ score_t.T
                        
            clustered_meat += weight * cross_products
            
        vcov_matrix = bread @ clustered_meat @ bread
        
        return vcov_matrix
        
    def _spatial_driscoll_kraay(self, residuals: np.ndarray, X: np.ndarray,
                              panel_structure: Dict[str, np.ndarray],
                              coordinates: Optional[np.ndarray]) -> np.ndarray:
        """Spatial extension of Driscoll-Kraay standard errors."""
        
        # Combine temporal and spatial correlation
        temporal_vcov = self._driscoll_kraay(residuals, X, panel_structure)
        
        if coordinates is not None:
            spatial_vcov = self._conley_spatial_hac(residuals, X, panel_structure, coordinates)
            # Simple combination - could be enhanced
            combined_vcov = (temporal_vcov + spatial_vcov) / 2
        else:
            combined_vcov = temporal_vcov
            
        return combined_vcov
        
    def _compute_spatial_weights(self, distances_km: np.ndarray) -> np.ndarray:
        """Compute spatial weights matrix using specified kernel."""
        
        cutoff = self.options.spatial_cutoff_km
        weights = np.zeros_like(distances_km)
        
        if self.options.kernel_type == KernelType.BARTLETT:
            mask = distances_km <= cutoff
            weights[mask] = 1 - distances_km[mask] / cutoff
        elif self.options.kernel_type == KernelType.TRUNCATED:
            weights[distances_km <= cutoff] = 1.0
        else:
            # Default to Bartlett
            mask = distances_km <= cutoff
            weights[mask] = 1 - distances_km[mask] / cutoff
            
        return weights
        
    def _compute_hac_vcov(self, residuals: np.ndarray, X: np.ndarray,
                         spatial_weights: np.ndarray) -> np.ndarray:
        """Compute HAC variance-covariance matrix."""
        
        # Compute bread
        meat = X.T @ X
        try:
            bread = np.linalg.inv(meat)
        except np.linalg.LinAlgError:
            bread = np.linalg.pinv(meat)
            
        # Compute weighted meat
        n_obs = len(residuals)
        clustered_meat = np.zeros_like(meat)
        
        for i in range(n_obs):
            for j in range(n_obs):
                weight = spatial_weights[i, j]
                if weight > 0:
                    score_i = X[i:i+1].T @ residuals[i:i+1]
                    score_j = X[j:j+1].T @ residuals[j:j+1]
                    clustered_meat += weight * (score_i @ score_j.T)
                    
        vcov_matrix = bread @ clustered_meat @ bread
        
        return vcov_matrix
        
    def _bartlett_kernel(self, lag: int, max_lag: int) -> float:
        """Bartlett kernel weights for temporal HAC."""
        if lag <= max_lag:
            return 1 - lag / (max_lag + 1)
        else:
            return 0.0
            
    def _test_spatial_correlation(self, residuals: np.ndarray,
                                panel_structure: Dict[str, np.ndarray],
                                coordinates: Optional[np.ndarray]) -> bool:
        """Test for spatial correlation in residuals."""
        
        if coordinates is None:
            return False
            
        try:
            # Simple Moran's I test
            from scipy.spatial.distance import pdist, squareform
            
            distances = squareform(pdist(coordinates))
            weights = 1 / (1 + distances)  # Inverse distance weights
            np.fill_diagonal(weights, 0)
            
            # Standardize weights
            row_sums = weights.sum(axis=1)
            weights = weights / row_sums[:, np.newaxis]
            
            # Compute Moran's I
            n = len(residuals)
            W = weights
            
            numerator = residuals.T @ W @ residuals
            denominator = residuals.T @ residuals / n
            
            morans_i = numerator / denominator
            
            # Simple threshold test
            return abs(morans_i) > 0.1
            
        except Exception as e:
            logger.warning(f"Spatial correlation test failed: {e}")
            return False
            
    def _test_temporal_correlation(self, residuals: np.ndarray,
                                 panel_structure: Dict[str, np.ndarray]) -> bool:
        """Test for temporal correlation in residuals."""
        
        try:
            # Group residuals by market and test for autocorrelation
            market_ids = panel_structure['market_id']
            time_ids = panel_structure['time_id']
            
            unique_markets = np.unique(market_ids)
            autocorrs = []
            
            for market in unique_markets:
                market_mask = market_ids == market
                market_resids = residuals[market_mask]
                market_times = time_ids[market_mask]
                
                # Sort by time
                time_order = np.argsort(market_times)
                sorted_resids = market_resids[time_order]
                
                if len(sorted_resids) > 1:
                    # Lag-1 autocorrelation
                    autocorr = np.corrcoef(sorted_resids[:-1], sorted_resids[1:])[0, 1]
                    if not np.isnan(autocorr):
                        autocorrs.append(autocorr)
                        
            if autocorrs:
                avg_autocorr = np.mean(autocorrs)
                return abs(avg_autocorr) > 0.1
            else:
                return False
                
        except Exception as e:
            logger.warning(f"Temporal correlation test failed: {e}")
            return False
            
    def _get_cluster_count(self, panel_structure: Dict[str, np.ndarray],
                          method: ClusteringMethod) -> int:
        """Get number of clusters for degrees of freedom correction."""
        
        if method == ClusteringMethod.MARKET_LEVEL:
            return len(np.unique(panel_structure['market_id']))
        elif method == ClusteringMethod.TIME_LEVEL:
            return len(np.unique(panel_structure['time_id']))
        else:
            return len(panel_structure['market_id'])  # Number of observations
            
    def _get_spatial_cluster_count(self, panel_structure: Dict[str, np.ndarray],
                                 coordinates: Optional[np.ndarray]) -> Optional[int]:
        """Get number of spatial clusters."""
        if coordinates is not None:
            return len(np.unique(panel_structure['market_id']))
        return None
        
    def _get_temporal_cluster_count(self, panel_structure: Dict[str, np.ndarray]) -> int:
        """Get number of temporal clusters."""
        return len(np.unique(panel_structure['time_id']))
        
    def _get_bandwidth_used(self) -> Optional[float]:
        """Get bandwidth used for HAC estimation."""
        if self.options.method in [ClusteringMethod.SPATIAL_HAC, ClusteringMethod.CONLEY]:
            return self.options.spatial_cutoff_km
        return None
        
    def create_clustering_report(self, results: ClusteringResults) -> str:
        """Create detailed clustering results report."""
        
        report = []
        report.append("# SPATIAL-TEMPORAL CLUSTERING REPORT")
        report.append(f"\n## Method: {results.clustering_method.value}")
        
        # Clustering details
        report.append(f"\n### Clustering Configuration")
        report.append(f"- **Spatial clusters**: {results.n_clusters_spatial or 'N/A'}")
        report.append(f"- **Temporal clusters**: {results.n_clusters_temporal}")
        report.append(f"- **Effective DOF**: {results.effective_degrees_freedom:.1f}")
        
        if results.bandwidth_used:
            report.append(f"- **Spatial bandwidth**: {results.bandwidth_used:.1f} km")
        if results.kernel_type_used:
            report.append(f"- **Kernel type**: {results.kernel_type_used.value}")
            
        # Diagnostic results
        report.append(f"\n### Correlation Diagnostics")
        spatial_status = "✓ Detected" if results.spatial_correlation_detected else "✗ Not detected"
        temporal_status = "✓ Detected" if results.temporal_correlation_detected else "✗ Not detected"
        report.append(f"- **Spatial correlation**: {spatial_status}")
        report.append(f"- **Temporal correlation**: {temporal_status}")
        
        # Standard error comparison
        report.append(f"\n### Standard Error Inflation")
        avg_inflation = np.mean(results.se_inflation_factor)
        max_inflation = np.max(results.se_inflation_factor)
        report.append(f"- **Average inflation factor**: {avg_inflation:.2f}")
        report.append(f"- **Maximum inflation factor**: {max_inflation:.2f}")
        
        # Summary statistics
        report.append(f"\n### Test Statistics Summary")
        report.append(f"- **Mean |t-statistic|**: {np.mean(np.abs(results.t_statistics)):.2f}")
        report.append(f"- **Significant coefficients (p<0.05)**: {np.sum(results.p_values < 0.05)}/{len(results.p_values)}")
        
        return "\n".join(report)