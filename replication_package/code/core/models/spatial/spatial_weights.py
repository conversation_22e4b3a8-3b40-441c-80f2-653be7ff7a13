"""
Spatial Weight Matrix Construction for Yemen Market Integration Analysis.

Implements multiple approaches to construct spatial connectivity matrices
that account for geographic, economic, and institutional factors.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from scipy.spatial.distance import cdist
from sklearn.metrics.pairwise import haversine_distances
import logging

logger = logging.getLogger(__name__)


@dataclass
class SpatialWeightOptions:
    """Configuration options for spatial weight matrix construction."""
    distance_cutoff_km: float = 500.0
    distance_decay_parameter: float = 2.0
    row_standardize: bool = True
    currency_zone_penalty: float = 0.3  # Discount for cross-zone connections
    admin_boundary_bonus: float = 1.2   # Bonus for same-governorate connections
    conflict_adjustment: bool = True
    min_weight_threshold: float = 0.001


class SpatialWeightMatrix:
    """
    Spatial weight matrix construction with Yemen-specific considerations.
    
    Supports multiple weight specifications:
    - Geographic distance weights
    - Economic connectivity weights  
    - Currency zone-aware weights
    - Conflict-adjusted weights
    """
    
    def __init__(self, options: Optional[SpatialWeightOptions] = None):
        """Initialize spatial weight matrix constructor."""
        self.options = options or SpatialWeightOptions()
        self._weight_matrix = None
        self._market_ids = None
        
    @classmethod
    def from_coordinates(
        cls,
        coordinates: pd.DataFrame,
        weight_type: str = 'distance',
        options: Optional[SpatialWeightOptions] = None
    ) -> 'SpatialWeightMatrix':
        """
        Create spatial weights from market coordinates.
        
        Args:
            coordinates: DataFrame with columns ['market_id', 'latitude', 'longitude']
            weight_type: Type of weights ('distance', 'economic', 'currency_aware')
            options: Configuration options
            
        Returns:
            SpatialWeightMatrix instance
        """
        instance = cls(options)
        instance._market_ids = coordinates['market_id'].tolist()
        
        if weight_type == 'distance':
            instance._weight_matrix = instance._create_distance_weights(coordinates)
        elif weight_type == 'economic':
            instance._weight_matrix = instance._create_economic_weights(coordinates)
        elif weight_type == 'currency_aware':
            instance._weight_matrix = instance._create_currency_aware_weights(coordinates)
        else:
            raise ValueError(f"Unknown weight type: {weight_type}")
            
        return instance
        
    def _create_distance_weights(self, coordinates: pd.DataFrame) -> np.ndarray:
        """Create geographic distance-based weights."""
        # Extract coordinates
        coords = coordinates[['latitude', 'longitude']].values
        coords_rad = np.radians(coords)
        
        # Calculate distance matrix using haversine formula
        distances_km = haversine_distances(coords_rad) * 6371  # Earth radius in km
        
        # Create weight matrix with distance decay
        n_markets = len(coordinates)
        W = np.zeros((n_markets, n_markets))
        
        for i in range(n_markets):
            for j in range(n_markets):
                if i != j:
                    distance = distances_km[i, j]
                    if distance <= self.options.distance_cutoff_km:
                        W[i, j] = 1 / (distance ** self.options.distance_decay_parameter)
        
        # Apply minimum weight threshold
        W[W < self.options.min_weight_threshold] = 0
        
        # Row standardize if requested
        if self.options.row_standardize:
            W = self._row_standardize(W)
            
        logger.info(f"Created distance weight matrix: {n_markets}x{n_markets}, "
                   f"sparsity: {np.mean(W > 0):.3f}")
        
        return W
        
    def _create_economic_weights(self, coordinates: pd.DataFrame) -> np.ndarray:
        """Create weights based on economic connectivity factors."""
        # Start with distance weights as base
        W_base = self._create_distance_weights(coordinates)
        n_markets = len(coordinates)
        W_economic = W_base.copy()
        
        # Apply economic factors if available in coordinates
        for i in range(n_markets):
            for j in range(n_markets):
                if i != j and W_base[i, j] > 0:
                    economic_factor = 1.0
                    
                    # Same governorate bonus
                    if 'governorate' in coordinates.columns:
                        same_gov = (coordinates.iloc[i]['governorate'] == 
                                  coordinates.iloc[j]['governorate'])
                        if same_gov:
                            economic_factor *= self.options.admin_boundary_bonus
                    
                    # Population-based adjustment (larger markets have stronger connections)
                    if 'population' in coordinates.columns:
                        pop_i = coordinates.iloc[i].get('population', 50000)  # Default
                        pop_j = coordinates.iloc[j].get('population', 50000)
                        pop_factor = np.sqrt(pop_i * pop_j) / 50000  # Normalize to 50k baseline
                        economic_factor *= min(pop_factor, 2.0)  # Cap at 2x multiplier
                    
                    # Transport infrastructure adjustment
                    if 'road_quality' in coordinates.columns:
                        road_i = coordinates.iloc[i].get('road_quality', 0.5)  # 0-1 scale
                        road_j = coordinates.iloc[j].get('road_quality', 0.5)
                        transport_factor = (road_i + road_j) / 2
                        economic_factor *= (0.5 + 0.5 * transport_factor)  # Range [0.5, 1.0]
                    
                    W_economic[i, j] *= economic_factor
        
        # Re-apply thresholding and standardization
        W_economic[W_economic < self.options.min_weight_threshold] = 0
        
        if self.options.row_standardize:
            W_economic = self._row_standardize(W_economic)
            
        logger.info(f"Created economic weight matrix with adjustments")
        
        return W_economic
        
    def _create_currency_aware_weights(self, coordinates: pd.DataFrame) -> np.ndarray:
        """Create weights that account for currency zone fragmentation."""
        # Start with economic weights
        W_currency = self._create_economic_weights(coordinates)
        n_markets = len(coordinates)
        
        # Apply currency zone penalties
        if 'currency_zone' in coordinates.columns:
            for i in range(n_markets):
                for j in range(n_markets):
                    if i != j and W_currency[i, j] > 0:
                        # Check if markets are in different currency zones
                        zone_i = coordinates.iloc[i]['currency_zone']
                        zone_j = coordinates.iloc[j]['currency_zone']
                        
                        if zone_i != zone_j:
                            # Apply penalty for cross-zone connections
                            W_currency[i, j] *= self.options.currency_zone_penalty
            
            logger.info(f"Applied currency zone penalties with factor {self.options.currency_zone_penalty}")
        
        # Apply conflict adjustments if requested
        if self.options.conflict_adjustment and 'conflict_intensity' in coordinates.columns:
            W_currency = self._apply_conflict_adjustments(W_currency, coordinates)
        
        # Final thresholding and standardization
        W_currency[W_currency < self.options.min_weight_threshold] = 0
        
        if self.options.row_standardize:
            W_currency = self._row_standardize(W_currency)
            
        return W_currency
        
    def _apply_conflict_adjustments(self, W: np.ndarray, coordinates: pd.DataFrame) -> np.ndarray:
        """Adjust weights based on conflict intensity."""
        n_markets = len(coordinates)
        W_conflict = W.copy()
        
        for i in range(n_markets):
            for j in range(n_markets):
                if i != j and W[i, j] > 0:
                    # Get conflict intensities (0-1 scale)
                    conflict_i = coordinates.iloc[i].get('conflict_intensity', 0.0)
                    conflict_j = coordinates.iloc[j].get('conflict_intensity', 0.0)
                    
                    # Reduce connectivity for high-conflict areas
                    avg_conflict = (conflict_i + conflict_j) / 2
                    conflict_discount = 1.0 - 0.5 * avg_conflict  # Up to 50% reduction
                    
                    W_conflict[i, j] *= max(conflict_discount, 0.1)  # Minimum 10% connection
        
        logger.info("Applied conflict intensity adjustments")
        return W_conflict
        
    def _row_standardize(self, W: np.ndarray) -> np.ndarray:
        """Row-standardize weight matrix."""
        row_sums = W.sum(axis=1)
        # Avoid division by zero for isolated markets
        row_sums[row_sums == 0] = 1
        return W / row_sums[:, np.newaxis]
        
    @property
    def matrix(self) -> np.ndarray:
        """Get the weight matrix."""
        if self._weight_matrix is None:
            raise ValueError("Weight matrix not constructed. Use from_coordinates() method.")
        return self._weight_matrix
        
    @property
    def market_ids(self) -> List[str]:
        """Get market IDs corresponding to matrix rows/columns."""
        if self._market_ids is None:
            raise ValueError("Market IDs not set.")
        return self._market_ids
        
    def get_neighbors(self, market_id: str, min_weight: float = 0.001) -> Dict[str, float]:
        """Get neighboring markets and their weights for a given market."""
        if market_id not in self._market_ids:
            raise ValueError(f"Market {market_id} not found in weight matrix")
            
        market_idx = self._market_ids.index(market_id)
        neighbors = {}
        
        for j, neighbor_id in enumerate(self._market_ids):
            weight = self._weight_matrix[market_idx, j]
            if weight >= min_weight and neighbor_id != market_id:
                neighbors[neighbor_id] = weight
                
        return dict(sorted(neighbors.items(), key=lambda x: x[1], reverse=True))
        
    def get_connectivity_stats(self) -> Dict[str, float]:
        """Calculate connectivity statistics for the weight matrix."""
        W = self._weight_matrix
        
        stats = {
            'sparsity': np.mean(W > 0),
            'avg_connections_per_market': np.mean(np.sum(W > 0, axis=1)),
            'max_connections': np.max(np.sum(W > 0, axis=1)),
            'min_connections': np.min(np.sum(W > 0, axis=1)),
            'avg_weight': np.mean(W[W > 0]) if np.any(W > 0) else 0,
            'weight_concentration': np.std(W[W > 0]) if np.any(W > 0) else 0
        }
        
        return stats
        
    def validate_matrix(self) -> Dict[str, bool]:
        """Validate weight matrix properties."""
        W = self._weight_matrix
        
        validation = {
            'non_negative': np.all(W >= 0),
            'zero_diagonal': np.all(np.diag(W) == 0),
            'finite_values': np.all(np.isfinite(W)),
            'some_connections': np.any(W > 0),
            'symmetric': np.allclose(W, W.T, atol=1e-10) if W.shape[0] == W.shape[1] else False
        }
        
        if self.options.row_standardize:
            validation['row_standardized'] = np.allclose(np.sum(W, axis=1), 1.0, atol=1e-10)
        
        return validation
        
    def to_dict(self) -> Dict:
        """Export weight matrix configuration for replication."""
        return {
            'options': self.options.__dict__,
            'n_markets': len(self._market_ids),
            'market_ids': self._market_ids,
            'connectivity_stats': self.get_connectivity_stats(),
            'validation': self.validate_matrix()
        }