"""
Spatial Panel Model Implementation for Market Integration Analysis.

Implements spatial panel models with fixed effects for analyzing
market integration across space and time in conflict settings.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import logging

from .spatial_lag_model import SpatialLagModel, SpatialLagResults
from .spatial_error_model import SpatialErrorModel, SpatialErrorResults

logger = logging.getLogger(__name__)


class SpatialPanelType(Enum):
    """Types of spatial panel models."""
    SPATIAL_LAG = "spatial_lag"
    SPATIAL_ERROR = "spatial_error"
    SPATIAL_DURBIN = "spatial_durbin"  # For future implementation


class FixedEffectsType(Enum):
    """Types of fixed effects for spatial panel models."""
    NONE = "none"
    MARKET = "market"
    TIME = "time"  
    COMMODITY = "commodity"
    MARKET_TIME = "market_time"
    MARKET_COMMODITY = "market_commodity"
    TIME_COMMODITY = "time_commodity"
    THREE_WAY = "market_time_commodity"


@dataclass
class SpatialPanelResults:
    """Results from spatial panel model estimation."""
    
    # Model specification
    model_type: SpatialPanelType
    fixed_effects: FixedEffectsType
    
    # Spatial model results (composition)
    spatial_results: Union[SpatialLagResults, SpatialErrorResults]
    
    # Panel-specific results
    fixed_effects_estimates: Dict[str, np.ndarray]
    within_r_squared: float
    between_r_squared: float
    overall_r_squared: float
    
    # Panel diagnostics
    hausman_test: Dict[str, float]
    panel_unit_root_tests: Dict[str, Dict[str, float]]
    cross_sectional_dependence_test: Dict[str, float]
    
    # Time-varying spatial effects (if applicable)
    spatial_parameter_evolution: Optional[pd.DataFrame] = None


class SpatialPanelModel:
    """
    Spatial Panel Model with Fixed Effects.
    
    Combines spatial econometric methods with panel data techniques
    to analyze market integration over space and time.
    """
    
    def __init__(self,
                 data: pd.DataFrame,
                 weight_matrix: np.ndarray,
                 outcome_var: str,
                 exog_vars: List[str],
                 market_id_var: str = 'market_id',
                 time_var: str = 'date',
                 commodity_var: Optional[str] = None):
        """
        Initialize spatial panel model.
        
        Args:
            data: Panel data with market-time observations
            weight_matrix: Spatial weight matrix (n_markets x n_markets)
            outcome_var: Name of dependent variable column
            exog_vars: List of exogenous variable column names
            market_id_var: Market identifier column name
            time_var: Time variable column name
            commodity_var: Commodity identifier column name (optional)
        """
        self.data = data.copy()
        self.W = weight_matrix
        self.outcome_var = outcome_var
        self.exog_vars = exog_vars
        self.market_id_var = market_id_var
        self.time_var = time_var
        self.commodity_var = commodity_var
        self.results = None
        
        # Validate and prepare data
        self._validate_inputs()
        self._prepare_panel_data()
        
    def _validate_inputs(self):
        """Validate model inputs for panel structure."""
        required_cols = [self.outcome_var, self.market_id_var, self.time_var] + self.exog_vars
        if self.commodity_var:
            required_cols.append(self.commodity_var)
            
        missing_cols = [col for col in required_cols if col not in self.data.columns]
        if missing_cols:
            raise ValueError(f"Missing columns in data: {missing_cols}")
            
        # Check panel balance
        if self.commodity_var:
            panel_groups = self.data.groupby([self.market_id_var, self.commodity_var]).size()
        else:
            panel_groups = self.data.groupby(self.market_id_var).size()
            
        if panel_groups.nunique() > 1:
            logger.warning("Panel is unbalanced - some market-commodity combinations have different time periods")
            
    def _prepare_panel_data(self):
        """Prepare panel data for spatial estimation."""
        # Sort by market, commodity (if applicable), and time
        sort_cols = [self.market_id_var, self.time_var]
        if self.commodity_var:
            sort_cols.insert(1, self.commodity_var)
            
        self.data = self.data.sort_values(sort_cols)
        
        # Create index mappings
        self.unique_markets = sorted(self.data[self.market_id_var].unique())
        self.unique_times = sorted(self.data[self.time_var].unique())
        
        if self.commodity_var:
            self.unique_commodities = sorted(self.data[self.commodity_var].unique())
        else:
            self.unique_commodities = None
            
        # Subset weight matrix to available markets
        if len(self.unique_markets) != self.W.shape[0]:
            logger.info(f"Subsetting weight matrix to {len(self.unique_markets)} available markets")
            market_indices = list(range(len(self.unique_markets)))
            self.W = self.W[np.ix_(market_indices, market_indices)]
            
        logger.info(f"Panel structure: {len(self.unique_markets)} markets, "
                   f"{len(self.unique_times)} time periods"
                   f"{f', {len(self.unique_commodities)} commodities' if self.unique_commodities else ''}")
                   
    def fit(self,
            model_type: SpatialPanelType = SpatialPanelType.SPATIAL_LAG,
            fixed_effects: FixedEffectsType = FixedEffectsType.MARKET_TIME,
            commodity_specific: bool = False) -> SpatialPanelResults:
        """
        Estimate spatial panel model.
        
        Args:
            model_type: Type of spatial model (lag or error)
            fixed_effects: Type of fixed effects to include
            commodity_specific: Whether to estimate separate models by commodity
            
        Returns:
            SpatialPanelResults object
        """
        logger.info(f"Estimating {model_type.value} spatial panel model with {fixed_effects.value} fixed effects")
        
        if commodity_specific and self.commodity_var:
            return self._fit_commodity_specific_models(model_type, fixed_effects)
        else:
            return self._fit_pooled_model(model_type, fixed_effects)
            
    def _fit_pooled_model(self,
                         model_type: SpatialPanelType,
                         fixed_effects: FixedEffectsType) -> SpatialPanelResults:
        """Fit pooled spatial panel model."""
        
        # Apply fixed effects transformation
        transformed_data = self._apply_fixed_effects_transformation(self.data, fixed_effects)
        
        # Estimate spatial model on transformed data
        if model_type == SpatialPanelType.SPATIAL_LAG:
            spatial_model = SpatialLagModel(
                transformed_data, self.W, self.outcome_var, self.exog_vars, self.market_id_var
            )
            spatial_results = spatial_model.fit()
            
        elif model_type == SpatialPanelType.SPATIAL_ERROR:
            spatial_model = SpatialErrorModel(
                transformed_data, self.W, self.outcome_var, self.exog_vars, self.market_id_var
            )
            spatial_results = spatial_model.fit()
            
        else:
            raise NotImplementedError(f"Model type {model_type} not yet implemented")
            
        # Calculate panel-specific statistics
        fixed_effects_estimates = self._extract_fixed_effects(fixed_effects)
        r_squared_stats = self._calculate_panel_r_squared(spatial_results, fixed_effects)
        panel_diagnostics = self._run_panel_diagnostics()
        
        # Assemble results
        results = SpatialPanelResults(
            model_type=model_type,
            fixed_effects=fixed_effects,
            spatial_results=spatial_results,
            fixed_effects_estimates=fixed_effects_estimates,
            within_r_squared=r_squared_stats['within'],
            between_r_squared=r_squared_stats['between'],
            overall_r_squared=r_squared_stats['overall'],
            hausman_test=panel_diagnostics['hausman'],
            panel_unit_root_tests=panel_diagnostics['unit_root'],
            cross_sectional_dependence_test=panel_diagnostics['cd_test']
        )
        
        self.results = results
        return results
        
    def _fit_commodity_specific_models(self,
                                     model_type: SpatialPanelType,
                                     fixed_effects: FixedEffectsType) -> SpatialPanelResults:
        """Fit separate spatial models for each commodity (Tier 2 approach)."""
        
        commodity_results = {}
        
        for commodity in self.unique_commodities:
            logger.info(f"Estimating model for commodity: {commodity}")
            
            # Subset data for this commodity
            commodity_data = self.data[self.data[self.commodity_var] == commodity].copy()
            
            if len(commodity_data) < 50:  # Minimum observations threshold
                logger.warning(f"Insufficient data for commodity {commodity} ({len(commodity_data)} obs)")
                continue
                
            # Apply fixed effects (only market and time for commodity-specific models)
            commodity_fe = FixedEffectsType.MARKET_TIME if fixed_effects != FixedEffectsType.NONE else FixedEffectsType.NONE
            transformed_data = self._apply_fixed_effects_transformation(commodity_data, commodity_fe)
            
            # Estimate spatial model
            try:
                if model_type == SpatialPanelType.SPATIAL_LAG:
                    spatial_model = SpatialLagModel(
                        transformed_data, self.W, self.outcome_var, self.exog_vars, self.market_id_var
                    )
                    commodity_results[commodity] = spatial_model.fit()
                    
                elif model_type == SpatialPanelType.SPATIAL_ERROR:
                    spatial_model = SpatialErrorModel(
                        transformed_data, self.W, self.outcome_var, self.exog_vars, self.market_id_var
                    )
                    commodity_results[commodity] = spatial_model.fit()
                    
            except Exception as e:
                logger.error(f"Failed to estimate model for commodity {commodity}: {e}")
                continue
                
        # Aggregate results across commodities
        # For now, return results for first successful commodity
        # Full implementation would aggregate coefficients, etc.
        if commodity_results:
            first_commodity = list(commodity_results.keys())[0]
            spatial_results = commodity_results[first_commodity]
            
            results = SpatialPanelResults(
                model_type=model_type,
                fixed_effects=fixed_effects,
                spatial_results=spatial_results,
                fixed_effects_estimates={},
                within_r_squared=0.0,
                between_r_squared=0.0,
                overall_r_squared=0.0,
                hausman_test={'statistic': 0.0, 'p_value': 1.0},
                panel_unit_root_tests={},
                cross_sectional_dependence_test={'statistic': 0.0, 'p_value': 1.0}
            )
            
            self.results = results
            return results
        else:
            raise ValueError("No commodities had sufficient data for estimation")
            
    def _apply_fixed_effects_transformation(self,
                                          data: pd.DataFrame,
                                          fixed_effects: FixedEffectsType) -> pd.DataFrame:
        """Apply within transformation for fixed effects."""
        
        transformed_data = data.copy()
        
        if fixed_effects == FixedEffectsType.NONE:
            return transformed_data
            
        # Define grouping variables based on fixed effects type
        if fixed_effects == FixedEffectsType.MARKET:
            group_vars = [self.market_id_var]
        elif fixed_effects == FixedEffectsType.TIME:
            group_vars = [self.time_var]
        elif fixed_effects == FixedEffectsType.COMMODITY and self.commodity_var:
            group_vars = [self.commodity_var]
        elif fixed_effects == FixedEffectsType.MARKET_TIME:
            group_vars = [self.market_id_var, self.time_var]
        elif fixed_effects == FixedEffectsType.MARKET_COMMODITY and self.commodity_var:
            group_vars = [self.market_id_var, self.commodity_var]
        elif fixed_effects == FixedEffectsType.TIME_COMMODITY and self.commodity_var:
            group_vars = [self.time_var, self.commodity_var]
        elif fixed_effects == FixedEffectsType.THREE_WAY and self.commodity_var:
            group_vars = [self.market_id_var, self.time_var, self.commodity_var]
        else:
            logger.warning(f"Fixed effects type {fixed_effects} not fully implemented")
            return transformed_data
            
        # Apply within transformation (demean by groups)
        transform_vars = [self.outcome_var] + self.exog_vars
        
        for var in transform_vars:
            if var in transformed_data.columns:
                group_means = transformed_data.groupby(group_vars)[var].transform('mean')
                transformed_data[var] = transformed_data[var] - group_means
                
        logger.info(f"Applied {fixed_effects.value} fixed effects transformation")
        
        return transformed_data
        
    def _extract_fixed_effects(self, fixed_effects: FixedEffectsType) -> Dict[str, np.ndarray]:
        """Extract estimated fixed effects."""
        # Simplified implementation - would need to store group means during transformation
        return {}
        
    def _calculate_panel_r_squared(self,
                                  spatial_results: Union[SpatialLagResults, SpatialErrorResults],
                                  fixed_effects: FixedEffectsType) -> Dict[str, float]:
        """Calculate within, between, and overall R-squared for panel model."""
        
        # Simplified implementation
        # Full implementation would calculate proper panel R-squared statistics
        
        return {
            'within': 0.0,
            'between': 0.0,
            'overall': 0.0
        }
        
    def _run_panel_diagnostics(self) -> Dict[str, Dict[str, float]]:
        """Run panel-specific diagnostic tests."""
        
        diagnostics = {
            'hausman': {'statistic': 0.0, 'p_value': 1.0},
            'unit_root': {},
            'cd_test': {'statistic': 0.0, 'p_value': 1.0}
        }
        
        # Panel unit root tests (simplified)
        for var in [self.outcome_var] + self.exog_vars:
            diagnostics['unit_root'][var] = {
                'adf_statistic': 0.0,
                'adf_p_value': 1.0,
                'ips_statistic': 0.0,
                'ips_p_value': 1.0
            }
            
        return diagnostics
        
    def predict(self, periods_ahead: int = 1) -> pd.DataFrame:
        """Generate spatial panel forecasts."""
        if self.results is None:
            raise ValueError("Model must be fitted before making predictions")
            
        # Simplified prediction implementation
        # Full implementation would handle spatial and temporal dependencies
        
        predictions = pd.DataFrame({
            'market_id': self.unique_markets,
            'predicted_value': np.zeros(len(self.unique_markets))
        })
        
        return predictions
        
    def summary(self) -> Dict[str, any]:
        """Generate comprehensive model summary."""
        if self.results is None:
            raise ValueError("Model must be fitted before generating summary")
            
        # Get spatial model summary
        if isinstance(self.results.spatial_results, SpatialLagResults):
            spatial_summary = {
                'spatial_parameter': {
                    'rho': self.results.spatial_results.rho,
                    'se': self.results.spatial_results.rho_se,
                    'p_value': self.results.spatial_results.rho_pvalue
                }
            }
        else:  # SpatialErrorResults
            spatial_summary = {
                'spatial_parameter': {
                    'lambda': self.results.spatial_results.lam,
                    'se': self.results.spatial_results.lam_se,
                    'p_value': self.results.spatial_results.lam_pvalue
                }
            }
            
        return {
            'model_type': f'Spatial Panel Model ({self.results.model_type.value})',
            'fixed_effects': self.results.fixed_effects.value,
            'panel_structure': {
                'n_markets': len(self.unique_markets),
                'n_time_periods': len(self.unique_times),
                'n_commodities': len(self.unique_commodities) if self.unique_commodities else None,
                'total_observations': self.results.spatial_results.n_obs
            },
            'spatial_results': spatial_summary,
            'panel_fit': {
                'within_r_squared': self.results.within_r_squared,
                'between_r_squared': self.results.between_r_squared,
                'overall_r_squared': self.results.overall_r_squared
            },
            'panel_diagnostics': {
                'hausman_test': self.results.hausman_test,
                'cross_sectional_dependence': self.results.cross_sectional_dependence_test
            }
        }