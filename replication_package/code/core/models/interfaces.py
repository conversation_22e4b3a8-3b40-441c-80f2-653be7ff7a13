"""Interfaces for econometric models."""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional

import numpy as np
import pandas as pd


@dataclass
class ModelResult:
    """Base class for model results."""
    
    model_type: str
    estimation_date: datetime
    n_observations: int
    parameters: Dict[str, float]
    standard_errors: Dict[str, float]
    t_statistics: Dict[str, float]
    p_values: Dict[str, float]
    r_squared: Optional[float] = None
    adjusted_r_squared: Optional[float] = None
    log_likelihood: Optional[float] = None
    aic: Optional[float] = None
    bic: Optional[float] = None
    diagnostics: Dict[str, Any] = None
    metadata: Dict[str, Any] = None
    
    def is_significant(self, param: str, alpha: float = 0.05) -> bool:
        """Check if parameter is statistically significant."""
        return self.p_values.get(param, 1.0) < alpha
    
    def get_confidence_interval(self, param: str, alpha: float = 0.05) -> Tuple[float, float]:
        """Get confidence interval for parameter."""
        from scipy import stats
        
        coef = self.parameters.get(param, 0.0)
        se = self.standard_errors.get(param, 0.0)
        
        # Calculate critical value
        df = self.n_observations - len(self.parameters)
        t_crit = stats.t.ppf(1 - alpha/2, df)
        
        lower = coef - t_crit * se
        upper = coef + t_crit * se
        
        return (lower, upper)


class Model(ABC):
    """Base interface for all models."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Get model name."""
        pass
    
    @property
    @abstractmethod
    def model_type(self) -> str:
        """Get model type (e.g., 'panel', 'time_series')."""
        pass
    
    @abstractmethod
    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate input data meets model requirements."""
        pass
    
    @abstractmethod
    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for estimation."""
        pass


class Estimator(ABC):
    """Base interface for model estimators."""
    
    @abstractmethod
    def estimate(self, model: Model, data: pd.DataFrame, **kwargs) -> ModelResult:
        """Estimate model parameters."""
        pass
    
    @abstractmethod
    def predict(self, model: Model, result: ModelResult, data: pd.DataFrame) -> np.ndarray:
        """Generate predictions using estimated model."""
        pass
    
    @abstractmethod
    def residuals(self, model: Model, result: ModelResult, data: pd.DataFrame) -> np.ndarray:
        """Calculate residuals."""
        pass