"""Threshold Vector Error Correction Model implementation."""

from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

from .vecm import VECMModel
from ..interfaces import ModelSpecification


class ThresholdVECMModel(VECMModel):
    """
    Threshold Vector Error Correction Model.
    
    Allows for regime-switching behavior in the adjustment process based on
    a threshold variable (e.g., transaction costs, conflict intensity).
    
    This is an advanced Tier 2 model for detecting non-linear market integration.
    """
    
    @property
    def name(self) -> str:
        """Model name."""
        return "Threshold Vector Error Correction Model (TVECM)"
    
    def __init__(self, specification: ModelSpecification):
        """Initialize threshold VECM model."""
        super().__init__(specification)
        
        # Threshold-specific parameters
        self.threshold_variable = specification.parameters.get("threshold_variable")
        self.n_regimes = specification.parameters.get("n_regimes", 2)
        self.threshold_values = specification.parameters.get("threshold_values", None)
        
        # Threshold search parameters
        self.trim_percent = specification.parameters.get("trim_percent", 0.15)
        self.grid_points = specification.parameters.get("grid_points", 100)
        self.bootstrap_reps = specification.parameters.get("bootstrap_reps", 1000)
        
        # Regime-specific parameters
        self.regime_specific_alpha = specification.parameters.get(
            "regime_specific_alpha", True
        )
        self.regime_specific_gamma = specification.parameters.get(
            "regime_specific_gamma", True
        )
        
        # Delay parameter
        self.threshold_delay = specification.parameters.get("threshold_delay", 1)
    
    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate data for threshold VECM."""
        # First run standard VECM validation
        errors = super().validate_data(data)
        
        # Check threshold variable
        if self.threshold_variable:
            if self.threshold_variable not in data.columns:
                errors.append(f"Threshold variable '{self.threshold_variable}' not found")
            else:
                # Check threshold variable properties
                thresh_data = data[self.threshold_variable]
                
                # Need sufficient variation
                if thresh_data.nunique() < 10:
                    errors.append(
                        f"Insufficient variation in threshold variable "
                        f"(only {thresh_data.nunique()} unique values)"
                    )
                
                # Check for missing values
                if thresh_data.isna().any():
                    errors.append("Missing values in threshold variable")
        else:
            # If no threshold variable specified, use error correction term
            if not self.coint_rank or self.coint_rank == 0:
                errors.append(
                    "Threshold variable not specified and no cointegration "
                    "for error correction term"
                )
        
        # Check minimum observations per regime
        min_obs_per_regime = len(self.endogenous_vars) * (self.k_ar_diff or 2) + 5
        total_min_obs = min_obs_per_regime * self.n_regimes
        
        if len(data) < total_min_obs:
            errors.append(
                f"Insufficient observations for {self.n_regimes} regimes: "
                f"have {len(data)}, need at least {total_min_obs}"
            )
        
        return errors
    
    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for threshold VECM estimation."""
        # First apply standard VECM preparation
        prepared = super().prepare_data(data)
        
        # Add lagged threshold variable if using delay
        if self.threshold_variable and self.threshold_delay > 0:
            if self.threshold_variable in prepared.columns:
                prepared[f"{self.threshold_variable}_lag{self.threshold_delay}"] = (
                    prepared[self.threshold_variable].shift(self.threshold_delay)
                )
        
        # Create potential threshold variable if not specified
        if not self.threshold_variable:
            # Use error correction term from linear VECM as threshold
            # Estimate linear VECM first to get ECT
            from statsmodels.tsa.vector_ar.vecm import VECM as SM_VECM
            
            try:
                # Estimate linear VECM to extract ECT
                linear_model = SM_VECM(
                    prepared[self.specification.dependent_variable], 
                    k_ar_diff=self.n_lags - 1,
                    coint_rank=self.coint_rank,
                    deterministic=self.deterministic
                )
                linear_result = linear_model.fit()
                
                # Extract error correction term
                # ECT = Y[t-1] - beta' * Y[t-1] where beta is cointegrating vector
                coint_vec = linear_result.beta
                y_data = prepared[self.specification.dependent_variable].values
                
                if len(y_data.shape) == 1:
                    y_data = y_data.reshape(-1, 1)
                
                # Calculate ECT for each observation
                ect = np.zeros(len(prepared))
                for t in range(1, len(prepared)):
                    ect[t] = y_data[t-1] @ coint_vec[0]  # First cointegrating relation
                
                prepared["ect_threshold"] = ect
                
            except Exception as e:
                # Fallback: use first difference of dependent variable
                logger = get_logger(__name__)
                logger.warning(f"Could not estimate ECT, using first difference: {e}")
                prepared["ect_threshold"] = prepared[self.specification.dependent_variable].diff()
        
        return prepared
    
    def get_diagnostics(self) -> List[str]:
        """Get diagnostic tests for threshold VECM."""
        # Standard VECM diagnostics
        diagnostics = super().get_diagnostics()
        
        # Add threshold-specific tests
        diagnostics.extend([
            "threshold_linearity",     # Test for threshold effects
            "regime_homogeneity",      # Test parameter equality across regimes
            "threshold_constancy",     # Test threshold stability
            "regime_classification",   # Check regime assignment
            "regime_duration"          # Analyze regime persistence
        ])
        
        return diagnostics
    
    def find_threshold_values(
        self,
        data: pd.DataFrame,
        threshold_var: pd.Series
    ) -> Tuple[List[float], float]:
        """
        Find optimal threshold values using grid search.
        
        Returns:
            Tuple of (threshold_values, sum_squared_residuals)
        """
        # Get threshold variable values
        thresh_data = threshold_var.dropna().sort_values()
        n_obs = len(thresh_data)
        
        # Determine search range (trim extreme values)
        trim_n = int(n_obs * self.trim_percent)
        search_range = thresh_data.iloc[trim_n:-trim_n]
        
        if self.n_regimes == 2:
            # Single threshold
            threshold_grid = np.linspace(
                search_range.min(),
                search_range.max(),
                self.grid_points
            )
            
            # Grid search for single threshold
            best_ssr = np.inf
            best_threshold = None
            
            for threshold in threshold_grid:
                # Split data by threshold
                regimes = pd.Series(0, index=data.index)
                regimes[threshold_var > threshold] = 1
                
                # Estimate model for each regime and calculate SSR
                ssr = 0.0
                for regime in [0, 1]:
                    regime_data = data[regimes == regime]
                    if len(regime_data) >= self.specification.parameters.get('min_obs_per_regime', 20):
                        # Simple OLS for each regime (would use full VECM in production)
                        y = regime_data[self.specification.dependent_variable]
                        X = regime_data[self.specification.independent_variables]
                        
                        if len(y) > len(X.columns):
                            try:
                                from sklearn.linear_model import LinearRegression
                                model = LinearRegression()
                                model.fit(X, y)
                                residuals = y - model.predict(X)
                                ssr += np.sum(residuals ** 2)
                            except:
                                ssr += np.inf
                    else:
                        ssr = np.inf
                        break
                
                if ssr < best_ssr:
                    best_ssr = ssr
                    best_threshold = threshold
            
            return [best_threshold], best_ssr
        
        elif self.n_regimes == 3:
            # Two thresholds - 2D grid search
            threshold_grid = np.linspace(
                search_range.min(),
                search_range.max(),
                int(np.sqrt(self.grid_points))
            )
            
            best_ssr = np.inf
            best_thresholds = None
            
            for th1 in threshold_grid[:-1]:
                for th2 in threshold_grid[1:]:
                    if th2 <= th1:
                        continue
                    
                    # Assign regimes
                    regimes = pd.Series(0, index=data.index)
                    regimes[threshold_var > th1] = 1
                    regimes[threshold_var > th2] = 2
                    
                    # Calculate SSR
                    ssr = 0.0
                    valid = True
                    
                    for regime in [0, 1, 2]:
                        regime_data = data[regimes == regime]
                        if len(regime_data) >= self.specification.parameters.get('min_obs_per_regime', 20):
                            y = regime_data[self.specification.dependent_variable]
                            X = regime_data[self.specification.independent_variables]
                            
                            if len(y) > len(X.columns):
                                try:
                                    from sklearn.linear_model import LinearRegression
                                    model = LinearRegression()
                                    model.fit(X, y)
                                    residuals = y - model.predict(X)
                                    ssr += np.sum(residuals ** 2)
                                except:
                                    valid = False
                                    break
                            else:
                                valid = False
                                break
                        else:
                            valid = False
                            break
                    
                    if valid and ssr < best_ssr:
                        best_ssr = ssr
                        best_thresholds = [th1, th2]
            
            return best_thresholds, best_ssr
        
        else:
            raise ValueError(f"Unsupported number of regimes: {self.n_regimes}")
    
    def test_threshold_effects(
        self,
        data: pd.DataFrame,
        test_type: str = "sup-wald"
    ) -> Dict[str, float]:
        """
        Test for presence of threshold effects.
        
        H0: Linear model (no threshold effects)
        H1: Threshold model
        
        Args:
            data: Prepared data
            test_type: Type of test ('sup-wald', 'ave-wald', 'exp-wald')
            
        Returns:
            Dictionary with test statistic and p-value
        """
        # Implement Hansen (1999) sup-Wald test for threshold effects
        from scipy import stats
        from src.core.utils.logging import get_logger
        logger = get_logger(__name__)
        
        # Estimate linear model (null hypothesis)
        y = data[self.specification.dependent_variable]
        X = data[self.specification.independent_variables]
        
        # Add constant if not present
        if 'const' not in X.columns:
            X = pd.concat([pd.Series(1, index=X.index, name='const'), X], axis=1)
        
        # Linear model
        try:
            from sklearn.linear_model import LinearRegression
            linear_model = LinearRegression(fit_intercept=False)
            linear_model.fit(X, y)
            linear_residuals = y - linear_model.predict(X)
            linear_ssr = np.sum(linear_residuals ** 2)
            
            # Find optimal threshold
            threshold_var = data[self.threshold_variable or "ect_threshold"]
            thresholds, threshold_ssr = self.find_threshold_values(data, threshold_var)
            
            # Calculate test statistic
            n = len(data)
            k = len(X.columns)
            
            if test_type == "sup-wald":
                # Sup-Wald test statistic
                f_stat = n * (linear_ssr - threshold_ssr) / threshold_ssr
                
                # Bootstrap critical values (simplified - would use proper bootstrap in production)
                np.random.seed(42)
                bootstrap_stats = []
                
                for _ in range(1000):
                    # Generate data under null (no threshold)
                    boot_residuals = np.random.choice(linear_residuals, size=n, replace=True)
                    boot_y = linear_model.predict(X) + boot_residuals
                    boot_data = data.copy()
                    boot_data[self.specification.dependent_variable] = boot_y
                    
                    # Find threshold on bootstrap data
                    _, boot_ssr = self.find_threshold_values(boot_data, threshold_var)
                    boot_f = n * (linear_ssr - boot_ssr) / boot_ssr
                    bootstrap_stats.append(boot_f)
                
                # Calculate p-value and critical values
                p_value = np.mean(np.array(bootstrap_stats) > f_stat)
                critical_values = {
                    "critical_value_10%": np.percentile(bootstrap_stats, 90),
                    "critical_value_5%": np.percentile(bootstrap_stats, 95),
                    "critical_value_1%": np.percentile(bootstrap_stats, 99)
                }
                
            elif test_type == "ave-wald":
                # Average Wald test (average over all possible thresholds)
                # Simplified implementation
                f_stat = n * (linear_ssr - threshold_ssr) / threshold_ssr
                # Use chi-square approximation
                p_value = 1 - stats.chi2.cdf(f_stat, df=k)
                critical_values = {
                    "critical_value_10%": stats.chi2.ppf(0.90, df=k),
                    "critical_value_5%": stats.chi2.ppf(0.95, df=k),
                    "critical_value_1%": stats.chi2.ppf(0.99, df=k)
                }
                
            else:  # exp-wald
                # Exponential Wald test
                f_stat = n * (linear_ssr - threshold_ssr) / threshold_ssr
                exp_stat = np.exp(0.5 * f_stat)
                # Approximate distribution
                p_value = 2 * (1 - stats.norm.cdf(np.sqrt(2 * np.log(exp_stat))))
                critical_values = {
                    "critical_value_10%": 2 * np.log(1 + stats.norm.ppf(0.95)),
                    "critical_value_5%": 2 * np.log(1 + stats.norm.ppf(0.975)),
                    "critical_value_1%": 2 * np.log(1 + stats.norm.ppf(0.995))
                }
                f_stat = exp_stat
            
            return {
                "test_statistic": float(f_stat),
                "p_value": float(p_value),
                **critical_values
            }
            
        except Exception as e:
            logger.error(f"Error in threshold effects test: {e}")
            # Return conservative result (fail to reject null)
            return {
                "test_statistic": 0.0,
                "p_value": 1.0,
                "critical_value_10%": np.inf,
                "critical_value_5%": np.inf,
                "critical_value_1%": np.inf
            }
    
    def assign_regimes(
        self,
        data: pd.DataFrame,
        threshold_values: List[float]
    ) -> pd.Series:
        """
        Assign observations to regimes based on threshold values.
        
        Returns:
            Series with regime assignments (0, 1, ..., n_regimes-1)
        """
        # Get threshold variable (with delay if specified)
        if self.threshold_delay > 0:
            thresh_col = f"{self.threshold_variable}_lag{self.threshold_delay}"
        else:
            thresh_col = self.threshold_variable or "ect_threshold"
        
        threshold_data = data[thresh_col]
        
        # Initialize with regime 0
        regimes = pd.Series(0, index=data.index)
        
        # Assign based on thresholds
        if self.n_regimes == 2:
            regimes[threshold_data > threshold_values[0]] = 1
        
        elif self.n_regimes == 3:
            regimes[threshold_data > threshold_values[0]] = 1
            regimes[threshold_data > threshold_values[1]] = 2
        
        return regimes
    
    def calculate_regime_probabilities(
        self,
        data: pd.DataFrame,
        threshold_values: List[float],
        smooth_transition: bool = False,
        gamma: float = 10.0
    ) -> pd.DataFrame:
        """
        Calculate regime probabilities.
        
        For sharp threshold model, probabilities are 0 or 1.
        For smooth transition, uses logistic function.
        
        Args:
            data: Data with threshold variable
            threshold_values: Threshold values
            smooth_transition: Whether to use smooth transition
            gamma: Smoothness parameter for logistic transition
            
        Returns:
            DataFrame with probability of each regime
        """
        # Get threshold variable
        thresh_col = (
            f"{self.threshold_variable}_lag{self.threshold_delay}"
            if self.threshold_delay > 0
            else self.threshold_variable or "ect_threshold"
        )
        threshold_data = data[thresh_col]
        
        if not smooth_transition:
            # Sharp transition
            regimes = self.assign_regimes(data, threshold_values)
            
            # Convert to probabilities (one-hot encoding)
            prob_df = pd.DataFrame(index=data.index)
            for regime in range(self.n_regimes):
                prob_df[f"regime_{regime}_prob"] = (regimes == regime).astype(float)
            
        else:
            # Smooth transition (logistic)
            prob_df = pd.DataFrame(index=data.index)
            
            if self.n_regimes == 2:
                # Single threshold
                z = gamma * (threshold_data - threshold_values[0])
                prob_regime_1 = 1 / (1 + np.exp(-z))
                
                prob_df["regime_0_prob"] = 1 - prob_regime_1
                prob_df["regime_1_prob"] = prob_regime_1
            
            else:
                # Multiple thresholds - would implement
                raise NotImplementedError(
                    "Smooth transition not implemented for multiple thresholds"
                )
        
        return prob_df