# Data Transformation Procedures - Master Document
## Yemen Market Integration Research Methodology Package

### Quick Navigation
- **Overview**: Systematic procedures for transforming raw data into analysis-ready panel datasets
- **Key Components**: Panel construction, spatial matching, currency conversion, conflict integration
- **Implementation**: From messy field data to 88.4% balanced panel coverage
- **Cross-References**: Links to data sources (Section 02), methodology (Section 03), code examples (Section 06)

### Search Keywords
**Primary Terms**: data transformation, panel construction, spatial matching, currency conversion, conflict integration
**Technical Terms**: 3D panel data, MNAR imputation, buffer analysis, kernel density, travel time calculation
**Processing Terms**: feature engineering, lagged variables, rolling correlations, seasonal adjustment, outlier detection
**Validation Terms**: data lineage, quality metrics, completeness assessment, spatial accuracy, temporal consistency

---

## Executive Summary

### Transformation Challenge
- **Raw Reality**: Fragmented sources, missing data (38%), inconsistent formats, spatial misalignment
- **Target**: 88.4% complete 3D panel (market × commodity × time) for rigorous econometric analysis
- **Innovation**: Conflict-aware transformation protocols handling systematic missingness
- **Result**: Analysis-ready dataset enabling breakthrough currency fragmentation discovery

### Core Transformations
- **Panel Construction**: 46,368 potential observations → 40,900 valid data points
- **Spatial Matching**: Markets to conflict zones with multiple buffer distances
- **Currency Integration**: Zone-specific exchange rates with temporal variation
- **Feature Engineering**: 50+ derived variables for advanced analysis

---

## Panel Construction Framework

### 3D Panel Structure Design

#### Dimensional Framework
```python
# Panel Dimensions
markets = 28      # Major urban markets
commodities = 23  # Core food items
months = 72      # 2018-2023 period
theoretical_obs = 28 × 23 × 72 = 46,368

# Actual Achievement
actual_obs = 40,900
coverage_rate = 88.4%
balanced_subset = 35,500 (76.5%)
```

#### Base Panel Creation Protocol
```python
def create_base_panel_structure():
    """Generate complete 3D panel framework"""
    
    # Market universe
    markets = [
        'Sana_a', 'Aden', 'Taiz', 'Al_Hudaydah', 'Ibb',
        'Dhamar', 'Al_Mukalla', 'Hajjah', 'Sa_ada', 'Amran',
        # ... complete list of 28 markets
    ]
    
    # Commodity universe  
    commodities = [
        'Wheat_Flour', 'Rice', 'Sugar', 'Vegetable_Oil',
        'Red_Beans', 'Chicken', 'Onions', 'Tomatoes',
        'Fuel_Petrol', 'Fuel_Diesel',
        # ... complete list of 23 items
    ]
    
    # Temporal framework
    date_range = pd.date_range(
        start='2018-01-01',
        end='2023-12-31',
        freq='MS'  # Month start
    )
    
    # Generate all combinations
    panel_index = pd.MultiIndex.from_product(
        [markets, commodities, date_range],
        names=['market_id', 'commodity', 'date']
    )
    
    return pd.DataFrame(index=panel_index).reset_index()
```

### Data Integration Pipeline

#### Stage 1: Raw Data Ingestion
```python
def ingest_raw_sources():
    """Load and validate all data sources"""
    
    sources = {
        'wfp_prices': load_wfp_data('data/raw/wfp/'),
        'exchange_rates': load_exchange_data('data/raw/exchange/'),
        'acled_conflict': load_acled_data('data/raw/acled/'),
        'aid_distribution': load_ocha_data('data/raw/ocha/'),
        'displacement': load_iom_data('data/raw/iom/'),
        'boundaries': load_spatial_data('data/spatial/')
    }
    
    # Validate each source
    validation_results = {}
    for source_name, data in sources.items():
        validation_results[source_name] = validate_source_quality(
            data, source_name
        )
    
    return sources, validation_results
```

#### Stage 2: Standardization and Harmonization
```python
def standardize_data_formats(raw_sources):
    """Harmonize formats across sources"""
    
    standardized = {}
    
    # WFP Price Standardization
    wfp_std = standardize_wfp_prices(raw_sources['wfp_prices'])
    # - Convert all to price per kg
    # - Standardize market names
    # - Validate currency specifications
    # - Handle multiple observations per market-commodity-date
    
    # Exchange Rate Standardization  
    exchange_std = standardize_exchange_rates(raw_sources['exchange_rates'])
    # - Fill missing dates with interpolation
    # - Assign rates to currency zones
    # - Validate against historical ranges
    
    # Conflict Data Standardization
    conflict_std = standardize_acled_data(raw_sources['acled_conflict'])
    # - Geocode event locations
    # - Categorize event types
    # - Calculate fatality metrics
    
    return {
        'prices': wfp_std,
        'exchange_rates': exchange_std,
        'conflict': conflict_std,
        'aid': raw_sources['aid_distribution'],
        'displacement': raw_sources['displacement']
    }
```

#### Stage 3: Panel Integration
```python
def integrate_into_panel(base_panel, standardized_data):
    """Merge all data sources into panel structure"""
    
    panel = base_panel.copy()
    
    # Integrate price data
    panel = panel.merge(
        standardized_data['prices'],
        on=['market_id', 'commodity', 'date'],
        how='left'
    )
    
    # Add exchange rates by currency zone
    panel = add_currency_zones(panel, standardized_data['exchange_rates'])
    
    # Calculate USD prices
    panel['price_usd'] = calculate_usd_prices(
        panel['price_yer'], 
        panel['exchange_rate'],
        panel['currency_zone']
    )
    
    # Integrate conflict data with spatial buffers
    panel = add_conflict_metrics(panel, standardized_data['conflict'])
    
    # Add aid and displacement variables
    panel = add_humanitarian_variables(
        panel, 
        standardized_data['aid'],
        standardized_data['displacement']
    )
    
    return panel
```

---

## Spatial Transformation Procedures

### Market-Conflict Spatial Matching

#### Multiple Buffer Analysis
```python
def create_conflict_buffers(markets_gdf, conflict_gdf):
    """Create multiple buffer distances for sensitivity analysis"""
    
    results = {}
    buffer_distances = [5, 10, 15, 20]  # kilometers
    
    for buffer_km in buffer_distances:
        # Convert km to degrees (rough approximation)
        buffer_deg = buffer_km / 111.32  # km per degree at equator
        
        # Create market buffers
        market_buffers = markets_gdf.copy()
        market_buffers['geometry'] = market_buffers.geometry.buffer(buffer_deg)
        
        # Spatial join with conflict events
        conflict_within_buffer = gpd.sjoin(
            conflict_within_timeframe,
            market_buffers,
            how='inner',
            predicate='within'
        )
        
        # Aggregate conflict metrics
        conflict_metrics = conflict_within_buffer.groupby(
            ['market_id', 'year_month']
        ).agg({
            'fatalities': ['sum', 'mean', 'max'],
            'events': 'count',
            'event_type': lambda x: x.value_counts().index[0]  # most common
        }).round(2)
        
        results[f'conflict_{buffer_km}km'] = conflict_metrics
    
    return results
```

#### Travel Time Calculation
```python
def calculate_realistic_travel_times(markets, road_network, terrain):
    """Calculate travel times accounting for Yemen's geography"""
    
    # Load road network
    roads = gpd.read_file(road_network)
    
    # Classify road types
    road_speeds = {
        'highway': 60,      # km/h
        'primary': 40,
        'secondary': 25,
        'tertiary': 15,
        'track': 8
    }
    
    # Account for terrain
    terrain_friction = {
        'flat': 1.0,
        'hilly': 1.5,
        'mountainous': 2.5,
        'very_mountainous': 4.0
    }
    
    # Calculate between all market pairs
    travel_matrix = np.zeros((len(markets), len(markets)))
    
    for i, origin in enumerate(markets):
        for j, destination in enumerate(markets):
            if i != j:
                travel_time = calculate_shortest_path(
                    origin, destination, roads, terrain_friction
                )
                travel_matrix[i, j] = travel_time
    
    return pd.DataFrame(
        travel_matrix,
        index=markets['market_name'],
        columns=markets['market_name']
    )
```

### Territorial Control Integration

#### Time-Varying Zone Assignment
```python
def assign_currency_zones_temporal(markets, territorial_control):
    """Assign markets to currency zones over time"""
    
    zone_assignments = []
    
    # Process each month
    for date in pd.date_range('2018-01-01', '2023-12-31', freq='MS'):
        
        # Get territorial control for this period
        control_period = territorial_control[
            (territorial_control['start_date'] <= date) &
            (territorial_control['end_date'] >= date)
        ]
        
        # Spatial join markets with control zones
        market_zones = gpd.sjoin(
            markets[['market_id', 'geometry']],
            control_period[['controller', 'geometry']],
            how='left',
            predicate='within'
        )
        
        # Assign exchange rates by controller
        market_zones['currency_zone'] = market_zones['controller'].map({
            'Houthis': 'houthi',
            'Government': 'government', 
            'Southern_Transitional_Council': 'government',
            'Contested': 'contested'
        })
        
        market_zones['date'] = date
        zone_assignments.append(
            market_zones[['market_id', 'date', 'currency_zone']]
        )
    
    return pd.concat(zone_assignments, ignore_index=True)
```

---

## Currency Transformation Protocols

### Exchange Rate Integration

#### Zone-Specific Rate Assignment
```python
def apply_zone_exchange_rates(panel, exchange_rates, zone_assignments):
    """Apply appropriate exchange rate by zone and time"""
    
    # Merge zone assignments
    panel_with_zones = panel.merge(
        zone_assignments,
        on=['market_id', 'date'],
        how='left'
    )
    
    # Handle missing zone assignments
    panel_with_zones['currency_zone'] = panel_with_zones['currency_zone'].fillna('government')
    
    # Merge exchange rates
    panel_with_rates = panel_with_zones.merge(
        exchange_rates,
        on=['date', 'currency_zone'],
        how='left'
    )
    
    # Interpolate missing rates
    panel_with_rates['exchange_rate'] = panel_with_rates.groupby(
        'currency_zone'
    )['exchange_rate'].transform(
        lambda x: x.interpolate(method='linear', limit=3)
    )
    
    return panel_with_rates
```

#### USD Price Calculation
```python
def calculate_usd_prices(panel):
    """Convert YER prices to USD using zone-specific rates"""
    
    def convert_to_usd(row):
        """Convert individual price to USD"""
        
        if pd.isna(row['price']):
            return np.nan
            
        # If already in USD, no conversion needed
        if row.get('currency', 'YER') == 'USD':
            return row['price']
            
        # Convert YER to USD
        if pd.notna(row['exchange_rate']):
            return row['price'] / row['exchange_rate']
        else:
            return np.nan
    
    panel['price_usd'] = panel.apply(convert_to_usd, axis=1)
    
    # Quality check: Flag suspicious conversions
    panel['conversion_flag'] = (
        (panel['price_usd'] > panel['price']) |  # USD > YER (impossible)
        (panel['price_usd'] < 0) |               # Negative prices
        (panel['exchange_rate'] < 100) |         # Suspiciously low rate
        (panel['exchange_rate'] > 5000)          # Suspiciously high rate
    )
    
    return panel
```

---

## Feature Engineering Framework

### Temporal Feature Creation

#### Seasonal and Calendar Effects
```python
def create_temporal_features(panel):
    """Generate comprehensive temporal features"""
    
    features = panel.copy()
    
    # Basic date components
    features['year'] = features['date'].dt.year
    features['month'] = features['date'].dt.month
    features['quarter'] = features['date'].dt.quarter
    
    # Islamic calendar (critical for Yemen)
    features['hijri_month'] = calculate_hijri_month(features['date'])
    features['ramadan'] = features['hijri_month'] == 9
    features['eid_period'] = features['hijri_month'].isin([10, 12])
    
    # Agricultural seasons
    features['harvest_season'] = features.apply(
        lambda x: is_harvest_season(x['date'], x['commodity']), axis=1
    )
    
    # Conflict-related temporal patterns
    features['war_duration'] = (features['date'] - pd.Timestamp('2015-03-26')).dt.days
    features['post_2020_aid_cut'] = features['date'] >= pd.Timestamp('2020-04-01')
    
    return features
```

#### Lagged Variables
```python
def create_lagged_variables(panel, lags=[1, 2, 3, 6, 12]):
    """Create lagged price and conflict variables"""
    
    panel_sorted = panel.sort_values(['market_id', 'commodity', 'date'])
    
    for lag in lags:
        # Lagged prices
        panel_sorted[f'price_usd_lag{lag}'] = panel_sorted.groupby(
            ['market_id', 'commodity']
        )['price_usd'].shift(lag)
        
        # Lagged conflict
        panel_sorted[f'conflict_lag{lag}'] = panel_sorted.groupby(
            'market_id'
        )['conflict_intensity'].shift(lag)
        
        # Price growth rates
        if lag == 1:
            panel_sorted['price_growth'] = (
                panel_sorted['price_usd'] / panel_sorted[f'price_usd_lag{lag}'] - 1
            )
    
    return panel_sorted
```

### Market Integration Metrics

#### Rolling Correlations
```python
def calculate_market_integration_metrics(panel, window=6):
    """Calculate dynamic market integration measures"""
    
    # Price correlations between market pairs
    integration_metrics = []
    
    for commodity in panel['commodity'].unique():
        commodity_data = panel[panel['commodity'] == commodity]
        
        # Pivot to get markets as columns
        price_matrix = commodity_data.pivot_table(
            index='date',
            columns='market_id', 
            values='price_usd'
        )
        
        # Calculate rolling correlations
        rolling_corr = price_matrix.rolling(window=window).corr()
        
        # Extract upper triangle (avoid duplicates)
        for date in rolling_corr.index.get_level_values(0).unique():
            corr_matrix = rolling_corr.loc[date]
            
            # Get upper triangle
            upper_tri = np.triu(corr_matrix.values, k=1)
            markets = corr_matrix.index.tolist()
            
            for i in range(len(markets)):
                for j in range(i+1, len(markets)):
                    if not np.isnan(upper_tri[i, j]):
                        integration_metrics.append({
                            'date': date,
                            'commodity': commodity,
                            'market_i': markets[i],
                            'market_j': markets[j],
                            'correlation': upper_tri[i, j]
                        })
    
    return pd.DataFrame(integration_metrics)
```

### Conflict and Security Features

#### Multi-Scale Conflict Measures
```python
def create_conflict_features(panel, conflict_data):
    """Generate comprehensive conflict intensity measures"""
    
    features = panel.copy()
    
    # Immediate conflict (past month)
    features['conflict_immediate'] = features['conflict_1month']
    
    # Accumulated conflict (past 6 months)
    features['conflict_accumulated'] = features[[
        'conflict_1month', 'conflict_2month', 'conflict_3month',
        'conflict_4month', 'conflict_5month', 'conflict_6month'
    ]].sum(axis=1)
    
    # Conflict trend (increasing/decreasing)
    features['conflict_trend'] = (
        features['conflict_1month'] - features['conflict_3month']
    )
    
    # Event type composition
    event_types = ['battles', 'explosions', 'protests', 'strategic_developments']
    for event_type in event_types:
        features[f'conflict_{event_type}_share'] = (
            features[f'{event_type}_count'] / 
            features['total_events'].replace(0, np.nan)
        )
    
    # Proximity to frontlines
    features['frontline_distance'] = calculate_frontline_distance(
        features[['market_id', 'date']],
        conflict_data
    )
    
    return features
```

---

## Quality Control Procedures

### Data Validation Framework

#### Completeness Assessment
```python
def assess_panel_completeness(panel):
    """Comprehensive completeness analysis"""
    
    metrics = {
        'overall': {
            'total_observations': len(panel),
            'complete_cases': panel.dropna().shape[0],
            'completeness_rate': panel.dropna().shape[0] / len(panel)
        },
        
        'by_dimension': {
            'temporal': panel.groupby('date')['price_usd'].apply(
                lambda x: x.notna().mean()
            ).to_dict(),
            'spatial': panel.groupby('market_id')['price_usd'].apply(
                lambda x: x.notna().mean()
            ).to_dict(),
            'commodity': panel.groupby('commodity')['price_usd'].apply(
                lambda x: x.notna().mean()
            ).to_dict()
        },
        
        'missing_patterns': analyze_missing_patterns(panel),
        'balanced_subsets': find_balanced_subsets(panel)
    }
    
    return CompletentessReport(metrics)
```

#### Outlier Detection
```python
def detect_contextual_outliers(panel):
    """Identify outliers accounting for conflict context"""
    
    outliers = []
    
    for commodity in panel['commodity'].unique():
        commodity_data = panel[panel['commodity'] == commodity]
        
        # Calculate bounds by conflict status
        for conflict_status in ['low', 'medium', 'high']:
            subset = commodity_data[
                commodity_data['conflict_category'] == conflict_status
            ]
            
            if len(subset) > 10:  # Minimum observations
                # Use robust statistics
                q25 = subset['price_usd'].quantile(0.25)
                q75 = subset['price_usd'].quantile(0.75)
                iqr = q75 - q25
                
                lower_bound = q25 - 2.5 * iqr  # Wider bounds for conflict
                upper_bound = q75 + 2.5 * iqr
                
                # Flag outliers
                outlier_indices = subset[
                    (subset['price_usd'] < lower_bound) |
                    (subset['price_usd'] > upper_bound)
                ].index
                
                outliers.extend(outlier_indices.tolist())
    
    panel['outlier_flag'] = panel.index.isin(outliers)
    
    return panel
```

---

## Cross-References and Navigation

### Implementation Resources
- **Code Examples**: Section 06 for detailed implementations
- **Data Sources**: Section 02 for source descriptions
- **Quality Standards**: Throughout for validation protocols
- **Methodology**: Section 03 for analysis applications

### Technical Documentation
- **Spatial Processing**: GeoPandas workflows
- **Panel Methods**: Pandas advanced operations
- **Missing Data**: Statistical imputation techniques
- **Feature Engineering**: Machine learning preprocessing

### Policy Applications
- **Real-time Processing**: Streaming transformation pipelines
- **Monitoring Systems**: Automated quality checks
- **Alert Generation**: Threshold-based transformation
- **Dashboard Feeds**: API-ready data formats

This comprehensive transformation framework enables the conversion of fragmented, conflict-affected data into analysis-ready datasets that support rigorous econometric research and practical policy applications.