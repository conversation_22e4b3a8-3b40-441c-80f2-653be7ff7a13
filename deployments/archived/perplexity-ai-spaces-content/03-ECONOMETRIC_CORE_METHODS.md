# Econometric Core Methods - Master Document
## Yemen Market Integration Research Methodology Package

### Quick Navigation
- **Overview**: Comprehensive three-tier econometric framework for analyzing market integration under currency fragmentation
- **Key Components**: Panel models, cointegration analysis, VECM specifications, identification strategies
- **Implementation**: From basic pooled models to advanced error correction with conflict adaptations
- **Cross-References**: Links to theoretical foundation (Section 01), data infrastructure (Section 02), advanced methods (Section 03)

### Search Keywords
**Primary Terms**: three-tier econometric framework, panel data models, cointegration analysis, Vector Error Correction Models, market integration
**Technical Terms**: Driscoll-Kraay standard errors, Arellano-Bond GMM, Johansen cointegration, Engle-Granger test, Phillips-Ouliaris, ARDL bounds testing
**Conflict Terms**: regime switching, structural breaks, conflict-aware missing data, event studies, threshold cointegration
**Application Terms**: currency zone effects, exchange rate mechanism testing, aid effectiveness measurement, policy simulation

---

## Executive Summary

### Three-Tier Framework Innovation
- **Tier 1**: Pooled panel analysis establishing baseline currency zone effects
- **Tier 2**: Commodity-specific cointegration revealing long-run relationships
- **Tier 3**: Advanced validation ensuring robustness and external validity
- **Integration**: Seamless progression from simple to sophisticated methods

### World Bank Publication Standards
- **Methodological Rigor**: Complete mathematical specifications with diagnostic testing
- **Causal Identification**: Natural experiments, instrumental variables, structural breaks
- **Policy Relevance**: Direct pathways from econometric results to humanitarian decisions
- **External Validation**: Cross-country testing protocols for generalization

---

## Tier 1: Pooled Panel Analysis

### Core Specification Framework

#### Multi-Dimensional Panel Structure
```
Data Structure: P_{i,j,t}
- i: Markets (28 major markets)
- j: Commodities (23 food items)  
- t: Time (72 months, 2018-2023)
- Total observations: 46,368 potential
- Actual coverage: 88.4% with conflict-aware handling
```

#### Base Panel Model
```
Mathematical Specification:
P_{i,j,t} = α + θ_i + φ_j + τ_t + δ·Conflict_{i,t} + γ·Zone_{i,t} + β'X_{i,j,t} + ε_{i,j,t}

Where:
- P_{i,j,t}: Log price of commodity j in market i at time t
- θ_i: Market fixed effects (unobserved market characteristics)
- φ_j: Commodity fixed effects (product-specific factors)  
- τ_t: Time fixed effects (macro shocks, seasonal patterns)
- Conflict_{i,t}: Conflict intensity measures with spatial buffers
- Zone_{i,t}: Currency zone indicators (Houthi vs Government)
- X_{i,j,t}: Time-varying controls (aid, displacement, infrastructure)
```

### Currency Zone Testing (H1 Hypothesis)

#### Dual Currency Analysis
```python
# H1 Test: Exchange Rate Mechanism
def test_currency_zone_effects(panel_data):
    """Test if currency zones affect prices differently in YER vs USD"""
    
    # Model 1: YER Price Analysis
    yer_model = smf.mixedlm(
        'log_price_yer ~ conflict_intensity + C(currency_zone) + aid_presence + displacement + C(commodity) + C(month)',
        data=panel_data,
        groups=panel_data['market_id']
    ).fit()
    
    # Model 2: USD Price Analysis  
    usd_model = smf.mixedlm(
        'log_price_usd ~ conflict_intensity + C(currency_zone) + aid_presence + displacement + C(commodity) + C(month)',
        data=panel_data,
        groups=panel_data['market_id']
    ).fit()
    
    # Test: Zone effects should be significant in YER, not in USD
    yer_zone_pvalue = yer_model.pvalues['C(currency_zone)[T.houthi]']
    usd_zone_pvalue = usd_model.pvalues['C(currency_zone)[T.houthi]']
    
    h1_support = (yer_zone_pvalue < 0.05) and (usd_zone_pvalue > 0.10)
    
    return {
        'h1_supported': h1_support,
        'yer_zone_effect': yer_model.params['C(currency_zone)[T.houthi]'],
        'usd_zone_effect': usd_model.params['C(currency_zone)[T.houthi]'],
        'interpretation': interpret_currency_results(yer_model, usd_model)
    }
```

### Advanced Panel Specifications

#### Dynamic Panel Models
```python
# Arellano-Bond GMM for dynamic effects
def estimate_dynamic_panel(panel_data):
    """Estimate dynamic panel with lagged dependent variable"""
    
    # First-difference GMM specification
    model_spec = """
    log_price_usd = β₀ + β₁·log_price_usd_{t-1} + β₂·conflict_intensity + 
                    β₃·currency_zone + β₄·aid_effectiveness + 
                    β₅·exchange_rate_volatility + market_FE + time_FE
    """
    
    # Use lagged levels as instruments
    instruments = ['log_price_usd_lag2', 'log_price_usd_lag3', 
                   'conflict_intensity_lag1', 'aid_lag1']
    
    # Arellano-Bond estimation
    dynamic_model = PanelOLS.from_formula(
        'log_price_usd ~ 1 + log_price_usd_lag1 + conflict_intensity + EntityEffects + TimeEffects',
        data=panel_data.set_index(['market_id', 'date'])
    ).fit(cov_type='kernel')
    
    # Hansen test for instrument validity
    hansen_test = dynamic_model.diagnostics['hansen_test']
    
    return dynamic_model, hansen_test
```

#### Spatial Correlation Handling
```python
# Driscoll-Kraay standard errors for spatial correlation
def estimate_with_spatial_correlation(panel_data):
    """Account for spatial correlation in conflict spillovers"""
    
    # Create spatial weights matrix
    spatial_weights = create_spatial_weights_matrix(
        markets_coordinates, 
        decay_function='inverse_distance',
        cutoff_km=50
    )
    
    # Panel regression with spatial correlation
    model = PanelOLS.from_formula(
        'log_price_usd ~ conflict_intensity + currency_zone + aid_presence + EntityEffects + TimeEffects',
        data=panel_data.set_index(['market_id', 'date'])
    ).fit(cov_type='kernel', kernel='bartlett', bandwidth=3)
    
    # Test for spatial correlation
    moran_test = spatial_autocorr_test(model.resids, spatial_weights)
    
    return model, moran_test
```

---

## Tier 2: Commodity-Specific Cointegration Analysis

### Long-Run Relationship Testing

#### Multiple Cointegration Approaches
```python
def comprehensive_cointegration_testing(commodity_data):
    """Apply multiple cointegration tests for robustness"""
    
    results = {}
    
    # 1. Engle-Granger Two-Step
    eg_result = engle_granger_test(
        commodity_data['log_price_market1'],
        commodity_data['log_price_market2']
    )
    results['engle_granger'] = eg_result
    
    # 2. Johansen Maximum Likelihood
    johansen_result = johansen_cointegration_test(
        commodity_data[['log_price_market1', 'log_price_market2', 'log_exchange_rate']]
    )
    results['johansen'] = johansen_result
    
    # 3. Phillips-Ouliaris Residual-Based
    po_result = phillips_ouliaris_test(
        commodity_data[['log_price_market1', 'log_price_market2']]
    )
    results['phillips_ouliaris'] = po_result
    
    # 4. ARDL Bounds Testing (for mixed integration orders)
    ardl_result = ardl_bounds_test(
        endog=commodity_data['log_price_market1'],
        exog=commodity_data[['log_price_market2', 'log_exchange_rate', 'conflict_intensity']]
    )
    results['ardl_bounds'] = ardl_result
    
    return results
```

#### Panel Cointegration Extensions
```python
def panel_cointegration_analysis(multi_market_data):
    """Pedroni panel cointegration for heterogeneous panels"""
    
    # Prepare panel data
    panel = multi_market_data.set_index(['market_pair', 'date'])
    
    # Pedroni cointegration tests
    pedroni_results = {
        'panel_v': pedroni_panel_v_test(panel),
        'panel_rho': pedroni_panel_rho_test(panel),
        'panel_pp': pedroni_panel_pp_test(panel),
        'panel_adf': pedroni_panel_adf_test(panel),
        'group_rho': pedroni_group_rho_test(panel),
        'group_pp': pedroni_group_pp_test(panel),
        'group_adf': pedroni_group_adf_test(panel)
    }
    
    # Consensus interpretation
    cointegration_evidence = interpret_pedroni_results(pedroni_results)
    
    return pedroni_results, cointegration_evidence
```

### Vector Error Correction Models (VECM)

#### VECM Specification for Market Integration
```python
def estimate_vecm_market_integration(price_data):
    """Estimate VECM for price transmission analysis"""
    
    # Test for cointegration rank
    rank_test = select_coint_rank(
        price_data[['log_price_market1', 'log_price_market2', 'log_exchange_rate']],
        det_order=1,
        k_ar_diff=2
    )
    
    # Estimate VECM with identified rank
    vecm_model = VECM(
        endog=price_data[['log_price_market1', 'log_price_market2']],
        exog=price_data[['conflict_intensity', 'aid_presence']],
        k_ar_diff=2,
        coint_rank=rank_test.rank,
        deterministic='ci'  # Constant in cointegrating relationship
    ).fit()
    
    # Extract long-run relationship
    alpha = vecm_model.alpha  # Adjustment coefficients
    beta = vecm_model.beta    # Cointegrating vectors
    
    # Test restrictions on cointegrating vector
    # H0: Perfect integration (β = [1, -1])
    restriction_test = test_cointegration_restriction(
        vecm_model, 
        restriction_matrix=np.array([[1, -1]])
    )
    
    return vecm_model, restriction_test
```

#### Impulse Response Analysis
```python
def vecm_impulse_responses(vecm_model, periods=24):
    """Generate impulse responses for policy analysis"""
    
    # Orthogonalized impulse responses
    irf = vecm_model.irf(periods=periods)
    
    # Plot responses to conflict shock
    conflict_shock_responses = {
        'market1_response': irf.irfs[:, 0, 2],  # Market 1 response to conflict
        'market2_response': irf.irfs[:, 1, 2],  # Market 2 response to conflict
        'confidence_bands': {
            'lower': irf.cum_effect_cis[:, :, 2, 0],
            'upper': irf.cum_effect_cis[:, :, 2, 1]
        }
    }
    
    # Variance decomposition
    fevd = vecm_model.fevd(periods=periods)
    
    return conflict_shock_responses, fevd
```

### Structural Break Cointegration

#### Gregory-Hansen Test for Regime Changes
```python
def test_cointegration_with_breaks(price_data):
    """Test for cointegration allowing for structural breaks"""
    
    # Gregory-Hansen test with different break types
    break_tests = {}
    
    # Level shift (C model)
    break_tests['level_shift'] = gregory_hansen_test(
        price_data['log_price_market1'],
        price_data['log_price_market2'],
        model='C'
    )
    
    # Level shift with trend (C/T model)  
    break_tests['level_trend'] = gregory_hansen_test(
        price_data['log_price_market1'],
        price_data['log_price_market2'],
        model='C/T'
    )
    
    # Regime shift (C/S model)
    break_tests['regime_shift'] = gregory_hansen_test(
        price_data['log_price_market1'],
        price_data['log_price_market2'],
        model='C/S'
    )
    
    # Identify break dates and estimate break models
    if any([test['reject_null'] for test in break_tests.values()]):
        break_date = identify_optimal_break_date(price_data)
        break_vecm = estimate_vecm_with_break(price_data, break_date)
        return break_tests, break_vecm
    
    return break_tests, None
```

---

## Advanced Diagnostic Testing

### Model Validation Framework

#### Comprehensive Diagnostics Suite
```python
def comprehensive_model_diagnostics(model, data):
    """Complete diagnostic testing for panel models"""
    
    diagnostics = {}
    
    # 1. Residual Analysis
    diagnostics['normality'] = jarque_bera_test(model.resids)
    diagnostics['serial_correlation'] = breusch_godfrey_test(model, lags=3)
    diagnostics['heteroskedasticity'] = white_test(model)
    
    # 2. Panel-Specific Tests
    diagnostics['cross_section_dependence'] = pesaran_cd_test(model.resids)
    diagnostics['panel_unit_root'] = {
        'levin_lin_chu': levin_lin_chu_test(data['log_price_usd']),
        'im_pesaran_shin': im_pesaran_shin_test(data['log_price_usd']),
        'adf_fisher': adf_fisher_test(data['log_price_usd'])
    }
    
    # 3. Structural Stability
    diagnostics['parameter_stability'] = chow_test(model, break_point=0.7)
    diagnostics['cusum_test'] = cusum_stability_test(model)
    
    # 4. Specification Tests
    diagnostics['ramsey_reset'] = ramsey_reset_test(model)
    diagnostics['hausman_test'] = hausman_specification_test(model)
    
    return diagnostics
```

### Robustness Testing Protocol

#### Multiple Specification Testing
```python
def robustness_testing_suite(data):
    """Test robustness across multiple specifications"""
    
    specifications = {
        'baseline': 'log_price_usd ~ conflict_intensity + currency_zone + EntityEffects + TimeEffects',
        'extended_controls': 'log_price_usd ~ conflict_intensity + currency_zone + aid_presence + displacement + rainfall + EntityEffects + TimeEffects',
        'interactions': 'log_price_usd ~ conflict_intensity*currency_zone + aid_presence*currency_zone + EntityEffects + TimeEffects',
        'non_linear': 'log_price_usd ~ conflict_intensity + I(conflict_intensity**2) + currency_zone + EntityEffects + TimeEffects',
        'lagged_effects': 'log_price_usd ~ conflict_intensity + conflict_intensity_lag1 + currency_zone + EntityEffects + TimeEffects'
    }
    
    results = {}
    for spec_name, formula in specifications.items():
        model = PanelOLS.from_formula(
            formula,
            data=data.set_index(['market_id', 'date'])
        ).fit(cov_type='kernel')
        
        results[spec_name] = {
            'coefficients': model.params,
            'std_errors': model.std_errors,
            'r_squared': model.rsquared,
            'diagnostics': comprehensive_model_diagnostics(model, data)
        }
    
    # Test coefficient stability across specifications
    coefficient_stability = test_coefficient_stability(results)
    
    return results, coefficient_stability
```

---

## Policy Application Framework

### Aid Effectiveness Testing (H2 Hypothesis)

#### Triple Difference Specification
```python
def test_aid_effectiveness_by_zone(panel_data):
    """Test differential aid effectiveness by currency zone"""
    
    # Triple difference: Post × Aid × Currency_Zone
    model = PanelOLS.from_formula(
        '''log_price_usd ~ 
           aid_presence + 
           C(currency_zone) + 
           post_aid_delivery +
           aid_presence:C(currency_zone) +
           aid_presence:post_aid_delivery +
           C(currency_zone):post_aid_delivery +
           aid_presence:C(currency_zone):post_aid_delivery +
           EntityEffects + TimeEffects''',
        data=panel_data.set_index(['market_id', 'date'])
    ).fit(cov_type='clustered', cluster_entity=True)
    
    # Key coefficient: Triple interaction
    triple_diff_coeff = model.params['aid_presence:C(currency_zone)[T.houthi]:post_aid_delivery']
    
    # Policy interpretation
    aid_effectiveness = calculate_aid_effectiveness_differential(model)
    
    return model, aid_effectiveness
```

### Exchange Rate Threshold Testing (H9)

#### Threshold VECM Implementation
```python
def estimate_threshold_vecm(data, threshold_variable='exchange_rate_differential'):
    """Estimate TVECM for threshold effects"""
    
    # Grid search for optimal threshold
    threshold_grid = np.percentile(
        data[threshold_variable], 
        np.arange(10, 91, 5)
    )
    
    best_threshold = None
    best_likelihood = -np.inf
    
    for threshold in threshold_grid:
        # Split sample by threshold
        regime_1 = data[data[threshold_variable] <= threshold]
        regime_2 = data[data[threshold_variable] > threshold]
        
        if len(regime_1) > 30 and len(regime_2) > 30:  # Minimum observations
            # Estimate separate VECMs
            vecm_1 = estimate_vecm_regime(regime_1)
            vecm_2 = estimate_vecm_regime(regime_2)
            
            # Combined likelihood
            combined_likelihood = vecm_1.llf + vecm_2.llf
            
            if combined_likelihood > best_likelihood:
                best_likelihood = combined_likelihood
                best_threshold = threshold
    
    # Estimate final TVECM with optimal threshold
    tvecm_model = estimate_final_tvecm(data, best_threshold)
    
    return tvecm_model, best_threshold
```

---

## Cross-References and Navigation

### Implementation Resources
- **Code Examples**: Section 06 for complete implementations
- **Data Requirements**: Section 02 for panel construction
- **Advanced Methods**: Section 03 for ML and Bayesian extensions
- **Validation**: Section 04 for external testing

### Theoretical Foundation
- **Hypothesis Testing**: Section 01 for H1-H10 specifications
- **Economic Theory**: Section 01 for spatial equilibrium
- **Policy Framework**: Section 09 for applications
- **Results Interpretation**: Section 07 for output templates

### Quality Assurance
- **Diagnostic Protocols**: Built into all specifications
- **Robustness Standards**: Multiple specification testing
- **Publication Standards**: World Bank compliance throughout
- **External Validation**: Cross-country testing protocols

This comprehensive econometric framework provides the methodological foundation for rigorously testing currency fragmentation effects on market integration, with specifications designed to meet World Bank publication standards while enabling direct policy applications.