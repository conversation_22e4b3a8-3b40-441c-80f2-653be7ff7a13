# Monthly Data Analysis Methodology for Conflict Economics

**Version:** 1.0
**Date:** June 1, 2025
**Prepared for:** Yemen Market Integration Research Project

## 1. Introduction

This document outlines the statistical procedures and methodologies specifically tailored for analyzing **monthly** price and conflict-related data within the context of the Yemen Market Integration research. Analyzing monthly data in conflict settings presents unique challenges, including potential irregularities, heightened volatility, structural breaks induced by conflict events, and the need for robust seasonal adjustment and noise reduction techniques.

The user instructions explicitly emphasized optimizing methodologies for **monthly data frequency**, correcting any potential misconception about using higher-frequency (daily/weekly) data for the core analysis framework. This guide details the appropriate time series techniques, seasonal adjustment methods, structural break detection protocols, and noise handling strategies suitable for this monthly frequency, ensuring the analysis aligns with World Bank standards for rigor and robustness.

We will cover:
- Characteristics of Monthly Conflict-Zone Data
- Stationarity Assessment for Monthly Series
- Robust Seasonal Adjustment using STL Decomposition
- Structural Break Detection in Monthly Data
- Noise Reduction and Filtering Techniques
- Considerations for Modeling Monthly Dynamics

## 2. Characteristics of Monthly Conflict-Zone Data

Monthly data (e.g., average monthly prices, monthly conflict event counts, monthly exchange rates) offers a balance between capturing dynamic changes and smoothing out high-frequency noise often present in daily or weekly data from conflict zones. However, it still possesses specific characteristics requiring careful methodological handling:

- **Seasonality:** Underlying seasonal patterns in agricultural commodities or economic activity may persist but could be distorted by conflict.
- **Structural Breaks:** Conflict events (battles, policy changes, supply route disruptions) can cause abrupt shifts in the mean, variance, or persistence of time series.
- **Volatility Clustering:** Periods of high and low price volatility may occur, linked to conflict intensity or economic instability.
- **Measurement Error/Noise:** Data collection in conflict zones can be challenging, potentially leading to higher measurement error or noise compared to stable environments.
- **Missing Data:** Data collection gaps are common.
- **Non-Stationarity:** Price series are typically non-stationary (I(1)), requiring differencing or cointegration analysis.

## 3. Stationarity Assessment for Monthly Series

Determining the order of integration is crucial before modeling.

**Protocol:** Apply both individual and panel unit root tests to log price series and key explanatory variables (e.g., conflict intensity).

**Methods:**

1.  **Augmented Dickey-Fuller (ADF) Test (for individual series):**
    *   **Specification:** Test the null hypothesis of a unit root ($H_0: \gamma = 0$) in the regression:
        $$\Delta y_t = \alpha + \beta t + \gamma y_{t-1} + \sum_{i=1}^{p} \delta_i \Delta y_{t-i} + \epsilon_t$$
    *   **Implementation:** Use `statsmodels.tsa.stattools.adfuller`. Select appropriate deterministic terms (`c`, `ct`) and lag length (`p`) using information criteria (AIC, BIC).
    *   **Considerations for Monthly Data:** Choose a reasonable `maxlag` (e.g., related to $\sqrt[3]{T}$ or Schwert criterion, often around 12-18 for typical macroeconomic monthly series lengths). Check residuals for autocorrelation.
    ```python
    from statsmodels.tsa.stattools import adfuller

    def check_stationarity_adf(series, max_lags=12, regression=\'ct\'):
        result = adfuller(series.dropna(), maxlag=max_lags, regression=regression, autolag=\'AIC\')
        print(f\'ADF Statistic: {result[0]:.4f}\')
        print(f\'p-value: {result[1]:.4f}\')
        print(f\'Lags Used: {result[2]}\')
        return result[1] < 0.05 # Returns True if stationary at 5%
    ```

2.  **Im-Pesaran-Shin (IPS) Panel Unit Root Test:**
    *   **Specification:** Averages individual ADF statistics across panel units. $H_0$: All panels contain a unit root. $H_1$: Some panels are stationary.
    *   **Implementation:** Requires running ADF on each panel unit and then calculating the standardized average t-statistic ($W_{tbar}$). Can be implemented manually or using specialized packages if available (though less common than ADF/KPSS in standard libraries).
    *   **Considerations for Monthly Data:** Ensure sufficient time periods (T) per entity for reliable ADF tests. Account for cross-sectional dependence if suspected (though IPS is more robust than some alternatives like LLC).
    ```python
    # Conceptual implementation (referencing time-series.md)
    # Requires running adfuller for each entity and standardizing the average ADF stat
    # See ips_panel_unit_root_test in time-series.md for a more detailed example
    ```

## 4. Robust Seasonal Adjustment (STL Decomposition)

Standard seasonal adjustment methods (like X-13-ARIMA-SEATS) can be sensitive to outliers and abrupt changes common in conflict data. STL (Seasonal and Trend decomposition using Loess) provides a more robust alternative.

**Protocol:** Apply STL decomposition to individual monthly log price series before further time series modeling (e.g., VECM, MSM) if seasonality is significant.

**Method:**

1.  **STL Decomposition:**
    *   **Specification:** Additively decomposes a series $Y_t$ into trend ($T_t$), seasonal ($S_t$), and remainder ($R_t$) components: $Y_t = T_t + S_t + R_t$. Uses iterated Loess smoothing, making it robust to outliers.
    *   **Implementation:** Use `statsmodels.tsa.seasonal.STL`.
    *   **Considerations for Monthly Data:** Set `period=12`. Adjust smoothing parameters (`seasonal`, `trend`, `low_pass`) if default values yield poor decomposition (e.g., seasonal component leaking into trend or remainder). Use `robust=True` for enhanced robustness to outliers.
    ```python
    from statsmodels.tsa.seasonal import STL
    import matplotlib.pyplot as plt

    def apply_stl(series, period=12, robust=True):
        stl = STL(series.dropna(), period=period, robust=robust)
        res = stl.fit()
        
        fig = res.plot()
        plt.show()
        
        seasonally_adjusted = series - res.seasonal
        return seasonally_adjusted, res.trend, res.seasonal, res.resid
    
    # Example usage for a specific entity
    # entity_series = panel_data.loc[entity_id][\"log_price\"]
    # adjusted_series, trend, seasonal, resid = apply_stl(entity_series)
    ```

## 5. Structural Break Detection in Monthly Data

Identifying conflict-induced breaks is critical for accurate modeling.

**Protocol:** Apply structural break tests to key series (log prices, price differences, conflict intensity) to identify potential break dates. Use these dates to inform regime-switching models or split-sample analyses.

**Methods:**

1.  **CUSUM Test (for OLS Residuals):**
    *   **Specification:** Tests for parameter instability in a linear regression model by examining the cumulative sum of residuals. Useful for detecting gradual changes or single breaks.
    *   **Implementation:** Fit a baseline model (e.g., AR model for $\Delta P_{i,j,t}$), then use `statsmodels.stats.diagnostic.breaks_cusumolsresid` on the residuals.
    *   **Considerations for Monthly Data:** Less effective at pinpointing multiple distinct break dates compared to methods like Bai-Perron.

2.  **Bai-Perron Test (Multiple Breakpoints):**
    *   **Specification:** Tests for multiple unknown structural breaks in a linear regression model by minimizing the sum of squared residuals across different segmentations. Allows specifying a maximum number of breaks or using information criteria.
        $$y_t = x_t'\beta_j + u_t, \quad t = T_{j-1}+1, ..., T_j$$
    *   **Implementation:** Use the `ruptures` library in Python. Common choices include `ruptures.Pelt` (for fast detection using penalized likelihood) or `ruptures.Dynp` (dynamic programming for fixed number of breaks).
    *   **Considerations for Monthly Data:** Requires specifying a minimum segment length (e.g., 12 or 24 months) to avoid spurious breaks. Choice of penalty value (`pen` in Pelt) or number of breaks (`n_bkps` in Dynp) is crucial and may require sensitivity analysis.
    ```python
    import ruptures as rpt

    def detect_breaks_bai_perron(series, model="l2", min_size=12, pen_value=None, n_breaks=None):
        """Detect breaks using Pelt algorithm from ruptures package."""
        points = series.dropna().values
        algo = rpt.Pelt(model=model, min_size=min_size)
        
        if pen_value:
            algo.fit(points)
            result = algo.predict(pen=pen_value)
        elif n_breaks:
            # Dynp is often used for fixed number, Pelt needs penalty
            # For fixed number with Pelt, might need to search penalty or use Dynp
            algo_dynp = rpt.Dynp(model=model, min_size=min_size, jump=1)
            algo_dynp.fit(points)
            result = algo_dynp.predict(n_bkps=n_breaks)
        else:
            # Default penalty calculation (may need tuning)
            algo.fit(points)
            default_pen = rpt.costs.CostLinear().error(len(points)) # Example penalty
            result = algo.predict(pen=default_pen)

        # Convert indices to dates
        break_indices = [idx + series.dropna().index[0] for idx in result[:-1]] # Exclude end point
        break_dates = series.index[break_indices]
        print(f"Detected {len(break_dates)} breaks at: {break_dates.tolist()}")
        return break_dates

    # Example usage:
    # conflict_series = panel_data.loc[market_id][\"conflict_intensity\"]
    # break_dates = detect_breaks_bai_perron(conflict_series, pen_value=np.log(len(conflict_series))*2) # Example penalty
    ```

## 6. Noise Reduction and Filtering Techniques

Addressing potential measurement error or idiosyncratic noise in monthly conflict-zone data.

**Protocol:** Apply appropriate filtering or robust estimation techniques, especially when noise is suspected to be high.

**Methods:**

1.  **Moving Averages:**
    *   **Specification:** Simple smoothing by averaging over a rolling window (e.g., 3-month moving average).
    *   **Implementation:** `pandas.Series.rolling(window=3).mean()`.
    *   **Considerations:** Can induce lag and distort timing of peaks/troughs. Use cautiously, primarily for visualization or as input to very simple models.

2.  **Robust Regression Methods:**
    *   **Specification:** Use estimators less sensitive to outliers than OLS (e.g., M-estimators like Huber or Least Absolute Deviations - LAD).
    *   **Implementation:** `statsmodels.robust.robust_linear_model.RLM`.
    *   **Considerations:** Useful when specific observations (e.g., extreme price spikes) are suspected to be outliers unduly influencing OLS results.

3.  **Kalman Filter (as part of State-Space Models):**
    *   **Specification:** Inherently filters noise by separating the underlying state from observation noise (see Section 4.2.3 in Integration Guide).
    *   **Implementation:** Via `statsmodels.tsa.statespace` models.
    *   **Considerations:** Provides a model-based approach to filtering, estimating the signal-to-noise ratio from the data.

4.  **Wavelet Filtering (Advanced):**
    *   **Specification:** Decomposes series into different frequency components, allowing noise (often high-frequency) to be filtered out.
    *   **Implementation:** Requires specialized libraries like `PyWavelets`.
    *   **Considerations:** More complex method, typically used when specific frequency characteristics of noise are known or hypothesized.

## 7. Considerations for Modeling Monthly Dynamics

While detailed model specifications are in other guides, analyzing monthly data requires specific considerations:

- **Lag Length Selection:** Standard criteria (AIC, BIC) are applicable, but consider economic intuition (e.g., information lags in monthly data might be 1-3 months). Maximum lags in VAR/VECM for monthly data often range from 1 to 6, rarely exceeding 12.
- **Seasonality in Models:** If STL adjustment is not performed beforehand, include seasonal dummies or use SARIMA/Seasonal State-Space components in models like SARIMAX or UCM.
- **Interpreting Coefficients:** Coefficients on monthly lags represent month-on-month dynamics. Long-run effects need to be calculated based on model parameters (e.g., in VECM or ARDL models).
- **Temporal Aggregation:** Be mindful that monthly averages might mask significant intra-month volatility or events. Acknowledge this limitation.

## 8. Conclusion

Analyzing monthly data from conflict zones like Yemen requires careful application of time series techniques adapted to the data frequency and context. Robust methods for seasonal adjustment (STL), structural break detection (Bai-Perron), and appropriate stationarity testing (ADF, IPS) are crucial. Addressing potential noise through filtering or robust estimation further strengthens the analysis. By adhering to these protocols, the Yemen Market Integration research can effectively leverage its monthly data to produce rigorous and reliable insights, meeting the standards required for academic publication and policy relevance.

