# Practical Guide: Step-by-Step Implementation Examples Using Yemen Data

**Version:** 1.0
**Date:** June 2, 2025
**Audience:** Data Analysts, Econometricians, Researchers

## 1. Introduction

This guide provides concrete, step-by-step examples of how to implement the advanced econometric and machine learning methods (from Phase 1) using realistic Yemen data structures and scenarios. It bridges the gap between the theoretical descriptions in `MethodologyIntegrationGuideforAdvancedEconometricMethods.md` and `Step-by-StepImplementationofAdvancedEconometricMethods.md` and practical application.

Each example assumes you have prepared your data according to the procedures in `yemen-specific-data-adapters.md`, resulting in a panel DataFrame (`final_panel_df`) with appropriate identifiers (`market_id`, `commodity_id`, `date`), prices (`price_usd`, `log_price_usd`), conflict metrics (`conflict_intensity`), currency zone information (`currency_zone`), and other relevant controls.

**Note:** These examples use standard Python libraries (`scikit-learn`, `statsmodels`, `linearmodels`, `pymc`). Ensure your environment is set up as per `Step-by-StepImplementation...`. Code snippets are illustrative and may require adaptation based on your exact data and research questions.

## 2. ML Clustering: Taiz vs Aden Market Segmentation

**Objective:** Identify distinct market clusters based on characteristics like price volatility and conflict exposure, focusing on comparing Taiz (often contested) and Aden (government hub).

**Method:** K-Means Clustering (Reference: `ml_pattern_recognition.py`, `MethodologyIntegrationGuide...` Section 4.1.1)

**Assumed Input Data:** A market-level DataFrame (`market_features_df`) derived from the main panel, indexed by `market_id`, with columns like `avg_price_volatility`, `avg_conflict_intensity`, `distance_to_port`, `currency_zone_govt` (dummy).

```python
import pandas as pd
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns

# --- Assume market_features_df exists ---
# Example structure:
# market_id | avg_price_volatility | avg_conflict_intensity | distance_to_port | currency_zone_govt
# ----------|----------------------|------------------------|------------------|--------------------
# ADEN      | 0.15                 | 5                      | 10               | 1
# TAIZ      | 0.30                 | 50                     | 150              | 0 # (Simplified)
# SANA      | 0.05                 | 15                     | 200              | 0
# ...       | ...                  | ...                    | ...              | ...

# --- Implementation ---
def segment_markets(market_features_df, features_to_use, n_clusters=3):
    """Segments markets using K-Means clustering."""
    print(f"Segmenting markets based on: {features_to_use}")
    
    # Select features and handle missing values (e.g., fill with mean)
    features = market_features_df[features_to_use].fillna(market_features_df[features_to_use].mean())
    
    # Scale features
    scaler = StandardScaler()
    scaled_features = scaler.fit_transform(features)
    
    # Apply K-Means
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
    cluster_labels = kmeans.fit_predict(scaled_features)
    
    # Add labels back to the original DataFrame
    market_features_df["cluster_label"] = cluster_labels
    
    print(f"Assigned markets to {n_clusters} clusters.")
    
    # Analyze cluster characteristics (example)
    cluster_summary = market_features_df.groupby("cluster_label")[features_to_use].mean()
    print("\nCluster Characteristics (Averages):")
    print(cluster_summary)
    
    # Check Taiz and Aden
    print("\nCluster for Key Markets:")
    print(market_features_df.loc[["TAIZ", "ADEN"], ["cluster_label"] + features_to_use])
    
    return market_features_df, kmeans

# --- Example Usage ---
# features = ["avg_price_volatility", "avg_conflict_intensity", "distance_to_port"]
# clustered_markets_df, model = segment_markets(market_features_df, features, n_clusters=3)

# --- Visualization (Optional) ---
# Use PCA for 2D visualization
# from sklearn.decomposition import PCA
# pca = PCA(n_components=2)
# reduced_features = pca.fit_transform(scaled_features)
# clustered_markets_df["pca1"] = reduced_features[:, 0]
# clustered_markets_df["pca2"] = reduced_features[:, 1]
# 
# plt.figure(figsize=(10, 7))
# sns.scatterplot(data=clustered_markets_df, x="pca1", y="pca2", hue="cluster_label", palette="viridis", s=100)
# # Annotate key markets like Taiz and Aden
# for market in ["TAIZ", "ADEN"]:
#     if market in clustered_markets_df.index:
#         plt.text(clustered_markets_df.loc[market, "pca1"], clustered_markets_df.loc[market, "pca2"], market)
# plt.title("Market Segments based on Volatility, Conflict, and Port Distance")
# plt.show()
```

**Expected Output:** The function returns the `market_features_df` with an added `cluster_label` column. It prints the average characteristics of each cluster and the specific cluster assignments for Taiz and Aden. Visualization helps understand the separation.

**Sanity Checks:** Do the cluster characteristics make sense? Is Taiz (high conflict, volatility) in a different cluster than Aden (low conflict, port access)? Are clusters geographically coherent (optional check)?

## 3. Interactive Fixed Effects (IFE): Wheat Prices & Ramadan

**Objective:** Estimate the effect of Ramadan on wheat prices, controlling for unobserved market-specific trends and common shocks using IFE.

**Method:** Interactive Fixed Effects (Reference: `MethodologyIntegrationGuide...` Section 4.1.4, `Step-by-Step...` Section 4.1.4). We use `linearmodels` which can absorb high-dimensional FEs, approximating IFE if factors are correlated with standard FEs, or `pyhdfe` for a more direct approach (requires installation and potentially more complex setup).

**Assumed Input Data:** Long panel DataFrame (`final_panel_df`) indexed by (`market_id`, `commodity_id`, `date`), containing `log_price_usd`, `is_ramadan` (dummy variable for Ramadan month), and potentially other controls (`conflict_intensity`). Filtered for `commodity_id == 'wheat'`. 

```python
import pandas as pd
import numpy as np
from linearmodels import PanelOLS
# For more direct IFE, consider pyhdfe (requires installation/setup)
# from pyhdfe import create

# --- Assume final_panel_df exists and is indexed ---
# Filter for wheat
# wheat_panel = final_panel_df[final_panel_df.index.get_level_values('commodity_id') == 'wheat'].copy()
# Assume 'is_ramadan' (dummy 0/1) and 'conflict_intensity' columns exist

# --- Implementation (using PanelOLS with multi-way FE as approximation) ---
def estimate_ramadan_effect_ife_approx(wheat_panel):
    """Estimates Ramadan effect on wheat prices using PanelOLS with FE."""
    print("Estimating Ramadan effect on wheat prices (IFE Approximation using PanelOLS)...")
    
    # Define dependent and independent variables
    y = wheat_panel['log_price_usd']
    exog_vars = ['is_ramadan', 'conflict_intensity'] # Add other relevant controls
    X = wheat_panel[exog_vars]
    X = X.fillna(X.mean()) # Handle potential missing controls
    
    # Check for missing dependent variable
    valid_idx = y.notna() & X.notna().all(axis=1)
    y = y[valid_idx]
    X = X[valid_idx]
    
    if y.empty:
        print("Error: No valid data points remaining after handling NaNs.")
        return None
        
    # Estimate model with market and time fixed effects
    # This absorbs time-invariant market effects and common time shocks.
    # Interaction effects (true IFE) require more advanced methods or assumptions.
    model = PanelOLS(y, X, entity_effects=True, time_effects=True)
    
    # Use clustered standard errors (e.g., by market)
    results = model.fit(cov_type='clustered', cluster_entity=True)
    
    print("\nPanelOLS Results (IFE Approximation):")
    print(results)
    
    # Extract Ramadan coefficient and p-value
    ramadan_coef = results.params.get('is_ramadan', np.nan)
    ramadan_pval = results.pvalues.get('is_ramadan', np.nan)
    
    print(f"\nEstimated Ramadan Effect: {ramadan_coef:.4f} (p-value: {ramadan_pval:.4f})")
    if ramadan_pval < 0.05:
        print("Result is statistically significant at the 5% level.")
    else:
        print("Result is not statistically significant at the 5% level.")
        
    return results

# --- Example Usage ---
# Assume wheat_panel is prepared
# ife_results = estimate_ramadan_effect_ife_approx(wheat_panel)
```

**Expected Output:** The function prints the `PanelOLS` regression summary. The key output is the coefficient for `is_ramadan`, indicating the average percentage change in wheat prices during Ramadan, controlling for market fixed effects, time fixed effects, and conflict intensity. Statistical significance is also reported.

**Sanity Checks:** Is the sign of the Ramadan coefficient plausible (e.g., positive due to increased demand)? Is the magnitude reasonable? How does it compare to a model without fixed effects or with only one type of fixed effect?

## 4. Nowcasting: Predicting Next Month's Fuel Prices

**Objective:** Forecast the average fuel (diesel) price for the next month using historical price data and potentially leading indicators.

**Method:** SARIMAX (Seasonal Autoregressive Integrated Moving Average with Exogenous Regressors). (Reference: `MethodologyIntegrationGuide...` Section 4.3.2, `Step-by-Step...` Section 4.3.2)

**Assumed Input Data:** A time series (`pandas` Series) of average diesel prices across key markets (`diesel_price_ts`), indexed by date. Optionally, a DataFrame (`exog_vars_df`) containing exogenous variables (e.g., lagged global oil prices, conflict index) aligned with the price series date index.

```python
import pandas as pd
import numpy as np
import statsmodels.api as sm
from statsmodels.tsa.statespace.sarimax import SARIMAX
import matplotlib.pyplot as plt

# --- Assume diesel_price_ts (Series) and optionally exog_vars_df exist ---
# Example: diesel_price_ts = final_panel_df[final_panel_df.index.get_level_values('commodity_id') == 'diesel']\
#                               .groupby('date')['log_price_usd'].mean()
# Ensure it's a monthly series with a DatetimeIndex

# --- Implementation ---
def forecast_fuel_price(price_ts, exog_df=None, forecast_steps=1):
    """Forecasts fuel price using SARIMAX."""
    print("Forecasting next month's diesel price using SARIMAX...")
    
    # Ensure series has frequency information
    if price_ts.index.freq is None:
        price_ts = price_ts.asfreq('MS') # Assume Monthly Start frequency
        print("Warning: Time series frequency not set. Assuming Monthly Start ('MS').")
        
    # Handle missing values in the time series (e.g., interpolate)
    price_ts_filled = price_ts.interpolate(method='linear')
    
    # Define SARIMAX model order (p,d,q)x(P,D,Q,s)
    # These orders should ideally be determined by ACF/PACF plots and AIC/BIC comparisons
    # Example order (adjust based on analysis): Non-seasonal AR(1), I(1), MA(1)
    # Seasonal component (s=12 for monthly): AR(1), I(0), MA(0)
    order = (1, 1, 1)
    seasonal_order = (1, 0, 0, 12) 
    
    # Prepare exogenous variables if provided
    exog_train = None
    exog_forecast = None
    if exog_df is not None:
        # Ensure alignment and handle missing values
        exog_df = exog_df.reindex(price_ts_filled.index).fillna(method='ffill')
        exog_train = exog_df
        # Need future values of exog for forecasting (or forecast them separately)
        # Simple approach: assume last value persists
        last_exog = exog_df.iloc[-1:]
        future_dates = pd.date_range(start=price_ts_filled.index[-1] + pd.DateOffset(months=1),
                                     periods=forecast_steps, freq='MS')
        exog_forecast = pd.DataFrame(index=future_dates, data=np.tile(last_exog.values, (forecast_steps, 1)), columns=exog_df.columns)
        
    # Fit the SARIMAX model
    try:
        model = SARIMAX(price_ts_filled, 
                        exog=exog_train, 
                        order=order, 
                        seasonal_order=seasonal_order,
                        enforce_stationarity=False,
                        enforce_invertibility=False)
        results = model.fit(disp=False)
        print("\nSARIMAX Model Summary:")
        print(results.summary())
        
        # Get forecast
        forecast_obj = results.get_forecast(steps=forecast_steps, exog=exog_forecast)
        forecast_mean = forecast_obj.predicted_mean
        forecast_ci = forecast_obj.conf_int(alpha=0.05) # 95% confidence interval
        
        print(f"\nForecast for next {forecast_steps} month(s):")
        print(forecast_mean)
        print("\nConfidence Intervals (95%):")
        print(forecast_ci)
        
        # Plot results (optional)
        plt.figure(figsize=(12, 6))
        plt.plot(price_ts_filled.index, price_ts_filled, label='Observed')
        plt.plot(forecast_mean.index, forecast_mean, label='Forecast', color='red')
        plt.fill_between(forecast_ci.index, forecast_ci.iloc[:, 0], forecast_ci.iloc[:, 1], color='pink', alpha=0.3)
        plt.title('Diesel Price Forecast')
        plt.legend()
        plt.show()
        
        return forecast_mean, forecast_ci, results
        
    except Exception as e:
        print(f"Error fitting SARIMAX model: {e}")
        return None, None, None

# --- Example Usage ---
# forecast_val, conf_int, model_res = forecast_fuel_price(diesel_price_ts, forecast_steps=3)
```

**Expected Output:** The function prints the SARIMAX model summary, the forecasted price(s) for the next month(s), and the corresponding confidence intervals. A plot showing historical data and the forecast is also generated.

**Sanity Checks:** Does the forecast look reasonable compared to recent trends? Are the confidence intervals very wide (indicating high uncertainty)? Do the model diagnostics (e.g., residual analysis from `results.plot_diagnostics()`) look acceptable?

## 5. Regime-Switching: Detecting 2019 Currency Split

**Objective:** Identify if a regime-switching model applied to exchange rate data can detect the approximate timing of the major currency split/divergence around 2019.

**Method:** Markov-Switching Autoregression (MSM). (Reference: `MethodologyIntegrationGuide...` Section 4.4.1, `Step-by-Step...` Section 4.4.1)

**Assumed Input Data:** A time series (`pandas` Series) of the average *government-zone* parallel market exchange rate (`govt_zone_exr_ts`), indexed by date. This zone experienced the major depreciation.

```python
import pandas as pd
import numpy as np
import statsmodels.api as sm
from statsmodels.tsa.regime_switching.markov_autoregression import MarkovAutoregression
import matplotlib.pyplot as plt

# --- Assume govt_zone_exr_ts (Series) exists ---
# Example: govt_zone_exr_ts = exchange_rate_data[exchange_rate_data['currency_zone'] == 'government']\
#                               .groupby('date')['exchange_rate'].mean()
# Ensure it's monthly with DatetimeIndex

# --- Implementation ---
def detect_exchange_rate_regime_switch(exr_ts, k_regimes=2, order=1):
    """Applies a Markov-Switching Autoregression model to detect regimes."""
    print(f"Detecting {k_regimes} regimes in exchange rate using Markov Switching AR({order})...")
    
    # Ensure frequency
    if exr_ts.index.freq is None:
        exr_ts = exr_ts.asfreq('MS')
        print("Warning: Time series frequency not set. Assuming Monthly Start ('MS').")
        
    # Handle missing values (e.g., interpolate)
    exr_ts_filled = exr_ts.interpolate(method='linear').dropna()
    
    # Use log difference for potentially better stationarity within regimes
    log_diff_exr = np.log(exr_ts_filled).diff().dropna()
    
    # Fit the Markov Switching model
    # Allow mean and variance to switch, AR(order) coefficients common across regimes
    try:
        model = MarkovAutoregression(log_diff_exr, 
                                     k_regimes=k_regimes, 
                                     order=order, 
                                     switching_variance=True,
                                     switching_mean=True, # Or switching_intercept=True in newer statsmodels
                                     switching_ar=False) # Assume AR dynamics are stable
        
        results = model.fit()
        print("\nMarkov Switching Model Summary:")
        print(results.summary())
        
        # Get smoothed probabilities of being in each regime
        smoothed_probs = results.smoothed_marginal_probabilities
        
        # Identify regime characteristics (e.g., high vs low variance/mean)
        regime_means = results.params[[f'const[{i}]' for i in range(k_regimes)]]
        regime_vars = results.params[[f'sigma2[{i}]' for i in range(k_regimes)]]
        print("\nRegime Characteristics:")
        print(f"Means (log diff): {regime_means.values}")
        print(f"Variances (log diff): {regime_vars.values}")
        
        # Determine which regime corresponds to high volatility/depreciation
        high_vol_regime = np.argmax(regime_vars.values)
        print(f"Regime {high_vol_regime} identified as high volatility/depreciation regime.")
        
        # Plot probabilities
        plt.figure(figsize=(12, 6))
        for i in range(k_regimes):
            plt.plot(smoothed_probs.index, smoothed_probs[i], label=f'Smoothed Prob. Regime {i}')
        plt.title('Smoothed Regime Probabilities for Government Zone Exchange Rate (Log Diff)')
        plt.legend()
        plt.show()
        
        # Check probability around 2019
        prob_high_vol_2019 = smoothed_probs.loc['2019-01-01':'2019-12-01', high_vol_regime].mean()
        print(f"\nAverage probability of being in high volatility regime during 2019: {prob_high_vol_2019:.2f}")
        
        return results, smoothed_probs
        
    except Exception as e:
        print(f"Error fitting Markov Switching model: {e}")
        return None, None

# --- Example Usage ---
# ms_results, regime_probs = detect_exchange_rate_regime_switch(govt_zone_exr_ts)
```

**Expected Output:** The function prints the model summary, identifies the characteristics (mean, variance) of each regime, and plots the smoothed probability of being in each regime over time. It specifically reports the average probability of the high-volatility regime during 2019.

**Sanity Checks:** Does the plot show a clear shift in probabilities around 2019, with the high-volatility/depreciation regime becoming dominant? Are the estimated regime characteristics distinct (e.g., one regime with near-zero mean and low variance, the other with positive mean and high variance)?

## 6. Bayesian Analysis: Uncertainty in Marib Prices

**Objective:** Estimate the average wheat price in Marib governorate using a Bayesian approach to better quantify uncertainty, especially given potential data sparsity or volatility.

**Method:** Bayesian Hierarchical Model (using PyMC). (Reference: `MethodologyIntegrationGuide...` Section 4.5, `Step-by-Step...` Section 4.5)

**Assumed Input Data:** Long panel DataFrame (`final_panel_df`) filtered for `governorate_std == 'Marib'` and `commodity_id == 'wheat'`, containing `log_price_usd` and potentially time indicators (`year`, `month`).

```python
import pandas as pd
import numpy as np
import pymc as pm
import arviz as az
import matplotlib.pyplot as plt

# --- Assume final_panel_df exists ---
# Filter for Marib wheat data
# marib_wheat_df = final_panel_df[
#     (final_panel_df['governorate_std'] == 'Marib') & 
#     (final_panel_df['commodity_id'] == 'wheat')
# ].copy()
# marib_wheat_df = marib_wheat_df.dropna(subset=['log_price_usd'])

# --- Implementation ---
def estimate_marib_price_bayesian(marib_wheat_data):
    """Estimates Marib wheat price using a simple Bayesian model."""
    print("Estimating Marib wheat price distribution using Bayesian model (PyMC)...")
    
    if marib_wheat_data.empty:
        print("Error: No data available for Marib wheat.")
        return None
        
    log_prices = marib_wheat_data['log_price_usd'].values
    n_obs = len(log_prices)
    
    # Define the Bayesian model
    with pm.Model() as marib_model:
        # Priors for the mean (mu) and standard deviation (sigma) of log prices
        # Weakly informative priors
        mu = pm.Normal('mu', mu=np.mean(log_prices), sigma=np.std(log_prices) * 2) # Centered around sample mean
        sigma = pm.HalfCauchy('sigma', beta=np.std(log_prices)) # Positive values only
        
        # Likelihood of the observed data
        likelihood = pm.Normal('likelihood', mu=mu, sigma=sigma, observed=log_prices)
        
    # Run the MCMC sampler
    with marib_model:
        print("Running MCMC sampler...")
        # Use more draws for better accuracy, fewer for speed
        trace = pm.sample(1000, tune=1000, cores=1, return_inferencedata=True)
        print("Sampling complete.")
        
    # Analyze the results
    summary = az.summary(trace, hdi_prob=0.95) # 95% Highest Density Interval
    print("\nPosterior Summary:")
    print(summary)
    
    # Plot posterior distributions
    az.plot_posterior(trace)
    plt.suptitle("Posterior Distributions for Marib Wheat Log Price Parameters")
    plt.tight_layout(rect=[0, 0.03, 1, 0.95]) # Adjust layout to prevent title overlap
    plt.show()
    
    # Extract key results (e.g., posterior mean and HDI for mu)
    posterior_mean_mu = summary.loc['mu', 'mean']
    hdi_mu = summary.loc['mu', ['hdi_2.5%', 'hdi_97.5%']].values
    
    print(f"\nPosterior Mean for average log price (mu): {posterior_mean_mu:.4f}")
    print(f"95% Highest Density Interval for mu: [{hdi_mu[0]:.4f}, {hdi_mu[1]:.4f}]")
    # Convert back to price level (exponentiate) if needed, remembering this gives median
    print(f"Posterior Median Price (approx exp(mu)): {np.exp(posterior_mean_mu):.2f}")
          
    return trace, summary

# --- Example Usage ---
# Assume marib_wheat_df is prepared
# bayesian_trace, bayesian_summary = estimate_marib_price_bayesian(marib_wheat_df)
```

**Expected Output:** The function prints the MCMC sampling progress, a summary table of the posterior distributions for the model parameters (mean `mu` and standard deviation `sigma` of log prices), including means, standard deviations, and Highest Density Intervals (HDIs). It also generates plots of these posterior distributions and reports the key results for the average log price (`mu`).

**Sanity Checks:** Are the posterior distributions well-behaved (unimodal, reasonably symmetric)? Is the sampler convergence good (check `r_hat` in the summary, should be close to 1.0)? Does the 95% HDI for `mu` provide a reasonable range for the average log price in Marib? Is the uncertainty (width of the HDI, value of `sigma`) plausible given the likely data quality in Marib?

## 7. Conclusion

These examples demonstrate how to apply advanced methods to specific Yemen research questions. They provide a starting point for analysts to adapt and extend these techniques. Remember to always:

*   Start with clean, well-structured data (use `yemen-specific-data-adapters.md`).
*   Understand the assumptions of each method.
*   Perform sanity checks and interpret results in the context of Yemen's complex environment.
*   Validate findings using robustness checks (see `validation-test-suite.md`).
*   Translate technical outputs into actionable insights (see `policy-translation-framework.md`).
