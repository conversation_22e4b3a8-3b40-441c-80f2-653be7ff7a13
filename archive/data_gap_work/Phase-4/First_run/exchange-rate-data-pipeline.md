# Exchange Rate Data Collection and Imputation Pipeline for Yemen

## Objective
Transform the documented 535 vs 2,000+ YER/USD exchange rate differential from theory to practice through systematic data collection, cleaning, and imputation procedures designed for Yemen's dual currency environment.

## The Challenge: From Theory to Field Reality

**Theoretical Finding**: Houthi areas maintain stable rates around 535 YER/USD while government areas experience 4x depreciation to 2,000+ YER/USD[13][26][27].

**Field Reality**: Exchange rate data collection in Yemen faces:
- Multiple parallel markets with different rates
- Security constraints limiting access
- Political sensitivities around currency monitoring
- Incomplete reporting during conflict escalation
- Timing mismatches between price and exchange rate collection

## 1. Data Source Hierarchy for Yemen Exchange Rates

### Primary Sources (In Order of Reliability)

#### Tier 1: WFP Global Market Monitor (HDX Verified)
- **Access**: https://data.humdata.org/dataset/global-market-monitor
- **Coverage**: 23 districts of governorate capitals[19]
- **Frequency**: Daily collection, bi-weekly updates
- **Reliability**: High - third-party collection with key informant backup
- **Format**: Standardized CSV with YER/USD rates by location

#### Tier 2: Local Money Changers and Traders
- **Access**: Direct contact through WhatsApp groups, Telegram channels
- **Coverage**: Major cities in both zones
- **Frequency**: Real-time updates but inconsistent
- **Reliability**: Medium - requires verification
- **Format**: Text messages, voice recordings

#### Tier 3: NGO Consortium Reports
- **Access**: Yemen Cash Consortium, OCHA reports
- **Coverage**: Operational areas only
- **Frequency**: Weekly to monthly
- **Reliability**: Good for operational areas
- **Format**: Excel reports, PDF bulletins

### Secondary Sources (For Validation Only)

#### Official Central Bank Rates
- **CBY-Aden**: Currently inaccessible but use for historical validation
- **CBY-Sana'a**: Limited access, use for Houthi zone validation
- **Status**: Treat as reference points, not operational data

## 2. Collection Protocols by Governorate

### Zone Classification for Data Collection

```
HOUTHI_CONTROLLED = {
    'primary': ['Sana', 'Hodeidah', 'Amran', 'Sadah'],
    'secure_collection': True,
    'expected_rate_range': [530, 540],
    'volatility_threshold': 0.02  # 2% daily change is suspicious
}

GOVERNMENT_CONTROLLED = {
    'primary': ['Aden', 'Taiz', 'Marib', 'Hadramaut'],
    'secure_collection': False,
    'expected_rate_range': [1800, 2200],
    'volatility_threshold': 0.05  # 5% daily change acceptable
}

CONTESTED = {
    'primary': ['Dhale', 'Border areas'],
    'secure_collection': False,
    'expected_rate_range': [800, 1500],
    'volatility_threshold': 0.10  # 10% daily change possible
}
```

### Daily Collection Protocol

#### Morning Collection (8:00-10:00 AM Local Time)
1. **Houthi Areas**: Contact established money changers in Sana'a, Hodeidah
2. **Government Areas**: Contact traders in Aden, Taiz markets
3. **Record**: Buy rate, sell rate, volume indicators, market conditions

#### Data Validation Steps
```python
def validate_daily_rate(rate, zone, date, previous_rates):
    """
    Validate exchange rate based on Yemen-specific criteria.
    """
    validation_results = {'valid': True, 'flags': []}
    
    # Zone-specific range checks
    if zone == 'houthi':
        if not (530 <= rate <= 540):
            validation_results['flags'].append('Outside expected Houthi range')
    elif zone == 'government':
        if rate < 1000:  # Too low for government zone
            validation_results['flags'].append('Suspiciously low for government zone')
    
    # Volatility check
    if len(previous_rates) > 0:
        prev_rate = previous_rates[-1]
        pct_change = abs(rate - prev_rate) / prev_rate
        
        zone_threshold = ZONE_CONFIG[zone]['volatility_threshold']
        if pct_change > zone_threshold:
            validation_results['flags'].append(f'High volatility: {pct_change:.1%}')
    
    # Set validation status
    validation_results['valid'] = len(validation_results['flags']) == 0
    
    return validation_results
```

## 3. Missing Data Imputation Methods

### Hierarchical Imputation Strategy

#### Step 1: Same-Zone Forward Fill (1-3 days)
```python
def same_zone_forward_fill(df, max_days=3):
    """
    Fill missing rates using previous rates within same zone.
    """
    # Sort by zone and date
    df = df.sort_values(['zone', 'date'])
    
    # Forward fill within zone, limited to max_days
    df['rate_ffill'] = df.groupby('zone')['exchange_rate'].transform(
        lambda x: x.fillna(method='ffill', limit=max_days)
    )
    
    return df
```

#### Step 2: Zone Average Interpolation (4-7 days)
```python
def zone_average_interpolation(df):
    """
    Use zone average for medium-term gaps.
    """
    # Calculate daily zone averages
    zone_daily_avg = df.groupby(['date', 'zone'])['exchange_rate'].mean()
    
    # Merge back and fill gaps
    df = df.merge(zone_daily_avg.rename('zone_avg'), on=['date', 'zone'], how='left')
    
    # Fill remaining gaps with zone average
    df['rate_interpolated'] = df['rate_ffill'].fillna(df['zone_avg'])
    
    return df
```

#### Step 3: Trend-Based Projection (8+ days)
```python
def trend_based_projection(df, window=7):
    """
    Project rates based on recent trends for long gaps.
    """
    for zone in df['zone'].unique():
        zone_data = df[df['zone'] == zone].copy()
        
        # Calculate trend from last 'window' observations
        for i, row in zone_data.iterrows():
            if pd.isna(row['rate_interpolated']):
                # Get recent valid observations
                recent_rates = zone_data[zone_data['date'] < row['date']]['rate_interpolated'].dropna()
                
                if len(recent_rates) >= window:
                    # Simple linear trend
                    x = np.arange(len(recent_rates[-window:]))
                    y = recent_rates[-window:].values
                    
                    # Fit linear regression
                    slope, intercept = np.polyfit(x, y, 1)
                    
                    # Project forward
                    days_forward = (row['date'] - recent_rates.index[-1]).days
                    projected_rate = intercept + slope * (window + days_forward)
                    
                    df.loc[df.index == i, 'rate_interpolated'] = projected_rate
    
    return df
```

## 4. Quality Control Procedures

### Real-Time Validation Checklist

#### Monday Morning in Sana'a Example
**Question**: "Which rate to use when multiple sources give different values?"

**Procedure**:
1. **Check Source Reliability**
   - WFP third-party: 536 YER/USD ✓ (Most reliable)
   - Local trader WhatsApp: 534 YER/USD ✓ (Within expected range)
   - Unverified Telegram: 580 YER/USD ✗ (Outside range, flag)

2. **Apply Validation Rules**
   ```python
   sources = [
       {'rate': 536, 'source': 'WFP', 'reliability': 0.95},
       {'rate': 534, 'source': 'Local trader', 'reliability': 0.80},
       {'rate': 580, 'source': 'Unverified', 'reliability': 0.30}
   ]
   
   # Weight by reliability
   weighted_rate = sum(s['rate'] * s['reliability'] for s in sources) / sum(s['reliability'] for s in sources)
   # Result: 535.3 YER/USD
   ```

3. **Final Decision**
   - Use: 535 YER/USD (rounded weighted average)
   - Flag: Unverified source for investigation
   - Document: Decision rationale in data log

### Weekly Quality Assessment

#### Data Completeness Report
```python
def weekly_quality_report(df, week_start, week_end):
    """
    Generate weekly data quality report.
    """
    week_data = df[(df['date'] >= week_start) & (df['date'] <= week_end)]
    
    report = {}
    
    for zone in week_data['zone'].unique():
        zone_data = week_data[week_data['zone'] == zone]
        
        total_expected = 7  # Days in week
        total_collected = zone_data['exchange_rate'].notna().sum()
        total_imputed = zone_data['is_imputed'].sum()
        
        report[zone] = {
            'completeness': total_collected / total_expected,
            'imputation_rate': total_imputed / total_expected,
            'avg_rate': zone_data['exchange_rate'].mean(),
            'volatility': zone_data['exchange_rate'].std() / zone_data['exchange_rate'].mean(),
            'status': 'Good' if total_collected/total_expected > 0.8 else 'Warning'
        }
    
    return report
```

## 5. Integration with WFP Price Data

### Temporal Alignment Protocol

#### Challenge: Price data collected monthly, exchange rates collected daily
**Solution**: Create month-average rates with quality indicators

```python
def align_with_price_data(exchange_df, price_df):
    """
    Align exchange rates with monthly price collection.
    """
    # Convert to month-year for alignment
    exchange_df['year_month'] = exchange_df['date'].dt.to_period('M')
    price_df['year_month'] = price_df['date'].dt.to_period('M')
    
    # Calculate monthly statistics
    monthly_rates = exchange_df.groupby(['year_month', 'zone']).agg({
        'exchange_rate': ['mean', 'median', 'std', 'count'],
        'is_imputed': 'sum'
    }).round(2)
    
    # Flatten column names
    monthly_rates.columns = ['_'.join(col) for col in monthly_rates.columns]
    monthly_rates = monthly_rates.reset_index()
    
    # Add quality indicators
    monthly_rates['data_quality'] = monthly_rates.apply(
        lambda row: 'High' if row['exchange_rate_count'] >= 25 and row['is_imputed_sum'] <= 5
                   else 'Medium' if row['exchange_rate_count'] >= 15
                   else 'Low', axis=1
    )
    
    # Merge with price data
    price_with_rates = price_df.merge(
        monthly_rates[['year_month', 'zone', 'exchange_rate_median', 'data_quality']],
        on=['year_month', 'zone'],
        how='left'
    )
    
    return price_with_rates
```

## 6. Decision Trees for Common Implementation Challenges

### Decision Tree 1: Missing Exchange Rate Data

```
Missing rate data for Aden on Tuesday?
├── Was there conflict in Aden on Monday-Tuesday?
│   ├── YES: Use previous day's rate (acceptable for 1-2 days)
│   └── NO: Check other government zone cities
│       ├── Taiz has data: Use Taiz rate (same zone)
│       └── No government data: Use trend projection
│
└── Is this the 3rd consecutive day missing?
    ├── YES: 
    │   ├── Check for systematic collection issue
    │   ├── Contact backup sources
    │   └── Consider using contested zone average if available
    └── NO: Use zone forward fill
```

### Decision Tree 2: Conflicting Rate Reports

```
Multiple sources give different rates (>5% difference)?
├── All sources within expected zone range?
│   ├── YES: Use reliability-weighted average
│   └── NO: Identify outliers
│       ├── Outlier is usually reliable source: Investigate market event
│       └── Outlier is unreliable source: Exclude and document
│
└── Sources from different zones reporting same area?
    ├── Check territorial control changes
    ├── Use most recent, reliable source
    └── Flag for manual review
```

## 7. Emergency Protocols

### During Conflict Escalation
1. **Immediate Actions**
   - Switch to backup data collection networks
   - Increase reliance on zone averages
   - Document data collection constraints

2. **Communication Protocol**
   - Daily check-ins with data collectors
   - Immediate flag of collector safety concerns
   - Suspend collection if security risk exceeds acceptable threshold

3. **Data Handling**
   - Increase tolerance for missing data (up to 50% for short periods)
   - Use longer backward/forward fills (up to 7 days)
   - Document conflict impact on data quality

### Data Collector Safety Checklist
- [ ] Local security assessment completed
- [ ] Communication protocols established
- [ ] Backup collection methods identified
- [ ] Emergency contact procedures tested
- [ ] Data collection value vs risk assessed daily

## 8. Implementation Timeline

### Week 1-2: Setup
- Establish contact with WFP HDX data team
- Set up automated data feeds from Tier 1 sources
- Configure validation scripts and quality checks

### Week 3-4: Validation
- Run validation protocols on historical data
- Identify and document systematic patterns
- Calibrate imputation parameters

### Week 5-6: Operations
- Begin daily data collection routine
- Implement real-time quality monitoring
- Generate weekly quality reports

### Week 7+: Maintenance
- Monthly review of collection protocols
- Quarterly assessment of source reliability
- Semi-annual update of validation thresholds

## 9. Success Criteria

### Data Quality Targets
- **Completeness**: >80% of trading days have data
- **Accuracy**: <5% deviation from verified rates
- **Timeliness**: Daily rates available within 24 hours
- **Zone Coverage**: Both Houthi and government zones represented daily

### Implementation Success
- Field teams can follow procedures without external support
- Data quality meets standards for econometric analysis
- Policy teams receive timely, reliable exchange rate information
- System operates effectively during moderate conflict escalation

## 10. Troubleshooting Guide

### Common Issues and Solutions

| Issue | Symptoms | Solution |
|-------|----------|----------|
| Houthi rates suddenly increase | Rate jumps from 535 to 600+ | Check for new banknote policy changes |
| Government rates too stable | No variation for 5+ days | Verify data collection, may indicate collection failure |
| Large zone differential | >300% difference | Normal, but verify both rates independently |
| Missing data clusters | Multiple zones missing same dates | Check for internet/communication disruption |
| Validator flags all data | All rates marked as suspicious | Review validation thresholds, may need recalibration |

This pipeline provides the practical bridge between the theoretical understanding of Yemen's dual currency system and the field reality of collecting reliable exchange rate data in a conflict environment. The procedures are designed to maintain data quality while acknowledging the operational constraints of working in Yemen.