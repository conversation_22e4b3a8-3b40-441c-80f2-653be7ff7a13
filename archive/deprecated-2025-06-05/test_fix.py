#!/usr/bin/env python3
"""
Simple test script to verify the data quality orchestrator fix.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_data_ingestion_import():
    """Test that we can import the data ingestion service."""
    try:
        from src.application.services.data_ingestion_service import DataIngestionService
        print("✅ DataIngestionService import successful")
        return True
    except Exception as e:
        print(f"❌ DataIngestionService import failed: {e}")
        return False

def test_wfp_processor_method():
    """Test that WFP processor has the correct method."""
    try:
        from src.infrastructure.processors.wfp_processor import WFPProcessor
        
        # Check that process method exists
        processor = WFPProcessor()
        assert hasattr(processor, 'process'), "WFPProcessor should have 'process' method"
        
        # Check that process_price_data method does NOT exist (was the bug)
        assert not hasattr(processor, 'process_price_data'), "WFPProcessor should NOT have 'process_price_data' method"
        
        print("✅ WFP processor method check passed")
        return True
    except Exception as e:
        print(f"❌ WFP processor method check failed: {e}")
        return False

def test_data_quality_framework():
    """Test basic data quality framework imports."""
    try:
        from src.infrastructure.data_quality.data_quality_orchestrator import DataQualityOrchestrator
        from src.infrastructure.data_quality.integration import DataQualityService
        
        # Test basic instantiation
        orchestrator = DataQualityOrchestrator()
        service = DataQualityService()
        
        print("✅ Data quality framework import successful")
        return True
    except Exception as e:
        print(f"❌ Data quality framework import failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing fixes for PriceObservation subscriptable error...")
    print("=" * 60)
    
    tests = [
        test_data_ingestion_import,
        test_wfp_processor_method,
        test_data_quality_framework,
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! The fix appears to be working.")
        return 0
    else:
        print("⚠️ Some tests failed. There may be remaining issues.")
        return 1

if __name__ == "__main__":
    exit(main())