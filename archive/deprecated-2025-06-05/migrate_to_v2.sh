#!/bin/bash

# Yemen Market Integration: V1 to V2 Migration Script
# This script safely migrates from V1 (src/) to V2 (v2/src/) structure

set -e  # Exit on any error

echo "🚀 Yemen Market Integration V1 to V2 Migration"
echo "================================================"

# Check if we're in the right directory
if [ ! -d "src" ] || [ ! -d "v2" ]; then
    echo "❌ Error: Must be run from project root with both src/ and v2/ directories"
    exit 1
fi

# Create backup timestamp
BACKUP_TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="archive/v1_backup_${BACKUP_TIMESTAMP}"

echo "📦 Step 1: Creating V1 backup..."
mkdir -p "${BACKUP_DIR}"
cp -r src/ "${BACKUP_DIR}/"
cp -r tests/ "${BACKUP_DIR}/" 2>/dev/null || true
cp -r scripts/ "${BACKUP_DIR}/" 2>/dev/null || true
cp -r notebooks/ "${BACKUP_DIR}/" 2>/dev/null || true
echo "✅ V1 backup created at: ${BACKUP_DIR}"

echo "🔄 Step 2: Preparing V2 migration..."
# Check V2 completeness
if [ ! -d "v2/src/core" ] || [ ! -d "v2/src/infrastructure" ] || [ ! -d "v2/src/interfaces" ]; then
    echo "❌ Error: V2 implementation appears incomplete"
    echo "   Missing required directories in v2/src/"
    exit 1
fi

echo "🗑️  Step 3: Removing old V1 structure..."
rm -rf src/

echo "📁 Step 4: Moving V2 to main src/ location..."
mv v2/src/ src/

echo "📋 Step 5: Updating configuration files..."
# Update pyproject.toml if it references old paths
if grep -q "yemen_market" pyproject.toml; then
    echo "   Updating pyproject.toml package references..."
    # Note: V2 uses a different package structure, may need manual updates
fi

# Update any import paths in remaining files
echo "   Checking for import path updates needed..."
# This would need to be customized based on actual import patterns

echo "🧹 Step 6: Cleaning up..."
# Move remaining V2 files to appropriate locations
if [ -d "v2/tests" ]; then
    echo "   Moving V2 tests..."
    rm -rf tests/
    mv v2/tests/ tests/
fi

if [ -d "v2/scripts" ]; then
    echo "   Moving V2 scripts..."
    # Backup existing scripts
    mv scripts/ "${BACKUP_DIR}/scripts_v1/" 2>/dev/null || true
    mv v2/scripts/ scripts/
fi

if [ -d "v2/docs" ]; then
    echo "   Merging V2 documentation..."
    cp -r v2/docs/* docs/ 2>/dev/null || true
fi

# Move Kubernetes configs to root level
if [ -d "v2/kubernetes" ]; then
    echo "   Moving Kubernetes configurations..."
    mv v2/kubernetes/ kubernetes/
fi

# Move Docker configs
if [ -f "v2/Dockerfile" ]; then
    echo "   Moving Docker configurations..."
    mv v2/Dockerfile* ./ 2>/dev/null || true
    mv v2/docker-compose.yml ./ 2>/dev/null || true
fi

echo "✅ Step 7: Migration complete!"
echo ""
echo "📊 Migration Summary:"
echo "   ✅ V1 backed up to: ${BACKUP_DIR}"
echo "   ✅ V2 promoted to main src/ directory"
echo "   ✅ Tests and scripts updated"
echo "   ✅ Documentation merged"
echo "   ✅ Kubernetes configs moved to kubernetes/"
echo ""
echo "🔧 Next Steps:"
echo "   1. Update any remaining import paths"
echo "   2. Run tests: pytest tests/"
echo "   3. Verify functionality: python -m src.yemen_market"
echo "   4. Update IDE configurations"
echo "   5. Commit changes to git"
echo ""
echo "🔄 To rollback if needed:"
echo "   rm -rf src/ && cp -r ${BACKUP_DIR}/src/ src/"
echo ""
echo "🎉 Yemen Market Integration V2 is now active!"