#!/usr/bin/env python3
"""
Systematic fix for yemen_market import issues in integration tests.
This script updates old package imports to new src structure.
"""

import os
import re
from pathlib import Path

# Mapping from old yemen_market imports to new src imports
IMPORT_MAPPINGS = {
    # Logging utilities
    'from yemen_market.utils.logging import': 'from src.core.utils.logging import',
    'from yemen_market.utils import setup_logging': 'from src.core.utils.logging import setup_logging',
    
    # Config/settings - these don't exist in new structure, need to be removed or replaced
    'from yemen_market.config.settings import RAW_DATA_DIR, PROCESSED_DATA_DIR': '# Config imports removed - use direct paths',
    
    # Data processors - map to infrastructure
    'from yemen_market.data.wfp_processor import WFPProcessor': 'from src.infrastructure.processors.wfp_processor import WFPProcessor',
    'from yemen_market.data.acaps_processor import ACAPSProcessor': 'from src.infrastructure.processors.acaps_processor import ACAPSProcessor', 
    'from yemen_market.data.acled_processor import ACLEDProcessor': 'from src.infrastructure.processors.acled_processor import ACLEDProcessor',
    'from yemen_market.data.spatial_joins import SpatialJoiner': '# SpatialJoiner moved to panel_builder or removed',
    'from yemen_market.data.panel_builder import PanelBuilder': 'from src.infrastructure.processors.panel_builder import PanelBuilder',
    'from yemen_market.data.hdx_client import HDXClient': 'from src.infrastructure.external_services.hdx_client import HDXClient',
    
    # Feature engineering
    'from yemen_market.features.feature_engineering import FeatureEngineer': '# FeatureEngineer functionality moved to services',
    
    # Three tier models - these need significant restructuring
    'from yemen_market.models.three_tier.integration.three_tier_runner import ThreeTierAnalysis': 'from src.application.services.three_tier_analysis_service import ThreeTierAnalysisService',
    'from yemen_market.models.three_tier.diagnostics import ThreeTierPanelDiagnostics': 'from src.infrastructure.diagnostics.panel_diagnostics import PanelDiagnosticTests',
    'from yemen_market.models.three_tier.core.results_container import ResultsContainer': '# ResultsContainer functionality is now in model result objects',
    'from yemen_market.models.three_tier.tier1_pooled.pooled_panel_model import': 'from src.core.models.panel.pooled_panel import PooledPanelModel',
    'from yemen_market.models.three_tier.tier1_pooled.standard_errors import StandardErrorCorrector': 'from src.infrastructure.estimators.standard_errors import StandardErrorEstimator',
    'from yemen_market.models.three_tier.tier2_commodity import': 'from src.core.models.time_series import',
}

# Specific pattern replacements
PATTERN_REPLACEMENTS = [
    # Remove LINEARMODELS_AVAILABLE references as it's handled differently now
    (r'from yemen_market\.models\.three_tier\.tier1_pooled\.pooled_panel_model import \(\s*PooledPanelModel, LINEARMODELS_AVAILABLE\s*\)', 
     'from src.core.models.panel.pooled_panel import PooledPanelModel'),
]

def fix_imports_in_file(file_path):
    """Fix imports in a single test file."""
    print(f"Fixing imports in {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        original_content = content
        
        # Apply direct mappings
        for old_import, new_import in IMPORT_MAPPINGS.items():
            if old_import in content:
                print(f"  Replacing: {old_import}")
                print(f"  With: {new_import}")
                content = content.replace(old_import, new_import)
        
        # Apply pattern replacements
        for pattern, replacement in PATTERN_REPLACEMENTS:
            if re.search(pattern, content):
                print(f"  Applying pattern replacement: {pattern[:50]}...")
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
        
        # Additional fixes for specific issues
        # Fix sys.path additions to point to src correctly
        content = re.sub(
            r'sys\.path\.insert\(0, str\(Path\(__file__\)\.parent\.parent\.parent\)\)',
            'sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))',
            content
        )
        
        # Remove or comment out references to non-existent features
        content = re.sub(
            r'LINEARMODELS_AVAILABLE',
            'True  # linearmodels is available',
            content
        )
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ Updated {file_path}")
            return True
        else:
            print(f"  ℹ️  No changes needed in {file_path}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error fixing {file_path}: {e}")
        return False

def main():
    """Fix all integration test import issues."""
    print("=" * 60)
    print("FIXING INTEGRATION TEST IMPORT ISSUES")
    print("=" * 60)
    
    # Get all integration test files with yemen_market imports
    test_dir = Path(__file__).parent / "tests" / "integration"
    
    files_with_issues = [
        "test_diagnostic_integration.py",
        "test_full_pipeline.py", 
        "test_hdx_client.py",
        "test_tier1_integration.py",
        "test_tier2_integration.py",
        "test_three_tier_integration.py"
    ]
    
    fixed_count = 0
    total_count = 0
    
    for filename in files_with_issues:
        file_path = test_dir / filename
        if file_path.exists():
            total_count += 1
            if fix_imports_in_file(file_path):
                fixed_count += 1
        else:
            print(f"⚠️  File not found: {file_path}")
    
    print("\n" + "=" * 60)
    print(f"RESULTS: Fixed {fixed_count}/{total_count} test files")
    
    if fixed_count > 0:
        print("✅ Import fixes applied successfully!")
        print("Some tests may still need additional structural changes.")
    else:
        print("ℹ️  No import fixes were needed.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()