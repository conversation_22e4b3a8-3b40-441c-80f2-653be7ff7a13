# Deprecated Files Archive - June 5, 2025

This directory contains files that were deprecated during the migration to UV package management and root directory cleanup.

## Files Moved Here:

### Package Management (Deprecated - Use UV)
- `requirements.txt` - Legacy pip requirements file
- `fix_test_imports.py` - Legacy script for import fixes

### Completed Phase Reports (Archived)
- `DATA_PIPELINE_VALIDATION_SUMMARY.md`
- `PHASE_2_COMPLETION_REPORT.md` 
- `PHASE_2_HANDOVER_PROMPT.md`
- `PHASE_3_ENHANCEMENT_PROMPT.md`
- `PHASE_3_ENHANCEMENT_SUMMARY.md`
- `PHASE_3_HANDOVER_PROMPT.md`
- `PHASE_3_ISSUES_RESOLVED.md`
- `REPOSITORY_LEVEL_ENFORCEMENT_SUMMARY.md`

### Legacy Scripts
- `migrate_to_v2.sh` - Completed migration script
- `test_fix.py` - Legacy test fix script

### Other Files
- `coverage.xml` - Moved to reports/coverage/
- `EXCHANGE_RATE_DATA_SOURCES.md` - Moved to docs/

## Current Package Management

The project now uses UV exclusively. See:
- `pyproject.toml` - Main dependency configuration
- `uv.lock` - Locked dependency versions
- `CLAUDE.md` - Environment setup instructions

## Migration Date
June 5, 2025 - Comprehensive cleanup and UV migration