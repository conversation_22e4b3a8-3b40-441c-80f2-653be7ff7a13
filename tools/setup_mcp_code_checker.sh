#!/bin/bash

# MCP Code Checker Installation Script
# This script should be run from outside the yemen-market-integration directory

echo "Installing MCP Code Checker..."

# Navigate to parent directory
cd ..

# Clone the repository
echo "Cloning MCP Code Checker repository..."
git clone https://github.com/mcpflow/mcp_server_code_checker_python.git

# Enter the directory
cd mcp_server_code_checker_python

# Install dependencies using UV (if available) or fallback to pip
echo "Installing dependencies..."
if command -v uv &> /dev/null; then
    echo "Using UV for installation..."
    uv sync
else
    echo "UV not found, using pip..."
    python -m venv .venv
    source .venv/bin/activate
    pip install -e .
fi

# Get the current directory for configuration
MCP_DIR=$(pwd)
PROJECT_DIR="$HOME/Documents/GitHub/yemen-market-integration"
PYTHON_EXEC="$MCP_DIR/.venv/bin/python"
VENV_PATH="$PROJECT_DIR/.venv"

# Create <PERSON> configuration
echo "Creating Claude Desktop configuration..."
cat > mcp_claude_config.json << EOF
{
    "mcpServers": {
        "code_checker": {
            "command": "$PYTHON_EXEC",
            "args": [
                "$MCP_DIR/src/main.py",
                "--project-dir", "$PROJECT_DIR",
                "--python-executable", "$PROJECT_DIR/.venv/bin/python",
                "--venv-path", "$VENV_PATH"
            ],
            "env": {
                "PYTHONPATH": "$MCP_DIR"
            }
        }
    }
}
EOF

echo "Configuration saved to mcp_claude_config.json"
echo ""
echo "To complete setup:"
echo "1. Copy the configuration from mcp_claude_config.json"
echo "2. Add it to your Claude Desktop config file:"
echo "   macOS: ~/Library/Application Support/Claude/claude_desktop_config.json"
echo "   Windows: %APPDATA%\Claude\claude_desktop_config.json"
echo "3. Restart Claude Desktop"
echo ""
echo "Installation complete!"